package com.zto.devops.sign;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.application.service.SceneRecordServiceImpl;
import com.zto.devops.qc.client.exception.ApiSignAuthException;
import com.zto.devops.qc.client.service.testmanager.apitest.model.SceneDataCenterExecuteReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.SceneTaskResultQueryReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.SceneTaskResultResp;
import com.zto.devops.qc.infrastructure.auth.ApiSignAuthClient;
import com.zto.devops.qc.infrastructure.auth.SignAuthContext;
import com.zto.devops.qc.infrastructure.auth.SignatureUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 场景记录服务签名鉴权集成测试
 * 
 * <AUTHOR>
 * @since 2025-08-14
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SignAuthTest {
    
    @Autowired
    private SceneRecordServiceImpl sceneRecordService;
    
    /**
     * 测试executeSceneDataCenter接口的签名鉴权
     */
    @Test
    public void testExecute() {
        // 准备测试数据
        String accessKey = "AK_SCRIPT_TEST_001";
        String secretKey = "sk_test_script_scene_data_center_secret_key_001";
        String httpMethod = "POST";
        String requestUri = "/tm/apitest/executeSceneDataCenter";
        
        SceneDataCenterExecuteReq req = new SceneDataCenterExecuteReq();
        req.setSceneCode("SNF979388218212352000");
        req.setProductCode("399");
        JSONObject inputParam = new JSONObject();
        inputParam.put("key", "value");
        req.setInputParameter(inputParam);
        
        // 设置签名鉴权参数
        ApiSignAuthClient.setSignAuthForDubboCall(accessKey, secretKey, httpMethod, requestUri, req);
        
        try {
            // 调用接口（这里可能会因为业务逻辑失败，但不应该因为鉴权失败）
            Result<String> result = sceneRecordService.executeSceneDataCenter(req);
            // 如果到这里说明鉴权通过了
            System.out.println("executeSceneDataCenter鉴权测试通过");
        } catch (ApiSignAuthException e) {
            throw new AssertionError("签名鉴权应该通过，但失败了: " + e.getMessage());
        } catch (Exception e) {
            // 其他业务异常是正常的，说明鉴权通过了
            System.out.println("executeSceneDataCenter鉴权通过，业务异常: " + e.getMessage());
        }
    }
    
    /**
     * 测试querySceneTaskResult接口的签名鉴权
     */
    @Test
    public void testQuery() {
        // 准备测试数据
        String accessKey = "AK_SCRIPT_TEST_001";
        String secretKey = "sk_test_script_scene_data_center_secret_key_001";
        String httpMethod = "POST";
        String requestUri = "/tm/apitest/querySceneTaskResult";
        
        SceneTaskResultQueryReq req = new SceneTaskResultQueryReq();
        req.setTaskId("test_task_id_123");
        
        // 设置签名鉴权参数
        ApiSignAuthClient.setSignAuthForDubboCall(accessKey, secretKey, httpMethod, requestUri, req);
        
        try {
            // 调用接口
            Result<SceneTaskResultResp> result = sceneRecordService.querySceneTaskResult(req);
            System.out.println("querySceneTaskResult鉴权测试通过");
        } catch (ApiSignAuthException e) {
            throw new AssertionError("签名鉴权应该通过，但失败了: " + e.getMessage());
        } catch (Exception e) {
            // 其他业务异常是正常的，说明鉴权通过了
            System.out.println("querySceneTaskResult鉴权通过，业务异常: " + e.getMessage());
        }
    }
    
    /**
     * 测试无效签名的情况
     */
    @Test(expected = ApiSignAuthException.class)
    public void testInvalidSignature() {
        // 设置无效的签名上下文
        SignAuthContext context = new SignAuthContext();
        context.setAccessKey("INVALID_KEY");
        context.setSignature("invalid_signature");
        context.setTimestamp(System.currentTimeMillis());
        context.setNonce("test_nonce");
        context.setHttpMethod("POST");
        context.setRequestUri("/tm/apitest/executeSceneDataCenter");
        
        SignAuthContext.setContext(context);
        
        SceneDataCenterExecuteReq req = new SceneDataCenterExecuteReq();
        req.setSceneCode("SNF979388218212352000");
        
        // 应该抛出ApiSignAuthException
        sceneRecordService.executeSceneDataCenter(req);
    }
    
    /**
     * 测试过期签名的情况
     */
    @Test(expected = ApiSignAuthException.class)
    public void testExpiredSignature() {
        String accessKey = "AK_SCRIPT_TEST_001";
        String secretKey = "sk_test_script_scene_data_center_secret_key_001";
        
        // 设置过期的时间戳（1小时前）
        long expiredTimestamp = System.currentTimeMillis() - 3600 * 1000;
        
        SignAuthContext context = new SignAuthContext();
        context.setAccessKey(accessKey);
        context.setTimestamp(expiredTimestamp);
        context.setNonce(SignatureUtils.generateNonce());
        context.setHttpMethod("POST");
        context.setRequestUri("/tm/apitest/executeSceneDataCenter");
        
        SceneDataCenterExecuteReq req = new SceneDataCenterExecuteReq();
        req.setSceneCode("SNF979388218212352000");
        
        // 生成签名（虽然签名正确，但时间戳过期）
        String signature = SignatureUtils.generateSignature(
            accessKey, secretKey, context.getHttpMethod(), context.getRequestUri(),
            context.getTimestamp(), context.getNonce(), req
        );
        context.setSignature(signature);
        
        SignAuthContext.setContext(context);
        
        // 应该抛出ApiSignAuthException
        sceneRecordService.executeSceneDataCenter(req);
    }
}
