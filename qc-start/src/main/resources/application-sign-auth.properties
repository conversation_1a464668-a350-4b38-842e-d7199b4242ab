# API签名鉴权配置示例
# 在主配置文件中引入：spring.profiles.include=sign-auth

# 是否启用API签名鉴权
qc.api.sign.auth.enabled=true

# 签名默认有效期（秒）- 5分钟
qc.api.sign.auth.default-expire-seconds=300

# 允许的时间偏差（秒）- 1分钟
qc.api.sign.auth.default-time-skew-seconds=60

# Nonce缓存过期时间（秒）- 10分钟
qc.api.sign.auth.nonce-cache-expire-seconds=600

# 是否记录鉴权日志
qc.api.sign.auth.log-enabled=true

# 是否在开发环境跳过鉴权
qc.api.sign.auth.skip-in-dev=false

# Redis缓存前缀
qc.api.sign.auth.redis-cache-prefix=qc:api:sign:auth
