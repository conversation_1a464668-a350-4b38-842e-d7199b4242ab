package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.entity.UserInfo;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.qc.client.model.testmanager.apitest.command.SaveUserVariableCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.command.SceneDataCenterExecuteCommand;
import com.zto.devops.qc.client.service.testmanager.apitest.SceneRecordService;
import com.zto.devops.qc.client.service.testmanager.apitest.model.*;
import com.zto.devops.qc.domain.service.SceneRecordDomainService;
import com.zto.devops.qc.domain.service.SceneRecordQueryService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.ZsmpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Service
@Slf4j
@ZsmpService(name = "接口自动化接口目录", group = "测试管理/用例工厂日志")
public class SceneRecordServiceImpl extends GatewayBase implements SceneRecordService {

    @Autowired
    private SceneRecordDomainService sceneRecordDomainService;

    @Autowired
    private SceneRecordQueryService sceneRecordQueryService;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @ZApiOperation(
            description = "测试管理-接口测试-执行造数并上传日志",
            apiName = "tm/apitest/executeSceneDataCenter",
            namespace = "luban")
    @Override
    public Result<String> executeSceneDataCenter(SceneDataCenterExecuteReq req) {
        String generateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        SceneDataCenterExecuteCommand command = new SceneDataCenterExecuteCommand(generateId);
        command.setSceneCode(req.getSceneCode());
        command.setInputParameter(req.getInputParameter());
        command.setProductCode(req.getProductCode());
        GatewayContext.fillCurrentUser(command);
        return StatusCode.OK.build(sceneRecordDomainService.executeSceneDataCenter(command));
    }

    @ZApiOperation(
            description = "测试管理-接口测试-查询造数结果",
            apiName = "tm/apitest/querySceneTaskResult",
            namespace = "luban")
    @Override
    public Result<SceneTaskResultResp> querySceneTaskResult(SceneTaskResultQueryReq req) {
        return StatusCode.OK.build(sceneRecordQueryService.querySceneTaskResult(req.getTaskId()));
    }

    @ZApiOperation(
            description = "测试管理-接口测试-保存选择的cookie用户",
            apiName = "tm/apitest/saveUserVariable",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> saveUserVariable(SaveUserVariableReq req) {
        String generateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        SaveUserVariableCommand command = new SaveUserVariableCommand(generateId);
        command.setVariableCode(req.getVariableCode());
        command.setProductCode(req.getProductCode());
        GatewayContext.fillCurrentUser(command);
        sceneRecordQueryService.saveUserVariable(command, command.getTransactor());
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口测试-查询选择的cookie用户",
            apiName = "tm/apitest/queryUserVariable",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<String> queryUserVariable(UserVariableQueryReq req) {
        UserInfo userInfo = GatewayContext.currentUser();
        if(null == userInfo) {
            throw new ServiceException("当前操作人不能为空!");
        }
        return StatusCode.OK.build(sceneRecordQueryService.queryUserVariable(req.getProductCode(), userInfo.getUser_id()));
    }

}
