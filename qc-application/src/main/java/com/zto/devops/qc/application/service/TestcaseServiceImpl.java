package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.baidu.unbiz.fluentvalidator.ComplexResult;
import com.baidu.unbiz.fluentvalidator.FluentValidator;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.command.impexp.AddExpCommand;
import com.zto.devops.framework.client.dto.Page;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.exception.FluentException;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.common.validator.LengthValidator;
import com.zto.devops.framework.common.validator.NotBlankValidator;
import com.zto.devops.framework.common.validator.NotNullValidator;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.service.AsyncImpExpService;
import com.zto.devops.qc.application.converter.AutomaticSourceRecordConverter;
import com.zto.devops.qc.application.converter.TestcaseAdapterConverter;
import com.zto.devops.qc.client.enums.issue.AttachmentDocumentTypeEnum;
import com.zto.devops.qc.client.enums.issue.TagTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.*;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.*;
import com.zto.devops.qc.client.service.testmanager.cases.ITestcaseService;
import com.zto.devops.qc.client.service.testmanager.cases.model.*;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.AutomaticSourceRecordRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.domain.service.*;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWSsoAuth;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.ZsmpParam;
import com.zto.zsmp.annotation.ZsmpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@ZsmpService(name = "用例接口目录")
public class TestcaseServiceImpl extends GatewayBase implements ITestcaseService {

    @Autowired
    private TestcaseStepQueryDomainService testcaseStepQueryDomainService;

    @Autowired
    private TestcaseRelationQueryDomainService testcaseRelationQueryDomainService;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private TestcaseCommandDomainService testcaseCommandDomainService;

    @Autowired
    private TestcaseQueryDomainService testcaseQueryDomainService;

    @Autowired
    private TagQueryDomainService tagQueryDomainService;

    @Autowired
    private TestcaseAdapterConverter testcaseAdapterConverter;

    @Autowired
    private AutomaticTaskCommandDomainService automaticTaskCommandDomainService;

    @Autowired
    private AutomaticSourceRecordConverter automaticSourceRecordConverter;

    @Autowired
    private TestcaseTagCommendDomainService testcaseTagCommendDomainService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private AsyncImpExpService asyncImpExpService;

    @Autowired
    private QcConfigBasicService config;
    @Autowired
    private AutomaticSourceRecordRepository automaticSourceRecordRepository;

    @Autowired
    private TestcaseAttachentCommandDomainService testcaseAttachentCommandDomainService;

    @Autowired
    private IProjectRpcService projectRpcService;

    @Override
    public List<TestcaseByBusinessCodeVO> listRelationTestCaseQuery(ListRelationTestCaseQuery query) {
        return testcaseRelationQueryDomainService.listRelationTestCaseQuery(query);
    }

    @ZApiOperation(description = "测试管理-获取执行用例详情",
            apiName = "tm/cases/getExecuteCaseDetail", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<ExecuteCaseResp> executeCaseDetail(ExecuteCaseReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getTestcaseCode(), new NotBlankValidator("用例"))
                .on(req.getAutomaticTaskCode(), new NotBlankValidator("自动化任务"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        FindExecuteCaseQuery query = new FindExecuteCaseQuery();
        query.setTestcaseCode(req.getTestcaseCode());
        query.setAutomaticTaskCode(req.getAutomaticTaskCode());
        ExecuteCaseVO vo = testcaseQueryDomainService.findExecuteCaseQuery(query);
        ExecuteCaseResp resp = testcaseAdapterConverter.converter(vo);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "测试管理-获取用例执行历史",
            apiName = "tm/cases/getExecuteRecordList", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<TestcaseExecuteRecordVO> getExecuteRecordList(ListTestcaseExecuteRecordReq req) {
        if (StringUtils.isBlank(req.getCode())) {
            throw new ServiceException("用例不能为空！");
        }
        PageExecuteRecordQuery query = testcaseAdapterConverter.converter(req);
        GatewayContext.fillCurrentUser(query);
        ListTestcaseExecuteRecordVO recordVO = testcaseQueryDomainService.getExecuteRecords(query);
        return StatusCode.OK.build(recordVO.getList(), req.getPage(), req.getSize(), recordVO.getTotal());
    }

    @ZApiOperation(description = "测试管理-导出用例列表",
            apiName = "tm/cases/exportTestcase", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addExportTestcase(ListTestcaseReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getExportType(), new NotNullValidator("导出方式"))
                .on(req.getCheckCodeList(), new NotNullValidator("勾选测试用例"))
                .when(ExportTestCaseEnum.CHECK.equals(req.getExportType()))
                .on(req.getProductCode(), new NotBlankValidator("产品"))
                .on(req.getGroupType(), new NotNullValidator("分组类型"))
                .on(req.getParentCode(), new NotBlankValidator("上级节点"))
                .when(TestcaseGroupTypeEnum.GROUP.equals(req.getGroupType()))
                .on(req.getPlanCode(), new NotBlankValidator("测试计划"))
                .when(Boolean.TRUE.equals(req.getPlanPattern()))
                .on(req.getTestStage(), new NotNullValidator("测试阶段"))
                .when(Boolean.TRUE.equals(req.getPlanPattern()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ListTestcaseExpQuery expQuery = new ListTestcaseExpQuery();
        testcaseAdapterConverter.converterQuery(req, expQuery);
        testcaseQueryDomainService.handle(expQuery);
        GatewayContext.fillCurrentUser(expQuery);
        AddExpCommand command = new AddExpCommand(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        command.setFileName(testcaseQueryDomainService.getTestcaseExpFileName());
        command.setQueryCondition(asyncImpExpService.convertMap(expQuery));
        command.setType("EXP_TEST_CASE");
        GatewayContext.fillCurrentUser(command);
        asyncImpExpService.addExpJob(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-获取测试计划用例详情",
            apiName = "tm/cases/getPlanCaseDetail", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<PlanCaseResp> planCaseDetail(PlanCaseReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCaseCode(), new NotBlankValidator("用例"))
                .on(req.getPlanCode(), new NotBlankValidator("测试计划"))
                .on(req.getTestStage(), new NotNullValidator("测试阶段"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        FindPlanCaseQuery query = new FindPlanCaseQuery();
        query.setCaseCode(req.getCaseCode());
        query.setPlanCode(req.getPlanCode());
        query.setTestStage(req.getTestStage());
        GatewayContext.fillCurrentUser(query);
        PlanCaseVO vo = testcaseQueryDomainService.planCaseDetail(query);
        PlanCaseResp resp = testcaseAdapterConverter.converter(vo);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "测试管理-获取用例关联缺陷", apiName = "tm/cases/getRelationIssueList", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<TestcaseIssueResp>> getRelationIssueList(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ServiceException("用例不能为空！");
        }
        ListRelationIssueQuery query = new ListRelationIssueQuery(code);
        List<TestcaseIssueVO> vos = testcaseQueryDomainService.handle(query);
        List<TestcaseIssueResp> list = new ArrayList<>();
        for (TestcaseIssueVO vo : vos) {
            TestcaseIssueResp resp = testcaseAdapterConverter.converter(vo);
            list.add(resp);
        }
        return StatusCode.OK.build(list);
    }


    @ZApiOperation(description = "测试管理-获取用例关联需求", apiName = "tm/cases/getRelationRequirementList", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<TestcaseRequirementResp>> getRelationRequirementList(List<String> codeList, DomainEnum domain) {
        if (CollectionUtils.isEmpty(codeList)) {
            throw new ServiceException("用例不能为空！");
        }
        ListRelationRequirementQuery query = new ListRelationRequirementQuery();
        query.setCodeList(codeList);
        query.setDomain(domain);
        List<TestcaseRequirementVO> vos = testcaseQueryDomainService.handle(query);

        if (CollectionUtils.isEmpty(vos)) {
            return StatusCode.OK.build();
        }
        List<TestcaseRequirementResp> list = new ArrayList<>();
        for (TestcaseRequirementVO vo : vos) {
            TestcaseRequirementResp resp = testcaseAdapterConverter.converter(vo);
            list.add(resp);
        }
        return StatusCode.OK.build(list);
    }


    @ZApiOperation(description = "用例管理-查询所有分组和用例",
            apiName = "tm/cases/getTestcaseAll", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<ListTestcaseResp>> getTestcaseAll(ListTestcaseReq req) {
        checkTestCaseParams(req);
        TestcaseQuery query = new TestcaseQuery();
        testcaseAdapterConverter.converterPage(req, query);
        GatewayContext.fillCurrentUser(query);
        List<ListTestcaseVO> vos = testcaseQueryDomainService.handle(query);
        List<ListTestcaseResp> respList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(vos)) {
            respList = testcaseAdapterConverter.converterList(vos);
        }
        return StatusCode.OK.build(respList);
    }

    @ZApiOperation(description = "测试管理-获取用例详情",
            apiName = "tm/cases/getTestcaseDetail", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<TestcaseResp> detail(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ServiceException("用例不能为空！");
        }
        FindTestcaseQuery query = new FindTestcaseQuery(code);
        GatewayContext.fillCurrentUser(query);
        TestcaseVO vo = testcaseQueryDomainService.handle(query);
        TestcaseResp resp = testcaseAdapterConverter.converter(vo);
        return StatusCode.OK.build(resp);
    }


    @ZApiOperation(description = "批量测试用例加入批量测试计划接口", apiName = "tm/case/addBatchCaseInBatchTestPlan", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addBatchCaseInBatchTestPlan(AddBatchCaseInBatchTestPlanReq req) {
        testcaseCommandDomainService.batchJoinPlan(req.getCaseList(), GatewayContext.currentUser().toSimpleUser());
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-添加附件", apiName = "tm/cases/addAttachment", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addAttachment(AddTestcaseAttachmentReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getBusinessCode(), new NotBlankValidator("用例"))
                .on(req.getRemoteFileId(), new NotBlankValidator("附件地址"))
                .on(req.getRemoteFileId(), new LengthValidator(0, 1000, "附件地址"))
                .on(req.getName(), new NotBlankValidator("附件名称"))
                .on(req.getName(), new LengthValidator(0, 100, "附件名称"))
                .on(req.getType(), new NotNullValidator("附件类型"))
                .on(req.getSize(), new NotBlankValidator("附件大小"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (!checkAttachmentSize(req.getSize())) {
            throw new ServiceException("附件大小不超过100M！");
        }
        String code = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddTestcaseAttachmentCommand command = new AddTestcaseAttachmentCommand(code);
        command.setDomain(DomainEnum.TESTCASE);
        command.setBusinessCode(req.getBusinessCode());
        command.setUrl(req.getUrl());
        command.setRemoteFileId(req.getRemoteFileId());
        command.setName(req.getName());
        command.setType(req.getType());
        command.setDocumentType(AttachmentDocumentTypeEnum.BUSINESS);
        command.setFileType(req.getFileType());
        command.setSize(req.getSize());
        command.setOperateCaseCode(req.getOperateCaseCode());
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.addTestcaseAttachment(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-添加标签", apiName = "tm/cases/addTag", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addTag(AddTestcaseTagReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getBusinessCode(), new NotBlankValidator("业务编码"))
                .on(req.getTagName(), new NotBlankValidator("标签"))
                .on(req.getTagName(), new LengthValidator(0, 10, "标签"))
                .on(req.getDomain(), new NotNullValidator("领域"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        String code = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddTestcaseTagCommand command = new AddTestcaseTagCommand(code);
        command.setBusinessCode(req.getBusinessCode());
        command.setTagName(req.getTagName());
        command.setDomain(req.getDomain());
        command.setType(TagTypeEnum.BUSINESS);
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.addTestcaseTag(command);
        return StatusCode.OK.build();
    }


    @ZApiOperation(description = "测试管理-用例关联业务", apiName = "tm/cases/associateDomain", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> associateTestcaseDomain(AssociateTestcaseDomainReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCode(), new NotBlankValidator("用例"))
                .on(req.getDomain(), new NotNullValidator("领域"))
                .onEach(req.getCodeList(), new NotBlankValidator("关联业务"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (CollectionUtils.isEmpty(req.getCodeList())) {
            return StatusCode.OK.build();
        }
        AssociateTestcaseDomainCommand command = new AssociateTestcaseDomainCommand(req.getCode());
        command.setDomain(req.getDomain());
        command.setCodeList(req.getCodeList());
        command.setOperateCaseCode(req.getOperateCaseCode());
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.associateTestcaseDomain(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-批量添加标签", apiName = "tm/cases/batchAddTag", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchAddTag(BatchAddTestcaseTagReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getBusinessCodes(), new NotNullValidator("业务编码"))
                .on(req.getTagNames(), new NotNullValidator("标签"))
                .on(req.getDomain(), new NotNullValidator("领域"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        String code = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        BatchAddTestcaseTagCommand command = new BatchAddTestcaseTagCommand(code);
        command.setBusinessCodes(req.getBusinessCodes());
        command.setTagNames(req.getTagNames());
        command.setDomain(req.getDomain());
        command.setType(TagTypeEnum.BUSINESS);
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.addBatchTestcaseTag(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-用例管理批量变更用例状态", apiName = "tm/cases/batchChangeTestcaseStatus", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchChangeTestcaseStatus(BatchChangeTestcaseStatusReq req) {
        if (req.getStatus().equals(TestcaseStatusEnum.DISABLE) && req.getAbandonReason() == null) {
            throw new ServiceException("已停用需填写停用原因");
        }
        if (req.getStatus().equals(TestcaseStatusEnum.NORMAL) && ((null != req.getAutoFlag()) && !req.getAutoFlag())) {
            throw new ServiceException("手工用例暂不支持批量启用");
        }
        ComplexResult cr = FluentValidator.checkAll()
                .onEach(req.getCodeList(), new BatchOperateCaseValidatorHandler())
                .on(req.getStatus(), new NotNullValidator("状态"))
                .on(req.getAbandonReason(), new NotNullValidator("停用原因"))
                .when(TestcaseStatusEnum.DISABLE.equals(req.getStatus()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        //手工用例批量停用，自动化用例支持批量启用/停用
        testcaseCommandDomainService.batchChangeCaseStatus(req.getCodeList()
                        .stream().map(BatchOperateCaseVO::getCode)
                        .collect(Collectors.toList()),
                req.getStatus(), req.getAbandonReason(), GatewayContext.currentUser().toSimpleUser());
        return StatusCode.OK.build();
    }


    @ZApiOperation(description = "测试管理-用例管理-修改版本（批量）",
            apiName = "tm/cases/batchChangeVersion", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchChangeVersion(BatchChangeVersionReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getTargetVersionCode(), new NotBlankValidator("目标版本code不能为空"))
                .on(req.getUseOriginalGroup(), new NotNullValidator("是否复用原分组标识不能为空"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        testcaseCommandDomainService.batchChangeVersion(req.getCodeList(), req.getTargetVersionCode(),
                req.getTargetParentCode(), req.getUseOriginalGroup(), req.getDeleteOriginalGroup(), GatewayContext.currentUser().toSimpleUser());
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-用例管理批量复制用例", apiName = "tm/cases/batchCopyTestcase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchCopyTestcase(BatchCopyTestcaseReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .onEach(req.getCodeList(), new BatchOperateCaseValidatorHandler())
                .on(req.getParentCode(), new NotNullValidator("父code不能为空"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        List<String> caseCodes = req.getCodeList()
                .stream()
                .map(BatchOperateCaseVO::getCode)
                .collect(Collectors.toList());
        testcaseCommandDomainService.batchCopyCase(caseCodes, req.getParentCode(), req.getSetCore(),
                GatewayContext.currentUser().toSimpleUser());
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-用例管理批量修改责任人", apiName = "tm/cases/batchModifyCaseDutyUser", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchModifyCaseDutyUser(BatchModifyCaseDutyUserReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getDutyUser(), new NotBlankValidator("责任人"))
                .on(req.getDutyUserId(), new NotNullValidator("责任人"))
                .onEach(req.getCodeList(), new BatchOperateCaseValidatorHandler())
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        User dutyUser = new User(req.getDutyUserId(), req.getDutyUser());
        // 批量更改负责人
        testcaseCommandDomainService.batchModifyCaseDutyUser(req.getCodeList().stream().map(BatchOperateCaseVO::getCode).collect(Collectors.toList()),
                dutyUser,
                GatewayContext.currentUser().toSimpleUser());
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-用例管理批量修改用例等级", apiName = "tm/cases/batchModifyCaseGrade", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchModifyCaseGrade(BatchModifyCaseGradeReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getPriority(), new NotNullValidator("等级"))
                .onEach(req.getCodeList(), new BatchOperateCaseValidatorHandler())
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        testcaseCommandDomainService.batchModifyCaseGrade(req.getCodeList()
                        .stream()
                        .map(BatchOperateCaseVO::getCode).collect(Collectors.toList()),
                req.getPriority(),
                GatewayContext.currentUser().toSimpleUser());
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-用例管理批量移动手工用例", apiName = "tm/cases/batchMoveTestcaseModel", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchMoveTestcaseModel(BatchMoveTestcaseModelReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getParentCode(), new NotNullValidator("父code不能为空"))
                .onEach(req.getCodeList(), new BatchOperateCaseValidatorHandler())
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        testcaseCommandDomainService.batchMoveCase(req.getCodeList().stream().map(BatchOperateCaseVO::getCode).collect(Collectors.toList()),
                req.getParentCode(),
                req.getSetCore(),
                GatewayContext.currentUser().toSimpleUser());
        return StatusCode.OK.build();
    }


    @ZApiOperation(description = "测试管理-用例管理批量删除用例", apiName = "tm/cases/batchRemoveTestcase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchRemoveTestcase(BatchRemoveTestcaseReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .onEach(req.getCodeList(), new BatchOperateCaseValidatorHandler())
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        testcaseCommandDomainService.batchRemoveCase(req.getCodeList().stream().map(BatchOperateCaseVO::getCode).collect(Collectors.toList()),
                GatewayContext.currentUser().toSimpleUser());
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-用例管理设置核心用例（批量）",
            apiName = "tm/cases/batchSetCore", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchSetCore(BatchSetCoreReq req) {
        testcaseCommandDomainService.batchSetCore(req.getUseOriginalGroup(), req.getCodeList(), req.getParentCode(),
                GatewayContext.currentUser().toSimpleUser());
        return StatusCode.OK.build();
    }


    @ZApiOperation(description = "测试管理-变更测试计划用例执行人", apiName = "tm/cases/changePlanCaseExecutor", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> changePlanCaseExecutor(ChangePlanCaseExecutorReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCaseCode(), new NotBlankValidator("用例"))
                .on(req.getPlanCode(), new NotBlankValidator("测试计划"))
                .on(req.getTestStage(), new NotNullValidator("测试阶段"))
                .on(req.getExecutor(), new NotBlankValidator("执行人"))
                .on(req.getExecutorId(), new NotNullValidator("执行人"))
                .on(req.getOperateCaseCode(), new NotBlankValidator("日志记录"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ChangePlanCaseExecutorCommand command = new ChangePlanCaseExecutorCommand(req.getCaseCode());
        command.setPlanCode(req.getPlanCode());
        command.setTestStage(req.getTestStage());
        command.setExecutor(req.getExecutor());
        command.setExecutorId(req.getExecutorId());
        command.setOperateCaseCode(req.getOperateCaseCode());
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.changePlanCaseExecutor(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-变更用例责任人", apiName = "tm/cases/changeTestcaseDutyUser", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> changeTestcaseDutyUser(ChangeTestcaseDutyUserReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCode(), new NotBlankValidator("用例"))
                .on(req.getDutyUser(), new NotBlankValidator("责任人"))
                .on(req.getDutyUserId(), new NotNullValidator("责任人"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ChangeTestcaseDutyUserCommand command = new ChangeTestcaseDutyUserCommand(req.getCode());
        command.setDutyUser(req.getDutyUser());
        command.setDutyUserId(req.getDutyUserId());
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.changeTestcaseDutyUser(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-查询标签下的用例数量", apiName = "tm/cases/tagCasesNo", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Long> tagCasesNo(@ZsmpParam(description = "标签唯一标识", sample = "SNF994641594810368000", required = true) String code,
                                   @ZsmpParam(description = "产品唯一标识", sample = "399", required = true) String productCode) {
        if (StringUtils.isBlank(code)) {
            throw new ServiceException("标签不能为空！");
        }
        FindTestcaseTagQuery query = new FindTestcaseTagQuery(code);
        TagVO vo = tagQueryDomainService.findTestcaseTagQuery(query);
        if (null == vo) {
            return StatusCode.OK.build();
        }
        FindTagTestcaseNoQuery queryNo = new FindTagTestcaseNoQuery(vo.getTagName(), DomainEnum.TESTCASE, productCode);
        Long no = tagQueryDomainService.findTagTestcaseNoQuery(queryNo);
        return StatusCode.OK.build(no);
    }

    @ZApiOperation(description = "测试管理-xmind用例新增", apiName = "tm/cases/xmindAdd", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<String> xmindAdd(XmindCaseAddReq req) {
        XmindCaseAddCommand command = testcaseAdapterConverter.converter(req);
        GatewayContext.fillCurrentUser(command);
        if (command.getAttribute() != null) {
            if (command.getAttribute().equals(TestcaseAttributeEnum.MODULE)) {
                // 新增分组
                AddTestcaseReq addReq = buildAddTestModuleReq(command);
                // 校验分组层级
                checkModuleLayer(addReq);
                String nodeId = addTestCaseOrModule(addReq);
                return StatusCode.OK.build(nodeId);
            } else if (command.getAttribute().equals(TestcaseAttributeEnum.TESTCASE)) {
                // 新增用例
                AddTestcaseReq addReq = buildAddTestcaseReq(command);
                String nodeId = addTestCaseOrModule(addReq);
                return StatusCode.OK.build(nodeId);
            }
        }
        // 新增步骤
        if (req.getParentCode().equals(TestcaseGroupTypeEnum.ALL.name())
                || req.getParentCode().equals(TestcaseGroupTypeEnum.NO_GROUP.name())) {
            throw new ServiceException("此处不能添加步骤！");
        }
        command.setAggregateId(req.getParentCode());
        testcaseCommandDomainService.xmindCaseAddCommand(command);
        return StatusCode.OK.build(getStepSortId(command));
    }

    @ZApiOperation(description = "测试管理-xmind查询", apiName = "tm/cases/xmindDetail", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<ListXmindDetailResp> getXmindDetail(ListXmindDetailReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品"))
                .on(req.getGroupType(), new NotNullValidator("分组类型"))
                .on(req.getParentCode(), new NotBlankValidator("上级节点"))
                .when(TestcaseGroupTypeEnum.GROUP.equals(req.getGroupType()))
                .on(req.getPlanCode(), new NotBlankValidator("测试计划"))
                .when(Boolean.TRUE.equals(req.getPlanPattern()))
                .on(req.getTestStage(), new NotNullValidator("测试阶段"))
                .when(Boolean.TRUE.equals(req.getPlanPattern()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new ServiceException(cr.getErrors().get(0).getErrorMsg());
        }
        checkTestCaseCode(req);
        PageXmindDetailQuery query = testcaseAdapterConverter.converterPageXmindDetailQuery(req);
        GatewayContext.fillCurrentUser(query);
        Page page = testcaseQueryDomainService.pageXmindDetailQuery(query);
        List<ListXmindDetailResp> respList = new ArrayList<>();
        if (null != page && CollectionUtil.isNotEmpty(page.getList())) {
            respList = testcaseAdapterConverter.convertList(page.getList());
        }
        return StatusCode.OK.build(respList, req.getPage(), req.getSize(), page.getTotal());
    }

    @ZApiOperation(description = "测试管理-xmind一键展开", apiName = "tm/cases/queryAllXmindCase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<ListXmindDetailResp>> queryAllXmindCase(QueryAllXmindCaseReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品"))
                .on(req.getVersionCode(), new NotNullValidator("版本"))
                .on(req.getParentCode(), new NotBlankValidator("上级节点"))
                .on(req.getSetCore(), new NotNullValidator("是否核心用例"))
                .on(req.getType(), new NotNullValidator("用例类型"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new ServiceException(cr.getErrors().get(0).getErrorMsg());
        }
        return StatusCode.OK.build(testcaseQueryDomainService.queryAllXmindCase(req));
    }

    @ZApiOperation(description = "测试管理-移动手工用例", apiName = "tm/cases/moveTestcase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> moveTestcase(MoveTestcaseModelReq req) {
        if (StringUtils.isBlank(req.getCode())) {
            throw new ServiceException("用例不能为空！");
        }
        MoveTestcaseCommand command = new MoveTestcaseCommand(req.getCode());
        if (TestcaseGroupTypeEnum.NO_GROUP.name().equals(req.getParentCode())) {
            command.setParentCode(StringUtils.EMPTY);
        } else {
            command.setParentCode(StringUtils.defaultString(req.getParentCode()));
        }
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.moveTestcase(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-查询分组下所有用例", apiName = "tm/cases/listTestcaseCode", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<String>> listTestcaseCode(ListTestcaseCodeReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品"))
                .on(req.getType(), new NotNullValidator("类型"))
                .on(req.getPlanCode(), new NotBlankValidator("测试计划"))
                .when(Boolean.TRUE.equals(req.getPlanPattern()))
                .on(req.getTestStage(), new NotNullValidator("测试阶段"))
                .when(Boolean.TRUE.equals(req.getPlanPattern()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ListTestcaseCodeQuery query = new ListTestcaseCodeQuery();
        testcaseAdapterConverter.converter(req, query);
        List<String> codeList = testcaseQueryDomainService.listTestcaseCode(query);
        return StatusCode.OK.build(codeList);
    }

    @ZApiOperation(description = "测试管理-查询用例模块树列表",
            apiName = "tm/cases/listModule", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<ListTestcaseModuleResp>> listModule(ListTestcaseModuleReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getType(), new NotNullValidator("类型"))
                .on(req.getProductCode(), new NotBlankValidator("产品"))
                .on(req.getPlanCode(), new NotBlankValidator("测试计划"))
                .when(Boolean.TRUE.equals(req.getPlanPattern()))
                .on(req.getTestStage(), new NotNullValidator("测试阶段"))
                .when(Boolean.TRUE.equals(req.getPlanPattern()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ListTestcaseModuleQuery query = new ListTestcaseModuleQuery();
        testcaseAdapterConverter.converter(req, query);
        GatewayContext.fillCurrentUser(query);
        List<ListTestcaseModuleVO> vos = testcaseQueryDomainService.listModule(query);
        List<ListTestcaseModuleResp> list = new ArrayList<>();
        for (ListTestcaseModuleVO vo : vos) {
            ListTestcaseModuleResp resp = testcaseAdapterConverter.converter(vo);
            list.add(resp);
        }
        return StatusCode.OK.build(list);
    }

    @ZApiOperation(description = "测试管理-查询用例列表(懒加载+分页)",
            apiName = "tm/cases/pageTestcase",
            timeout = 5000,
            namespace = "luban",
            responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<ListTestcaseResp> pageTestcase(ListTestcaseReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品"))
                .on(req.getGroupType(), new NotNullValidator("分组类型"))
                .on(req.getParentCode(), new NotBlankValidator("上级节点"))
                .when(TestcaseGroupTypeEnum.GROUP.equals(req.getGroupType()))
                .on(req.getPlanCode(), new NotBlankValidator("测试计划"))
                .when(Boolean.TRUE.equals(req.getPlanPattern()))
                .on(req.getTestStage(), new NotNullValidator("测试阶段"))
                .when(Boolean.TRUE.equals(req.getPlanPattern()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new ServiceException(cr.getErrors().get(0).getErrorMsg());
        }
        PageTestcaseQuery query = new PageTestcaseQuery();
        testcaseAdapterConverter.converterPage(req, query);
        GatewayContext.fillCurrentUser(query);
        Page page = testcaseQueryDomainService.pageTestcase(query);
        if (CollectionUtils.isNotEmpty(page.getList()) && !TestcaseTypeEnum.MANUAL.equals(req.getType())) {
            // 设置心跳按钮权限
            testcaseQueryDomainService.buildButtonPermission(page.getList(), req, GatewayContext.currentUser());
        }
        List<ListTestcaseResp> respList = new ArrayList<>();
        if (null != page && CollectionUtil.isNotEmpty(page.getList())) {
            respList = testcaseAdapterConverter.converterList(page.getList());
        }
        return StatusCode.OK.build(respList, req.getPage(), req.getSize(), page.getTotal());
    }

    @ZApiOperation(description = "测试管理-拖曳用例分组", apiName = "tm/cases/dragModule", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> dragModule(DragModuleReq req) {
        ComplexResult cr = checkAll()
                .on(req.getCode(), new NotBlankValidator("节点code"))
                .on(req.getParentCode(), new NotNullValidator("上级code"))
                .on(req.getTargetCode(), new NotNullValidator("目标code"))
                .on(req.getAction(), new NotNullValidator("action"))
                .on(req.getType(), new NotNullValidator("类型"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (TestcaseGroupTypeEnum.ALL.name().equals(req.getParentCode())) {
            req.setParentCode("");
        }
        if (DragModuleActionEnum.INNER.equals(req.getAction())) {
            req.setParentCode(req.getTargetCode());
        }
        if (TestcaseTypeEnum.SOURCERECORD.equals(req.getType())) {
            DragAutomaticRecordCommand command = new DragAutomaticRecordCommand(req.getCode());
            command.setParentCode(req.getParentCode());
            command.setTargetCode(req.getTargetCode());
            command.setAction(req.getAction());
            GatewayContext.fillCurrentUser(command);
            testcaseCommandDomainService.dragAutomaticRecord(command);
        } else {
            DragTestcaseModuleCommand command = new DragTestcaseModuleCommand(req.getCode());
            command.setParentCode(req.getParentCode());
            command.setTargetCode(req.getTargetCode());
            command.setAction(req.getAction());
            command.setType(req.getType());
            GatewayContext.fillCurrentUser(command);
            testcaseCommandDomainService.dragTestcaseModuleCommand(command);
        }
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-xmind用例编辑/删除", apiName = "tm/cases/xmindEdit", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> xmindEdit(XmindCaseEditReq req) {
        if (StringUtil.isEmpty(req.getId())) {
            throw new ServiceException("节点id不能为空！");
        }
        if (req.getOperation().equals(XmindOperationEnum.UPDATE)) {
            if (StringUtil.isEmpty(req.getProductCode())) {
                throw new ServiceException("产品code不能为空！");
            }
            this.checkEditParams(req);
        }
        // 删除手工用例或模块
        if (req.getAttribute() != null && XmindOperationEnum.DELETE.equals(req.getOperation())) {
            if (TestcaseGroupTypeEnum.ALL.name().equals(req.getId())
                    || TestcaseGroupTypeEnum.NO_GROUP.name().equals(req.getId())) {
                throw new ServiceException("全部用例节点和未分组用例节点不能删除！");
            }
            deleteTestcase(req.getId(), req.getAttribute().name());
            return StatusCode.OK.build();
        }
        XmindCaseEditCommand command = testcaseAdapterConverter.converter(req);
        command.setAggregateId(this.getParentCode(req));
        GatewayContext.fillCurrentUser(command);
        if (req.getOperation().equals(XmindOperationEnum.CUT) || req.getOperation().equals(XmindOperationEnum.COPY)) {
            testcaseCommandDomainService.copyOrCutModuleAndTestcase(command);
            return StatusCode.OK.build();
        }
        // 修改其他节点
        testcaseCommandDomainService.xmindCaseEditCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "删除手工用例or模块",
            apiName = "tm/testcase/deleteTestcase", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> deleteTestcase(String code, String attribute) {
        TestcaseAttributeEnum attributeEnum = TestcaseAttributeEnum.getEnumByName(attribute);
        if (null == attributeEnum) {
            throw new ServiceException("没有该数据类型！");
        }
        //校验类型
        FindTestcaseEntityByCodeQuery query = new FindTestcaseEntityByCodeQuery();
        query.setCode(code);
        TestcaseVO dto = testcaseQueryDomainService.findTestcaseEntityByCodeQuery(query);
        if (null == dto) {
            return StatusCode.OK.build();
        }
        if (!dto.getAttribute().name().equals(attributeEnum.name())) {
            throw new ServiceException("数据类型不匹配！");
        }
        DeleteTestcaseCommand command = new DeleteTestcaseCommand(code);
        command.setAttribute(attributeEnum);
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.deleteTestcaseCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "批量删除xmind用例or模块",
            apiName = "tm/cases/batchXmindDelete", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchXmindDelete(BatchXmindDeleteReq req) {
        if(CollectionUtils.isEmpty(req.getCodeList())) {
            throw new ServiceException("用例或分组List不能为空！");
        }
        User user = GatewayContext.currentUser().toSimpleUser();
        testcaseCommandDomainService.batchXmindDelete(req, user);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-xmind筛选接口", apiName = "tm/cases/xmindFilter", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<String>> xmindFilter(ListXmindDetailReq req) {
        checkTestCaseParams(req);
        checkTestCaseCode(req);
        XmindFilterQuery query = testcaseAdapterConverter.convert(req);
        GatewayContext.fillCurrentUser(query);
        List<String> list = testcaseQueryDomainService.xmindFilterQuery(query);
        return StatusCode.OK.build(list);
    }

    /**
     * 校验附件大小
     *
     * @param size 附件大小 byte
     * @return true/false
     */
    private boolean checkAttachmentSize(String size) {
        size = size.replace("KB", "");
        if (!NumberUtils.isParsable(size)) {
            return false;
        }
        double kb = Double.parseDouble(size);
        double mb = kb / 1024;
        return mb >= 0 && mb <= 100;
    }

    private AddTestcaseReq buildAddTestModuleReq(XmindCaseAddCommand command) {
        AddTestcaseReq addReq = new AddTestcaseReq();
        addReq.setAttribute(command.getAttribute());
        addReq.setLayer(command.getLayer());
        addReq.setName(command.getTopic());
        addReq.setParentCode(command.getParentCode());
        addReq.setProductCode(command.getProductCode());
        addReq.setType(TestcaseTypeEnum.MANUAL);
        addReq.setSetCore(command.getSetCore());
        addReq.setVersionCode(command.getVersionCode());
        return addReq;
    }

    private void checkModuleLayer(AddTestcaseReq req) {
        //判断层级是否达到十层
        if (org.apache.commons.lang3.StringUtils.isEmpty(req.getParentCode()) ||
                TestcaseGroupTypeEnum.ALL.name().equals(req.getParentCode())) {
            return;
        }
        FindCaseOrModuleByCodeQuery query = new FindCaseOrModuleByCodeQuery(req.getParentCode());
        TestcaseVO vo = testcaseQueryDomainService.findCaseOrModuleByCodeQuery(query);
        if (vo == null) {
            throw new ServiceException("上一级节点不存在！");
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(vo.getPath())) {
            return;
        }
        int layer = vo.getPath().split("\\.").length + 1;
        if (layer > 10) {
            throw new ServiceException("层级结构不能超过十层！");
        }
    }

    private String addTestCaseOrModule(AddTestcaseReq req) {
        // 参数校验
        checkAddTestcaseReq(req);
        AddTestcaseCommand command = testcaseAdapterConverter.convertor(req);
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.addTestcaseCommand(command);
        return command.getAggregateId();
    }

    private void checkAddTestcaseReq(AddTestcaseReq req) {
        ComplexResult cr = checkAll()
                .on(req.getName(), new NotBlankValidator("标题"))
                .on(req.getProductCode(), new NotBlankValidator("产品"))
                .on(req.getAttribute(), new NotNullValidator("属性"))
                .on(req.getType(), new NotNullValidator("类型"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        checkTagListSize(req.getTags());
        this.checkModel(req);
        //标题查重
        this.queryTestcaseByName(req, true);
    }

    private void checkTagListSize(List<TagVO> tags) {
        if (CollectionUtil.isNotEmpty(tags) && tags.size() > 10) {
            throw new ServiceException("关联的用例标签不能超过十个！");
        }
    }

    private void checkModel(AddTestcaseReq req) {
        if (StringUtil.isNotBlank(req.getName()) && req.getAttribute() != null) {
            req.setName(req.getName().trim());
            if (TestcaseAttributeEnum.TESTCASE.equals(req.getAttribute()) && req.getName().length() > 150) {
                throw new ServiceException("用例标题不能超过150字！");
            }
            if (TestcaseAttributeEnum.MODULE.equals(req.getAttribute()) && req.getName().length() > 60) {
                throw new ServiceException("分组名称不能超过60字！");
            }
        }
        if (StringUtil.isNotBlank(req.getPrecondition()) && req.getPrecondition().length() > 200) {
            throw new ServiceException("前置条件不能超过200字！");
        }
        if (StringUtil.isNotBlank(req.getComment()) && req.getComment().length() > 200) {
            throw new ServiceException("备注不能超过200字！");
        }
        if (null != req.getLayer() && req.getLayer() > 10) {
            throw new ServiceException("层级结构不能超过十层！");
        }
        if (null != req.getAttachments() && req.getAttachments().size() > 0) {
            for (AttachmentVO at : req.getAttachments()) {
                if (at.getName().length() > 100) {
                    throw new ServiceException("上传文件名称不能超过100字！");
                }
                if (at.getUrl().length() > 1000) {
                    throw new ServiceException("上传文件url不能超过1000字！");
                }
            }
        }
        if (null != req.getTags() && req.getTags().size() > 0) {
            for (TagVO tag : req.getTags()) {
                if (tag.getTagName().length() > 10) {
                    throw new ServiceException("标签名称不能超过10字！");
                }
            }
        }
        if (null != req.getTestSteps() && req.getTestSteps().size() > 0) {
            for (TestcaseStepVO fu : req.getTestSteps()) {
                ComplexResult c = checkAll()
                        .on(fu.getStepDesc(), new LengthValidator(-1, 200, "步骤描述"))
                        .on(fu.getExpectResult(), new LengthValidator(-1, 1000, "预期结果"))
                        .doValidate()
                        .result(ResultCollectors.toComplex());
                if (!c.isSuccess()) {
                    throw new FluentException(c.getErrors());
                }
            }
        }
        if (TestcaseGroupTypeEnum.NO_GROUP.name().equals(req.getParentCode())
                || TestcaseGroupTypeEnum.ALL.name().equals(req.getParentCode()) || null == req.getParentCode()) {
            req.setParentCode("");
        }
    }

    //Boolean true 新增  false 修改
    private void queryTestcaseByName(AddTestcaseReq req, Boolean bo) {
        if (StringUtil.isEmpty(req.getName())) {
            return;
        }
        FindTestcaseByNameQuery query = new FindTestcaseByNameQuery();
        query.setName(req.getName());
        query.setParentCode(req.getParentCode());
        query.setAttribute(req.getAttribute());
        query.setType(req.getType());
        query.setProductCode(req.getProductCode());
        query.setSetCore(req.getSetCore());
        query.setVersionCode(req.getVersionCode());
        List<TestcaseVO> testcaseVOList = testcaseQueryDomainService.findTestcaseByNameQuery(query);
        if (null != testcaseVOList && testcaseVOList.size() > 0) {
            if (!bo && testcaseVOList.size() == 1 && req.getName().equals(testcaseVOList.get(0).getName()) && req.getCode().equals(testcaseVOList.get(0).getCode())) {
                return;
            } else {
                throw new ServiceException("名称不能重复");
            }
        }
    }

    private AddTestcaseReq buildAddTestcaseReq(XmindCaseAddCommand command) {
        AddTestcaseReq addReq = buildAddTestModuleReq(command);
        addReq.setDutyUser(command.getTransactor().getUserName());
        addReq.setDutyUserId(command.getTransactor().getUserId());
        addReq.setPriority(command.getPriority());
        addReq.setComment("");
        addReq.setPrecondition("");
        List<TestcaseStepVO> testSteps = new ArrayList<>();
        Integer sort = 1;
        while (testSteps.size() < 3) {
            TestcaseStepVO stepVO = new TestcaseStepVO();
            stepVO.setStepDesc("");
            stepVO.setExpectResult("");
            stepVO.setSort(sort);
            testSteps.add(stepVO);
            sort++;
        }
        addReq.setTestSteps(testSteps);
        addReq.setSetCore(command.getSetCore());
        addReq.setVersionCode(command.getVersionCode());
        return addReq;
    }

    private String getStepSortId(XmindCaseAddCommand command) {
        ListTestcaseStepQuery stepQuery = new ListTestcaseStepQuery(command.getParentCode());
        List<TestcaseStepVO> list = testcaseStepQueryDomainService.listTestcaseStepQuery(stepQuery);
        int sort = 1;
        if (CollectionUtil.isNotEmpty(list)) {
            sort = list.get(list.size() - 1).getSort();
        }
        return command.getAggregateId() + TestCaseTagNameEnum.STEP.name() + sort;
    }

    private void checkTestCaseCode(ListXmindDetailReq req) {
        //查询某个版本，未分组用例
        if (StringUtils.isNotBlank(req.getCaseCode()) && req.getCaseCode().startsWith("no_group_plan_case_module___")) {
            req.setVersionCode(req.getCaseCode().replace("no_group_plan_case_module___", ""));
            req.setCaseCode(null);
            req.setParentCode("");
            req.setGroupType(TestcaseGroupTypeEnum.NO_GROUP);
            return;
        }
        if (StringUtils.isNotBlank(req.getCaseCode()) && req.getCaseCode().startsWith(TestcaseGroupTypeEnum.NO_GROUP.name() + "___")) {
            req.setVersionCode(req.getCaseCode().replace(TestcaseGroupTypeEnum.NO_GROUP.name() + "___", ""));
            req.setCaseCode(null);
            req.setParentCode("");
            req.setGroupType(TestcaseGroupTypeEnum.NO_GROUP);
            return;
        }

        //测试计划-某个版本用例
        if (StringUtils.isNotBlank(req.getCaseCode()) && req.getCaseCode().contains("VER") && StringUtils.isNotBlank(req.getPlanCode())) {
            req.setPlanCaseVersionCodeList(Arrays.asList(req.getCaseCode()));
            req.setVersionCode("");
            req.setCaseCode(null);
            req.setParentCode(null);
            req.setGroupType(TestcaseGroupTypeEnum.ALL);
            return;
        }

        //单独查询某个版本用例
        if (StringUtils.isNotBlank(req.getCaseCode()) && req.getCaseCode().contains("VER")) {
            req.setVersionCode(req.getCaseCode());
            req.setCaseCode(null);
            req.setParentCode(null);
            req.setGroupType(TestcaseGroupTypeEnum.ALL);
            return;
        }

        if (StringUtils.isNotEmpty(req.getCaseCode())
                && !TestcaseGroupTypeEnum.ALL.name().equals(req.getCaseCode())
                && !TestcaseGroupTypeEnum.NO_GROUP.name().equals(req.getCaseCode())) {
            // 校验用例或分组是否存在，不存在返回全部用例
            FindCaseOrModuleByCodeQuery query = new FindCaseOrModuleByCodeQuery(req.getCaseCode());
            TestcaseVO vo = testcaseQueryDomainService.findCaseOrModuleByCodeQuery(query);
            if (vo == null) {
                req.setCaseCode(null);
                req.setParentCode(null);
                req.setGroupType(TestcaseGroupTypeEnum.ALL);
            }
        }

        //查询测试计划所有用例
        if (StringUtils.isNotBlank(req.getPlanCode())) {
            List<String> versionCodeList = testcaseQueryDomainService.selectVersionCodeByPlanCode(req.getPlanCode());
            if (CollectionUtils.isNotEmpty(versionCodeList)) {
                req.setPlanCaseVersionCodeList(versionCodeList);
                req.setVersionCode("");
            }
        }
    }

    private void checkEditParams(XmindCaseEditReq req) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(req.getTopic())) {
            if (req.getAttribute() != null) {
                throw new ServiceException("分组或用例名称不能为空！");
            }
        }
        AddTestcaseReq addReq = buildAddTestcaseReq(req);
        // 格式校验
        this.checkModel(addReq);
        if (req.getAttribute() != null) {
            // 名称重复校验
            this.queryTestcaseByName(addReq, false);
        }
    }

    private AddTestcaseReq buildAddTestcaseReq(XmindCaseEditReq req) {
        AddTestcaseReq addReq = new AddTestcaseReq();
        addReq.setParentCode(req.getParentCode());
        addReq.setCode(req.getParentCode());
        if (req.getAttribute() != null) {
            addReq.setCode(req.getId());
            addReq.setName(req.getTopic());
            addReq.setAttribute(req.getAttribute());
            addReq.setType(TestcaseTypeEnum.MANUAL);
            addReq.setProductCode(req.getProductCode());
            return addReq;
        }
        if (req.getId().contains(TestCaseTagNameEnum.PRECONDITION.name())) {
            addReq.setPrecondition(req.getTopic());
        }
        if (req.getId().contains(TestCaseTagNameEnum.REMARK.name())) {
            addReq.setComment(req.getTopic());
        }
        if (req.getId().contains(TestCaseTagNameEnum.STEP.name())) {
            TestcaseStepVO vo = new TestcaseStepVO();
            vo.setStepDesc(req.getTopic());
            vo.setExpectResult("");
            List<TestcaseStepVO> testSteps = Arrays.asList(vo);
            addReq.setTestSteps(testSteps);
        }
        if (req.getId().contains(TestCaseTagNameEnum.EXPECT.name())) {
            TestcaseStepVO vo = new TestcaseStepVO();
            vo.setExpectResult(req.getTopic());
            vo.setStepDesc("");
            List<TestcaseStepVO> testSteps = Arrays.asList(vo);
            addReq.setTestSteps(testSteps);
        }
        return addReq;
    }

    /**
     * xmind编辑
     */
    private String getParentCode(XmindCaseEditReq req) {
        if (req.getId().contains(TestCaseTagNameEnum.EXPECT.name())) {
            return req.getId().split(TestCaseTagNameEnum.EXPECT.name())[0];
        }
        if (StringUtil.isEmpty(req.getParentCode())
                || TestcaseGroupTypeEnum.NO_GROUP.name().equals(req.getParentCode())
                || TestcaseGroupTypeEnum.ALL.name().equals(req.getParentCode())) {
            return req.getId();
        }
        return req.getParentCode();
    }

    private void checkTestCaseParams(ListTestcaseReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品"))
                .on(req.getGroupType(), new NotNullValidator("分组类型"))
                .on(req.getParentCode(), new NotBlankValidator("上级节点"))
                .when(TestcaseGroupTypeEnum.GROUP.equals(req.getGroupType()))
                .on(req.getPlanCode(), new NotBlankValidator("测试计划"))
                .when(Boolean.TRUE.equals(req.getPlanPattern()))
                .on(req.getTestStage(), new NotNullValidator("测试阶段"))
                .when(Boolean.TRUE.equals(req.getPlanPattern()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
    }

    @ZApiOperation(description = "新增手工用例/模块", apiName = "tm/testcase/addTestcase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addTestcase(AddTestcaseReq req) {
        checkAddTestcaseReq(req);
        AddTestcaseCommand command = testcaseAdapterConverter.convertor(req);
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.addTestcaseCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "修改手工用例/模块", apiName = "tm/testcase/editTestcase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editTestcase(AddTestcaseReq req) {
        ComplexResult cr = checkAll()
                .on(req.getCode(), new NotBlankValidator("code不能为空"))
                .on(req.getProductCode(), new NotBlankValidator("产品不能为空"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        //判断是否有数据  or 是否是默认全部用例 or 未分组的分组
        FindTestcaseEntityByCodeQuery query = new FindTestcaseEntityByCodeQuery();
        query.setCode(req.getCode());
        TestcaseVO dto = testcaseQueryDomainService.findTestcaseEntityByCodeQuery(query);
        if (Objects.isNull(dto)) {
            throw new ServiceException("未查到该数据或该数据不能修改！");
        }
        checkTagListSize(req.getTags());
        this.checkModel(req);
        this.queryTestcaseByName(req, false);
        EditTestcaseCommand command = testcaseAdapterConverter.convertEdit(req);
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.editTestcaseCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-移除标签", apiName = "tm/cases/removeTag", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> removeTag(@ZsmpParam(description = "标签唯一标识", sample = "111", required = true) String code) {
        if (StringUtils.isBlank(code)) {
            throw new ServiceException("标签不能为空！");
        }
        if (StringUtil.isNotEmpty(redisService.getKey(code))) {
            return StatusCode.OK.build();
        } else {
            redisService.setKey(code, code, 10, TimeUnit.SECONDS);
        }
        FindTestcaseTagQuery query = new FindTestcaseTagQuery(code);
        TagVO tag = tagQueryDomainService.findTestcaseTagQuery(query);
        if (null == tag) {
            return StatusCode.OK.build();
        }
        RemoveTestcaseTagCommand command = new RemoveTestcaseTagCommand(code);
        GatewayContext.fillCurrentUser(command);
        testcaseTagCommendDomainService.removeTestcaseTagCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-变更用例状态", apiName = "tm/cases/changeTestcaseStatus", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> changeStatus(ChangeTestcaseStatusReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCode(), new NotBlankValidator("用例"))
                .on(req.getStatus(), new NotNullValidator("状态"))
                .on(req.getAbandonReason(), new NotNullValidator("停用原因"))
                .when(TestcaseStatusEnum.DISABLE.equals(req.getStatus()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ChangeTestcaseStatusCommand command = new ChangeTestcaseStatusCommand(req.getCode());
        command.setStatus(req.getStatus());
        command.setAbandonReason(req.getAbandonReason());
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.changeTestcaseStatus(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-修改版本", apiName = "tm/cases/changeVersion", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> changeVersion(ChangeVersionReq req) {
        if (StringUtils.isBlank(req.getCode())) {
            throw new ServiceException("用例不能为空！");
        }
        ChangeVersionCommand command = new ChangeVersionCommand(req.getCode());
        command.setUseOriginalGroup(req.getUseOriginalGroup());
        command.setTargetVersionCode(req.getTargetVersionCode());
        if (TestcaseGroupTypeEnum.NO_GROUP.name().equals(req.getTargetParentCode())) {
            command.setTargetParentCode(StringUtils.EMPTY);
        } else {
            command.setTargetParentCode(StringUtils.defaultString(req.getTargetParentCode()));
        }
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.changeVersion(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-校验用例创建人和责任人", apiName = "tm/cases/checkCreatorOrDutyUser", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<CheckCreatorOrDutyUserResp> checkCreatorOrDutyUser(List<String> testcaseCodeList) {
        CheckCreatorOrDutyUserQuery query = new CheckCreatorOrDutyUserQuery(testcaseCodeList);
        GatewayContext.fillCurrentUser(query);
        CheckCreatorOrDutyUserVO vo = testcaseQueryDomainService.handle(query);
        CheckCreatorOrDutyUserResp resp = testcaseAdapterConverter.converter(vo);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "测试管理-校验用例状态", apiName = "tm/cases/checkTestcaseStatus", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<CheckTestcaseStatusResp> checkTestcaseStatus(List<String> testcaseCodeList) {
        CheckTestcaseStatusQuery query = new CheckTestcaseStatusQuery(testcaseCodeList);
        CheckTestcaseStatusVO vo = testcaseQueryDomainService.handle(query);
        CheckTestcaseStatusResp resp = testcaseAdapterConverter.converter(vo);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "测试管理-查询用例列表",
            apiName = "tm/cases/simpleListCase",
            timeout = 5000,
            namespace = "luban",
            responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<PageSimpleTestCaseVO> simpleListCase(SimpleListCaseReq req) {
        PageSimpleTestCaseVO result = new PageSimpleTestCaseVO();
        result.setList(Collections.emptyList());
        result.setTotal(0L);
        if (StringUtil.isEmpty(req.getProductCode())) {
            return StatusCode.OK.build(result);
        }
        PageSimpleTestCaseQuery query = new PageSimpleTestCaseQuery();
        query.setProductCode(req.getProductCode());
        query.setSearchKey(req.getSearchKey());
        query.setAttribute(TestcaseAttributeEnum.TESTCASE);
        query.setStatus(Collections.singletonList(TestcaseStatusEnum.NORMAL));
        query.setSize(req.getSize());
        query.setPage(req.getPage());
        return StatusCode.OK.build(testcaseQueryDomainService.pageSimpleTestCaseQuery(query));
    }

    @ZApiOperation(description = "测试管理-获取执行明细列表",
            apiName = "tm/cases/listExecuteCase", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<ListExecuteCaseResp>> listExecuteCase(ListExecuteCaseReq req) {
        if (StringUtils.isBlank(req.getCode())) {
            return StatusCode.OK.build();
        }
        ListExecuteCaseQuery query = new ListExecuteCaseQuery();
        testcaseAdapterConverter.converter(req, query);
        List<ListExecuteCaseVO> vos = testcaseQueryDomainService.listExecuteCaseQuery(query);
        List<ListExecuteCaseResp> list = new ArrayList<>();
        for (ListExecuteCaseVO vo : vos) {
            ListExecuteCaseResp resp = testcaseAdapterConverter.converter(vo);
            list.add(resp);
        }
        return StatusCode.OK.build(list);
    }

    @ZApiOperation(description = "测试管理-导入测试用例", timeout = 5000, apiName = "tm/cases/importTestcase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<ImportResultResp> importTestcase(ImportTestcaseReq req) {
        if (StringUtil.isNotBlank(req.getRemoteFileId())) {
            req.setFileUrl(projectRpcService.getInnerFileUrl(GatewayContext.currentUser().getOpenId(), req.getRemoteFileId()));
        }
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("所属产品code"))
                .on(req.getFileUrl(), new NotBlankValidator("文件地址"))
                .on(req.getFileName(), new NotBlankValidator("文件名"))
                .on(req.getFileType(), new NotNullValidator("文件类型"))
                .on(req.getSetCore(), new NotNullValidator("是否核心用例"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        req.setUser(GatewayContext.currentUser().toSimpleUser());
        ImportResultResp resp = testcaseCommandDomainService.importTestcase(req);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "手工用例修改标题及等级",
            apiName = "tm/testcase/editTestcaseTitleOrPriority", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editTestcaseTitleOrPriority(EditTestcaseTitleReq req) {
        FindTestcaseEntityByCodeQuery query = new FindTestcaseEntityByCodeQuery();
        query.setCode(req.getCode());
        TestcaseVO dto = testcaseQueryDomainService.handle(query);
        if (Objects.isNull(dto)) {
            throw new ServiceException("未查到该数据或该数据不能修改！");
        }
        if (StringUtil.isNotBlank(req.getName())) {
            if (TestcaseAttributeEnum.TESTCASE.equals(dto.getAttribute()) && req.getName().length() > 150) {
                throw new ServiceException("标题不能超过150字！");
            }
            if (TestcaseAttributeEnum.MODULE.equals(dto.getAttribute()) && req.getName().length() > 60) {
                throw new ServiceException("标题不能超过60字！");
            }
        }
        EditTestcaseTitleCommand command = testcaseAdapterConverter.convertor(req);
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.editTestcaseTitle(command, dto);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-校验用例是否关联计划", apiName = "tm/cases/queryCaseRelatedPlan", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<QueryCaseRelatedPlanVO> queryCaseRelatedPlan(QueryCaseRelatedPlanReq req) {
        List<String> caseCodeList = req.getCodeList();
        if (CollectionUtil.isEmpty(caseCodeList)) {
            throw new ServiceException("用例code不能为空！");
        }
        int minSize = config.getMinSize();
        if (caseCodeList.size() > minSize) {
            throw new ServiceException("一次最多校验" + minSize + "条用例");
        }
        CaseRelatePlanQuery query = new CaseRelatePlanQuery();
        query.setCodeList(req.getCodeList());
        GatewayContext.fillCurrentUser(query);
        QueryCaseRelatedPlanVO vo = testcaseQueryDomainService.caseRelatePlanQuery(query);
        return StatusCode.OK.build(vo);
    }

    @ZApiOperation(description = "测试管理-解除用例关联业务", apiName = "tm/cases/releaseRelation", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> releaseTestcaseRelation(ReleaseTestcaseRelationReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCode(), new NotBlankValidator("用例"))
                .on(req.getDomain(), new NotNullValidator("领域"))
                .on(req.getBusinessCode(), new NotBlankValidator("业务"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ReleaseTestcaseRelationCommand command = new ReleaseTestcaseRelationCommand(req.getCode());
        command.setDomain(req.getDomain());
        command.setBusinessCode(req.getBusinessCode());
        command.setOperateCaseCode(req.getOperateCaseCode());
        GatewayContext.fillCurrentUser(command);
        testcaseCommandDomainService.releaseTestcaseRelationCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-移除附件", apiName = "tm/cases/removeAttachment", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> removeAttachment(RemoveTestcaseAttachmentReq req) {
        if (StringUtils.isBlank(req.getCode())) {
            throw new ServiceException("附件不能为空！");
        }
        RemoveTestcaseAttachmentCommand command = new RemoveTestcaseAttachmentCommand(req.getCode());
        command.setOperateCaseCode(req.getOperateCaseCode());
        GatewayContext.fillCurrentUser(command);
        testcaseAttachentCommandDomainService.removeTestcaseAttachmentCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "删除分组提示分组信息",
            apiName = "tm/testcase/groupsInformation", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<GroupsInformationResp> groupsInformation(String code, String productCode) {
        if (StringUtil.isEmpty(code)) {
            throw new ServiceException("分组code不能为空");
        }
        if (StringUtil.isEmpty(productCode)) {
            throw new ServiceException("产品code不能为空");
        }
        GroupsInformationByCodeQuery query = new GroupsInformationByCodeQuery(code, productCode);
        GroupsInformationVO join = testcaseQueryDomainService.handle(query);
        GroupsInformationResp res = new GroupsInformationResp();
        BeanUtils.copyProperties(join, res);
        return StatusCode.OK.build(res);
    }

    @ZApiOperation(description = "移动分组",
            apiName = "tm/testcase/moveModule", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> moveModule(MoveModuleReq req) {
        ComplexResult cr = checkAll()
                .on(req.getName(), new NotBlankValidator("分组名称"))
                .on(req.getCode(), new NotBlankValidator("分组编号"))
                .on(req.getParentCode(), new NotBlankValidator("移动后父分组编号"))
                .on(req.getParentName(), new NotBlankValidator("移动后父分组名称"))
                .on(req.getProductCode(), new NotBlankValidator("产品"))
                .on(req.getType(), new NotNullValidator("类型"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (req.getType().equals(TestcaseTypeEnum.SOURCERECORD)) {
            automaticSourceRecordRepository.checkRecordName(req);
            MoveAutomaticRecordCommand command = automaticSourceRecordConverter.convertor(req);
            GatewayContext.fillCurrentUser(command);
            testcaseCommandDomainService.moveAutomaticRecordCommand(command);
        } else {
            this.checkMoveModule(req);
            this.queryModuleByName(req);
            MoveModuleCommand command = testcaseAdapterConverter.convertor(req);
            GatewayContext.fillCurrentUser(command);
            testcaseCommandDomainService.moveModuleCommand(command);
        }
        return StatusCode.OK.build();
    }

    /**
     * 标题查重
     *
     * @param req
     */
    private void queryModuleByName(MoveModuleReq req) {
        SimpleTestcaseQuery query = new SimpleTestcaseQuery();
        query.setName(req.getName());
        query.setParentCodeList(req.getParentCode().equals(TestcaseGroupTypeEnum.ALL.name()) ?
                Arrays.asList(org.apache.commons.lang3.StringUtils.EMPTY) :
                Arrays.asList(req.getParentCode()));
        query.setAttribute(req.getAttribute());
        query.setType(req.getType());
        query.setProductCode(req.getProductCode());
        query.setVersionCode(req.getVersionCode());
        List<TestcaseVO> testcaseVOList = testcaseQueryDomainService.simpleTestcaseQuery(query);
        if (CollectionUtil.isNotEmpty(testcaseVOList)) {
            throw new ServiceException("同层级下，分组不能重名");
        }
    }

    /**
     * 移动分组校验
     *
     * @param req
     */
    private void checkMoveModule(MoveModuleReq req) {
        ComplexResult cr = checkAll()
                .on(req.getParentCode(), new NotBlankValidator("父节点code"))
                .on(req.getOldParentCode(), new NotBlankValidator("所选节点父节点编号"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (req.getParentCode().equals(TestcaseGroupTypeEnum.NO_GROUP.name())) {
            throw new ServiceException("不能移动到未分组用例下");
        }
        if (req.getOldParentCode().equals(req.getParentCode())) {
            throw new ServiceException("目标分组相同，无需移动");
        }
        // 如果是第一层，newPath = null,如果是第二层，newPath = ""
        if (null == req.getNewPath() || req.getParentCode().equals(TestcaseGroupTypeEnum.ALL.name())) {
            return;
        }
        if (req.getCode().equals(req.getParentCode()) || req.getNewPath().contains(req.getCode())) {
            throw new ServiceException("不能移动到子节点");
        }
        FindTestcaseModulePathQuery query = new FindTestcaseModulePathQuery();
        query.setCode(req.getCode());
        query.setType(req.getType().name());
        query.setAttribute(req.getAttribute().name());
        query.setOldPath(StringUtil.isEmpty(req.getOldPath()) ? req.getCode() :
                req.getOldPath() + "." + req.getCode());
        query.setNewPath(StringUtil.isEmpty(req.getNewPath()) ? req.getParentCode() :
                req.getNewPath() + "." + req.getParentCode());
        int maxLayer = testcaseQueryDomainService.findTestcaseModulePath(query);
        if (maxLayer > 10) {
            throw new ServiceException("层级结构不能超过十层");
        }
    }

    @ZApiOperation(description = "测试管理-统计用例状态", apiName = "tm/cases/statisticCaseStatus", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<TestCaseStatusNumVO> statisticCaseStatus(StatisticCaseStatusReq req) {
        StatisticCaseStatusQuery query = new StatisticCaseStatusQuery(req.getCaseCodeList());
        TestCaseStatusNumVO vo = testcaseQueryDomainService.statisticCaseStatusQuery(query);
        return StatusCode.OK.build(vo);
    }


    @ZApiOperation(description = "版本详情-统计个数", apiName = "tm/cases/statisticVersionCase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<StatisticVersionCaseNumVO> statisticVersionCase(StatisticVersionCaseReq req) {
        return StatusCode.OK.build(testcaseQueryDomainService.statisticVersionCase(req));
    }

}
