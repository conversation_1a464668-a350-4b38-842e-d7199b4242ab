package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baidu.unbiz.fluentvalidator.ComplexResult;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.github.pagehelper.PageInfo;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.entity.UserInfo;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.FluentException;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.common.validator.LengthValidator;
import com.zto.devops.framework.common.validator.NotBlankValidator;
import com.zto.devops.framework.common.validator.NotNullValidator;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.qc.application.converter.ApiTestConvertor;
import com.zto.devops.qc.application.converter.SceneConvertor;
import com.zto.devops.qc.client.enums.rpc.MemberTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.devops.qc.client.model.rpc.pipeline.ApplicationResp;
import com.zto.devops.qc.client.model.testmanager.apitest.command.*;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.*;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskBaseInfo;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.SingleLinkBaseInfo;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.PageApiTestVariableVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.PageSceneInfoVO;
import com.zto.devops.qc.client.service.testmanager.apitest.ApiTestService;
import com.zto.devops.qc.client.service.testmanager.apitest.model.*;
import com.zto.devops.qc.client.service.testmanager.cases.model.ApiTestCaseExecuteDetailReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ApiTestCaseExecuteDetailResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExecuteApiCaseReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExecuteApiTestReq;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.repository.LinkMapRepository;
import com.zto.devops.qc.domain.service.ApiTestCommandDomainService;
import com.zto.devops.qc.domain.service.ApiTestQueryDomainService;
import com.zto.devops.qc.domain.service.LinkMapDomainService;
import com.zto.devops.qc.domain.service.SceneCommandDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.titans.common.util.JsonUtil;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayWebAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @create 2023/5/24 17:24
 */
@Service
@Slf4j
@ZsmpService(name = "接口自动化接口目录", group = "测试管理/接口自动化")
public class ApiTestServiceImpl extends GatewayBase implements ApiTestService {

    @Autowired
    private ApiTestConvertor apiTestConvertor;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private ApiTestCommandDomainService apiTestCommandDomainService;

    @Autowired
    private ApiTestQueryDomainService apiTestQueryDomainService;

    @Autowired
    private LinkMapRepository linkMapRepository;

    @Autowired
    private LinkMapDomainService linkMapDomainService;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    @Autowired
    private SceneCommandDomainService sceneCommandDomainService;

    @Autowired
    private SceneConvertor sceneConvertor;

    @ZsmpApiOperation(description = "测试管理-接口自动化-新增变量",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/addApiTestVariable",
                    description = "新增变量",
                    tags = "V3.63.0",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> addApiTestVariable(AddApiTestVariableReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号productCode"))
                .on(req.getVariableName(), new NotBlankValidator("变量名称variableName"))
                .on(req.getVariableName(), new LengthValidator(1, 200, "变量名称variableName"))
                .on(req.getVariableKey(), new NotBlankValidator("变量键variableKey"))
                .on(req.getVariableKey(), new LengthValidator(1, 200, "变量键variableKey"))
                .on(req.getVariableValue(), new NotBlankValidator("变量值variableValue"))
                .on(req.getVariableValue(), new LengthValidator(1, 2000, "变量值variableValue"))
                .on(req.getType(), new NotNullValidator("类型type"))
                .on(req.getLinkCode(), new NotNullValidator("链路编码linkCode"))
                .on(req.getSubVariableType(), new NotNullValidator("变量子类型subVariableType"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        String generateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddApiTestVariableCommand command = apiTestConvertor.convertor(req, generateId);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.addApiTestVariable(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(description = "测试管理-接口自动化-编辑变量",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/editApiTestVariable",
                    description = "编辑变量",
                    tags = "V3.63.0",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> editApiTestVariable(EditApiTestVariableReq req) {
        ComplexResult cr = checkAll()
                .on(req.getVariableCode(), new NotBlankValidator("变量编号variableCode"))
                .on(req.getProductCode(), new NotBlankValidator("产品编号productCode"))
                .on(req.getProductName(), new NotBlankValidator("产品编号productName"))
                .on(req.getVariableName(), new NotBlankValidator("变量名称variableName"))
                .on(req.getVariableName(), new LengthValidator(1, 200, "变量名称variableName"))
                .on(req.getVariableKey(), new NotBlankValidator("变量键variableKey"))
                .on(req.getVariableKey(), new LengthValidator(1, 200, "变量键variableKey"))
                .on(req.getVariableValue(), new NotBlankValidator("变量值variableValue"))
                .on(req.getVariableValue(), new LengthValidator(1, 2000, "变量值variableValue"))
                .on(req.getType(), new NotNullValidator("变量类型type"))
                .on(req.getLinkCode(), new NotNullValidator("链路编码linkCode"))
                .on(req.getSubVariableType(), new NotNullValidator("变量子类型subVariableType"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        EditApiTestVariableCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.editApiTestVariable(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(description = "测试管理-接口自动化-分页查询变量",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/queryApiTestVariablePage",
                    description = "编辑变量",
                    tags = "V3.63.0",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    namespace = "luban"))
    @Override
    public PageResult<PageApiTestVariableResp> queryApiTestVariablePage(PageApiTestVariableReq req) {
        PageApiTestVariableVO pageApiTestVariableVO = apiTestQueryDomainService.queryApiTestVariablePage(req);
        List<PageApiTestVariableResp> list = apiTestConvertor.convertor(pageApiTestVariableVO.getList());
        return StatusCode.OK.build(list, req.getPage(), req.getSize(), pageApiTestVariableVO.getTotal());
    }

    @ZsmpApiOperation(description = "测试管理-接口自动化-更新变量状态",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/updateApiTestVariableStatus",
                    description = "更新变量状态",
                    tags = "V3.63.0",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> updateApiTestVariableStatus(UpdateApiTestVariableStatusReq req) {
        ComplexResult cr = checkAll()
                .on(req.getVariableCode(), new NotBlankValidator("变量编号variableCode"))
                .on(req.getVariableStatus(), new NotNullValidator("变量状态variableStatus"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        UpdateApiTestVariableStatusCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.updateApiTestVariableStatus(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(description = "测试管理-接口自动化-删除变量",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/deleteApiTestVariable",
                    description = "删除变量",
                    tags = "V3.63.0",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> deleteApiTestVariable(DeleteApiTestVariableReq req) {
        ComplexResult cr = checkAll()
                .on(req.getVariableCode(), new NotBlankValidator("变量编号variableCode"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        DeleteApiTestVariableCommand command = new DeleteApiTestVariableCommand(req.getVariableCode());
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.deleteApiTestVariable(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(description = "测试管理-接口自动化-获取变量执行结果",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/getApiTestVariableResult",
                    description = "获取变量执行结果",
                    tags = "V3.63.0",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<String> getApiTestVariableResult(GetApiTestVariableResultReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号productCode"))
                .on(req.getVariableKey(), new NotBlankValidator("变量键variableKey"))
                .on(req.getLinkCode(), new NotNullValidator("链路编号linkCode"))
                .on(req.getNameSpace(), new NotBlankValidator("命名空间nameSpace"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        Map<String, String> result = apiTestCommandDomainService.getApiTestVariableResult(req);
        return StatusCode.OK.build(JSONObject.toJSONString(result));
    }

    @ZApiOperation(
            description = "测试管理-接口自动化-批量执行接口",
            apiName = "tm/apitest/executeApiTest",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> executeApiTest(ExecuteApiTestReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getTrigMode(), new NotNullValidator("触发方式"))
                .on(req.getEnv(), new NotBlankValidator("执行环境"))
                .on(req.getEnvName(), new NotBlankValidator("执行环境名称"))
                .on(req.getApiCodes(), new NotNullValidator("接口code"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ExecuteApiTestCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.executeApiTest(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口自动化-批量执行用例",
            apiName = "tm/apitest/executeApiCase",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> executeApiCase(ExecuteApiCaseReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getTrigMode(), new NotNullValidator("触发方式"))
                .on(req.getEnv(), new NotBlankValidator("执行环境"))
                .on(req.getEnvName(), new NotBlankValidator("执行环境名称"))
                .on(req.getCaseCodes(), new NotNullValidator("用例code"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ExecuteApiCaseCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.executeApiCase(command);
        return StatusCode.OK.build();
    }


    @ZsmpApiOperation(
            description = "测试管理-接口自动化-执行回调",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/executeCallBack",
                    description = "执行回调",
                    tags = "V3.63.0",
                    webAuth = @GatewayWebAuth(needSession = false, needUserInfoContext = false),
                    namespace = "luban"))
    @Override
    public Result<Boolean> executeCallBack(ExecuteApiCallBackReq req) {
        ComplexResult cr = checkAll()
                .on(req.getTaskCode(), new NotBlankValidator("任务编号"))
                .on(req.getCaseCode(), new NotBlankValidator("用例code"))
                .on(req.getResult(), new NotNullValidator("执行结果"))
                .on(req.getPath(), new NotBlankValidator("json文件path"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ExecuteApiCallBackCommand command = apiTestConvertor.convertor(req);
        return StatusCode.OK.build(apiTestCommandDomainService.executeCallBack(command));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-执行明细查询",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/listExecuteDetail",
                    description = "执行明细查询",
                    tags = "V3.63.1",
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<List<ListExecuteDetailResp>> listExecuteDetail(ListExecuteDetailReq req) {
        if (StringUtils.isBlank(req.getCode())) {
            throw new ServiceException("任务编号不能为空！");
        }
        List<ListExecuteDetailResp> respList = apiTestQueryDomainService.listExecuteDetail(req);
        return StatusCode.OK.build(respList);
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-全量同步文档",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/syncApiTestDoc",
                    description = "全量同步文档",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> syncApiTestDoc(SyncApiTestDocReq req) {
        apiTestCommandDomainService.syncApiMetaData(req);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口自动化-接口用例详情查询",
            apiName = "tm/apitest/getApiCaseDetail",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<ApiCaseDetailResp> getApiCaseDetail(ApiCaseDetailReq req) {
        ComplexResult cr = checkAll()
                .on(req.getCaseCode(), new NotBlankValidator("用例code"))
                .on(req.getStatus(), new NotNullValidator("用例状态"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(apiTestQueryDomainService.getApiCaseDetail(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-接口分页",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/api/page",
                    description = "测试管理-接口自动化-接口分页",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    namespace = "luban"))
    @Override
    public PageResult<ApiVO> pageApi(PageApiReq req) {
        if (StringUtil.isBlank(req.getProductCode())) {
            throw new ServiceException("productCode不能为空！");
        }
        PageApiVO vo = apiTestQueryDomainService.queryApiPage(req);
        return StatusCode.OK.build(vo.getList(), req.getPage(), req.getSize(), vo.getTotal());
    }

    @ZApiOperation(
            description = "测试管理-接口自动化-接口分页（精简版）",
            apiName = "tm/apitest/api/lite/page",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<ApiLiteInfoVO> pageApiLiteInfo(PageApiLiteInfoReq req) {
        if (StringUtil.isBlank(req.getProductCode())) {
            throw new ServiceException("productCode不能为空！");
        }
        PageApiLiteInfoVO vo = apiTestQueryDomainService.queryApiLiteInfoPage(req);
        return StatusCode.OK.build(vo.getList(), req.getPage(), req.getSize(), vo.getTotal());
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-用例分页",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/case/page",
                    description = "测试管理-接口自动化-用例分页",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    namespace = "luban"))
    @Override
    public PageResult<ApiCaseVO> pageApiCase(PageApiCaseReq req) {
        if (StringUtil.isBlank(req.getProductCode())) {
            throw new ServiceException("productCode不能为空！");
        }
        PageApiCaseVO vo = apiTestQueryDomainService.queryApiCasePage(req);
        return StatusCode.OK.build(vo.getList(), req.getPage(), req.getSize(), vo.getTotal());
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-用例分页(子用例)",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/case/pageChildren",
                    description = "测试管理-接口自动化-用例分页(子用例)",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    namespace = "luban"))
    @Override
    public PageResult<ApiCaseChildVO> pageApiCaseChildren(PageApiCaseChildrenReq req) {
        if (StringUtil.isBlank(req.getProductCode()) || StringUtil.isBlank(req.getCaseCode())) {
            throw new ServiceException("productCode 和 caseCode 不能为空！");
        }
        PageApiCaseChildrenVO vo = apiTestQueryDomainService.queryApiCaseChildrenPage(req);
        return StatusCode.OK.build(vo.getList(), req.getPage(), req.getSize(), vo.getTotal());
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-应用ID列表",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/appId/list",
                    description = "测试管理-接口自动化-应用ID列表",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<List<ApplicationResp>> listAppId(ListAppIdReq req) {
        if (StringUtil.isBlank(req.getProductCode())) {
            throw new ServiceException("productCode不能为空！");
        }
        List<ApplicationResp> resultList = apiTestQueryDomainService.queryAppIdList(req);
        return StatusCode.OK.build(resultList);
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-文档版本号分页",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/api/docVersion/page",
                    description = "测试管理-接口自动化-文档版本号分页",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public PageResult<ApiDocVersionVO> pageApiDocVersion(PageApiDocVersionReq req) {
        if (StringUtil.isBlank(req.getProductCode())) {
            throw new ServiceException("productCode不能为空！");
        }
        PageApiDocVersionVO vo = apiTestQueryDomainService.queryApiDocVersionPage(req);
        return StatusCode.OK.build(vo.getList(), req.getPage(), req.getSize(), vo.getTotal());
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-子用例数",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/case/count",
                    description = "测试管理-接口自动化-子用例数",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Integer> countCase(CountCaseReq req) {
        if (StringUtil.isBlank(req.getProductCode()) || CollectionUtil.isEmpty(req.getApiCodeList())) {
            throw new ServiceException("产品Code 或 接口code 不能为空！");
        }
        return StatusCode.OK.build(apiTestQueryDomainService.countCase(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-更新接口用例用户",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/updateApiCaseUser",
                    description = "更新接口用例用户",
                    tags = "V3.63.1",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> updateApiCaseUser(UpdateApiCaseUserReq req) {
        ComplexResult cr = checkAll()
                .on(req.getCaseCode(), new NotBlankValidator("用例code"))
                .on(req.getUserVariableCode(), new NotBlankValidator("用户变量code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        UpdateApiCaseUserCommand command = new UpdateApiCaseUserCommand(req.getCaseCode());
        command.setUserVariableCode(req.getUserVariableCode());
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.updateApiCaseUserCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "新增场景基础信息", apiName = "tm/apitest/addSceneBasicInfo", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addSceneBasicInfo(AddSceneBasicInfoReq req) {

        if (StringUtils.isNotBlank(req.getSceneType()) && req.getSceneType().equals(UseCaseFactoryTypeEnum.CREATE.name())) {
            ComplexResult cr = checkAll()
                    .on(req.getProductCode(), new NotBlankValidator("产品code"))
                    .on(req.getSceneName(), new NotBlankValidator("造数名称"))
                    .on(req.getSceneName(), new LengthValidator(1, 60, "造数名称"))
                    .on(req.getParentCode(), new NotBlankValidator("分组"))
                    .on(req.getSceneInfoDesc(), new LengthValidator(0, 500, "场景描述"))
                    .when(StringUtil.isNotEmpty(req.getSceneInfoDesc()))
                    .doValidate()
                    .result(ResultCollectors.toComplex());
            if (!cr.isSuccess()) {
                throw new FluentException(cr.getErrors());
            }
        } else {
            ComplexResult cr = checkAll()
                    .on(req.getProductCode(), new NotBlankValidator("产品code"))
                    .on(req.getSceneName(), new NotBlankValidator("场景名称"))
                    .on(req.getSceneName(), new LengthValidator(1, 60, "场景名称"))
                    .on(req.getParentCode(), new NotBlankValidator("分组"))
                    .on(req.getSceneInfoDesc(), new LengthValidator(0, 500, "场景描述"))
                    .when(StringUtil.isNotEmpty(req.getSceneInfoDesc()))
                    .doValidate()
                    .result(ResultCollectors.toComplex());
            if (!cr.isSuccess()) {
                throw new FluentException(cr.getErrors());
            }
        }
        EditSceneInfoCommand command = new EditSceneInfoCommand(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        command.setProductCode(req.getProductCode());
        command.setSceneName(req.getSceneName());
        command.setSceneInfoDesc(req.getSceneInfoDesc());
        command.setParentCode(req.getParentCode());
        command.setSceneType(req.getSceneType());
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.addSceneBasicInfoCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "编辑场景基础信息", apiName = "tm/apitest/editSceneBasicInfo", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editSceneBasicInfo(EditSceneBasicInfoReq req) {
        ComplexResult cr = checkAll()
                .on(req.getSceneCode(), new NotBlankValidator("场景code"))
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getSceneName(), new NotBlankValidator("场景名称"))
                .on(req.getSceneName(), new LengthValidator(1, 60, "场景名称"))
                .on(req.getSceneInfoDesc(), new LengthValidator(0, 500, "场景描述"))
                .when(StringUtil.isNotEmpty(req.getSceneInfoDesc()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        EditSceneInfoCommand command = new EditSceneInfoCommand(req.getSceneCode());
        command.setProductCode(req.getProductCode());
        command.setSceneName(req.getSceneName());
        command.setSceneInfoDesc(req.getSceneInfoDesc());
        command.setParentCode(req.getParentCode());
        command.setSceneType(UseCaseFactoryTypeEnum.SCENE.name());
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.editSceneBasicInfoCommand(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-获取内置函数",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/getInitMethod",
                    description = "获取内置函数",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<List<ApiTestInitMethodResp>> getInitMethod(ApiTestInitMethodReq req) {
        return StatusCode.OK.build(apiTestQueryDomainService.queryApiTestInitMethod(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-保存草稿",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/editSceneInfo",
                    description = "保存草稿",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<EditSceneBasicInfoResp> editSceneInfo(EditSceneInfoReq req) {
        ComplexResult cr = checkAll()
                .on(req.getSceneCode(), new NotBlankValidator("场景code"))
                .on(req.getSceneFrontData(), new NotBlankValidator("前端数据"))
                .on(req.getSceneBackData(), new NotNullValidator("后端数据"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        EditSceneInfoCommand command = new EditSceneInfoCommand(req.getSceneCode());
        command.setSceneFrontData(req.getSceneFrontData());
        command.setSceneBackData(req.getSceneBackData().toJSONString());
        command.setDbIds(req.getDbIds());
        GatewayContext.fillCurrentUser(command);
        return StatusCode.OK.build(apiTestCommandDomainService.editSceneInfoCommand(command));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-保存并发布",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/publishSceneInfo",
                    description = "保存并发布",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<EditSceneBasicInfoResp> publishSceneInfo(EditSceneInfoReq req) {
        if (StringUtils.isEmpty(req.getSceneCode())) {
            throw new ServiceException("场景code不能为空！");
        }
        EditSceneInfoCommand command = new EditSceneInfoCommand(req.getSceneCode());
        GatewayContext.fillCurrentUser(command);
        return StatusCode.OK.build(apiTestCommandDomainService.publishSceneInfoCommand(command));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-发布造数",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/publishDataCenter",
                    description = "发布造数",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<EditSceneBasicInfoResp> publishDataCenter(PublishDataCenterReq req) {
        if (StringUtils.isEmpty(req.getSceneCode())) {
            throw new ServiceException("造数code不能为空！");
        }
        EditSceneInfoCommand command = new EditSceneInfoCommand(req.getSceneCode());
        command.setProductCode(req.getProductCode());
        GatewayContext.fillCurrentUser(command);
        // 发布造数需要判断是否超管和创建者
        if (!apiTestCommandDomainService.hasPermission(command.getTransactor(),
                command.getAggregateId(),
                command.getProductCode())) {
            throw new ServiceException("当前人无操作无权限！");
        }
        return StatusCode.OK.build(apiTestCommandDomainService.publishDataCenter(command));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-场景链路查询",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/querySceneLink",
                    description = "测试管理-接口自动化-场景链路查询",
                    tags = "V3.63.2",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public PageResult<SingleLinkBaseInfo> querySceneLink(PageLinkMapReq req) {
        if (StringUtil.isBlank(req.getSceneCode())) {
            throw new ServiceException("sceneCode不能为空！");
        }
        if (req.getSceneVersion() == null) {
            throw new ServiceException("sceneVersion不能为空！");
        }
        apiTestCommandDomainService.generateLink(req.getSceneCode(), req.getSceneVersion());
        PageLinkMapVO vo = linkMapRepository.querySceneLink(req);
        return StatusCode.OK.build(vo.getList(), req.getPage(), req.getSize(), vo.getTotal());
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-节点执行调试",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/node/debug",
                    description = "测试管理-接口自动化-节点执行调试",
                    tags = "V3.157.0",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<DebugTaskBaseInfo> debugOnNode(DebugOnNodeReq req) {
        if (StringUtil.isBlank(req.getSceneCode())) {
            throw new ServiceException("sceneCode不能为空！");
        }
        if (req.getDebugType() == null) {
            throw new ServiceException("debugType不能为空！");
        }
        if (StringUtil.isEmpty(req.getNodeCode())) {
            throw new ServiceException("节点Code不能为空！");
        }
        User user = GatewayContext.currentUser().toSimpleUser();
        DebugTaskBaseInfo debugTaskBaseInfo = linkMapDomainService.debugOnNode(req, user);
        return StatusCode.OK.build(debugTaskBaseInfo);
    }

    @ZsmpApiOperation(description = "测试管理-接口自动化-分页查询场景",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/querySceneInfoPage",
                    description = "分页查询场景",
                    tags = "V3.63.2",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    namespace = "luban"))
    @Override
    public PageResult<SceneInfoResp> querySceneInfoPage(PageSceneInfoReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号productCode"))
                .on(req.getParentCode(), new NotBlankValidator("分组"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        req.setCurrentUserId(GatewayContext.currentUser().getSsoUserId());
        req.setPermissions(GatewayContext.currentUser().getPermissions());
        PageSceneInfoVO pageSceneInfoVO = apiTestQueryDomainService.querySceneInfoPage(req);
        List<SceneInfoResp> list = apiTestConvertor.convertorSceneInfoVOLsit(pageSceneInfoVO.getList());
        return StatusCode.OK.build(list, req.getPage(), req.getSize(), pageSceneInfoVO.getTotal());
    }

    @ZsmpApiOperation(description = "测试管理-接口自动化-查询场景详情",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/querySceneInfo",
                    description = "查询场景详情",
                    tags = "V3.63.2",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    namespace = "luban"))
    @Override
    public Result<SceneInfoResp> querySceneInfo(SceneInfoReq req) {
        ComplexResult cr = checkAll()
                .on(req.getSceneCode(), new NotBlankValidator("场景编号sceneCode"))
                .on(req.getStatus(), new NotNullValidator("状态status"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(apiTestQueryDomainService.querySceneInfo(req, GatewayContext.currentSsoUserId()));
    }

    @ZsmpApiOperation(description = "测试管理-接口自动化-查询最新场景草稿详情",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/queryLatestEditSceneInfo",
                    description = "查询最新场景草稿详情",
                    tags = "V3.63.2",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    namespace = "luban"))
    @Override
    public Result<SceneInfoResp> queryLatestEditSceneInfo(QueryLatestEditSceneInfoReq req) {
        ComplexResult cr = checkAll()
                .on(req.getSceneCode(), new NotBlankValidator("场景编号sceneCode"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(apiTestQueryDomainService.queryLatestEditSceneInfo(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-进入登记库提示",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/autoSourceEnterTip",
                    description = "进入登记库提示",
                    tags = "V3.63.2",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<EnterAutoSourceTipResp> autoSourceEnterTip(EnterAutoSourceTipReq req) {
        return StatusCode.OK.build(apiTestQueryDomainService.autoSourceEnterTip(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-查询进入登记库当前按钮状态",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/sourceCurrentStatus",
                    description = "查询进入登记库当前按钮状态",
                    tags = "V3.63.2",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<SourceCurrentStatusResp> queryCurrentStatus(SourceCurrentStatusReq req) {
        return StatusCode.OK.build(apiTestQueryDomainService.queryCurrentStatus(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-获取接口列表",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/pageApiInfo",
                    description = "获取接口列表",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public PageResult<PageApiInfoResp> pageApiInfo(PageApiInfoReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPage(), new NotNullValidator("页数"))
                .on(req.getSize(), new NotNullValidator("数据大小"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if ("SCENE".equals(req.getSource()) && StringUtil.isEmpty(req.getProductCode())
                && StringUtil.isEmpty(req.getApiCode())) {
            throw new ServiceException("产品code不能为空！");
        }
        PageInfo<PageApiInfoResp> pageInfo = apiTestQueryDomainService.queryPageApiInfo(req);
        return StatusCode.OK.build(pageInfo.getList(), pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal());
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-变更场景/链路状态",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/changeLinkStatus",
                    description = "变更场景/链路状态",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> changeLinkStatus(ChangeLinkStatusReq req) {
        ComplexResult cr = checkAll()
                .on(req.getSceneCode(), new NotBlankValidator("场景code"))
                .on(req.getSceneVersion(), new NotNullValidator("场景版本"))
                .when(StringUtils.isNotEmpty(req.getLinkMapCode()))
                .on(req.getEnable(), new NotNullValidator("状态"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ChangeLinkStatusCommand command = new ChangeLinkStatusCommand(req.getSceneCode());
        command.setSceneVersion(req.getSceneVersion());
        command.setLinkMapCode(req.getLinkMapCode());
        command.setEnable(req.getEnable());
        command.setSceneType(req.getSceneType());
        GatewayContext.fillCurrentUser(command);
        // 停用造数需要判断是否超管和创建者
        if (UseCaseFactoryTypeEnum.CREATE.equals(command.getSceneType())) {
            if (!apiTestCommandDomainService.hasPermission(command.getTransactor(),
                    command.getAggregateId(), req.getProductCode())) {
                throw new ServiceException("当前人无操作无权限！");
            }
        }
        apiTestCommandDomainService.changeLinkStatusCommand(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-登记库分页（精简版）",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/pageAutomaticLiteInfo",
                    description = "测试管理-接口自动化-登记库分页（精简版）",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public PageResult<AutomaticRecordLiteInfoVO> pageAutomaticLiteInfo(PageAutomaticRecordLiteInfoReq req) {
        if (StringUtil.isBlank(req.getProductCode())) {
            throw new ServiceException("productCode不能为空！");
        }
        PageAutomaticRecordLiteInfoVO vo = apiTestQueryDomainService.queryAutomaticRecordLiteInfoPage(req);
        return StatusCode.OK.build(vo.getList(), req.getPage(), req.getSize(), vo.getTotal());
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-生成登记库",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/generateAutomaticSource",
                    description = "生成登记库",
                    tags = "V3.63.2",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> generateAutomaticSource(AutomaticSourceGenerateReq req) {
        AutomaticSourceGenerateCommand command = new AutomaticSourceGenerateCommand(req.getSceneCode());
        command.setSceneCode(req.getSceneCode());
        command.setStatus(req.getStatus());
        command.setSceneVersion(req.getSceneVersion());
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.generateAutomaticSource(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-停用场景图校验",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/checkDisableScene",
                    description = "停用场景图校验",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<CheckDisableSceneResp> checkDisableScene(CheckDisableSceneReq req) {
        if (StringUtils.isEmpty(req.getSceneCode())) {
            throw new ServiceException("场景code不能为空！");
        }
        return StatusCode.OK.build(apiTestQueryDomainService.querySceneRelatedPlanAndTask(req.getSceneCode()));
    }

    @ZApiOperation(description = "复制场景", apiName = "tm/apitest/copyScene", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<String> copySceneInfo(CopySceneInfoReq req) {
        ComplexResult cr = checkAll()
                .on(req.getSceneCode(), new NotBlankValidator("场景code"))
                .on(req.getSceneName(), new NotBlankValidator("场景名称"))
                .on(req.getSceneName(), new LengthValidator(1, 60, "场景名称"))
                .on(req.getParentCode(), new NotBlankValidator("分组"))
                .on(req.getSceneInfoDesc(), new LengthValidator(0, 500, "场景描述"))
                .when(StringUtil.isNotEmpty(req.getSceneInfoDesc()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        CopySceneInfoCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        return StatusCode.OK.build(apiTestCommandDomainService.copySceneInfoReq(command));
    }


    @ZsmpApiOperation(
            description = "测试管理-接口自动化-获取产品下所有数据库",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/queryProductDb",
                    description = "获取产品下所有数据库",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<JSONArray> queryProductDb(QueryProductDbReq req) {
        log.info("获取产品下所有数据库请求。{}", JsonUtil.toJSON(req));
        if (StringUtils.isEmpty(req.getProductCode())) {
            throw new ServiceException("产品code不能为空！");
        }
        return StatusCode.OK.build(apiTestQueryDomainService.queryProductDb(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-查调试日志",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/queryApiDebugLog",
                    description = "查调试日志",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<ApiDebugLogResp> queryApiDebugLog(ApiDebugLogReq req) {
        if (StringUtils.isBlank(req.getTaskId())) {
            throw new ServiceException("任务code不能为空！");
        }
        return StatusCode.OK.build(apiTestQueryDomainService.queryApiDebugLog(req.getTaskId()));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-push调试日志",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/pushApiDebugLog",
                    description = "push调试日志",
                    webAuth = @GatewayWebAuth(needSession = false, needUserInfoContext = false),
                    namespace = "luban"))
    @Override
    public Result<Void> pushApiDebugLog(ApiDebugLogPushReq req) {
        if (StringUtils.isBlank(req.getTaskId())) {
            throw new ServiceException("任务code不能为空！");
        }
        apiTestQueryDomainService.pushApiDebugLog(req);
        return StatusCode.OK.build();
    }


    @ZsmpApiOperation(
            description = "测试管理-接口自动化-终止调试任务",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/abortDebugTask",
                    description = "终止调试任务",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> abortDebugTask(AbortDebugTaskReq req) {
        if (StringUtils.isBlank(req.getTaskId())) {
            throw new ServiceException("任务编号不能为空！");
        }
        apiTestCommandDomainService.abortDebugTask(req);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口自动化-调试节点回调接口",
            apiName = "tm/apitest/debugCallBack",
            namespace = "luban"
    )
    @ZGWWebAuth(needUserInfoContext = false)
    @Override
    public Result<Void> debugCallBack(DebugCallBackReq req) {
        ComplexResult cr = checkAll()
                .on(req.getTaskId(), new NotBlankValidator("任务编号"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        apiTestCommandDomainService.debugCallBack(req);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-调试结束回调",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/debugCallBackEnd",
                    description = "调试结束回调",
                    webAuth = @GatewayWebAuth(needSession = false, needUserInfoContext = false),
                    namespace = "luban"))
    @Override
    public Result<Void> debugCallBackEnd(DebugCallBackEndReq req) {
        ComplexResult cr = checkAll()
                .on(req.getTaskId(), new NotBlankValidator("任务编号"))
                .on(req.getType(), new NotNullValidator("回调类型"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        apiTestCommandDomainService.debugCallBackEnd(req);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-调试任务日志",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/debugCallBackLog",
                    description = "调试结束回调",
                    webAuth = @GatewayWebAuth(needSession = false, needUserInfoContext = false),
                    namespace = "luban"))
    @Override
    public Result<Void> debugCallBackLog(DebugCallBackLogReq req) {
        ComplexResult cr = checkAll()
                .on(req.getTaskId(), new NotBlankValidator("任务编号"))
                .on(req.getType(), new NotNullValidator("回调类型"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        linkMapDomainService.debugCallBackLog(req);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-删除场景图",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/deleteSceneInfo",
                    description = "删除场景图",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    tags = "3.444.0",
                    namespace = "luban"))
    @Override
    public Result<Void> deleteSceneInfo(DeleteSceneInfoReq req) {
        if (StringUtils.isBlank(req.getSceneCode())) {
            throw new ServiceException("场景图code不能为空！");
        }
        DeleteSceneInfoCommand command = new DeleteSceneInfoCommand(req.getSceneCode());
        command.setSceneType(req.getSceneType());
        GatewayContext.fillCurrentUser(command);
        // 删除造数需要判断是否超管和创建者
        if (UseCaseFactoryTypeEnum.CREATE.equals(command.getSceneType())) {
            if (!apiTestCommandDomainService.hasPermission(command.getTransactor(),
                    command.getAggregateId(), req.getProductCode())) {
                throw new ServiceException("当前人无操作无权限！");
            }
        }
        apiTestCommandDomainService.deleteSceneInfo(command);
        return StatusCode.OK.build();
    }


    @ZsmpApiOperation(
            description = "测试管理-接口自动化-取Jmeter变量",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/linkmap/getVariable",
                    description = "取Jmeter变量",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    tags = "V3.180.0",
                    namespace = "luban"))
    @Override
    public Result<JSONObject> getVariable(GetVariableReq req) {
        ComplexResult cr = checkAll()
                .on(req.getSceneCode(), new NotBlankValidator("场景code"))
                .on(req.getNodeCode(), new NotBlankValidator("节点code"))
                .on(req.getSceneVersion(), new NotNullValidator("场景版本号"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(linkMapDomainService.getVariable(req));
    }


    @ZsmpApiOperation(
            description = "测试管理-接口自动化-编辑场景鉴权",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/editSceneAuthentication",
                    description = "编辑场景鉴权",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<EditSceneAuthenticationResp> editSceneAuthentication(EditSceneAuthenticationReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getSceneCode(), new NotBlankValidator("场景编号"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        EditSceneAuthenticationCommand command = new EditSceneAuthenticationCommand(req.getSceneCode());
        GatewayContext.fillCurrentUser(command);
        return StatusCode.OK.build(apiTestCommandDomainService.editSceneAuthenticationReq(command));
    }

    @ZsmpApiOperation(description = "测试管理-接口自动化-获取明文",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/getPlainVariable",
                    description = "获取明文",
                    tags = "V3.199.0",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<String> getPlainVariable(QueryApiTestVariableValueReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getVariableCode(), new NotBlankValidator("变量code"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(apiTestQueryDomainService.getPlainVariable(req.getVariableCode()));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-获取接口mock地址",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/getApiMockUrl",
                    description = "获取接口mock地址",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<QueryApiMockUrlResp> getApiMockUrl(QueryApiMockUrlReq req) {
        if (StringUtils.isEmpty(req.getApiCode())) {
            throw new ServiceException("接口code不能为空！");
        }
        return StatusCode.OK.build(apiTestQueryDomainService.queryApiMockUrl(req.getApiCode()));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-场景图迁移初始化接口",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/sceneInitial",
                    description = "场景图迁移初始化接口",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> sceneInitial(SceneInitialReq req) {
        apiTestCommandDomainService.sceneInitial(req);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-索引树查询接口",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/listModule",
                    description = "索引树查询接口",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<List<SceneModuleQueryResp>> listModule(SceneModuleQueryReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        req.setCurrentUserId(GatewayContext.currentUser().getSsoUserId());
        req.setPermissions(GatewayContext.currentUser().getPermissions());
        return StatusCode.OK.build(apiTestQueryDomainService.listSceneModule(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-删除场景分组提示",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/deleteSceneModuleTips",
                    description = "删除场景分组提示",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<DeleteSceneModuleTipsResp> deleteSceneModuleTips(DeleteSceneModuleReq req) {
        if (StringUtils.isEmpty(req.getCode())) {
            throw new ServiceException("分组code不能为空！");
        }
        return StatusCode.OK.build(apiTestQueryDomainService.queryDeleteSceneModuleTips(req.getCode()));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-删除场景分组",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/deleteSceneModule",
                    description = "删除场景分组",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> deleteSceneModule(DeleteSceneModuleReq req) {
        if (StringUtils.isEmpty(req.getCode())) {
            throw new ServiceException("分组code不能为空！");
        }
        DeleteSceneModuleCommand command = new DeleteSceneModuleCommand(req.getCode());
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.deleteSceneModule(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-新增场景分组",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/addSceneModule",
                    description = "新增场景分组",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> addSceneModule(AddSceneModuleReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getParentCode(), new NotBlankValidator("父分组编号"))
                .on(req.getSceneIndexName(), new NotBlankValidator("分组名称"))
                .on(req.getSceneIndexName(), new LengthValidator(1, 60, "分组名称"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        String generateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddSceneModuleCommand command = apiTestConvertor.convertor(req, generateId);
        GatewayContext.fillCurrentUser(command);
        command.setSceneType(UseCaseFactoryTypeEnum.SCENE.name());
        apiTestCommandDomainService.addSceneModule(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-编辑场景分组",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/editSceneModule",
                    description = "编辑场景分组",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> editSceneModule(EditSceneModuleReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getCode(), new NotNullValidator("分组或场景编号"))
                .on(req.getSceneIndexName(), new NotBlankValidator("分组或场景名称"))
                .on(req.getSceneIndexName(), new LengthValidator(1, 30, "分组或场景名称"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        EditSceneModuleCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        command.setSceneType(UseCaseFactoryTypeEnum.SCENE.name());
        apiTestCommandDomainService.editSceneModule(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-移动场景分组",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/moveSceneModule",
                    description = "移动场景分组",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> moveSceneModule(MoveSceneModuleReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getParentCode(), new NotBlankValidator("父分组编号"))
                .on(req.getCode(), new NotNullValidator("分组或场景编号"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        MoveSceneModuleCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        command.setSceneType(UseCaseFactoryTypeEnum.SCENE.name());
        apiTestCommandDomainService.moveSceneModule(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-获取授权用户",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/queryAuthorizedUsers",
                    description = "获取授权用户",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Set<Long>> queryAuthorizedUsers(QueryAuthorizedUsersReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        List<String> memberTypes = Arrays.asList(
                MemberTypeEnum.TESTER_OWNER.name(),
                MemberTypeEnum.DEVELOPER_OWNER.name(),
                MemberTypeEnum.ARCHITECT.name(),
                MemberTypeEnum.PRODUCTER_OWNER.name(),
                MemberTypeEnum.PROJECTER_OWNER.name());
        return StatusCode.OK.build(apiTestQueryDomainService.queryAuthorizedUsers(req, memberTypes));
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-批量删除场景",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/batchDeleteScene",
                    description = "批量删除场景",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> batchDeleteScene(BatchDeleteSceneReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getSceneCodeList(), new NotNullValidator("场景编号集合"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        BatchDeleteSceneCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.batchDeleteScene(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-批量移动场景",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/batchMoveScene",
                    description = "批量移动场景",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> batchMoveScene(BatchMoveSceneReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getParentCode(), new NotBlankValidator("父分组编号"))
                .on(req.getSceneCodeList(), new NotNullValidator("场景编号集合"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        BatchMoveSceneCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.batchMoveScene(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-用例工厂批量执行",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/batchExecute",
                    description = "用例工厂批量执行",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> batchExecute(SceneBatchExecuteReq req) {
        if (CollectionUtils.isEmpty(req.getSceneCodeList())) {
            throw new ServiceException("场景列表不能为空！");
        }
        if (null == req.getTrigMode()) {
            throw new ServiceException("触发方式不能为空！");
        }
        if (null == req.getProductCode()) {
            throw new ServiceException("产品code不能为空！");
        }
        if (null == req.getAutoEnv()) {
            throw new ServiceException("动态多环境标识不能为空！");
        }
        UserInfo currentUser = GatewayContext.currentUser();
        apiTestCommandDomainService.batchExecute(currentUser, req);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-批量新增变量",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/batchAddVariable",
                    description = "批量新增变量",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> batchAddVariable(BatchAddVariableReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getProductName(), new NotBlankValidator("产品名称"))
                .on(req.getApiTestVariableList(), new NotNullValidator("变量集合"))
                .on(req.getType(), new NotNullValidator("变量类型"))
                .on(req.getLinkCode(), new NotNullValidator("业务编码"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        BatchAddVariableCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.batchAddVariable(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-查询场景自定义变量",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/querySceneVariable",
                    description = "查询场景自定义变量",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<List<BaseApiTestVariableResp>> querySceneVariable(QuerySceneVariableReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("所属产品编码"))
                .on(req.getLinkCode(), new NotBlankValidator("业务编码"))
                .on(req.getType(), new NotNullValidator("变量类型"))
                .on(req.getSubVariableType(), new NotNullValidator("变量子类型"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        List<BaseApiTestVariableResp> apiTestVariableList = apiTestQueryDomainService.querySceneVariable(req);
        return StatusCode.OK.build(apiTestVariableList);
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-更新场景阶段记录",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/updateSceneStepRecord",
                    description = "更新场景阶段记录",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> updateSceneStepRecord(UpdateSceneStepRecordReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("所属产品编码"))
                .on(req.getSceneId(), new NotNullValidator("场景主键"))
                .on(req.getStepRecord(), new NotBlankValidator("阶段记录"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        String generateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        UpdateSceneStepRecordCommand command = apiTestConvertor.convertor(req, generateId);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.updateSceneStepRecord(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "编辑造数基础信息", apiName = "tm/apitest/editPreDataBasicInfo", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editPreDataBasicInfo(EditDataBasicInfoReq req) {
        ComplexResult cr = checkAll()
                .on(req.getSceneCode(), new NotBlankValidator("造数code"))
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getSceneName(), new NotBlankValidator("造数名称"))
                .on(req.getSceneName(), new LengthValidator(1, 60, "造数名称"))
                .on(req.getSceneInfoDesc(), new LengthValidator(0, 500, "造数描述"))
                .when(StringUtil.isNotEmpty(req.getSceneInfoDesc()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        EditPreDataInfoCommand command = new EditPreDataInfoCommand(req.getSceneCode());
        command.setProductCode(req.getProductCode());
        command.setSceneName(req.getSceneName());
        command.setSceneInfoDesc(req.getSceneInfoDesc());
        command.setParentCode(req.getParentCode());
        command.setSceneType(UseCaseFactoryTypeEnum.CREATE.name());
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.editPreDataBasicInfoCommand(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-编辑造数分组",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/editPreDataModule",
                    description = "编辑造数分组",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> editPreDataModule(EditPreDataModuleReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getCode(), new NotNullValidator("分组或造数编号"))
                .on(req.getSceneIndexName(), new NotBlankValidator("分组或造数名称"))
                .on(req.getSceneIndexName(), new LengthValidator(1, 30, "分组或造数名称"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        EditPreDataModuleCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        command.setSceneType(UseCaseFactoryTypeEnum.CREATE.name());
        apiTestCommandDomainService.editPreDataModule(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-新增造数分组",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/addPreDataModule",
                    description = "新增造数分组",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> addPreDataModule(AddPreDataModuleReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getParentCode(), new NotBlankValidator("父分组编号"))
                .on(req.getSceneIndexName(), new NotBlankValidator("分组名称"))
                .on(req.getSceneIndexName(), new LengthValidator(1, 60, "分组名称"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        String generateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddPreDataModuleCommand command = apiTestConvertor.convertor(req, generateId);
        GatewayContext.fillCurrentUser(command);
        command.setSceneType(UseCaseFactoryTypeEnum.CREATE.name());
        apiTestCommandDomainService.addPreDataModule(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-接口自动化-移动造数分组",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/movePreDataModule",
                    description = "移动造数分组",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> movePreDataModule(MovePreDataModuleReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getParentCode(), new NotBlankValidator("父分组编号"))
                .on(req.getCode(), new NotNullValidator("分组或造数编号"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        MovePreDataModuleCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        command.setSceneType(UseCaseFactoryTypeEnum.CREATE.name());
        apiTestCommandDomainService.movePreDataModule(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-造数中心-保存造数草稿",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/editPreDataInfo",
                    description = "保存造数草稿",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<EditPreDataBasicInfoResp> editPreDataInfo(EditPreDataInfoReq req) {
        ComplexResult cr = checkAll()
                .on(req.getSceneCode(), new NotBlankValidator("造数code"))
                .on(req.getSceneFrontData(), new NotBlankValidator("前端数据"))
                .on(req.getSceneBackData(), new NotNullValidator("后端数据"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        EditPreDataCommand command = new EditPreDataCommand(req.getSceneCode());
        command.setSceneFrontData(req.getSceneFrontData());
        command.setSceneBackData(req.getSceneBackData().toJSONString());
        command.setDbIds(req.getDbIds());
        GatewayContext.fillCurrentUser(command);
        return StatusCode.OK.build(apiTestCommandDomainService.editPreDataInfoCommand(command));
    }

    @ZsmpApiOperation(
            description = "测试管理-造数中心-批量新增变量",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/batchAddParameter",
                    description = "批量新增变量",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> batchAddParameter(BatchAddPreDataVariableReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getProductName(), new NotBlankValidator("产品名称"))
                .on(req.getType(), new NotNullValidator("类型"))
                .on(req.getLinkCode(), new NotNullValidator("业务编码"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        BatchAddPreDataVariableCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.validParam(command);
        apiTestCommandDomainService.batchAddPreDataVariable(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-场景测试-共享/取消共享造数",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/sharePreData",
                    description = "共享/取消共享造数",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> sharePreData(SharePreDataReq req) {
        ComplexResult cr = checkAll()
                .on(req.getSceneCode(), new NotBlankValidator("造数code"))
                .on(req.getShareStatus(), new NotNullValidator("共享状态"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        SharePreDataCommand command = new SharePreDataCommand(req.getSceneCode());
        command.setShareStatus(req.getShareStatus());
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.sharePreData(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-场景测试-批量共享/批量取消共享造数",
            apiName = "tm/apitest/batchSharePreData",
            namespace = "luban"
    )
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Integer> batchSharePreData(BatchSharePreDataReq req) {
        ComplexResult cr = checkAll()
                .on(req.getSceneCodeList(), new NotNullValidator("造数code列表"))
                .on(req.getShareStatus(), new NotNullValidator("共享状态"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        String generateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        BatchSharePreDataCommand command = new BatchSharePreDataCommand(generateId);
        command.setSceneCodeList(req.getSceneCodeList());
        command.setShareStatus(req.getShareStatus());
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.batchSharePreData(command);
        return StatusCode.OK.build(command.getSceneCodeList().size());
    }

    @ZsmpApiOperation(
            description = "测试管理-场景测试-造数共享列表查询",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/listSharedModule",
                    description = "造数共享列表查询",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<List<SharedSceneModuleQueryResp>> listSharedModule(SharedSceneModuleQueryReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getParentCode(), new NotBlankValidator("上级code"))
                .when(StringUtil.isEmpty(req.getSearch()))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        req.setCurrentUserId(GatewayContext.currentUser().getSsoUserId());
        req.setCurrentUserName(GatewayContext.currentUser().getCnName());
        return StatusCode.OK.build(apiTestQueryDomainService.listSharedModule(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-场景测试-查询redis列表",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/listRedis",
                    description = "查询redis列表",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<List<ListRedisQueryResp>> listRedis(listRedisQueryReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(apiTestQueryDomainService.listRedis(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-场景测试-查询es列表",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/listES",
                    description = "查询es列表",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<List<ListESQueryResp>> listES(listESQueryReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(apiTestQueryDomainService.listES(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-场景测试-分页查询消费主题",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/pageMessageTopic",
                    description = "分页查询消费主题",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public PageResult<PageMessageTopicResp> pageMessageTopic(PageMessageTopicReq req) {
        PageInfo<PageMessageTopicResp> pageInfo = apiTestQueryDomainService.queryPageMessageTopic(req);
        return StatusCode.OK.build(pageInfo.getList(), pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal());
    }

    @ZsmpApiOperation(
            description = "测试管理-查询当前用户权限",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/checkUserPermission",
                    description = "查询es列表",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Boolean> checkUserPermission(CheckUserPermisionReq req) {
        log.info("checkUserPermission req : {}", JsonUtil.toJSON(req));
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        CheckUserPermisionCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        return StatusCode.OK.build(apiTestQueryDomainService.hasProductAuth(command.getProductCode(), command.getTransactor()));
    }

    @ZsmpApiOperation(
            description = "测试管理-场景测试-复制场景分组",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/copyModule",
                    description = "复制场景分组",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> copyModule(SceneModuleCopyReq req) {
        if (StringUtils.isBlank(req.getModuleCode())) {
            throw new ServiceException("复制的分组不能为空！");
        }
        String generateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        SceneModuleCopyCommand command = new SceneModuleCopyCommand(generateId);
        command.setModuleCode(req.getModuleCode());
        command.setProductCode(req.getProductCode());
        command.setSceneType(req.getSceneType());
        command.setProductName(req.getProductName());
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.copyModule(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-场景测试-查询接口列表（场景图列表筛选）",
            apiName = "tm/apitest/pageApiLite",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<PageApiLiteResp> pageApiLite(PageApiLiteReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPage(), new NotNullValidator("页数"))
                .on(req.getSize(), new NotNullValidator("数据大小"))
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        PageInfo<PageApiLiteResp> pageInfo = apiTestQueryDomainService.queryPageApiLite(req);
        return StatusCode.OK.build(pageInfo.getList(), pageInfo.getPageNum(), pageInfo.getSize(), pageInfo.getTotal());
    }

    @ZsmpApiOperation(
            description = "测试管理-场景测试-新增授权接口",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/authorize",
                    description = "新增授权接口",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> authorize(ApiTestAuthorizeReq req) {
        if (CollectionUtils.isEmpty(req.getAuthorizeProductList())) {
            throw new ServiceException("授权产品不能为空！");
        }
        if (CollectionUtils.isEmpty(req.getAuthorizeDbList())) {
            throw new ServiceException("授权数据库不能为空！");
        }
        DbAuthorizeCommand command = apiTestConvertor.convertor(req);
        command.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.dbAuthorize(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-场景测试-取消授权接口",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/revoke",
                    description = "取消授权接口",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> revoke(ApiTestRevokeReq req) {
        if (StringUtils.isBlank(req.getProductCode())) {
            throw new ServiceException("产品code不能为空!");
        }
        if (StringUtils.isBlank(req.getAuthorizeProductCode())) {
            throw new ServiceException("授权产品code不能为空！");
        }
        ApiTestRevokeCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.dbRevoke(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-场景测试-数据库授权列表查询接口",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/queryAuthorizeList",
                    description = "数据库授权列表查询接口",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<QueryAuthorizeListResp> queryAuthorizeList(QueryAuthorizeListReq req) {
        if (StringUtils.isBlank(req.getProductCode())) {
            throw new ServiceException("产品code不能为空!");
        }
        User user = GatewayContext.currentUser().toSimpleUser();
        return StatusCode.OK.build(apiTestQueryDomainService.queryAuthorizeList(req, user));
    }

    @ZsmpApiOperation(
            description = "测试管理-场景测试-调试日志",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/queryDebugLog",
                    description = "调试日志",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<QueryDebugLogResp> queryDebugLog(QueryDebugLinkReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getSceneCode(), new NotBlankValidator("场景code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(apiTestQueryDomainService.queryDebugLog(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-场景测试-查询调试链路",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/queryDebugLink",
                    description = "查询调试链路",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<QueryDebugLinkResp> queryDebugLink(QueryDebugLinkReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getSceneCode(), new NotBlankValidator("场景code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(apiTestQueryDomainService.queryDebugLink(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-场景测试-调试节点结果详情",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/queryDebugNodeResult",
                    description = "调试节点结果详情",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<QueryDebugNodeResultResp> queryDebugNodeResult(QueryDebugNodeResultReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getSceneCode(), new NotBlankValidator("场景code"))
                .on(req.getNodeCode(), new NotBlankValidator("节点code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(apiTestQueryDomainService.queryDebugNodeResult(req));
    }

    @ZsmpApiOperation(
            description = "测试管理-造数中心-保存调试链路",
            gatewayApi = @GatewayApi(
                    name = "tm/apitest/saveDebugLink",
                    description = "保存调试链路",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> saveDebugRecord(SaveDebugLinkReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getSceneCode(), new NotBlankValidator("场景code"))
                .on(req.getTaskId(), new NotBlankValidator("任务id"))
                .on(req.getStatus(), new NotBlankValidator("状态"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        SaveDebugInfoCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.saveDebugRecord(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口测试-保存全局配置接口",
            apiName = "tm/apitest/saveApiGlobalConfiguration",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> saveApiGlobalConfiguration(AddApiGlobalConfigurationReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        AddApiGlobalConfigurationCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.saveApiGlobalConfiguration(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口测试-查询全局配置接口",
            apiName = "tm/apitest/queryApiGlobalConfiguration",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<QueryApiGlobalConfigurationResp> queryApiGlobalConfiguration(String productCode) {
        ComplexResult cr = checkAll()
                .on(productCode, new NotBlankValidator("产品code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(apiTestQueryDomainService.queryApiGlobalConfiguration(productCode));
    }

    @ZApiOperation(
            description = "测试管理-接口自动化-查询异常用例数据",
            apiName = "tm/apitest/queryApiCaseException",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<ApiCaseExceptionResp> queryApiCaseException(QueryApiCaseExceptionReq req) {
        ComplexResult cr = checkAll()
                .on(req.getParentCaseCode(), new NotBlankValidator("接口用例code"))
                .on(req.getStatus(), new NotNullValidator("接口用例状态"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ApiCaseExceptionResp resp = apiTestQueryDomainService.queryApiCaseException(req);
        return StatusCode.OK.build(Collections.singletonList(resp), req.getPage(), req.getSize(), resp.getTotal());
    }

    @ZApiOperation(
            description = "测试管理-接口自动化-更新异常用例数据",
            apiName = "tm/apitest/updateApiCaseException",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> updateApiCaseException(UpdateApiCaseExceptionReq req) {
        ComplexResult cr = checkAll()
                .on(req.getCaseCode(), new NotBlankValidator("用例code"))
                .on(req.getKey(), new NotBlankValidator("变量名"))
                .on(req.getValue(), new NotBlankValidator("变量值"))
                .on(req.getAsserts(), new NotBlankValidator("断言"))
                .on(req.getApiConfigType(), new NotNullValidator("接口配置类型"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        UpdateApiCaseExceptionCommand command = apiTestConvertor.convert(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.updateApiCaseException(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口自动化-批量删除异常用例数据",
            apiName = "tm/apitest/batchDeleteApiCaseException",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchDeleteApiCaseException(BatchDeleteApiCaseExceptionReq req) {
        if (CollectionUtils.isEmpty(req.getCaseCodeList())) {
            throw new ServiceException("用例code列表不能为空！");
        }
        apiTestCommandDomainService.batchDeleteApiCaseException(req.getCaseCodeList(), GatewayContext.currentUser().toSimpleUser());
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口自动化-查询接口字段配置",
            apiName = "tm/apitest/queryApiFieldConfig",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<QueryApiFieldConfigResp>> queryApiFieldConfig(QueryApiFieldConfigReq req) {
        if (StringUtils.isBlank(req.getApiCode())) {
            throw new ServiceException("接口code不能为空！");
        }
        return StatusCode.OK.build(apiTestQueryDomainService.queryApiFieldConfig(req));
    }

    @ZApiOperation(
            description = "测试管理-接口自动化-生成异常用例数据",
            apiName = "tm/apitest/generateApiCaseException",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> generateApiCaseException(GenerateApiCaseExceptionReq req) {
        ComplexResult cr = checkAll()
                .on(req.getParentCaseCode(), new NotBlankValidator("接口用例code"))
                .on(req.getStatus(), new NotNullValidator("接口用例状态"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        GenerateApiCaseExceptionCommand command = apiTestConvertor.convert(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.generateApiCaseException(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "新增接口测试用例", apiName = "tm/apitest/addApiTestCase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<String> addApiTestCase(AddApiTestCaseReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getProductCode(), new LengthValidator(1, 50, "产品编号"))
                .on(req.getCaseName(), new NotBlankValidator("用例名称"))
                .on(req.getCaseName(), new LengthValidator(1, 100, "用例名称"))
                .on(req.getApiCode(), new NotBlankValidator("接口编码"))
                .on(req.getApiCode(), new LengthValidator(1, 50, "接口编码"))
                .on(req.getStatus(), new NotNullValidator("用例状态"))
                .on(req.getCaseType(), new NotNullValidator("用例类型"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        String generateId = aggregateIdGenerateService.generateId(AggregateType.TEST_CASE);
        AddApiTestCaseCommand command = apiTestConvertor.convertor(req, generateId);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.addApiTestCase(command);
        return StatusCode.OK.build(generateId);
    }

    @ZApiOperation(description = "编辑接口测试用例", apiName = "tm/apitest/editApiTestCase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Date> editApiTestCase(EditApiTestCaseReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getProductCode(), new LengthValidator(1, 50, "产品编号"))
                .on(req.getCaseCode(), new NotBlankValidator("用例编码"))
                .on(req.getCaseName(), new NotBlankValidator("用例名称"))
                .on(req.getCaseName(), new LengthValidator(1, 100, "用例名称"))
                .on(req.getApiCode(), new NotBlankValidator("接口编码"))
                .on(req.getApiCode(), new LengthValidator(1, 50, "接口编码"))
                .on(req.getStatus(), new NotNullValidator("用例状态"))
                .on(req.getCaseType(), new NotNullValidator("用例类型"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        EditApiTestCaseCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.editApiTestCase(command);
        return StatusCode.OK.build(new Date());
    }

    @ZApiOperation(description = "发布接口测试用例", apiName = "tm/apitest/publishApiTestCase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> publishApiTestCase(PublishApiTestCaseReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getProductCode(), new LengthValidator(1, 50, "产品编号"))
                .on(req.getCaseCode(), new NotBlankValidator("用例编码"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        PublishApiTestCaseCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.publishApiTestCase(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "批量发布接口测试用例", apiName = "tm/apitest/batchPublishApiTestCase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchPublishApiTestCase(BatchPublishApiTestCaseReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getPublishCaseFlag(), new NotNullValidator("发布维度"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (req.getPublishCaseFlag() && CollectionUtil.isEmpty(req.getCaseCodeList())) {
            throw new ServiceException("用例code不能为空");
        }
        if (!req.getPublishCaseFlag() && CollectionUtil.isEmpty(req.getApiCodeList())) {
            throw new ServiceException("接口code不能为空");
        }
        BatchPublishApiTestCaseCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        int bigSize = qcConfigBasicService.getBigSize();
        if (command.getBatchSize() > bigSize) {
            throw new ServiceException("批量操作一次最多支持" + bigSize + "条数据");
        }
        apiTestCommandDomainService.batchPublishApiTestCase(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口自动化-单接口调试",
            apiName = "tm/apitest/apiDebug",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<DebugApiTaskInfo> apiDebug(ApiDebugReq req) {
        if (StringUtils.isBlank(req.getCaseCode())) {
            throw new ServiceException("用例code不能为空！");
        }
        User user = GatewayContext.currentUser().toSimpleUser();
        return StatusCode.OK.build(linkMapDomainService.debugApi(req, user));
    }

    @ZApiOperation(
            description = "测试管理-接口测试-用例分页",
            apiName = "tm/apitest/pageApiTestCase",
            responseDateFormat = "yyyy-MM-dd HH:mm:ss",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<PageApiTestCaseVO> pageApiTestCase(PageApiTestCaseReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotNullValidator("产品code"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        PageApiTestCaseResp resp = apiTestQueryDomainService.queryApiTestCasePage(req);
        return StatusCode.OK.build(resp.getList(), req.getPage(), req.getSize(), resp.getTotal());
    }

    @ZApiOperation(
            description = "测试管理-接口测试-用例分页(子用例)",
            apiName = "tm/apitest/pageApiTestCaseChildren",
            responseDateFormat = "yyyy-MM-dd HH:mm:ss",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<PageApiTestCaseVO> pageApiTestCaseChildren(PageApiTestCaseChildrenReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getParentCaseCode(), new NotBlankValidator("父用例code"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        PageApiTestCaseChildrenResp resp = apiTestQueryDomainService.queryApiTestCasePageChildren(req);
        return StatusCode.OK.build(resp.getList(), req.getPage(), req.getSize(), resp.getTotal());
    }

    @ZApiOperation(
            description = "测试管理-接口测试-批量操作校验",
            apiName = "tm/apitest/verifyBatchOperation",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<CheckBatchOperationVO> verifyBatchOperation(VerifyBatchOperationReq req) {
        ComplexResult cr = checkAll()
                .on(req.getApiCaseCodeList(), new NotNullValidator("用例编号"))
                .on(req.getProductCode(), new NotNullValidator("产品code"))
                .on(req.getOperation(), new NotNullValidator("操作类型"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        VerifyBatchOperationCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        CheckBatchOperationVO result = apiTestCommandDomainService.verifyBatchOperation(command);
        return StatusCode.OK.build(result);
    }

    @ZApiOperation(
            description = "测试管理-接口测试-修改用例状态",
            apiName = "tm/apitest/changeApiCaseStatus",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> changeApiCaseStatus(ChangeApiCaseStatusReq req) {
        ComplexResult cr = checkAll()
                .on(req.getApiCaseCode(), new NotBlankValidator("用例编号"))
                .on(req.getStatus(), new NotNullValidator("状态"))
                .on(req.getOperation(), new NotNullValidator("操作类型"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ChangeApiCaseStatusCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.changeApiCaseStatus(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口测试-批量修改用例状态",
            apiName = "tm/apitest/batchChangeApiCaseStatus",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchChangeApiCaseStatus(BatchChangeApiCaseStatusReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getApiCaseCodeList(), new NotNullValidator("用例编号集合"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        BatchChangeApiCaseStatusCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        int bigSize = qcConfigBasicService.getBigSize();
        if (CollectionUtil.isNotEmpty(command.getApiCaseCodeList()) && command.getApiCaseCodeList().size() > bigSize) {
            throw new ServiceException("批量操作一次最多支持" + bigSize + "条数据");
        }
        apiTestCommandDomainService.batchChangeApiCaseStatus(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口测试-获取执行明细列表",
            apiName = "tm/apitest/queryApiTestCaseExecuteDetail",
            responseDateFormat = "yyyy-MM-dd HH:mm:ss",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<ApiTestCaseExecuteDetailResp>> queryApiTestCaseExecuteDetail(ApiTestCaseExecuteDetailReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getTaskCodeList(), new NotNullValidator("任务编号集合"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(apiTestQueryDomainService.queryApiTestCaseExecuteDetail(req));
    }

    @ZApiOperation(
            description = "测试管理-接口测试-刷新场景图关联接口",
            apiName = "tm/apitest/refreshSceneApiRelation",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> refreshSceneApiRelation(String productCode) {
        apiTestCommandDomainService.refreshSceneApiRelation(productCode);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-造数中心-收藏/取消收藏造数接口",
            apiName = "tm/apitest/favoritesPreData",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> favoritesPreData(FavoritesPreDataReq req) {
        ComplexResult cr = checkAll()
                .on(req.getSceneCode(), new NotBlankValidator("造数code"))
                .on(req.getFavoritesStatus(), new NotNullValidator("收藏状态"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        FavoritesPreDataCommand command = new FavoritesPreDataCommand(req.getSceneCode());
        command.setFavoritesStatus(req.getFavoritesStatus());
        command.setProductCode(req.getProductCode());
        command.setType(req.getType());
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.favoritesPreData(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口测试-刷新场景图接口code",
            apiName = "tm/apitest/refreshSceneApiCode",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> refreshSceneApiCode(String productCode) {
        apiTestCommandDomainService.refreshSceneApiCode(productCode);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口测试-批量生成接口用例",
            apiName = "tm/apitest/batchGenerateApiCase",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchGenerateApiCase(BatchGenerateApiCaseReq req) {
        BatchGenerateApiCaseCommand command = new BatchGenerateApiCaseCommand(null);
        command.setApiCodes(req.getApiCodes());
        command.setCaseCodes(req.getCaseCodes());
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.batchGenerateApiCase(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口测试-导入jmx脚本解析",
            apiName = "tm/apitest/uploadJmxFile",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> uploadJmxFile(JmxFileUploadReq req) {
        JmxFileUploadCommand command = apiTestConvertor.convertor(req);
        command.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.uploadJmxFile(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口测试-刷新用例造数变更标识",
            apiName = "tm/apitest/refreshApiCasePreDataUpdate",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> refreshApiCasePreDataUpdate() {
        apiTestCommandDomainService.refreshApiCasePreDataUpdate();
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口测试-清除已删除场景与接口关联关系",
            apiName = "tm/apitest/deleteSceneApiRelation",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> deleteSceneApiRelation() {
        apiTestCommandDomainService.refreshApiTestRelateScene();
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口测试-刷新接口关联字段",
            apiName = "tm/apitest/refreshApiTestField",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> refreshApiTestField(RefreshApiTestFieldReq req) {
        apiTestCommandDomainService.refreshApiTestField(req);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口测试-场景用例批量加入测试计划",
            apiName = "tm/apitest/sceneBatchAddToTestPlan",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> sceneBatchAddToTestPlan(SceneBatchToTestPlanReq req) {
        if (StringUtils.isEmpty(req.getProductCode())) {
            throw new ServiceException("产品编号不能为空");
        }
        if (CollectionUtils.isEmpty(req.getTestPlanList())) {
            throw new ServiceException("测试计划不能为空");
        }
        if (CollectionUtils.isEmpty(req.getSceneCodeList())) {
            throw new ServiceException("场景编号不能为空");
        }
        SceneBatchAddToTestPlanCommand command = sceneConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        sceneCommandDomainService.sceneBatchAddToTestPlan(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-场景测试-添加标签功能",
            apiName = "tm/apitest/addSceneTag",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addSceneTag(AddSceneTagReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        AddSceneTagCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.addSceneTag(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-接口自动化-复制手工用例",
            apiName = "tm/apitest/copyApiTestCase",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> copyApiTestCase(CopyApiTestCaseReq req) {
        ComplexResult cr = checkAll()
                .on(req.getCaseCode(), new NotBlankValidator("用例code"))
                .on(req.getCaseName(), new NotBlankValidator("用例名称"))
                .on(req.getCaseName(), new LengthValidator(1, 100, "用例名称"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        CopyApiTestCaseCommand command = apiTestConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.copyApiTestCase(command);
        return StatusCode.OK.build();
    }

    @Override
    public List<ApiCaseDocVO> listApiCaseByDocIds(String productCode, List<Long> docIds) {
        return apiTestQueryDomainService.listApiCaseByDocIds(productCode,docIds);
    }

    @ZApiOperation(description = "文档运行历史转case", apiName = "tm/apitest/addApiTestCaseByDocInvokeHistory", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<String> addApiTestCaseByDocInvokeHistory(DocInvokeHistoryToApiTestCaseVO req) {
        String generateId = aggregateIdGenerateService.generateId(AggregateType.TEST_CASE);
        AddApiTestCaseCommand command =apiTestCommandDomainService.buildAddApiTestCaseCommandByDocInvokeHistory(req, generateId);
        GatewayContext.fillCurrentUser(command);
        apiTestCommandDomainService.addApiTestCase(command);

        PublishApiTestCaseCommand publishApiTestCaseCommand = new PublishApiTestCaseCommand(generateId);
        publishApiTestCaseCommand.setProductCode(req.getProductCode());
        publishApiTestCaseCommand.setCaseCode(generateId);
        GatewayContext.fillCurrentUser(publishApiTestCaseCommand);
        apiTestCommandDomainService.publishApiTestCase(publishApiTestCaseCommand);
        return StatusCode.OK.build(generateId);
    }
}
