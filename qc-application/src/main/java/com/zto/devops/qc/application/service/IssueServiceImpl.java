package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.baidu.unbiz.fluentvalidator.ComplexResult;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.command.impexp.AddExpCommand;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.entity.UserInfo;
import com.zto.devops.framework.client.entity.action.ActionLog;
import com.zto.devops.framework.client.entity.action.PageActionLogVO;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.enums.impexp.ExpProcessorEnum;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.exception.FluentException;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.query.ListLogQuery;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.time.DateUtil;
import com.zto.devops.framework.common.validator.LengthValidator;
import com.zto.devops.framework.common.validator.NotBlankValidator;
import com.zto.devops.framework.common.validator.NotNullValidator;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.service.AsyncImpExpService;
import com.zto.devops.qc.application.converter.IssueRespConverter;
import com.zto.devops.qc.application.converter.PageIssueQueryConverter;
import com.zto.devops.qc.application.converter.issue.AddIssueCommandConverter;
import com.zto.devops.qc.application.converter.issue.EditIssueCommandConverter;
import com.zto.devops.qc.client.constants.Constant;
import com.zto.devops.qc.client.enums.constants.DefaultValueEnum;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.qc.client.model.issue.command.*;
import com.zto.devops.qc.client.model.issue.entity.*;
import com.zto.devops.qc.client.model.issue.query.*;
import com.zto.devops.qc.client.model.relevantUser.entity.MyTaskTotalVO;
import com.zto.devops.qc.client.model.relevantUser.query.*;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.devops.qc.client.model.report.entity.IssueNumStatisticsVO;
import com.zto.devops.qc.client.model.rpc.project.VersionBaseEvent;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTagsByBusinessCodeQuery;
import com.zto.devops.qc.client.service.issue.IIssueService;
import com.zto.devops.qc.client.service.issue.model.*;
import com.zto.devops.qc.domain.gateway.repository.ITestcaseRelationRepository;
import com.zto.devops.qc.domain.service.*;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWSsoAuth;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.titans.common.util.StringUtil;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayAuth;
import com.zto.zsmp.annotation.gateway.GatewayAuthType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * 缺陷管理
 */
@Slf4j
@Service
@ZsmpService(name = "缺陷接口目录")
public class IssueServiceImpl extends GatewayBase implements IIssueService {

    @Autowired
    private TagQueryDomainService tagQueryDomainService;

    @Autowired
    private IssueQueryDomainService issueQueryDomainService;

    @Autowired
    private MyTaskQueryDomainService taskQueryDomainService;

    @Autowired
    private EventLogQueryDomainService eventLogQueryDomainService;

    @Autowired
    private IssueCommandDomainService issueCommandDomainService;

    @Autowired
    private IssueRespConverter issueRespConverter;

    @Autowired
    private AddIssueCommandConverter addIssueCommandConverter;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private IssueDomainService issueDomainService;

    @Autowired
    private PageIssueQueryConverter pageIssueQueryConverter;

    @Autowired
    private EditIssueCommandConverter editIssueCommandConverter;

    @Autowired
    private ITestcaseRelationRepository iTestcaseRelationRepository;

    @Autowired
    private AsyncImpExpService asyncImpExpService;

    private static final Integer ISSUE_DESC_MAX_LENGTH = 8000;

    @Override
    public Integer requirementQuery(RequirementQuery query) {
        return issueQueryDomainService.requirementQuery(query);
    }

    @Override
    public Map<String, Long> getIssueCountByFixedVersionCode(List<String> fixedVersionCodes) {
        return issueQueryDomainService.getIssueCountByFixedVersionCode(fixedVersionCodes);
    }

    @Override
    public PageIssueVO pageIssueQuery(PageIssueQuery query) {
        return issueQueryDomainService.pageIssueQuery(query);
    }

    @Override
    public List<RelatedMatterStatusCountVO> listRelatedIssueStatusCountQuery(ListRelatedIssueStatusCountQuery query) {
        return issueQueryDomainService.listRelatedIssueStatusCountQuery(query);
    }

    @Override
    public List<IssueVO> listIssueForVersionQuery(ListIssueForVersionQuery query) {
        return issueQueryDomainService.listIssueForVersionQuery(query);
    }

    @Override
    public List<MyTaskVO> myIssueObjQuery(MyIssueObjQuery query) {
        return taskQueryDomainService.myIssueObjQuery(query);
    }

    @Override
    public MyTaskTotalVO myIssueTotalQuery(MyIssueTotalQuery query) {
        return taskQueryDomainService.myIssueTotalQuery(query);
    }

    @Override
    public Long listRelevantUserTypeCountQuery(ListRelevantUserTypeCountQuery query) {
        return taskQueryDomainService.listRelevantUserTypeCountQuery(query);
    }

    @Override
    public PageIssueVO pageIssueThingQuery(PageIssueThingQuery query) {
        return issueQueryDomainService.pageIssueThingQuery(query);
    }

    @Override
    public List<IssueBaseVO> listBySprintQuery(ListBySprintQuery query) {
        return issueQueryDomainService.listBySprintQuery(query);
    }

    @Override
    public List<IssueVO> simpleIssueQuery(SimpleIssueQuery query) {
        return issueQueryDomainService.simpleIssueQuery(query);
    }

    @ZApiOperation(description = "一站式研发-缺陷域日志接口",
            apiName = "qc/logs", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<ActionLog> listLogs(ListActionLogReq req) {
        ListLogQuery query = new ListLogQuery();
        query.setAggregateId(req.getBusinessCode());
        query.setPage(req.getPage());
        query.setSize(req.getSize());

        PageActionLogVO actionVO = eventLogQueryDomainService.queryActionLog(query);
        return StatusCode.OK.build(actionVO.getLogs(), req.getPage(), req.getSize(), actionVO.getTotal());
    }

    @Override
    public List<IssueVO> issueQuery(IssueQuery issueQuery) {
        return issueQueryDomainService.issueQuery(issueQuery);
    }

    @Override
    public void handleLinkSprintIssueCommand(List<LinkSprintIssueCommand> commands) {
        issueCommandDomainService.handleLinkSprintIssueCommand(commands);
    }

    @Override
    public void handleVersionPlanedEvent(VersionPlannedReq req) {
        issueCommandDomainService.handleVersionPlanedEvent(req);
    }

    @ZApiOperation(description = "一站式研发-交付验证", apiName = "qc/issue/deliveryValidated", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> deliveryValidated(DeliveryValidatedReq req) {
        DeliveryValidatedCommand command = new DeliveryValidatedCommand(req.getCode());
        command.setContent(req.getContent());
        Version fixVersion = new Version();
        fixVersion.setName(req.getFixVersion());
        fixVersion.setCode(req.getFixVersionCode());
        command.setFixVersion(fixVersion);
        GatewayContext.fillCurrentUser(command);
        command.setPermissions(GatewayContext.currentUser().getPermissions());
        issueCommandDomainService.deliveryIssue(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "一站式研发-缺陷详情", apiName = "qc/issue/detail", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<IssueResp> findIssueByCode(FindIssueByCodeReq req) {
        FindIssueByCodeQuery query = new FindIssueByCodeQuery();
        if (GatewayContext.currentUser() != null) {
            query.setPermissions(GatewayContext.currentUser().getPermissions());
        }
        query.setCode(req.getCode());
        query.setIssue(req.getIssue());
        query.setCurrentUserId(GatewayContext.currentUser().toSimpleUser().getUserId());
        IssueVO issueVO = issueQueryDomainService.findIssueByCode(query);
        if (issueVO == null) {
            throw new ServiceException("没有对应的缺陷数据!");
        }
        IssueResp issueResp = issueRespConverter.convert(issueVO);
        issueResp.setEnumrationDesc();
        issueResp.setFixVersionName(StringUtils.isNotBlank(issueResp.getFixVersionName()) ? issueResp.getFixVersionName() : null);
        UserInfo user = GatewayContext.currentUser();
        if (user.getPermissions() != null && user.getPermissions().contains("test-issue") && query.getIssue() != null && !query.getIssue()) {
            List<Event> list = new ArrayList<>();
            Event event = new Event("testExamination");
            if (issueResp.getExamination()) {
                event.setCode("testExaminationResult");
            }
            list.add(event);
            issueResp.setOperableEvents(list);
        }
        return StatusCode.OK.build(issueResp);
    }

    @ZApiOperation(description = "查询版本未关闭缺陷",
            apiName = "qc/issue/findUnClosedByVersionCodes", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<IssueVO>> findUnClosedByVersionCodes(FindUnClosedReq req) {
        if (CollectionUtils.isEmpty(req.getVersionCodes())) {
            throw new ServiceException("参数不能为空！");
        }
        List<IssueVO> list = issueQueryDomainService.listUnClosedIssueByVersionCodesQuery(req.getVersionCodes());
        return StatusCode.OK.build(list);
    }

    @Override
    public Result<List<IssueVO>> findUnClosedByFixedVersionCodes(FindUnClosedReq req) {
        if (CollectionUtils.isEmpty(req.getVersionCodes())) {
            throw new ServiceException("参数不能为空！");
        }
        List<IssueVO> list = issueQueryDomainService.listUnClosedIssueByFixedVersionCodes(req.getVersionCodes());
        return StatusCode.OK.build(list);
    }

    @ZApiOperation(description = "根据业务code（发现版本code 或 修复版本code）查询遗留缺陷",
            apiName = "qc/issue/issueLegacyList", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<IssueLegacyListResp>> issueLegacyList(IssueLegacyListReq req) {
        if (!req.isValidReportType()) {
            throw new ServiceException("报告类型异常!");
        }

        IssueLegacyListQuery query = new IssueLegacyListQuery();
        query.setBusinessCode(req.getBusinessCode());
        query.initTestMethodList(req.getReportType());

        List<IssueLegacyVO> issueLegacyVOList = issueQueryDomainService.issueLegacyListQuery(query);
        List<IssueLegacyListResp> resp = issueRespConverter.convertLegacyList(issueLegacyVOList);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "根据业务code（发现版本code or 修复版本code）统计issue个数",
            apiName = "qc/issue/issueNumStatistic", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<IssueNumStatisticsResp> issueNumStatistic(IssueNumStatisticsReq req) {
        if (!req.isValidReportType()) {
            throw new ServiceException("报告类型异常!");
        }

        IssueNumStatisticQuery query = new IssueNumStatisticQuery();
        query.setBusinessCode(req.getBusinessCode());
        query.initTestMethodList(req.getReportType());

        IssueNumStatisticsVO issueLegacyVO = issueQueryDomainService.issueNumStatisticQuery(query);
        IssueNumStatisticsResp resp = issueRespConverter.convertStatistic(issueLegacyVO);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "根据业务code（发现版本code or 修复版本code）统计用户体验issue个数",
            apiName = "qc/issue/issueUiTestNumStatistic", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<IssueNumStatisticsResp> issueUiTestNumStatistic(IssueNumStatisticsReq req) {
        IssueNumStatisticQuery query = new IssueNumStatisticQuery();
        query.setBusinessCode(req.getBusinessCode());
        query.initTestMethodList(req.getReportType());

        IssueNumStatisticsVO issueLegacyVO = issueQueryDomainService.issueUiTestNumStatisticQuery(query);
        IssueNumStatisticsResp resp = issueRespConverter.convertStatistic(issueLegacyVO);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "缺陷关联需求", apiName = "qc/issue/issueRelated", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> issueRelated(IssueRelatedRequirementReq req) {
        IssueRelatedRequirementCommand command = new IssueRelatedRequirementCommand(req.getIssueCode());
        command.setRequirementCode(req.getRequirementCode());
        command.setRequirementLevel(req.getRequirementLevel());
        command.setRequirementName(req.getRequirementName());
        command.setPermissions(GatewayContext.currentUser().getPermissions());
        GatewayContext.fillCurrentUser(command);
        issueCommandDomainService.issueRelatedRequirementCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "一站式研发--创建缺陷", apiName = "qc/issue/add", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> addIssue(AddIssueReq req) {
        ComplexResult cr = checkAll()
                .on(req.getTitle(), new NotBlankValidator("标题"))
                .on(req.getTitle(), new LengthValidator(-1, 240, "标题"))
//                .on(req.getDescription(), new LengthValidator(-1, 8000, "描述"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (StringUtil.isNotBlank(req.getDescription()) && req.getDescription().length() > ISSUE_DESC_MAX_LENGTH) {
            throw new ServiceException("缺陷描述字符长度不能超过" + ISSUE_DESC_MAX_LENGTH);
        }
        this.validateAddIssue(req);
        AddIssueCommand command = new AddIssueCommand(aggregateIdGenerateService.generateId(AggregateType.ISSUE));
        addIssueCommandConverter.convert(req, command);
        command.setVersionConfirm("before");
        GatewayContext.fillCurrentUser(command);

        if (CollectionUtil.isNotEmpty(req.getAttachments())) {
            for (AttachmentVO attachmentVO : req.getAttachments()) {
                attachmentVO.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                attachmentVO.setBusinessCode(command.getAggregateId());
                attachmentVO.setDomain(DomainEnum.ISSUE);
                attachmentVO.setType(AttachmentTypeEnum.FILE);
                attachmentVO.setDocumentType(AttachmentDocumentTypeEnum.BUSINESS);
            }
            command.setAttachments(req.getAttachments());
        }

        if (CollectionUtil.isNotEmpty(req.getTags())) {
            for (TagVO tagVO : req.getTags()) {
                tagVO.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                tagVO.setBusinessCode(command.getAggregateId());
                tagVO.setDomain(DomainEnum.ISSUE);
                tagVO.setType(TagTypeEnum.BUSINESS);
            }
            command.setTags(req.getTags());
        }
        if (CollectionUtil.isNotEmpty(req.getCcUserList())) {
            for (RelevantUserVO relevantUserVO : req.getCcUserList()) {
                relevantUserVO.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                relevantUserVO.setBusinessCode(command.getAggregateId());
                relevantUserVO.setDomain(DomainEnum.ISSUE);
                relevantUserVO.setType(RelevantUserTypeEnum.CC);
            }
            command.setRelevantUserVOS(req.getCcUserList());
        }
        if(StringUtil.isNotBlank(req.getTestUserName())&&Objects.nonNull(req.getTestUserId())){
            User tester = new User(req.getTestUserId(),req.getTestUserName());
            command.setTester(tester);
        }else{
            User tester = new User(GatewayContext.currentUser().getSsoUserId(),GatewayContext.currentUser().getCnName());
            command.setTester(tester);
        }
        issueCommandDomainService.addIssue(command);
        return StatusCode.OK.build();
    }


    @ZApiOperation(description = "一站式研发--延期修复", apiName = "qc/issue/delayFix", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> delayFixIssue(DelayFixIssueReq req) {
        DelayFixIssueCommand command = new DelayFixIssueCommand(req.getCode());
        command.setContent(req.getContent());
        command.setReason(req.getReason());
        command.setPermissions(GatewayContext.currentUser().getPermissions());
        Version version = new Version();
        version.setCode(DefaultValueEnum.VERSION_VALUE.getValue());
        version.setName("");
        command.setFixVersion(version);
        GatewayContext.fillCurrentUser(command);
        issueCommandDomainService.delayFixIssue(command);
        return StatusCode.OK.build();
    }


    @ZApiOperation(description = "一站式研发--退回开发修复", apiName = "qc/issue/backToRepair", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> backToRepair(BackToRepairReq req) {
        BackToRepairCommand command = new BackToRepairCommand(req.getCode());
        command.setContent(req.getContent());
        command.setReason(req.getReason());
        command.setPermissions(GatewayContext.currentUser().getPermissions());
        GatewayContext.fillCurrentUser(command);
        issueCommandDomainService.backToRepair(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "一站式研发--缺陷 流转", apiName = "qc/issue/circulation", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> circulation(CirculationIssueReq req) {
        CirculationIssueCommand command = new CirculationIssueCommand(req.getCode());
        command.setContent(req.getContent());
        command.setDevelopId(req.getDevelopId());
        command.setDevelopName(req.getDevelopName());
        command.setTestId(req.getTestId());
        command.setTestName(req.getTestName());
        command.setPermissions(GatewayContext.currentUser().getPermissions());
        GatewayContext.fillCurrentUser(command);
        issueCommandDomainService.circulation(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "一站式研发--确认关闭 缺陷", apiName = "qc/issue/close", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> confirmClose(ConfirmCloseReq req) {
        ConfirmCloseCommand command = new ConfirmCloseCommand(req.getCode());
        command.setContent(req.getContent());
        command.setPermissions(GatewayContext.currentUser().getPermissions());
        GatewayContext.fillCurrentUser(command);
        issueCommandDomainService.confirmClose(command);
        return StatusCode.OK.build();
    }

    private void validateAddIssue(AddIssueReq req) {
        ComplexResult cr = checkAll()
                .on(req.getTitle(), new LengthValidator(-1, 150, "缺陷标题"))
                //.on(req.getDescription(), new NotBlankValidator("缺陷描述"))
                .on(req.getProductCode(), new NotBlankValidator("产品名称"))
/*                .on(req.getFindVersionCode(), new NotBlankValidator("发现版本"))*/
                .on(req.getDevelopUserId(), new NotNullValidator("开发负责人"))
                .on(req.getPriority(), new NotNullValidator("缺陷优先级"))
                .on(req.getRootCause(), new NotNullValidator("BUG根源"))
                .on(req.getType(), new NotNullValidator("BUG类型"))
                .on(req.getTestMethod(), new NotNullValidator("测试方法"))
                .on(req.getRepetitionRate(), new NotNullValidator("重现概率"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
    }

    @ZApiOperation(description = "一站式研发--重新打开 缺陷", apiName = "qc/issue/reopen", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> reopenIssue(ReopenIssueReq req) {
        ReopenIssueCommand command = new ReopenIssueCommand(req.getCode());
        command.setContent(req.getContent());
        command.setPermissions(GatewayContext.currentUser().getPermissions());
        GatewayContext.fillCurrentUser(command);
        issueDomainService.reopenIssue(command);
//        awaitCommandGateway.sendSync(command, IssueHandler.ISSUE_GROUP_NAME);
//        awaitCommandGateway.syncSend(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "一站式研发--开始修复缺陷", apiName = "qc/issue/startFix", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> startFixIssue(StartFixIssueReq req) {
        StartFixIssueCommand command = new StartFixIssueCommand(req.getCode());
        command.setContent(req.getContent());
        command.setPermissions(GatewayContext.currentUser().getPermissions());
        GatewayContext.fillCurrentUser(command);
//        awaitCommandGateway.sendSync(command, IssueHandler.ISSUE_GROUP_NAME);
//        awaitCommandGateway.syncSend(command);
        issueDomainService.startFixIssue(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "一站式研发--验证通过并关闭", apiName = "qc/issue/validatedAccessClosed", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> validatedAccessClosed(ValidatedAccessClosedReq req) {
        ValidatedAccessClosedCommand command = new ValidatedAccessClosedCommand(req.getCode());
        command.setContent(req.getContent());
        command.setFixVersion(req.getFixVersion());
        command.setFixVersionCode(req.getFixVersionCode());
        command.setPermissions(GatewayContext.currentUser().getPermissions());
        GatewayContext.fillCurrentUser(command);
//        awaitCommandGateway.sendSync(command, IssueHandler.ISSUE_GROUP_NAME);
//        awaitCommandGateway.syncSend(command);
        issueDomainService.validatedAccessClosed(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-获取缺陷关联的用例", apiName = "tm/issue/getRelationTestcase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<RelationTestcaseListVO>> getRelationTestcaseList(String code) {
        if (StringUtil.isEmpty(code)) {
            throw new ServiceException("缺陷不能为空！");
        }
        RelationTestcaseListQuery query = new RelationTestcaseListQuery();
        query.setCode(code);
        List<RelationTestcaseListVO> vos = iTestcaseRelationRepository.getRelationTestcaseList(query);
        return StatusCode.OK.build(vos);
    }

    @ZApiOperation(description = "宝盒移动端-查询缺陷详情",
            apiName = "qc/issue/detailM", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<IssueResp> findIssueByCodeM(String issueCode) {
        FindIssueByCodeQuery query = new FindIssueByCodeQuery();
        if (GatewayContext.currentUser() != null) {
            query.setPermissions(GatewayContext.currentUser().getPermissions());
        }
        query.setCode(issueCode);
        query.setCurrentUserId(GatewayContext.currentUser().toSimpleUser().getUserId());
        log.info("宝盒移动端-查询缺陷详情{}-{}", issueCode, query.getCurrentUserId());
        IssueVO issueVO = issueQueryDomainService.findIssueByCode(query);
        if (null != issueVO) {
            IssueResp issueResp = issueRespConverter.convert(issueVO);
            issueResp.setEnumrationDesc();
            issueResp.setFixVersionName(StringUtil.isNotBlank(issueResp.getFixVersionName()) ? issueResp.getFixVersionName() : null);
            List<Event> operableEvents = issueResp.getOperableEvents();
            issueResp.setOperableEvents(getSortBtn(operableEvents));
            UserInfo user = GatewayContext.currentUser();
            if (user.getPermissions() != null && user.getPermissions().contains("test-issue") && query.getIssue() != null && !query.getIssue()) {
                Event event = new Event("testExamination");
                List<Event> list = new ArrayList<>();
                if (issueResp.getExamination()) {
                    event.setCode("testExaminationResult");
                }
                list.add(event);
                issueResp.setOperableEvents(list);
            }
            return StatusCode.OK.build(issueResp);
        }
        return StatusCode.OK.build(null);
    }

    private List<Event> getSortBtn(List<Event> operableEvents) {
        List<Event> sortOperableEvents = new ArrayList<>();
        if (CollectionUtil.isEmpty(operableEvents)) {
            return sortOperableEvents;
        }
        Event fix = operableEvents.stream().filter(t -> IssueEvent.FIX.name().equals(t.getCode())).findFirst().orElse(null);
        if (fix != null) {
            sortOperableEvents.add(fix);
        }
        Event delay = operableEvents.stream().filter(t -> IssueEvent.DELAY.name().equals(t.getCode())).findFirst().orElse(null);
        if (delay != null) {
            sortOperableEvents.add(delay);
        }
        Event deliver = operableEvents.stream().filter(t -> IssueEvent.DELIVER.name().equals(t.getCode())).findFirst().orElse(null);
        if (deliver != null) {
            sortOperableEvents.add(deliver);
        }
        Event close = operableEvents.stream().filter(t -> IssueEvent.TEST_PASS_CLOSE.name().equals(t.getCode())).findFirst().orElse(null);
        if (close != null) {
            sortOperableEvents.add(close);
        }
        Event ret = operableEvents.stream().filter(t -> IssueEvent.RETURN.name().equals(t.getCode())).findFirst().orElse(null);
        if (ret != null) {
            sortOperableEvents.add(ret);
        }
        Event reopen = operableEvents.stream().filter(t -> IssueEvent.REOPEN.name().equals(t.getCode())).findFirst().orElse(null);
        if (reopen != null) {
            sortOperableEvents.add(reopen);
        }
        return sortOperableEvents;
    }

    @ZApiOperation(description = "一站式研发--编辑缺陷", apiName = "qc/issue/edit", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> editIssue(EditIssueReq req) {
        ComplexResult cr = checkAll()
                .on(req.getTitle(), new NotBlankValidator("标题"))
                .on(req.getTitle(), new LengthValidator(-1, 240, "标题"))
              //  .on(req.getDescription(), new LengthValidator(-1, 8000, "描述"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (StringUtil.isNotBlank(req.getDescription()) && req.getDescription().length() > ISSUE_DESC_MAX_LENGTH) {
            throw new ServiceException("缺陷描述字符长度不能超过" + ISSUE_DESC_MAX_LENGTH);
        }
        EditIssueCommand command = editIssueCommandConverter.convert(req);
        command.setAggregateId(req.getCode());
        command.setPermissions(GatewayContext.currentUser().getPermissions());
        GatewayContext.fillCurrentUser(command);
        issueCommandDomainService.editIssue(command);
        return StatusCode.OK.build();
    }


    @ZApiOperation(description = "缺陷-分页查询",
            timeout = 5000,
            apiName = "qc/issue/page",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public PageResult<IssueResp> pageIssue(PageIssueReq req) {
        if (CollectionUtil.isNotEmpty(req.getTagName()) && StringUtil.isNotBlank(req.getTagName().get(0)) && req.getTagName().get(0).length() > 10) {
            throw new ServiceException("标签字数不能超过10个字！");
        }
        PageIssueQuery query = pageIssueQueryConverter.convert(req);
        query.setCurrentUserId(GatewayContext.currentUser().getUser_id());
        PageIssueVO pageIssueVO = issueQueryDomainService.pageIssue(query);
        List<IssueResp> list = issueRespConverter.convert(pageIssueVO.getList());
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(IssueResp::setEnumrationDesc);
            for(IssueResp resp : list){
                if(resp.getStatus().name().equals(IssueStatus.CLOSED.name())){
                    resp.setWarn("");
                    resp.setWarnDay(0);
                }
            }
            return StatusCode.OK.build(list, req.getPage(), req.getSize(), pageIssueVO.getTotal());
        }
        return StatusCode.OK.build(list, req.getPage(), req.getSize(), pageIssueVO.getTotal());
    }


    @ZApiOperation(description = "缺陷-泳道列表", apiName = "qc/issue/laneIssueList", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<LaneIssueCtxVO> listLaneQuery(PageIssueReq req) {
        PageLaneIssueQuery query = pageIssueQueryConverter.convertLaneQuery(req);
        query.setCurrentUserId(GatewayContext.currentUser().getUser_id());
        GatewayContext.fillCurrentUser(query);
        LaneIssueCtxVO vo = issueQueryDomainService.listLaneQuery(query);
        return StatusCode.OK.build(vo);
    }

    @ZApiOperation(description = "拒绝缺陷", apiName = "qc/issue/refused", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> refusedIssue(RefusedIssueReq req) {
        RefusedIssueCommand command = new RefusedIssueCommand(req.getCode());
        command.setContent(req.getContent());
        command.setReason(req.getReason());
        command.setPermissions(GatewayContext.currentUser().getPermissions());
        GatewayContext.fillCurrentUser(command);
        issueCommandDomainService.refusedIssue(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "删除缺陷", apiName = "qc/issue/remove", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> removeIssue(RemoveIssueReq req) {
        RemoveIssueCommand command = new RemoveIssueCommand(req.getCode());
        command.setContent(req.getContent());
        command.setPermissions(GatewayContext.currentUser().getPermissions());
        GatewayContext.fillCurrentUser(command);
        issueCommandDomainService.removeIssue(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "一站式研发--版本甬道编辑缺陷", apiName = "qc/issue/editIssueByVersionLane", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> editIssueByVersionLane(EditIssueByLaneReq req) {
        EditIssueCommand command = editIssueCommandConverter.convert(req);
        command.setAggregateId(req.getCode());
        command.setPermissions(GatewayContext.currentUser().getPermissions());
        GatewayContext.fillCurrentUser(command);
        issueCommandDomainService.editIssue(command);
        return StatusCode.OK.build();
    }


    /*************************************** 外域的dubbo接口 ***************************************************/
    @Override
    public ListMyTaskVO myIssueQuery(MyIssueQuery query) {
        return issueQueryDomainService.myIssueQuery(query);
    }

    /*************************************** 对外接口 begin***************************************************/

    @ZsmpApiOperation(
            name = "对外-通过发现版本code查询所有缺陷详情",
            description = "对外-通过发现版本code查询所有缺陷详情",
            gatewayApi = @GatewayApi(namespace = "luban",
                    name = "issueApi/findIssueByVersionCode",
                    description = "对外-通过发现版本code查询所有缺陷详情",
                    tags = "feature-V3.20.0",
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    auth = @GatewayAuth(type = GatewayAuthType.DIGEST, enable = true, value = Constant.GATEWAY_SIGN)
            ))
    @Override
    public Result<List<IssueResp>> findIssueByVersionCode(FindIssueByVersionCodeReq req) {
        if (Objects.isNull(req.getVersionCode())) {
            throw new ServiceException("参数不能为空");
        }
        IssueQuery query = new IssueQuery();
        query.setFindVersionList(Arrays.asList(req.getVersionCode()));
        List<IssueVO> issueVOList = issueQueryDomainService.issueQuery(query);
        List<IssueResp> issueRespList = issueRespConverter.convertIssueResp(issueVOList);
        return StatusCode.OK.build(issueRespList);
    }

    @ZsmpApiOperation(name = "对外-通过缺陷code查询缺陷详情",
            description = "对外-通过缺陷code查询缺陷详情",
            gatewayApi = @GatewayApi(namespace = "luban",
                    name = "issueApi/findIssueByIssueCode",
                    description = "对外-通过缺陷code查询缺陷详情",
                    tags = "feature-V3.20.0",
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    auth = @GatewayAuth(type = GatewayAuthType.DIGEST, enable = true, value = Constant.GATEWAY_SIGN)
            ))
    @Override
    public Result<IssueResp> findIssueByIssueCode(FindIssueByIssueCodeReq req) {
        if (Objects.isNull(req.getIssueCode())) {
            throw new ServiceException("参数不能为空");
        }
        SimpleSingleIssueQuery query = new SimpleSingleIssueQuery();
        query.setCode(req.getIssueCode());
        IssueVO issueVO = issueQueryDomainService.findIssueByIssueCode(query);
        IssueResp issueResp = issueRespConverter.convert(issueVO);
        return StatusCode.OK.build(issueResp);
    }

    @ZsmpApiOperation(name = "对外-通过缺陷code查询所有缺陷流转信息",
            description = "对外-通过缺陷code查询所有缺陷流转信息",
            gatewayApi = @GatewayApi(namespace = "luban",
                    name = "issueApi/findIssueTransitionNodeByIssueCode",
                    description = "对外-通过缺陷code查询所有缺陷流转信息",
                    tags = "feature-V3.20.0",
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    auth = @GatewayAuth(type = GatewayAuthType.DIGEST, enable = true, value = Constant.GATEWAY_SIGN)
            ))
    @Override
    public Result<List<TransitionNodeResp>> findIssueTransitionNodeByIssueCode(FindIssueTransitionNodeByIssueCodeReq req) {
        if (Objects.isNull(req.getIssueCode())) {
            throw new ServiceException("参数不能为空");
        }
        ListTransitionNodesByBusinessCodeQuery query = new ListTransitionNodesByBusinessCodeQuery();
        query.setBusinessCode(req.getIssueCode());
        List<TransitionNodeVO> transitionNodeVOList = issueQueryDomainService.findIssueTransitionNodeByIssueCode(query);
        List<TransitionNodeResp> transitionNodeRespList = issueRespConverter.convertTransitionNodeResp(transitionNodeVOList);

        return StatusCode.OK.build(transitionNodeRespList);
    }

    @ZsmpApiOperation(name = "对外-通过缺陷code查询所有缺陷标签信息",
            description = "对外-通过缺陷code查询所有缺陷标签信息",
            gatewayApi = @GatewayApi(namespace = "luban",
                    name = "issueApi/findIssueTagByIssueCode",
                    description = "对外-通过缺陷code查询所有缺陷标签信息",
                    tags = "feature-V3.20.0",
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    auth = @GatewayAuth(type = GatewayAuthType.DIGEST, enable = true, value = Constant.GATEWAY_SIGN)
            ))
    @Override
    public Result<List<IssueTagResp>> findIssueTagByIssueCode(FindIssueTagByIssueCodeReq req) {
        if (Objects.isNull(req.getIssueCode())) {
            throw new ServiceException("参数不能为空");
        }
        ListTagsByBusinessCodeQuery query = new ListTagsByBusinessCodeQuery();
        query.setBusinessCode(req.getIssueCode());
        List<TagVO> tagVOList = tagQueryDomainService.listTagsByBusinessCodeQuery(query.getBusinessCode());
        List<IssueTagResp> issueTagRespList = issueRespConverter.convert(tagVOList);
        return StatusCode.OK.build(issueTagRespList);
    }

    @ZsmpApiOperation(name = "对外-获取产品维度缺陷统计数据",
            description = "对外-获取产品维度缺陷统计数据",
            gatewayApi = @GatewayApi(namespace = "luban",
                    name = "statisticsIssueApi/getStatisticsProductIssue",
                    description = "对外-获取产品维度缺陷统计数据",
                    tags = "feature-V3.20.0",
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    auth = @GatewayAuth(type = GatewayAuthType.DIGEST,enable = true,value = Constant.GATEWAY_SIGN)
            ))
    @Override
    public Result<StatisticsProductIssueResp> getStatisticsProductIssue(StatisticsProductIssueReq req) {
        if(Objects.isNull(req.getStartDate()) || Objects.isNull(req.getEndDate()) || StringUtil.isBlank(req.getProductCode())){
            throw new ServiceException("参数不能为空");
        }
        FindStatisticsIssueReq findStatisticsIssueReq = issueRespConverter.convert(req);
        StatisticsIssueResp resp = issueDomainService.getStatisticsIssue(findStatisticsIssueReq);
        return StatusCode.OK.build(issueRespConverter.convert(resp));
    }

    /*************************************** 对外接口 end***************************************************/

    @Override
    public List<HandlerWaitFixIssueCountVO> handlerWaitFixIssueCountByProductCodeListQuery(List<String> productCodeList) {
        return issueQueryDomainService.handlerWaitFixIssueCountByProductCodeListQuery(Arrays.asList(IssueStatus.WAIT_FIX.name(), IssueStatus.FIXING.name(), IssueStatus.DELAY_FIX.name()), productCodeList);
    }

    @Override
    public List<DevThingVO> listIssueByHandleUserId(Long handleUserId) {
        return issueQueryDomainService.listIssueByHandleUserId(handleUserId);
    }

    @Override
    public void updateIssueHandleUserId(List<DevThingUpdateVO> vos) {
        issueCommandDomainService.updateIssueHandleUserId(vos);
    }

    @Override
    public List<TagVO> listBusinessTagWithFixVersionCode(String versionCode) {
        return tagQueryDomainService.listBusinessTagWithFixVersionCode(versionCode);
    }

    @Override
    public IssueVO selectIssueByCode(FindIssueByCodeQuery query) {
        return issueQueryDomainService.findIssueByCode(query);
    }

    @Override
    public void closeIssueByCode(String issueCode,User user) {
        ConfirmCloseCommand command = new ConfirmCloseCommand(issueCode);
        GatewayContext.fillCurrentUser(command);
        command.setContent("已转需求，缺陷关闭");
        command.setTransactor(user);
        issueCommandDomainService.confirmCloseAndSelect(command);
    }

    @ZApiOperation(
            description = "测试管理-缺陷-测试发送消息（临时）",
            apiName = "qc/issue/issueMsgJob",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> issueMsgJob() {
        issueCommandDomainService.issueMsgJob();
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(name = "慢sql转缺陷", description = "慢sql转缺陷",
            gatewayApi = @GatewayApi(namespace = "luban",
                    name = "qc/issue/slowSqlToIssue",
                    description = "慢sql转缺陷",
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    auth = @GatewayAuth(type = GatewayAuthType.DIGEST,
                            enable = true,
                            value = com.zto.devops.project.client.service.version.constants.Constant.GATEWAY_SIGN)
            ))
    @Override
    public Result<SlowSqlToIssueResp> slowSqlToIssue(SlowSqlToIssueReq req) {
        ComplexResult cr = checkAll()
                .on(req.getTitle(), new NotBlankValidator("标题"))
                .on(req.getDescription(), new NotBlankValidator("描述"))
                .on(req.getTitle(), new LengthValidator(-1, 240, "标题"))
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getProductName(), new NotBlankValidator("产品名称"))
                .on(req.getTestUserName(), new NotBlankValidator("测试人员"))
                .on(req.getTestUserId(), new NotNullValidator("测试人员id"))
                .on(req.getLocation(), new NotBlankValidator("ip地址"))
                .on(req.getAppId(), new NotBlankValidator("应用id"))
                .on(req.getStartAt(), new NotNullValidator("执行时间"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(issueCommandDomainService.addIssueForSlowSql(fillCommand(req)));
    }

    private AddIssueCommand fillCommand(SlowSqlToIssueReq req) {
        AddIssueCommand command = new AddIssueCommand(aggregateIdGenerateService.generateId(AggregateType.ISSUE));
        addIssueCommandConverter.convert(req, command);

        if (StringUtil.isNotBlank(req.getTestUserName()) && Objects.nonNull(req.getTestUserId())) {
            command.setTester(new User(req.getTestUserId(), req.getTestUserName()));
        }
        if (StringUtil.isNotBlank(req.getFindUserName()) && Objects.nonNull(req.getFindUserId())) {
            command.setFinder(new User(req.getFindUserId(), req.getFindUserName()));
        }
        command.setVersionConfirm("before");
        command.setUploadDescFlag(StringUtil.isNotBlank(req.getDescription()) && req.getDescription().length() > ISSUE_DESC_MAX_LENGTH);
        return command;
    }


    @ZsmpApiOperation(name = "缺陷详情-token访问", description = "缺陷详情-token访问",
            gatewayApi = @GatewayApi(namespace = "luban",
                    name = "qc/issue/findIssueByCode",
                    description = "缺陷详情-token访问",
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    auth = @GatewayAuth(type = GatewayAuthType.DIGEST,
                            enable = true,
                            value = com.zto.devops.project.client.service.version.constants.Constant.GATEWAY_SIGN)
            ))
    @Override
    public Result<IssueResp> findIssueByCodeToken(FindIssueByCodeTokenReq req) {
        IssueVO issueVO = issueQueryDomainService.findIssueDetail(req.getCode());
        if (issueVO == null) {
            throw new ServiceException("没有对应的缺陷数据!");
        }
        return StatusCode.OK.build(issueRespConverter.convert(issueVO));
    }

    @Override
    public void planVersionIssue(VersionBaseEvent event) {
        issueCommandDomainService.planVersionIssue(event);
    }


    @ZApiOperation(
            description = "测试管理-缺陷-导出缺陷",
            apiName = "qc/issue/exportIssue",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> exportIssue(ExportIssueReq req) {
        ExpIssueQuery query = checkParameter(req);
        query.setCurrentUserId(GatewayContext.currentUser().getUser_id());
        issueQueryDomainService.exportIssueCount(query);
        AddExpCommand command = new AddExpCommand(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        command.setFileName("缺陷导出列表-" + DateUtil.dateToString(new Date(), DateUtil.DATE_ALL));
        command.setQueryCondition(asyncImpExpService.convertMap(query));
        command.setType(ExpProcessorEnum.EXP_PRODUCT_ISSUE.name());
        GatewayContext.fillCurrentUser(command);
        asyncImpExpService.addExpJob(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(
            description = "测试管理-缺陷-导出缺陷条数统计",
            apiName = "qc/issue/exportIssueCount",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Integer> exportIssueCount(ExportIssueReq req) {
        ExpIssueQuery query = checkParameter(req);
        query.setCurrentUserId(GatewayContext.currentUser().getUser_id());
        List<IssueVO> issueList = issueQueryDomainService.exportIssueCount(query);
        return StatusCode.OK.build(issueList.size());
    }

    private ExpIssueQuery checkParameter(ExportIssueReq req) {
        if (CollectionUtil.isNotEmpty(req.getTagName()) && StringUtil.isNotBlank(req.getTagName().get(0)) && req.getTagName().get(0).length() > 10) {
            throw new ServiceException("标签字数不能超过10个字！");
        }
        ExpIssueQuery query = pageIssueQueryConverter.convertExp(req);
        query.setPage(1);
        query.setSize(100000);
        return query;
    }

}
