package com.zto.devops.qc.application.job;

import com.zto.devops.qc.domain.service.InterfaceCoverageDomainService;
import com.zto.zss.common.exception.ZSSException;
import com.zto.zss.worker.processor.BasicProcessor;
import com.zto.zss.worker.processor.model.ProcessResult;
import com.zto.zss.worker.processor.model.TaskContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component("interfaceCoverageStatisticsJob")
public class InterfaceCoverageStatisticsJob implements BasicProcessor {

    @Autowired
    private InterfaceCoverageDomainService interfaceCoverageDomainService;

    @Override
    public ProcessResult process(TaskContext context) throws ZSSException {
        log.info("接口覆盖率获取昨日调用量、错误量任务");
        interfaceCoverageDomainService.getInterfaceCallsNumberFromZcat();
        return ProcessResult.success("OK");
    }

}
