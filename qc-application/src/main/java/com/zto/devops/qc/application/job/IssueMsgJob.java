package com.zto.devops.qc.application.job;

import com.alibaba.fastjson.JSON;
import com.zto.devops.qc.domain.service.IssueCommandDomainService;
import com.zto.zss.common.exception.ZSSException;
import com.zto.zss.worker.processor.BasicProcessor;
import com.zto.zss.worker.processor.model.ProcessResult;
import com.zto.zss.worker.processor.model.TaskContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 缺陷定时发送消息...(工作日早10 晚5)
 */
@Slf4j
@Component("issueMsgJob")
public class IssueMsgJob implements BasicProcessor {

    @Autowired
    private IssueCommandDomainService issueCommandDomainService;

    @Override
    public ProcessResult process(TaskContext taskContext) throws ZSSException {
        try {
            log.info("缺陷定时发送消息.参数：{}", JSON.toJSONString(taskContext));
            issueCommandDomainService.issueMsgJob();
        } catch (Exception e) {
            log.error("系统异常", e);
            return ProcessResult.error("系统异常了哦", JSON.toJSONString(taskContext));
        }
        return ProcessResult.success("OK");
    }


}
