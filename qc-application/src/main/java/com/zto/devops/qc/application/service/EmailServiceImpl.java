package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.qc.application.converter.EmailQueryConverter;
import com.zto.devops.qc.client.model.testmanager.email.entity.DetailEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.PageEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.VersionEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.query.DetailEmailQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.PageEmailQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.VersionEmailQuery;
import com.zto.devops.qc.client.service.testmanager.email.IEmailService;
import com.zto.devops.qc.client.service.testmanager.email.model.*;
import com.zto.devops.qc.domain.service.EmailQueryDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.titans.common.util.JsonUtil;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayWebAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * 邮件管理
 */
@Slf4j
@Service
@ZsmpService(name = "邮件接口目录")
public class EmailServiceImpl implements IEmailService {

    @Autowired
    private EmailQueryConverter emailQueryConverter;
    @Autowired
    private EmailQueryDomainService emailQueryDomainService;


    @ZApiOperation(description = "测试管理--邮件列表（分页）",
            apiName = "tm/email/pageEmail", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<PageEmailResp> pageEmail(PageEmailReq req) {
        log.info("PageEmailReq->{}", JsonUtil.toJSON(req));
        PageEmailQuery query = new PageEmailQuery();
        if (!Objects.isNull(req)) {
            query = emailQueryConverter.convert(req);
            GatewayContext.fillCurrentUser(query);
        }
        PageEmailVO pageEmailVO = emailQueryDomainService.pageEmail(query);
        List<PageEmailResp> list = emailQueryConverter.convert(pageEmailVO.getEmailVOS());
        return StatusCode.OK.build(list, req.getPage(), req.getSize(), pageEmailVO.getTotal());
    }

    @ZApiOperation(description = "测试管理-邮件-详情",
            apiName = "tm/email/detailEmail", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<DetailEmailResp> detailEmail(DetailEmailReq req) {
        log.info("DetailEmailReq->{}", JsonUtil.toJSON(req));
        DetailEmailQuery query = new DetailEmailQuery();
        if (!Objects.isNull(req)) {
            query = emailQueryConverter.convert(req);
            GatewayContext.fillCurrentUser(query);
        }
        DetailEmailVO detailEmail = emailQueryDomainService.detailEmail(query);
        DetailEmailResp resp = emailQueryConverter.convert(detailEmail);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "版本概览-测试邮件列表查询",
            apiName = "tm/email/getVersionEmail", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<EmailResp>> getVersionEmail(VersionEmailReq req) {
        VersionEmailQuery query = new VersionEmailQuery(req.getVersionCode());
        List<VersionEmailVO> emailVOList = emailQueryDomainService.getVersionEmail(query);
        List<EmailResp> emailRespList = emailQueryConverter.convertor(emailVOList);
        return StatusCode.OK.build(emailRespList);
    }
}
