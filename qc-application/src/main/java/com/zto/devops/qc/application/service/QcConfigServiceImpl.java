package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.qc.application.converter.QcConfigAdapterConverter;
import com.zto.devops.qc.client.model.testmanager.config.entity.QcConfigVO;
import com.zto.devops.qc.client.service.testmanager.config.QcConfigService;
import com.zto.devops.qc.client.service.testmanager.config.model.QcConfigReq;
import com.zto.devops.qc.client.service.testmanager.config.model.QcConfigResp;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayWebAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@Service
@ZsmpService(name = "qc配置接口目录", group = "qc配置")
public class QcConfigServiceImpl extends GatewayBase implements QcConfigService {

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    @Autowired
    private QcConfigAdapterConverter qcConfigConverter;

    @ZsmpApiOperation(description = "获取配置",
            gatewayApi = @GatewayApi(
                    tags = "V3.0.0",
                    name = "tm/testmanager/config",
                    description = "获取配置",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<QcConfigResp> getConfig(QcConfigReq req) {
        QcConfigResp resp = new QcConfigResp();
        if (req.getConfig() != null) {
            QcConfigVO configVO = qcConfigBasicService.getConfig(req.getConfig());
            resp = qcConfigConverter.converter(configVO);
        }
        return StatusCode.OK.build(resp);
    }

}
