package com.zto.devops.qc.application.converter;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.*;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestVariableVO;
import com.zto.devops.qc.client.service.testmanager.apitest.model.*;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExecuteApiCaseReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExecuteApiTestReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface ApiTestConvertor {

    @Mapping(target = "subVariableType", expression = "java(com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum.valueOf(req.getSubVariableType()))")
    AddApiTestVariableCommand convertor(AddApiTestVariableReq req, String aggregateId);

    @Mapping(target = "aggregateId", source = "req.variableCode")
    @Mapping(target = "subVariableType", expression = "java(com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum.valueOf(req.getSubVariableType()))")
    EditApiTestVariableCommand convertor(EditApiTestVariableReq req);

    List<PageApiTestVariableResp> convertor(List<ApiTestVariableVO> voList);

    @Mapping(target = "aggregateId", source = "req.variableCode")
    UpdateApiTestVariableStatusCommand convertor(UpdateApiTestVariableStatusReq req);

    GetApiTestVariableResultCommand convertor(GetApiTestVariableResultReq req);

    ExecuteApiTestCommand convertor(ExecuteApiTestReq req);

    ExecuteApiCallBackCommand convertor(ExecuteApiCallBackReq req);

    ExecuteApiCaseCommand convertor(ExecuteApiCaseReq req);

    @Mapping(target = "sceneFrontData", ignore = true)
    @Mapping(target = "sceneBackData", ignore = true)
    @Mapping(target = "tags", expression = "java((entityDO.getSceneTagData() != null && !entityDO.getSceneTagData().isEmpty())" +
            " ? java.util.Arrays.stream(entityDO.getSceneTagData().split(\",\"))" +
            ".map(String::trim).collect(java.util.stream.Collectors.toList())" +
            " : java.util.Collections.emptyList())")
    SceneInfoResp convertorSceneInfoVO(SceneInfoEntityDO entityDO);

    List<SceneInfoResp> convertorSceneInfoVOLsit(List<SceneInfoEntityDO> voList);

    @Mapping(target = "aggregateId", source = "req.sceneCode")
    CopySceneInfoCommand convertor(CopySceneInfoReq req);

    AddSceneModuleCommand convertor(AddSceneModuleReq req, String aggregateId);

    AddPreDataModuleCommand convertor(AddPreDataModuleReq req, String aggregateId);

    EditSceneModuleCommand convertor(EditSceneModuleReq req);

    EditPreDataModuleCommand convertor(EditPreDataModuleReq req);

    MoveSceneModuleCommand convertor(MoveSceneModuleReq req);

    MovePreDataModuleCommand convertor(MovePreDataModuleReq req);

    BatchDeleteSceneCommand convertor(BatchDeleteSceneReq req);

    BatchMoveSceneCommand convertor(BatchMoveSceneReq req);

    BatchAddVariableCommand convertor(BatchAddVariableReq req);

    UpdateSceneStepRecordCommand convertor(UpdateSceneStepRecordReq req, String aggregateId);

    BatchAddPreDataVariableCommand convertor(BatchAddPreDataVariableReq req);

    CheckUserPermisionCommand convertor(CheckUserPermisionReq req);

    DbAuthorizeCommand convertor(ApiTestAuthorizeReq req);

    ApiTestRevokeCommand convertor(ApiTestRevokeReq req);

    SaveDebugInfoCommand convertor(SaveDebugLinkReq req);

    AddApiGlobalConfigurationCommand convertor(AddApiGlobalConfigurationReq req);

    @Mapping(target = "caseReqData", expression = "java(mapJSONObjectToString(req.getCaseReqData()))")
    @Mapping(target = "caseType", expression = "java(com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTypeEnum.getCode(req.getCaseType()))")
    AddApiTestCaseCommand convertor(AddApiTestCaseReq req, String aggregateId);

    default String mapJSONObjectToString(JSONObject jsonObject) {
        if (null == jsonObject) {
            return null;
        }
        return JSONObject.toJSONString(jsonObject);
    }

    @Mapping(target = "caseReqData", expression = "java(mapJSONObjectToString(req.getCaseReqData()))")
    @Mapping(target = "caseType", expression = "java(com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTypeEnum.getCode(req.getCaseType()))")
    EditApiTestCaseCommand convertor(EditApiTestCaseReq req);

    PublishApiTestCaseCommand convertor(PublishApiTestCaseReq req);

    BatchPublishApiTestCaseCommand convertor(BatchPublishApiTestCaseReq req);

    @Mapping(target = "aggregateId", source = "caseCode")
    UpdateApiCaseExceptionCommand convert(UpdateApiCaseExceptionReq req);

    @Mapping(target = "aggregateId", source = "parentCaseCode")
    GenerateApiCaseExceptionCommand convert(GenerateApiCaseExceptionReq req);

    VerifyBatchOperationCommand convertor(VerifyBatchOperationReq req);

    ChangeApiCaseStatusCommand convertor(ChangeApiCaseStatusReq req);

    BatchChangeApiCaseStatusCommand convertor(BatchChangeApiCaseStatusReq req);

    JmxFileUploadCommand convertor(JmxFileUploadReq req);

    AddSceneTagCommand convertor(AddSceneTagReq req);

    CopyApiTestCaseCommand convertor(CopyApiTestCaseReq req);

}
