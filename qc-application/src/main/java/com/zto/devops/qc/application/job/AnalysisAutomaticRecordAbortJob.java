package com.zto.devops.qc.application.job;

import com.zto.devops.qc.domain.service.AutomaticSourceRecordCommandDomainService;
import com.zto.zss.common.exception.ZSSException;
import com.zto.zss.worker.processor.BasicProcessor;
import com.zto.zss.worker.processor.model.ProcessResult;
import com.zto.zss.worker.processor.model.TaskContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 登记库解析超时任务终止
 */
@Slf4j
@Component("analysisAutomaticRecordAbortJob")
public class AnalysisAutomaticRecordAbortJob implements BasicProcessor {

    @Autowired
    private AutomaticSourceRecordCommandDomainService automaticSourceRecordCommandDomainService;

    @Override
    public ProcessResult process(TaskContext taskContext) throws ZSSException {
        log.info("开始执行qc域解析库超时终止");
        automaticSourceRecordCommandDomainService.analysisAutomaticAbort();
        log.info("执行结束");
        // QC用例库解析超时停止
        return ProcessResult.success("OK");
    }
}
