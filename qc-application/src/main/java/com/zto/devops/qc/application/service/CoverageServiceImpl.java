package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.baidu.unbiz.fluentvalidator.ComplexResult;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.PageObjectResult;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.exception.FluentException;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.common.util.time.DateUtil;
import com.zto.devops.framework.common.validator.LengthValidator;
import com.zto.devops.framework.common.validator.NotBlankValidator;
import com.zto.devops.framework.common.validator.NotNullValidator;
import com.zto.devops.qc.client.enums.report.CodeCoverResult;
import com.zto.devops.qc.client.enums.rpc.FlowLaneTypeEnum;
import com.zto.devops.qc.client.enums.rpc.MemberTypeEnum;
import com.zto.devops.qc.client.enums.rpc.PublishStrategyEnum;
import com.zto.devops.qc.client.enums.rpc.VersionTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.GenerateTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.devops.qc.client.model.parameter.CoverageRecordEditParameter;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.rpc.pipeline.event.ReleasedQcEvent;
import com.zto.devops.qc.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.qc.client.model.rpc.product.query.ListProductMemberByPIdQuery;
import com.zto.devops.qc.client.model.rpc.project.SimpleListVersionVO;
import com.zto.devops.qc.client.model.rpc.project.SimpleVersionListQuery;
import com.zto.devops.qc.client.model.rpc.project.VersionInfoVO;
import com.zto.devops.qc.client.model.rpc.project.VersionVO;
import com.zto.devops.qc.client.model.testmanager.coverage.command.*;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.*;
import com.zto.devops.qc.client.model.testmanager.coverage.query.*;
import com.zto.devops.qc.client.service.coverage.CoverageService;
import com.zto.devops.qc.client.service.coverage.convertor.CoverageAdapterConverter;
import com.zto.devops.qc.client.service.coverage.convertor.CoverageRecordQueryConverter;
import com.zto.devops.qc.client.service.coverage.model.req.*;
import com.zto.devops.qc.client.service.coverage.model.resp.*;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.domain.service.CoverageDomainService;
import com.zto.devops.qc.domain.service.CoverageQueryDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.titans.common.util.JsonUtil;
import com.zto.zsmp.annotation.ZsmpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@ZsmpService(name = "代码覆盖率接口目录", group = "测试管理/代码覆盖率")
public class CoverageServiceImpl extends GatewayBase implements CoverageService {

    @Autowired
    private CoverageAdapterConverter coverageConverter;
    @Autowired
    private CoverageRecordQueryConverter coverageRecordQueryConverter;
    @Autowired
    private ZtoOssService ztoOssService;
    @Autowired
    private CoverageDomainService coverageDomainService;
    @Autowired
    private CoverageQueryDomainService coverageQueryDomainService;
    @Autowired
    private RedisService redisTemplate;
    @Autowired
    private IProductRpcService productRpcService;
    @Autowired
    private IProjectRpcService projectRpcService;

    @ZApiOperation(description = "查询覆盖率报告列表", apiName = "tm/coverage/getCoverageReportListPage", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageObjectResult<CoverageReportPageResp> getCoverageReportListPage(CoverageRecordPageReq req) {
        ComplexResult cr = checkAll().on(req.getVersionCode(), new NotBlankValidator(
                "版本编号versionCode不能为空！")).doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        CoverageRecordPageQuery query = new CoverageRecordPageQuery();
        if (null != req) {
            query = coverageRecordQueryConverter.convert(req);
        }
        PageCoverageRecordVO pageCoverageRecordVO = coverageQueryDomainService.getCoverageReportListPage(query);
        CoverageReportPageResp resp = new CoverageReportPageResp();
        resp.setCoverageRecordVOList(pageCoverageRecordVO.getList());
        if (CollectionUtil.isNotEmpty(pageCoverageRecordVO.getList())) {
            CoverageVersionRateQuery rateQuery = new CoverageVersionRateQuery(query.getVersionCode());
            CoverageVersionRateVO rateVO = coverageQueryDomainService.getCoverageVersionRate(rateQuery);
            resp.setBranchVersionRate(rateVO.getBranchVersionRate());
            resp.setDisplayButton(coverageQueryDomainService.isButtonDisplay(req.getVersionCode()));
        }
        return StatusCode.OK.build(resp, req.getPage(), req.getSize(),
                pageCoverageRecordVO.getTotal());
    }

    @ZApiOperation(description = "前置校验生成条件",
            timeout = 20000,
            apiName = "tm/coverage/verifyGenerateCondition",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<VerifyGenerateConditionResp> verifyGenerateCondition(VerifyGenerateConditionReq req) {

        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号productCode"))
                .on(req.getVersionCode(), new NotBlankValidator("版本编号versionCode"))
                .on(req.getVersionName(), new NotBlankValidator("版本名称versionName"))
                .on(req.getRecordType(), new NotNullValidator("报告类型recordType"))
                .on(req.getAppIdList(), new NotNullValidator("应用编号appIdList"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        VerifyCoverageConditionCommand command = coverageConverter.convertor(req);
        GatewayContext.fillCurrentUser(command);
        //是否生成中
        checkCoverageGenerateStatus(req.getVersionCode(), req.getRecordType(), req.getAppIdList());
        //校验版本状态
        VersionInfoVO versionInfoVO = coverageDomainService.checkVersionInfo(req.getVersionCode());
        VerifyGenerateConditionResp resp = coverageDomainService.verifyGenerateCondition(command.buildParameter(), versionInfoVO);
        //删除缓存
        coverageDomainService.deleteVerifyKey(req.getVersionCode(), req.getRecordType(), req.getAppIdList());
        return StatusCode.OK.build(resp);
    }

    /**
     * 当前版本正在生成中，不进行校验
     */
    private void checkCoverageGenerateStatus(String versionCode, RecordTypeEnum recordType, List<String> appIdList) {
        if (StringUtil.isBlank(versionCode) || null == recordType || CollectionUtil.isEmpty(appIdList)) {
            return;
        }
        for (String appId : appIdList) {
            if (!redisTemplate.hasKey("verify_" + versionCode + appId)) {
                redisTemplate.setKey("verify_" + versionCode + appId, appId, 10L, TimeUnit.SECONDS);
            } else {
                throw new ServiceException("覆盖率数据正在校验中，请勿频繁操作！");
            }
            if (redisTemplate.hasKey(versionCode + appId + recordType)) {
                throw new ServiceException("覆盖率数据正在生成中，请两分钟后重试！");
            }
        }
    }

    @ZApiOperation(description = "批量代码覆盖率报告生成接口", apiName = "tm/coverage/batchCoverage", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchCoverageReport(BatchCoverageReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号productCode"))
                .on(req.getVersionCode(), new NotBlankValidator("版本编号versionCode"))
                .on(req.getVersionName(), new NotBlankValidator("版本名称versionName"))
                .on(req.getRecordType(), new NotNullValidator("报告类型recordType"))
                .on(req.getAllAppIdList(), new NotNullValidator("应用编号appIdList"))
                .on(req.getDataList(), new NotNullValidator("校验结果dataList"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        coverageDomainService.checkVersionInfo(req.getVersionCode());
        String flowLaneType = checkFlowLaneType(req.getVersionCode(), req.getRecordType());
        for (String appId : req.getAllAppIdList()) {
            checkRepeatData(req.getVersionCode() + appId + req.getRecordType(),
                    req.getVersionCode() + appId + req.getRecordType());
        }
        BatchCoverageCommand command = coverageConverter.convertor(req);
        GatewayContext.fillCurrentUser(command);
        checkUserPermission(command.getTransactor(), command.getProductCode());
        CoverageRecordGenerateParameter parameter = coverageConverter.converter(req);
        parameter.setGenerateType(GenerateTypeEnum.MANUAL);
        parameter.setCreator(command.getTransactor().getUserName());
        parameter.setCreatorId(command.getTransactor().getUserId());
        parameter.setModifier(command.getTransactor().getUserName());
        parameter.setModifierId(command.getTransactor().getUserId());
        parameter.setTaskId(req.getVersionCode() + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss"));
        parameter.setFlowLaneType(flowLaneType);
        coverageDomainService.generateCoverageReportByValidData(parameter, req.getDataList());
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "获取预签名地址-上传", apiName = "tm/public/generatePreSignedUrl", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<String> generatePreSignedUrl(OssBasicReq req) {
        if (StringUtil.isEmpty(req.getBucketName())) {
            throw new ServiceException("存储桶名称bucketName不能为空！");
        }
        if (StringUtil.isEmpty(req.getFileName())) {
            throw new ServiceException("文件fileName不能为空！");
        }
        String result = ztoOssService.generatePreSignedUrl(req.getBucketName(), req.getFileName());
        return StatusCode.OK.build(result);
    }

    @Override
    @ZApiOperation(description = "查代码覆盖率结果 & 不达标应用列表", apiName = "tm/coverage/queryCodeCoverResult", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    public Result<TmCodeCoverResultResp> queryCodeCoverResult(TmCodeCoverResultReq req) {
        ComplexResult cr = checkAll()
                .on(req.getVersionCode(), new NotBlankValidator("版本编码"))
                .on(req.getReportType(), new NotNullValidator("报告类型不能为空"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        //手动发布，默认达标
        VersionVO versionVO = projectRpcService.findVersionQuery(req.getVersionCode());
        if (versionVO != null && versionVO.getPublishStrategy().equals(PublishStrategyEnum.MANUAL)) {
            TmCodeCoverResultResp resp = new TmCodeCoverResultResp();
            resp.setCodeCoverResult(CodeCoverResult.STANDARD);
            return StatusCode.OK.build(resp);
        }
        //非白名单应用列表
        List<String> appIdList = coverageDomainService.getNotWhiteListByProductCode(versionVO.getProductCode());
        if (CollectionUtil.isEmpty(appIdList)) {
            TmCodeCoverResultResp resp = new TmCodeCoverResultResp();
            resp.setCodeCoverResult(CodeCoverResult.STANDARD);
            return StatusCode.OK.build(resp);
        }
        CoverageResultQuery resultQuery = coverageConverter.convertResultQuery(req);
        resultQuery.setAppIdList(appIdList);
        CoverageResultVO vo = coverageQueryDomainService.getCoverageResult(resultQuery);
        TmCodeCoverResultResp resp = null;
        if (!Objects.isNull(vo)) {
            resp = coverageConverter.convertCodeCoverResult(vo);
        }
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "查代码覆盖率结果 & 不达标应用列表", apiName = "tm/coverage/queryCodeCoverResultList", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<TmCodeCoverResultResp> queryCodeCoverResultList(TmCodeCoverResultListReq req) {
        SimpleVersionListQuery query = new SimpleVersionListQuery();
        query.setCode(req.getVersionCodeList());
        SimpleListVersionVO listVersionVO = projectRpcService.simpleVersionListQuery(query);
        if (listVersionVO == null || CollectionUtil.isEmpty(listVersionVO.getSimpleVersionVOList())) {
            throw new ServiceException("版本信息不能为空");
        }
        String planCode = "";
        if (CollectionUtil.isNotEmpty(req.getVersionCodeList())) {
            planCode = coverageQueryDomainService.selectTestPlanByVersion(req.getVersionCodeList().get(0));
        }
        //是否确认过
        if (coverageQueryDomainService.checkCompleted(req.getVersionCodeList())) {
            TmCodeCoverResultResp resp = new TmCodeCoverResultResp();
            resp.setCodeCoverResult(CodeCoverResult.STANDARD);
            return StatusCode.OK.build(resp);
        }

        List<VersionVO> autoList = new ArrayList<>();
        listVersionVO.getSimpleVersionVOList().forEach(t -> {
            VersionVO versionVO = projectRpcService.findVersionQuery(t.getCode());
            //手动发布，默认达标
            if (versionVO != null && (versionVO.getPublishStrategy().equals(PublishStrategyEnum.MANUAL) || VersionTypeEnum.URGENT_TYPE.name().equals(versionVO.getType()))) {
                return;
            }
            autoList.add(versionVO);
        });
        if (CollectionUtil.isEmpty(autoList)) {
            TmCodeCoverResultResp resp = new TmCodeCoverResultResp();
            resp.setCodeCoverResult(CodeCoverResult.STANDARD);
            return StatusCode.OK.build(resp);
        }
        //非白名单应用列表
        Set<String> productCodeList = autoList.stream().map(VersionVO::getProductCode).collect(Collectors.toSet());
        List<String> appIdList = new ArrayList<>();
        productCodeList.forEach(t -> {
            appIdList.addAll(coverageDomainService.getNotWhiteListByProductCode(t));
        });
        List<String> appIds = appIdList.stream().distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(appIds)) {
            TmCodeCoverResultResp resp = new TmCodeCoverResultResp();
            resp.setCodeCoverResult(CodeCoverResult.STANDARD);
            return StatusCode.OK.build(resp);
        }
        CoverageResultListQuery resultQuery = coverageConverter.convertResultListQuery(req);
        resultQuery.setAppIdList(appIds);
        GatewayContext.fillCurrentUser(resultQuery);
        CoverageResultVO vo = coverageQueryDomainService.getCoverageResultList(resultQuery);
        TmCodeCoverResultResp resp = null;
        if (!Objects.isNull(vo)) {
            resp = coverageConverter.convertCodeCoverResult(vo);
            resp.setCoverageRelatedTestPlanCode(planCode);
        }
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "添加代码覆盖率不达标原因", apiName = "tm/coverage/addCodeCoverResultReason", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addCodeCoverResultReason(AddCodeCoverResultReasonReq req) {
        if (CollectionUtil.isEmpty(req.getCoverageReasonVOS())) {
            return StatusCode.OK.build();
        }
        EditCoverageReasonCommand command = new EditCoverageReasonCommand(req.getProductCode());
        command.setCoverageReasonVOS(req.getCoverageReasonVOS());
        GatewayContext.fillCurrentUser(command);
        checkUserPermission(command.getTransactor(), req.getProductCode());
        checkCoverageReason(command.getCoverageReasonVOS());
        coverageDomainService.updateCoverageReason(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "删除oss对象", apiName = "tm/public/cleanObject", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Boolean> cleanObject(OssBasicReq req) {
        if (StringUtil.isEmpty(req.getBucketName())) {
            throw new ServiceException("存储桶名称bucketName不能为空！");
        }
        if (StringUtil.isEmpty(req.getFileName())) {
            throw new ServiceException("文件名fileName不能为空！");
        }
        Boolean result = ztoOssService.cleanObject(req.getBucketName(), req.getFileName());
        return StatusCode.OK.build(result);
    }

    @ZApiOperation(description = "代码覆盖率修改备注", apiName = "tm/coverage/editCoverage", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editCoverage(EditCoverageReq req) {
        ComplexResult cr = checkAll()
                .on(req.getVersionCode(), new NotBlankValidator("版本code"))
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getDiffType(), new NotNullValidator("差异类型"))
                .on(req.getComment(), new LengthValidator(-1, 2000, "备注"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        EditCoverageCommand command = coverageConverter.convert(req);
        GatewayContext.fillCurrentUser(command);
        checkUserPermission(command.getTransactor(), command.getProductCode());
        CoverageRecordEditParameter parameter = coverageConverter.converter(req);
        parameter.setModifier(command.getTransactor().getUserName());
        parameter.setModifierId(command.getTransactor().getUserId());
        coverageDomainService.editCoverage(parameter);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "任务查询接口", apiName = "tm/coverage/getCoverageTask", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<CoverageTaskResp> getCoverageTask(CoverageTaskReq req) {
        if (StringUtil.isEmpty(req.getProductCode())) {
            throw new ServiceException("产品code不能为空！");
        }
        CoverageTaskQuery query = coverageConverter.converter(req);
        if (StringUtil.isNotEmpty(req.getTaskId())) {
            query.setTaskIdList(Arrays.asList(req.getTaskId()));
        }
        PageCoverageTaskVO pageCoverageTaskVO = coverageQueryDomainService.getPageCoverageTask(query);
        List<CoverageTaskResp> list = coverageConverter
                .convertList(pageCoverageTaskVO.getList());
        return StatusCode.OK.build(list, req.getPage(), req.getSize(),
                pageCoverageTaskVO.getTotal());
    }

    @ZApiOperation(description = "触发生成exec文件", apiName = "tm/coverage/generateCoverageExec", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> generateCoverageExec(ReleasedQcEvent event) {
        log.info("generateCoverageExec request : {}", JsonUtil.toJSON(event));
        if (CollectionUtil.isEmpty(event.getVersionCodes())) {
            throw new ServiceException("版本编号versionCodes不能为空");
        }
        if (StringUtil.isEmpty(event.getBranchName())) {
            throw new ServiceException("分支名称branchName不能为空");
        }
        if (null == event.getTransactor()) {
            throw new ServiceException("创建人信息transactor不能为空");
        }
        if (CollectionUtil.isEmpty(event.getAppIds())) {
            throw new ServiceException("关联应用集appIds不能为空");
        }
        coverageDomainService.generateCoverageExec(event);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "获取覆盖率报告地址", apiName = "tm/coverage/getReportUrl", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<String> getReportUrl(OssBasicReq req) {
        if (StringUtil.isEmpty(req.getBucketName())) {
            throw new ServiceException("存储桶名称bucketName不能为空！");
        }
        if (StringUtil.isEmpty(req.getFileName())) {
            throw new ServiceException("文件fileName不能为空！");
        }
        return StatusCode.OK.build(coverageQueryDomainService.getReportUrl(req));
    }

    @ZApiOperation(description = "修改不达标原因", apiName = "tm/coverage/editReason", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editReason(CoverageReasonEditReq req) {
        ComplexResult cr = checkAll()
                .on(req.getVersionCode(), new NotBlankValidator("版本code"))
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getAppId(), new NotBlankValidator("应用id"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        CoverageNotStandardReasonEditCommand command = coverageConverter.convert(req);
        GatewayContext.fillCurrentUser(command);
        checkUserPermission(command.getTransactor(), req.getProductCode());
        coverageDomainService.editReason(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "查询不达标原因下拉框", apiName = "tm/coverage/queryReasonList", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<QueryReasonListResp> queryReasonList(QueryReasonListReq req) {
        ComplexResult cr = checkAll()
                .on(req.getVersionCode(), new NotBlankValidator("版本code"))
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getAppId(), new NotBlankValidator("应用id"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        checkUserPermission(GatewayContext.currentUser().toSimpleUser(), req.getProductCode());
        return StatusCode.OK.build(coverageQueryDomainService.queryReasonList(req));
    }

    @ZApiOperation(description = "查询最近一次覆盖率报告", apiName = "tm/coverage/getLatestCoverageReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<CoverageRecordResp> getLatestCoverageReport(CoverageRecordReq req) {
        ComplexResult cr = checkAll()
                .on(req.getVersionCode(), new NotBlankValidator("versionCode"))
                .on(req.getAppId(), new NotBlankValidator("appId"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(coverageQueryDomainService.getLatestCoverageReport(req));
    }

    @ZApiOperation(description = "查询覆盖率报告类增量数据", apiName = "tm/coverage/getCoverageReportClassInfo", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<CoverageReportClassInfoResp> getCoverageReportClassInfo(CoverageReportClassInfoReq req) {
        ComplexResult cr = checkAll()
                .on(req.getVersionCode(), new NotBlankValidator("versionCode"))
                .on(req.getAppId(), new NotBlankValidator("appId"))
                .on(req.getCommitId(), new NotBlankValidator("commitId"))
                .on(req.getPackages(), new NotBlankValidator("packages"))
                .on(req.getClassName(), new NotBlankValidator("className"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(coverageQueryDomainService.getCoverageReportClassInfo(req));
    }

    private void checkRepeatData(String key, String value) {
        if (!redisTemplate.hasKey(key)) {
            redisTemplate.setKey(key, value, 120L, TimeUnit.SECONDS);
        } else {
            throw new ServiceException("请两分钟后重试！");
        }
    }

    /**
     * 校验当前人操作权限
     */
    private void checkUserPermission(User user, String productCode) {
        if (null == user) {
            throw new ServiceException("当前人无操作权限！");
        }
        // 超管校验
        if (checkSuperUser(user, productCode)) {
            return;
        }
        List<ProductMemberVO> list = productRpcService.findProductMemberByIdQuery(productCode, null);
        if (CollectionUtil.isEmpty(list)) {
            throw new ServiceException("当前人无操作权限！");
        }
        list = list.stream().filter(t ->
                t.getUserId().equals(user.getUserId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            throw new ServiceException("当前人无操作权限！");
        }
    }

    /**
     * 超管权限校验
     */
    private Boolean checkSuperUser(User user, String productCode) {
        ListProductMemberByPIdQuery productQuery = new ListProductMemberByPIdQuery();
        Long userId = user.getUserId();
        List<String> memberTypes = Arrays.asList(MemberTypeEnum.SUPPER.name());
        productQuery.setProductCode(productCode);
        productQuery.setMemberTypes(memberTypes);
        return productRpcService.checkProductPermission(userId, productQuery);
    }

    private String checkFlowLaneType(String versionCode, RecordTypeEnum recordType) {
//        String flowLaneType = coverageDomainService.getFlowLaneType(versionCode);
//        if (flowLaneType.equals(FlowLaneTypeEnum.FLOW_FEATURE.name()) && !recordType.equals(RecordTypeEnum.FEATURE)) {
//            throw new ServiceException("[功能开发泳道]不能生成分支或主干覆盖率");
//        }
//        if (flowLaneType.equals(FlowLaneTypeEnum.FLOW_TEST.name()) && !recordType.equals(RecordTypeEnum.BRANCH)) {
//            throw new ServiceException("[功能测试泳道]不能生成开发或主干覆盖率");
//        }
//        if (flowLaneType.equals(FlowLaneTypeEnum.FLOW_PROD.name()) && !recordType.equals(RecordTypeEnum.MASTER)) {
//            throw new ServiceException("[回归/发布泳道]不能生成开发或分支覆盖率");
//        }
        if (recordType.equals(RecordTypeEnum.FEATURE)) {
            return FlowLaneTypeEnum.FLOW_FEATURE.name();
        } else if (recordType.equals(RecordTypeEnum.BRANCH)) {
            return FlowLaneTypeEnum.FLOW_TEST.name();
        } else {
            return FlowLaneTypeEnum.FLOW_PROD.name();
        }
    }

    /**
     * 校验代码覆盖率不达标原因
     *
     * @param coverageReasonVOS {@link CoverageReasonVO}
     * @return
     */
    private void checkCoverageReason(List<CoverageReasonVO> coverageReasonVOS) {
        if (CollectionUtil.isEmpty(coverageReasonVOS)) {
            throw new ServiceException("请补全代码覆盖率不达标原因！");
        }
        coverageReasonVOS.forEach(vo -> {
            if (StringUtil.isBlank(vo.getFinalReason())) {
                throw new ServiceException("请补全应用【" + vo.getAppId() + "】代码覆盖率不达标原因！");
            }
            if (StringUtil.isNotBlank(vo.getCustomReason()) && vo.getCustomReason().length() > 500) {
                throw new ServiceException("自定义不达标原因，最多支持500个字符！");
            }
            if (vo.getFinalReason().length() > 999) {
                throw new ServiceException("不达标原因过长无法保存，请联系管理员！");
            }
        });
    }

    @Override
    public List<CoverageVersionRateVO> getBranchVersionRateByVersionCodes(List<String> versionCodes) {
        return coverageQueryDomainService.getVersionRecordRateList(versionCodes);
    }
}
