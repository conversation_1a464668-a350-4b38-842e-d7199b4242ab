package com.zto.devops.qc.application.comsumer;

import com.zto.consumer.MsgConsumedStatus;
import com.zto.devops.framework.domain.gateway.ConsumeEventService;
import com.zto.titans.mq.annotation.ZMSListener;
import com.zto.titans.mq.annotation.ZMSListenerParameter;
import com.zto.titans.mq.enums.MQMsgEnum;
import com.zto.titans.mq.enums.ZMSResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/3/22
 * @Version 1.0
 */
@Slf4j
@Component
public class EventConsumer {

    @Autowired
    private ConsumeEventService consumeEventService;


    @ZMSListener(consumerGroup = "devops_event_qc_consumer")
    public ZMSResult consumer(@ZMSListenerParameter(name = MQMsgEnum.BODY) String body,
                              @ZMSListenerParameter(name = MQMsgEnum.TAG) String tag) {
        log.debug("MQ消息消费：{}", body);
        try {
            consumeEventService.handle(body, tag);
        } catch (Exception e) {
            log.error("MQ消息消费处理异常: ", e);
        }
        return ZMSResult.status(MsgConsumedStatus.SUCCEED);
    }
}
