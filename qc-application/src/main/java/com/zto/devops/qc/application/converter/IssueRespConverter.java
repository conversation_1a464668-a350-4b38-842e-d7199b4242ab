package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.issue.entity.TransitionNodeVO;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.devops.qc.client.model.report.entity.IssueNumStatisticsVO;
import com.zto.devops.qc.client.service.issue.model.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring")
public interface IssueRespConverter {

    @Mapping(target = "reopenTime", source = "reopenTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "findTime", source = "findTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "startFixTime", source = "startFixTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "delayFixTime", source = "delayFixTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "deliverTime", source = "deliverTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "rejectTime", source = "rejectTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "closeTime", source = "closeTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "updateTime", source = "updateTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "gmtCreate", source = "gmtCreate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "fixVersionCode",expression = "java(com.zto.devops.qc.client.enums.constants.DefaultValueEnum.VERSION_VALUE.isDefaultValue(issueVO.getFixVersionCode())?null:issueVO.getFixVersionCode())")
    @Mapping(target = "fixVersionName",expression = "java(com.zto.devops.qc.client.enums.constants.DefaultValueEnum.BLANKSTRING.isDefaultValue(issueVO.getFixVersionName())?null:issueVO.getFixVersionName())")
    IssueResp convert(IssueVO issueVO);

    List<IssueResp> convert(List<IssueVO> issueVOs);

    List<IssueLegacyListResp> convertLegacyList(List<IssueLegacyVO> issueLegacyVOList);

    IssueNumStatisticsResp convertStatistic(IssueNumStatisticsVO issueNumStatisticsVO);

    List<IssueTagResp> convert(Collection<TagVO> vos);

    List<IssueResp> convertIssueResp(Collection<IssueVO> vos);

    List<TransitionNodeResp> convertTransitionNodeResp(Collection<TransitionNodeVO> vos);

    FindStatisticsIssueReq convert(StatisticsProductIssueReq req);

    StatisticsProductIssueResp convert(StatisticsIssueResp resp);
}
