package com.zto.devops.qc.application.converter.issue;

import com.zto.devops.qc.client.model.issue.command.AddIssueCommand;
import com.zto.devops.qc.client.service.issue.model.AddIssueReq;
import com.zto.devops.qc.client.service.issue.model.SlowSqlToIssueReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface AddIssueCommandConverter {

//    @AfterMapping
//    default void convertAfter(AddIssueReq req, @MappingTarget AddIssueCommand command) {
//        command.setAggregateId(AggregateIdUtil.generateId(AggregateType.ISSUE));
//        command.setStatus(IssueStatus.DELAY_FIX);
//        if (CollectionUtil.isNotEmpty(req.getAttachments())) {
//            for (AttachmentVO attachmentVO : req.getAttachments()) {
//                attachmentVO.setBusinessCode(command.getAggregateId());
//                attachmentVO.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
//                attachmentVO.setDomain(DomainEnum.ISSUE);
//                attachmentVO.setType(AttachmentTypeEnum.FILE);
//            }
//        }
//        if (CollectionUtil.isNotEmpty(req.getTags())) {
//            for (TagVO tagVO : req.getTags()) {
//                tagVO.setBusinessCode(command.getAggregateId());
//                tagVO.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
//                tagVO.setDomain(DomainEnum.ISSUE);
//                tagVO.setType(TagTypeEnum.BUSINESS);
//            }
//        }
//    }

    @Mapping(target = "handler.userId", source = "developUserId")
    @Mapping(target = "handler.userName", source = "developUserName")
    @Mapping(target = "developer.userId", source = "developUserId")
    @Mapping(target = "developer.userName", source = "developUserName")
    @Mapping(target = "product.code", source = "productCode")
    @Mapping(target = "product.name", source = "productName")
    @Mapping(target = "requirement.code", source = "requirementCode")
    @Mapping(target = "requirement.name", source = "requirementName")
    @Mapping(target = "findVersion.code", source = "findVersionCode")
    @Mapping(target = "findVersion.name", source = "findVersionName")
    @Mapping(target = "sprint.name", source = "sprintName")
    @Mapping(target = "sprint.code", source = "sprintCode")
    void convert(AddIssueReq addIssueReq, @MappingTarget AddIssueCommand command);

    @Mapping(target = "product.code", source = "productCode")
    @Mapping(target = "product.name", source = "productName")
    void convert(SlowSqlToIssueReq req, @MappingTarget AddIssueCommand command);

}
