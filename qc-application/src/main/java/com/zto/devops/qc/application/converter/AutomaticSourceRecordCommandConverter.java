package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.testmanager.cases.command.AddAutomaticRecordCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.AddAutomaticRecordLogCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.EditAutomaticPersonLiableCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.EditAutomaticRecordCommand;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordVO;
import com.zto.devops.qc.client.service.testmanager.cases.model.AddAutomaticRecordReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.AutomaticRecordResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.EditAutomaticPersonLiableReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.EditAutomaticRecordReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface AutomaticSourceRecordCommandConverter {

    AddAutomaticRecordCommand convert(AddAutomaticRecordReq req, String aggregateId);

    @Mapping(target = "aggregateId", source = "code")
    EditAutomaticRecordCommand convert(EditAutomaticRecordReq req);

    AddAutomaticRecordLogCommand convert(EditAutomaticRecordCommand command);

    EditAutomaticRecordCommand convert(AutomaticRecordVO vo);

    @Mapping(target = "aggregateId", source = "code")
    EditAutomaticPersonLiableCommand convertor(EditAutomaticPersonLiableReq req);

    AutomaticRecordResp convertor(AutomaticRecordVO vo);
}
