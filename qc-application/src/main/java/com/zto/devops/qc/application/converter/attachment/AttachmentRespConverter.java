package com.zto.devops.qc.application.converter.attachment;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.service.attachment.model.AttachmentResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AttachmentRespConverter {

    AttachmentRespConverter INSTANCE = Mappers.getMapper(AttachmentRespConverter.class);

    @Mapping(target = "gmtCreate", source = "gmtCreate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "gmtModified", source = "gmtModified", dateFormat = "yyyy-MM-dd HH:mm:ss")
    AttachmentResp convert(AttachmentVO attachmentVO);

    List<AttachmentResp> convert(List<AttachmentVO> attachmentVOList);
}
