package com.zto.devops.qc.application.comsumer;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.zto.consumer.KafkaMessageListener;
import com.zto.consumer.MsgConsumedStatus;
import com.zto.devops.framework.domain.gateway.mq.IZMSEnvironmentClientService;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.MetaDataDocMsgVO;
import com.zto.devops.qc.domain.service.ApiTestDocDataService;
import com.zto.titans.mq.annotation.ZMSListener;
import com.zto.titans.mq.annotation.ZMSListenerParameter;
import com.zto.titans.mq.enums.MQMsgEnum;
import com.zto.titans.mq.enums.ZMSResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Properties;
import java.util.concurrent.Executor;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class DocDataConsumer implements KafkaMessageListener {

    @Autowired
    private ApiTestDocDataService apiTestDocDataService;
    @Autowired
    private IZMSEnvironmentClientService ZMSEnvironmentClientService;

    private final Executor CONSUMER_EXECUTOR = new ThreadPoolExecutor(
            50,
            200,
            5L,
            TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            ThreadFactoryBuilder.create().setNamePrefix("Doc-Data-Devops-Consumer-").build());

//    @ZMSListener(consumerGroup = "doc_data_devops_consumer", maxBatchRecords = "10")
    public ZMSResult consumer(@ZMSListenerParameter(name = MQMsgEnum.BODY) String body) {
        log.info("consumer [doc_data_devops_consumer] body [{}]", body);
        try {
            MetaDataDocMsgVO metaDataDocMsg = JSON.parseObject(body, MetaDataDocMsgVO.class);
            CONSUMER_EXECUTOR.execute(() -> apiTestDocDataService.updateApiMetaData(metaDataDocMsg));
        } catch (Exception e) {
            log.error("doc_data_devops_consumer error", e);
        }
        return ZMSResult.status(MsgConsumedStatus.SUCCEED);
    }

    public MsgConsumedStatus onMessage(ConsumerRecord<String, byte[]> msg) {
        log.info("对外mq消费组doc_data_devops_consumer消费,body:{}", new String(msg.value()));
        if (this.isEasy()) {
            throw RUNTIME_EXCEPTION;
        }
        ZMSResult ZMSResult = consumer(new String(msg.value()));
        return ZMSResult.getConsumedStatus();
    }

    @PostConstruct
    public void subscribe() {
        // 注册消费组
        Properties properties = new Properties();
        properties.setProperty("maxBatchRecords", "10");
        ZMSEnvironmentClientService.subscribeToOutside("doc_data_devops_consumer", null, properties, this);
    }
}
