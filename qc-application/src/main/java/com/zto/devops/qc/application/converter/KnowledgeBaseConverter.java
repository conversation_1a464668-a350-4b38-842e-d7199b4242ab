package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.knowledgebase.command.RemoveKnowledgeBaseCommand;
import com.zto.devops.qc.client.model.knowledgebase.command.UpdateKnowledgeBaseCommand;
import com.zto.devops.qc.client.model.knowledgebase.query.KnowledgeBaseVO;
import com.zto.devops.qc.client.service.knowledgebase.model.KnowledgeBaseByProductCodeResp;
import com.zto.devops.qc.client.service.knowledgebase.model.RemoveKnowledgeBaseByProductCodeReq;
import com.zto.devops.qc.client.service.knowledgebase.model.AddKnowledgeBaseByProductCodeReq;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface KnowledgeBaseConverter {

    KnowledgeBaseByProductCodeResp converter(KnowledgeBaseVO vo);

    UpdateKnowledgeBaseCommand converter(AddKnowledgeBaseByProductCodeReq req);

    RemoveKnowledgeBaseCommand converter(RemoveKnowledgeBaseByProductCodeReq req);
}
