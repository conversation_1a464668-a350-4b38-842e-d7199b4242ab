package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.baidu.unbiz.fluentvalidator.ComplexResult;
import com.baidu.unbiz.fluentvalidator.FluentValidator;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.FluentException;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.validator.LengthValidator;
import com.zto.devops.framework.common.validator.ListSizeValidator;
import com.zto.devops.framework.common.validator.NotBlankValidator;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.*;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.*;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.*;
import com.zto.devops.qc.client.service.testmanager.cases.model.*;
import com.zto.devops.qc.client.service.testmanager.task.AutomaticSchedulerService;
import com.zto.devops.qc.domain.converter.AutomaticSchedulerAdapterConvertor;
import com.zto.devops.qc.domain.gateway.util.CronUtilService;
import com.zto.devops.qc.domain.service.AutomaticSchedulerCommandDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpParam;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayWebAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

@Slf4j
@ZsmpService(name = "用例服务", group = "测试管理/定时任务")
@Service
public class AutomaticSchedulerServiceImpl implements AutomaticSchedulerService {
    @ApolloConfig()
    private Config config;

    @Autowired
    private AutomaticSchedulerAdapterConvertor automaticSchedulerConvertor;
    @Autowired
    private AutomaticSchedulerCommandDomainService automaticSchedulerCommandDomainService;
    @Autowired
    private CronUtilService cronUtilService;


    @ZApiOperation(description = "测试管理-新增定时任务", apiName = "tm/scheduler/add", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> add(AddAutomaticSchedulerReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("所属产品"))
                .on(req.getSchedulerName(), new LengthValidator(1, 60, "任务名称"))
                .on(req.getCrontab(), new NotBlankValidator("crontab表达式"))
                .on(req.getExecuteEnv(), new NotBlankValidator("运行空间"))
                .on(req.getExecuteTag(), new NotBlankValidator("运行空间标签"))
                .on(req.getCcList(), new ListSizeValidator(0L, 10L, "抄送人"))
                .when(CollectionUtils.isNotEmpty(req.getCcList()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        cronUtilService.checkCrontab(req.getCrontab());
        String aggregateId = AggregateIdUtil.generateId(AggregateType.SNOWFLAKE);
        AddAutomaticSchedulerCommand command = new AddAutomaticSchedulerCommand(aggregateId);
        automaticSchedulerConvertor.converter(req, command);
        GatewayContext.fillCurrentUser(command);
        automaticSchedulerCommandDomainService.addAutomaticScheduler(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-修改定时任务", apiName = "tm/scheduler/edit", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> edit(EditAutomaticSchedulerReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getExecuteTag(), new NotBlankValidator("运行空间标签"))
                .on(req.getSchedulerCode(), new NotBlankValidator("任务编码"))
                .on(req.getSchedulerName(), new LengthValidator(1, 60, "任务名称"))
                .when(null != req.getSchedulerName())
                .on(req.getCrontab(), new NotBlankValidator("crontab表达式"))
                .when(null != req.getCrontab())
                .on(req.getExecuteEnv(), new NotBlankValidator("运行空间"))
                .when(null != req.getExecuteEnv())
                .on(req.getCcList(), new ListSizeValidator(0L, 10L, "抄送人"))
                .when(CollectionUtils.isNotEmpty(req.getCcList()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (StringUtils.isNotBlank(req.getCrontab())) {
            cronUtilService.checkCrontab(req.getCrontab());
        }
        EditAutomaticSchedulerCommand command = new EditAutomaticSchedulerCommand(req.getSchedulerCode());
        automaticSchedulerConvertor.converter(req, command);
        GatewayContext.fillCurrentUser(command);
        automaticSchedulerCommandDomainService.editAutomaticScheduler(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-删除定时任务", apiName = "tm/scheduler/delete", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> delete(AutomaticSchedulerSimpleReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getSchedulerCode(), new NotBlankValidator("任务编码"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        DeleteAutomaticSchedulerCommand command = new DeleteAutomaticSchedulerCommand(req.getSchedulerCode());
        GatewayContext.fillCurrentUser(command);
        automaticSchedulerCommandDomainService.deleteAutomaticScheduler(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-定时任务-关联用例", apiName = "tm/scheduler/addCase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addCase(AddSchedulerCasesReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getSchedulerCode(), new NotBlankValidator("任务编码"))
                .onEach(req.getCaseCodeList(), new NotBlankValidator("用例code"))
                .when(CollectionUtils.isNotEmpty(req.getCaseCodeList()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        int minSize = Integer.parseInt(config.getProperty("minSize", ""));
        if (req.getCaseCodeList().size() > minSize) {
            throw new ServiceException("一次最多支持关联" + minSize + "条用例");
        }
        AddSchedulerCasesCommand command = new AddSchedulerCasesCommand(req.getSchedulerCode());
        automaticSchedulerConvertor.convertor(req, command);
        GatewayContext.fillCurrentUser(command);
        automaticSchedulerCommandDomainService.addSchedulerCases(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-定时任务-删除关联用例", apiName = "tm/scheduler/removeCase", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> removeCase(RemoveSchedulerCasesReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getSchedulerCode(), new NotBlankValidator("任务编码"))
                .onEach(req.getCaseCodeList(), new NotBlankValidator("用例code"))
                .when(CollectionUtils.isNotEmpty(req.getCaseCodeList()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        int minSize = Integer.parseInt(config.getProperty("minSize", ""));
        if (req.getCaseCodeList().size() > minSize) {
            throw new ServiceException("一次最多支持关联" + minSize + "条用例");
        }
        RemoveSchedulerCasesCommand command = new RemoveSchedulerCasesCommand(req.getSchedulerCode());
        automaticSchedulerConvertor.convertor(req, command);
        GatewayContext.fillCurrentUser(command);
        automaticSchedulerCommandDomainService.removeSchedulerCases(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-定时任务-分页",
            apiName = "tm/scheduler/page", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<AutomaticSchedulerVO> page(PageSchedulerReq req) {
        PageSchedulerQuery query = new PageSchedulerQuery();
        automaticSchedulerConvertor.converter(req, query);
        if (CollectionUtils.isNotEmpty(req.getCreatorList())) {
            query.setCreatorIdList(req.getCreatorList());
        }
        PageSchedulerVO resp = automaticSchedulerCommandDomainService.query(query);
        return StatusCode.OK.build(resp.getList(), req.getPage(), req.getSize(), resp.getTotal());
    }

    @ZApiOperation(description = "测试管理-定时任务-详情", apiName = "tm/scheduler/detail", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<AutomaticSchedulerDetailVO> detail(AutomaticSchedulerSimpleReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getSchedulerCode(), new NotBlankValidator("任务编码"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        SchedulerDetailQuery query = new SchedulerDetailQuery(req.getSchedulerCode());
        AutomaticSchedulerDetailVO resp = automaticSchedulerCommandDomainService.query(query);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "查询定时任关联用例-分组",
            apiName = "tm/scheduler/getModuleList", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<SchedulerCaseVO>> getModuleList(SchedulerModuleListReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getSchedulerCode(), new NotBlankValidator("定时任务code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        SchedulerModuleListQuery query = automaticSchedulerConvertor.converter(req);
        GatewayContext.fillCurrentUser(query);
        List<SchedulerCaseVO> list = automaticSchedulerCommandDomainService.query(query);
        return StatusCode.OK.build(list);
    }

    @ZApiOperation(description = "查询定时任务关联用例code", apiName = "tm/scheduler/listSchedulerCaseCode", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<String>> listSchedulerCaseCode(SchedulerCaseCodeListReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getSchedulerCode(), new NotBlankValidator("定时任务code"))
                .on(req.getParentCode(), new NotBlankValidator("父节点code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        SchedulerCaseCodeListQuery query = automaticSchedulerConvertor.converter(req);
        GatewayContext.fillCurrentUser(query);
        List<String> codeList = automaticSchedulerCommandDomainService.query(query);
        return StatusCode.OK.build(codeList);
    }

    @ZApiOperation(description = "查询定时任务关联用例",
            apiName = "tm/scheduler/getListSchedulerCase", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<SchedulerCaseVO>> getListSchedulerCase(ListSchedulerCaseReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getSchedulerCode(), new NotBlankValidator("定时任务code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ListSchedulerCaseQuery query = automaticSchedulerConvertor.converter(req);
        GatewayContext.fillCurrentUser(query);

        List<SchedulerCaseVO> list = automaticSchedulerCommandDomainService.query(query);
        return StatusCode.OK.build(list);
    }

    @ZApiOperation(description = "测试管理-定时任务-列表（产品维度）", apiName = "tm/scheduler/list", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<ProductSchedulerVO>> list(ProductSchedulerReq req) {
        ProductSchedulerQuery query = new ProductSchedulerQuery();
        automaticSchedulerConvertor.converter(req, query);
        List<ProductSchedulerVO> resp = automaticSchedulerCommandDomainService.query(query);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "获取运行时间",
            apiName = "tm/scheduler/getNextExecuteTimes", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<String> getNextExecuteTimes(@ZsmpParam(description = "cron表达式", sample = "0 0/30 * * * ?", required = true) String cron) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(cron, new NotBlankValidator("cron表达式"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        cronUtilService.checkCrontab(cron);
        List<String> list = cronUtilService.nextExecuteTimes(cron, new Date(), 5);
        return StatusCode.OK.build(StringUtils.join(list, ","));
    }

    @ZApiOperation(description = "测试管理-定时任务-运行一次", apiName = "tm/scheduler/run", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> run(RunSchedulerReq req) {
        AutomaticSchedulerExecutionCommand command = new AutomaticSchedulerExecutionCommand(req.getSchedulerCode());
        command.setTrigMode(AutomaticTaskTrigModeEnum.SCHEDULER_ONCE);
        GatewayContext.fillCurrentUser(command);
        automaticSchedulerCommandDomainService.automaticSchedulerExecution(command);
        return StatusCode.OK.build();
    }
}
