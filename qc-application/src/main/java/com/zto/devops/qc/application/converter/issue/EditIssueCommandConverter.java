package com.zto.devops.qc.application.converter.issue;

import com.zto.devops.qc.client.model.issue.command.EditIssueCommand;
import com.zto.devops.qc.client.service.issue.model.EditIssueByLaneReq;
import com.zto.devops.qc.client.service.issue.model.EditIssueReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface EditIssueCommandConverter {

    @Mapping(source = "developUserId", target = "developer.userId")
    @Mapping(source = "developUserName", target = "developer.userName")
    @Mapping(source = "testUserId", target = "tester.userId")
    @Mapping(source = "testUserName", target = "tester.userName")
    @Mapping(source = "requirementCode", target = "requirement.code")
    @Mapping(source = "requirementName", target = "requirement.name")
    @Mapping(source = "findVersionCode", target = "findVersion.code")
    @Mapping(source = "findVersionName", target = "findVersion.name")
    @Mapping(source = "fixVersionCode", target = "fixVersion.code")
    @Mapping(source = "fixVersionName", target = "fixVersion.name")
    @Mapping(source = "sprintCode", target = "sprint.code")
    @Mapping(source = "sprintName", target = "sprint.name")
    EditIssueCommand convert(EditIssueReq req);

    @Mapping(source = "developUserId", target = "developer.userId")
    @Mapping(source = "developUserName", target = "developer.userName")
    @Mapping(source = "testUserId", target = "tester.userId")
    @Mapping(source = "testUserName", target = "tester.userName")
    EditIssueCommand convert(EditIssueByLaneReq req);

}
