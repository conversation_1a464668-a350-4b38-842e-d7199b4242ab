package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.issue.query.ExpIssueQuery;
import com.zto.devops.qc.client.model.issue.query.PageIssueQuery;
import com.zto.devops.qc.client.model.issue.query.PageIssueThingQuery;
import com.zto.devops.qc.client.model.issue.query.PageLaneIssueQuery;
import com.zto.devops.qc.client.service.issue.model.ExportIssueReq;
import com.zto.devops.qc.client.service.issue.model.PageIssueReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface PageIssueQueryConverter {

    @Mapping(target = "orderField", expression = "java(com.zto.devops.qc.client.enums.issue.IssueFieldEnum.getNameByCode(pageIssueReq.getOrderField()))")
    PageIssueQuery convert(PageIssueReq pageIssueReq);

    @Mapping(target = "orderField", expression = "java(com.zto.devops.qc.client.enums.issue.IssueFieldEnum.getNameByCode(pageIssueReq.getOrderField()))")
    ExpIssueQuery convertExp(ExportIssueReq pageIssueReq);

    @Mapping(target = "orderField", expression = "java(com.zto.devops.qc.client.enums.issue.IssueFieldEnum.getNameByCode(pageIssueReq.getOrderField()))")
    PageLaneIssueQuery convertLaneQuery(PageIssueReq pageIssueReq);

    PageIssueThingQuery convertThing(PageIssueReq pageIssueReq);
}
