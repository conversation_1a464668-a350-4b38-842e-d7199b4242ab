package com.zto.devops.qc.application.job;

import com.zto.devops.qc.domain.service.TmTestPlanCommandDomainService;
import com.zto.zss.common.exception.ZSSException;
import com.zto.zss.worker.processor.BasicProcessor;
import com.zto.zss.worker.processor.model.ProcessResult;
import com.zto.zss.worker.processor.model.TaskContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 延迟验收版本定时创建待办工单.
 */
@Slf4j
@Component("DelayAcceptAuditJob")
public class DelayAcceptAuditJob implements BasicProcessor {

    @Autowired
    private TmTestPlanCommandDomainService planCommandDomainService;

    @Override
    public ProcessResult process(TaskContext taskContext) throws ZSSException {
        try {
            log.info("DelayAcceptAuditJob_begin!");
            planCommandDomainService.sendAudit();
        } catch (Exception e) {
            log.error("DelayAcceptAuditJob_error: ", e);
            return ProcessResult.error("系统异常", e.getMessage());
        }
        return ProcessResult.success("OK");
    }
}
