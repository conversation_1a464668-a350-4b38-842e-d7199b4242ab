package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.application.converter.KnowledgeBaseConverter;
import com.zto.devops.qc.client.model.knowledgebase.query.KnowledgeBaseByProductCodeQuery;
import com.zto.devops.qc.client.service.knowledgebase.KnowledgeBaseService;
import com.zto.devops.qc.client.service.knowledgebase.model.KnowledgeBaseByProductCodeReq;
import com.zto.devops.qc.client.service.knowledgebase.model.KnowledgeBaseByProductCodeResp;
import com.zto.devops.qc.client.service.knowledgebase.model.RemoveKnowledgeBaseByProductCodeReq;
import com.zto.devops.qc.client.service.knowledgebase.model.AddKnowledgeBaseByProductCodeReq;
import com.zto.devops.qc.domain.service.KnowledgeBaseCommandDomainService;
import com.zto.devops.qc.domain.service.KnowledgeBaseQueryDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayWebAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@Service
@ZsmpService(name = "知识库接口目录")
public class KnowledgeBaseServiceImpl extends GatewayBase implements KnowledgeBaseService {


    @Autowired
    private KnowledgeBaseQueryDomainService knowledgeBaseQueryDomainService;

    @Autowired
    private KnowledgeBaseCommandDomainService knowledgeBaseCommandDomainService;

    @Autowired
    private KnowledgeBaseConverter converter;

    @ZApiOperation(description = "获取知识库文档啊url", apiName = "tm/knowledge/getKnowledgeBaseDetail", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<KnowledgeBaseByProductCodeResp> getKnowledgeBaseDetail(KnowledgeBaseByProductCodeReq req) {
        if (StringUtil.isBlank(req.getProductCode())) {
            throw new ServiceException("产品code不能为空");
        }
        KnowledgeBaseByProductCodeQuery query = new KnowledgeBaseByProductCodeQuery();
        query.setProductCode(req.getProductCode());
        return StatusCode.OK.build(converter.converter(knowledgeBaseQueryDomainService.getKnowledgeBaseDetail(query)));
    }

    @Override
    public Result<Void> addMemberPermission(AddKnowledgeBaseByProductCodeReq req) {
        knowledgeBaseCommandDomainService.addMemberPermission(converter.converter(req));
        return StatusCode.OK.build();
    }

    @Override
    public Result<Void> RemoveMemberPermission(RemoveKnowledgeBaseByProductCodeReq req) {
        knowledgeBaseCommandDomainService.removeMemberPermission(converter.converter(req));
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "同步接口", apiName = "tm/knowledge/synchronizeData", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> synchronizeData() {
        knowledgeBaseCommandDomainService.synchronizeData();
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "同步产品权限接口", apiName = "tm/knowledge/synchronizePermissionData", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> synchronizePermissionData() {
        knowledgeBaseCommandDomainService.synchronizePermissionData();
        return StatusCode.OK.build();
    }

    @Override
    public Result<Void> createProductKnowledge(String productCode) {
        knowledgeBaseCommandDomainService.createProductKnowledge(productCode);
        return StatusCode.OK.build();
    }
}
