package com.zto.devops.qc.application.service;


import com.alibaba.dubbo.config.annotation.Service;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import com.zto.devops.qc.client.model.issue.command.AddRelevantUserCommand;
import com.zto.devops.qc.client.model.issue.command.RemoveRelevantUserCommand;
import com.zto.devops.qc.client.service.relevantUser.IRelevantUserService;
import com.zto.devops.qc.client.service.relevantUser.model.CCIssueReq;
import com.zto.devops.qc.client.service.relevantUser.model.UnfollowIssueReq;
import com.zto.devops.qc.domain.service.RelevantUserCommandDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWSsoAuth;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayAuth;
import com.zto.zsmp.annotation.gateway.GatewayAuthType;
import com.zto.zsmp.annotation.gateway.GatewayWebAuth;
import org.springframework.beans.factory.annotation.Autowired;

@Service
@ZsmpService(name = "缺陷关注及抄送接口目录")
public class RelevantUserServiceImpl extends GatewayBase implements IRelevantUserService {

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private RelevantUserCommandDomainService relevantUserCommandDomainService;

    @ZApiOperation(description = "一站式研发--抄送缺陷", apiName = "qc/relevantUser/CCIssue", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    @Deprecated
    public Result<Void> CCIssue(CCIssueReq req) {
        String aggregateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddRelevantUserCommand command = new AddRelevantUserCommand(req.getIssueCode());
        command.setBusinessCode(req.getIssueCode());
        command.setCode(aggregateId);
        command.setDomain(DomainEnum.ISSUE);
        command.setType(RelevantUserTypeEnum.CC);
        command.setUserList(req.getCcUserList());
        GatewayContext.fillCurrentUser(command);
        relevantUserCommandDomainService.ccIssueCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "一站式研发--取消抄送缺陷", apiName = "qc/relevantUser/unCCIssue", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> unCCIssue(UnfollowIssueReq req) {
        RemoveRelevantUserCommand command = new RemoveRelevantUserCommand(req.getIssueCode());
        command.setCode(req.getCode());
        GatewayContext.fillCurrentUser(command);
        relevantUserCommandDomainService.unCCIssueCommand(command);
        return StatusCode.OK.build();
    }
}
