package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.report.entity.OperatedTestManageVO;
import com.zto.devops.qc.client.model.testmanager.report.command.*;
import com.zto.devops.qc.client.model.testmanager.report.entity.*;
import com.zto.devops.qc.client.model.testmanager.report.query.AccessReportDetailQuery;
import com.zto.devops.qc.client.model.testmanager.report.query.OnlineSmokeReportDetailQuery;
import com.zto.devops.qc.client.model.testmanager.report.query.PageReportMqQuery;
import com.zto.devops.qc.client.model.testmanager.report.query.PermitReportDetailQuery;
import com.zto.devops.qc.client.service.report.model.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface TestReportConverter {

    OperatedTestManageResp convertor(OperatedTestManageVO vo);

    AccessReportDetailQuery convertAccess(QueryTestReportDetailReq req);

    TmAccessReportDetailResp converter(TmAccessReportDetailVO vo);

    OnlineSmokeReportDetailQuery convertSmoke(QueryTestReportDetailReq req);

    TmOnlineSmokeReportDetailResp converter(TmSmokeReportDetailVO detailVO);

    PermitReportDetailQuery convertPermit(QueryTestReportDetailReq req);

    TmPermitReportDetailResp converter(TmPermitReportDetailVO permitReportDetailVO);

    @Mapping(target = "presentationDate", source = "planPresentationDate")
    @Mapping(target = "publishDate", source = "planOnlineDate")
    @Mapping(target = "publishDay", source = "planOnlineDay")
    ExternalTestReportResp convertor(ExternalTestReportDetailVO externalReportVO);

    @Mapping(target = "aggregateId", expression = "java(com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.TEST_REPORT))")
    @Mapping(target = "reportName", source = "req.reportName")
    @Mapping(target = "reportType", expression = "java(com.zto.devops.qc.client.enums.testmanager.report.ReportType.TEST_ACCESS)")
    AddTmAccessReportCommand convertorAccessAdd(EditAccessTestReportReq req);

    @Mapping(target = "aggregateId", source = "req.reportCode")
    @Mapping(target = "reportCode", source = "req.reportCode")
    @Mapping(target = "reportType", expression = "java(com.zto.devops.qc.client.enums.testmanager.report.ReportType.TEST_ACCESS)")
    EditTmAccessReportCommand convertor(EditAccessTestReportReq req);

    @Mapping(target = "aggregateId", expression = "java(com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.TEST_REPORT))")
    @Mapping(target = "reportType", expression = "java(com.zto.devops.qc.client.enums.testmanager.report.ReportType.ONLINE_SMOKE)")
    AddTmOnlineSmokeReportCommand convertorOnlineSmokeAdd(EditOnlineSmokeTestReportReq req);

    @Mapping(target = "aggregateId", source = "req.reportCode")
    @Mapping(target = "reportCode", source = "req.reportCode")
    @Mapping(target = "reportType", expression = "java(com.zto.devops.qc.client.enums.testmanager.report.ReportType.ONLINE_SMOKE)")
    EditTmOnlineSmokeReportCommand convertor(EditOnlineSmokeTestReportReq req);

    @Mapping(target = "aggregateId", source = "reportCode")
    EditAndSendReviewReportCommand editAndSendReviewConvert(EditAndSendReviewReportReq req);

    @Mapping(target = "aggregateId", expression = "java(com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.TEST_REPORT))")
    AddAndSendReviewReportCommand addAndSendReviewConvert(EditAndSendReviewReportReq req);

    @Mapping(target = "aggregateId", expression = "java(com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.TEST_REPORT))")
    @Mapping(target = "reportType", expression = "java(com.zto.devops.qc.client.enums.testmanager.report.ReportType.TEST_PERMIT)")
    AddTmPermitReportCommand convertorPermitAdd(EditPermitTestReportReq req);

    @Mapping(target = "aggregateId", source = "req.reportCode")
    @Mapping(target = "reportCode", source = "req.reportCode")
    @Mapping(target = "reportType", expression = "java(com.zto.devops.qc.client.enums.testmanager.report.ReportType.TEST_PERMIT)")
    EditTmPermitReportCommand convertor(EditPermitTestReportReq req);

    @Mapping(target = "aggregateId", expression = "java(com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.TEST_REPORT))")
    @Mapping(target = "reportType", expression = "java(com.zto.devops.qc.client.enums.testmanager.report.ReportType.SIMPLE_PROCESS)")
    AddAndSendSimpleTestReportCommand convert(SendSimpleTestReportReq req);

    @Mapping(target = "aggregateId", source = "req.reportCode")
    @Mapping(target = "reportCode", source = "req.reportCode")
    @Mapping(target = "reportType", expression = "java(com.zto.devops.qc.client.enums.testmanager.report.ReportType.SIMPLE_PROCESS)")
    EditAndSendSimpleTestReportCommand convertEditAndSendSimpleTestReportCommand(SendSimpleTestReportReq req);

    ReviewReportDetailResp convertor(ReviewReportDetailVO reviewReportVO);

    MobileTestReportDetailResp convert(MobileTestReportDetailVO vo);

    AddReviewReportCommand addReviewConvert(EditReviewReportReq req);

    @Mapping(target = "aggregateId", source = "reportCode")
    EditReviewReportCommand editReviewConvert(EditReviewReportReq req);

    AddExternalTestReportCommand addExternalConvert(EditExternalTestReportReq req);

    @Mapping(target = "aggregateId", source = "reportCode")
    EditExternalTestReportCommand editExternalConvert(EditExternalTestReportReq req);

    AddMobileTestReportCommand addMobileConvert(EditMobileTestReportReq req);

    @Mapping(target = "aggregateId", source = "reportCode")
    EditMobileTestReportCommand editMobileConvert(EditMobileTestReportReq req);

    @Mapping(target = "planPresentationDay", source = "presentationDay")
    SimpleTestReportDetailResp convert(SimpleTestReportDetailVO vo);

    AddSimpleTestReportCommand convert(SaveSimpleTestReportReq req, String aggregateId);

    @Mapping(target = "aggregateId", source = "reportCode")
    EditSimpleTestReportCommand convert(SaveSimpleTestReportReq req);

    AddAndSendTmAccessTestReportCommand convert(AddAccessTestReportReq req, String aggregateId);

    @Mapping(target = "aggregateId", source = "reportCode")
    EditAndSendTmAccessTestReportCommand convert(AddAccessTestReportReq req);

    AddAndSendExternalTestReportCommand convert(AddExternalTestReportReq req, String aggregateId);

    @Mapping(target = "aggregateId", source = "reportCode")
    EditAndSendExternalTestReportCommand convert(AddExternalTestReportReq req);

    AddAndSendMobileTestReportCommand convert(AddMobileTestReportReq req, String aggregateId);

    @Mapping(target = "aggregateId", source = "reportCode")
    EditAndSendMobileTestReportCommand convert(AddMobileTestReportReq req);

    AddAndSendTmOnlineSmokeTestReportCommand convert(AddOnlineSmokeTestReportReq req, String aggregateId);

    @Mapping(target = "aggregateId", source = "reportCode")
    EditAndSendTmOnlineSmokeTestReportCommand convert(AddOnlineSmokeTestReportReq req);

    AddAndSendTmPermitTestReportCommand convert(AddPermitTestReportReq req, String aggregateId);

    @Mapping(target = "aggregateId", source = "reportCode")
    EditAndSendTmPermitTestReportCommand convert(AddPermitTestReportReq req);

    PageReportMqQuery convert(PageReportReq req);

    QueryPermitResultResp converter(QueryPermitResultVO vo);

    ComputeMetricsCommand convert(ComputeMetricsReq vo);
}
