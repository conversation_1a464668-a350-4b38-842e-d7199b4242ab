package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.qc.application.converter.attachment.AttachmentRespConverter;
import com.zto.devops.qc.client.enums.issue.AttachmentDocumentTypeEnum;
import com.zto.devops.qc.client.model.issue.command.AddAttachmentCommand;
import com.zto.devops.qc.client.model.issue.command.RemoveAttachmentCommand;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.query.ListAttachmentsByBusinessCodeQuery;
import com.zto.devops.qc.client.service.attachment.AttachmentService;
import com.zto.devops.qc.client.service.attachment.model.AddAttachmentReq;
import com.zto.devops.qc.client.service.attachment.model.AttachmentResp;
import com.zto.devops.qc.client.service.attachment.model.ListAttachmentsByBusinessCodeReq;
import com.zto.devops.qc.client.service.attachment.model.RemoveAttachmentReq;
import com.zto.devops.qc.domain.service.AttachmentCommandDomainService;
import com.zto.devops.qc.domain.service.AttachmentQueryDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWSsoAuth;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayWebAuth;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@ZsmpService(name = "一站式研发-附件相关接口", group = "一站式研发-附件")
@Service
public class AttachmentServiceImpl extends GatewayBase implements AttachmentService {

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private AttachmentCommandDomainService attachmentCommandDomainService;

    @Autowired
    private AttachmentQueryDomainService attachmentQueryDomainService;

    @Autowired
    private AttachmentRespConverter respConverter;

    @ZApiOperation(description = "一站式研发--缺陷查询附件", apiName = "qc/attachment/list", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<AttachmentResp>> ListAttachmentsByBusinessCode(ListAttachmentsByBusinessCodeReq req) {
        List<AttachmentVO> attachmentVOList = attachmentQueryDomainService.query(new ListAttachmentsByBusinessCodeQuery(req.getBusinessCode()));
        List<AttachmentResp> list = respConverter.convert(attachmentVOList);
        return StatusCode.OK.build(list);
    }



    @ZApiOperation(description = "一站式研发--添加附件", apiName = "qc/attachment/add", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addAttachment(AddAttachmentReq req) {
        AddAttachmentCommand command = new AddAttachmentCommand(req.getBusinessCode());
        command.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        command.setDomain(DomainEnum.ISSUE);
        command.setName(req.getName());
        command.setRemoteFileId(req.getRemoteFileId());
        command.setUrl(req.getUrl());
        command.setBusinessCode(req.getBusinessCode());
        command.setDocumentType(AttachmentDocumentTypeEnum.BUSINESS);
        command.setType(req.getType());
        GatewayContext.fillCurrentUser(command);
        attachmentCommandDomainService.handle(command);
        return StatusCode.OK.build();
    }


    @ZApiOperation(description = "一站式研发--移除附件", apiName = "qc/attachment/remove", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> removeAttachment(RemoveAttachmentReq req) {
        RemoveAttachmentCommand command = new RemoveAttachmentCommand(req.getBusinessCode());
        command.setCode(req.getCode());
        GatewayContext.fillCurrentUser(command);
        attachmentCommandDomainService.handle(command);
        return StatusCode.OK.build();
    }
}
