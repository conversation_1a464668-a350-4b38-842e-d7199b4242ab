package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.testmanager.cases.query.FindSortedPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.plan.command.ChangeCaseExecuteResultCommand;
import com.zto.devops.qc.client.model.testmanager.plan.command.EditPlanStageStatusCommand;
import com.zto.devops.qc.client.model.testmanager.plan.command.EditPlanStatusCommand;
import com.zto.devops.qc.client.model.testmanager.plan.entity.*;
import com.zto.devops.qc.client.model.testmanager.plan.query.*;
import com.zto.devops.qc.client.service.plan.model.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring")
public interface TmTestPlanAdapterConvertor {

    RelatedPlanReportQuery convertor(RelatedTestReq req);

    RelatedPlanReportResp convertor(RelatedPlanReportVO relatedVO);

    PagePlanIssueQuery convertor(TestPlanIssueReq req);

    List<PlanIssueResp> convertor(List<TmPlanCaseIssueVO> list);

    @Mapping(target = "reopenTime", source = "reopenTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "findTime", source = "findTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "startFixTime", source = "startFixTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "delayFixTime", source = "delayFixTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "deliverTime", source = "deliverTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "rejectTime", source = "rejectTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "closeTime", source = "closeTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "updateTime", source = "updateTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "gmtCreate", source = "gmtCreate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "fixVersionCode", expression = "java(com.zto.devops.qc.client.enums.constants.DefaultValueEnum" +
            ".VERSION_VALUE.isDefaultValue(vo.getFixVersionCode())?null:vo.getFixVersionCode())")
    @Mapping(target = "fixVersionName", expression = "java(com.zto.devops.qc.client.enums.constants.DefaultValueEnum" +
            ".BLANKSTRING.isDefaultValue(vo.getFixVersionName())?null:vo.getFixVersionName())")
    PlanIssueResp convertor(TmPlanCaseIssueVO vo);

    @Mapping(target = "orderField", expression = "java(com.zto.devops.qc.client.enums.testmanager.plan.PlanFieldEnum.getNameByCode(req.getOrderField()))")
    PlanListQuery convertor(PlanPageReq req);

    @Mapping(target = "search", source = "codeOrTitle")
    @Mapping(target = "planCode", source = "code")
    PageListPlanCaseQuery convertor(PageTestPlanCaseListReq req);

    List<PagePlanTestcaseResp> converterList(Collection<TestPlanCaseVO> list);

    @Mapping(target = "search", source = "codeOrTitle")
    @Mapping(target = "planCode", source = "code")
    ListPlanCaseCodeQuery convertor(TestPlanCaseCodeListReq req);

    ListPlanPhaseQuery convertor(ListPlanPhaseReq req);

    void convertor(FindSortedPlanCaseReq req, @MappingTarget FindSortedPlanCaseQuery query);

    @Mapping(target = "planName", source = "name")
    PageTestPlanQuery convertor(PageTestPlanReq req);

    List<PageTestPlanResp> convertorList(List<PageTestPlanBaseVO> list);

    @Mapping(target = "code", source = "planCode")
    @Mapping(target = "name", source = "planName")
    PageTestPlanResp convertor(PageTestPlanBaseVO vo);

    VersionPlanQuery convertor(VersionPlanReq req);

    List<TestPlanResp> convertor(Collection<TmTestPlanVO> versionPlanVOList);

    @Mapping(target = "search", source = "codeOrTitle")
    @Mapping(target = "planCode", source = "code")
    ListPlanCaseQuery convertor(TestPlanCaseListReq req);

    @Mapping(target = "planCode", source = "code")
    ListPlanCaseModuleQuery convertor(TestPlanCaseModuleListReq req);

    @Mapping(target = "aggregateId", source = "planCode")
    EditPlanStageStatusCommand convertor(EditTestPlanStageReq req);

    @Mapping(target = "aggregateId", source = "planCode")
    EditPlanStatusCommand convertor(EditTestPlanStatusReq req);

    TmTestPlanSendEmailVO convertor(SendTestPlanReq req);

    @Mapping(target = "code", source = "planCode")
    TmTestPlanVO convertor(AddMobileSpecialTestPlanReq req);

    AssociatedTestPlanListQuery convertor(AssociatedTestPlanListReq req);

    @Mapping(target = "aggregateId", source = "planCode")
    ChangeCaseExecuteResultCommand convertor(ChangeCaseExecuteResultReq req);

    CurrentPersonPermissionInformationQuery convertor(CurrentPersonPermissionInformationReq req);

    @Mapping(target = "code", source = "planCode")
    TmTestPlanVO convertor(AddCommonTestPlanReq req);

    @Mapping(target = "code", source = "planCode")
    TmTestPlanVO convertor(AddSafeTestPlanReq req);

    VerifyTestPassConditionQuery convertor(VerifyTestPassConditionReq req);

    List<PagePlanPhaseResp> convertorPlaseList(List<PlanPhaseVO> list);
}
