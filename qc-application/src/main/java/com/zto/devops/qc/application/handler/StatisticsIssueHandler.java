package com.zto.devops.qc.application.handler;

import com.zto.devops.framework.common.message.EventHandler;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.project.client.model.version.event.VersionOnlineEvent;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.event.*;
import com.zto.devops.qc.client.model.issue.query.SimpleSingleIssueQuery;
import com.zto.devops.qc.domain.service.IssueQueryDomainService;
import com.zto.devops.qc.domain.service.StatisticsIssueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class StatisticsIssueHandler {

    @Autowired
    private IssueQueryDomainService issueQueryDomainService;
    @Autowired
    private StatisticsIssueService statisticsIssueService;

    @EventHandler
    public void handle(VersionOnlineEvent event) {
        log.info("版本上线--" + event.getCode());
        this.statisticsIssue(event.getCode());
    }

    @EventHandler
    public void handle(IssueAddedEvent event) {
        log.info("新增缺陷--" + event.getCode());
        if (event.getFindVersion() == null) {
            return;
        }
        this.statisticsIssue(event.getFindVersion().getCode());
    }

    @EventHandler
    public void handle(IssueEditedEvent event) {
        log.info("编辑缺陷--" + event.getCode());
        if (event.getFindVersion() == null) {
            return;
        }
        this.statisticsIssue(event.getFindVersion().getCode());
    }

    @EventHandler
    public void handle(IssueEditedVersionChangedEvent event) {
        log.info("编辑缺陷版本修改前--" + event.getCode());
        if (event.getFindVersion() == null) {
            return;
        }
        this.statisticsIssue(event.getFindVersion().getCode());
    }

    @EventHandler
    public void handle(IssueVersionEditedEvent event) {
        log.info("缺陷关联版本--" + event.getCode());
        if (event.getFindVersion() == null) {
            return;
        }
        this.statisticsIssue(event.getFindVersion().getCode());
    }

    @EventHandler
    public void handle(IssueRemovedEvent event) {
        log.info("删除缺陷--" + event.getCode());
        String versionCode = this.getIssueFindVersionCode(event.getCode());
        this.statisticsIssue(versionCode);
    }

    @EventHandler
    public void handle(IssueStartFixedEvent event) {
        log.info("IssueStartFixedEvent--" + event.getCode());
        String versionCode = this.getIssueFindVersionCode(event.getCode());
        this.statisticsIssue(versionCode);
    }

    @EventHandler
    public void handle(IssueDelayFixedEvent event) {
        log.info("IssueDelayFixedEvent--" + event.getCode());
        String versionCode = this.getIssueFindVersionCode(event.getCode());
        this.statisticsIssue(versionCode);
    }

    @EventHandler
    public void handle(IssueDeliveryValidatedEvent event) {
        log.info("IssueDeliveryValidatedEvent--" + event.getCode());
        String versionCode = this.getIssueFindVersionCode(event.getCode());
        this.statisticsIssue(versionCode);
    }

    @EventHandler
    public void handle(IssueValidatedAccessClosedEvent event) {
        log.info("IssueValidatedAccessClosedEvent--" + event.getCode());
        String versionCode = this.getIssueFindVersionCode(event.getCode());
        this.statisticsIssue(versionCode);
    }

    @EventHandler
    public void handle(IssueBackToRepairedEvent event) {
        log.info("IssueBackToRepairedEvent--" + event.getCode());
        String versionCode = this.getIssueFindVersionCode(event.getCode());
        this.statisticsIssue(versionCode);
    }

    @EventHandler
    public void handle(IssueRefusedEvent event) {
        log.info("IssueRefusedEvent--" + event.getCode());
        String versionCode = this.getIssueFindVersionCode(event.getCode());
        this.statisticsIssue(versionCode);
    }

    @EventHandler
    public void handle(IssueReopenEvent event) {
        log.info("IssueReopenEvent--" + event.getCode());
        String versionCode = this.getIssueFindVersionCode(event.getCode());
        this.statisticsIssue(versionCode);
    }

    @EventHandler
    public void handle(IssueConfirmClosedEvent event) {
        log.info("IssueConfirmClosedEvent--" + event.getCode());
        String versionCode = this.getIssueFindVersionCode(event.getCode());
        this.statisticsIssue(versionCode);
    }

    private String getIssueFindVersionCode(String issueCode) {
        if (StringUtil.isBlank(issueCode)) {
            return null;
        }
        SimpleSingleIssueQuery query = new SimpleSingleIssueQuery();
        query.setCode(issueCode);
        IssueVO issueVO = issueQueryDomainService.findIssueByIssueCode(query);
        if (issueVO == null) {
            return null;
        }
        return issueVO.getFindVersionCode();
    }

    private void statisticsIssue(String versionCode) {
        if (StringUtil.isBlank(versionCode)) {
            return;
        }
        statisticsIssueService.statisticsVersionIssue(versionCode);
    }
}
