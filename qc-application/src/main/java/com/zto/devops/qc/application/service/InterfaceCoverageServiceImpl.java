package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.baidu.unbiz.fluentvalidator.ComplexResult;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.github.pagehelper.PageInfo;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.exception.FluentException;
import com.zto.devops.framework.common.validator.NotNullValidator;
import com.zto.devops.qc.client.service.coverage.InterfaceCoverageService;
import com.zto.devops.qc.client.service.coverage.model.req.ListVersionInterfaceReq;
import com.zto.devops.qc.client.service.coverage.model.req.PageInterfaceCoverageInfoReq;
import com.zto.devops.qc.client.service.coverage.model.req.QueryInterfaceCoverageRateReq;
import com.zto.devops.qc.client.service.coverage.model.req.ZcatMqBodyReq;
import com.zto.devops.qc.client.service.coverage.model.resp.InterfaceCoverageInfoResp;
import com.zto.devops.qc.client.service.coverage.model.resp.InterfaceCoverageRateResp;
import com.zto.devops.qc.client.service.coverage.model.resp.VersionInterfaceResp;
import com.zto.devops.qc.domain.service.InterfaceCoverageDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayWebAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Service
@Slf4j
@ZsmpService(name = "接口覆盖率服务", group = "测试管理/接口覆盖率")
public class InterfaceCoverageServiceImpl  extends GatewayBase implements InterfaceCoverageService {

    @Autowired
    private InterfaceCoverageDomainService interfaceCoverageDomainService;

    @ZApiOperation(description = "测试管理--接口覆盖率列表（分页）",
            apiName = "tm/interfaceCoverage/pageInfo", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<InterfaceCoverageInfoResp> queryPageInterfaceCoverageInfo(PageInterfaceCoverageInfoReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPage(), new NotNullValidator("页数"))
                .on(req.getSize(), new NotNullValidator("数据大小"))
                .on(req.getAppId(), new NotNullValidator("应用ID"))
                .on(req.getVersionCode(), new NotNullValidator("版本Code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        PageInfo<InterfaceCoverageInfoResp> pageInfo = interfaceCoverageDomainService.queryPageInterfaceCoverageInfo(req);
        return StatusCode.OK.build(pageInfo.getList(), pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal());
    }

    @ZApiOperation(description = "测试管理--获取接口覆盖率",
            apiName = "tm/interfaceCoverage/getRate", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<InterfaceCoverageRateResp> queryInterfaceCoverageRate(QueryInterfaceCoverageRateReq req){
        ComplexResult cr = checkAll()
                .on(req.getAppId(), new NotNullValidator("应用ID"))
                .on(req.getVersionCode(), new NotNullValidator("版本Code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(interfaceCoverageDomainService.queryInterfaceCoverageRate(req));
    }

    @ZApiOperation(description = "测试管理--同步zcat采集的接口测试数据）",
            apiName = "tm/interfaceCoverage/syncInterfaceTestedInfo", namespace = "luban")
    @Override
    public Result<Boolean> syncInterfaceTestedInfo(ZcatMqBodyReq req){
        ComplexResult cr = checkAll()
                .on(req.getAppId(), new NotNullValidator("应用ID"))
                .on(req.getLocation(), new NotNullValidator("应用ip"))
                .on(req.getInterfaceName(), new NotNullValidator("接口"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(interfaceCoverageDomainService.syncInterfaceTestedInfo(req));
    }

    @ZsmpApiOperation(description = "版本内变更影响的接口列表",
            gatewayApi = @GatewayApi(namespace = "luban",
                    name = "tm/interface/versionRelated",
                    description = "版本内变更影响的接口列表",
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true))
    )
    @Override
    public Result<VersionInterfaceResp> queryInterfaceRelatedToVersion(ListVersionInterfaceReq req) {
        return StatusCode.OK.build(interfaceCoverageDomainService.queryInterfaceRelatedToVersion(req.getVersionCode()));
    }
}