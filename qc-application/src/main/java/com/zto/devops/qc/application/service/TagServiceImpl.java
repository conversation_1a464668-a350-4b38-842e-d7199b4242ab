package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.baidu.unbiz.fluentvalidator.ComplexResult;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.exception.FluentException;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.common.validator.NotBlankValidator;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.qc.client.enums.issue.TagTypeEnum;
import com.zto.devops.qc.client.model.issue.command.AddTagCommand;
import com.zto.devops.qc.client.model.issue.command.RemoveTagCommand;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.DeleteTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindTestcaseTagQuery;
import com.zto.devops.qc.client.service.tag.ITagService;
import com.zto.devops.qc.client.service.tag.model.AddTagReq;
import com.zto.devops.qc.client.service.tag.model.ListTagsByBusinessCodeReq;
import com.zto.devops.qc.client.service.tag.model.RemoveTagReq;
import com.zto.devops.qc.domain.service.TagCommandDomainService;
import com.zto.devops.qc.domain.service.TagQueryDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWSsoAuth;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpParam;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayAuth;
import com.zto.zsmp.annotation.gateway.GatewayAuthType;
import com.zto.zsmp.annotation.gateway.GatewayWebAuth;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@ZsmpService(name = "标签接口")
public class TagServiceImpl extends GatewayBase implements ITagService {

    @Autowired
    private TagQueryDomainService tagQueryDomainService;

    @Autowired
    private TagCommandDomainService tagCommandDomainService;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @ZApiOperation(description = "查询标签", apiName = "qc/tag/list", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<TagVO>> listTagsByBusinessCode(ListTagsByBusinessCodeReq req) {
        //查询转换
        List<TagVO> tagVOList = tagQueryDomainService.listTagsByBusinessCodeAndDomainQuery(req.getBusinessCode(),req.getDomain());
        return StatusCode.OK.build(tagVOList);
    }

    @ZApiOperation(description = "添加标签", apiName = "qc/tag/add", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addTag(AddTagReq req) {
        ComplexResult cr = checkAll()
                .on(req.getBusinessCode(), new NotBlankValidator("业务编码"))
                .on(req.getTagName(), new NotBlankValidator("标签名称"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        String aggregateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddTagCommand command = new AddTagCommand(req.getBusinessCode());
        command.setCode(aggregateId);
        if (null != req.getDomain()) {
            command.setDomain(req.getDomain());
        }
        command.setBusinessCode(req.getBusinessCode());
        if (null != req.getDomain() && DomainEnum.SCENE.equals(req.getDomain())) {
            command.setTagName(req.getTagName().trim());
        } else {
            command.setTagName(req.getTagName());
        }
        command.setType(TagTypeEnum.BUSINESS);
        command.setTagCode(req.getTagCode());
        GatewayContext.fillCurrentUser(command);
        tagCommandDomainService.addTagCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "移除标签", apiName = "qc/tag/remove", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @ZGWSsoAuth(box = true)
    @Override
    public Result<Void> removeTag(RemoveTagReq req) {
        ComplexResult cr = checkAll()
                .on(req.getBusinessCode(), new NotBlankValidator("业务编码"))
                .on(req.getCode(), new NotBlankValidator("标签编码"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        RemoveTagCommand command = new RemoveTagCommand(req.getBusinessCode());
        command.setCode(req.getCode());
        GatewayContext.fillCurrentUser(command);
        tagCommandDomainService.removeTagCommand(command);
        return StatusCode.OK.build();
    }

    @Override
    @ZsmpApiOperation(description = "测试管理-删除标签库标签",
            gatewayApi = {@GatewayApi(namespace = "luban", name = "tm/cases/deleteTag",
                    description = "测试管理-删除标签库标签", tags = "v3.20.0",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true))
            })
    public Result<Void> deleteTag(@ZsmpParam(description = "标签唯一标识", sample = "", required = true) String code) {
        if (StringUtils.isBlank(code)) {
            throw new ServiceException("标签不能为空！");
        }
        if (StringUtil.isNotEmpty(redisTemplate.opsForValue().get(code))) {
            return StatusCode.OK.build();
        } else {
            redisTemplate.opsForValue().set(code, code, 10, TimeUnit.SECONDS);
        }
        FindTestcaseTagQuery query = new FindTestcaseTagQuery(code);
        TagVO tagVO = tagQueryDomainService.findTestcaseTagQuery(query);
        if (null == tagVO) {
            return StatusCode.OK.build();
        }
        DeleteTestcaseTagCommand command = new DeleteTestcaseTagCommand(code);
        GatewayContext.fillCurrentUser(command);
        tagCommandDomainService.deleteTagCommand(command);
        return StatusCode.OK.build();
    }


}
