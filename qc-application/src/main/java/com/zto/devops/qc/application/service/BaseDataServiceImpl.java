package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONArray;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.qc.client.model.rpc.huiyan.StoreBrandVO;
import com.zto.devops.qc.client.model.rpc.huiyan.StoreNameVO;
import com.zto.devops.qc.client.model.rpc.outlet.ItemTypeVO;
import com.zto.devops.qc.client.model.rpc.waybill.LabelNameVO;
import com.zto.devops.qc.client.service.testmanager.basedata.BaseDataService;
import com.zto.devops.qc.client.service.testmanager.basedata.model.*;
import com.zto.devops.qc.domain.service.BaseDataQueryDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.ZsmpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service
@Slf4j
@ZsmpService(name = "基础信息接口目录", group = "测试管理/基础信息")
public class BaseDataServiceImpl extends GatewayBase implements BaseDataService {

    @Autowired
    private BaseDataQueryDomainService baseDataQueryDomainService;

    @ZApiOperation(
            description = "测试管理-基础信息-查询人员信息",
            apiName = "tm/basedata/queryUserInfo",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<JSONArray> queryUserInfo(UserQueryReq req) {
        return StatusCode.OK.build(baseDataQueryDomainService.queryUserInfo(req.getKeyWord()));
    }

    @ZApiOperation(
            description = "测试管理-基础信息-物品类型枚举",
            apiName = "tm/basedata/itemType",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<ItemTypeVO>> itemType(ItemTypeReq req) {
        return StatusCode.OK.build(baseDataQueryDomainService.itemType(req));
    }

    @ZApiOperation(
            description = "测试管理-基础信息-标签枚举",
            apiName = "tm/basedata/queryTagList",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<LabelNameVO>> queryTagList(TagListQueryReq req) {
        return StatusCode.OK.build(baseDataQueryDomainService.queryTagList(req));
    }

    @ZApiOperation(
            description = "测试管理-基础信息-班次枚举",
            apiName = "tm/basedata/queryClassList",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<ItemTypeVO>> queryClassList(ClassListQueryReq req) {
        return StatusCode.OK.build(baseDataQueryDomainService.queryClassList(req));
    }

    @ZApiOperation(
            description = "测试管理-基础信息-门店品牌",
            apiName = "tm/basedata/queryStoreBrand",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<StoreBrandVO>> queryStoreBrand(StoreBrandQueryReq req) {
        return StatusCode.OK.build(baseDataQueryDomainService.queryStoreBrand(req));
    }

    @ZApiOperation(
            description = "测试管理-基础信息-代理点名称",
            apiName = "tm/basedata/queryStoreName",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<StoreNameVO>> queryStoreName(ClassListQueryReq req) {
        return StatusCode.OK.build(baseDataQueryDomainService.queryStoreName(req));
    }

}
