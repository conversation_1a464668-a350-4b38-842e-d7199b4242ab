package com.zto.devops.qc.application.service;

import cn.hutool.core.io.FileUtil;
import com.alibaba.dubbo.config.annotation.Service;
import com.baidu.unbiz.fluentvalidator.ComplexResult;
import com.baidu.unbiz.fluentvalidator.FluentValidator;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.command.impexp.AddExpCommand;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.FluentException;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.common.validator.LengthValidator;
import com.zto.devops.framework.common.validator.NotBlankValidator;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.service.AsyncImpExpService;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.application.converter.AutomaticSourceRecordCommandConverter;
import com.zto.devops.qc.client.enums.rpc.MemberTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordAnalyticMethodEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.rpc.pipeline.AutomaticTaskCDExecutedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.command.*;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.*;
import com.zto.devops.qc.client.service.testmanager.cases.IAutomaticSourceRecordService;
import com.zto.devops.qc.client.service.testmanager.cases.model.*;
import com.zto.devops.qc.domain.service.AutomaticSourceRecordCommandDomainService;
import com.zto.devops.qc.domain.service.AutomaticSourceRecordQueryDomainService;
import com.zto.devops.qc.domain.service.AutomaticTaskCommandDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpParam;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayWebAuth;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@Service
@ZsmpService(name = "自动化登记库接口")
public class AutomaticSourceRecordServiceImpl extends GatewayBase implements IAutomaticSourceRecordService {

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private AutomaticSourceRecordCommandConverter automaticSourceRecordCommandConverter;

    @Autowired
    private AutomaticSourceRecordQueryDomainService automaticSourceRecordQueryDomainService;

    @Autowired
    private AutomaticSourceRecordCommandDomainService automaticSourceRecordCommandDomainService;

    @Autowired
    private AsyncImpExpService asyncImpExpService;

    @Autowired
    private AutomaticTaskCommandDomainService automaticTaskCommandDomainService;

    @ZApiOperation(description = "新增登记库", apiName = "tm/automatic/addAutomaticRecord", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<String> addAutomaticRecord(AddAutomaticRecordReq req) {
        if (StringUtil.isNotBlank(req.getName()) && req.getName().length() > 60) {
            throw new ServiceException("标题不能超过60字！");
        }
        if (StringUtil.isNotBlank(req.getFileName()) && req.getFileName().length() > 100) {
            throw new ServiceException("文件名过长，请修改名称后重试！");
        }
        this.checkName(req.getName(), req.getProductCode(), null);
        String aggregateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddAutomaticRecordCommand command = automaticSourceRecordCommandConverter.convert(req, aggregateId);
        GatewayContext.fillCurrentUser(command);
        automaticSourceRecordCommandDomainService.addAutomaticRecordCommand(command);
        automaticSourceRecordCommandDomainService.sendCaseCommand(command);
        return StatusCode.OK.build(aggregateId);
    }

    private void checkName(String name, String productCode, String code) {
        AutomaticRecordByNameQuery query = new AutomaticRecordByNameQuery();
        query.setName(name);
        query.setProductCode(productCode);
        List<AutomaticRecordVO> automaticRecordVOList =
                automaticSourceRecordQueryDomainService.automaticRecordByNameQuery(query);
        if (CollectionUtil.isNotEmpty(automaticRecordVOList)) {
            if (StringUtil.isEmpty(code)) {
                throw new ServiceException("登记库标题不能重复,请重新输入！");
            } else if (automaticRecordVOList.stream().anyMatch(vo -> !vo.getCode().equals(code))) {
                throw new ServiceException("登记库标题不能重复,请重新输入！");
            }
        }
    }

    @ZApiOperation(description = "修改登记库", apiName = "tm/automatic/editAutomaticRecord", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editAutomaticRecord(EditAutomaticRecordReq req) {
        if (StringUtil.isNotBlank(req.getName()) && req.getName().length() > 60) {
            throw new ServiceException("标题不能超过60字！");
        }
        if (StringUtil.isNotBlank(req.getFileName()) && req.getFileName().length() > 100) {
            throw new ServiceException("文件名过长，请修改名称后重试！");
        }
        this.checkName(req.getName(), req.getProductCode(), req.getCode());
        EditAutomaticRecordCommand command = automaticSourceRecordCommandConverter.convert(req);
        GatewayContext.fillCurrentUser(command);
        command.setFileName(null);
        automaticSourceRecordCommandDomainService.editAutomaticRecordCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "修改登记库责任人",
            apiName = "tm/automatic/editAutomaticPersonLiable", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editAutomaticPersonLiable(EditAutomaticPersonLiableReq req) {
        AutomaticRecordQuery query = new AutomaticRecordQuery();
        query.setCode(req.getCode());
        GatewayContext.fillCurrentUser(query);
        AutomaticRecordVO automaticRecordVO = automaticSourceRecordQueryDomainService.automaticRecordQuery(query);
        if (Objects.nonNull(automaticRecordVO)) {
            if (!query.getTransactor().getUserId().equals(automaticRecordVO.getCreatorId())
                    && !query.getTransactor().getUserId().equals(automaticRecordVO.getPersonLiableId())
                    && !query.getTransactor().getPermissions().contains(MemberTypeEnum.SUPPER.name())) {
                throw new ServiceException("没有权限操作！");
            }
        }
        EditAutomaticPersonLiableCommand command = automaticSourceRecordCommandConverter.convertor(req);
        GatewayContext.fillCurrentUser(command);
        automaticSourceRecordCommandDomainService.editAutomaticPersonLiable(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "解析登记库", apiName = "tm/automatic/startAutomaticRecord", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<String> startAutomaticRecord(String code) {
        AutomaticRecordQuery query = new AutomaticRecordQuery();
        query.setCode(code);
        GatewayContext.fillCurrentUser(query);
        AutomaticRecordVO automaticRecordVO = automaticSourceRecordQueryDomainService.automaticRecordQuery(query);
        if (null == automaticRecordVO) {
            throw new ServiceException("参数异常！");
        }
        EditAutomaticRecordCommand command = automaticSourceRecordCommandConverter.convert(automaticRecordVO);
        command.setAggregateId(code);
        GatewayContext.fillCurrentUser(command);
        command.setStatus(AutomaticStatusEnum.IN_PROGRESS);
        automaticSourceRecordCommandDomainService.editAutomaticRecordCommand(command);
        String logCode = aggregateIdGenerateService.generateId(AggregateType.TEST_CASE);
        AddAutomaticRecordLogCommand addAutomaticRecordLogCommand =
                automaticSourceRecordCommandConverter.convert(command);
        addAutomaticRecordLogCommand.setAggregateId(command.getAggregateId());
        addAutomaticRecordLogCommand.setTransactor(command.getTransactor());
        addAutomaticRecordLogCommand.setProductCode(command.getProductCode());
        addAutomaticRecordLogCommand.setFileName(command.getFileName());
        addAutomaticRecordLogCommand.setAutomaticSourceCode(logCode);
        addAutomaticRecordLogCommand.setStatus(AutomaticStatusEnum.IN_PROGRESS.name());
        addAutomaticRecordLogCommand.setAnalyticMethod(AutomaticRecordAnalyticMethodEnum.MANUAL);
        automaticSourceRecordCommandDomainService.addAutomaticRecordLogCommand(addAutomaticRecordLogCommand);
        automaticSourceRecordCommandDomainService.sendEditCaseCommand(command, command.getFileName(), logCode);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "修改时解析脚本", apiName = "tm/automatic/analysisAutomaticRecord", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> analysisAutomaticRecord(EditAutomaticRecordReq req) {
        if (StringUtil.isBlank(req.getFileName())) {
            throw new ServiceException("文件名不能为空！");
        }
        if (req.getFileName().length() > 100) {
            throw new ServiceException("文件名过长，请修改名称后重试！");
        }
        AutomaticRecordLogQuery query = new AutomaticRecordLogQuery();
        query.setCode(req.getCode());
        query.setList(Arrays.asList(AutomaticStatusEnum.ANALYSIS.name(),
                AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name()));
        List<AutomaticRecordLogVO> voList = automaticSourceRecordQueryDomainService.automaticRecordLogQuery(query);
        if (CollectionUtil.isNotEmpty(voList)) {
            throw new ServiceException("请等待执行成功或操作已完成解析未确认的解析记录后再操作！");
        }
        EditAutomaticRecordCommand command = automaticSourceRecordCommandConverter.convert(req);
        GatewayContext.fillCurrentUser(command);
        String name = "";
        if (!req.getBo()) {
            name = FileUtil.getPrefix(req.getFileName()) + "_temp." + FileUtil.getSuffix(req.getFileName());
        } else {
            name = req.getFileName();
        }
        String logCode = aggregateIdGenerateService.generateId(AggregateType.TEST_CASE);
        AddAutomaticRecordLogCommand addAutomaticRecordLogCommand =
                automaticSourceRecordCommandConverter.convert(command);
        addAutomaticRecordLogCommand.setAggregateId(command.getAggregateId());
        addAutomaticRecordLogCommand.setTransactor(command.getTransactor());
        addAutomaticRecordLogCommand.setProductCode(command.getProductCode());
        addAutomaticRecordLogCommand.setFileName(name);
        addAutomaticRecordLogCommand.setAutomaticSourceCode(logCode);
        addAutomaticRecordLogCommand.setStatus(AutomaticStatusEnum.IN_PROGRESS.name());
        addAutomaticRecordLogCommand.setAnalyticMethod(AutomaticRecordAnalyticMethodEnum.MANUAL);
        automaticSourceRecordCommandDomainService.addAutomaticRecordLogCommand(addAutomaticRecordLogCommand);
        automaticSourceRecordCommandDomainService.sendEditCaseCommand(command, name, logCode);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "确认提交预览", apiName = "tm/automatic/submitAnalysisAutomaticRecord", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> submitAnalysisAutomaticRecord(SubmitAnalysisAutomaticReq req) {
        if (StringUtil.isBlank(req.getCode())) {
            throw new ServiceException("code不能为空！");
        }
        //查询log状态  如果不是终态 不可以修改
        AutomaticRecordLogByCodeQuery query = new AutomaticRecordLogByCodeQuery();
        query.setCode(req.getAutomaticSourceLogCode());
        AutomaticRecordLogVO automaticRecordLogVO =
                automaticSourceRecordQueryDomainService.automaticRecordLogByCodeQuery(query);
        if (!automaticRecordLogVO.getStatus().equals(AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name()) &&
                !automaticRecordLogVO.getStatus().equals(AutomaticStatusEnum.FAIL.name())) {
            throw new ServiceException("预览确认中,请刷新后再试！");
        }
        automaticSourceRecordCommandDomainService.editAutomaticLogStatusCommand(req.getAutomaticSourceLogCode());
        SubmitAnalysisAutomaticCommand command = new SubmitAnalysisAutomaticCommand(req.getCode());
        command.setAutomaticSourceLogCode(req.getAutomaticSourceLogCode());
        command.setCode(req.getCode());
        command.setAnalyticMethod(AutomaticRecordAnalyticMethodEnum.MANUAL);
        GatewayContext.fillCurrentUser(command);
        automaticSourceRecordCommandDomainService.submitAnalysisAutomaticRecord(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "取消提交预览",
            apiName = "tm/automatic/cancelAnalysisAutomaticRecord", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> cancelAnalysisAutomaticRecord(SubmitAnalysisAutomaticReq req) {
        if (StringUtil.isBlank(req.getCode())) {
            throw new ServiceException("code不能为空！");
        }
        //查询log状态  如果不是终态 不可以修改
        AutomaticRecordLogByCodeQuery query = new AutomaticRecordLogByCodeQuery();
        query.setCode(req.getAutomaticSourceLogCode());
        AutomaticRecordLogVO automaticRecordLogVO =
                automaticSourceRecordQueryDomainService.automaticRecordLogByCodeQuery(query);
        if (!automaticRecordLogVO.getStatus().equals(AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name()) &&
                !automaticRecordLogVO.getStatus().equals(AutomaticStatusEnum.FAIL.name())) {
            throw new ServiceException("预览确认中,请刷新后再试！");
        }
        CancelAnalysisAutomaticCommand command = new CancelAnalysisAutomaticCommand(req.getCode());
        command.setAutomaticSourceLogCode(req.getAutomaticSourceLogCode());
        command.setCode(req.getCode());
        command.setProductCode(req.getProductCode());
        GatewayContext.fillCurrentUser(command);
        automaticSourceRecordCommandDomainService.cancelAnalysisAutomaticRecord(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "删除登记库",
            apiName = "tm/automatic/deleteAutomaticRecord", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> deleteAutomaticRecord(AutomaticRecordSimpleReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCode(), new NotBlankValidator("登记库code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        DeleteAutomaticRecordCommand command = new DeleteAutomaticRecordCommand(req.getCode());
        GatewayContext.fillCurrentUser(command);
        command.setCode(req.getCode());
        automaticSourceRecordCommandDomainService.deleteAutomaticRecord(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "新增PyTest登记库", apiName = "tm/automatic/addPyTest", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addPyTest(AddAutomaticRecordReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getName(), new LengthValidator(1, 60, "标题"))
                .on(req.getGitAddress(), new NotBlankValidator("Git地址"))
                .on(req.getBranch(), new NotBlankValidator("分支名"))
                .on(req.getComment(), new LengthValidator(1, 200, "描述"))
                .when(StringUtil.isNotBlank(req.getComment()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        this.checkName(req.getName(), req.getProductCode(), null);
        String aggregateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddAutomaticRecordCommand command = automaticSourceRecordCommandConverter.convert(req, aggregateId);
        GatewayContext.fillCurrentUser(command);
        command.setAddress(req.getGitAddress());
        command.setType(AutomaticRecordTypeEnum.PYTEST);
        automaticSourceRecordCommandDomainService.addAutomaticRecordCommand(command);
        AnalysisAutomaticRecordVO vo = new AnalysisAutomaticRecordVO();
        vo.setGitUrl(command.getAddress());
        vo.setBranchName(command.getBranch());
        vo.setWorkDir(command.getWorkSpace());
        vo.setProductCode(command.getProductCode());
        vo.setCode(command.getAggregateId());
        vo.setTestcaseCode(command.getTestcaseCode());
        vo.setPath(command.getPath());
        vo.setInsert(true);
        vo.setTransactor(command.getTransactor());
        automaticSourceRecordCommandDomainService.submitAutomaticRecordAnalysis(vo, AutomaticRecordTypeEnum.PYTEST);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "修改PyTest登记库", apiName = "tm/automatic/editPyTest", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editPyTest(EditAutomaticRecordReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCode(), new NotBlankValidator("登记库唯一标识"))
                .on(req.getName(), new LengthValidator(1, 60, "标题"))
                .on(req.getGitAddress(), new NotBlankValidator("Git地址"))
                .on(req.getBranch(), new NotBlankValidator("分支名"))
                .on(req.getComment(), new LengthValidator(1, 200, "描述"))
                .when(StringUtil.isNotBlank(req.getComment()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        this.checkName(req.getName(), req.getProductCode(), req.getCode());
        EditAutomaticRecordCommand command = automaticSourceRecordCommandConverter.convert(req);
        GatewayContext.fillCurrentUser(command);
        command.setAddress(req.getGitAddress());
        automaticSourceRecordCommandDomainService.editAutomaticRecordCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "重新解析PyTest登记库", apiName = "tm/automatic/analysisPyTest", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> analysisPyTest(
            @ZsmpParam(description = "登记库唯一标识", sample = "SNF994641594810368000", required = true) String code) {
        if (StringUtil.isBlank(code)) {
            throw new ServiceException("登记库唯一标识不能为空！");
        }
        AutomaticRecordQuery automaticRecordQuery = new AutomaticRecordQuery();
        automaticRecordQuery.setCode(code);
        GatewayContext.fillCurrentUser(automaticRecordQuery);
        AutomaticRecordVO automaticRecordVO =
                automaticSourceRecordQueryDomainService.automaticRecordQuery(automaticRecordQuery);
        if (null == automaticRecordVO) {
            throw new ServiceException("数据为空,请重新操作！");
        }
        if (AutomaticStatusEnum.IN_PROGRESS.equals(automaticRecordVO.getStatus())) {
            throw new ServiceException("请等待解析完成后在重新解析！");
        }
        AutomaticRecordLogQuery automaticRecordLogQuery = new AutomaticRecordLogQuery();
        automaticRecordLogQuery.setCode(code);
        List<AutomaticRecordLogVO> automaticRecordLogVOList =
                automaticSourceRecordQueryDomainService.automaticRecordLogQuery(automaticRecordLogQuery);
        List<String> checkStatus = Arrays.asList(AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name(),
                AutomaticStatusEnum.IN_PROGRESS.name(), AutomaticStatusEnum.ANALYSIS.name());
        if (CollectionUtils.isNotEmpty(automaticRecordLogVOList)
                && checkStatus.contains(automaticRecordLogVOList.get(0).getStatus())) {
            throw new ServiceException("请操作已完成解析未确认的解析记录后再操作！");
        }
        String aggregateId = AggregateIdUtil.generateId(AggregateType.SNOWFLAKE);
        AddAutomaticRecordLogCommand command = new AddAutomaticRecordLogCommand(code);
        GatewayContext.fillCurrentUser(command);
        command.setProductCode(automaticRecordVO.getProductCode());
        command.setAutomaticSourceCode(aggregateId);
        command.setAddress(automaticRecordVO.getAddress());
        command.setStatus(AutomaticStatusEnum.IN_PROGRESS.name());
        command.setAnalyticMethod(AutomaticRecordAnalyticMethodEnum.MANUAL);
        automaticSourceRecordCommandDomainService.addAutomaticRecordLogCommand(command);
        AnalysisAutomaticRecordVO vo = new AnalysisAutomaticRecordVO();
        vo.setGitUrl(automaticRecordVO.getAddress());
        vo.setBranchName(automaticRecordVO.getBranch());
        vo.setWorkDir(automaticRecordVO.getWorkSpace());
        vo.setProductCode(automaticRecordVO.getProductCode());
        vo.setCode(automaticRecordVO.getCode());
        vo.setTestcaseCode(automaticRecordVO.getTestcaseCode());
        vo.setLogCode(aggregateId);
        vo.setInsert(false);
        vo.setTransactor(command.getTransactor());
        if (null != automaticRecordVO.getTestcaseVO()) {
            vo.setPath(automaticRecordVO.getTestcaseVO().getPath());
        }
        automaticSourceRecordCommandDomainService.submitAutomaticRecordAnalysis(vo, AutomaticRecordTypeEnum.PYTEST);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "解析PyTest结果回调", apiName = "tm/automatic/analysisPyTestCallback", namespace = "luban")
    @Override
    public Result<Void> analysisPyTestCallback(AnalysisAutomaticRecordCallbackReq req) {
        if (AutomaticStatusEnum.IN_PROGRESS.equals(req.getStatus())) {
            return StatusCode.OK.build();
        }
        AnalysisAutomaticRecordVO vo = Optional.ofNullable(req.getExt()).orElse(new AnalysisAutomaticRecordVO());
        vo.setCode(req.getCode());
        vo.setStatus(req.getStatus());
        vo.setFilename(req.getFilename());
        vo.setCommitId(req.getCommitid());
        vo.setOssPath(req.getOssPath());
        automaticSourceRecordCommandDomainService.analysisAutomaticRecord(vo);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "新增testng登记库", apiName = "tm/automatic/testNgAutomaticInsert", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> testNgAutomaticInsert(AddAutomaticRecordReq req) {
        if (StringUtil.isNotEmpty(req.getName()) && req.getName().length() > 60) {
            throw new ServiceException("标题不能超过60字！");
        }
        if (StringUtil.isEmpty(req.getGitAddress())) {
            throw new ServiceException("git地址不能为空！");
        }
        if (StringUtil.isEmpty(req.getBranch())) {
            throw new ServiceException("分支名不能为空！");
        }
        if (req.getGitAddress().length() > 200) {
            throw new ServiceException("git地址不能超过200字！");
        }
        if (req.getBranch().length() > 100) {
            throw new ServiceException("分支名不能超过100字！");
        }
        if (StringUtil.isNotBlank(req.getWorkSpace()) && req.getWorkSpace().length() > 200) {
            throw new ServiceException("工作目录不能超过200字！");
        }
        if (StringUtil.isNotBlank(req.getScanDirectory()) && req.getScanDirectory().length() > 200) {
            throw new ServiceException("扫描路径不能超过200字！");
        }
        if (StringUtil.isNotBlank(req.getComment()) && req.getComment().length() > 200) {
            throw new ServiceException("描述不能超过200字！");
        }
        this.checkName(req.getName(), req.getProductCode(), null);
        String aggregateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddAutomaticRecordCommand command = automaticSourceRecordCommandConverter.convert(req, aggregateId);
        GatewayContext.fillCurrentUser(command);
        command.setAddress(req.getGitAddress());
        automaticSourceRecordCommandDomainService.addAutomaticRecordCommand(command);
        AnalysisAutomaticRecordVO vo = new AnalysisAutomaticRecordVO();
        vo.setGitUrl(command.getAddress());
        vo.setBranchName(command.getBranch());
        vo.setWorkDir(command.getWorkSpace());
        vo.setProductCode(command.getProductCode());
        vo.setCode(command.getAggregateId());
        vo.setTestcaseCode(command.getTestcaseCode());
        vo.setPath(command.getPath());
        vo.setInsert(true);
        vo.setTransactor(command.getTransactor());
        automaticSourceRecordCommandDomainService.submitAutomaticRecordAnalysis(vo, AutomaticRecordTypeEnum.TESTNG);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "修改testng登记库", apiName = "tm/automatic/testNgAutomaticEdit", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> testNgAutomaticEdit(EditAutomaticRecordReq req) {
        if (StringUtil.isNotEmpty(req.getName()) && req.getName().length() > 60) {
            throw new ServiceException("标题不能超过60字！");
        }
        if (StringUtil.isEmpty(req.getGitAddress())) {
            throw new ServiceException("git地址不能为空！");
        }
        if (StringUtil.isEmpty(req.getBranch())) {
            throw new ServiceException("分支名不能为空！");
        }
        if (req.getGitAddress().length() > 200) {
            throw new ServiceException("git地址不能超过200字！");
        }
        if (req.getBranch().length() > 100) {
            throw new ServiceException("分支名不能超过100字！");
        }

        if (StringUtil.isNotBlank(req.getWorkSpace()) && req.getWorkSpace().length() > 200) {
            throw new ServiceException("工作目录不能超过200字！");
        }
        if (StringUtil.isNotBlank(req.getScanDirectory()) && req.getScanDirectory().length() > 200) {
            throw new ServiceException("扫描路径不能超过200字！");
        }

        if (StringUtil.isNotBlank(req.getComment()) && req.getComment().length() > 200) {
            throw new ServiceException("描述不能超过200字！");
        }
        this.checkName(req.getName(), req.getProductCode(), req.getCode());
        EditAutomaticRecordCommand command = automaticSourceRecordCommandConverter.convert(req);
        GatewayContext.fillCurrentUser(command);
        command.setAddress(req.getGitAddress());
        automaticSourceRecordCommandDomainService.editAutomaticRecordCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "重新解析testng", apiName = "tm/automatic/analysisAutomaticTestng", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> analysisAutomaticTestng(String code, String path) {
        if (StringUtil.isBlank(code)) {
            throw new ServiceException("唯一标识不能为空！");
        }
        AutomaticRecordQuery query = new AutomaticRecordQuery();
        GatewayContext.fillCurrentUser(query);
        query.setCode(code);
        AutomaticRecordVO automaticRecordVO = automaticSourceRecordQueryDomainService.automaticRecordQuery(query);
        if (Objects.isNull(automaticRecordVO)) {
            throw new ServiceException("数据为空,请重新操作！");
        }
        if (automaticRecordVO.getStatus().name().equals(AutomaticStatusEnum.IN_PROGRESS.name())) {
            throw new ServiceException("请等待解析完成后在重新解析！");
        }

        AutomaticRecordLogQuery automaticRecordLogQuery = new AutomaticRecordLogQuery();
        automaticRecordLogQuery.setCode(code);
        List<AutomaticRecordLogVO> voList =
                automaticSourceRecordQueryDomainService.automaticRecordLogQuery(automaticRecordLogQuery);
        if (CollectionUtil.isNotEmpty(voList)
                && (voList.get(0).getStatus().equals(AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name())
                || voList.get(0).getStatus().equals(AutomaticStatusEnum.IN_PROGRESS.name())
                || voList.get(0).getStatus().equals(AutomaticStatusEnum.ANALYSIS.name()))) {
            throw new ServiceException("请操作已完成解析未确认的解析记录后再操作！");
        }
        EditAutomaticRecordCommand command = new EditAutomaticRecordCommand(code);
        GatewayContext.fillCurrentUser(command);
        String logCode = aggregateIdGenerateService.generateId(AggregateType.TEST_CASE);
        AddAutomaticRecordLogCommand addAutomaticRecordLogCommand =
                automaticSourceRecordCommandConverter.convert(command);
        addAutomaticRecordLogCommand.setAggregateId(command.getAggregateId());
        addAutomaticRecordLogCommand.setTransactor(command.getTransactor());
        addAutomaticRecordLogCommand.setProductCode(automaticRecordVO.getProductCode());
        addAutomaticRecordLogCommand.setAutomaticSourceCode(logCode);
        addAutomaticRecordLogCommand.setAddress(automaticRecordVO.getAddress());
        addAutomaticRecordLogCommand.setStatus(AutomaticStatusEnum.IN_PROGRESS.name());
        addAutomaticRecordLogCommand.setAnalyticMethod(AutomaticRecordAnalyticMethodEnum.MANUAL);
        automaticSourceRecordCommandDomainService.addAutomaticRecordLogCommand(addAutomaticRecordLogCommand);
        AnalysisAutomaticRecordVO vo = new AnalysisAutomaticRecordVO();
        vo.setGitUrl(automaticRecordVO.getAddress());
        vo.setBranchName(automaticRecordVO.getBranch());
        vo.setWorkDir(automaticRecordVO.getWorkSpace());
        vo.setProductCode(automaticRecordVO.getProductCode());
        vo.setCode(automaticRecordVO.getCode());
        vo.setTestcaseCode(automaticRecordVO.getTestcaseCode());
        vo.setPath(path);
        vo.setLogCode(logCode);
        vo.setInsert(false);
        vo.setTransactor(command.getTransactor());
        automaticSourceRecordCommandDomainService.submitAutomaticRecordAnalysis(vo, AutomaticRecordTypeEnum.TESTNG);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "解析TestNg结果回调", apiName = "tm/automatic/testNgAnalysisCallback", namespace = "luban")
    @Override
    public Result<Void> testNgAnalysisCallback(TestNgAnalysisCallbackReq req) {
        if (AutomaticStatusEnum.IN_PROGRESS.equals(req.getStatus())) {
            return StatusCode.OK.build();
        }
        AnalysisAutomaticRecordVO vo = Optional.ofNullable(req.getExt()).orElse(new AnalysisAutomaticRecordVO());
        vo.setCode(req.getCode());
        vo.setStatus(req.getStatus());
        vo.setFilename(req.getFilename());
        vo.setCommitId(req.getCommitid());
        vo.setOssPath(req.getOssPath());
        automaticSourceRecordCommandDomainService.analysisAutomaticRecord(vo);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(description = "查询目录下所有登记库和用例数量",
            gatewayApi = @GatewayApi(name = "tm/automatic/countAutomaticAndTestcase",
                    description = "查询目录下所有登记库和用例数量",
                    tags = "3.20.0",
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<AutomaticAndTestcaseNoVO> countAutomaticAndTestcase(String code, TestcaseTypeEnum type, String productCode) {
        AutomaticAndTestcaseNoQuery query = new AutomaticAndTestcaseNoQuery();
        query.setCode(code);
        query.setType(type);
        query.setProductCode(productCode);
        AutomaticAndTestcaseNoVO automaticAndTestcaseNoVO = automaticSourceRecordQueryDomainService.countAutomaticAndTestcase(query);
        return StatusCode.OK.build(automaticAndTestcaseNoVO);
    }

    @ZApiOperation(description = "查询登记库详情",
            apiName = "tm/automatic/queryAutomaticRecord", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<AutomaticRecordResp> queryAutomaticRecord(AutomaticRecordSimpleReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCode(), new NotBlankValidator("登记库code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        AutomaticRecordQuery query = new AutomaticRecordQuery();
        GatewayContext.fillCurrentUser(query);
        query.setCode(req.getCode());
        AutomaticRecordVO automaticRecordVO = automaticSourceRecordQueryDomainService.automaticRecordQuery(query);
        List<AutomaticRecordLogVO> voList = new ArrayList<>();
        String tempFileName = "";
        String tempStatus = "";
        if (Objects.nonNull(automaticRecordVO)) {
            if (automaticRecordVO.getType().name().equals(AutomaticRecordTypeEnum.JMETER.name())) {
                automaticSourceRecordCommandDomainService.getFileName(automaticRecordVO);
            }
            if (automaticRecordVO.getType().name().equals(AutomaticRecordTypeEnum.JMETER_GIT.name())) {
                automaticRecordVO.setDataDir(automaticRecordVO.getDataFileAddress());
                automaticRecordVO.setLibDir(automaticRecordVO.getExtendJarAddress());
            }
            AutomaticRecordLogQuery automaticRecordLogQuery = new AutomaticRecordLogQuery();
            automaticRecordLogQuery.setCode(automaticRecordVO.getCode());
            voList = automaticSourceRecordQueryDomainService.automaticRecordLogQuery(automaticRecordLogQuery);
            if (CollectionUtil.isNotEmpty(voList) && voList.get(0).getEnable()) {
                tempStatus = voList.stream()
                        .filter(logVo -> !AutomaticStatusEnum.ANALYSISSUCCESSABANDONED.name().equals(logVo.getStatus()))
                        .findFirst().map(AutomaticRecordLogVO::getStatus).orElse("");
                if (!voList.get(0).getFileName().equals(automaticRecordVO.getFileName())
                        && Arrays.asList(AutomaticStatusEnum.IN_PROGRESS.name(), AutomaticStatusEnum.FAIL.name(),
                        AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name()).contains(voList.get(0).getStatus())) {
                    tempFileName = voList.get(0).getFileName();
                }
            }
            automaticRecordVO.setLogList(voList);
        }
        AutomaticRecordResp resp = automaticSourceRecordCommandConverter.convertor(automaticRecordVO);
        resp.setTempFileName(tempFileName);
        if (!"".equals(tempStatus)) {
            resp.setStatus(AutomaticStatusEnum.getEnumByName(tempStatus));
        }
        resp.setTempStatus(tempStatus);
        Button vo = this.getButton(automaticRecordVO, voList);
        resp.setGitAddress(automaticRecordVO.getAddress());
        if (null != vo) {
            resp.getButtons().add(vo);
        }
        return StatusCode.OK.build(resp);
    }

    private Button getButton(AutomaticRecordVO automaticRecordVO, List<AutomaticRecordLogVO> voList) {
        if (CollectionUtil.isEmpty(voList) && !AutomaticStatusEnum.IN_PROGRESS.equals(automaticRecordVO.getStatus())) {
            return new Button("重新解析", "analysisAutomaticRecord", 1);
        }
        if (CollectionUtil.isNotEmpty(voList)) {
            if (voList.get(0).getStatus().equals(AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name())) {
                return new Button("去确认", "toConfirm", 1);
            }
            if (!voList.get(0).getStatus().equals(AutomaticStatusEnum.IN_PROGRESS.name())
                    && !voList.get(0).getStatus().equals(AutomaticStatusEnum.ANALYSIS.name())) {
                return new Button("重新解析", "analysisAutomaticRecord", 1);
            }
        }
        return null;
    }

    @ZsmpApiOperation(description = "查询登记库修改历史详情",
            gatewayApi = @GatewayApi(name = "tm/automatic/queryAutomaticRecordLogCase",
                    description = "查询登记库修改历史详情",
                    tags = "3.20.0",
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<AutomaticSourceLogTestcaseListVO> queryAutomaticRecordLogCase(String code) {
        if (StringUtil.isEmpty(code)) {
            throw new ServiceException("参数异常！");
        }
        AutomaticSourceLogTestcaseQuery query = new AutomaticSourceLogTestcaseQuery();
        query.setCode(code);
        AutomaticSourceLogTestcaseListVO voList = automaticSourceRecordQueryDomainService.automaticSourceLogTestcaseQuery(query);
        voList.setAutomaticSourceLogCode(code);
        return StatusCode.OK.build(voList);
    }

    @ZsmpApiOperation(description = "检验文件大小",
            gatewayApi = @GatewayApi(name = "tm/automatic/checkFileSize",
                    description = "检验文件大小",
                    tags = "3.20.0",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Boolean> checkFileSize(@ZsmpParam(description = "日志文件url", sample = "", required = true) String url) {
        if (StringUtil.isBlank(url)) {
            throw new ServiceException("日志文件url不能为空！");
        }
        boolean bo = automaticSourceRecordCommandDomainService.checkFileSize(url);
        return StatusCode.OK.build(bo);
    }

    @ZsmpApiOperation(description = "删除临时文件",
            gatewayApi = @GatewayApi(name = "tm/automatic/deleteTempFileName",
                    description = "删除临时文件",
                    tags = "3.20.0",
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
                    namespace = "luban"))
    @Override
    public Result<Void> deleteTempFileName(String code) {
        AutomaticRecordLogQuery query = new AutomaticRecordLogQuery();
        query.setCode(code);
        List<AutomaticRecordLogVO> voList = automaticSourceRecordQueryDomainService.automaticRecordLogQuery(query);
        if (CollectionUtil.isNotEmpty(voList)) {
            if (voList.get(0).getStatus().equals(AutomaticStatusEnum.FAIL.name())) {
                DeleteTempFileNameCommand command = new DeleteTempFileNameCommand(code);
                command.setLogCode(voList.get(0).getCode());
                command.setCode(code);
                GatewayContext.fillCurrentUser(command);
                automaticSourceRecordCommandDomainService.deleteTempFileName(command);
            }
        }
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "下载文件",
            apiName = "tm/automatic/downloadAutomatic", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<String> downloadAutomatic(AutomaticRecordSimpleReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCode(), new NotBlankValidator("登记库code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        AutomaticRecordQuery query = new AutomaticRecordQuery();
        query.setCode(req.getCode());
        GatewayContext.fillCurrentUser(query);
        AutomaticRecordVO automaticRecordVO = automaticSourceRecordQueryDomainService.automaticRecordQuery(query);
        String generateId = aggregateIdGenerateService.generateId("TEST_CASE");
        AddExpCommand command = new AddExpCommand(generateId);
        command.setType("EXP_AUTOMATIC_RECORD");
        command.setFileName(automaticRecordVO.getName());
        command.setManualAction(true);
        GatewayContext.fillCurrentUser(command);
        asyncImpExpService.addExpJob(command);
        automaticSourceRecordCommandDomainService.downloadAutomaticNew(automaticRecordVO, generateId, command.getTransactor());
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "新增GitJmeter登记库", apiName = "tm/automatic/addGitJmeter", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addGitJmeter(AddAutomaticRecordReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getName(), new LengthValidator(1, 60, "标题"))
                .on(req.getGitAddress(), new NotBlankValidator("Git地址"))
                .on(req.getBranch(), new NotBlankValidator("分支名"))
                .on(req.getComment(), new LengthValidator(1, 200, "描述"))
                .when(StringUtil.isNotBlank(req.getComment()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        this.checkName(req.getName(), req.getProductCode(), null);
        String aggregateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddAutomaticRecordCommand command = automaticSourceRecordCommandConverter.convert(req, aggregateId);
        GatewayContext.fillCurrentUser(command);
        command.setAddress(req.getGitAddress());
        command.setDataFileAddress(req.getDataDir());
        command.setExtendJarAddress(req.getLibDir());
        command.setType(AutomaticRecordTypeEnum.JMETER_GIT);
        automaticSourceRecordCommandDomainService.addAutomaticRecordCommand(command);
        AnalysisAutomaticRecordVO vo = new AnalysisAutomaticRecordVO();
        vo.setGitUrl(command.getAddress());
        vo.setBranchName(command.getBranch());
        vo.setWorkDir(command.getWorkSpace());
        vo.setProductCode(command.getProductCode());
        vo.setCode(command.getAggregateId());
        vo.setTestcaseCode(command.getTestcaseCode());
        vo.setPath(command.getPath());
        vo.setInsert(true);
        vo.setTransactor(command.getTransactor());
        vo.setDataPath(command.getDataFileAddress());
        vo.setLibPath(command.getExtendJarAddress());
        automaticSourceRecordCommandDomainService.submitAutomaticRecordAnalysis(vo, AutomaticRecordTypeEnum.JMETER_GIT);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "修改GitJmeter登记库", apiName = "tm/automatic/editGitJmeter", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editGitJmeter(EditAutomaticRecordReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCode(), new NotBlankValidator("登记库唯一标识"))
                .on(req.getName(), new LengthValidator(1, 60, "标题"))
                .on(req.getGitAddress(), new NotBlankValidator("Git地址"))
                .on(req.getBranch(), new NotBlankValidator("分支名"))
                .on(req.getComment(), new LengthValidator(1, 200, "描述"))
                .when(StringUtil.isNotBlank(req.getComment()))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        this.checkName(req.getName(), req.getProductCode(), req.getCode());
        EditAutomaticRecordCommand command = automaticSourceRecordCommandConverter.convert(req);
        GatewayContext.fillCurrentUser(command);
        command.setAddress(req.getGitAddress());
        command.setDataFileAddress(req.getDataDir());
        command.setExtendJarAddress(req.getLibDir());
        automaticSourceRecordCommandDomainService.editAutomaticRecordCommand(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "重新解析GitJmeter登记库",
            gatewayApi =
            @GatewayApi(
                    namespace = "luban",
                    name = "tm/automatic/analysisGitJmeter",
                    description = "重新解析GitJmeter登记库",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true)))
    @Override
    public Result<Void> analysisGitJmeter(
            @ZsmpParam(description = "登记库唯一标识", sample = "", required = true) String code) {
        if (StringUtil.isBlank(code)) {
            throw new ServiceException("登记库唯一标识不能为空！");
        }
        AutomaticRecordQuery automaticRecordQuery = new AutomaticRecordQuery();
        automaticRecordQuery.setCode(code);
        GatewayContext.fillCurrentUser(automaticRecordQuery);
        AutomaticRecordVO automaticRecordVO =
                automaticSourceRecordQueryDomainService.automaticRecordQuery(automaticRecordQuery);
        if (null == automaticRecordVO) {
            throw new ServiceException("数据为空,请重新操作！");
        }
        if (AutomaticStatusEnum.IN_PROGRESS.equals(automaticRecordVO.getStatus())) {
            throw new ServiceException("请等待解析完成后在重新解析！");
        }
        AutomaticRecordLogQuery automaticRecordLogQuery = new AutomaticRecordLogQuery();
        automaticRecordLogQuery.setCode(code);
        List<AutomaticRecordLogVO> automaticRecordLogVOList =
                automaticSourceRecordQueryDomainService.automaticRecordLogQuery(automaticRecordLogQuery);
        List<String> checkStatus = Arrays.asList(AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name(),
                AutomaticStatusEnum.IN_PROGRESS.name(), AutomaticStatusEnum.ANALYSIS.name());
        if (CollectionUtils.isNotEmpty(automaticRecordLogVOList)
                && checkStatus.contains(automaticRecordLogVOList.get(0).getStatus())) {
            throw new ServiceException("请操作已完成解析未确认的解析记录后再操作！");
        }
        String aggregateId = AggregateIdUtil.generateId(AggregateType.SNOWFLAKE);
        AddAutomaticRecordLogCommand command = new AddAutomaticRecordLogCommand(code);
        GatewayContext.fillCurrentUser(command);
        command.setProductCode(automaticRecordVO.getProductCode());
        command.setAutomaticSourceCode(aggregateId);
        command.setAddress(automaticRecordVO.getAddress());
        command.setStatus(AutomaticStatusEnum.IN_PROGRESS.name());
        command.setAnalyticMethod(AutomaticRecordAnalyticMethodEnum.MANUAL);
        automaticSourceRecordCommandDomainService.addAutomaticRecordLogCommand(command);
        AnalysisAutomaticRecordVO vo = new AnalysisAutomaticRecordVO();
        vo.setGitUrl(automaticRecordVO.getAddress());
        vo.setBranchName(automaticRecordVO.getBranch());
        vo.setWorkDir(automaticRecordVO.getWorkSpace());
        vo.setProductCode(automaticRecordVO.getProductCode());
        vo.setCode(automaticRecordVO.getCode());
        vo.setTestcaseCode(automaticRecordVO.getTestcaseCode());
        vo.setLogCode(aggregateId);
        vo.setInsert(false);
        vo.setTransactor(command.getTransactor());
        if (null != automaticRecordVO.getTestcaseVO()) {
            vo.setPath(automaticRecordVO.getTestcaseVO().getPath());
        }
        vo.setDataPath(automaticRecordVO.getDataFileAddress());
        vo.setLibPath(automaticRecordVO.getExtendJarAddress());
        automaticSourceRecordCommandDomainService.submitAutomaticRecordAnalysis(vo, AutomaticRecordTypeEnum.JMETER_GIT);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "解析GitJmeter结果回调", apiName = "tm/automatic/analysisGitJmeterCallback", namespace = "luban")
    @Override
    public Result<Void> analysisGitJmeterCallback(AnalysisAutomaticRecordCallbackReq req) {
        if (AutomaticStatusEnum.IN_PROGRESS.equals(req.getStatus())) {
            return StatusCode.OK.build();
        }
        AnalysisAutomaticRecordVO vo = Optional.ofNullable(req.getExt()).orElse(new AnalysisAutomaticRecordVO());
        vo.setCode(req.getCode());
        vo.setStatus(req.getStatus());
        vo.setFilename(req.getFilename());
        vo.setCommitId(req.getCommitid());
        vo.setOssPath(req.getOssPath());
        automaticSourceRecordCommandDomainService.analysisGitJmeter(vo);
        return StatusCode.OK.build();
    }

    /**
     * pipeline调用
     * @param event
     */
    @Override
    public void handleAutomaticTaskCDExecutedEvent(AutomaticTaskCDExecutedEvent event) {
        automaticTaskCommandDomainService.onAutomaticTaskCDExecutedEvent(event);
    }
}
