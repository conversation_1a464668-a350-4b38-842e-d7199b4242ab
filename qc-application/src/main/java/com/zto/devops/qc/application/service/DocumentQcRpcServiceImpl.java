package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestVariableVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.UserCookieMapVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskInfo;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.AmazonS3ConfigVO;
import com.zto.devops.qc.client.service.testmanager.rpc.DocumentQcRpcService;
import com.zto.devops.qc.client.service.testmanager.rpc.model.UserCookieQueryReq;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.repository.ApiTestRepository;
import com.zto.devops.qc.domain.gateway.runner.QcRunnerGateway;
import com.zto.devops.qc.domain.service.AuthCookieDomainService;
import com.zto.devops.qc.domain.service.LinkMapDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Service
@Slf4j
public class DocumentQcRpcServiceImpl implements DocumentQcRpcService {

    @Autowired
    private AuthCookieDomainService authCookieDomainService;

    @Autowired
    private LinkMapDomainService linkMapDomainService;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    @Autowired
    private ApiTestRepository apiTestRepository;

    @Autowired
    private QcRunnerGateway qcRunnerGateway;

    @Override
    public Result<UserCookieMapVO> queryUserCookie(UserCookieQueryReq req) {
        return StatusCode.OK.build(authCookieDomainService.getUserCookie(req));
    }

    @Override
    public ApiTestVariableVO queryVariableByKey(String variableCode) {
        return apiTestRepository.getApiTestVariableByVariableCode(variableCode);
    }

    @Override
    public Result<Void> debugWithoutNode(DebugTaskInfo debugTaskInfo, User user) {
        linkMapDomainService.debugWithoutNode(debugTaskInfo, user);
        return StatusCode.OK.build();
    }

    @Override
    public AmazonS3ConfigVO getAmazonS3Config() {
        return qcConfigBasicService.getAmazonS3ConfigIntranet();
    }

    @Override
    public JSONObject queryDbConfig(String dbId) {
        return qcRunnerGateway.queryDbConfig(dbId);
    }
}
