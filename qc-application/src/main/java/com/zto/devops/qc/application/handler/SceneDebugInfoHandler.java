package com.zto.devops.qc.application.handler;

import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.common.message.EventHandler;
import com.zto.devops.qc.client.model.dto.TmSceneDebugRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.event.SaveDebugInfoEvent;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.repository.TmSceneDebugRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SceneDebugInfoHandler {

    @Autowired
    private ZtoOssService ztoOssService;

    @Autowired
    private TmSceneDebugRecordRepository tmSceneDebugRecordRepository;

    @EventHandler
    public void handle(SaveDebugInfoEvent event) {
        log.info("收到调试结果, SaveDebugInfoEvent: {}", JSON.toJSONString(event));
        TmSceneDebugRecordEntityDO debugRecord = tmSceneDebugRecordRepository.selectOneByTaskId(event.getTaskId());
        // 正常调试造数不记返回结果
        if(null == debugRecord || StringUtils.isBlank(debugRecord.getRecordCode())) {
            log.info("页面调试造数, 无需记录结果!");
            return;
        }
        //保存结果到日志文件
        String bucket = "autojmx";
        String filePath = "debug/" + event.getTaskId() + "-" + event.getTaskId() + "/debug.json";
        if(StringUtils.isBlank(event.getOutput())) {
            event.setOutput("{}");
        }
        boolean uploadFlag = ztoOssService.createObject(bucket, filePath, event.getOutput());
        if (!uploadFlag) {
            log.info("调试结果上传失败, taskId: {}", event.getTaskId());
            return;
        }
        // 更新数据
        tmSceneDebugRecordRepository.updateOssPathByTaskId(event.getTaskId(), filePath);
    }

}
