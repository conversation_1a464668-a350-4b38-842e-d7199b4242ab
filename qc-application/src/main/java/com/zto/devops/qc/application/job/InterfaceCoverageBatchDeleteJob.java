package com.zto.devops.qc.application.job;

import com.zto.devops.qc.domain.service.InterfaceCoverageDomainService;
import com.zto.zss.common.exception.ZSSException;
import com.zto.zss.worker.processor.BasicProcessor;
import com.zto.zss.worker.processor.model.ProcessResult;
import com.zto.zss.worker.processor.model.TaskContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component("interfaceCoverageBatchDeleteJob")
public class InterfaceCoverageBatchDeleteJob implements BasicProcessor {

    @Autowired
    private InterfaceCoverageDomainService interfaceCoverageDomainService;

    @Override
    public ProcessResult process(TaskContext context) throws ZSSException {
        log.info("接口覆盖率批量删除已验收版本历史数据");
        interfaceCoverageDomainService.batchDelete();
        return ProcessResult.success("OK");
    }

}
