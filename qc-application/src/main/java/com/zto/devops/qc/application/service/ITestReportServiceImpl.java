package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.baidu.unbiz.fluentvalidator.ComplexResult;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.FluentException;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.common.validator.*;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.qc.application.converter.TestReportConverter;
import com.zto.devops.qc.client.constants.Constant;
import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.enums.testmanager.report.TestReportTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.report.TmTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.dto.TmTestPlanEntityDO;
import com.zto.devops.qc.client.model.report.entity.OperatedTestManageVO;
import com.zto.devops.qc.client.model.report.entity.TestReportDateByVersionCodeVO;
import com.zto.devops.qc.client.model.report.entity.TestReportResultVO;
import com.zto.devops.qc.client.model.report.query.ListReportDateByVersionCodesQuery;
import com.zto.devops.qc.client.model.report.query.OperatedTestManageQuery;
import com.zto.devops.qc.client.model.report.query.ReportTestResultQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.TestPlanToSendMailQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanRangeVO;
import com.zto.devops.qc.client.model.testmanager.report.command.*;
import com.zto.devops.qc.client.model.testmanager.report.entity.*;
import com.zto.devops.qc.client.model.testmanager.report.query.*;
import com.zto.devops.qc.client.service.report.ITestReportService;
import com.zto.devops.qc.client.service.report.model.*;
import com.zto.devops.qc.domain.service.*;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.titans.common.util.JsonUtil;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayAuth;
import com.zto.zsmp.annotation.gateway.GatewayAuthType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@ZsmpService(name = "测试报告接口目录", group = "测试管理/测试报告")
public class ITestReportServiceImpl extends GatewayBase implements ITestReportService {

    @Autowired
    private TestReportQueryDomainService testReportQueryDomainService;

    @Autowired
    private TmTestReportCommandDomainService tmTestReportCommandDomainService;

    @Autowired
    private TmTestReportQueryDomainService tmTestReportQueryDomainService;

    @Autowired
    private TestReportConverter testReportConverter;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private TmTestPlanQueryDomainService testPlanQueryDomainService;

    @Autowired
    private QcVersionQualityMetricsCommandDomainService qualityMetricsCommandDomainService;

    @ZApiOperation(description = "一站式研发-测试报告-草稿箱详情-校验报告",
            apiName = "qc/report/checkReport", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public List<TestReportResultVO> queryTestReportResult(ReportTestResultQuery query) {
        return tmTestReportQueryDomainService.query(query);
    }

    @ZApiOperation(description = "一站式研发-版本概览-可执行报告、计划列表",
            apiName = "qc/version/operatedTestManage", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<OperatedTestManageResp> operatedTestManage(OperatedTestManageReq req) {
        OperatedTestManageQuery query = new OperatedTestManageQuery();
        query.setVersionCode(req.getVersionCode());
        query.setUserId(GatewayContext.currentUser().getUser_id());
        OperatedTestManageVO vo = testReportQueryDomainService.query(query);
        OperatedTestManageResp resp = testReportConverter.convertor(vo);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "tm-准入报告-详情",
            apiName = "tm/report/detailAccessReport", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<TmAccessReportDetailResp> detailAccessReport(QueryTestReportDetailReq req) {
        log.info("QueryTestReportDetailReq_access->{}", JsonUtil.toJSON(req));
        if (StringUtil.isBlank(req.getReportCode()) && StringUtil.isBlank(req.getPlanCode())) {
            throw new ServiceException("reportCode 和 planCode 不能同为空！");
        }
        // 校验是否发过计划邮件
        AccessReportDetailQuery query = new AccessReportDetailQuery();
        if (!Objects.isNull(req)) {
            query = testReportConverter.convertAccess(req);
            GatewayContext.fillCurrentUser(query);
        }
        TmAccessReportDetailVO accessReportVO = tmTestReportQueryDomainService.detailAccessReport(query);
        TmAccessReportDetailResp resp = null;
        if (!Objects.isNull(accessReportVO)) {
            resp = testReportConverter.converter(accessReportVO);
        }
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "tm-线上冒烟报告-详情",
            apiName = "tm/report/detailOnlineSmokeReport", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<TmOnlineSmokeReportDetailResp> detailOnlineSmokeReport(QueryTestReportDetailReq req) {
        log.info("QueryTestReportDetailReq_smoke->{}", JsonUtil.toJSON(req));
        if (StringUtil.isBlank(req.getReportCode()) && StringUtil.isBlank(req.getPlanCode())) {
            throw new ServiceException("reportCode 和 planCode 不能同为空！");
        }
        OnlineSmokeReportDetailQuery query = new OnlineSmokeReportDetailQuery();
        if (!Objects.isNull(req)) {
            query = testReportConverter.convertSmoke(req);
            GatewayContext.fillCurrentUser(query);
        }
        TmSmokeReportDetailVO smokeReportDetailVO = tmTestReportQueryDomainService.detailOnlineSmokeReport(query);
        TmOnlineSmokeReportDetailResp resp = null;
        if (!Objects.isNull(smokeReportDetailVO)) {
            resp = testReportConverter.converter(smokeReportDetailVO);
        }
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "tm-准出报告-详情",
            apiName = "tm/report/detailPermitReport", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<TmPermitReportDetailResp> detailPermitReport(QueryTestReportDetailReq req) {
        log.info("QueryTestReportDetailReq_permit->{}", JsonUtil.toJSON(req));
        if (StringUtil.isBlank(req.getReportCode()) && StringUtil.isBlank(req.getPlanCode())) {
            throw new ServiceException("reportCode 和 planCode 不能同为空！");
        }
        PermitReportDetailQuery query = new PermitReportDetailQuery();
        if (!Objects.isNull(req)) {
            query = testReportConverter.convertPermit(req);
            GatewayContext.fillCurrentUser(query);
        }
        TmPermitReportDetailVO permitReportDetailVO = tmTestReportQueryDomainService.detailPermitReport(query);
        TmPermitReportDetailResp resp = null;
        if (!Objects.isNull(permitReportDetailVO)) {
            resp = testReportConverter.converter(permitReportDetailVO);
        }
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "tm-测试报告--查询测试信息",
            apiName = "tm/report/queryCaseExecuteResult", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<CaseExecuteResultVO> queryCaseExecuteResult(QueryCaseExecuteResultReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPlanCode(), new NotBlankValidator("计划编号planCode不能为空！"))
                .on(req.getReportType(), new NotNullValidator("报告类型reportType不能为空！"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        CaseExecuteResultQuery caseExecuteResultQuery = new CaseExecuteResultQuery();
        caseExecuteResultQuery.setCode(req.getReportCode());
        caseExecuteResultQuery.setPlanCode(req.getPlanCode());
        caseExecuteResultQuery.setReportType(req.getReportType());
        // 不同的报告类型
        // 准入报告：冒烟阶段
        // 准出报告：功能测试阶段
        // 线上冒烟报告：线上冒烟阶段
        List<TestPlanStageEnum> testStageList = new ArrayList<>();
        if (TestReportTypeEnum.TEST_ACCESS.equals(req.getReportType())) {
            testStageList.add(TestPlanStageEnum.SMOKE_TEST);
        }
        if (TestReportTypeEnum.TEST_PERMIT.equals(req.getReportType())) {
            testStageList.add(TestPlanStageEnum.FUNCTIONAL_TEST);
        }
        if (TestReportTypeEnum.ONLINE_SMOKE.equals(req.getReportType())) {
            testStageList.add(TestPlanStageEnum.ONLINE_SMOKE_TEST);
        }
        caseExecuteResultQuery.setTestStageList(testStageList);
        CaseExecuteResultVO vo = tmTestReportQueryDomainService.queryCaseExecuteResult(caseExecuteResultQuery);
        return StatusCode.OK.build(vo);

    }

    @ZApiOperation(description = "测试管理-验收测试报告-查询",
            apiName = "tm/report/queryExternalTestReportDetail", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<ExternalTestReportResp> queryExternalTestReportDetail(QueryReportDetailReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPlanCode(), new NotBlankValidator("计划编码"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }

        FindTmExternalTestReportDetailQuery query = new FindTmExternalTestReportDetailQuery();

        query.setReportCode(req.getReportCode());
        query.setPlanCode(req.getPlanCode());
        query.setReportType(ReportType.CHECED_TEST);
        GatewayContext.fillCurrentUser(query);
        ExternalTestReportDetailVO externalReportVO = tmTestReportQueryDomainService.detailExternalTestReport(query);
        ExternalTestReportResp resp = testReportConverter.convertor(externalReportVO);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "tm-准入报告-暂存", apiName = "tm/report/editAccessReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editAccessReport(EditAccessTestReportReq req) {
        if (StringUtil.isNotBlank(req.getSummary()) && req.getSummary().length() > 500) {
            throw new ServiceException("描述字数不能超过500字！");
        }

        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
            AddTmAccessReportCommand command = testReportConverter.convertorAccessAdd(req);
            command.setReportCode(command.getAggregateId());
            command.setReportType(ReportType.TEST_ACCESS);
            command.setStatus(TestPlanStatusEnum.DRAFT);
            command.setActualPresentationDate(
                    addDate(req.getActualPresentationDate(), req.getActualPresentationDay()));
            GatewayContext.fillCurrentUser(command);
            tmTestReportCommandDomainService.addTmAccessReportCommand(command);
        } else {
            // 编辑
            EditTmAccessReportCommand command = testReportConverter.convertor(req);
            command.setReportType(ReportType.TEST_ACCESS);
            command.setActualPresentationDate(
                    addDate(req.getActualPresentationDate(), req.getActualPresentationDay()));
            GatewayContext.fillCurrentUser(command);
            tmTestReportCommandDomainService.editTmAccessReportCommand(command);
        }
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "tm-线上冒烟报告-暂存", apiName = "tm/report/editOnlineSmokeReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editOnlineSmokeReport(EditOnlineSmokeTestReportReq req) {
        if (StringUtil.isNotBlank(req.getSummary()) && req.getSummary().length() > 500) {
            throw new ServiceException("描述字数不能超过500字！");
        }
        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
            AddTmOnlineSmokeReportCommand command = testReportConverter.convertorOnlineSmokeAdd(req);
            command.setReportCode(command.getAggregateId());
            command.setStatus(TestPlanStatusEnum.DRAFT);
            GatewayContext.fillCurrentUser(command);
            tmTestReportCommandDomainService.addTmOnlineSmokeReportCommand(command);
        } else {
            // 编辑
            EditTmOnlineSmokeReportCommand command = testReportConverter.convertor(req);
            GatewayContext.fillCurrentUser(command);
            tmTestReportCommandDomainService.editTmOnlineSmokeReportCommand(command);
        }
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-用例评审报告-编辑并发送", apiName = "tm/report/sendReviewReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editSendReviewReport(EditAndSendReviewReportReq req) {

        ComplexResult cr = checkAll()
                .on(req.getPlanCode(), new NotBlankValidator("计划编码planCode不能为空！"))
                .on(req.getReportName(), new NotBlankValidator("报告名称name不能为空！"))
                .on(req.getReviewInfo(), new NotNullValidator("评审信息reviewInfo不能为空！"))
                .on(req.getPreview(), new NotNullValidator("邮件预览信息"))
                .on(req.getReceiveUsers(), new ListSizeValidator(1L, 200L, "收件人"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (null != req.getReviewOpinions() && !req.getReviewOpinions().isEmpty()) {
            if (req.getReviewOpinions().size() > 500) {
                throw new ServiceException("评审意见不得超过500条");
            }
            for (ReviewOpinionVO reviewOpinionVO : req.getReviewOpinions()) {
                ComplexResult c = checkAll()
                        .on(reviewOpinionVO.getDescription(), new LengthValidator(-1, 2000, "确认点以及修改项"))
                        .doValidate()
                        .result(ResultCollectors.toComplex());
                if (!c.isSuccess()) {
                    throw new FluentException(c.getErrors());
                }
            }
        }

        if (null != req.getReviewRenewals() && !req.getReviewRenewals().isEmpty()) {
            if (req.getReviewRenewals().size() > 500) {
                throw new ServiceException("更新内容不得超过500条");
            }
            for (ReviewRenewalVO reviewRenewalVO : req.getReviewRenewals()) {
                ComplexResult c = checkAll()
                        .on(reviewRenewalVO.getReviewBefore(), new LengthValidator(-1, 2000, "评审前信息"))
                        .on(reviewRenewalVO.getReviewAfter(), new LengthValidator(-1, 2000, "评审后信息"))
                        .on(reviewRenewalVO.getRenewalContent(), new LengthValidator(-1, 2000, "更新内容"))
                        .doValidate()
                        .result(ResultCollectors.toComplex());
                if (!c.isSuccess()) {
                    throw new FluentException(c.getErrors());
                }
            }
        }

        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
//            AddAndSendReviewReportCommand addCommand = new AddAndSendReviewReportCommand(AggregateIdUtil.generateId(AggregateType.REVIEW));
            AddAndSendReviewReportCommand addCommand = testReportConverter.addAndSendReviewConvert(req);
            addCommand.setReportCode(addCommand.getAggregateId());
            addCommand.setStatus(TestPlanStatusEnum.NORMAL);
            addCommand.setReportType(ReportType.CASE_REVIEW);
            GatewayContext.fillCurrentUser(addCommand);
            addCommand.setTestResult(TmTestResultEnum.getTmTestResultEnum(req.getTestResult()));
            tmTestReportCommandDomainService.addAndSendReviewReport(addCommand);
        } else {
            // 编辑
            EditAndSendReviewReportCommand editCommand = testReportConverter.editAndSendReviewConvert(req);
            editCommand.setStatus(TestPlanStatusEnum.NORMAL);
            editCommand.setReportType(ReportType.CASE_REVIEW);
            editCommand.setTestResult(TmTestResultEnum.getTmTestResultEnum(req.getTestResult()));
            GatewayContext.fillCurrentUser(editCommand);
            tmTestReportCommandDomainService.editAndSendReviewReport(editCommand);
//            commandGateway.syncSend(editCommand);
        }

        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "tm-简易测试报告--发送简易测试报告", apiName = "tm/report/sendSimpleTestReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> sendSimpleTestReport(SendSimpleTestReportReq req) {
        ComplexResult cr = checkAll()
                .on(req.getDeveloperCount(), new NotNullValidator("开发人数不能为空！"))
                .on(req.getTesterCount(), new NotNullValidator("测试人数不能为空！"))
                .on(req.getProductCode(), new NotBlankValidator("所属产品productCode不能为空！"))
                .on(req.getVersionCode(), new NotBlankValidator("版本versionCode不能为空！"))
                .on(req.getPlanCode(), new NotBlankValidator("计划编号planCode不能为空！"))
                .on(req.getReportName(), new NotBlankValidator("报告名称name不能为空！"))
                .on(req.getPlanPresentationDay(), new NotBlankValidator("计划提测时间planPresentationDay不能为空！"))
                .on(req.getPlanApprovalExitDay(), new NotBlankValidator("计划准出时间planApprovalExitDate不能为空！"))
                .on(req.getActualPresentationDate(), new NotNullValidator("实际提测时间actualPresentationDate不能为空！"))
                .on(req.getActualApprovalExitDate(), new NotNullValidator("实际准出时间actualApprovalExitDate不能为空！"))
                .on(req.getActualOnlineDate(), new NotNullValidator("实际上线时间actualPublishDate不能为空！"))
                .on(req.getActualPresentationDay(), new NotBlankValidator("实际提测时间actualPresentationDate不能为空！"))
                .on(req.getActualApprovalExitDay(), new NotBlankValidator("实际准出时间actualApprovalExitDate不能为空！"))
                .on(req.getDelay(), new NotNullValidator("是否延期delay不能为空！"))
                .on(req.getAsPlanedOnline(), new NotNullValidator("是否按计划范围上线asPlanedOnline不能为空！"))
                .on(req.getCaseExecuteResultVO(), new NotNullValidator("测试信息caseExecuteResultVO不能为空！"))
                .on(req.getPreview(), new NotBlankValidator("邮件预览信息"))
                .on(req.getReceiveUsers(), new ListSizeValidator(1L, 200L, "收件人"))
                .on(req.getTestResult(), new NotBlankValidator("总体测试结果testResult不能为空！"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (StringUtil.isNotBlank(req.getSummary()) && req.getSummary().length() > 500) {
            throw new ServiceException("描述字数不能超过500字！");
        }
        if (req.getTesterCount() < 0 || req.getDeveloperCount() < 0) {
            throw new ServiceException("人数不能小于0！");
        }
        //checkCoverageReason(req.getCodeCoverResult(), req.getCoverageReasonVOS());

        //校验实际提测和实际准出时间先后顺序
        Date actualPresentationDate = addDate(req.getActualPresentationDate(), req.getActualPresentationDay());
        Date actualApprovalExitDate = addDate(req.getActualApprovalExitDate(), req.getActualApprovalExitDay());
        checkDate(actualPresentationDate, actualApprovalExitDate);

        ZUITestResultEnum zuiTestResult = checkZUITestResult(req.getProductCode(), req.getVersionCode(), req.getZuiTestResult());
        req.setZuiTestResult(zuiTestResult);

        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
            AddAndSendSimpleTestReportCommand addAndSendSimpleTestReportCommand = testReportConverter.convert(req);
            addAndSendSimpleTestReportCommand.setReportCode(addAndSendSimpleTestReportCommand.getAggregateId());
            addAndSendSimpleTestReportCommand.setReportType(ReportType.SIMPLE_PROCESS);
            addAndSendSimpleTestReportCommand.setStatus(TestPlanStatusEnum.NORMAL);
            addAndSendSimpleTestReportCommand.setPlanPresentationDate(addDate(req.getPlanPresentationDate(), req.getPlanPresentationDay()));
            addAndSendSimpleTestReportCommand.setPlanApprovalExitDate(addDate(req.getPlanApprovalExitDate(), req.getPlanApprovalExitDay()));
            addAndSendSimpleTestReportCommand.setActualPresentationDate(actualPresentationDate);
            addAndSendSimpleTestReportCommand.setActualApprovalExitDate(actualApprovalExitDate);
            addAndSendSimpleTestReportCommand.setTestResult(TmTestResultEnum.getTmTestResultEnum(req.getTestResult()));
            GatewayContext.fillCurrentUser(addAndSendSimpleTestReportCommand);
//            commandGateway.syncSend(addAndSendSimpleTestReportCommand);
            tmTestReportCommandDomainService.addAndSendSimpleTestReport(addAndSendSimpleTestReportCommand);
        } else {
            // 编辑
            EditAndSendSimpleTestReportCommand editAndSendSimpleTestReportCommand = testReportConverter.convertEditAndSendSimpleTestReportCommand(req);
            editAndSendSimpleTestReportCommand.setReportType(ReportType.SIMPLE_PROCESS);
            editAndSendSimpleTestReportCommand.setStatus(TestPlanStatusEnum.NORMAL);
            editAndSendSimpleTestReportCommand.setPlanPresentationDate(addDate(req.getPlanPresentationDate(), req.getPlanPresentationDay()));
            editAndSendSimpleTestReportCommand.setPlanApprovalExitDate(addDate(req.getPlanApprovalExitDate(), req.getPlanApprovalExitDay()));
            editAndSendSimpleTestReportCommand.setActualPresentationDate(actualPresentationDate);
            editAndSendSimpleTestReportCommand.setActualApprovalExitDate(actualApprovalExitDate);
            editAndSendSimpleTestReportCommand.setTestResult(TmTestResultEnum.getTmTestResultEnum(req.getTestResult()));
            GatewayContext.fillCurrentUser(editAndSendSimpleTestReportCommand);
            tmTestReportCommandDomainService.editAndSendSimpleTestReport(editAndSendSimpleTestReportCommand);
//            commandGateway.syncSend(editAndSendSimpleTestReportCommand);
        }

        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-用例评审报告-详情",
            apiName = "tm/report/detailReviewReport", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<ReviewReportDetailResp> detailReviewReport(QueryReviewReportReq req) {
        FindReviewReportDetailQuery query = new FindReviewReportDetailQuery();

        query.setReportCode(req.getReportCode());
        query.setPlanCode(req.getPlanCode());
        query.setReportType(ReportType.CASE_REVIEW);
        if (StringUtil.isNotEmpty(req.getProductCode())) {
            query.setProductCode(req.getProductCode());
        }
        if (null != req.getReportUserId()) {
            query.setReportUserId(req.getReportUserId());
        }
        GatewayContext.fillCurrentUser(query);
        ReviewReportDetailVO reviewReportVO = tmTestReportQueryDomainService.detailReviewReport(query);
        ReviewReportDetailResp resp = testReportConverter.convertor(reviewReportVO);
        return StatusCode.OK.build(resp);
    }

    /**
     * 校验实际准出时间和实际提测时间先后顺序
     *
     * @param actualPresentationDate 实际提测日期
     * @param actualApprovalExitDate 实际准出日期
     */
    private void checkDate(Date actualPresentationDate, Date actualApprovalExitDate) {
        if (null != actualApprovalExitDate && null != actualApprovalExitDate) {
            if (actualPresentationDate.getTime() > actualApprovalExitDate.getTime()) {
                throw new ServiceException("实际准出时间不应早于实际提测时间！");
            }
        }
    }

    public Date addDate(Date date, String addTime) {
        if (null == date) {
            return null;
        }
        if ((null == addTime || "".equals(addTime))) {
            return date;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String format = simpleDateFormat.format(date);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        format = format + " " + addTime;
        try {
            Date da1 = dateFormat.parse(format);
            return da1;
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return null;
    }

    @ZApiOperation(description = "tm-准出报告-暂存", apiName = "tm/report/editPermitReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editPermitReport(EditPermitTestReportReq req) {
        if (StringUtil.isNotBlank(req.getSummary()) && req.getSummary().length() > 500) {
            throw new ServiceException("描述字数不能超过500字！");
        }
        checkModuleTest(req.getModuleTestVOS());
        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
            AddTmPermitReportCommand command = testReportConverter.convertorPermitAdd(req);
            command.setReportCode(command.getAggregateId());
            command.setReportType(ReportType.TEST_PERMIT);
            command.setStatus(TestPlanStatusEnum.DRAFT);
            command.setActualApprovalExitDate(
                    addDate(req.getActualApprovalExitDate(), req.getActualApprovalExitDay()));
            GatewayContext.fillCurrentUser(command);
            tmTestReportCommandDomainService.addTmPermitReportCommand(command);
        } else {
            // 编辑
            EditTmPermitReportCommand command = testReportConverter.convertor(req);
            command.setActualApprovalExitDate(
                    addDate(req.getActualApprovalExitDate(), req.getActualApprovalExitDay()));
            command.setReportType(ReportType.TEST_PERMIT);
            GatewayContext.fillCurrentUser(command);
            tmTestReportCommandDomainService.editTmPermitReportCommand(command);
        }
        return StatusCode.OK.build();
    }

    private void checkModuleTest(List<TmModuleTestVO> moduleTestVOS) {
        if (!moduleTestVOS.isEmpty()) {
            for (TmModuleTestVO fu : moduleTestVOS) {
                ComplexResult c = checkAll().on(fu.getTestType(), new LengthValidator(-1, 500, "功能测试点"))
                        .on(fu.getValidIssueCount() == null ? 0 : fu.getValidIssueCount(), new MaxNumberValidator(1000, "有效缺陷数"))
                        .on(fu.getLegacyIssueCount() == null ? 0 : fu.getLegacyIssueCount(), new MaxNumberValidator(1000, "遗留P012缺陷数"))
                        .doValidate()
                        .result(ResultCollectors.toComplex());
                if (!c.isSuccess()) {
                    throw new FluentException(c.getErrors());
                }
            }
        }
        List<TmModuleTestVO> hasTypeList = moduleTestVOS.stream().filter(t -> StringUtil.isNotEmpty(t.getTestType())).collect(Collectors.toList());
        List<String> typeList = hasTypeList.stream().map(TmModuleTestVO::getTestType).distinct().collect(Collectors.toList());
        if (typeList.size() != hasTypeList.size()) {
            throw new ServiceException("功能测试点不能相同,请重新输入");
        }
    }

    @ZApiOperation(description = "移动专项测试报告-查询详情",
            apiName = "tm/report/queryMobileTestReportDetail", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<MobileTestReportDetailResp> queryMobileTestReportDetail(QueryReportDetailReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPlanCode(), new NotBlankValidator("计划编码"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        FindTmMobileTestReportDetailQuery query = new FindTmMobileTestReportDetailQuery();
        query.setReportCode(req.getReportCode());
        query.setPlanCode(req.getPlanCode());
        query.setReportType(ReportType.SPECIAL_MOBILE);
        GatewayContext.fillCurrentUser(query);
        MobileTestReportDetailVO mobileTestReportDetailVO =
                tmTestReportQueryDomainService.findTmMobileTestReportDetailQuery(query);
        MobileTestReportDetailResp resp = testReportConverter.convert(mobileTestReportDetailVO);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "测试管理-用例评审报告-暂存", apiName = "tm/report/editReviewReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addReviewReport(EditReviewReportReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPlanCode(), new NotBlankValidator("计划编码planCode不能为空！"))
                .on(req.getReportName(), new NotBlankValidator("报告名称name不能为空！"))
                .on(req.getReviewInfo(), new NotNullValidator("评审信息reviewInfo不能为空！"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }

        if (null != req.getReviewOpinions() && !req.getReviewOpinions().isEmpty()) {
            if (req.getReviewOpinions().size() > 500) {
                throw new ServiceException("评审意见不得超过500条");
            }
            for (ReviewOpinionVO reviewOpinionVO : req.getReviewOpinions()) {
                ComplexResult c = checkAll()
                        .on(reviewOpinionVO.getDescription(), new LengthValidator(-1, 2000, "确认点以及修改项"))
                        .doValidate()
                        .result(ResultCollectors.toComplex());
                if (!c.isSuccess()) {
                    throw new FluentException(c.getErrors());
                }
            }
        }

        if (null != req.getReviewRenewals() && !req.getReviewRenewals().isEmpty()) {
            if (req.getReviewRenewals().size() > 500) {
                throw new ServiceException("更新内容不得超过500条");
            }
            for (ReviewRenewalVO reviewRenewalVO : req.getReviewRenewals()) {
                ComplexResult c = checkAll()
                        .on(reviewRenewalVO.getReviewBefore(), new LengthValidator(-1, 2000, "评审前信息"))
                        .on(reviewRenewalVO.getReviewAfter(), new LengthValidator(-1, 2000, "评审后信息"))
                        .on(reviewRenewalVO.getRenewalContent(), new LengthValidator(-1, 2000, "更新内容"))
                        .doValidate()
                        .result(ResultCollectors.toComplex());
                if (!c.isSuccess()) {
                    throw new FluentException(c.getErrors());
                }
            }
        }

        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
            AddReviewReportCommand addCommand = testReportConverter.addReviewConvert(req);
            addCommand.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.TEST_REPORT));
            addCommand.setReportCode(addCommand.getAggregateId());
            addCommand.setReportType(ReportType.CASE_REVIEW);
            addCommand.setStatus(TestPlanStatusEnum.NORMAL);
            GatewayContext.fillCurrentUser(addCommand);
            tmTestReportCommandDomainService.addReviewReportCommand(addCommand);
        } else {
            // 编辑
            EditReviewReportCommand editCommand = testReportConverter.editReviewConvert(req);
            editCommand.setReportType(ReportType.CASE_REVIEW);
            editCommand.setStatus(TestPlanStatusEnum.NORMAL);
            GatewayContext.fillCurrentUser(editCommand);
            tmTestReportCommandDomainService.editReviewReportCommand(editCommand);
        }
        return StatusCode.OK.build();
    }


    @ZApiOperation(description = "测试管理-验收测试报告-暂存", apiName = "tm/report/editExternalTestReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addExternalTestReport(EditExternalTestReportReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPlanCode(), new NotBlankValidator("关联计划planCode不能为空！"))
                .on(req.getReportName(), new NotBlankValidator("报告名称"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (StringUtil.isNotBlank(req.getSummary()) && req.getSummary().length() > 500) {
            throw new ServiceException("描述字数不能超过500字！");
        }
        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
            AddExternalTestReportCommand addCommand = testReportConverter.addExternalConvert(req);
            addCommand.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.TEST_REPORT));
            addCommand.setStatus(TestPlanStatusEnum.NORMAL);
            addCommand.setReportType(ReportType.CHECED_TEST);
            addCommand.setReportCode(addCommand.getAggregateId());
            GatewayContext.fillCurrentUser(addCommand);
            tmTestReportCommandDomainService.addExternalTestReportCommand(addCommand);
        } else {
            // 编辑
            EditExternalTestReportCommand editCommand = testReportConverter.editExternalConvert(req);
            editCommand.setStatus(TestPlanStatusEnum.NORMAL);
            editCommand.setReportType(ReportType.CHECED_TEST);
            GatewayContext.fillCurrentUser(editCommand);
            tmTestReportCommandDomainService.editExternalTestReportCommand(editCommand);
        }
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-移动专项测试报告-暂存", apiName = "tm/report/editMobileTestReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addMobileTestReport(EditMobileTestReportReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPlanCode(), new NotBlankValidator("关联计划planCode不能为空！"))
                .on(req.getReportName(), new NotBlankValidator("报告名称name不能为空！"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (StringUtil.isNotBlank(req.getSummary()) && req.getSummary().length() > 500) {
            throw new ServiceException("描述字数不能超过500字！");
        }
        checkModuleTest(req.getModuleTestVOS());

        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
            AddMobileTestReportCommand addCommand = testReportConverter.addMobileConvert(req);
            addCommand.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.TEST_REPORT));
            addCommand.setReportCode(addCommand.getAggregateId());
            addCommand.setReportType(ReportType.SPECIAL_MOBILE);
            addCommand.setStatus(TestPlanStatusEnum.NORMAL);
            addCommand.setActualTestStart(addDate(
                    addCommand.getActualTestStart(), addCommand.getActualTestStartDay()));
            addCommand.setActualTestEnd(addDate(
                    addCommand.getActualTestEnd(), addCommand.getActualTestEndDay()));
            GatewayContext.fillCurrentUser(addCommand);
            tmTestReportCommandDomainService.addMobileTestReportCommand(addCommand);
        } else {
            // 编辑
            EditMobileTestReportCommand editCommand = testReportConverter.editMobileConvert(req);
            editCommand.setReportType(ReportType.SPECIAL_MOBILE);
            editCommand.setStatus(TestPlanStatusEnum.NORMAL);
            editCommand.setActualTestStart(addDate(
                    editCommand.getActualTestStart(), editCommand.getActualTestStartDay()));
            editCommand.setActualTestEnd(addDate(
                    editCommand.getActualTestEnd(), editCommand.getActualTestEndDay()));
            GatewayContext.fillCurrentUser(editCommand);
            tmTestReportCommandDomainService.editMobileTestReportCommand(editCommand);
        }
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "查询安全测试结果(安全漏洞)",
            apiName = "tm/report/querySecurityTestResult", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<SecurityTestResultResp> querySecurityTestResult(String versionCode) {
        if (StringUtil.isBlank(versionCode)) {
            throw new ServiceException("版本编码不能为空!");
        }
        //查当前版本安全测试计划
        TmTestPlanEntityDO entity = tmTestReportQueryDomainService.findSafetyPlanByVersionQuery(versionCode);
        if (Objects.isNull(entity)) {
            throw new ServiceException("未找到安全测试计划!");
        }
        SecurityTestResultResp resp = new SecurityTestResultResp();
        if (TestPlanNewStatusEnum.TERMINATED.equals(entity.getStatus())) {
            log.info("该安全计划已终止, 无需进行安全测试！计划code{}", entity.getCode());
            resp.setSecurityTestResult(SecurityTestResult.PASS);
            resp.setSecurityTestResultCode(SecurityTestResult.PASS.name());
            resp.setSecurityTestResultDesc(SecurityTestResult.PASS.getValue());
            resp.setSecurityUserId(entity.getModifierId());
            resp.setSecurityUserName(entity.getModifier());
            return StatusCode.OK.build(resp);
        }
        //查测试范围表
        TestPlanRangeVO vo = tmTestReportQueryDomainService.getSafeTestPlanRange(entity.getRelationPlanCode());
        if (!Objects.isNull(vo)) {
            SecurityTestResult result = SecurityTestResult.getEnumByName(vo.getStatus());
            //如果测试结果不通过，查安全漏洞
            if (SecurityTestResult.NOPASS.equals(result)) {
                resp = tmTestReportQueryDomainService.selectSecurityTestResult(entity.getCode());
            }
            resp.setSecurityTestResult(result);
            resp.setSecurityTestResultCode(result.name());
            resp.setSecurityTestResultDesc(result.getValue());
            resp.setSecurityUserId(vo.getExecutorId());
            resp.setSecurityUserName(vo.getExecutor());
        }
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "查询简易测试报告",
            apiName = "tm/report/querySimpleTestReportDetail", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<SimpleTestReportDetailResp> querySimpleTestReportDetail(QueryTestReportDetailReq req) {
        if (StringUtil.isBlank(req.getPlanCode())) {
            throw new ServiceException("计划编号planCode不能为空!");
        }
        FindTmSimpleTestReportDetailQuery query = new FindTmSimpleTestReportDetailQuery();
        query.setReportCode(req.getReportCode());
        query.setPlanCode(req.getPlanCode());
        query.setReportType(ReportType.SIMPLE_PROCESS);
        GatewayContext.fillCurrentUser(query);
        SimpleTestReportDetailVO vo = tmTestReportQueryDomainService.findTmSimpleTestReportDetailQuery(query);
        SimpleTestReportDetailResp resp = testReportConverter.convert(vo);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "暂存简易测试报告", apiName = "tm/report/saveSimpleTestReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> saveSimpleTestReport(SaveSimpleTestReportReq req) {
        ComplexResult cr = checkAll()
                .on(req.getReportName(), new NotBlankValidator("报告名称"))
                .on(req.getPlanCode(), new NotBlankValidator("计划编号"))
                .on(req.getProductCode(), new NotBlankValidator("所属产品编号"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (StringUtil.isNotBlank(req.getSummary()) && req.getSummary().length() > 500) {
            throw new ServiceException("描述字数不能超过500字！");
        }
        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
            String aggregateId = aggregateIdGenerateService.generateId(AggregateType.TEST_REPORT);
            AddSimpleTestReportCommand addSimpleTestReportCommand = testReportConverter.convert(req, aggregateId);
            buildAddSimpleTestReportCommand(addSimpleTestReportCommand, req);
            GatewayContext.fillCurrentUser(addSimpleTestReportCommand);
            tmTestReportCommandDomainService.addSimpleTestReportCommand(addSimpleTestReportCommand);
        } else {
            // 编辑
            EditSimpleTestReportCommand editSimpleTestReportCommand = testReportConverter.convert(req);
            buildAddSimpleTestReportCommand(editSimpleTestReportCommand, req);
            GatewayContext.fillCurrentUser(editSimpleTestReportCommand);
            tmTestReportCommandDomainService.editSimpleTestReportCommand(editSimpleTestReportCommand);
        }
        return StatusCode.OK.build();
    }

    private void buildAddSimpleTestReportCommand(SimpleTestReportCommand command, SaveSimpleTestReportReq req) {
        command.setReportCode(command.getAggregateId());
        command.setReportType(ReportType.SIMPLE_PROCESS);
        command.setStatus(TestPlanStatusEnum.NORMAL);
        command.setActualPresentationDate(
                addDate(req.getActualPresentationDate(), req.getActualPresentationDay()));
        command.setActualApprovalExitDate(
                addDate(req.getActualApprovalExitDate(), req.getActualApprovalExitDay()));
    }


    @ZApiOperation(description = "准入报告-发送邮件", apiName = "tm/report/sendAccessReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> sendAccessReport(AddAccessTestReportReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPreview(), new NotNullValidator("邮件预览信息"))
                .on(req.getReceiveUsers(), new ListSizeValidator(1L, 200L, "收件人"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (StringUtil.isNotBlank(req.getSummary()) && req.getSummary().length() > 500) {
            throw new ServiceException("描述字数不能超过500字！");
        }
        // 校验是否发过计划邮件
        checkTestPlanEmail(req.getPlanCode());
        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
            String aggregateId = aggregateIdGenerateService.generateId(AggregateType.TEST_REPORT);
            AddAndSendTmAccessTestReportCommand command = testReportConverter.convert(req, aggregateId);
            GatewayContext.fillCurrentUser(command);
            command.setCaseNum(req.getCaseExecuteResultVO());
            command.setReportCode(command.getAggregateId());
            command.setStatus(TestPlanStatusEnum.NORMAL);
            command.setSendUserId(command.getTransactor().getUserId());
            command.setSendUserName(command.getTransactor().getUserName());
            command.setPlanPresentationDate(addDate(req.getPresentationDate(), req.getPresentationDay()));
            command.setActualPresentationDate(addDate(req.getActualPresentationDate(), req.getActualPresentationDay()));
            command.setReportType(ReportType.TEST_ACCESS);
            tmTestReportCommandDomainService.addAndSendTmAccessTestReportCommand(command);
        } else {
            // 编辑
            EditAndSendTmAccessTestReportCommand command = testReportConverter.convert(req);
            GatewayContext.fillCurrentUser(command);
            command.setCaseNum(req.getCaseExecuteResultVO());
            command.setStatus(TestPlanStatusEnum.NORMAL);
            command.setSendUserId(command.getTransactor().getUserId());
            command.setSendUserName(command.getTransactor().getUserName());
            command.setPlanPresentationDate(addDate(req.getPresentationDate(), req.getPresentationDay()));
            command.setActualPresentationDate(addDate(req.getActualPresentationDate(), req.getActualPresentationDay()));
            command.setReportType(ReportType.TEST_ACCESS);
            tmTestReportCommandDomainService.editAndSendTmAccessTestReportCommand(command);
        }
        return StatusCode.OK.build();
    }

    private void checkTestPlanEmail(String planCode) {
        TestPlanToSendMailQuery query = new TestPlanToSendMailQuery();
        query.setPlanCode(planCode);
        query.setPlanType(TestPlanNewTypeEnum.TEST_PLAN.name());
        if (!testPlanQueryDomainService.checkTestPlanEmail(query)) {
            throw new ServiceException("发送报告前，请先发送计划邮件！");
        }
    }

    @ZApiOperation(description = "验收测试报告-编辑并发送", apiName = "tm/report/sendExternalTestReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editAndSendExternalTestReport(AddExternalTestReportReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPlanCode(), new NotBlankValidator("关联计划"))
                .on(req.getProductCode(), new NotBlankValidator("所属产品"))
                .on(req.getVersionCode(), new NotBlankValidator("版本"))
                .on(req.getReportName(), new NotBlankValidator("报告名称"))
                .on(req.getCheckStartDate(), new NotNullValidator("验收开始时间"))
                .on(req.getCheckEndDate(), new NotNullValidator("验收结束时间"))
                .on(req.getActualPublishDate(), new NotNullValidator("实际上线时间"))
                .on(req.getDelay(), new NotNullValidator("是否延期"))
                .on(req.getPreview(), new NotNullValidator("邮件预览信息"))
                .on(req.getReceiveUsers(), new ListSizeValidator(1L, 200L, "收件人"))
                .on(req.getTestResult(), new NotBlankValidator("总体测试结果"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (StringUtil.isNotBlank(req.getSummary()) && req.getSummary().length() > 500) {
            throw new ServiceException("描述字数不能超过500字！");
        }
        if (!SecurityTestResult.PASS.equals(req.getSecurityTestResult())) {
            throw new ServiceException("安全测试结果不通过，无法发送报告！");
        }

        ZUITestResultEnum zuiTestResult = checkZUITestResult(req.getProductCode(), req.getVersionCode(), req.getZuiTestResult());
        req.setZuiTestResult(zuiTestResult);

        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
            String aggregateId = aggregateIdGenerateService.generateId(AggregateType.TEST_REPORT);
            AddAndSendExternalTestReportCommand addCommand = testReportConverter.convert(req, aggregateId);
            addCommand.setStatus(TestPlanStatusEnum.NORMAL);
            addCommand.setReportType(ReportType.CHECED_TEST);
            addCommand.setReportCode(addCommand.getAggregateId());
            addCommand.setPresentationDate(addDate(req.getPresentationDate(), req.getPresentationDay()));
            addCommand.setPublishDate(addDate(req.getPublishDate(), req.getPublishDay()));
            addCommand.setTestResult(TmTestResultEnum.getTmTestResultEnum(req.getTestResult()));
            GatewayContext.fillCurrentUser(addCommand);
            tmTestReportCommandDomainService.addAndSendExternalTestReportCommand(addCommand);
        } else {
            // 编辑
            EditAndSendExternalTestReportCommand editCommand = testReportConverter.convert(req);
            editCommand.setPresentationDate(addDate(req.getPresentationDate(), req.getPresentationDay()));
            editCommand.setPublishDate(addDate(req.getPublishDate(), req.getPublishDay()));
            editCommand.setReportType(ReportType.CHECED_TEST);
            editCommand.setTestResult(TmTestResultEnum.getTmTestResultEnum(req.getTestResult()));
            GatewayContext.fillCurrentUser(editCommand);
            tmTestReportCommandDomainService.editAndSendExternalTestReportCommand(editCommand);
        }
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "移动专项测试报告-编辑并发送", apiName = "tm/report/sendMobileTestReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editAndSendMobileTestReport(AddMobileTestReportReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPlanCode(), new NotBlankValidator("关联计划"))
                .on(req.getReportName(), new NotBlankValidator("报告名称"))
                .on(req.getActualTestStart(), new NotNullValidator("实际测试开始时间"))
                .on(req.getActualTestEnd(), new NotNullValidator("实际测试结束时间"))
                .on(req.getPreview(), new NotNullValidator("邮件预览信息"))
                .on(req.getTestResult(), new NotBlankValidator("总体测试结果"))
                .on(req.getReceiveUsers(), new ListSizeValidator(1L, 200L, "收件人"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (StringUtil.isNotBlank(req.getSummary()) && req.getSummary().length() > 500) {
            throw new ServiceException("描述字数不能超过500字！");
        }
        checkModuleTest(req.getModuleTestVOS());
        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
            String aggregateId = aggregateIdGenerateService.generateId(AggregateType.TEST_REPORT);
            AddAndSendMobileTestReportCommand addCommand = testReportConverter.convert(req, aggregateId);
            addCommand.setReportCode(addCommand.getAggregateId());
            addCommand.setReportType(ReportType.SPECIAL_MOBILE);
            addCommand.setStatus(TestPlanStatusEnum.NORMAL);
            addCommand.setTestResult(TmTestResultEnum.getTmTestResultEnum(req.getTestResult()));
            GatewayContext.fillCurrentUser(addCommand);
            tmTestReportCommandDomainService.addAndSendMobileTestReportCommand(addCommand);
        } else {
            // 编辑
            EditAndSendMobileTestReportCommand editCommand = testReportConverter.convert(req);
            editCommand.setReportType(ReportType.SPECIAL_MOBILE);
            editCommand.setStatus(TestPlanStatusEnum.NORMAL);
            editCommand.setTestResult(TmTestResultEnum.getTmTestResultEnum(req.getTestResult()));
            GatewayContext.fillCurrentUser(editCommand);
            tmTestReportCommandDomainService.editAndSendMobileTestReportCommand(editCommand);
        }
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "线上冒烟报告-发送邮件", apiName = "tm/report/sendOnlineSmokeReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> sendOnlineSmokeReport(AddOnlineSmokeTestReportReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPreview(), new NotNullValidator("邮件预览信息"))
                .on(req.getReceiveUsers(), new ListSizeValidator(1L, 200L, "收件人"))
                .on(req.getTestResult(), new NotBlankValidator("总体测试结果"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (StringUtil.isNotBlank(req.getSummary()) && req.getSummary().length() > 500) {
            throw new ServiceException("描述字数不能超过500字！");
        }
        // 校验是否发过计划邮件
        checkTestPlanEmail(req.getPlanCode());

        ZUITestResultEnum zuiTestResult = checkZUITestResult(req.getProductCode(), req.getVersionCode(), req.getZuiTestResult());
        req.setZuiTestResult(zuiTestResult);

        //校验准出报告测试结果
        checkPermitResult(req.getPlanCode());

        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
            String aggregateId = aggregateIdGenerateService.generateId(AggregateType.TEST_REPORT);
            AddAndSendTmOnlineSmokeTestReportCommand command = testReportConverter.convert(req, aggregateId);
            GatewayContext.fillCurrentUser(command);
            command.setCaseNum(req.getCaseExecuteResultVO());
            command.setReportCode(command.getAggregateId());
            command.setStatus(TestPlanStatusEnum.NORMAL);
            command.setSendUserId(command.getTransactor().getUserId());
            command.setSendUserName(command.getTransactor().getUserName());
            command.setReportType(ReportType.ONLINE_SMOKE);
            command.setTestResult(TmTestResultEnum.getTmTestResultEnum(req.getTestResult()));
            tmTestReportCommandDomainService.addAndSendTmOnlineSmokeTestReportCommand(command);
        } else {
            // 编辑
            EditAndSendTmOnlineSmokeTestReportCommand command = testReportConverter.convert(req);
            GatewayContext.fillCurrentUser(command);
            command.setCaseNum(req.getCaseExecuteResultVO());
            command.setStatus(TestPlanStatusEnum.NORMAL);
            command.setSendUserId(command.getTransactor().getUserId());
            command.setSendUserName(command.getTransactor().getUserName());
            command.setReportType(ReportType.ONLINE_SMOKE);
            command.setTestResult(TmTestResultEnum.getTmTestResultEnum(req.getTestResult()));
            tmTestReportCommandDomainService.editAndSendTmOnlineSmokeTestReportCommand(command);
        }
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "准出报告-发送邮件", apiName = "tm/report/sendPermitReport", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> sendPermitReport(AddPermitTestReportReq req) {
        ComplexResult cr = checkAll()
                .on(req.getPlanCode(), new NotBlankValidator("计划编码"))
                .on(req.getReportName(), new NotBlankValidator("报告名称"))
                .on(req.getSecurityTestResult(), new NotNullValidator("安全测试结果"))
                .on(req.getPreview(), new NotNullValidator("邮件预览信息"))
                .on(req.getReceiveUsers(), new ListSizeValidator(1L, 200L, "收件人"))
                .on(req.getTestResult(), new NotBlankValidator("总体测试结果"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (StringUtil.isNotBlank(req.getSummary()) && req.getSummary().length() > 500) {
            throw new ServiceException("描述字数不能超过500字！");
        }
        if (!SecurityTestResult.PASS.equals(req.getSecurityTestResult())) {
            throw new ServiceException("安全测试结果不通过，无法发送报告！");
        }
        if ((!req.getTestResult().equals(TmTestResultEnum.NO_PASS.name()))
                && (null == req.getActualApprovalExitDate()
                || null == req.getActualApprovalExitDay()
                || null == req.getDelayDays())) {
            throw new ServiceException("实际准出时间和延期天数不能为空！");
        }
        // 校验是否发过计划邮件
        checkTestPlanEmail(req.getPlanCode());
        //checkCoverageReason(req.getCodeCoverResult(), req.getCoverageReasonVOS());
        checkModuleTest(req.getModuleTestVOS());

        ZUITestResultEnum zuiTestResult = checkZUITestResult(req.getProductCode(), req.getVersionCode(), req.getZuiTestResult());
        req.setZuiTestResult(zuiTestResult);

        if (StringUtil.isBlank(req.getReportCode())) {
            // 创建
            String aggregateId = aggregateIdGenerateService.generateId(AggregateType.TEST_REPORT);
            AddAndSendTmPermitTestReportCommand command = testReportConverter.convert(req, aggregateId);
            GatewayContext.fillCurrentUser(command);
            command.setCaseNum(req.getCaseExecuteResultVO());
            command.setReportCode(command.getAggregateId());
            command.setStatus(TestPlanStatusEnum.NORMAL);
            command.setSendUserId(command.getTransactor().getUserId());
            command.setSendUserName(command.getTransactor().getUserName());
            command.setActualApprovalExitDate(addDate(req.getActualApprovalExitDate(), req.getActualApprovalExitDay()));
            command.setPlanApprovalExitDate(addDate(req.getApprovalExitDate(), req.getApprovalExitDay()));
            command.setReportType(ReportType.TEST_PERMIT);
            command.setTestResult(TmTestResultEnum.getTmTestResultEnum(req.getTestResult()));
            tmTestReportCommandDomainService.addAndSendTmPermitTestReportCommand(command);
        } else {
            // 编辑
            EditAndSendTmPermitTestReportCommand command = testReportConverter.convert(req);
            GatewayContext.fillCurrentUser(command);
            command.setCaseNum(req.getCaseExecuteResultVO());
            command.setStatus(TestPlanStatusEnum.NORMAL);
            command.setSendUserId(command.getTransactor().getUserId());
            command.setSendUserName(command.getTransactor().getUserName());
            command.setActualApprovalExitDate(addDate(req.getActualApprovalExitDate(), req.getActualApprovalExitDay()));
            command.setPlanApprovalExitDate(addDate(req.getApprovalExitDate(), req.getApprovalExitDay()));
            command.setReportType(ReportType.TEST_PERMIT);
            command.setTestResult(TmTestResultEnum.getTmTestResultEnum(req.getTestResult()));
            tmTestReportCommandDomainService.editAndSendTmPermitTestReportCommand(command);
        }
        return StatusCode.OK.build();
    }

    private void checkPermitResult(String planCode) {
        QueryPermitResultVO resultVO = tmTestReportQueryDomainService.queryPermitResult(planCode);
        if (!resultVO.getSentFlag()) {
            throw new ServiceException("请先发送准入和准出报告再发送线上冒烟报告");
        }
        if (resultVO.getTestResult().equals(TmTestResultEnum.NO_PASS)) {
            throw new ServiceException("当前版本准出报告结果不通过，不可以发送线上冒烟报告，请先更新准出报告结果为通过，再发送线上冒烟报告！");
        }
    }

    /**
     * 校验zui测试结果
     *
     * @param productCode   产品code
     * @param versionCode   版本code
     * @param zuiTestResult zui测试结果
     * @return
     */
    private ZUITestResultEnum checkZUITestResult(String productCode, String versionCode, ZUITestResultEnum zuiTestResult) {
        Boolean zuiFlag = tmTestReportQueryDomainService.containsZUI(productCode, versionCode);
        //未关联zui应用，刷新zui测试结果
        if (null == zuiFlag || !zuiFlag) {
            return ZUITestResultEnum.UNKNOWN;
        }
        //zui测试结果必填校验
        if (zuiFlag && null == zuiTestResult) {
            throw new ServiceException("zui测试结果不能为空！");
        }
        return zuiTestResult;
    }

    @ZApiOperation(description = "tm-线上冒烟报告-查询准出报告测试结果",
            apiName = "tm/report/queryPermitResult", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<QueryPermitResultResp> queryPermitResult(QueryPermitResultReq req) {
        log.info("checkPermitTestResult->{}", JsonUtil.toJSON(req));
        if (StringUtil.isBlank(req.getPlanCode())) {
            throw new ServiceException(" planCode 不能同为空!");
        }
        QueryPermitResultVO resultVO = tmTestReportQueryDomainService.queryPermitResult(req.getPlanCode());
        QueryPermitResultResp resp = Objects.isNull(resultVO) ? null : testReportConverter.converter(resultVO);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "tm-刷度量指标历史数据", apiName = "tm/quality/computeMetrics", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> computeMetrics(ComputeMetricsReq req) {
        if ( CollectionUtils.isEmpty(req.getVersionCodes())) {
            throw new ServiceException("版本code不能为空");
        }
        if (req.getVersionCodes().size() > 200) {
            throw new ServiceException("一次最多支持刷200条");
        }
        ComputeMetricsCommand command = testReportConverter.convert(req);
        GatewayContext.fillCurrentUser(command);
        qualityMetricsCommandDomainService.computeMetrics(command);
        return StatusCode.OK.build();
    }

    /*************************************** 对外接口 begin***************************************************/

    @ZsmpApiOperation(description = "对外-查询测试报告",
            gatewayApi = @GatewayApi(
                    namespace = "luban",
                    name = "reportApi/queryReportList",
                    description = "对外-查询测试报告",
                    responseDateFormat = "yyyy-MM-dd HH:mm:ss",
                    auth = @GatewayAuth(type = GatewayAuthType.DIGEST, enable = true, value = Constant.GATEWAY_SIGN)))
    @Override
    public Result<List<AllReportDateResp>> queryReportList(PageReportReq req) {
        log.info("req----------------" + JsonUtil.toJSON(req));
        PageReportMqQuery query = testReportConverter.convert(req);
        if (null != req && null != req.getProductCode()) {
            List<String> st = new ArrayList<>();
            st.add(req.getProductCode());
            query.setProductCodes(st);
        }
        if (null != req.getReportTypeList() && req.getReportTypeList().size() > 0) {
            List<ReportType> typeList = new ArrayList<>();
            for (Integer i : req.getReportTypeList()) {
                typeList.add(ReportType.getEnumByType(i));
            }
            query.setReportTypes(typeList);
        }
        log.info("query----------------" + JsonUtil.toJSON(query));
        List<AllReportDateResp> allReportDateRespList = tmTestReportQueryDomainService.reList(query);
        return StatusCode.OK.build(allReportDateRespList);
    }

    @Override
    public List<TestReportDateByVersionCodeVO> ListReportDateByVersionCodes(ListReportDateByVersionCodesQuery query){
       return tmTestReportQueryDomainService.ListReportDateByVersionCodes(query);
    }

    /*************************************** 对外接口 end***************************************************/
}
