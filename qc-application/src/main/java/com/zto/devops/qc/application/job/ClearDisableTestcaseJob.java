package com.zto.devops.qc.application.job;

import com.zto.devops.qc.domain.service.TestcaseCommandDomainService;
import com.zto.zss.common.exception.ZSSException;
import com.zto.zss.worker.processor.BasicProcessor;
import com.zto.zss.worker.processor.model.ProcessResult;
import com.zto.zss.worker.processor.model.TaskContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Slf4j
@Component("clearDisableTestcaseJob")
public class ClearDisableTestcaseJob implements BasicProcessor {

    @Autowired
    private TestcaseCommandDomainService testcaseCommandDomainService;

    @Override
    public ProcessResult process(TaskContext context) throws ZSSException {
        log.info("ClearDisableTestcaseJob StartTime: {}", LocalDateTime.now());
        try {
            testcaseCommandDomainService.clearDisableTestcaseJob();
        } catch (Exception e) {
            log.error("ClearDisableTestcaseJob Error", e);
        }
        log.info("ClearDisableTestcaseJob FinishTime: {}", LocalDateTime.now());
        return ProcessResult.success("OK");
    }
}
