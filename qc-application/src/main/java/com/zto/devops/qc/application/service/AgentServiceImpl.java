package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baidu.unbiz.fluentvalidator.ComplexResult;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.exception.FluentException;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.validator.LengthValidator;
import com.zto.devops.framework.common.validator.NotBlankValidator;
import com.zto.devops.framework.common.validator.NotNullValidator;
import com.zto.devops.qc.client.enums.agent.ChaosExceptionTypeEnum;
import com.zto.devops.qc.client.enums.agent.ChaosRuleTypeEnum;
import com.zto.devops.qc.client.service.agent.AgentService;
import com.zto.devops.qc.client.service.agent.model.*;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.QcAgentRuleConfigRepository;
import com.zto.devops.qc.domain.gateway.zcat.ZCatService;
import com.zto.devops.qc.domain.service.AgentDomainService;
import com.zto.devops.qc.domain.service.AgentQueryDomainService;
import com.zto.devops.qc.domain.util.JsEngineSecurityCheckUtils;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.titans.common.util.StringUtil;
import com.zto.zsmp.annotation.ZsmpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@ZsmpService(name = "一站式研发-agent相关接口", group = "一站式研发-agent")
@Service
@Slf4j
public class AgentServiceImpl extends GatewayBase implements AgentService {
    @Autowired
    private RedisService redisService;
    @Autowired
    private AgentDomainService agentDomainService;
    @Autowired
    private AgentQueryDomainService agentQueryDomainService;
    @Autowired
    private QcAgentRuleConfigRepository qcAgentRuleConfigRepository;
    @Autowired
    private ZCatService zCatService;

    @ZApiOperation(description = "一站式研发--获取异常类信息", apiName = "api/agent/queryExceptionInfo", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<JSONArray> queryExceptionInfo(ExceptionInfoReq req) {
        ComplexResult cr = checkAll()
                .on(req.getAppId(), new NotBlankValidator("appId"))
                .on(req.getVersionCode(), new NotBlankValidator("版本编号"))
                .on(req.getClassPath(), new NotBlankValidator("类全路径"))
                .on(req.getLineNum(), new NotNullValidator("行号"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(agentDomainService.queryAnalyze(req));
    }

    @ZApiOperation(description = "一站式研发--agent端拉取故障注入配置信息", apiName = "api/agent/conf/pull", namespace = "luban")
    @Override
    public Result<Map<String, Object>> pullConf(RuleConfigReq req) {
        ComplexResult cr = checkAll()
                .on(req.getAppid(), new NotBlankValidator("应用appid"))
                .on(req.getDynEnv(), new NotBlankValidator("版本tag-dynEnv"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(agentDomainService.pullConfig(req.getAppid(), req.getDynEnv(), req.getAgentIp()));
    }

    @ZApiOperation(description = "一站式研发--分页获取故障注入配置信息",
            apiName = "api/agent/conf/pageQuery",
            namespace = "luban",
            responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<RuleConfigVO> pageQueryConf(PageRuleConfigReq req) {
        PageRuleConfigResp resp = agentDomainService.pageQueryAgentRuleConfig(req);
        return StatusCode.OK.build(resp.getList(), req.getPage(), req.getSize(), resp.getTotal());
    }

    @ZApiOperation(description = "一站式研发--根据ID获取故障注入配置信息", apiName = "api/agent/conf/queryConfById", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<RuleConfigVO> queryConfById(RuleConfigReq req) {
        ComplexResult cr = checkAll()
                .on(req.getId(), new NotNullValidator("规则id"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(agentDomainService.queryAgentRuleConfig(req.getId()));
    }

    @ZApiOperation(description = "一站式研发--新增故障注入配置信息",
            apiName = "api/agent/conf/new",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Boolean> newConf(RuleConfigReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getAppid(), new NotBlankValidator("注入应用"))
                .on(req.getVersionCode(), new NotBlankValidator("版本号"))
                .on(req.getClassName(), new NotBlankValidator("注入项"))
                .on(req.getRuleType(), new NotNullValidator("注入类型"))
                .on(req.getExceptionType(), new NotNullValidator("注入规则"))
                .on(req.getInjectionRuleName(), new NotNullValidator("注入规则名称"))
                .on(req.getInjectionRuleName(), new LengthValidator(1, 60, "注入规则名称"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (req.getRuleType().equals(ChaosRuleTypeEnum.ServiceClass)) {
            serviceClassVerify(req);
        }
        if (req.getExceptionType().equals(ChaosExceptionTypeEnum.ResultMock)) {
            resultMockVerify(req);
        }
        return StatusCode.OK.build(qcAgentRuleConfigRepository.newRuleConfig(req, GatewayContext.currentUser().toSimpleUser()));
    }

    @ZApiOperation(description = "一站式研发--编辑故障注入配置信息", apiName = "api/agent/conf/edit", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Boolean> modifyConf(RuleConfigReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品编号"))
                .on(req.getAppid(), new NotBlankValidator("注入应用"))
                .on(req.getVersionCode(), new NotBlankValidator("版本号"))
                .on(req.getId(), new NotNullValidator("规则编号"))
                .on(req.getRuleType(), new NotNullValidator("注入类型"))
                .on(req.getExceptionType(), new NotNullValidator("注入规则"))
                .on(req.getClassName(), new NotBlankValidator("注入项"))
                .on(req.getInjectionRuleName(), new NotNullValidator("规则名称"))
                .on(req.getInjectionRuleName(), new LengthValidator(1, 60, "规则名称"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (req.getExceptionType().equals(ChaosExceptionTypeEnum.ResultMock)) {
            resultMockVerify(req);
        }
        return StatusCode.OK.build(qcAgentRuleConfigRepository.modifyRuleConfig(req, GatewayContext.currentUser().toSimpleUser()));
    }

    @ZApiOperation(description = "一站式研发--批量操作故障注入配置信息", apiName = "api/agent/conf/batchOpt", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Boolean> batchOptConf(BatchOptRuleConfigReq req) {
        ComplexResult cr = checkAll()
                .on(req.getIds(), new NotNullValidator("规则编号集合ids"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (CollectionUtil.isEmpty(req.getIds())) {
            throw new ServiceException("规则编号集合不能为空");
        }
        if ((req.getEnable() == null) && (req.getStatus() == null)) {
            throw new ServiceException("操作目的不能为空");
        }
        return StatusCode.OK.build(qcAgentRuleConfigRepository.batchOptRuleConfig(req, GatewayContext.currentUser().toSimpleUser()));
    }

    @ZApiOperation(description = "一站式研发--删除故障注入配置信息", apiName = "api/agent/conf/remove", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Boolean> removeConf(RuleConfigReq req) {
        ComplexResult cr = checkAll()
                .on(req.getAppid(), new NotBlankValidator("应用appid"))
                .on(req.getVersionCode(), new NotBlankValidator("版本versionCode"))
                .on(req.getId(), new NotNullValidator("规则id"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        return StatusCode.OK.build(qcAgentRuleConfigRepository.removeRuleConfig(req, GatewayContext.currentUser().toSimpleUser()));
    }

    @ZApiOperation(description = "一站式研发--agent故障注入配置信息置失效", apiName = "api/agent/conf/delete", namespace = "luban")
    @Override
    public Result<Void> delete(RuleConfigReq req) {
        ComplexResult cr = checkAll()
                .on(req.getAppid(), new NotBlankValidator("应用appid"))
                .on(req.getVersionCode(), new NotBlankValidator("版本versionCode"))
                .on(req.getId(), new NotNullValidator("规则id"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        qcAgentRuleConfigRepository.modifyRuleStatus(req, new User(0L, "AGENT"));
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "一站式研发--获取依赖的服务", apiName = "api/agent/queryDependencyService", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<JSONObject> queryDependencyService(DependencyQueryReq req) {
        return StatusCode.OK.build(zCatService.queryDependencyService(req));
    }

    @ZApiOperation(description = "一站式研发--获取故障注入异常类型列表",
            apiName = "api/agent/conf/getChaosExceptionType",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Map<String, List<Map<String, String>>>> getChaosExceptionType() {
        return StatusCode.OK.build(agentQueryDomainService.getChaosExceptionTypeList());
    }

    @ZApiOperation(description = "一站式研发--查询版本是否包含故障注入规则", apiName = "api/agent/conf/isIncludeChaosRule", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<String>> isIncludeChaosRule(IsIncludeChaosRuleReq req) {
        ComplexResult cr = checkAll()
                .on(req.getVersionCodes(), new NotNullValidator("versionCodes"))
                .doValidate().result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (CollectionUtil.isEmpty(req.getVersionCodes())) {
            throw new ServiceException("versionCodes不能为空");
        }
        return StatusCode.OK.build(agentQueryDomainService.isContainChaosRule(req));
    }

    private void serviceClassVerify(RuleConfigReq req) {
        if (MapUtils.isEmpty(req.getRuleBody())) {
            throw new ServiceException("规则体ruleBody不能为空！");
        }
        Map<String, Object> body = req.getRuleBody();
        if (Objects.isNull(body.get("exceptionClassName"))) {
            throw new ServiceException("异常类exceptionClassName不能为空！");
        }
        if (Objects.isNull(body.get("constructionMethodParamName"))) {
            throw new ServiceException("构造方法constructionMethodParamName不能为空！");
        }
        String constructionMethodParamName = body.get("constructionMethodParamName").toString();
        String[] names;
        if (constructionMethodParamName.startsWith("(") && constructionMethodParamName.endsWith(")")) {
            names = constructionMethodParamName.substring(1, constructionMethodParamName.length() - 1).split(",");
        } else {
            names = constructionMethodParamName.split(",");
        }
        String[] params = new String[0];
        if (Objects.nonNull(body.get("constructionMethodParamValue"))) {
            String constructionMethodParamValue = body.get("constructionMethodParamValue").toString();
            params = constructionMethodParamValue.split(",");
        }
        if (params.length != names.length) {
            throw new ServiceException("构造方法入参与自定义入参数量不一致！");
        }
    }

    private void resultMockVerify(RuleConfigReq req) {
        Map<String, Object> body = req.getRuleBody();
        if (MapUtils.isEmpty(body)) {
            throw new ServiceException("规则体ruleBody不能为空！");
        }
        if (Objects.isNull(body.get("mockTag"))) {
            throw new ServiceException("TAG不能为空！");
        }
        if (Objects.isNull(body.get("mockRule"))) {
            throw new ServiceException("注入规则不能为空！");
        } else {
            JsEngineSecurityCheckUtils.checkInsecureKeyword(String.valueOf(body.get("mockRule")));
        }
        if (Objects.isNull(body.get("mockResponseBody"))) {
            throw new ServiceException("注入响应不能为空！");
        }
    }

}
