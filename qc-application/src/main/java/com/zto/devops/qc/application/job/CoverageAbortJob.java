package com.zto.devops.qc.application.job;

import com.zto.devops.qc.domain.service.CoverageDomainService;
import com.zto.zss.common.exception.ZSSException;
import com.zto.zss.worker.processor.BasicProcessor;
import com.zto.zss.worker.processor.model.ProcessResult;
import com.zto.zss.worker.processor.model.TaskContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component("coverageAbortJob")
public class CoverageAbortJob implements BasicProcessor {

    @Autowired
    private CoverageDomainService coverageDomainService;

    @Override
    public ProcessResult process(TaskContext context) throws ZSSException {
        log.info("覆盖率生成超时终止任务");
        coverageDomainService.coverageTimeoutAbort();
        return ProcessResult.success("OK");
    }

}
