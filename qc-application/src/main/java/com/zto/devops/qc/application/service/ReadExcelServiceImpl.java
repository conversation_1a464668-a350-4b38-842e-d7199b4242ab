package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.baidu.unbiz.fluentvalidator.ComplexResult;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.exception.FluentException;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.validator.NotBlankValidator;
import com.zto.devops.project.client.service.common.ICommonService;
import com.zto.devops.qc.client.model.report.entity.*;
import com.zto.devops.qc.client.service.excel.ReadExeclService;
import com.zto.devops.qc.client.service.excel.model.ReadExcelReq;
import com.zto.devops.qc.domain.gateway.impexp.IExcelProcess;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWSsoAuth;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayWebAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


@Slf4j
@Service
@ZsmpService(name = "excel文件解析")
public class ReadExcelServiceImpl extends GatewayBase implements ReadExeclService {

    @Autowired
    private IExcelProcess excelProcess;

    @Reference
    private ICommonService commonService;

    @ZApiOperation(description = "解析测试计划-功能测试点", timeout = 5000,
            apiName = "qc/excel/readExcelToPlanFunctionPoint", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<PlanFunctionPointResp>> readExcelToPlanFunctionPoint(ReadExcelReq req) {
        ComplexResult cr = checkAll()
                .on(req.getRemoteFileId(), new NotBlankValidator("文件地址"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        List<PlanFunctionPointResp> resps = excelProcess.ExcelWriterPFP(commonService.getInnerFileUrl(GatewayContext.currentUser().getOpenId(), req.getRemoteFileId()));
        if (resps.size() > 500) {
            throw new ServiceException("数据超过500！请删减后重新提交");
        }

        return StatusCode.OK.build(resps);
    }

    @ZApiOperation(description = "解析移动专项测试计划", timeout = 5000,
            apiName = "qc/excel/readExcelToPlanMobile", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<PlanMobileResp>> readExcelToPlanMobile(ReadExcelReq req) {
        ComplexResult cr = checkAll()
                .on(req.getRemoteFileId(), new NotBlankValidator("文件地址"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        List<PlanMobileResp> resps = excelProcess.ExcelWriterPM(commonService.getInnerFileUrl(GatewayContext.currentUser().getOpenId(), req.getRemoteFileId()));
        if (resps.size() > 500) {
            throw new ServiceException("数据超过500！请删减后重新提交");
        }
        return StatusCode.OK.build(resps);
    }
}
