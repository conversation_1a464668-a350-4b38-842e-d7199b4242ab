package com.zto.devops.qc.application.handler;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.google.common.collect.Multimaps;
import com.zto.devops.qc.client.service.agent.model.ExchangeResp;
import com.zto.devops.qc.client.service.agent.model.entity.Worker;
import com.zto.titans.common.entity.Result;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.Collection;


public class AgentDeferredResultHolder {
    public static Multimap<Worker, DeferredResult<ResponseEntity<Result<ExchangeResp>>>> getDeferredResults() {
        return deferredResults;
    }

    private static Multimap<Worker, DeferredResult<ResponseEntity<Result<ExchangeResp>>>>
            deferredResults = Multimaps.synchronizedSetMultimap(HashMultimap.create());

    public static void setDeferredResults(Multimap<Worker, DeferredResult<ResponseEntity<Result<ExchangeResp>>>> deferredResults) {
        AgentDeferredResultHolder.deferredResults = deferredResults;
    }

    public static boolean isExist(Worker worker) {
        if (CollectionUtils.isNotEmpty(deferredResults.get(worker))) {
            return true;
        }
        return false;
    }

    public static void setResult(String exchangeCommand, String desc, Worker worker) {
        Collection<DeferredResult<ResponseEntity<Result<ExchangeResp>>>> results = deferredResults.get(worker);
        if (CollectionUtils.isNotEmpty(results)) {
            for (DeferredResult<ResponseEntity<Result<ExchangeResp>>> deferredResult : Lists.newArrayList(results)) {
                deferredResult.setResult(new ResponseEntity<>(Result.success(new ExchangeResp(exchangeCommand, desc)), HttpStatus.OK));
                return;
            }
        }
    }


}
