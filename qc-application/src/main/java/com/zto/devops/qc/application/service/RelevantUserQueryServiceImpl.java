package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.github.pagehelper.PageHelper;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.fsm.Event;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.issue.IssueEvent;
import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import com.zto.devops.qc.client.model.parameter.TaskBaseParameter;
import com.zto.devops.qc.client.model.parameter.TaskResultParameter;
import com.zto.devops.qc.client.model.relevantUser.query.ListMyTaskVO;
import com.zto.devops.qc.client.model.relevantUser.query.MyIssueQuery;
import com.zto.devops.qc.client.model.relevantUser.query.MyTaskVO;
import com.zto.devops.qc.client.service.relevantUser.RelevantUserQueryService;
import com.zto.devops.qc.client.service.relevantUser.model.MyTaskReq;
import com.zto.devops.qc.client.service.relevantUser.model.MyTaskResp;
import com.zto.devops.qc.client.service.relevantUser.model.RelevantConverter;
import com.zto.devops.qc.domain.converter.RelevantUserConverter;
import com.zto.devops.qc.domain.gateway.repository.IRelevantUserRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProductGroupRpcService;
import com.zto.devops.qc.domain.statemachine.IssueContext;
import com.zto.devops.qc.domain.statemachine.IssueStateMachineUtil;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.gateway.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务查询
 * 我的待办
 * 我的已办
 * 我创建的
 * <p>
 * 需求查询
 * 我的待办
 * 我的已办
 * 我创建的
 * 我的关注
 * 抄送我的
 * 迭代查询
 * 我的待办
 * 我的已办
 * 我创建的
 * 版本
 * 我的待办
 * 我的已办
 * 我创建的
 */

/**
 * @ClassName: RequirementGateway
 * @Description: 需求网关
 * @Author: cher
 * @Date: 2021/7/7 17:04
 * @menu 需求相关操作
 **/
@Service
@Slf4j
public class RelevantUserQueryServiceImpl extends GatewayBase implements RelevantUserQueryService {

//
//    @Autowired
//    private QueryGateway queryGateway;

    private final RelevantConverter relevantUserConverter = RelevantConverter.INSTANCE;
    private final RelevantUserConverter relevantUserConvertor = RelevantUserConverter.INSTANCE;

    @Autowired
    private IRelevantUserRepository iRelevantUserRepository;

    @Autowired
    private IProductGroupRpcService productGroupRpcService;

    @ZsmpApiOperation(description = "我的缺陷", gatewayApi = @GatewayApi(
            namespace = "luban", name = "workBench/queryMyIssue", description = "我的缺陷",timeout = 3000,
            featureList = @GatewayFeatureList(removeOutKey = true, packageResponse = false), responseDateFormat = "yyyy-MM-dd HH:mm:ss",
            tags = "3.19.0",
            webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true),
            auth = @GatewayAuth(enable = true, type = GatewayAuthType.SSO, box = true))
    )
    @Override
    public PageResult<MyTaskResp> queryMyIssue(MyTaskReq myTaskReq) {
        MyIssueQuery query = relevantUserConverter.convertTask(myTaskReq);
        GatewayContext.fillCurrentUser(query);
        query.setUserId(GatewayContext.currentUser().getUser_id());
        query.setDomain(DomainEnum.ISSUE);


        ListMyTaskVO listMyTaskVO = new ListMyTaskVO();
        TaskBaseParameter taskBaseParameter = relevantUserConvertor.convertTask(query);
        com.github.pagehelper.Page<TaskResultParameter> pageInfo = PageHelper.startPage(query.getPage(), query.getSize());
        if ("priority".equals(taskBaseParameter.getOrderField())) {
            taskBaseParameter.setOrderField("priorityIndex");
        }
        if ("gmt_create".equals(taskBaseParameter.getOrderField())) {
            taskBaseParameter.setOrderField("gmtCreate");
        }
        /*if (CollectionUtil.isEmpty(query.getStatus())) {
            List<String> statuses = getDefaultStatus(DomainEnum.ISSUE, query.getRelevantUserTypes(), TabEnum.WORK_BENCH);
            taskBaseParameter.setStatus(statuses);
        }*/
        if (StringUtil.isNotEmpty(query.getGroupId())) {
            String productCode = productGroupRpcService.getProductCodeByGroupId(query.getGroupId());
            if (StringUtil.isNotEmpty(productCode)) {
                taskBaseParameter.setProductCode(productCode);
            }else{
                return StatusCode.OK.build(new ArrayList<>(), myTaskReq.getPage(), myTaskReq.getSize(), 0);
            }
        }
        List<TaskResultParameter> taskResultParameters = iRelevantUserRepository.queryMyIssue(taskBaseParameter);
//        if (CollectionUtils.isEmpty(taskResultParameters)) {
//            return listMyTaskVO;
//        }
        List<MyTaskVO> myTaskVOS =
                taskResultParameters.stream().map(taskResultParameter -> relevantUserConvertor.convertIssue(taskResultParameter)).collect(Collectors.toList());
        if (query.getRelevantUserTypes().contains(RelevantUserTypeEnum.CURRENT_HANDLE_USER)
                ||query.getRelevantUserTypes().contains(RelevantUserTypeEnum.CREATOR)) {
            buildIssueButton(myTaskVOS, query.getTransactor());
        }
        buildWarn(myTaskVOS);
        listMyTaskVO.setTotal(pageInfo.getTotal());
        listMyTaskVO.setVos(myTaskVOS);


        List<MyTaskResp> resList = new ArrayList<>();
        if (null != listMyTaskVO && CollectionUtils.isNotEmpty(listMyTaskVO.getVos())) {
            resList = relevantUserConverter.convert(listMyTaskVO.getVos());
            for(MyTaskResp resp : resList){
                if(resp.getStatus().equals(IssueStatus.CLOSED.name())){
                    resp.setWarn("");
                    resp.setWarnDay(0);
                }
            }
        }
        return StatusCode.OK.build(resList, myTaskReq.getPage(), myTaskReq.getSize(), listMyTaskVO.getTotal());
    }

    private void buildWarn(List<MyTaskVO> myTaskVOS) {
        myTaskVOS.stream().forEach(myTaskVO -> {
            String warn = "";
            double divide = new BigDecimal(myTaskVO.getWarnDay()/24).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            double compareDay = IssuePriority.LOW.name().equals(myTaskVO.getPriority()) ? 1*24 : 0.5*24;
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.WAIT_FIX.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未处理";
                warn = "超时" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.FIXING.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未修复";
                warn = "超时" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.TESTING.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未验证";
                warn = "超时" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.REJECTED.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未处理";
                warn = "超时" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > 7*24) {
                // warn = "已逾期超过7天";
                warn = "超时" + divide + "天";
            }
            myTaskVO.setWarnDay(divide);
            myTaskVO.setWarn(warn);
        });
    }

    private void buildIssueButton(List<MyTaskVO> myTaskVOS, User user) {
        for (MyTaskVO p : myTaskVOS) {
            List<Event> events = IssueStateMachineUtil.getInstance()
                    .readyEvent(IssueStatus.getByName(p.getStatus()).toState(), IssueContext.convert(p, user.getUserId()));
            events.removeIf(event -> (event.getCode().equals(IssueEvent.START.toEvent().getCode()) && event.getName().equals(IssueEvent.START.toEvent().getName())
                    || event.getCode().equals(IssueEvent.END.toEvent().getCode()) && event.getName().equals(IssueEvent.END.toEvent().getName())));
            if (CollectionUtil.isEmpty(events)) {
                continue;
            }
            List<Button> buttonVOS = events.stream().map(e -> {
                Button buttonVO = new Button();
                buttonVO.setCode(e.getCode());
                buttonVO.setName(e.getName());
                return buttonVO;
            }).filter(b -> "FIX".equals(b.getCode()) || "DELAY".equals(b.getCode()) || "DELIVER".equals(b.getCode())
                    || "TEST_PASS_CLOSE".equals(b.getCode()) || "RETURN".equals(b.getCode()) || "CONFIRM_CLOSE".equals(b.getCode()))
                    .collect(Collectors.toList());
            p.setButtonVOs(buttonVOS);
        }
    }

}
