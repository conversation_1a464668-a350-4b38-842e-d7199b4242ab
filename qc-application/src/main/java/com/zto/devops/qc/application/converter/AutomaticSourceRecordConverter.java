package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.testmanager.cases.command.MoveAutomaticRecordCommand;
import com.zto.devops.qc.client.model.testmanager.cases.query.MoveModuleReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface AutomaticSourceRecordConverter {


    @Mapping(target = "aggregateId", source = "code")
    MoveAutomaticRecordCommand convertor(MoveModuleReq req);
}
