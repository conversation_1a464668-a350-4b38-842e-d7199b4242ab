package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseButtonEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseEditFieldEnum;
import com.zto.devops.qc.client.model.testmanager.cases.command.*;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.*;
import com.zto.devops.qc.client.service.testmanager.cases.model.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring")
public interface TestcaseAdapterConverter {

    ExecuteCaseResp converter(ExecuteCaseVO vo);

    PageXmindDetailQuery converterPageXmindDetailQuery(ListXmindDetailReq req);

    void converter(PageAutomaticTaskGroupReq req, @MappingTarget PageAutomaticTaskGroupQuery query);

    void converter(ListTestcaseCodeReq req, @MappingTarget ListTestcaseCodeQuery query);

    List<ListXmindDetailResp> convertList(Collection<ListXmindDetailVO> list);

    void converter(ListTestcaseModuleReq req, @MappingTarget ListTestcaseModuleQuery query);

    ListTestcaseModuleResp converter(ListTestcaseModuleVO vo);

    void converterPage(ListTestcaseReq req, @MappingTarget PageTestcaseQuery query);

    void converterPage(ListTestcaseReq req, @MappingTarget TestcaseQuery query);

    List<ListTestcaseResp> converterList(Collection<ListTestcaseVO> list);

    ListTestcaseResp converter(ListTestcaseVO vo);

    @Mapping(target = "code", expression = "java(button.name())")
    @Mapping(target = "name", expression = "java(button.getDesc())")
    ButtonVO converter(TestcaseButtonEnum button);

    PageExecuteRecordQuery converter(ListTestcaseExecuteRecordReq req);

    @Mapping(target = "aggregateId", expression = "java(com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(\"TEST_CASE\"))")
    AddTestcaseCommand convertor(AddTestcaseReq req);

    XmindCaseAddCommand converter(XmindCaseAddReq req);

    XmindCaseEditCommand converter(XmindCaseEditReq req);

    XmindFilterQuery convert(ListXmindDetailReq req);

    void converterQuery(ListTestcaseReq req, @MappingTarget ListTestcaseExpQuery query);

    @Mapping(target = "aggregateId", source = "code")
    EditTestcaseCommand convertEdit(AddTestcaseReq req);

    PlanCaseResp converter(PlanCaseVO vo);

    @Mapping(target = "code", expression = "java(editField.name())")
    @Mapping(target = "name", expression = "java(editField.getDesc())")
    FieldVO converter(TestcaseEditFieldEnum editField);

    CheckCreatorOrDutyUserResp converter(CheckCreatorOrDutyUserVO vo);

    CheckTestcaseStatusResp converter(CheckTestcaseStatusVO vo);

    TestcaseIssueResp converter(TestcaseIssueVO vo);

    TestcaseRequirementResp converter(TestcaseRequirementVO vo);

    TestcaseResp converter(TestcaseVO vo);

    @Mapping(target = "automaticTaskCode", source = "code")
    void converter(ListExecuteCaseReq req, @MappingTarget ListExecuteCaseQuery query);

    ListExecuteCaseResp converter(ListExecuteCaseVO vo);

    @Mapping(target = "aggregateId", source = "code")
    EditTestcaseTitleCommand convertor(EditTestcaseTitleReq req);

    @Mapping(target = "aggregateId", source = "code")
    MoveModuleCommand convertor(MoveModuleReq req);

    void converter(ListExecuteEnvReq req, @MappingTarget ListExecuteEnvQuery query);

}
