package com.zto.devops.qc.application.converter;

import com.zto.devops.qc.client.model.testmanager.email.entity.DetailEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.EmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.VersionEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.query.DetailEmailQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.PageEmailQuery;
import com.zto.devops.qc.client.service.testmanager.email.model.*;
import org.mapstruct.Mapper;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring")
public interface EmailQueryConverter {
    DetailEmailQuery convert(DetailEmailReq req);

    List<EmailResp> convertor(Collection<VersionEmailVO> versionEmailVOS);

    DetailEmailResp convert(DetailEmailVO vo);

    PageEmailQuery convert(PageEmailReq req);

    List<PageEmailResp> convert(List<EmailVO> emailVOS);
}
