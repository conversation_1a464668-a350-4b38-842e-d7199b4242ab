package com.zto.devops.qc.application.filter;

import com.alibaba.dubbo.common.extension.Activate;
import com.zto.devops.framework.application.filter.auth.AbstractAuthFilter;
import com.zto.devops.framework.application.filter.auth.exception.AuthErrorException;
import com.zto.devops.framework.client.entity.UserInfo;
import com.zto.devops.framework.client.enums.SystemErrorCodeEnum;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.rpc.MemberTypeEnum;
import com.zto.devops.qc.client.model.dto.AutomaticSchedulerEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestPlanEntityDO;
import com.zto.devops.qc.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.qc.client.model.rpc.product.query.ListProductMemberByPIdQuery;
import com.zto.devops.qc.client.model.rpc.project.VersionVO;
import com.zto.devops.qc.domain.gateway.repository.AutomaticSchedulerRepository;
import com.zto.devops.qc.domain.gateway.repository.AutomaticSourceRecordRepository;
import com.zto.devops.qc.domain.gateway.repository.CoverageRepository;
import com.zto.devops.qc.domain.gateway.repository.ITmTestPlanRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Activate(group = {"provider"}, order = Integer.MAX_VALUE)
public class AuthFilter extends AbstractAuthFilter {

    private IProductRpcService productRpcService;
    private IProjectRpcService projectRpcService;
    private CoverageRepository coverageRepository;
    private AutomaticSchedulerRepository automaticSchedulerRepository;
    private AutomaticSourceRecordRepository automaticSourceRecordRepository;
    private ITmTestPlanRepository testPlanRepository;

    public void setProductRpcService(IProductRpcService productRpcService) {
        this.productRpcService = productRpcService;
    }

    public void setProjectRpcService(IProjectRpcService projectRpcService) {
        this.projectRpcService = projectRpcService;
    }

    public void setCoverageRepository(CoverageRepository coverageRepository) {
        this.coverageRepository = coverageRepository;
    }

    public void setAutomaticSchedulerRepository(AutomaticSchedulerRepository automaticSchedulerRepository) {
        this.automaticSchedulerRepository = automaticSchedulerRepository;
    }

    public void setAutomaticSourceRecordRepository(AutomaticSourceRecordRepository automaticSourceRecordRepository) {
        this.automaticSourceRecordRepository = automaticSourceRecordRepository;
    }

    public void setITmTestPlanRepository(ITmTestPlanRepository testPlanRepository) {
        this.testPlanRepository = testPlanRepository;
    }

    @Override
    protected boolean hasAuth(String type, Object targetCode, UserInfo userInfo) {
        switch (type) {
            case AuthTypeConstant.RECORD_CODE:
                return hasRecordAuth((String) targetCode, userInfo);
            case AuthTypeConstant.PRODUCT_CODE:
                return hasProductAuth((String) targetCode, userInfo);
            case AuthTypeConstant.PLAN_CODE:
                return hasPlanAuth((String) targetCode, userInfo);
            case AuthTypeConstant.APP_ID:
                return hasAppIdAuth((String) targetCode, userInfo);
            case AuthTypeConstant.SCHEDULER_CODE:
                return hasSchedulerAuth((String) targetCode, userInfo);
            case AuthTypeConstant.VERSION_CODE:
                return hasVersionAuth((String) targetCode, userInfo);
            default:
                return true;
        }
    }

    private boolean hasRecordAuth(String code, UserInfo userInfo) {
        AutomaticSourceRecordEntityDO entityDO = automaticSourceRecordRepository.selectByPrimaryKey(code);
        if (null == entityDO || StringUtil.isEmpty(entityDO.getProductCode())) {
            throw new AuthErrorException(SystemErrorCodeEnum.AUTH_CODE_NOT_FOUND);
        }
        return hasProductAuth(entityDO.getProductCode(), userInfo);
    }

    private boolean hasVersionAuth(String code, UserInfo userInfo) {
        VersionVO versionVO = projectRpcService.findVersionQuery(code);
        if (null == versionVO || StringUtil.isEmpty(versionVO.getProductCode())) {
            throw new AuthErrorException(SystemErrorCodeEnum.AUTH_CODE_NOT_FOUND);
        }
        return hasProductAuth(versionVO.getProductCode(), userInfo);
    }

    private boolean hasAppIdAuth(String code, UserInfo userInfo) {
        // 1. 查询appId
        // 2. 获取appId关联的产品
        // 3. 判断该用户是否具备该产品的权限
        return false;
    }

    private boolean hasPlanAuth(String code, UserInfo userInfo) {
        TmTestPlanEntityDO tmTestPlanEntityDO = testPlanRepository.selectTmTestPlanByCode(code);
        if (null == tmTestPlanEntityDO || StringUtil.isEmpty(tmTestPlanEntityDO.getProductCode())) {
            throw new AuthErrorException(SystemErrorCodeEnum.AUTH_CODE_NOT_FOUND);
        }
        return hasProductAuth(tmTestPlanEntityDO.getProductCode(), userInfo);
    }

    private boolean hasSchedulerAuth(String code, UserInfo userInfo) {
        AutomaticSchedulerEntityDO automaticSchedulerEntityDO = automaticSchedulerRepository.selectByPrimaryKey(code);
        if (null == automaticSchedulerEntityDO || StringUtil.isEmpty(automaticSchedulerEntityDO.getProductCode())) {
            throw new AuthErrorException(SystemErrorCodeEnum.AUTH_CODE_NOT_FOUND);
        }
        return hasProductAuth(automaticSchedulerEntityDO.getProductCode(), userInfo);
    }

    private boolean hasProductAuth(String code, UserInfo userInfo) {
        if (null == userInfo) {
            return false;
        }
        // 超管校验
        if (checkSuperUser(userInfo, code)) {
            return true;
        }
        List<ProductMemberVO> list = productRpcService.findProductMemberByIdQuery(code, null);
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        list = list.stream().filter(t -> t.getUserId().equals(userInfo.getUser_id())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        return true;
    }

    private Boolean checkSuperUser(UserInfo userInfo, String productCode) {
        ListProductMemberByPIdQuery productQuery = new ListProductMemberByPIdQuery();
        Long userId = userInfo.getUser_id();
        List<String> memberTypes = Arrays.asList(MemberTypeEnum.SUPPER.name());
        productQuery.setProductCode(productCode);
        productQuery.setMemberTypes(memberTypes);
        return productRpcService.checkProductPermission(userId, productQuery);
    }
}
