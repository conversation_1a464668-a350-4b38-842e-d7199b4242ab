package com.zto.devops.qc.application.job;

import com.zto.devops.qc.domain.service.AutomaticTaskCommandDomainService;
import com.zto.zss.common.exception.ZSSException;
import com.zto.zss.worker.processor.BasicProcessor;
import com.zto.zss.worker.processor.model.ProcessResult;
import com.zto.zss.worker.processor.model.TaskContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component("autoTestApiExecuteJob")
public class AutoTestApiExecuteJob implements BasicProcessor {

    @Autowired
    private AutomaticTaskCommandDomainService automaticTaskCommandDomainService;

    @Override
    public ProcessResult process(TaskContext context) throws ZSSException {
        automaticTaskCommandDomainService.executeApiTestTask();
        return ProcessResult.success("OK");
    }
}
