package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.entity.UserInfo;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.application.converter.TestcaseAdapterConverter;
import com.zto.devops.qc.client.model.testmanager.cases.command.ExecuteCallbackCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.TerminateAutomaticTaskCommand;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteTagVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.PageAutomaticTaskGroupVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListExecuteEnvQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageAutomaticTaskGroupQuery;
import com.zto.devops.qc.client.service.testmanager.cases.model.*;
import com.zto.devops.qc.client.service.testmanager.task.IAutomaticTaskService;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.service.AutomaticTaskCommandDomainService;
import com.zto.devops.qc.domain.service.AutomaticTaskQueryDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayWebAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
@Service
@ZsmpService(name = "自动化任务接口目录")
public class AutomaticTaskServiceImpl implements IAutomaticTaskService {

    @Autowired
    private AutomaticTaskQueryDomainService automaticTaskQueryDomainService;

    @Autowired
    private AutomaticTaskCommandDomainService automaticTaskCommandDomainService;

    @Autowired
    private TestcaseAdapterConverter testcaseAdapterConverter;

    @Autowired
    private IPipelineRpcService iPipelineRpcService;

    @ZApiOperation(description = "测试管理-分页查询自动化任务执行记录（按父任务分组）",
            apiName = "tm/cases/pageAutomaticTaskGroup",
            timeout = 10000,
            namespace = "luban",
            responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<AutomaticTaskGroupResp> pageAutomaticTaskGroup(PageAutomaticTaskGroupReq req) {
        if (StringUtils.isBlank(req.getProductCode())) {
            throw new ServiceException("产品不能为空！");
        }
        PageAutomaticTaskGroupQuery query = new PageAutomaticTaskGroupQuery();
        testcaseAdapterConverter.converter(req, query);
        PageAutomaticTaskGroupVO page = automaticTaskQueryDomainService.pageAutomaticTaskGroup(query);
        List<AutomaticTaskGroupResp> resp = AutomaticTaskGroupResp.buildSelf(page.getList());
        return StatusCode.OK.build(resp, page.getPage(), page.getSize(), page.getTotal());
    }

    @ZApiOperation(description = "测试管理-查询自动化任务执行空间",
            apiName = "tm/cases/listExecuteEnv", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<ListExecuteEnvResp> listExecuteEnv(ListExecuteEnvReq req) {
        if (StringUtils.isBlank(req.getProductCode())) {
            throw new ServiceException("产品不能为空！");
        }
        ListExecuteEnvQuery query = new ListExecuteEnvQuery();
        testcaseAdapterConverter.converter(req, query);
        List<ExecuteTagVO> tagVOS = automaticTaskQueryDomainService.listExecuteEnv(query);
        return StatusCode.OK.build(ListExecuteEnvResp.buildSelf(tagVOS));
    }

    @ZApiOperation(description = "测试管理-执行自动化任务", apiName = "tm/cases/execute", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> execute(ExecuteAutomaticTaskReq req) {
        if (CollectionUtils.isEmpty(req.getTestcaseCodeList())) {
            throw new ServiceException("用例列表不能为空！");
        }
        if (null == req.getTrigMode()) {
            throw new ServiceException("触发方式不能为空！");
        }
        if (null == req.getProductCode()) {
            throw new ServiceException("产品code不能为空！");
        }
        if (null == req.getAutoEnv()) {
            throw new ServiceException("动态多环境标识不能为空！");
        }
        UserInfo currentUser = GatewayContext.currentUser();
        automaticTaskCommandDomainService.executeAutoTask(currentUser, req);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-执行自动化任务回调", apiName = "tm/cases/executeCallback", namespace = "luban")
    @Override
    public Result<Void> executeCallback(ExecuteCallbackReq req) {
        if (StringUtils.isBlank(req.getCode())) {
            throw new ServiceException("自动化任务不能为空！");
        }
        ExecuteCallbackCommand command = new ExecuteCallbackCommand(req.getCode());
        command.setBuildId(req.getBuildId());
        command.setStatus(req.getStatus());
        command.setOssPath(req.getOssPath());
        automaticTaskCommandDomainService.callback(command);
        return StatusCode.OK.build();
    }

    @ZsmpApiOperation(
            description = "测试管理-终止自动化任务",
            gatewayApi =
            @GatewayApi(
                    namespace = "luban",
                    name = "tm/cases/terminate",
                    description = "测试管理-终止自动化任务",
                    tags = "3.20.0",
                    webAuth = @GatewayWebAuth(needSession = true, needUserInfoContext = true)))
    @Override
    public Result<Void> terminate(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ServiceException("自动化任务不能为空！");
        }
        TerminateAutomaticTaskCommand command = new TerminateAutomaticTaskCommand(code);
        GatewayContext.fillCurrentUser(command);
        automaticTaskCommandDomainService.terminateAutomaticTaskCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-终止自动化任务(批量)", apiName = "tm/cases/batchTerminate", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchTerminate(BatchTerminateTaskReq req) {
        if (null == req || StringUtils.isBlank(req.getCode())) {
            throw new ServiceException("自动化任务不能为空！");
        }
        if (null == req.getParentFlag()) {
            throw new ServiceException("是否父任务不能为空！");
        }
        User user = GatewayContext.currentUser().toSimpleUser();
        automaticTaskCommandDomainService.batchTerminateAutoTask(req.getCode(), req.getParentFlag(), user);
        return StatusCode.OK.build();
    }

    @Override
    public void terminatePipelineTask(String taskId, User user) {
        log.info("terminatePipelineTask >>> {}, {}", taskId, JSON.toJSONString(user));
        if (StringUtils.isEmpty(taskId)) {
            throw new ServiceException("自动化任务id不能为空！");
        }
        if (null == user) {
            user = new User(0L, "系统");
        }
        automaticTaskCommandDomainService.batchTerminateAutoTask(taskId, true, user);
    }

    @ZApiOperation(description = "测试管理-自动化用例执行结果回调", apiName = "tm/cases/tcExecuteResultCallback", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> tcExecuteResultCallback(ExecuteCallbackReq req) {
        if (StringUtils.isBlank(req.getCode())) {
            throw new ServiceException("自动化任务不能为空！");
        }
        log.info(">>>ExecuteCallbackReq:{}", JSON.toJSONString(req));
        ExecuteCallbackCommand command = new ExecuteCallbackCommand(req.getCode());
        command.setBuildId(req.getBuildId());
        command.setStatus(req.getStatus());
        command.setOssPath(req.getOssPath());
        automaticTaskCommandDomainService.tcExecuteResultCallback(command);
        return StatusCode.OK.build();
    }

    @Override
    @ZApiOperation(
            description = "测试管理-执行定时任务（临时）",
            apiName = "tm/cases/executeAutoTestCronTask",
            namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    public Result<Void> executeAutoTestCronTask() {
        automaticTaskCommandDomainService.executeAutoTestCronTask();
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-自动化任务重试", apiName = "tm/cases/retryTask", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> retryTask(RetryAutomaticTaskReq req) {
        if (CollectionUtils.isEmpty(req.getTaskCodeList())) {
            throw new ServiceException("自动化任务不能为空！");
        }
        automaticTaskCommandDomainService.retryAutomaticTask(req.getTaskCodeList(), GatewayContext.currentUser());
        return StatusCode.OK.build();
    }
}
