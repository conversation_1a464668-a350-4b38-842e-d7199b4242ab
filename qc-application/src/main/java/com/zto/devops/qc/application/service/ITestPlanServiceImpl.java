package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.baidu.unbiz.fluentvalidator.ComplexResult;
import com.baidu.unbiz.fluentvalidator.FluentValidator;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.Page;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.FluentException;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.common.validator.LengthValidator;
import com.zto.devops.framework.common.validator.NotBlankValidator;
import com.zto.devops.framework.common.validator.NotNullValidator;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.application.converter.TmTestPlanAdapterConvertor;
import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.devops.qc.client.enums.testmanager.plan.*;
import com.zto.devops.qc.client.model.report.entity.PlanCaseButtonVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ButtonVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.FindSortedPlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindSortedPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.TestPlanToSendMailQuery;
import com.zto.devops.qc.client.model.testmanager.plan.command.*;
import com.zto.devops.qc.client.model.testmanager.plan.entity.*;
import com.zto.devops.qc.client.model.testmanager.plan.query.*;
import com.zto.devops.qc.client.service.issue.model.IssueResp;
import com.zto.devops.qc.client.service.plan.ITestPlanService;
import com.zto.devops.qc.client.service.plan.model.*;
import com.zto.devops.qc.domain.service.TmTestPlanCommandDomainService;
import com.zto.devops.qc.domain.service.TmTestPlanQueryDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.ZsmpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@ZsmpService(name = "测试计划接口目录")
public class ITestPlanServiceImpl extends GatewayBase implements ITestPlanService {

    @Autowired
    private TmTestPlanAdapterConvertor tmTestPlanAdapterConvertor;

    @Autowired
    private TmTestPlanQueryDomainService tmTestPlanQueryDomainService;

    @Autowired
    private TmTestPlanCommandDomainService tmTestPlanCommandDomainService;


    @ZApiOperation(description = "测试计划详情",
            apiName = "tm/plan/testPlanDetail", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<TmTestPlanVO> testPlanDetail(TestPlanDetailReq req) {
        FindTmTestPlanQuery query = new FindTmTestPlanQuery(req.getCode());
        GatewayContext.fillCurrentUser(query);
        TmTestPlanVO vo = tmTestPlanQueryDomainService.testPlanDetail(query);
        if (vo != null) {
            tmTestPlanQueryDomainService.dealPlanOperate(vo.getProductCode(), GatewayContext.currentUser().getSsoUserId(), Collections.singletonList(vo));
        }
        return StatusCode.OK.build(vo);
    }

    @ZApiOperation(description = "获取测试计划的提示信息", apiName = "tm/plan/tip", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<TestPlanTipVO> testPlanTip(TestPlanDetailReq req) {
        FindTmTestPlanTipQuery query = new FindTmTestPlanTipQuery();
        query.setPlanCode(req.getCode());
        return StatusCode.OK.build(tmTestPlanQueryDomainService.findTmTestPlanTipQuery(query));
    }

    @ZApiOperation(description = "查询相关计划、报告接口",
            apiName = "tm/plan/relatedPlanReport", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<RelatedPlanReportResp> relatedTest(RelatedTestReq req) {
        log.info("查询相关计划、报告,RelatedTestReq:{}", JSONObject.toJSONString(req));
        if (StringUtil.isAllEmpty(req.getPlanCode(), req.getReportCode())) {
            throw new ServiceException("请求参数不正确！");
        }
        RelatedPlanReportQuery query = tmTestPlanAdapterConvertor.convertor(req);
        RelatedPlanReportVO relatedVO = tmTestPlanQueryDomainService.relatedTest(query);
        RelatedPlanReportResp resp = tmTestPlanAdapterConvertor.convertor(relatedVO);
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "测试计划缺陷tab查询接口",
            apiName = "tm/plan/planIssue", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<PlanIssueResp> getPlanIssue(TestPlanIssueReq req) {
        PagePlanIssueQuery query = tmTestPlanAdapterConvertor.convertor(req);
        query.setCurrentUserId(GatewayContext.currentUser().getUser_id());
        PageCaseIssueVO caseIssueVO = tmTestPlanQueryDomainService.getPlanIssue(query);
        List<PlanIssueResp> list = tmTestPlanAdapterConvertor.convertor(caseIssueVO.getList());
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(IssueResp::setEnumrationDesc);
        }
        return StatusCode.OK.build(list, req.getPage(), req.getSize(), caseIssueVO.getTotal());
    }

    @ZApiOperation(description = "发送邮件校验接口", apiName = "tm/plan/checkPlanEmail", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> checkPlanEmail(SendPlanCheckReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("所属产品"))
                .on(req.getProductName(), new NotBlankValidator("所属产品名称"))
                .on(req.getVersionCode(), new NotBlankValidator("关联版本"))
                .on(req.getVersionName(), new NotBlankValidator("关联版本名称"))
                .on(req.getPlanCode(), new NotBlankValidator("计划编号"))
                .on(req.getPlanName(), new NotBlankValidator("计划名称"))
                .on(req.getPlanName(), new LengthValidator(-1, 60, "计划名称"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (req.getPlanType().equals(TestPlanNewTypeEnum.TEST_PLAN)) {
            checkTestPlan(req);
        } else if (req.getPlanType().equals(TestPlanNewTypeEnum.SAFETY_TEST)) {
            checkSafePlan(req);
        } else {
            checkMobilePlan(req);
        }
        return StatusCode.OK.build();
    }

    private void checkTestPlan(SendPlanCheckReq req) {
        ComplexResult commonPlan = checkAll()
                .on(req.getTestStrategy(), new NotNullValidator("测试策略"))
                .on(req.getExploratoryTest(), new NotNullValidator("探索性测试"))
                .on(req.getStaticAnalysis(), new NotNullValidator("静态分析"))
                .on(req.getSecurityScan(), new NotNullValidator("安全扫描"))
                .on(req.getMobileSpecialTest(), new NotNullValidator("移动专项测试"))
                .on(req.getPlanDirectorId(), new NotNullValidator("测试计划负责人"))
                .on(req.getAccessDate(), new NotNullValidator("计划提测日期"))
                .on(req.getAccessDatePartition(), new NotNullValidator("计划提测时段"))
                .on(req.getPermitDate(), new NotNullValidator("计划准出日期"))
                .on(req.getPermitDatePartition(), new NotNullValidator("计划准出时段"))
                .on(req.getTesterNum(), new NotNullValidator("测试人数"))
                .on(req.getDeveloperNum(), new NotNullValidator("开发人数"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!commonPlan.isSuccess()) {
            throw new FluentException(commonPlan.getErrors());
        }
    }

    private void checkSafePlan(SendPlanCheckReq req) {
        ComplexResult safePlan = checkAll()
                .on(req.getLastTestDate(), new NotNullValidator("最晚测试日期"))
                .on(req.getPriority(), new NotNullValidator("优先级"))
                .on(req.getPermissionsTest(), new NotNullValidator("权限测试"))
                .on(req.getTestInformation(), new NotBlankValidator("测试信息"))
                .on(req.getTestInformation(), new LengthValidator(-1, 500, "测试信息"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!safePlan.isSuccess()) {
            throw new FluentException(safePlan.getErrors());
        }
        if (req.getPermissionsTest()) {
            if (StringUtil.isEmpty(req.getPermissionsTestInformation())) {
                throw new ServiceException("权限测试信息不能为空");
            }
            if (req.getPermissionsTestInformation().length() > 500) {
                throw new ServiceException("权限测试信息长度不能超过500");
            }
        }
    }

    private void checkMobilePlan(SendPlanCheckReq req) {
        ComplexResult mobilePlan = checkAll()
                .on(req.getRelationPlanCode(), new NotBlankValidator("关联计划编号"))
                .on(req.getAccessDate(), new NotNullValidator("计划提测日期"))
                .on(req.getAccessDatePartition(), new NotNullValidator("计划提测时段"))
                .on(req.getPermitDate(), new NotNullValidator("计划准出日期"))
                .on(req.getPermitDatePartition(), new NotNullValidator("计划准出时段"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!mobilePlan.isSuccess()) {
            throw new FluentException(mobilePlan.getErrors());
        }
    }

    @ZApiOperation(description = "查询测试计划关联用例-结果统计",
            apiName = "tm/plan/planCaseResultCount", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<TestPlanCaseResultVO> planCaseResultCount(TestPlanCaseResultCountReq req) {
        PlanCaseResultCountQuery query = new PlanCaseResultCountQuery();
        query.setPlanCode(req.getCode());
        query.setTestStage(req.getTestStage());
        TestPlanCaseResultVO vo = tmTestPlanQueryDomainService.planCaseResultCount(query);
        return StatusCode.OK.build(vo);
    }


    @ZApiOperation(description = "计划分页查询列表",
            apiName = "tm/plan/pageTestPlanList", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<TmTestPlanVO> pageTestPlanList(PlanPageReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        PlanListQuery query = tmTestPlanAdapterConvertor.convertor(req);
        PageTmTestPlanVO page = tmTestPlanQueryDomainService.pageTestPlanList(query);
        //计划操作处理
        tmTestPlanQueryDomainService.dealPlanOperate(req.getProductCode(), GatewayContext.currentUser().getSsoUserId(), page.getPlanList());
        return StatusCode.OK.build(page.getPlanList(), req.getPage(), req.getSize(), page.getTotal());
    }

    @ZApiOperation(description = "懒加载查询测试计划关联用例",
            apiName = "tm/plan/pageListPlanCase", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<PagePlanTestcaseResp> pageListPlanCase(PageTestPlanCaseListReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getCode(), new NotBlankValidator("计划code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        PageListPlanCaseQuery query = tmTestPlanAdapterConvertor.convertor(req);
        GatewayContext.fillCurrentUser(query);

        Page page = tmTestPlanQueryDomainService.pageListPlanCase(query);
        List<PagePlanTestcaseResp> respList = new ArrayList<>();
        if (null != page && CollectionUtil.isNotEmpty(page.getList())) {
            respList = tmTestPlanAdapterConvertor.converterList(page.getList());
        }
        return StatusCode.OK.build(respList, req.getPage(), req.getSize(), page.getTotal());
    }

    @ZApiOperation(description = "查询测试计划关联分组下所有用例",
            apiName = "tm/plan/listTestPlanCaseCode", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<String>> listTestPlanCaseCode(TestPlanCaseCodeListReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getCode(), new NotBlankValidator("计划code"))
                .on(req.getParentCode(), new NotBlankValidator("父节点code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ListPlanCaseCodeQuery query = tmTestPlanAdapterConvertor.convertor(req);
        GatewayContext.fillCurrentUser(query);
        List<String> codeList = tmTestPlanQueryDomainService.listTestPlanCaseCode(query);
        return StatusCode.OK.build(codeList);
    }

    @ZApiOperation(description = "查询测试计划与阶段",
            apiName = "tm/plan/ListPlanPhase", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<PagePlanPhaseResp> listPlanPhase(@Valid ListPlanPhaseReq req) {
        ListPlanPhaseQuery query = tmTestPlanAdapterConvertor.convertor(req);
        PagePlanPhaseVO pageVO = tmTestPlanQueryDomainService.listPlanPhase(query);
        List<PagePlanPhaseResp> list = tmTestPlanAdapterConvertor.convertorPlaseList(pageVO.getList());
        return StatusCode.OK.build(list, req.getPage(), req.getSize(), pageVO.getTotal());
    }

    @ZApiOperation(description = "查询测试计划用例上一条/下一条",
            apiName = "tm/plan/findSortedPlanCase", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<FindSortedPlanCaseResp> findSortedPlanCase(FindSortedPlanCaseReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCaseCode(), new NotBlankValidator("用例"))
                .on(req.getPlanCode(), new NotBlankValidator("测试计划"))
                .on(req.getTestStage(), new NotNullValidator("测试阶段"))
                .on(req.getTestcaseType(), new NotNullValidator("测试用例类型"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        FindSortedPlanCaseQuery query = new FindSortedPlanCaseQuery();
        tmTestPlanAdapterConvertor.convertor(req, query);
        FindSortedPlanCaseVO vo = tmTestPlanQueryDomainService.findSortedPlanCase(query);
        FindSortedPlanCaseResp resp = new FindSortedPlanCaseResp();
        resp.setPrevious(vo.getPrevious());
        resp.setNext(vo.getNext());
        return StatusCode.OK.build(resp);
    }

    @ZApiOperation(description = "分页查询关联计划列表",
            apiName = "tm/plan/getPagePlan", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public PageResult<PageTestPlanResp> getPagePlan(PageTestPlanReq req) {
        log.info("查询关联计划列表,PageTestPlanReq:{}", JSONObject.toJSONString(req));
        PageTestPlanQuery query = tmTestPlanAdapterConvertor.convertor(req);
        PageTestPlanVO pageVO = tmTestPlanQueryDomainService.getPagePlan(query);
        List<PageTestPlanResp> list = tmTestPlanAdapterConvertor.convertorList(pageVO.getList());
        return StatusCode.OK.build(list, req.getPage(), req.getSize(), pageVO.getTotal());
    }

    @ZApiOperation(description = "版本概览-测试计划列表查询",
            apiName = "tm/plan/getVersionPlan", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<TestPlanResp>> getVersionPlan(VersionPlanReq req) {
        ComplexResult cr = checkAll()
                .on(req.getVersionCode(), new NotBlankValidator("版本code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        VersionPlanQuery query = tmTestPlanAdapterConvertor.convertor(req);
        List<TmTestPlanVO> versionPlanVOList = tmTestPlanQueryDomainService.getVersionPlan(query);
        List<TestPlanResp> testPlanRespList = tmTestPlanAdapterConvertor.convertor(versionPlanVOList);
        return StatusCode.OK.build(testPlanRespList);
    }

    @ZApiOperation(description = "查询测试计划关联用例",
            apiName = "tm/plan/listPlanCase", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<TestPlanCaseVO>> listPlanCase(TestPlanCaseListReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .on(req.getCode(), new NotBlankValidator("计划code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ListPlanCaseQuery query = tmTestPlanAdapterConvertor.convertor(req);
        GatewayContext.fillCurrentUser(query);
        List<TestPlanCaseVO> list = tmTestPlanQueryDomainService.listPlanCase(query);
        return StatusCode.OK.build(list);
    }

    @ZApiOperation(description = "查询测试计划关联用例-操作按钮",
            apiName = "tm/plan/listPlanCaseButtons", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<PlanCaseButtonVO>> listPlanCaseButtons(String planCode, TestPlanStageEnum testStage) {
        ComplexResult cr = checkAll()
                .on(planCode, new NotBlankValidator("计划code"))
                .on(testStage, new NotNullValidator("测试阶段"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (ObjectUtils.isEmpty(testStage)) {
            throw new ServiceException("测试阶段不能为空");
        }
        List<PlanCaseButtonVO> buttonVOS = tmTestPlanQueryDomainService.dealPlanStageOperate(planCode,
                GatewayContext.currentUser().getUser_id(),
                testStage);
        return StatusCode.OK.build(buttonVOS);
    }

    @ZApiOperation(description = "查询测试计划关联用例-分组",
            apiName = "tm/plan/listPlanCaseModule", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<TestPlanCaseVO>> listPlanCaseModule(TestPlanCaseModuleListReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCode(), new NotBlankValidator("计划code"))
                .on(req.getProductCode(), new NotBlankValidator("产品code"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ListPlanCaseModuleQuery query = tmTestPlanAdapterConvertor.convertor(req);
        GatewayContext.fillCurrentUser(query);
        List<TestPlanCaseVO> list = tmTestPlanQueryDomainService.listPlanCaseModule(query);
        return StatusCode.OK.build(list);
    }

    @ZApiOperation(description = "测试计划三阶段状态变更接口",
            apiName = "tm/plan/editStageStatus", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> changeStageStatus(EditTestPlanStageReq req) {
        if (StringUtil.isEmpty(req.getPlanCode())) {
            throw new ServiceException("测试计划code不能为空！");
        }
        // 普通测试计划&自定义/全流程&测试周期>2
        // 冒烟阶段-开始测试按钮 校验是否发过邮件
        if (req.getType().equals(TestPlanButttonTypeEnum.START)
                && req.getStage().equals(TestPlanStageEnum.SMOKE_TEST)) {
            checkRequestParameter(req.getPlanCode());
        }
        EditPlanStageStatusCommand command = tmTestPlanAdapterConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        tmTestPlanCommandDomainService.changeStageStatus(command);
        return StatusCode.OK.build();
    }

    private void checkRequestParameter(String planCode) {
        FindTmTestPlanQuery query = new FindTmTestPlanQuery(planCode);
        GatewayContext.fillCurrentUser(query);
        TmTestPlanVO vo = tmTestPlanQueryDomainService.testPlanDetail(query);
        if (vo.getType().equals(TestPlanNewTypeEnum.TEST_PLAN)) {
            if (vo.getTestStrategy().equals(TestPlanStrategyEnum.STANDARD_TEST)
                    || (vo.getIsSimpleTest() != null && !vo.getIsSimpleTest())) {
                checkTestPlanEmail(vo);
            }
        }
    }

    private void checkTestPlanEmail(TmTestPlanVO vo) {
        TestPlanToSendMailQuery query = new TestPlanToSendMailQuery();
        query.setPlanCode(vo.getCode());
        query.setPlanType(TestPlanNewTypeEnum.TEST_PLAN.name());
        Boolean checkResult = tmTestPlanQueryDomainService.checkTestPlanEmail(query);
        if (!checkResult) {
            throw new ServiceException("请先发送计划邮件再开始测试！");
        }
    }

    @ZApiOperation(description = "测试计划状态变更接口", apiName = "tm/plan/editStatus", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> changePlanStatus(EditTestPlanStatusReq req) {
        if (StringUtil.isEmpty(req.getPlanCode())) {
            throw new ServiceException("测试计划code不能为空！");
        }
        if (StringUtil.isEmpty(req.getStatus().name())) {
            throw new ServiceException("测试计划变更状态不能为空！");
        }
        // 普通测试计划&自定义/全流程&测试周期>2
        // 冒烟阶段-开始测试按钮 校验是否发过邮件
        if (req.getStatus().equals(TestPlanNewStatusEnum.IN_PROGRESS)) {
            checkRequestParameter(req.getPlanCode());
        }
        EditPlanStatusCommand command = tmTestPlanAdapterConvertor.convertor(req);
        command.setSecurityTestResult(SecurityTestResult.NOOPERATE);
        GatewayContext.fillCurrentUser(command);
        tmTestPlanCommandDomainService.changePlanStatus(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "设置测试计划用例阶段接口",
            apiName = "tm/plan/modifyCaseTestPlanStage", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> modifyCaseTestStage(@Valid ModifyCaseTestStageReq req) {
        if (CollectionUtil.isEmpty(req.getCaseIds())) {
            throw new ServiceException("用例code不能为空");
        }
        tmTestPlanCommandDomainService.batchJoinPlan(req.getCaseIds(), req.getPlanCode(),
                req.getTestStage(), GatewayContext.currentUser().toSimpleUser());
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "移除测试计划用例",
            apiName = "tm/plan/removeCaseFromTestPlan", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> removeCaseFromTestPlan(@Valid RemoveCaseFromTestPlanReq req) {
        if (CollectionUtil.isEmpty(req.getCaseIds())) {
            throw new ServiceException("用例code不能为空");
        }
        tmTestPlanCommandDomainService.batchRemoveFromPlan(req.getCaseIds(), req.getPlanCode(),
                req.getTestStage(), GatewayContext.currentUser().toSimpleUser());
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试计划发送邮件接口",
            apiName = "tm/plan/sendPlanEmail", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> sendPlanEmail(SendTestPlanReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("所属产品"))
                .on(req.getProductName(), new NotBlankValidator("所属产品名称"))
                .on(req.getVersionCode(), new NotBlankValidator("关联版本"))
                .on(req.getVersionName(), new NotBlankValidator("关联版本名称"))
                .on(req.getBusinessCode(), new NotBlankValidator("报告/计划code"))
                .on(req.getBusinessName(), new NotBlankValidator("报告/计划名称"))
                .on(req.getEmailType(), new NotNullValidator("邮件类型"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        tmTestPlanCommandDomainService.checkTestPlanName(req.getBusinessName(), req.getProductCode(), req.getBusinessCode());
        SendTestPlanCommand command = new SendTestPlanCommand(req.getBusinessCode());
        TmTestPlanSendEmailVO tmTestPlanVO = tmTestPlanAdapterConvertor.convertor(req);
        command.setTmTestPlanSendEmailVO(tmTestPlanVO);
        GatewayContext.fillCurrentUser(command);
        tmTestPlanCommandDomainService.sendPlanEmail(command);
        return StatusCode.OK.build();
    }

    private void checkPoint(List<TestFunctionPointVO> pointList) {
        if (CollectionUtil.isEmpty(pointList)) {
            return;
        }
        List<TestFunctionPointVO> hasTypeList = pointList.stream().filter(t -> StringUtil.isNotEmpty(t.getType())).collect(Collectors.toList());
        List<String> typeList = hasTypeList.stream().map(TestFunctionPointVO::getType).distinct().collect(Collectors.toList());
        if (typeList.size() != hasTypeList.size()) {
            throw new ServiceException("测试类型不能相同,请重新输入");
        }
        List<TestFunctionPointVO> hasPointContentList = pointList.stream().filter(t -> StringUtil.isNotEmpty(t.getFunctionPoint())).collect(Collectors.toList());
        List<String> pointContentList = hasPointContentList.stream().map(TestFunctionPointVO::getFunctionPoint).distinct().collect(Collectors.toList());
        if (hasPointContentList.size() != pointContentList.size()) {
            throw new ServiceException("测试内容不能相同,请重新输入");
        }
        if (pointList.size() > 500) {
            throw new ServiceException("测试功能点最多500条");
        }
        boolean max = pointList.stream().anyMatch(t -> StringUtil.isNotEmpty(t.getFunctionPoint()) && t.getFunctionPoint().length() > 500);
        if (max) {
            throw new ServiceException("测试功能点单条长度最大500字");
        }
        boolean maxType = pointList.stream().anyMatch(t -> StringUtil.isNotEmpty(t.getType()) && t.getType().length() > 30);
        if (maxType) {
            throw new ServiceException("测试功能点类型长度最大30字");
        }
    }

    @ZApiOperation(description = "新增移动专项测试计划",
            apiName = "tm/plan/addMobileSpecialTestPlan", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<String> addMobileSpecialTestPlan(AddMobileSpecialTestPlanReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("所属产品"))
                .on(req.getVersionCode(), new NotBlankValidator("关联版本"))
                .on(req.getPlanName(), new NotBlankValidator("计划名称"))
                .on(req.getRelationPlanCode(), new NotBlankValidator("关联计划编号"))
                .on(req.getPlanName(), new LengthValidator(-1, 60, "计划名称"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        checkPoint(req.getPointList());
        String code = AggregateIdUtil.generateId(AggregateType.TEST_PLAN);
        checkTestPlanName(req.getPlanName(), req.getProductCode(), code);
        AddMobileSpecialTestPlanCommand command = new AddMobileSpecialTestPlanCommand(code);
        TmTestPlanVO vo = tmTestPlanAdapterConvertor.convertor(req);
        vo.setCode(code);
        command.setTestPlanVO(vo);
        GatewayContext.fillCurrentUser(command);
        tmTestPlanCommandDomainService.addMobileSpecialTestPlanCommand(command);
        return StatusCode.OK.build(code);
    }

    private void checkTestPlanName(String planName, String productCode, String planCode) {
        FindPlanByProductQuery query = new FindPlanByProductQuery(planName, productCode, planCode);
        if (tmTestPlanQueryDomainService.findPlanByProductQuery(query)) {
            throw new ServiceException("计划标题不可重复，当前标题已存在，请重新编辑标题后提交！");
        }
    }

    @ZApiOperation(description = "查询关联测试计划接口",
            apiName = "tm/plan/associatedTestPlanList", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<TmTestPlanVO>> associatedTestPlanList(AssociatedTestPlanListReq req) {
        AssociatedTestPlanListQuery query = tmTestPlanAdapterConvertor.convertor(req);
        List<TmTestPlanVO> list = tmTestPlanQueryDomainService.associatedTestPlanListQuery(query);
        return StatusCode.OK.build(list);
    }

    @ZApiOperation(description = "关联用例加入测试计划",
            apiName = "tm/plan/batchAddCase", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> batchAddCaseInTestPlan(BatchAddCaseInTestPlanReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .onEach(req.getCaseCodeList(), new NotBlankValidator("用例code"))
                .on(req.getTestPlanCode(), new NotNullValidator("计划code"))
                .on(req.getTestStage(), new NotNullValidator("测试阶段"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        tmTestPlanCommandDomainService.batchJoinPlan(req.getCaseCodeList(), req.getTestPlanCode(),
                req.getTestStage(), GatewayContext.currentUser().toSimpleUser());
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "变更测试计划执行结果接口",
            apiName = "tm/plan/changeCaseExecuteResult", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> changeCaseExecuteResult(@Valid ChangeCaseExecuteResultReq req) {
        if (CollectionUtil.isEmpty(req.getCaseIds())) {
            throw new ServiceException("用例code不能为空");
        }
        ChangeCaseExecuteResultCommand command = tmTestPlanAdapterConvertor.convertor(req);
        GatewayContext.fillCurrentUser(command);
        tmTestPlanCommandDomainService.changeCaseExecuteResultCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "测试管理-变更测试计划用例结果备注", apiName = "tm/plan/changePlanCaseResultComment", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> changePlanCaseResultComment(ChangePlanCaseResultCommentReq req) {
        ComplexResult cr = FluentValidator.checkAll()
                .on(req.getCaseCode(), new NotBlankValidator("用例"))
                .on(req.getPlanCode(), new NotBlankValidator("测试计划"))
                .on(req.getTestStage(), new NotNullValidator("测试阶段"))
                .on(req.getResultComment(), new LengthValidator(0, 200, "结果备注"))
                .on(req.getOperateCaseCode(), new NotBlankValidator("日志记录"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        ChangePlanCaseResultCommentCommand command = new ChangePlanCaseResultCommentCommand(req.getPlanCode());
        command.setCaseCode(req.getCaseCode());
        command.setTestStage(req.getTestStage());
        command.setResultComment(req.getResultComment());
        command.setOperateCaseCode(req.getOperateCaseCode());
        GatewayContext.fillCurrentUser(command);
        tmTestPlanCommandDomainService.changePlanCaseResultCommentCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "获取当前人的操作权限信息接口",
            apiName = "tm/plan/currentPersonPermissionInformation", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<ButtonVO>> currentPersonPermissionInformation(@Valid CurrentPersonPermissionInformationReq req) {
        if (req.getProductCode().isEmpty()) {
            throw new ServiceException("产品code不能为空！");
        }
        CurrentPersonPermissionInformationQuery query = tmTestPlanAdapterConvertor.convertor(req);
        GatewayContext.fillCurrentUser(query);
        return StatusCode.OK.build(tmTestPlanQueryDomainService.currentPersonPermissionInformationQuery(query));
    }

    @ZApiOperation(description = "编辑普通测试计划",
            apiName = "tm/plan/editCommonTestPlan", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editCommonTestPlan(AddCommonTestPlanReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("所属产品"))
                .on(req.getVersionCode(), new NotBlankValidator("关联版本"))
                .on(req.getPlanCode(), new NotBlankValidator("计划编号"))
                .on(req.getPlanName(), new NotBlankValidator("计划名称"))
                .on(req.getPlanName(), new LengthValidator(-1, 60, "计划名称"))
                .on(req.getTestStrategy(), new NotNullValidator("测试策略"))
                .on(req.getExploratoryTest(), new NotNullValidator("探索性测试"))
                .on(req.getStaticAnalysis(), new NotNullValidator("静态分析"))
                .on(req.getSecurityScan(), new NotNullValidator("安全扫描"))
                .on(req.getMobileSpecialTest(), new NotNullValidator("移动专项测试"))
                .on(req.getPlanDirectorId(), new NotNullValidator("测试计划负责人"))
                .on(req.getPlanDirectorName(), new NotBlankValidator("测试计划负责人"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (req.getTesterNum() > 99999 || req.getTesterNum() <= 0) {
            throw new ServiceException("测试人数必须在1-99999之间");
        }
        if (req.getDeveloperNum() > 99999 || req.getDeveloperNum() <= 0) {
            throw new ServiceException("开发人数必须在1-99999之间");
        }
        checkTestPlanName(req.getPlanName(), req.getProductCode(), req.getPlanCode());
        checkPoint(req.getPointList());
        TmTestPlanVO vo = tmTestPlanAdapterConvertor.convertor(req);
        EditCommonTestPlanCommand command = new EditCommonTestPlanCommand(req.getPlanCode());
        command.setTestPlanVO(vo);
        GatewayContext.fillCurrentUser(command);
        tmTestPlanCommandDomainService.editCommonTestPlanCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "编辑移动专项测试计划",
            apiName = "tm/plan/editMobileSpecialTestPlan", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editMobileSpecialTestPlan(AddMobileSpecialTestPlanReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("所属产品"))
                .on(req.getVersionCode(), new NotBlankValidator("关联版本"))
                .on(req.getVersionName(), new NotBlankValidator("关联版本名称"))
                .on(req.getPlanName(), new NotBlankValidator("计划名称"))
                .on(req.getPlanCode(), new NotBlankValidator("计划编号"))
                .on(req.getRelationPlanCode(), new NotBlankValidator("关联计划编号"))
                .on(req.getPlanName(), new LengthValidator(-1, 60, "计划名称"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        if (StringUtil.isNotEmpty(req.getComment()) && req.getComment().length() > 500) {
            throw new ServiceException("备注信息长度不能超过500");
        }
        checkTestPlanName(req.getPlanName(), req.getProductCode(), req.getPlanCode());
        checkPoint(req.getPointList());
        EditMobileSpecialTestPlanCommand command = new EditMobileSpecialTestPlanCommand(req.getPlanCode());
        TmTestPlanVO vo = tmTestPlanAdapterConvertor.convertor(req);
        command.setTestPlanVO(vo);
        GatewayContext.fillCurrentUser(command);
        tmTestPlanCommandDomainService.editMobileSpecialTestPlanCommand(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "编辑安全测试计划",
            apiName = "tm/plan/editSafeTestPlan", namespace = "luban", responseDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> editSafeTestPlan(AddSafeTestPlanReq req) {
        ComplexResult cr = checkAll()
                .on(req.getProductCode(), new NotBlankValidator("所属产品"))
                .on(req.getVersionCode(), new NotBlankValidator("关联版本"))
                .on(req.getPlanName(), new NotBlankValidator("计划名称"))
                .on(req.getPlanCode(), new NotBlankValidator("计划编号"))
                .on(req.getPlanName(), new LengthValidator(-1, 60, "计划名称"))
                .on(req.getLastTestDate(), new NotNullValidator("最晚测试日期"))
                .on(req.getPriority(), new NotNullValidator("优先级"))
                .on(req.getPermissionsTest(), new NotNullValidator("权限测试"))
                .on(req.getTestInformation(), new NotBlankValidator("测试信息"))
                .on(req.getTestInformation(), new LengthValidator(-1, 500, "测试信息"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        checkTestPlanName(req.getPlanName(), req.getProductCode(), req.getPlanCode());
        if(req.getPermissionsTest()){
            if (StringUtil.isEmpty(req.getPermissionsTestInformation())){
                throw new ServiceException("权限测试信息不能为空");
            }
            if (req.getPermissionsTestInformation().length() > 500){
                throw new ServiceException("权限测试信息长度不能超过500");
            }
        }
        EditSafeTestPlanCommand command = new EditSafeTestPlanCommand(req.getPlanCode());
        TmTestPlanVO vo = tmTestPlanAdapterConvertor.convertor(req);
        command.setTestPlanVO(vo);
        GatewayContext.fillCurrentUser(command);
        tmTestPlanCommandDomainService.editSafeTestPlanCommand(command);
        return StatusCode.OK.build();
    }

    @Override
    public Result<Boolean> stageTestStart(StageTestStartReq req) {
       return StatusCode.OK.build(
               tmTestPlanQueryDomainService.testPlanStageStart(req.getVersionCode(), req.getStage())
       );
    }

    @Override
    public Result<Boolean> testPlanStart(String versionCode) {
        return StatusCode.OK.build(
                tmTestPlanQueryDomainService.testPlanStart(versionCode)
        );
    }

    @ZApiOperation(description = "编测试管理-接口自动化-校验测试通过是否满足条件", apiName = "tm/apitest/verifyTestPassCondition", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<VerifyTestPassConditionResp> verifyTestPassCondition(VerifyTestPassConditionReq req) {
        ComplexResult cr = checkAll()
                .on(req.getVersionCode(), new NotBlankValidator("版本code"))
                .on(req.getEvent(), new NotNullValidator("事件类型"))
                .doValidate()
                .result(ResultCollectors.toComplex());
        if (!cr.isSuccess()) {
            throw new FluentException(cr.getErrors());
        }
        VerifyTestPassConditionQuery query = tmTestPlanAdapterConvertor.convertor(req);
        return StatusCode.OK.build(tmTestPlanQueryDomainService.verifyTestPassCondition(query));
    }

    @ZApiOperation(description = "关闭延迟验收工单", apiName = "tm/plan/closeDelayAcceptAudit", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> closeDelayAcceptAudit(CloseDelayAcceptAuditReq req) {
        tmTestPlanCommandDomainService.closeDelayAcceptAudit(req.getVersionCodeList());
        return StatusCode.OK.build();
    }
}
