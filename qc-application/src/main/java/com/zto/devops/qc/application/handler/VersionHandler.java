package com.zto.devops.qc.application.handler;


import com.zto.devops.framework.common.message.EventHandler;
import com.zto.devops.project.client.model.version.event.VersionOnlineEvent;
import com.zto.devops.qc.domain.service.ApiTestDocDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class VersionHandler {

    @Autowired
    private ApiTestDocDataService apiTestDocDataService;

    @EventHandler
    public void handle(VersionOnlineEvent event) {
        log.info("监听到【VersionOnlineEvent】事件，{}", event.getCode());
        apiTestDocDataService.archiveApiTestVersion(event.getCode());
    }
}
