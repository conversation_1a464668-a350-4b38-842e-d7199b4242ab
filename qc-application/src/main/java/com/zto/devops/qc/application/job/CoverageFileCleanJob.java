package com.zto.devops.qc.application.job;

import com.zto.devops.qc.domain.service.CoverageDomainService;
import com.zto.zss.common.exception.ZSSException;
import com.zto.zss.worker.processor.BasicProcessor;
import com.zto.zss.worker.processor.model.ProcessResult;
import com.zto.zss.worker.processor.model.TaskContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component("coverageFileCleanJob")
public class CoverageFileCleanJob implements BasicProcessor {

    @Autowired
    private CoverageDomainService coverageDomainService;

    @Override
    public ProcessResult process(TaskContext context) throws ZSSException {
        log.info("开始执行 -> 覆盖率源码文件每日定时清理任务!");
        coverageDomainService.cleanCoverageFile();
        return ProcessResult.success("OK");
    }

}
