package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.zto.devops.framework.application.context.GatewayContext;
import com.zto.devops.framework.application.service.GatewayBase;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.qc.client.model.issue.command.AddCommentCommand;
import com.zto.devops.qc.client.model.issue.command.RemoveCommentCommand;
import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import com.zto.devops.qc.client.service.comment.CommentService;
import com.zto.devops.qc.client.service.comment.model.AddCommentReq;
import com.zto.devops.qc.client.service.comment.model.ListCommentsByBusinessCodeReq;
import com.zto.devops.qc.client.service.comment.model.RemoveCommentReq;
import com.zto.devops.qc.domain.service.CommentCommandDomainService;
import com.zto.devops.qc.domain.service.CommentQueryDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWSsoAuth;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.titans.common.util.StringUtil;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import com.zto.zsmp.annotation.gateway.GatewayApi;
import com.zto.zsmp.annotation.gateway.GatewayWebAuth;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@ZsmpService(name = "一站式研发-评论相关接口", group = "一站式研发-评论")
@Service
public class CommentServiceImpl extends GatewayBase implements CommentService {

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private CommentCommandDomainService commentCommandDomainService;

    @Autowired
    private CommentQueryDomainService commentQueryDomainService;

    @ZApiOperation(description = "一站式研发--添加评论", apiName = "qc/comment/add", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> addComment(AddCommentReq req) {
        if (StringUtil.isNotBlank(req.getContent()) && req.getContent().length() > 500) {
            throw new ServiceException("评论内容字数不能超过500个字！");
        }
        AddCommentCommand command = new AddCommentCommand(req.getBusinessCode());
        command.setDomain(DomainEnum.ISSUE);
        command.setBusinessCode(req.getBusinessCode());
        command.setContent(req.getContent());
        command.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        command.setRepliedCode(req.getRepliedCode());
        command.setTopRepliedCode(req.getTopRepliedCode());
        command.setRepliedUserId(req.getRepliedUserId());
        command.setRepliedUserName(req.getRepliedUserName());
        GatewayContext.fillCurrentUser(command);
        commentCommandDomainService.addComment(command);
        return StatusCode.OK.build();
    }

    @ZApiOperation(description = "一站式研发--查询评论", apiName = "qc/comment/list", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<List<CommentVO>> listCommentsByBusinessCode(ListCommentsByBusinessCodeReq req) {
        List<CommentVO> CommentVOList = commentQueryDomainService.queryCommentVOS(req);
        return StatusCode.OK.build(CommentVOList);
    }

    @ZApiOperation(description = "一站式研发--移除评论", apiName = "qc/comment/remove", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Void> removeComment(RemoveCommentReq req) {
        RemoveCommentCommand command = new RemoveCommentCommand(req.getBusinessCode());
        command.setDomain(DomainEnum.ISSUE);
        command.setBusinessCode(req.getBusinessCode());
        command.setCode(req.getCode());
        GatewayContext.fillCurrentUser(command);
        commentCommandDomainService.removeComment(command);
        return StatusCode.OK.build();
    }
}
