package com.zto.devops.qc.application.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.service.scene.UserCookieService;
import com.zto.devops.qc.domain.service.SceneCookieDomainService;
import com.zto.doc.annotation.ZApiOperation;
import com.zto.doc.annotation.ZGWWebAuth;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class UserCookieServiceImpl implements UserCookieService {

    @Autowired
    private SceneCookieDomainService sceneCookieDomainService;

    @ZApiOperation(description = "清理用户Cookie缓存", apiName = "scene/cookie/cleanSceneCache", namespace = "luban")
    @ZGWWebAuth(needUserInfoContext = true)
    @Override
    public Result<Boolean> cleanSceneCache(
            @GatewayModelProperty(description = "缓存类型：testCaseCookie；testCaseDbInfo；") String type,
            @GatewayModelProperty(description = "缓存后缀：cookie则为；GW_{变量code}_{网关域名前缀}; DB则为db id；") String suffixKey) {
        Result<Boolean> result = new Result<>();
        result.setData(sceneCookieDomainService.deleteCookie(type, suffixKey));
        return result;
    }
}
