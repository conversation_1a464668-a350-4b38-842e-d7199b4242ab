<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>qc-parent</artifactId>
        <groupId>com.zto.devops.qc</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>qc-infrastructure</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <aws.sdk.version>1.11.106</aws.sdk.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zto.titans</groupId>
            <artifactId>titans-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.framework</groupId>
            <artifactId>devops-infrastructure</artifactId>
            <version>${framework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.qc</groupId>
            <artifactId>qc-domain</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.qc</groupId>
            <artifactId>qc-application</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.project</groupId>
            <artifactId>project-client</artifactId>
            <version>${project-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.product</groupId>
            <artifactId>product-client</artifactId>
            <version>${devops.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.pipeline</groupId>
            <artifactId>pipeline-client</artifactId>
            <version>${devopsPipeline.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.user</groupId>
            <artifactId>user-client</artifactId>
            <version>${devops.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-core</artifactId>
            <version>1.11.837</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.11.106</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>search-manager</groupId>
            <artifactId>search-manager-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.bff</groupId>
            <artifactId>bff-client</artifactId>
            <version>${devops.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.8</version>
        </dependency>
        <dependency>
            <artifactId>base-center-emp-share</artifactId>
            <groupId>com.zto</groupId>
            <version>1.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.zto.titans</groupId>
                    <artifactId>titans-http</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.21</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jgit</groupId>
            <artifactId>org.eclipse.jgit</artifactId>
            <version>5.5.0.201909110433-r</version>
        </dependency>
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.core</artifactId>
            <version>0.8.19</version>
        </dependency>
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.report</artifactId>
            <version>0.8.18</version>
        </dependency>
        <dependency>
            <groupId>org.gitlab4j</groupId>
            <artifactId>gitlab4j-api</artifactId>
            <version>${gitlab-api.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.11.3</version>
        </dependency>
        <dependency>
            <groupId>com.github.eljah</groupId>
            <artifactId>xmindjbehaveplugin</artifactId>
            <version>0.8</version>
        </dependency>
        <dependency>
            <groupId>com.zto.waybill</groupId>
            <artifactId>waybill-tag-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.zto.huiyan</groupId>
            <artifactId>huiyan-terminal-facade</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.zto</groupId>
            <artifactId>base-center-outlet-share</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.zto.agent</groupId>
            <artifactId>base-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.qc</groupId>
            <artifactId>jar-scan</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.zto.iam.sdk</groupId>
            <artifactId>iam-sdk-java</artifactId>
            <version>2022.407.1545</version>
        </dependency>
    </dependencies>

</project>
