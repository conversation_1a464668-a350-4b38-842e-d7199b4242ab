package com.zto.devops.qc.infrastructure.auth;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.Assert;

import java.util.Map;

/**
 * 签名工具类测试
 * 
 * <AUTHOR>
 * @since 2025-08-14
 */
public class SignatureUtilsTest {
    
    @Test
    public void testGenerateAndVerifySignature() {
        // 测试数据
        String accessKey = "AK_TEST_001";
        String secretKey = "sk_test_secret_key_001";
        String httpMethod = "POST";
        String requestUri = "/tm/apitest/executeSceneDataCenter";
        long timestamp = System.currentTimeMillis();
        String nonce = SignatureUtils.generateNonce();
        
        JSONObject requestBody = new JSONObject();
        requestBody.put("sceneCode", "SNF979388218212352000");
        requestBody.put("productCode", "399");
        
        // 生成签名
        String signature = SignatureUtils.generateSignature(
            accessKey, secretKey, httpMethod, requestUri, timestamp, nonce, requestBody
        );
        
        Assert.assertNotNull("签名不能为空", signature);
        Assert.assertTrue("签名长度应该大于0", signature.length() > 0);
        
        // 验证签名
        boolean isValid = SignatureUtils.verifySignature(
            accessKey, secretKey, signature, httpMethod, requestUri, timestamp, nonce, requestBody
        );
        
        Assert.assertTrue("签名验证应该通过", isValid);
    }
    
    @Test
    public void testSignatureWithDifferentBody() {
        String accessKey = "AK_TEST_001";
        String secretKey = "sk_test_secret_key_001";
        String httpMethod = "POST";
        String requestUri = "/tm/apitest/executeSceneDataCenter";
        long timestamp = System.currentTimeMillis();
        String nonce = SignatureUtils.generateNonce();
        
        JSONObject requestBody1 = new JSONObject();
        requestBody1.put("sceneCode", "SNF979388218212352000");
        
        JSONObject requestBody2 = new JSONObject();
        requestBody2.put("sceneCode", "SNF979388218212352001");
        
        // 生成签名
        String signature = SignatureUtils.generateSignature(
            accessKey, secretKey, httpMethod, requestUri, timestamp, nonce, requestBody1
        );
        
        // 用不同的请求体验证签名应该失败
        boolean isValid = SignatureUtils.verifySignature(
            accessKey, secretKey, signature, httpMethod, requestUri, timestamp, nonce, requestBody2
        );
        
        Assert.assertFalse("不同请求体的签名验证应该失败", isValid);
    }
    
    @Test
    public void testTimestampValidation() {
        long currentTime = System.currentTimeMillis();
        
        // 测试有效时间戳
        Assert.assertTrue("当前时间戳应该有效", 
            SignatureUtils.isTimestampValid(currentTime, 300, 60));
        
        // 测试过期时间戳
        long expiredTimestamp = currentTime - 400 * 1000; // 400秒前
        Assert.assertFalse("过期时间戳应该无效", 
            SignatureUtils.isTimestampValid(expiredTimestamp, 300, 60));
        
        // 测试时间偏差过大的时间戳
        long futureTimestamp = currentTime + 120 * 1000; // 120秒后
        Assert.assertFalse("时间偏差过大的时间戳应该无效", 
            SignatureUtils.isTimestampValid(futureTimestamp, 300, 60));
    }
    
    @Test
    public void testGenerateNonce() {
        String nonce1 = SignatureUtils.generateNonce();
        String nonce2 = SignatureUtils.generateNonce();
        
        Assert.assertNotNull("Nonce不能为空", nonce1);
        Assert.assertNotNull("Nonce不能为空", nonce2);
        Assert.assertNotEquals("两次生成的Nonce应该不同", nonce1, nonce2);
        Assert.assertEquals("Nonce长度应该为32", 32, nonce1.length());
    }
    
    @Test
    public void testAuthorizationHeader() {
        String accessKey = "AK_TEST_001";
        String signature = "test_signature";
        long timestamp = System.currentTimeMillis();
        String nonce = "test_nonce";
        
        // 构造授权头
        String authHeader = SignatureUtils.buildAuthorizationHeader(accessKey, signature, timestamp, nonce);
        
        Assert.assertNotNull("授权头不能为空", authHeader);
        Assert.assertTrue("授权头应该包含AccessKey", authHeader.contains("AccessKey=" + accessKey));
        Assert.assertTrue("授权头应该包含Signature", authHeader.contains("Signature=" + signature));
        
        // 解析授权头
        Map<String, String> params = SignatureUtils.parseAuthorizationHeader(authHeader);
        
        Assert.assertEquals("AccessKey应该正确", accessKey, params.get("AccessKey"));
        Assert.assertEquals("Signature应该正确", signature, params.get("Signature"));
        Assert.assertEquals("Timestamp应该正确", String.valueOf(timestamp), params.get("Timestamp"));
        Assert.assertEquals("Nonce应该正确", nonce, params.get("Nonce"));
    }
    
    @Test
    public void testInvalidAuthorizationHeader() {
        // 测试无效的授权头
        Map<String, String> params1 = SignatureUtils.parseAuthorizationHeader(null);
        Assert.assertTrue("空授权头应该返回空Map", params1.isEmpty());
        
        Map<String, String> params2 = SignatureUtils.parseAuthorizationHeader("Bearer token");
        Assert.assertTrue("错误格式的授权头应该返回空Map", params2.isEmpty());
        
        Map<String, String> params3 = SignatureUtils.parseAuthorizationHeader("ZTO-HMAC-SHA256 ");
        Assert.assertTrue("空参数的授权头应该返回空Map", params3.isEmpty());
    }
}
