<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.IssueLegacyMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.IssueLegacyEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="code" jdbcType="VARCHAR" property="code"/>
    <result column="report_code" jdbcType="VARCHAR" property="reportCode" />
    <result column="issue_code" jdbcType="VARCHAR" property="issueCode" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="handle_user_id" jdbcType="BIGINT" property="handleUserId" />
    <result column="handle_user_name" jdbcType="VARCHAR" property="handleUserName" />
    <result column="develop_user_id" jdbcType="BIGINT" property="developUserId" />
    <result column="develop_user_name" jdbcType="VARCHAR" property="developUserName" />
    <result column="status" jdbcType="VARCHAR" property="status" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.StatusHandler"/>
    <result column="priority" jdbcType="VARCHAR" property="priority" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.PriorityHandler"/>
    <result column="find_user_id" jdbcType="BIGINT" property="findUserId" />
    <result column="find_user_name" jdbcType="VARCHAR" property="findUserName" />
    <result column="find_version_code" jdbcType="VARCHAR" property="findVersionCode" />
    <result column="find_version_name" jdbcType="VARCHAR" property="findVersionName" />
  </resultMap>

  <select id="selectByReportCode" resultMap="BaseResultMap">
    select *
    from qc_issue_legacy t
    where t.report_code = #{reportCode} and t.enable=true
  </select>
</mapper>