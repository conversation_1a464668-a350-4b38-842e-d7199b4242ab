<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.AttachmentMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.AttachmentEntity">
    <id column="code" jdbcType="VARCHAR" property="code" />
    <result column="domain" jdbcType="VARCHAR" property="domain" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler"/>
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="document_type" jdbcType="VARCHAR" property="documentType" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.AttachmentDocumentTypeHandler"/>
    <result column="type" jdbcType="VARCHAR" property="type" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.AttachmentTypeHandler"/>
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="fileType" jdbcType="VARCHAR" property="fileType" typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AttachmentFileTypeHandler" />
    <result column="size" jdbcType="VARCHAR" property="size" />
  </resultMap>
</mapper>


