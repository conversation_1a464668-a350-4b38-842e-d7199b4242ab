<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TmEmailMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TmEmailEntity">
    <id column="email_code" jdbcType="VARCHAR" property="emailCode" />
    <result column="email_name" jdbcType="VARCHAR" property="emailName" />
    <result column="email_type" jdbcType="VARCHAR" property="emailType" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.EmailTypeHandler"/>
    <result column="email_source" jdbcType="VARCHAR" property="emailSource" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.EmailSourceHandler"/>
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="relate_plan_code" jdbcType="VARCHAR" property="relatePlanCode" />
    <result column="relate_plan_name" jdbcType="VARCHAR" property="relatePlanName" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="businessCode" />
    <result column="version_code" jdbcType="VARCHAR" property="versionCode" />
    <result column="version_name" jdbcType="VARCHAR" property="versionName" />
    <result column="preview" jdbcType="VARCHAR" property="preview" />
  </resultMap>

  <select id="selectVersionEmail"
          parameterType="com.zto.devops.qc.client.model.testmanager.email.query.VersionEmailQuery"
          resultType="com.zto.devops.qc.infrastructure.dao.entity.TmVersionEmailEntity">
    SELECT DISTINCT
    qr.code AS emailCode,
    qr.name AS emailName,
    qr.plan_code AS businessCode,
    qr.report_type AS emailType,
    qt.product_code AS productCode,
    qt.product_name AS productName,
    qr.version_code AS versionCode,
    qr.version_name AS versionName,
    qr.gmt_create AS gmtCreate,
    qr.gmt_modified AS gmtModified,
    qr.creator AS creator,
    qr.creator_id AS creatorId,
    qr.modifier AS modifier,
    qr.modifier_id AS modifierId,
    qr.modifier AS sender,
    qr.modifier_id AS senderId,
    qr.gmt_modified as sendDate,
    'OLD' AS source
    FROM
    qc_test_report qr
    LEFT JOIN
    qc_test_plan qt ON qt.code = qr.plan_code
    LEFT JOIN
    qc_test_plan_main qtm ON qtm.test_plan_code = qr.plan_code
    WHERE
    qr.enable = TRUE
    AND qr.status = 'NORMAL'
    AND qtm.version_code = #{versionCode}
    UNION ALL
    SELECT DISTINCT
    tp.code AS emailCode,
    tp.plan_name AS emailName,
    tp.code AS businessCode,
    tp.type AS emailType,
    tp.product_code AS productCode,
    tp.product_name AS productName,
    tp.version_code AS versionCode,
    tp.version_name AS versionName,
    tp.gmt_create AS gmtCreate,
    tp.gmt_modified AS gmtModified,
    tp.creator AS creator,
    tp.creator_id AS creatorId,
    tp.modifier AS modifier,
    tp.modifier_id AS modifierId,
    tp.modifier AS sender,
    tp.modifier_id AS senderId,
    tp.gmt_modified as sendDate,
    'OLD' AS source
    FROM
    qc_test_plan tp
    WHERE
    tp.enable = TRUE
    AND tp.status = 'NORMAL'
    AND tp.version_code = #{versionCode}
    union all
    SELECT email_code AS emailCode,
    email_name AS emailName,
    business_code AS businessCode,
    email_type AS emailType,
    product_code AS productCode,
    product_name AS productName,
    version_code AS versionCode,
    version_name AS versionName,
    gmt_create AS gmtCreate,
    gmt_modified AS gmtModified,
    creator AS creator,
    creator_id AS creatorId,
    modifier AS modifier,
    modifier_id AS modifierId,
    sender AS sender,
    sender_id AS senderId,
    send_date as sendDate,
    'NEW' AS source FROM lbd_qc.tm_email where version_code = #{versionCode}
    ORDER BY gmtCreate
  </select>


  <insert id="insertBatch">
    insert into tm_email(
    email_code,
    email_name,
    email_type,
    email_source,
    business_code,
    business_name,
    relate_plan_code,
    relate_plan_name,
    version_code,
    version_name,
    plan_presentation_date,
    plan_approval_exit_date,
    send_date,
    sender_id,
    sender,
    creator_id,
    creator,
    modifier_id,
    modifier,
    gmt_create,
    gmt_modified,
    enable,
    preview,
    product_code,
    product_name,
    data_source,
    sync_create)
    VALUES
    <foreach collection="list" separator="," item="item" index="index">
      (
      #{item.emailCode},
      #{item.emailName},
      #{item.emailType},
      #{item.emailSource},
      #{item.businessCode},
      #{item.businessName},
      #{item.relatePlanCode},
      #{item.relatePlanName},
      #{item.versionCode},
      #{item.versionName},
      #{item.planPresentationDate},
      #{item.planApprovalExitDate},
      #{item.sendDate},
      #{item.senderId},
      #{item.sender},
      #{item.creatorId},
      #{item.creator},
      #{item.modifierId},
      #{item.modifier},
      #{item.gmtCreate},
      #{item.gmtModified},
      #{item.enable},
      #{item.preview},
      #{item.productCode},
      #{item.productName},
      #{item.dataSource},
      #{item.syncCreate}
      )
    </foreach>
  </insert>
</mapper>


