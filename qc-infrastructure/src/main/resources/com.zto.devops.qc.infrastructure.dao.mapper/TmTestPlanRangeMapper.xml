<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TmTestPlanRangeMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanRangeEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="plan_code" jdbcType="VARCHAR" property="planCode" />
    <result column="test_range" jdbcType="VARCHAR" property="testRange" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="test_time" jdbcType="DATE" property="testTime" />
    <result column="test_range_status" jdbcType="TINYINT" property="testRangeStatus" />
    <result column="priority" jdbcType="VARCHAR" property="priority" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="executor_id" jdbcType="BIGINT" property="executorId" />
    <result column="executor" jdbcType="VARCHAR" property="executor" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>

  <select id="selectListByVersionCode" resultType="com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanRangeEntity">
    select ttp.code as code ,
           ttpr.test_range as testRange ,
           ttpr.executor_id as executorId,
           ttpr.executor as executor,
           ttpr.status as status
    from tm_test_plan ttp
           left join tm_test_plan_range ttpr on ttp.code = ttpr.plan_code
    where ttpr.test_range = 'SAFETY_SCANNING'
      and ttpr.enable = 1
      and ttpr.status is not null
      and ttp.version_code = #{versionCode}
  </select>

  <select id="selectAssociatedTestPlanCode" resultType="java.lang.String">
    select distinct
      plan_code
    from tm_test_plan_range t1
    left join tm_test_plan t2 on t2.relation_plan_code = t1.plan_code and t2.enable = 1 and t2.type = 'MOBILE_SPECIAL'
    where t1.enable = 1 and t1.test_range = 'MOBILE_TEST' and t1.test_range_status = 1
      and t2.relation_plan_code is null
  </select>
</mapper>