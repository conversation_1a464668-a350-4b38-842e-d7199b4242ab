<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.CoverageBranchBasicMapper">

    <insert id="batchInsert">
        insert into qc_coverage_branch_basic (version_code, version_name, app_id, branch_name,
        basic_branch_name, basic_commit_id, creator_id, creator, modifier_id, modifier, git_project_id)
        values
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.versionCode,jdbcType=VARCHAR},#{item.versionName,jdbcType=VARCHAR},
             #{item.appId,jdbcType=VARCHAR}, #{item.branchName,jdbcType=VARCHAR},
             #{item.basicBranchName,jdbcType=VARCHAR},#{item.basicCommitId,jdbcType=VARCHAR},
             #{item.creatorId,jdbcType=BIGINT},#{item.creator,jdbcType=VARCHAR},
             #{item.modifierId,jdbcType=BIGINT},#{item.modifier,jdbcType=VARCHAR},
             #{item.gitProjectId,jdbcType=BIGINT})
        </foreach>
    </insert>

</mapper>