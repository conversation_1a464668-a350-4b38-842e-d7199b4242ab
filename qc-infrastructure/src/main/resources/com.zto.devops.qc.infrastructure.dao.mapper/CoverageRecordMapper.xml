<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.CoverageRecordMapper">

    <select id="getCoverageRecordList"
            parameterType="com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageRecordPageQuery"
            resultType="com.zto.devops.qc.infrastructure.dao.entity.CoverageRecordEntity">
        select version_code,
        version_name,
        app_id,
        branch_diff_type,
        branch_remark,
        if(branch_status = 'SUCCEED', '',branch_record_url) as branch_record_url,
        branch_bucket_name,
        branch_file_name,
        branch_record_rate,
        branch_record_error_msg,
        branch_status,
        branch_creator,
        branch_creator_id,
        branch_gmt_create,
        branch_commit_id,
        branch_git_compare_url,
        comment,
        branch_standard_rate AS standard_rate
        from (
        SELECT
        ANY_VALUE(version_code) as version_code,
        ANY_VALUE(version_name) as version_name,
        ANY_VALUE(r.app_id) as app_id,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.standard_rate ELSE NULL END) AS branch_standard_rate,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.diff_type ELSE NULL END) AS branch_diff_type,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.remark ELSE NULL END) AS branch_remark,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.record_url ELSE NULL END) branch_record_url,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.bucket_name ELSE NULL END) branch_bucket_name,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.file_name ELSE NULL END) branch_file_name,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.record_rate ELSE NULL END) branch_record_rate,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.record_error_msg ELSE NULL END) branch_record_error_msg,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.status ELSE NULL END) branch_status,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.creator ELSE NULL END) branch_creator,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.creator_id ELSE NULL END) branch_creator_id,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.gmt_create ELSE NULL END) branch_gmt_create,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.commit_id ELSE NULL END) branch_commit_id,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.git_compare_url ELSE NULL END) branch_git_compare_url,
        MAX(CASE r.record_type WHEN 'BRANCH' THEN r.comment ELSE NULL END) as comment
        FROM qc_coverage_record r
        WHERE r.enable = 1
          and r.diff_type = 'INCREMENT'
          and r.record_type = 'BRANCH'
        <if test="versionCode != null and versionCode != ''">
            AND r.version_code = #{versionCode}
        </if>
        <if test="appId != null and appId != ''">
            AND r.app_id like CONCAT(#{appId},'%')
        </if>
        GROUP BY r.app_id) t
        where 1=1
        <if test="branchStatus != null and branchStatus.size > 0">
            AND t.branch_status in
            <foreach collection="branchStatus" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="creatorId != null">
            AND (t.branch_creator_id = #{creatorId})
        </if>
        <if test="gmtCreateStart != null">
            AND (t.branch_gmt_create >= #{gmtCreateStart})
        </if>
        <if test="gmtCreateEnd != null">
            AND (#{gmtCreateEnd} >= t.branch_gmt_create)
        </if>
        <choose>
            <when test="orderField != null and orderField != ''">
                order by
                <include refid="orderField"/>
                <include refid="orderType"/>
            </when>
            <otherwise>
                order by t.app_id
            </otherwise>
        </choose>
    </select>

    <select id="getCoverageResultList"
            parameterType="com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageResultQuery"
            resultType="java.lang.String">
        select *
        FROM qc_coverage_record r
        WHERE enable = 1
        AND r.version_code = #{versionCode}
    </select>

    <select id="getVersionRecordRate" resultType="com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageVersionRateVO">
        select
        SUM(CASE record_type WHEN 'BRANCH' THEN code_cover_num ELSE NULL END) as branchCodeCoverNum,
        SUM(CASE record_type WHEN 'BRANCH' THEN code_sum ELSE NULL END) as branchCodeSum,
        SUM(CASE record_type WHEN 'MASTER' THEN code_cover_num ELSE NULL END) as masterCodeCoverNum,
        SUM(CASE record_type WHEN 'MASTER' THEN code_sum ELSE NULL END) as masterCodeSum,
        version_code as version_code
        from qc_coverage_record
        where version_code = #{versionCode}
        and enable = 1
        and diff_type = 'INCREMENT'
    </select>

    <select id="getVersionRecordRateList" resultType="com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageVersionRateVO">
        select
            SUM(CASE record_type WHEN 'BRANCH' THEN code_cover_num ELSE NULL END) as branchCodeCoverNum,
            SUM(CASE record_type WHEN 'BRANCH' THEN code_sum ELSE NULL END) as branchCodeSum,
            SUM(CASE record_type WHEN 'MASTER' THEN code_cover_num ELSE NULL END) as masterCodeCoverNum,
            SUM(CASE record_type WHEN 'MASTER' THEN code_sum ELSE NULL END) as masterCodeSum,
            version_code as version_code
        from qc_coverage_record
        where version_code in
          <foreach collection="list" close=")" index="index" item="item" open="(" separator=",">
              #{item}
          </foreach>
          and enable = 1
          and diff_type = 'INCREMENT'
        GROUP BY version_code
    </select>

    <select id="selectTaskIdFromCoverage" resultType="com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageTaskVO">
        select r.task_id as taskId, MAX(r.record_type) as recordType, MAX(r.version_name) as versionName,
        MAX(r.diff_type) as diffType, MAX(r.version_code) as versionCode, MAX(r.creator) as recordCreator,
        MAX(r.gmt_create) as gmtCreate, MAX(r.commit_id) as commitId, MAX(r.git_compare_url) as gitCompareUrl
        from qc_coverage_record r
        <include refid="base_sql"></include>
        group by r.task_id
        order by gmtCreate desc
    </select>

    <select id="selectCoverageByTaskId" resultType="com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageTaskVO">
        select r.task_id as taskId, r.app_id as appId, r.standard_rate as standardRate,
        r.record_rate as recordRate, r.status as recordStatus, r.record_type as recordType,
        r.diff_type as diffType, r.record_error_msg as recordErrorMsg, r.version_code as versionCode,
        r.version_name as versionName, r.env_name as envName, r.creator as recordCreator,
        r.gmt_create as recordCreate, r.gmt_modified as recordModified, r.comment,
        if(r.status = 'SUCCEED', '', r.record_url) as recordUrl,
        r.code_cover_num as codeCoverNum, r.code_sum as codeSum, r.id, r.commit_id, r.git_compare_url,
        r.bucket_name as bucketName, r.file_name as fileName
        from qc_coverage_record r
        <include refid="base_sql"></include>
        order by r.gmt_create
    </select>

    <sql id="base_sql">
        where r.task_id != ""
        and r.product_code = #{productCode}
        <if test="appId != null and appId != ''">
            and r.app_id = #{appId}
        </if>
        <if test="taskIdList != null and taskIdList.size > 0">
            and r.task_id in
            <foreach collection="taskIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size > 0">
            and r.status in
            <foreach collection="statusList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="recordTypeList != null and recordTypeList.size > 0">
            and r.record_type in
            <foreach collection="recordTypeList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="versionCodeList != null and versionCodeList.size > 0">
            and r.version_code in
            <foreach collection="versionCodeList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="diffTypeList != null and diffTypeList.size > 0">
            and r.diff_type in
            <foreach collection="diffTypeList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="creatorIdList != null and creatorIdList.size > 0">
            and r.creator_id in
            <foreach collection="creatorIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="recordCreateStart != null and recordCreateEnd != null">
            and r.gmt_create >= #{recordCreateStart}
            and #{recordCreateEnd} >= r.gmt_create
        </if>
        <if test="recordModifiedStart != null and recordModifiedEnd != null">
            and r.gmt_modified >= #{recordModifiedStart}
            and #{recordModifiedEnd} >= r.gmt_modified
        </if>
    </sql>

    <sql id="orderField">
        <choose>
            <when test="orderField == @com.zto.devops.qc.client.enums.testmanager.coverage.CoverageFieldEnum@branch_record_rate.name()">
                branch_record_rate
            </when>
        </choose>
    </sql>

    <sql id="orderType">
        <choose>
            <when test="orderType == 'ASC'">
                asc
            </when>
            <when test="orderType == 'asc'">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </sql>

</mapper>
