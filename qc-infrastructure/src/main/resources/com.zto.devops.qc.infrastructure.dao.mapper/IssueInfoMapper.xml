<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.IssueInfoMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.IssueInfoEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="code" jdbcType="VARCHAR" property="code"/>
    <result column="report_code" jdbcType="VARCHAR" property="reportCode" />
    <result column="issue_count" jdbcType="INTEGER" property="issueCount" />
    <result column="valid_issue_count" jdbcType="INTEGER" property="validIssueCount"/>
    <result column="legacy_issue_count" jdbcType="INTEGER" property="legacyIssueCount" />
    <result column="legacy_issue_high" jdbcType="INTEGER" property="legacyIssueHigh" />
  </resultMap>

  <select id="selectByReportCode" resultMap="BaseResultMap">
    select *
    from qc_issue_info t
    where t.report_code = #{reportCode} and t.enable=true
  </select>
</mapper>