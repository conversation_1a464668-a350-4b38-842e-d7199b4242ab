<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.CoverageRecordBasicMapper">

    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.CoverageRecordBasicEntity">
        <result column="id" jdbcType="BIGINT" property="id" />
        <result column="dept_id" jdbcType="BIGINT" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="product_code" jdbcType="VARCHAR" property="productCode" />
        <result column="product_name" jdbcType="VARCHAR" property="productName" />
        <result column="version_code" jdbcType="VARCHAR" property="versionCode" />
        <result column="version_name" jdbcType="VARCHAR" property="versionName" />
        <result column="generate_type" property="generateType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.GenerateTypeHandler"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="app_type" jdbcType="VARCHAR" property="appType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AppTypeHandler"/>
        <result column="test_strategy" jdbcType="VARCHAR" property="testStrategy" />
        <result column="is_white_list" jdbcType="BOOLEAN" property="isWhiteList" />
        <result column="commit_id" jdbcType="VARCHAR" property="commitId" />
        <result column="branch_name" jdbcType="VARCHAR" property="branchName" />
        <result column="enable" jdbcType="BOOLEAN" property="enable" />
        <result column="record_url" jdbcType="VARCHAR" property="recordUrl" />
        <result column="record_type" jdbcType="VARCHAR" property="recordType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.RecordTypeHandler"/>
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.CoverageRecordStatusHandler" />
        <result column="record_rate" jdbcType="DECIMAL" property="recordRate" />
        <result column="record_error_msg" jdbcType="VARCHAR" property="recordErrorMsg" />
        <result column="standard_rate" jdbcType="DECIMAL" property="standardRate" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="basic_branch_name" jdbcType="VARCHAR" property="basicBranchName" />
        <result column="basic_commit_id" jdbcType="VARCHAR" property="basicCommitId" />
        <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
        <result column="modifier" jdbcType="VARCHAR" property="modifier" />
        <result column="gmt_modified" jdbcType="DATE" property="gmtModified" />
        <result column="gmt_create" jdbcType="DATE" property="gmtCreate" />
        <result column="env_name" jdbcType="VARCHAR" property="envName" />
        <result column="task_id" jdbcType="VARCHAR" property="taskId" />
        <result column="comment" jdbcType="VARCHAR" property="comment" />
        <result column="diff_type" jdbcType="VARCHAR" property="diffType" />
        <result column="flow_lane_type" jdbcType="VARCHAR" property="flowLaneType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.FlowLaneTypeHandler" />
        <result column="bucket_name" jdbcType="VARCHAR" property="bucketName" />
        <result column="file_name" jdbcType="VARCHAR" property="fileName" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, dept_id, dept_name, product_code, product_name, version_code, version_name,
        generate_type, app_id, app_name, app_type, test_strategy,
        is_white_list, commit_id, branch_name, enable, record_url, record_type, status,
        record_rate, record_error_msg, standard_rate, remark,
        basic_branch_name, basic_commit_id,
        creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified, env_name, task_id,
        comment, diff_type, flow_lane_type, bucket_name, file_name
    </sql>

    <sql id="Base_Publish_Column_List">
        id, app_type, version_code, version_name, app_id,
        commit_id, branch_name, enable, git_url, deployment_identity, service_name,
        port, package_name, gmt_create, gmt_modified, env_name, output_file_name, flow_lane_type
    </sql>

    <select id="getInitialCoverageRecords"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_coverage_record cr
        right join (
        SELECT
        MAX(id) max_id
        FROM
        qc_coverage_record r
        WHERE
        1 = 1
        and (diff_type = '' or diff_type = 'INCREMENT')
        <if test="versionCode != null and versionCode != ''">
            AND r.version_code = #{versionCode}
        </if>
        <if test="recordType != null">
            AND r.record_type = #{recordType}
        </if>
        <if test="appIdList != null and appIdList.size > 0">
        and r.app_id in
        <foreach collection="appIdList" item="item" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
    </if>
        GROUP BY app_id) t on t.max_id = cr.id
        where cr.remark != '取消应用关联关系'
    </select>

    <select id="getExistCoverageRecords"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_coverage_record r
        WHERE r.enable = 1
        <if test="versionCode != null and versionCode != ''">
            AND r.version_code = #{versionCode}
        </if>
        <if test="branchNames != null and branchNames.size > 0">
            and r.branch_name in
            <foreach collection="branchNames" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="appType != null and appType != ''">
            AND r.app_type = #{appType}
        </if>
    </select>

    <select id="selectCoverageRecords"
            resultType="com.zto.devops.qc.infrastructure.dao.entity.CoverageRecordGenerateEntity">
        SELECT
        r.id,
        r.product_code,
        r.version_code,
        r.version_name,
        r.app_id,
        r.record_type,
        r.diff_type,
        r.status,
        r.remark,
        p.branch_name,
        p.commit_id,
        p.app_type,
        p.package_name,
        p.output_file_name,
        p.git_url,
        p.gmt_create,
        p.env_name,
        if(p.flow_lane_type = '', 'NULL', p.flow_lane_type) flow_lane_type,
        b.basic_branch_name,
        b.basic_commit_id,
        b.git_project_id
        FROM qc_coverage_record r
        LEFT JOIN
        (SELECT
        <include refid="Base_Publish_Column_List"/>
        FROM
        qc_coverage_publish
        WHERE
        id IN (SELECT
        MAX(id)
        FROM
        qc_coverage_publish
        WHERE 1 = 1
        <if test="versionCode != null and versionCode != ''">
            AND version_code = #{versionCode}
        </if>
        <if test="appId != null and appId != ''">
            AND app_id = #{appId}
        </if>
        <if test="branchName != null and branchName != ''">
            AND branch_name = #{branchName}
        </if>
        <choose>
            <when test="envName != null and envName != ''">
                AND env_name = #{envName}
            </when>
            <otherwise>
                <if test="flowLaneType != null">
                    AND flow_lane_type in ('FLOW_TEST','FLOW_PROD')
                </if>
            </otherwise>
        </choose>
        GROUP BY app_id)) p ON p.version_code = r.version_code
        AND p.app_id = r.app_id
        left join qc_coverage_branch_basic b on b.version_code = r.version_code
        and b.app_id = r.app_id and b.branch_name = r.branch_name
        WHERE r.enable = 1
        <if test="versionCode != null and versionCode != ''">
            AND r.version_code = #{versionCode}
        </if>
        <if test="recordType != null">
            AND r.record_type = #{recordType}
        </if>
        <if test="appId != null and appId != ''">
            AND r.app_id = #{appId}
        </if>
        <if test="diffType != null">
            AND r.diff_type = #{diffType}
        </if>
        ORDER by id DESC LIMIT 1
    </select>

    <insert id="batchInsert">
        insert into qc_coverage_record (app_id, app_name, product_code, product_name, branch_name,
        version_code, version_name, record_type, status, test_strategy, dept_id, dept_name, standard_rate)
        values
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.appId,jdbcType=VARCHAR},
            #{item.appName,jdbcType=VARCHAR},
            #{item.productCode,jdbcType=VARCHAR},
            #{item.productName,jdbcType=VARCHAR},
            #{item.branchName,jdbcType=VARCHAR},
            #{item.versionCode,jdbcType=VARCHAR},
            #{item.versionName,jdbcType=VARCHAR},
            #{item.recordType,jdbcType=VARCHAR},
            #{item.status,jdbcType=VARCHAR},
            #{item.testStrategy,jdbcType=VARCHAR},
            #{item.deptId,jdbcType=VARCHAR},
            #{item.deptName,jdbcType=VARCHAR},
            #{item.standardRate,jdbcType=DECIMAL})
        </foreach>
    </insert>

    <update id="delCoverageRecord">
        update qc_coverage_record
        set enable = 0, gmt_modified = now()
        where enable = 1
        <if test="versionCode != null and versionCode != ''">
            AND version_code = #{versionCode}
        </if>
        <if test="recordType != null">
            AND record_type = #{recordType}
        </if>
        <if test="appId != null and appId != ''">
            AND app_id = #{appId}
        </if>
        <if test="diffType != null">
            AND diff_type = #{diffType}
        </if>
    </update>

    <update id="batchUpdate">
        update qc_coverage_record
        set record_error_msg = #{recordErrorMsg}, status = #{status}, gmt_modified = now()
        where enable = 1
        <if test="versionCode != null and versionCode != ''">
            AND version_code = #{versionCode}
        </if>
        <if test="recordType != null">
            AND record_type = #{recordType}
        </if>
        <if test="list != null and list.size > 0">
            and app_id in
            <foreach collection="list" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="updateCoverageRecordById">
        update qc_coverage_record
        <trim prefix="set" suffixOverrides=",">
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="recordErrorMsg != null and recordErrorMsg != ''">
                record_error_msg = #{recordErrorMsg},
            </if>
            <if test="recordUrl != null and recordUrl != ''">
                record_url = #{recordUrl},
            </if>
            <if test="commitId != null and commitId != ''">
                commit_id = #{commitId},
            </if>
            <if test="basicCommitId != null and basicCommitId != ''">
                basic_commit_id = #{basicCommitId},
            </if>
            <if test="basicBranchName != null and basicBranchName != ''">
                basic_branch_name = #{basicBranchName},
            </if>
            <if test="recordRate != null ">
                record_rate = #{recordRate},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="codeCoverNum != null ">
                code_cover_num = #{codeCoverNum},
            </if>
            <if test="codeSum != null ">
                code_sum = #{codeSum},
            </if>
            <if test="envName != null and envName != ''">
                env_name = #{envName},
            </if>
            <if test="gitCompareUrl != null and gitCompareUrl != ''">
                git_compare_url = #{gitCompareUrl},
            </if>
            <if test="bucketName != null and bucketName != ''">
                bucket_name = #{bucketName},
            </if>
            <if test="fileName != null and fileName != ''">
                file_name = #{fileName},
            </if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="getRemark"
            resultType="java.lang.String">
        SELECT remark
        FROM qc_coverage_record
        WHERE enable = 0
        AND version_code = #{versionCode}
        AND app_id = #{appId}
        AND record_type = #{recordType}
        ORDER BY id DESC
        LIMIT 1
    </select>

    <select id="selectListByVersionCode" resultType="com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageAppInfoVO" parameterType="java.lang.String">
        select qcr.version_code as versionCode,
        qcr.app_id as appId,
        qcr.remark as reason,
        qcr.status as status,
        qcr.record_url as recordUrl,
        qcr.record_type as recordType,
        qcr.record_rate as recordRate,
        qcr.standard_rate as standardRate
        from qc_coverage_record qcr
        where 1 = 1
        and qcr.diff_type = 'INCREMENT'
        and qcr.test_strategy is not null
        and qcr.test_strategy != 'EXPLORE_TEST'
        and qcr.enable = 1
        and qcr.version_code = #{versionCode}
        <if test="list != null and list.size > 0">
            and qcr.app_id in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by qcr.app_id
    </select>

    <select id="selectListByVersionCodeList" resultType="com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageAppInfoVO" parameterType="java.lang.String">
        select qcr.version_code as versionCode,
        qcr.version_name as versionName,
        qcr.app_id as appId,
        qcr.remark as reason,
        qcr.status as status,
        qcr.record_url as recordUrl,
        qcr.record_type as recordType,
        qcr.record_rate as recordRate,
        qcr.standard_rate as standardRate
        from qc_coverage_record qcr
        where 1 = 1
        and qcr.diff_type = 'INCREMENT'
        and qcr.test_strategy is not null
        and qcr.enable = 1
        and qcr.version_code in
        <foreach collection="versionCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="list != null and list.size > 0">
            and qcr.app_id in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by qcr.app_id
    </select>

    <select id="getCoverageRunningList"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_coverage_record r
        WHERE r.enable = 1
        AND r.status = 'RUNNING'
        AND #{nowTime} >= r.gmt_modified
    </select>

    <select id="getGitCompareUrl" resultType="string">
        select concat(replace(p.git_url, '.git', ''), '/-/compare/master...', p.branch_name, '?from_project_id=', r.git_project_id, '&amp;straight=false&amp;view=parallel') as gitCompareUrl
        from qc_coverage_branch_basic r
        left join qc_coverage_publish p on r.version_code = p.version_code and r.app_id = p.app_id and r.branch_name = p.branch_name
        where r.version_code = #{versionCode} and r.app_id = #{appId} and r.branch_name = #{branchName} limit 1
    </select>

    <select id="getCoverageRecordListByLimit" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_coverage_record
        where
        status = 'SUCCEED'
        and bucket_name = ''
        limit 100
    </select>

    <select id="getExistCoverageRecordsByAppIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_coverage_record r
        WHERE r.enable = 1
        <if test="versionCode != null and versionCode != ''">
            AND r.version_code = #{versionCode}
        </if>
        <if test="branchNames != null and branchNames.size > 0">
            and r.branch_name in
            <foreach collection="branchNames" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="appType != null and appType != ''">
            AND r.app_type = #{appType}
        </if>
        <if test="appIdList != null and appIdList.size > 0">
            and r.app_id in
            <foreach collection="appIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectAutoGenerateResultByTaskId" resultMap="BaseResultMap">
        select qcr.version_code,
               qcr.version_name,
               qcr.product_code,
               qcr.app_id,
               qcr.app_name,
               qcr.status,
               qcr.record_error_msg
        from qc_coverage_record qcr
        where 1 = 1
          and qcr.enable = true
          and qcr.generate_type = 'AUTO'
          and qcr.record_type = 'BRANCH'
          and qcr.diff_type = 'INCREMENT'
          and qcr.task_id = #{taskId}
        <if test="statusList != null and statusList.size > 0">
            and qcr.status in
            <foreach collection="statusList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        order by qcr.gmt_modified desc
    </select>

    <select id="selectAutoGenerateResultByAppIdList"
            resultType="com.zto.devops.qc.infrastructure.dao.entity.CoverageRecordBasicEntity">
        select qcr.version_code,
        qcr.version_name,
        qcr.product_code,
        qcr.app_id,
        qcr.status,
        qcr.record_error_msg
        from qc_coverage_record qcr
        where 1 = 1
        and qcr.enable = true
        and qcr.generate_type = 'AUTO'
        and qcr.record_type = 'BRANCH'
        and qcr.diff_type = 'INCREMENT'
        and qcr.remark != '取消应用关联关系'
        and qcr.version_code = #{versionCode}
        <if test="appIdList != null and appIdList.size > 0">
            and qcr.app_id in
            <foreach collection="appIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        order by qcr.gmt_modified desc
    </select>

</mapper>
