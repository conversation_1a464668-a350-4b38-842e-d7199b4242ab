<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TagMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TagEntity">
    <id column="code" jdbcType="VARCHAR" property="code" />
    <result column="domain" jdbcType="VARCHAR" property="domain" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler"/>
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="type" jdbcType="VARCHAR" property="type" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.TagTypeHandler"/>
    <result column="tag_alias" jdbcType="VARCHAR" property="tagAlias" />
    <result column="tag_name" jdbcType="VARCHAR" property="tagName" />
  </resultMap>

  <insert id="saveBatch" parameterType="list" keyProperty="id" useGeneratedKeys="true">
    INSERT INTO qc_tag (
    code,
    domain,
    business_code,
    type,
    tag_name,
    creator_id,
    creator,
    modifier_id,
    modifier)
    VALUES
    <foreach collection="list" separator="," item="item" index="index">
      (#{item.code},
      #{item.domain},
      #{item.businessCode},
      #{item.type},
      #{item.tagName},
      #{item.creatorId},
      #{item.creator},
      #{item.modifierId},
      #{item.modifier})
    </foreach>
  </insert>

  <select id = "tagTestcaseNo" resultType="java.lang.Long">
        select count(*) from qc_tag s left join tm_testcase t on s.business_code = t.code
        where s.tag_name  = #{tagName} and s.domain = #{domain} and s.enable = 1  and t.product_code = #{productCode}
  </select>

  <select id = "tagTestcaseCodeList" resultType="java.lang.String">
    select s.code from qc_tag s left join tm_testcase t on s.business_code = t.code
    where s.tag_name  = #{tagName} and s.domain = #{domain} and s.enable = 1  and t.product_code = #{productCode}
  </select>

  <delete id="deleteByCodes" >
    delete from  qc_tag where code in
    <foreach collection="codes" item="code" separator="," open="(" close=")">#{code}</foreach>
  </delete>

  <select id="queryBusinessTagWithFixVersionCode" resultMap="BaseResultMap" parameterType="string">
    select *
    from qc_tag t
           left join qc_issue r on t.business_code = r.code
    where  t.enable = 1 and r.fix_version_code = #{versionCode} and t.type = 'BUSINESS'
  </select>

  <insert id="insertSceneTag">
    insert into qc_tag (
      code,
      domain,
      business_code,
      tag_name,
      tag_alias,
      type,
      creator_id,
      creator,
      modifier_id,
      modifier
    ) select
      #{code},
      #{domain, typeHandler=com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler},
      #{businessCode},
      #{tagName},
      #{tagAlias},
      #{type, typeHandler=com.zto.devops.qc.infrastructure.dao.typehandler.TagTypeHandler},
      #{creatorId},
      #{creator},
      #{modifierId},
      #{modifier}
    from dual
    where not exists (
      select id from qc_tag where enable = 1
      and domain = #{domain, typeHandler=com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler}
      and type = #{type, typeHandler=com.zto.devops.qc.infrastructure.dao.typehandler.TagTypeHandler}
      and business_code = #{businessCode} and tag_name = #{tagName} and tag_alias = #{tagAlias}
    )
  </insert>

</mapper>