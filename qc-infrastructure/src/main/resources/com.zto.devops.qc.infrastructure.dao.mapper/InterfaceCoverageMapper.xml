<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.InterfaceCoverageMapper">

    <insert id="batchSave" parameterType="list">
        <foreach collection="list" separator=";" item="item" index="index">
            INSERT INTO `lbd_qc`.`qc_interface_coverage`
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.interfaceCoverageCode != null">
                    interface_coverage_code,
                </if>
                <if test="item.appId != null">
                    app_id,
                </if>
                <if test="item.versionCode != null">
                    version_code,
                </if>
                <if test="item.versionName != null">
                    version_name,
                </if>
                <if test="item.commitId != null">
                    commit_id,
                </if>
                <if test="item.interfaceDocAddress != null">
                    interface_doc_address,
                </if>
                <if test="item.interfaceFullClassName != null">
                    interface_full_class_name,
                </if>
                <if test="item.interfaceMethodName != null">
                    interface_method_name,
                </if>
                <if test="item.interfaceMethodDesc != null">
                    interface_method_desc,
                </if>
                <if test="item.interfaceMethodType != null">
                    interface_method_type,
                </if>
                <if test="item.interfaceMethodAnnotation != null">
                    interface_method_annotation,
                </if>
                <if test="item.interfaceCallNumber != null">
                    interface_call_number,
                </if>
                <if test="item.interfaceErrorNumber != null">
                    interface_error_number,
                </if>
                <if test="item.modifyClassName != null">
                    modify_class_name,
                </if>
                <if test="item.modifyMethodName != null">
                    modify_method_name,
                </if>
                <if test="item.modifyMethodDesc != null">
                    modify_method_desc,
                </if>
                <if test="item.modifyMethodMd5 != null">
                    modify_method_md5,
                </if>
                <if test="item.interfaceAlias != null">
                    interface_alias,
                </if>
                <if test="item.methodParameterStr != null">
                    method_parameter_str,
                </if>
                <if test="item.isCovered != null">
                    is_covered,
                </if>
                <if test="item.zcatMetricKey != null">
                    zcat_metric_key,
                </if>
                <if test="item.creatorId != null">
                    creator_id,
                </if>
                <if test="item.creator != null">
                    creator,
                </if>
                <if test="item.gmtCreate != null">
                    gmt_create,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.interfaceCoverageCode != null">
                    #{item.interfaceCoverageCode,jdbcType=VARCHAR},
                </if>
                <if test="item.appId != null">
                    #{item.appId,jdbcType=VARCHAR},
                </if>
                <if test="item.versionCode != null">
                    #{item.versionCode,jdbcType=VARCHAR},
                </if>
                <if test="item.versionName != null">
                    #{item.versionName,jdbcType=VARCHAR},
                </if>
                <if test="item.commitId != null">
                    #{item.commitId,jdbcType=VARCHAR},
                </if>
                <if test="item.interfaceDocAddress != null">
                    #{item.interfaceDocAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.interfaceFullClassName != null">
                    #{item.interfaceFullClassName,jdbcType=VARCHAR},
                </if>
                <if test="item.interfaceMethodName != null">
                    #{item.interfaceMethodName,jdbcType=VARCHAR},
                </if>
                <if test="item.interfaceMethodDesc != null">
                    #{item.interfaceMethodDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.interfaceMethodType != null">
                    #{item.interfaceMethodType,jdbcType=VARCHAR},
                </if>
                <if test="item.interfaceMethodAnnotation != null">
                    #{item.interfaceMethodAnnotation,jdbcType=VARCHAR},
                </if>
                <if test="item.interfaceCallNumber != null">
                    #{item.interfaceCallNumber,jdbcType=INTEGER},
                </if>
                <if test="item.interfaceErrorNumber != null">
                    #{item.interfaceErrorNumber,jdbcType=INTEGER},
                </if>
                <if test="item.modifyClassName != null">
                    #{item.modifyClassName,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyMethodName != null">
                    #{item.modifyMethodName,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyMethodDesc != null">
                    #{item.modifyMethodDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyMethodMd5 != null">
                    #{item.modifyMethodMd5,jdbcType=VARCHAR},
                </if>
                <if test="item.interfaceAlias != null">
                    #{item.interfaceAlias,jdbcType=VARCHAR},
                </if>
                <if test="item.methodParameterStr != null">
                    #{item.methodParameterStr,jdbcType=VARCHAR},
                </if>
                <if test="item.isCovered != null">
                    #{item.isCovered,jdbcType=INTEGER},
                </if>
                <if test="item.zcatMetricKey != null">
                    #{item.zcatMetricKey,jdbcType=VARCHAR},
                </if>
                <if test="item.creatorId != null">
                    #{item.creatorId,jdbcType=BIGINT},
                </if>
                <if test="item.creator != null">
                    #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.gmtCreate != null">
                    #{item.gmtCreate,jdbcType=TIMESTAMP},
                </if>
            </trim>
        </foreach>
    </insert>
</mapper>
