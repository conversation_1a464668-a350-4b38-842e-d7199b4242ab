<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TestcaseExecuteRecordMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TestcaseExecuteRecordEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="testcase_code" property="testcaseCode" jdbcType="VARCHAR"/>
        <result column="test_plan_code" property="testPlanCode" jdbcType="VARCHAR"/>
        <result column="test_stage" property="testStage" jdbcType="VARCHAR"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanStageHandler"/>
        <result column="result" property="result" jdbcType="VARCHAR"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseStatusHandler"/>
        <result column="automatic_task_code" property="automaticTaskCode" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="finish_time" property="finishTime" jdbcType="TIMESTAMP"/>
        <result column="result_file" property="resultFile" jdbcType="VARCHAR"/>
        <result column="exec_log_file" property="execLogFile" jdbcType="VARCHAR"/>
        <result column="report_file" property="reportFile" jdbcType="VARCHAR"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP"/>
        <result column="modifier_id" property="modifierId" jdbcType="BIGINT"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="TestcaseExecuteRecordVO" type="com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseExecuteRecordVO">
        <result column="id" property="id" jdbcType="BIGINT" />
        <result column="test_plan_code" property="testPlanCode" jdbcType="VARCHAR"/>
        <result column="plan_name" property="testPlanName" jdbcType="VARCHAR"/>
        <result column="test_stage" property="testStage" jdbcType="VARCHAR"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanStageHandler"/>
        <result column="result" property="result" jdbcType="VARCHAR"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseStatusHandler"/>
        <result column="creator_id" property="executorId" jdbcType="BIGINT"/>
        <result column="creator" property="executor" jdbcType="VARCHAR"/>
        <result column="gmt_modified" property="executeTime" jdbcType="TIMESTAMP"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="finish_time" property="finishTime" jdbcType="TIMESTAMP"/>
        <result column="report_file" property="reportFile" jdbcType="VARCHAR"/>
        <result column="exec_log_file" property="execLogFile" jdbcType="VARCHAR"/>
        <result column="env"  property="env" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectByTestcaseCode" resultMap="TestcaseExecuteRecordVO">
        SELECT
            t1.id,
            t1.test_plan_code,
            t1.test_stage,
            t1.result,
            t1.creator_id,
            t1.creator,
            t1.gmt_modified,
            t1.start_time,
            t1.finish_time,
            t1.exec_log_file,
            t1.report_file,
            t2.plan_name,
            t3.env
        FROM tm_testcase_execute_record t1
        LEFT JOIN tm_test_plan t2 ON t2.code = t1.test_plan_code
        LEFT JOIN tm_automatic_task t3 ON t3.code = t1.automatic_task_code
        WHERE t1.testcase_code = #{code}
        <if test="type != null">
        AND t3.trig_mode = #{type}
        </if>
        ORDER BY t1.gmt_create DESC
    </select>

    <resultMap id="ListExecuteCaseVO" type="com.zto.devops.qc.client.model.testmanager.cases.entity.ListExecuteCaseVO">
        <result column="testcase_code" property="code" jdbcType="VARCHAR" />
        <result column="automatic_task_code" property="automaticTaskCode" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="attribute" property="attribute" jdbcType="VARCHAR"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcaseAttributeTypeHandler"/>
        <result column="parent_code" property="parentCode" jdbcType="VARCHAR"/>
        <result column="path" property="path" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="enable" property="enable" jdbcType="BOOLEAN" />
        <result column="result" property="result" jdbcType="VARCHAR"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseStatusHandler"/>
        <result column="creator_id" property="executorId" jdbcType="BIGINT"/>
        <result column="creator" property="executor" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="finish_time" property="finishTime" jdbcType="TIMESTAMP"/>
        <result column="report_file" property="reportFile" jdbcType="VARCHAR"/>
        <result column="exec_log_file" property="execLogFile" jdbcType="VARCHAR"/>
        <result column="type" jdbcType="VARCHAR" property="type"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcaseTypeTypeHandler"/>
    </resultMap>

    <resultMap id="TestcaseExecuteNumVO" type="com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseExecuteNumVO">
        <result column="testcase_code" property="code" jdbcType="VARCHAR" />
        <result column="execute_num" property="executeNum" jdbcType="INTEGER"/>
    </resultMap>

    <select id="selectByAutomaticTaskCode" resultMap="ListExecuteCaseVO">
        SELECT
            t1.testcase_code,
            t1.automatic_task_code,
            t1.result,
            t1.creator_id,
            t1.creator,
            t1.start_time,
            t1.finish_time,
            t1.exec_log_file,
            t1.report_file,
            t2.name,
            t2.attribute,
            t2.parent_code,
            t2.path,
            t2.sort,
            t2.enable
        FROM tm_testcase_execute_record t1
        LEFT JOIN tm_testcase t2 ON t2.code = t1.testcase_code
        WHERE t1.automatic_task_code = #{automaticTaskCode}
        <if test="executorIdList != null and executorIdList.size() > 0">
            AND t1.creator_id IN
            <foreach collection="executorIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="status != null and status.size() > 0">
            AND t1.result IN
            <foreach collection="status" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTimeBegin != null ">
            AND t1.start_time > #{startTimeBegin}
        </if>
        <if test="startTimeEnd != null ">
            AND t1.start_time  <![CDATA[ < ]]>  #{startTimeEnd}
        </if>
        <if test="finishTimeBegin != null ">
            AND t1.finish_time > #{finishTimeBegin}
        </if>
        <if test="finishTimeEnd != null ">
            AND t1.finish_time  <![CDATA[ < ]]>  #{finishTimeEnd}
        </if>
        ORDER BY t2.id
    </select>

    <resultMap id="ExecuteCaseVO" type="com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteCaseVO">
        <result column="testcase_code" property="code" jdbcType="VARCHAR" />
        <result column="automatic_task_code" property="automaticTaskCode" jdbcType="VARCHAR"/>
        <result column="test_plan_code" property="testPlanCode" jdbcType="VARCHAR"/>
        <result column="result" property="result" jdbcType="VARCHAR"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseStatusHandler"/>
        <result column="result_file" property="resultFile" jdbcType="VARCHAR"/>
        <result column="report_file" property="reportFile" jdbcType="VARCHAR"/>
        <result column="exec_log_file" property="execLogFile" jdbcType="VARCHAR"/>
        <result column="creator_id" property="executorId" jdbcType="BIGINT"/>
        <result column="creator" property="executor" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="parent_code" property="parentCode" jdbcType="VARCHAR"/>
        <result column="product_code" property="productCode" jdbcType="VARCHAR"/>
        <result column="type" jdbcType="VARCHAR" property="type"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcaseTypeTypeHandler" />
        <result column="priority" jdbcType="VARCHAR" property="priority"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcasePriorityTypeHandler" />
        <result column="enable" property="enable" jdbcType="BOOLEAN" />
        <result column="node_type" jdbcType="VARCHAR" property="nodeType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticNodeTypeHandler" />
        <result column="modifier_id" property="modifierId" jdbcType="BIGINT"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP"/>
        <result column="path" property="path" jdbcType="VARCHAR"/>
        <result column="comment" property="comment" jdbcType="VARCHAR"/>
        <result column="task_id" property="automaticTaskId" jdbcType="VARCHAR"/>
        <result column="plan_name" property="testPlanName" jdbcType="VARCHAR"/>
        <result column="version_code" property="versionCode" jdbcType="VARCHAR"/>
        <result column="automatic_source_type" jdbcType="VARCHAR" property="automaticRecordType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticRecordTypeHandler"/>
        <result column="interface_name" property="interfaceName" jdbcType="VARCHAR" />
    </resultMap>

    <select id="selectExecuteCase" resultMap="ExecuteCaseVO">
        SELECT
            t1.testcase_code,
            t1.automatic_task_code,
            t1.test_plan_code,
            t1.result,
            t1.result_file,
            t1.exec_log_file,
            t1.report_file,
            t1.creator_id,
            t1.creator,
            t2.name,
            t2.product_code,
            t2.parent_code,
            t2.type,
            t2.priority,
            t2.node_type,
            t2.enable,
            t2.modifier_id,
            t2.modifier,
            t2.gmt_modified,
            t2.path,
            t2.comment,
            t2.interface_name,
            t3.task_id,
            t4.plan_name,
            t4.version_code,
            t5.type as automatic_source_type
        FROM tm_testcase_execute_record t1
        LEFT JOIN tm_testcase t2 ON t2.code = t1.testcase_code
        LEFT JOIN tm_automatic_task t3 ON t3.code = t1.automatic_task_code
        LEFT JOIN tm_test_plan t4 ON t4.code = t1.test_plan_code
        INNER JOIN tm_automatic_source_record t5 on t5.code = t2.automatic_source_code
        WHERE t1.testcase_code = #{testcaseCode}
            AND t1.automatic_task_code = #{automaticTaskCode}
    </select>

    <select id="selectCurrentResult" resultMap="ExecuteCaseVO">
        SELECT
            t1.result_file,
            t1.report_file,
            t1.exec_log_file,
            t2.task_id
        FROM tm_testcase_execute_record t1
        LEFT JOIN tm_automatic_task t2 ON t2.code = t1.automatic_task_code
        WHERE t1.testcase_code = #{caseCode}
            AND t1.test_plan_code = #{planCode}
            AND t1.test_stage = #{testStage}
        ORDER BY t1.id DESC
        LIMIT 1
    </select>

    <select id="selectLastRecord" resultType="com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanCaseEntity">
        SELECT
            result AS status,
            creator_id AS executorId,
            creator AS executor
        FROM tm_testcase_execute_record
        WHERE result != 'TERMINATION'
            AND testcase_code = #{testcaseCode}
            AND test_plan_code = #{testPlanCode}
            AND test_stage = #{testStage}
        ORDER BY id DESC
        LIMIT 1
    </select>

    <select id="selectExecuteNumByTestcaseCodeList" parameterType="java.util.List" resultMap="TestcaseExecuteNumVO">
        select
        testcase_code,
        count(1) as execute_num
        from tm_testcase_execute_record
        <if test="testcaseCodeList != null and testcaseCodeList.size > 0">
            where testcase_code in
            <foreach collection="testcaseCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by testcase_code
    </select>

    <insert id="insertBatch">
        INSERT INTO tm_testcase_execute_record (
        <if test="entity.testPlanCode != null and entity.testStage != null">
            test_plan_code, test_stage,
        </if>
        <if test="entity.creatorId != null">
            creator_id, creator,
        </if>
        <if test="entity.modifierId != null">
            modifier_id, modifier,
        </if>
            testcase_code, result, automatic_task_code, start_time, gmt_create
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            <if test="entity.testPlanCode != null and entity.testStage != null">
                #{entity.testPlanCode}, #{entity.testStage},
            </if>
            <if test="entity.creatorId != null">
                #{entity.creatorId}, #{entity.creator},
            </if>
            <if test="entity.modifierId != null">
                #{entity.modifierId}, #{entity.modifier},
            </if>
            #{item}, #{entity.result}, #{entity.automaticTaskCode}, #{entity.startTime}, #{entity.gmtCreate}
            )
        </foreach>
    </insert>

    <select id="selectTestcaseCodeList" resultType="java.lang.String">
        SELECT
            testcase_code
        FROM tm_testcase_execute_record
        WHERE automatic_task_code = #{automaticTaskCode}
    </select>

    <select id="selectApiExecuteRecord" resultMap="BaseResultMap">
        SELECT t.testcase_code,
               t.result,
               t.automatic_task_code,
               t.start_time,
               t.finish_time,
               t.enable,
               t.creator_id,
               t.creator,
               t.gmt_create,
               t.modifier_id,
               t.modifier,
               t.gmt_modified,
               t.result_file,
               t.exec_log_file,
               t.report_file
        FROM tm_testcase_execute_record t
        WHERE t.automatic_task_code = #{code}
        <if test="executorIdList != null and executorIdList.size() > 0">
            AND t.creator_id IN
            <foreach collection="executorIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="status != null and status.size() > 0">
            AND t.result IN
            <foreach collection="status" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTimeBegin != null ">
            AND t.start_time > #{startTimeBegin}
        </if>
        <if test="startTimeEnd != null ">
            AND t.start_time  <![CDATA[ < ]]>  #{startTimeEnd}
        </if>
        <if test="finishTimeBegin != null ">
            AND t.finish_time > #{finishTimeBegin}
        </if>
        <if test="finishTimeEnd != null ">
            AND t.finish_time  <![CDATA[ < ]]>  #{finishTimeEnd}
        </if>
        ORDER BY t.id
    </select>

    <update id="updateResultNotFinished">
        UPDATE
            tm_testcase_execute_record t1,
            tm_automatic_task t2
        SET
            t1.exec_log_file = t2.exec_log_file,
            t1.report_file = t2.report_file,
            t1.result = #{result}
        WHERE t1.automatic_task_code = t2.code
        AND t1.automatic_task_code = #{automaticTaskCode}
        AND t1.finish_time is null
    </update>

    <select id="selectLastThreeTimes" resultMap="BaseResultMap">
        SELECT
            tter.testcase_code testcaseCode,
            tter.result result
        FROM tm_testcase_execute_record tter
        left join tm_automatic_task tat on tat.code = tter.automatic_task_code
        WHERE 1=1
        AND testcase_code = #{testcaseCode}
        AND tat.trig_mode = 'HEART_BEAT'
        AND tter.start_time <![CDATA[ <= ]]>(
            select tt.start_time from tm_testcase_execute_record tt
            left join tm_automatic_task ta on ta.code = tt.automatic_task_code
            where tt.automatic_task_code = #{automaticTaskCode}
            and tt.testcase_code = #{testcaseCode}
            and ta.trig_mode = 'HEART_BEAT'
            limit 1
        )
        ORDER BY tter.start_time desc
        LIMIT 3
    </select>

    <select id="selectWithListExecuteCaseVO" resultMap="ListExecuteCaseVO">
        SELECT
            code,
            name,
            attribute,
            enable,
            parent_code
        FROM tm_testcase
        WHERE code IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ORDER BY id
    </select>

    <select id="getRecordNotSuccess" resultType="java.lang.Long">
        select id
        from tm_testcase_execute_record
        where automatic_task_code = #{taskCode}
        and result not in ('PASSED', 'FAILED')
    </select>



    <select id="selectByVersionCode" resultMap="ListExecuteCaseVO">
        SELECT
            s.code,s.type,
            ( SELECT r.result FROM tm_testcase_execute_record r WHERE r.testcase_code = code ORDER BY id DESC LIMIT 1 ) as result
        FROM
            tm_testcase s
        WHERE
        s.version_code = #{versionCode}
            and s.enable = 1 and s.attribute ="TESTCASE"
    </select>


    <select id="selectAutoByPlanCode" resultMap="ListExecuteCaseVO">
        SELECT
            s.case_code as code ,
            s.status as result,"AUTO" as type
        FROM
            tm_test_plan_case s
        WHERE  s.enable = 1 and s.plan_code = #{planCode} and s.case_type = "AUTO"
        <if test="testStage != null">
            and s.test_stage = #{testStage}
        </if>
    </select>

    <select id="selectManualByPlanCode" resultMap="ListExecuteCaseVO">
        SELECT
        s.case_code as code ,
        s.status as result,"MANUAL" as type
        FROM
        tm_test_plan_case s left join tm_testcase t  on s.case_code = t.code
        WHERE  s.enable = 1 and s.plan_code = #{planCode} and s.case_type = "MANUAL"
        <if test="testStage != null">
            and s.test_stage = #{testStage}
        </if>
    </select>

    <select id="selectStatusByTaskId"
            resultType="com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum">
        select
            t2.result
        from tm_automatic_task t1
        left join tm_testcase_execute_record t2 on t2.automatic_task_code = t1.code
        where t1.task_id = #{taskId}
    </select>
</mapper>
