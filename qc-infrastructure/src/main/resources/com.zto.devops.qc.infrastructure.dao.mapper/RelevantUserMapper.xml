<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.RelevantUserMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.RelevantUserEntity">
        <id column="code" jdbcType="VARCHAR" property="code" />
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="handle_type" jdbcType="VARCHAR" property="handleType"/>
        <result column="type" jdbcType="VARCHAR" property="type" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.RelevantUserTypeHandler"/>
        <result column="business_code" jdbcType="VARCHAR" property="businessCode"/>
        <result column="domain" jdbcType="VARCHAR" property="domain" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler"/>
        <result column="old_object_id" jdbcType="BIGINT" property="oldObjectId"/>
        <result column="action" jdbcType="BIGINT" property="action"/>
        <result column="access_tag" jdbcType="BIGINT" property="accessTag"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
    </resultMap>

    <select id="queryMyObj" resultType="com.zto.devops.qc.client.model.parameter.TaskResultParameter"
            parameterType="com.zto.devops.qc.client.model.parameter.TaskBaseParameter">
        SELECT DISTINCT b.code business_code, b.title name, b.status, b.priority, b.handle_user_id, b.handle_user_name, b.gmt_create, 'ISSUE' domain FROM qc_issue b
        inner join qc_relevant_user u on b.code = u.business_code
        where b.enable = 1
        <if test="name != null and  name != ''  ">
            and (b.`title` like CONCAT('%',#{name},'%') or b.code like CONCAT('%',#{name},'%'))
        </if>
        and u.enable =1 and u.domain = 'ISSUE' and
        u.type in (
        'CURRENT_HANDLE_USER',
        'HANDLED_USER',
        'CREATOR',
        'CC'
        ) and u.user_id = #{userId}
    </select>

    <select id="queryMyIssue" resultType="com.zto.devops.qc.client.model.parameter.TaskResultParameter"
            parameterType="com.zto.devops.qc.client.model.parameter.TaskBaseParameter">
        SELECT
        t1.code,t1.business_code,timestampdiff(hour,t2.gmt_create,now()) as warnDay,t1.type as relevantUserType,t2.gmt_create as gmtCreate,
        t2.title as name,t2.`status`,t2.priority,t2.product_code,
        (case t2.priority when 'URGENCY' then 5
        when 'HIGH' then 4
        when 'MIDDLE' then 3
        when 'LOW' then 2
        else 1 end ) as priorityIndex,
        t2.title,t2.find_version_code,t2.find_version_name,t2.develop_user_name,t2.test_user_id,t2.test_user_name,
        t2.handle_user_id ,t2.find_user_id,t2.develop_user_id
        FROM (<include refid="pm_relevant_user"/>) t1
        LEFT JOIN qc_issue t2 ON t1.business_code = t2.CODE
        WHERE
        t2.`enable` = 1
        <if test="status != null and status.size>0 ">
            and t2.`status` in
            <foreach collection="status" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="versionCodeList != null  and versionCodeList.size>0 ">
            and t2.find_version_code in
            <foreach collection="versionCodeList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="name != null and  name != ''  ">
            and ( t2.`title` like CONCAT('%',#{name},'%') or t2.code like CONCAT('%',#{name},'%'))
        </if>
        <if test="productCode != null and  productCode != ''  ">
            and t2.product_code = #{productCode}
        </if>
        <if test="orderField!=null and orderField !=''">
            order by <include refid="orderField"/> <include refid="orderType"/>
        </if>
    </select>

    <sql id="pm_relevant_user">
        <choose>
            <when test="relevantUserTypes.contains('HANDLED_USER')">
                select p1.code,p1.business_code,p1.type,p1.gmt_create
                from (
                select * from qc_relevant_user t where t.user_id=#{userId} and t.domain= #{domain}
                and t.type in
                <foreach collection="relevantUserTypes" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
                ) p1
                LEFT JOIN (
                select * from qc_relevant_user t where t.user_id=#{userId} and t.domain=#{domain}
                and t.type in
                <foreach collection="relevantUserTypes" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
                ) p2

                on p1.business_code = p2.business_code and p1.gmt_create &lt; p2.gmt_create

                where p2.id is null
            </when>
            <otherwise>
                select code,business_code,p1.type,p1.gmt_create

                from qc_relevant_user p1

                where p1.user_id= #{userId}
                and p1.type in
                <foreach collection="relevantUserTypes" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
                and p1.`enable` = 1 and p1.domain = #{domain}
            </otherwise>
        </choose>
    </sql>

    <select id="queryMyTaskTotal" resultMap="BaseResultMap">
        select business_code,domain from (select business_code,domain from qc_relevant_user t
        where t.user_id=#{userId} and t.type in
        <foreach collection="type" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
        and t.`enable`=1) t1
        LEFT JOIN qc_issue t2 on t1.business_code=t2.code
        where  t2.`status` in
        <foreach collection="issueStatuses" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
        <if test="productCode != null ">
            and t2.product_code =#{productCode}
        </if>
    </select>

    <select id="findByBusinessCode" resultMap="BaseResultMap">
        select *
        from qc_relevant_user t
        where t.enable = 1
          and t.business_code = #{businessCode}
        order by id desc
    </select>

    <select id="countUserTypeIssueCount" parameterType="list"
            resultType="java.lang.Long">
        select count(DISTINCT r.business_code) total FROM qc_relevant_user r
        left join qc_issue t2 on r.business_code = t2.code
        where r.domain='ISSUE' and r.user_id=#{userId} and r.enable=1 and t2.enable=1
        <if test="status != null  and status.size>0">
            and t2.`status` in
            <foreach collection="status" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <sql id="orderField">
        <choose>
            <when test="orderField == 'priorityIndex'">
                priorityIndex
            </when>
            <when test="orderField == 'gmtCreate'">
                gmtCreate
            </when>
            <when test="orderField == 'warnDay'">
                warnDay
            </when>
        </choose>
    </sql>

    <sql id="orderType">
        <choose>
            <when test="orderType == 'ASC'">
                asc
            </when>
            <when test="orderType == 'asc'">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </sql>

</mapper>