<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.MobileSpecialMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.MobileSpecialEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <id column="code" jdbcType="VARCHAR" property="code" />
    <result column="test_plan_main_code" jdbcType="VARCHAR" property="testPlanMainCode" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="remarks" jdbcType="LONGVARCHAR" property="remarks" />
    <result column="test_plan_code" jdbcType="VARCHAR" property="testPlanCode" />
  </resultMap>
  <sql id="Base_Column_List">
      id, code, test_plan_main_code, creator_id, creator, gmt_create, modifier_id, modifier,
    gmt_modified, enable, test_plan_code,remarks
  </sql>
  <select id="selectMobileSpecialTestByCode" resultType="com.zto.devops.qc.client.model.testPlan.query.MobileSpecialTestPlanDto">
      select sa.code,sa.creator_id,sa.creator,sa.gmt_create,sa.modifier_id,sa.modifier,
             sa.gmt_modified,sa.enable,sa.test_plan_code,sa.remarks,
             te.plan_name,te.product_code,te.product_name,te.type,te.test_director_id,te.test_director_name,
             te.edit_no,te.status,
             te.version_code,te.version_name,te.product_source,
             te.preview,te.json,
             te.plan_name as test_plan_name ,t.code as planCode ,t.dept_id as deptId ,t.dept_name as deptName,
             t.product_director_id as productDirectorId,t.product_director_name as productDirectorName,
             m.start_date,m.publish_date,m.presentation_date,m.approval_exit_date
      from qc_mobile_special sa
               LEFT JOIN qc_test_plan te ON sa.test_plan_code = te.code
               left join qc_test_plan t on t.code = te.plan_code
               left join qc_test_plan_main m ON m.test_plan_code = t. CODE
    where te.code = #{code} and sa.enable = 1
  </select>

</mapper>