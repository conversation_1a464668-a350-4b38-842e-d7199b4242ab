<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.CaseStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.CaseStatisticsEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="code" jdbcType="VARCHAR" property="code"/>
    <result column="report_code" jdbcType="VARCHAR" property="reportCode" />
    <result column="frog_plan_code" jdbcType="VARCHAR" property="frogPlanCode" />
    <result column="frog_plan_name" jdbcType="VARCHAR" property="frogPlanName" />
    <result column="case_count" jdbcType="INTEGER" property="caseCount" />
    <result column="plan_smoke_case_count" jdbcType="INTEGER" property="planSmokeCaseCount" />
    <result column="smoke_access_count" jdbcType="INTEGER" property="smokeAccessCount" />
  </resultMap>

  <select id="selectByReportCode" resultMap="BaseResultMap">
    select *
    from qc_case_statistics t
    where t.report_code = #{reportCode} and t.enable=true
  </select>
</mapper>