<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.AutomaticSchedulerMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.AutomaticSchedulerEntity">
        <id column="scheduler_code" jdbcType="VARCHAR" property="schedulerCode" />
        <result column="id" jdbcType="BIGINT" property="id" />
        <result column="scheduler_name" jdbcType="VARCHAR" property="schedulerName" />
        <result column="product_code" jdbcType="VARCHAR" property="productCode" />
        <result column="switch" jdbcType="BIT" property="switchFlag" />
        <result column="crontab" jdbcType="VARCHAR" property="crontab" />
        <result column="execute_env" jdbcType="VARCHAR" property="executeEnv" />
        <result column="execute_tag" jdbcType="VARCHAR" property="executeTag" />
        <result column="execute_space_code" jdbcType="VARCHAR" property="executeSpaceCode" />
        <result column="coverage_flag" jdbcType="BIT" property="coverageFlag" />
        <result column="message_flag" jdbcType="BIT" property="messageFlag" />
        <result column="execute_result" jdbcType="VARCHAR" property="executeResult"
                typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticStatusHandler"/>
        <result column="executor_id" jdbcType="BIGINT" property="executorId" />
        <result column="executor" jdbcType="VARCHAR" property="executor" />
        <result column="execute_time" jdbcType="TIMESTAMP" property="executeTime" />
        <result column="task_id" jdbcType="VARCHAR" property="taskId" />
        <result column="enable" jdbcType="BIT" property="enable" />
        <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
        <result column="modifier" jdbcType="VARCHAR" property="modifier" />
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    </resultMap>

    <select id="selectBySchedulerCode" resultMap="BaseResultMap">
        select *
        from tm_automatic_scheduler tas
        where tas.scheduler_code = #{schedulerCode} limit 1
    </select>

    <select id="selectList" resultType="com.zto.devops.qc.client.model.testmanager.scheduler.entity.AutomaticSchedulerVO">
        select tas.scheduler_code as schedulerCode,
        tas.scheduler_name as schedulerName,
        tas.switch as switchFlag,
        tas.crontab as crontab,
        tas.execute_env as executeEnv,
        tas.execute_tag as executeTag,
        tas.execute_space_code as executeSpaceCode,
        tas.message_flag as messageFlag,
        tas.execute_result as executeResultName,
        tas.execute_time as executeTime,
        tas.executor as executor,
        tas.creator as creator,
        tas.task_id as taskId,
        count(distinct tasc.case_code) as caseNum
        from tm_automatic_scheduler tas
        left join tm_automatic_scheduler_case tasc on tas.scheduler_code = tasc.scheduler_code
        where 1 = 1
        and tas.enable = true
        and (tasc.id is null or tasc.enable = true)
        and tas.product_code = #{productCode}
        <if test="schedulerName != null and  schedulerName != ''  ">
            and  tas.scheduler_name  like CONCAT('%',#{schedulerName},'%') ESCAPE '/'
        </if>
        <if test="executeStartTime != null and executeEndTime != null">
            and tas.execute_time between #{executeStartTime} and #{executeEndTime}
        </if>
        <if test="switchFlagList != null and switchFlagList.size() > 0">
            AND tas.switch IN
            <foreach collection="switchFlagList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="envList != null and envList.size() > 0">
            AND tas.execute_env IN
            <foreach collection="envList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="executeSpaceCodeList != null and executeSpaceCodeList.size() > 0">
            AND tas.execute_space_code IN
            <foreach collection="executeSpaceCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="resultList != null and resultList.size() > 0">
            AND tas.execute_result IN
            <foreach collection="resultList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="creatorIdList != null and creatorIdList.size() > 0">
            AND tas.creator_id IN
            <foreach collection="creatorIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by tas.scheduler_code
        order by tas.id desc
    </select>

    <select id="selectByProductCode" resultType="com.zto.devops.qc.client.model.testmanager.scheduler.entity.ProductSchedulerVO">
        select
        tas.scheduler_code as schedulerCode,
        tas.scheduler_name as schedulerName,
        tas.product_code as productCode
        from tm_automatic_scheduler tas
        where tas.product_code = #{productCode}
        <if test="schedulerName != null and  schedulerName != ''  ">
            and tas.scheduler_name like CONCAT('%',#{schedulerName},'%')
        </if>
    </select>
</mapper>