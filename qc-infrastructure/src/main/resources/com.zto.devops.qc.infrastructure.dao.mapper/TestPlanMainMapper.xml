<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TestPlanMainMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TestPlanMainEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="test_plan_code" jdbcType="VARCHAR" property="testPlanCode"/>
        <result column="version_code" jdbcType="VARCHAR" property="versionCode"/>
        <result column="version_name" jdbcType="VARCHAR" property="versionName"/>
        <result column="developer_no" jdbcType="INTEGER" property="developerNo"/>
        <result column="tester_no" jdbcType="INTEGER" property="testerNo"/>
        <result column="product_director_id" jdbcType="BIGINT" property="productDirectorId"/>
        <result column="product_director_name" jdbcType="VARCHAR" property="productDirectorName"/>
        <result column="mobile_special_test" jdbcType="BIT" property="mobileSpecialTest"/>
        <result column="integrate_test" jdbcType="BIT" property="integrateTest"/>
        <result column="security_scan" jdbcType="BIT" property="securityScan"/>
        <result column="static_analysis" jdbcType="BIT" property="staticAnalysis"/>
        <result column="performance_test" jdbcType="BIT" property="performanceTest"/>
        <result column="performance_test_time" jdbcType="TIMESTAMP" property="performanceTestTime"/>
        <result column="performance_test_director_id" jdbcType="BIGINT" property="performanceTestDirectorId"/>
        <result column="performance_test_director_name" jdbcType="VARCHAR" property="performanceTestDirectorName"/>
        <result column="exploratory_test" jdbcType="BIT" property="exploratoryTest"/>
        <result column="exploratory_test_director_id" jdbcType="BIGINT" property="exploratoryTestDirectorId"/>
        <result column="exploratory_test_director_name" jdbcType="VARCHAR" property="exploratoryTestDirectorName"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="enable" jdbcType="BIT" property="enable"/>
        <result column="exploratory_test_time" jdbcType="TIMESTAMP" property="exploratoryTestTime"/>
        <result column="static_analysis_director_id" jdbcType="BIGINT" property="staticAnalysisDirectorId"/>
        <result column="static_analysis_director_name" jdbcType="VARCHAR" property="staticAnalysisDirectorName"/>
        <result column="static_analysis_time" jdbcType="TIMESTAMP" property="staticAnalysisTime"/>
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="publish_date" jdbcType="TIMESTAMP" property="publishDate"/>
        <result column="presentation_date" jdbcType="TIMESTAMP" property="presentationDate"/>
        <result column="approval_exit_date" jdbcType="TIMESTAMP" property="approvalExitDate"/>
        <result column="remarks" jdbcType="LONGVARCHAR" property="remarks"/>
        <result column="accurate_result" jdbcType="VARCHAR" property="accurateResult"/>
        <result column="safety_result" jdbcType="VARCHAR" property="safetyResult"/>
        <result column="safety_user_id" jdbcType="VARCHAR" property="safetyUserId"/>
        <result column="safety_user_name" jdbcType="VARCHAR" property="safetyUserName"/>
    </resultMap>
    <!-- !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! -->
    <!-- !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! -->
    <!-- test_plan_main 中 version_start_time 版本开始时间 plan_online_time 版本上线时间   test_plan_time  计划提测时间    test_plan_morning  是否是上午  plan_out_time  计划准出时间  plan_out_morning  计划准出时间是否是上午   已废弃 未删除   -->


    <sql id="Base_Column_List">
        id, code, test_plan_code, version_code, version_name, developer_no, tester_no, product_director_id,
    product_director_name, mobile_special_test,start_date,publish_date,presentation_date,approval_exit_date,
    integrate_test, security_scan, static_analysis, performance_test, performance_test_time,
    performance_test_director_id, performance_test_director_name, exploratory_test, exploratory_test_director_id,
    exploratory_test_director_name, creator_id, creator, gmt_create, modifier_id, modifier,
    gmt_modified, enable, exploratory_test_time, static_analysis_director_id, static_analysis_director_name,
    static_analysis_time,remarks,accurate_result,safety_result,safety_user_id,safety_user_name
    </sql>

    <select id="selectTestPlanMainByCode" resultType="com.zto.devops.qc.client.model.testPlan.query.TestPlanDto">
        select te.code,sa.test_plan_code,sa.version_code,sa.version_name,sa.developer_no,sa.tester_no,sa.product_director_id,
               sa.product_director_name,sa.mobile_special_test,sa.start_date,sa.publish_date,sa.presentation_date,sa.approval_exit_date,
               sa.integrate_test,sa.security_scan,sa.static_analysis,sa.performance_test,sa.performance_test_time,
               sa.performance_test_director_id,sa.performance_test_director_name,sa.exploratory_test,sa.exploratory_test_director_id,
               sa.exploratory_test_director_name,sa.creator_id,sa.creator,sa.gmt_create,sa.modifier_id,sa.modifier,
               sa.gmt_modified,sa.enable,sa.exploratory_test_time,sa.static_analysis_director_id,sa.static_analysis_director_name,
               sa.static_analysis_time,sa.remarks,
               te.plan_name,te.product_code,te.product_name,te.type,te.plan_code,te.test_director_id,
               te.test_director_name
                ,te.edit_no,te.status,te.dept_id,te.dept_name,
               te.product_source,
               te.preview,te.json,sa.accurate_result,sa.safety_result,sa.safety_user_id,sa.safety_user_name
        from qc_test_plan_main sa
                 left join qc_test_plan te on sa.test_plan_code = te.code
        where te.code = #{code}
          and sa.enable = 1
    </select>

    <select id="selectTestPlanMainByProductCode" resultType="com.zto.devops.qc.client.model.testPlan.query.TestPlanDto">
        select sa.code,sa.test_plan_code,sa.version_code,sa.version_name,sa.developer_no,sa.tester_no,sa.product_director_id,
               sa.product_director_name,sa.mobile_special_test,sa.start_date,sa.publish_date,sa.presentation_date,sa.approval_exit_date,
               sa.integrate_test,sa.security_scan,sa.static_analysis,sa.performance_test,sa.performance_test_time,
               sa.performance_test_director_id,sa.performance_test_director_name,sa.exploratory_test,sa.exploratory_test_director_id,
               sa.exploratory_test_director_name,sa.creator_id,sa.creator,sa.gmt_create,sa.modifier_id,sa.modifier,
               sa.gmt_modified,sa.enable,sa.exploratory_test_time,sa.static_analysis_director_id,sa.static_analysis_director_name,
               sa.static_analysis_time,sa.remarks,
               te.plan_name,te.product_code,te.product_name,te.type,te.plan_code,te.test_director_id,
               te.test_director_name,sa.start_date,sa.publish_date,sa.presentation_date,sa.approval_exit_date
                ,te.edit_no,te.status,te.dept_id,te.dept_name,
               te.product_source,
               te.preview,te.json,sa.accurate_result,sa.safety_result,sa.safety_user_id,sa.safety_user_name
        from qc_test_plan_main sa
                 left join qc_test_plan te on sa.test_plan_code = te.code
        where te.product_code = #{productCode}
          and sa.enable = 1
          and te.status = 'NORMAL'
    </select>

    <select id="selectTestPlanMainByVersionCode" resultType="com.zto.devops.qc.client.model.testPlan.query.TestPlanDto">
        select sa.code,sa.test_plan_code,sa.version_code,sa.version_name,sa.developer_no,sa.tester_no,sa.product_director_id,
               sa.product_director_name,sa.mobile_special_test,sa.start_date,sa.publish_date,sa.presentation_date,sa.approval_exit_date,
               sa.integrate_test,sa.security_scan,sa.static_analysis,sa.performance_test,sa.performance_test_time,
               sa.performance_test_director_id,sa.performance_test_director_name,sa.exploratory_test,sa.exploratory_test_director_id,
               sa.exploratory_test_director_name,sa.creator_id,sa.creator,sa.gmt_create,sa.modifier_id,sa.modifier,
               sa.gmt_modified,sa.enable,sa.exploratory_test_time,sa.static_analysis_director_id,sa.static_analysis_director_name,
               sa.static_analysis_time,sa.remarks,
               te.plan_name,te.product_code,te.product_name,te.type,te.plan_code,te.test_director_id,
               te.test_director_name
                ,te.edit_no,te.status,te.dept_id,te.dept_name,
               te.product_source,
               te.preview,te.json,sa.accurate_result,sa.safety_result,sa.safety_user_id,sa.safety_user_name
        from qc_test_plan_main sa
                 left join qc_test_plan te on sa.test_plan_code = te.code
        where sa.version_code = #{versionCode}
          and sa.enable = 1
          and te.status = 'NORMAL'
    </select>

    <select id="selectTestPlanByType" resultType="com.zto.devops.qc.client.model.testPlan.query.TestPlanDto">
        select sa.code,sa.test_plan_code,sa.version_code,sa.version_name,sa.developer_no,sa.tester_no,sa.product_director_id,
               sa.product_director_name,sa.mobile_special_test,sa.start_date,sa.publish_date,sa.presentation_date,sa.approval_exit_date,
               sa.integrate_test,sa.security_scan,sa.static_analysis,sa.performance_test,sa.performance_test_time,
               sa.performance_test_director_id,sa.performance_test_director_name,sa.exploratory_test,sa.exploratory_test_director_id,
               sa.exploratory_test_director_name,sa.creator_id,sa.creator,sa.gmt_create,sa.modifier_id,sa.modifier,
               sa.gmt_modified,sa.enable,sa.exploratory_test_time,sa.static_analysis_director_id,sa.static_analysis_director_name,
               sa.static_analysis_time,sa.remarks,
               te.plan_name,te.product_code,te.product_name,te.type,te.plan_code,te.test_director_id,
               te.test_director_name
                ,te.edit_no,te.status,te.dept_id,te.dept_name,
               te.product_source,
               te.preview,te.json,sa.accurate_result,sa.safety_result,sa.safety_user_id,sa.safety_user_name
        from qc_test_plan_main sa
                 left join qc_test_plan te on sa.test_plan_code = te.code
        where sa.enable = 1
          and te.type = #{type}
          and sa.code = #{testPlanCode}
          and te.status = 'NORMAL'
    </select>

    <select id="selectMobileTestPlanByType" resultType="com.zto.devops.qc.client.model.testPlan.query.TestPlanDto">
        select sa.code,sa.test_plan_code,sa.version_code,sa.version_name,sa.developer_no,sa.tester_no,sa.product_director_id,
        sa.product_director_name,sa.mobile_special_test,sa.start_date,sa.publish_date,sa.presentation_date,sa.approval_exit_date,
        sa.integrate_test,sa.security_scan,sa.static_analysis,sa.performance_test,sa.performance_test_time,
        sa.performance_test_director_id,sa.performance_test_director_name,sa.exploratory_test,sa.exploratory_test_director_id,
        sa.exploratory_test_director_name,sa.creator_id,sa.creator,sa.gmt_create,sa.modifier_id,sa.modifier,
        sa.gmt_modified,sa.enable,sa.exploratory_test_time,sa.static_analysis_director_id,sa.static_analysis_director_name,
        sa.static_analysis_time,sa.remarks,
        te.plan_name,te.product_code,te.product_name,te.type,te.plan_code,te.test_director_id,
        te.test_director_name
        ,te.edit_no,te.status,te.dept_id,te.dept_name,
        te.product_source,
        te.preview,te.json,sa.accurate_result,sa.safety_result,sa.safety_user_id,sa.safety_user_name
        from qc_test_plan_main sa left join qc_test_plan te on sa.test_plan_code = te.code
        where sa.enable = 1 and sa.mobile_special_test = 1 and te.status ='NORMAL'  and sa.test_plan_code not in
        (select t.plan_code from qc_test_plan t where t.enable = 1 and t.status ='NORMAL' and t.type = #{type} and t.plan_code is not null)
        <if test="planName!=null and planName!=''">
            and (te.plan_name like CONCAT('%',#{planName},'%') or te.code = #{planName})
        </if>
        order by sa.id desc
    </select>

    <select id="selectIntegrateTestPlanByType" resultType="com.zto.devops.qc.client.model.testPlan.query.TestPlanDto">
        select sa.code,sa.test_plan_code,sa.version_code,sa.version_name,sa.developer_no,sa.tester_no,sa.product_director_id,
        sa.product_director_name,sa.mobile_special_test,sa.start_date,sa.publish_date,sa.presentation_date,sa.approval_exit_date,
        sa.integrate_test,sa.security_scan,sa.static_analysis,sa.performance_test,sa.performance_test_time,
        sa.performance_test_director_id,sa.performance_test_director_name,sa.exploratory_test,sa.exploratory_test_director_id,
        sa.exploratory_test_director_name,sa.creator_id,sa.creator,sa.gmt_create,sa.modifier_id,sa.modifier,
        sa.gmt_modified,sa.enable,sa.exploratory_test_time,sa.static_analysis_director_id,sa.static_analysis_director_name,
        sa.static_analysis_time,sa.remarks,
        te.plan_name,te.product_code,te.product_name,te.type,te.plan_code,te.test_director_id,
        te.test_director_name
        ,te.edit_no,te.status,te.dept_id,te.dept_name,
        te.product_source,
        te.preview,te.json ,sa.accurate_result,sa.safety_result ,sa.safety_user_id,sa.safety_user_name
        from qc_test_plan_main sa left join qc_test_plan te on sa.test_plan_code = te.code
        where sa.enable = 1 and sa.integrate_test = 1 and te.status ='NORMAL'   and sa.test_plan_code not in
        (select t.plan_code from qc_test_plan t where t.enable = 1 and t.status ='NORMAL' and t.type = #{type} and t.plan_code is not null)
        <if test="planName!=null and planName!=''">
            and (te.plan_name like CONCAT('%',#{planName},'%') or te.code = #{planName})
        </if>
        order by sa.id desc
    </select>

    <select id="selectSafetyTestPlanByType" resultType="com.zto.devops.qc.client.model.testPlan.query.TestPlanDto">
        select sa.code,sa.test_plan_code,sa.version_code,sa.version_name,sa.developer_no,sa.tester_no,sa.product_director_id,
        sa.product_director_name,sa.mobile_special_test,sa.start_date,sa.publish_date,sa.presentation_date,sa.approval_exit_date,
        sa.integrate_test,sa.security_scan,sa.static_analysis,sa.performance_test,sa.performance_test_time,
        sa.performance_test_director_id,sa.performance_test_director_name,sa.exploratory_test,sa.exploratory_test_director_id,
        sa.exploratory_test_director_name,sa.creator_id,sa.creator,sa.gmt_create,sa.modifier_id,sa.modifier,
        sa.gmt_modified,sa.enable,sa.exploratory_test_time,sa.static_analysis_director_id,sa.static_analysis_director_name,
        sa.static_analysis_time,sa.remarks,
        te.plan_name,te.product_code,te.product_name,te.type,te.plan_code,te.test_director_id,
        te.test_director_name
        ,te.edit_no,te.status,te.dept_id,te.dept_name,
        te.product_source,
        te.preview,te.json ,sa.accurate_result,sa.safety_result ,sa.safety_user_id,sa.safety_user_name
        from qc_test_plan_main sa left join qc_test_plan te on sa.test_plan_code = te.code
        where sa.enable = 1 and sa.security_scan = 1 and te.status ='NORMAL'
        <if test="list!=null and list.size>0 ">
            and te.product_code in
            <foreach collection="list" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        and sa.test_plan_code not in
        (select t.plan_code from qc_test_plan t where t.enable = 1 and t.status ='NORMAL' and t.type = #{type} and t.plan_code is not null)
        <if test="planName!=null and planName!=''">
            and (te.plan_name like CONCAT('%',#{planName},'%') or te.code = #{planName})
        </if>
        order by sa.id desc
    </select>

    <select id="testAccessPlanQuery" resultType="com.zto.devops.qc.client.model.testPlan.query.TestPlanDto">
        select sa.code,sa.test_plan_code,sa.version_code,sa.version_name,sa.developer_no,sa.tester_no,sa.product_director_id,
        sa.product_director_name,sa.mobile_special_test,sa.start_date,sa.publish_date,sa.presentation_date,sa.approval_exit_date,
        sa.integrate_test,sa.security_scan,sa.static_analysis,sa.performance_test,sa.performance_test_time,
        sa.performance_test_director_id,sa.performance_test_director_name,sa.exploratory_test,sa.exploratory_test_director_id,
        sa.exploratory_test_director_name,sa.creator_id,sa.creator,sa.gmt_create,sa.modifier_id,sa.modifier,
        sa.gmt_modified,sa.enable,sa.exploratory_test_time,sa.static_analysis_director_id,sa.static_analysis_director_name,
        sa.static_analysis_time,sa.remarks,
        te.plan_name,te.product_code,te.product_name,te.type,te.plan_code,te.test_director_id,
        te.test_director_name,te.edit_no,te.status,te.dept_id,te.dept_name,
        te.product_source,
        te.preview,te.json,sa.accurate_result,sa.safety_result ,sa.safety_user_id,sa.safety_user_name
        from qc_test_plan_main sa left join qc_test_plan te on sa.test_plan_code = te.code
        where sa.enable = 1 and te.status ='NORMAL'
        <if test="list!=null and list.size>0 ">
            and te.product_code in
            <foreach collection="list" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        and te.code not in
        (select p.code from qc_test_report t left join qc_test_plan p on t.plan_code = p.code where p.code is not null
        and t.report_type ='TEST_ACCESS' and t.status ='NORMAL'
        )
        <if test="planName!=null and planName!=''">
            and (te.plan_name like CONCAT('%',#{planName},'%') or te.code = #{planName})
        </if>
    </select>


    <select id="testPermitWithoutAccessPlanQuery" resultType="com.zto.devops.qc.client.model.testPlan.query.TestPlanDto">
        select sa.code,sa.test_plan_code,sa.version_code,sa.version_name,sa.developer_no,sa.tester_no,sa.product_director_id,
        sa.product_director_name,sa.mobile_special_test,sa.start_date,sa.publish_date,sa.presentation_date,sa.approval_exit_date,
        sa.integrate_test,sa.security_scan,sa.static_analysis,sa.performance_test,sa.performance_test_time,
        sa.performance_test_director_id,sa.performance_test_director_name,sa.exploratory_test,sa.exploratory_test_director_id,
        sa.exploratory_test_director_name,sa.creator_id,sa.creator,sa.gmt_create,sa.modifier_id,sa.modifier,
        sa.gmt_modified,sa.enable,sa.exploratory_test_time,sa.static_analysis_director_id,sa.static_analysis_director_name,
        sa.static_analysis_time,sa.remarks,
        te.plan_name,te.product_code,te.product_name,te.type,te.plan_code,te.test_director_id,
        te.test_director_name
        ,te.edit_no,te.status,te.dept_id,te.dept_name,
        te.product_source,
        te.preview,te.json,sa.accurate_result,sa.safety_result ,sa.safety_user_id,sa.safety_user_name
        from qc_test_plan_main sa left join qc_test_plan te on sa.test_plan_code = te.code
        where sa.enable = 1 and te.status ='NORMAL'
        <if test="list!=null and list.size>0 ">
            and te.product_code in
            <foreach collection="list" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        and te.code not in
        (select p.code from (select * from qc_test_report s where s.report_type = 'TEST_PERMIT' and s.status = 'NORMAL') t left join
        qc_test_plan p on t.plan_code = p.code where p.code is not null and t.report_type ='TEST_PERMIT'
        )
        <if test="planName!=null and planName!='' ">
            and (te.plan_name like CONCAT('%',#{planName},'%') or te.code = #{planName})
        </if>

    </select>

    <select id="testPermitWithoutOnlineSmokePlanQuery" resultType="com.zto.devops.qc.client.model.testPlan.query.TestPlanDto">
        select sa.code,sa.test_plan_code,sa.version_code,sa.version_name,sa.developer_no,sa.tester_no,sa.product_director_id,
        sa.product_director_name,sa.mobile_special_test,sa.start_date,sa.publish_date,sa.presentation_date,sa.approval_exit_date,
        sa.integrate_test,sa.security_scan,sa.static_analysis,sa.performance_test,sa.performance_test_time,
        sa.performance_test_director_id,sa.performance_test_director_name,sa.exploratory_test,sa.exploratory_test_director_id,
        sa.exploratory_test_director_name,sa.creator_id,sa.creator,sa.gmt_create,sa.modifier_id,sa.modifier,
        sa.gmt_modified,sa.enable,sa.exploratory_test_time,sa.static_analysis_director_id,sa.static_analysis_director_name,
        sa.static_analysis_time,sa.remarks,
        te.plan_name,te.product_code,te.product_name,te.type,te.plan_code,te.test_director_id,
        te.test_director_name
        ,te.edit_no,te.status,te.dept_id,te.dept_name,
        te.product_source,
        te.preview,te.json,sa.accurate_result,sa.safety_result ,sa.safety_user_id,sa.safety_user_name
        from qc_test_plan_main sa left join qc_test_plan te on sa.test_plan_code = te.code
        where sa.enable = 1 and te.status ='NORMAL'
        <if test="list!=null and list.size>0 ">
            and te.product_code in
            <foreach collection="list" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        and te.code not in
        (select p.code from (select * from qc_test_report s where s.report_type = 'TEST_PERMIT' ) t left join
        qc_test_plan p on t.plan_code = p.code where p.code is not null and t.report_type ='ONLINE_SMOKE'
        )
        <if test="planName!=null and planName!=''">
            and (te.plan_name like CONCAT('%',#{planName},'%') or te.code = #{planName})
        </if>

    </select>


    <select id="testMobilePlanList" resultType="com.zto.devops.qc.client.model.testPlan.query.TestPlanDto">
        select p.code,p.plan_name,p.product_code,p.product_name,p.type,p.plan_code,p.test_director_id,
        p.test_director_name,p.creator_id,p.creator,p.gmt_create,
        p.modifier_id,p.modifier,p.gmt_modified,p.enable,p.edit_no,p.status,
        p.version_code,p.version_name,p.product_source,
        p.preview,p.json ,s.dept_name as deptName ,s.dept_id as deptId,
        s.product_director_name as productDirectorName,s.product_director_id as productDirectorId
        ,p.code as testPlanCode
        from qc_test_plan p
        left join qc_test_report t on p.code = t.plan_code and t.status='NORMAL'
        left join qc_test_plan s on p.plan_code=s.code and s.type = 'TEST_PLAN'
        where t.code is null and p.test_director_id =#{testDirectorId} and p.type = 'MOBILE_SPECIAL'
        <if test="planName!=null and planName!=''">
            and (p.plan_name like CONCAT('%',#{planName},'%') or p.code = #{planName})
        </if>
    </select>


    <select id="testIntegrationPlanList" resultType="com.zto.devops.qc.client.model.testPlan.query.TestPlanDto">
        select p.code,p.plan_name,p.product_code,p.product_name,p.type,p.plan_code,p.test_director_id,
        p.test_director_name,p.creator_id,p.creator,p.gmt_create,
        p.modifier_id,p.modifier,p.gmt_modified,p.enable,p.edit_no,p.status,
        p.version_code,p.version_name,p.product_source,
        p.preview,p.json  ,s.dept_name as deptName ,s.dept_id as deptId,s.product_director_name as productDirectorName,
        s.product_director_id as productDirectorId ,p.code as testPlanCode
        from qc_test_plan p
        left join qc_test_report t on p.code = t.plan_code and t.status='NORMAL'
        left join qc_test_plan s on p.plan_code=s.code and s.type = 'TEST_PLAN'
        where t.code is null and p.test_director_id =#{testDirectorId} and p.type = 'INTEGRATION_TEST'
        <if test="planName!=null and planName!=''" >
            and (p.plan_name like CONCAT('%',#{planName},'%') or p.code = #{planName})
        </if>
    </select>


    <select id="testCaseReviewQuery" resultType="com.zto.devops.qc.client.model.testPlan.query.TestPlanDto">
        select sa.code,sa.test_plan_code,sa.version_code,sa.version_name,sa.developer_no,sa.tester_no,sa.product_director_id,
        sa.product_director_name,sa.mobile_special_test,sa.start_date,sa.publish_date,sa.presentation_date,sa.approval_exit_date,
        sa.integrate_test,sa.security_scan,sa.static_analysis,sa.performance_test,sa.performance_test_time,
        sa.performance_test_director_id,sa.performance_test_director_name,sa.exploratory_test,sa.exploratory_test_director_id,
        sa.exploratory_test_director_name,sa.creator_id,sa.creator,sa.gmt_create,sa.modifier_id,sa.modifier,
        sa.gmt_modified,sa.enable,sa.exploratory_test_time,sa.static_analysis_director_id,sa.static_analysis_director_name,
        sa.static_analysis_time,sa.remarks,
        te.plan_name,te.product_code,te.product_name,te.type,te.plan_code,te.test_director_id,
        te.test_director_name
        ,te.edit_no,te.status,te.dept_id,te.dept_name,
        te.product_source,
        te.preview,te.json ,sa.accurate_result,sa.safety_result ,sa.safety_user_id,sa.safety_user_name
        from qc_test_plan_main sa left join qc_test_plan te on sa.test_plan_code = te.code
        where sa.enable = 1 and te.status ='NORMAL'
        <if test="list!=null and list.size>0 ">
            and te.product_code in
            <foreach collection="list" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="planName!=null">
            and (te.plan_name like CONCAT('%',#{planName},'%') or te.code = #{planName})
        </if>
        and te.code not in
        (select s.plan_code from qc_test_report s where s.report_type = 'CASE_REVIEW' and s.enable=1
        )
    </select>

    <select id="selectByCode" resultMap="BaseResultMap">
        select t.code,t.test_plan_code,t.version_code,t.version_name,t.developer_no,t.tester_no,t.product_director_id,
               t.product_director_name,t.mobile_special_test,t.start_date,t.publish_date,t.presentation_date,t.approval_exit_date,
               t.integrate_test,t.security_scan,t.static_analysis,t.performance_test,t.performance_test_time,
               t.performance_test_director_id,t.performance_test_director_name,t.exploratory_test,t.exploratory_test_director_id,
               t.exploratory_test_director_name,t.creator_id,t.creator,t.gmt_create,t.modifier_id,t.modifier,
               t.gmt_modified,t.enable,t.exploratory_test_time,t.static_analysis_director_id,t.static_analysis_director_name,
               t.static_analysis_time,t.test_plan_time,t.test_plan_morning,t.plan_out_time,t.plan_out_morning,t.remarks
                ,t.accurate_result,t.safety_result,t.safety_user_id,t.safety_user_name
        from qc_test_plan_main t
        where t.test_plan_code = #{planCode}
    </select>

</mapper>