<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.KnowledgeBaseMapper">

    <insert id="insertBatch" parameterType="list" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO qc_knowledge_base (
        space_id,
        product_code,
        url,
        creator_id,
        creator,
        gmt_create,
        modifier_id,
        modifier,
        gmt_modified)
        VALUES
        <foreach collection="list" separator="," item="item" index="index">
            ( #{item.spaceId},
            #{item.productCode},
            #{item.url},
            #{item.creatorId},
            #{item.creator},
            #{item.gmtCreate},
            #{item.modifierId},
            #{item.modifier},
            #{item.gmtModified})
        </foreach>
    </insert>
    <delete id="deleteAll">
        delete  from qc_knowledge_base where  1 = 1
    </delete>
</mapper>


