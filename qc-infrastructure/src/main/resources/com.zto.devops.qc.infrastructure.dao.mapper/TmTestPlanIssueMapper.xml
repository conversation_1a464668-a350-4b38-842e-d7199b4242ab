<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TmTestPlanIssueMapper">

    <select id="selectPlanIssue" resultType="com.zto.devops.qc.client.model.testmanager.plan.entity.TmPlanCaseIssueVO">
        select qi.code, qi.title, qi.status, qi.handle_user_id as handleUserId,
               qi.handle_user_name as handleUserName, qi.priority,
               qt.code as caseCode, qt.name as caseName,
               qi.develop_user_id as developUserId,
               qi.develop_user_name as developUserName,
               qi.test_user_id as testUserId,
               qi.test_user_name as testUserName,
               qi.product_code as productCode,
               qi.find_version_code as findVersionCode,
               qi.find_version_name as findVersionName,
               qi.fix_version_code as fixVersionCode,
               qi.fix_version_name as fixVersionName,
               qi.gmt_create as gmtCreate
        from qc_issue qi
        left join tm_testcase_relation qr on qr.business_code = qi.code
        left join tm_testcase qt on qt.code = qr.testcase_code
        where qi.enable = true
        <if test="versionCode!=null and versionCode!=''">
            and (qi.find_version_code=#{versionCode} or qi.fix_version_code=#{versionCode})
        </if>
        <if test="codeOrTitle!=null and codeOrTitle!=''">
            and (qi.title like "%"#{codeOrTitle}"%" or qi.code like "%"#{codeOrTitle}"%")
        </if>
        <if test="statusList!=null  and  statusList.size > 0">
            and qi.status in
            <foreach collection="statusList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="priorityList!=null and priorityList.size > 0">
            and qi.priority in
            <foreach collection="priorityList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="handleUserIdList!=null and handleUserIdList.size > 0">
            and qi.handle_user_id in
            <foreach collection="handleUserIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="developUserIdList!=null and developUserIdList.size > 0">
            and qi.develop_user_id in
            <foreach collection="developUserIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="testUserIdList!=null and testUserIdList.size > 0">
            and qi.test_user_id in
            <foreach collection="testUserIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="relatedList != null and relatedList.size > 0">
            <trim prefix="and (" suffix=")" prefixOverrides="OR">
                <if test="relatedList.contains(@com.zto.devops.qc.client.enums.issue.RelatedToMeEnum@CURRENT_HANDLE_USER)">
                    OR qi.code in (select business_code as code from qc_relevant_user where enable=true and
                    user_id=#{currentUserId} and type="CURRENT_HANDLE_USER")
                </if>
                <if test="relatedList.contains(@com.zto.devops.qc.client.enums.issue.RelatedToMeEnum@CREATOR)">
                    OR qi.code in (select business_code as code from qc_relevant_user where enable=true and
                    user_id=#{currentUserId} and type="CREATOR")
                </if>
                <if test="relatedList.contains(@com.zto.devops.qc.client.enums.issue.RelatedToMeEnum@HANDLED_USER) ">
                    OR qi.code in (select business_code as code from qc_relevant_user where enable=true and
                    user_id=#{currentUserId} and type="HANDLED_USER")
                </if>
                <if test="relatedList.contains(@com.zto.devops.qc.client.enums.issue.RelatedToMeEnum@CC) ">
                    OR qi.code in (select business_code as code from qc_relevant_user where enable=true and
                    user_id=#{currentUserId} and type="CC")
                </if>
            </trim>
        </if>
        order by qi.id desc
    </select>
</mapper>
