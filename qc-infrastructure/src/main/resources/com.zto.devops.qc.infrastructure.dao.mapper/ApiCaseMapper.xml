<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.ApiCaseMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.ApiCaseEntity">
        <id column="case_code" jdbcType="VARCHAR" property="caseCode"/>
        <result column="case_name" jdbcType="VARCHAR" property="caseName"/>
        <result column="api_code" jdbcType="VARCHAR" property="apiCode"/>
        <result column="node_code" jdbcType="VARCHAR" property="nodeCode"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.ApiCaseSourceTypeHandler"/>
        <result column="parent_code" jdbcType="VARCHAR" property="parentCode"/>
        <result column="priority" jdbcType="VARCHAR" property="priority"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcasePriorityTypeHandler"/>
        <result column="req_data" jdbcType="VARCHAR" property="reqData"/>
        <result column="asserts" jdbcType="VARCHAR" property="asserts"/>
        <result column="user_variable_code" jdbcType="VARCHAR" property="userVariableCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="enable" jdbcType="INTEGER" property="enable"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.ApiCaseEnableTypeHandler"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>

    <select id="selectApiCaseList" resultType="com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiCaseVO">
        select
        any_value(tac.case_code) as caseCode,
        any_value(tac.case_name) as caseName,
        any_value(tat.api_code) as apiCode,
        any_value(tat.api_name) as apiName,
        any_value(tat.api_address) as apiAddress,
        any_value(tac.source_type) as sourceTypeNum,
        any_value(tac.enable) as enableNum,
        any_value(tac.latest_task_id) as taskCode,
        any_value(task.start_time) as executeTime,
        TIMESTAMPDIFF(second,task.start_time,task.finish_time) as duration,
        count(distinct tter.testcase_code) as executeCaseCount,
        any_value(tac.latest_execute_result) as testResultStr,
        any_value(tat.doc_version) as docVersion
        from
        tm_api_case tac
        left join tm_automatic_task task on tac.latest_task_id = task.code
        left join tm_api_test tat on tac.api_code = tat.api_code
        left join tm_testcase_execute_record tter on tter.automatic_task_code = tac.latest_task_id
        where 1=1
        and tac.product_code = #{productCode}
        and tat.api_type = 'HTTP'
        <if test="apiCode != null and apiCode != ''">
            and tac.api_code = #{apiCode}
        </if>
        <if test="caseName != null and caseName !='' ">
            and tac.case_name like CONCAT('%',#{caseName},'%') ESCAPE '/'
        </if>
        <if test="startTime != null and endTime != null  ">
            and task.start_time between #{startTime} and #{endTime}
        </if>
        <if test="apiNameList != null and apiNameList.size() > 0">
            and tat.api_name in
            <foreach collection="apiNameList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="typeNumList != null and typeNumList.size() > 0">
            and tac.source_type in
            <foreach collection="typeNumList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="enableNumList != null and enableNumList.size() > 0">
            and tac.enable in
            <foreach collection="enableNumList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="apiAddressList != null and apiAddressList.size() > 0">
            and tat.api_address in
            <foreach collection="apiAddressList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="testResultList != null and testResultList.size() > 0">
            and tac.latest_execute_result in
            <foreach collection="testResultList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="docVersionList != null and docVersionList.size() > 0">
            and tat.doc_version in
            <foreach collection="docVersionList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by tac.case_code
        order by any_value(tac.gmt_modified) desc
    </select>

    <select id="selectApiCaseChildren"
            resultType="com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiCaseChildVO">
        select
        tac.case_code as caseCode,
        tac.case_name as caseName,
        tac.parent_code as parentCode ,
        tat.api_code as apiCode,
        tat.api_name as apiName,
        tat.api_address as apiAddress,
        tac.source_type as sourceTypeNum,
        tter.automatic_task_code as taskCode,
        tter.`result` as testResult,
        time_to_sec(tter.finish_time) - time_to_sec(tter.start_time) as duration,
        tter.start_time as executeTime,
        tac.enable as enableNum,
        tac.gmt_modified as gmtModified
        from
        tm_api_case tac
        left join tm_api_test tat on tac.api_code = tat.api_code
        left join (
            select * from tm_testcase_execute_record temp
            where  temp.id in (
                select max(temp1.id) from tm_testcase_execute_record temp1
                left join tm_api_case tac on temp1.testcase_code = tac.case_code
                where tac.parent_code = #{caseCode} group by temp1.testcase_code
            )
        ) tter on tac.case_code = tter.testcase_code
        where
        1=1
        and tac.parent_code = #{caseCode}
        and tac.product_code = #{productCode}
        <if test="caseName != null and caseName !='' ">
            and tac.case_name like CONCAT('%',#{caseName},'%') ESCAPE '/'
        </if>
        <if test="testResultList != null and testResultList.size() > 0">
            and tter.`result` in
            <foreach collection="testResultList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by tac.gmt_modified desc
    </select>

    <select id="selectExecuteResultByParentCodeAndTaskCode" resultType="java.lang.String">
        select group_concat(distinct tter.`result`)
        from tm_testcase_execute_record tter
        left join tm_api_test_case tac on tac.case_code = tter.testcase_code
        where tac.parent_case_code = #{parentCode}
          and tter.automatic_task_code = #{taskCode}
    </select>

    <select id="statApiCaseExecuteResult"
            resultType="com.zto.devops.qc.client.model.testmanager.apitest.entity.StatApiCaseExecuteResultVO">
        select group_concat(distinct tter.`result`) as executeResultMerge,
               tac.parent_code                      as parentCode
        from tm_testcase_execute_record tter
                 left join tm_api_case tac on tter.testcase_code = tac.case_code
        where 1 = 1
          and tter.automatic_task_code = #{taskCode}
          and tac.source_type = 2
        group by tac.parent_code
    </select>

    <delete id="deleteByProductCode">
        delete from tm_api_case where product_code = #{productCode} limit 1000
    </delete>

    <select id="countApiTestCaseForExecute" resultType="java.lang.Integer">
        select count(distinct tatc.case_code)
        from tm_api_test tat
        left join tm_api_test_case tatc on tat.api_code = tatc.api_code
        where 1=1
        and tat.enable = 1
        and tat.product_code = #{productCode}
        and tatc.enable = 1
        and tatc.case_type = 1
        and tatc.status = 'publish'
        and tatc.product_code = #{productCode}
        <if test="list != null and list.size() > 0">
            and tatc.api_code in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
