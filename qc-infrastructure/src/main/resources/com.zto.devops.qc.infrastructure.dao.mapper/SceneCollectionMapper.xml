<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.SceneCollectionMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.SceneCollectionEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="scene_index_code" jdbcType="VARCHAR" property="sceneIndexCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="favorite_type" jdbcType="VARCHAR" property="favoriteType"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>

    <insert id="batchInsert">
        insert into tm_scene_collection (
        scene_index_code, product_code, favorite_type,
         creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified
        ) values
        <foreach collection="list" item="item" separator=",">
            (#{item.sceneIndexCode},#{item.productCode}, #{item.favoriteType},#{item.creatorId}, #{item.creator}, #{item.gmtCreate}, #{item.modifierId},
            #{item.modifier},#{item.gmtModified})
        </foreach>
    </insert>
</mapper>