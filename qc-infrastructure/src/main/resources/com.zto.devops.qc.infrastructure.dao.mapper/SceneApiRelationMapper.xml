<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.SceneApiRelationMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.SceneApiRelationEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="scene_code" jdbcType="VARCHAR" property="sceneCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="api_type" jdbcType="VARCHAR" property="apiType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.ApiTypeHandler"/>
        <result column="api_address" jdbcType="VARCHAR" property="apiAddress"/>
        <result column="enable" jdbcType="BIT" property="enable"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="api_test_index" jdbcType="INTEGER" property="apiTestIndex"/>
    </resultMap>

    <resultMap id="PageApiLiteRespMap"
               type="com.zto.devops.qc.client.service.testmanager.apitest.model.PageApiLiteResp">
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="api_type" jdbcType="VARCHAR" property="apiType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.ApiTypeHandler"/>
        <result column="api_address" jdbcType="VARCHAR" property="apiAddress"/>
        <result column="api_name" jdbcType="VARCHAR" property="apiName"/>
    </resultMap>

</mapper>