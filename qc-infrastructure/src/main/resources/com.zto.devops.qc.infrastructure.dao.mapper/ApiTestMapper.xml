<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.ApiTestMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.ApiTestEntity">
        <id column="api_code" jdbcType="VARCHAR" property="apiCode"/>
        <result column="api_name" jdbcType="VARCHAR" property="apiName"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="api_type" jdbcType="VARCHAR" property="apiType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.ApiTypeHandler"/>
        <result column="req_method" jdbcType="VARCHAR" property="reqMethod"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.RequestMethodTypeHandler"/>
        <result column="api_address" jdbcType="VARCHAR" property="apiAddress"/>
        <result column="api_desc" jdbcType="VARCHAR" property="apiDesc"/>
        <result column="doc_id" jdbcType="BIGINT" property="docId"/>
        <result column="doc_version" jdbcType="VARCHAR" property="docVersion"/>
        <result column="api_data" jdbcType="VARCHAR" property="apiData"/>
        <result column="enable" jdbcType="INTEGER" property="enable"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.ApiTestEnableTypeHandler"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="doc_product_code" jdbcType="VARCHAR" property="docProductCode"/>
        <result column="api_test_index" jdbcType="INTEGER" property="apiTestIndex"/>
    </resultMap>

    <select id="selectDocVersionList"
            resultType="com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiDocVersionVO">
        select distinct tat.doc_version as docVersion
        from tm_api_test tat
        where tat.enable in (1, 2, 3) and tat.product_code = #{productCode}
        <if test="docVersion != null and docVersion !='' ">
            and tat.doc_version like CONCAT('%',#{docVersion},'%')
        </if>
    </select>


    <select id="selectApiLiteGroup"
            resultType="com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiLiteInfoVO">
        SELECT
        ANY_VALUE(tat.api_code) AS apiCode,
        ANY_VALUE(tat.api_name) AS apiName,
        ANY_VALUE(tat.api_address) AS apiAddress
        FROM
        tm_api_test tat
        WHERE
        1=1
        <if test="statusList == null or statusList.isEmpty()">
            AND tat.enable IN (1, 2, 3)
        </if>
        <if test="statusList != null and !statusList.isEmpty()">
            AND tat.enable IN
            <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="apiCode != null and apiCode != ''">
            AND tat.api_code = #{apiCode}
        </if>
        AND tat.product_code = #{productCode}
        <choose>
            <when test="searchName == null">
                <if test="nameOrAddress != null and nameOrAddress != ''">
                    AND (tat.api_address LIKE CONCAT('%', #{nameOrAddress}, '%') OR tat.api_name LIKE CONCAT('%',
                    #{nameOrAddress}, '%'))
                </if>
                GROUP BY tat.api_code
            </when>
            <when test="searchName == true">
                <if test="nameOrAddress != null and nameOrAddress != ''">
                    AND tat.api_name LIKE CONCAT('%', #{nameOrAddress}, '%')
                </if>
                GROUP BY tat.api_name
            </when>
            <otherwise>
                <if test="nameOrAddress != null and nameOrAddress != ''">
                    AND tat.api_address LIKE CONCAT('%', #{nameOrAddress}, '%')
                </if>
                GROUP BY tat.api_address
            </otherwise>
        </choose>
    </select>


    <select id="selectApiList" resultMap="BaseResultMap">
        select
        *
        from
        tm_api_test tat
        where
        1=1
        and tat.enable in (1, 2, 3)
        and tat.product_code = #{productCode}
        and tat.api_type = 'HTTP'
        <if test="appIdList != null and appIdList.size() > 0">
            and tat.app_id in
            <foreach collection="appIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="reqMethodList != null and reqMethodList.size() > 0">
            and tat.req_method in
            <foreach collection="reqMethodList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="enableNumList != null and enableNumList.size() > 0">
            and tat.enable in
            <foreach collection="enableNumList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="docVersionList != null and docVersionList.size() > 0">
            and tat.doc_version in
            <foreach collection="docVersionList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="operatorNameList != null and operatorNameList.size() > 0">
            and tat.modifier in
            <foreach collection="operatorNameList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTime !=null and endTime != null">
            and tat.gmt_modified between #{startTime} and #{endTime}
        </if>
        <if test="apiDesc != null and apiDesc !='' ">
            and tat.api_desc like CONCAT('%',#{apiDesc},'%') ESCAPE '/'
        </if>
        <if test="nameOrAddress != null and nameOrAddress !='' ">
            and (tat.api_name like CONCAT('%',#{nameOrAddress},'%') ESCAPE '/'
            or tat.api_address like CONCAT('%',#{nameOrAddress},'%') ESCAPE '/')
        </if>
        order by tat.gmt_modified desc
    </select>

    <resultMap id="ApiVOMap" type="com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiVO">
        <result column="api_code" jdbcType="VARCHAR" property="apiCode"/>
        <result column="api_name" jdbcType="VARCHAR" property="apiName"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="api_type" jdbcType="VARCHAR" property="apiType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.ApiTypeHandler"/>
        <result column="req_method" jdbcType="VARCHAR" property="reqMethod"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.RequestMethodTypeHandler"/>
        <result column="api_address" jdbcType="VARCHAR" property="apiAddress"/>
        <result column="api_desc" jdbcType="VARCHAR" property="apiDesc"/>
        <result column="doc_id" jdbcType="BIGINT" property="docId"/>
        <result column="enable" jdbcType="INTEGER" property="enable"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.ApiTestEnableTypeHandler"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="doc_product_code" jdbcType="VARCHAR" property="docProductCode"/>
        <result column="doc_version" jdbcType="VARCHAR" property="docVersion"/>
        <result column="scene_related" jdbcType="BIT" property="sceneRelated"/>
        <result column="tag_name" jdbcType="VARCHAR" property="tagName"/>
    </resultMap>

    <select id="selectUniqueApiList" resultMap="ApiVOMap">
        select
        t.api_code,
        t.api_name,
        t.app_id,
        t.api_type,
        t.req_method,
        t.api_address,
        t.api_desc,
        t.doc_id,
        t.enable,
        t.modifier_id,
        t.modifier,
        t.gmt_modified,
        t.doc_product_code,
        (select group_concat(distinct doc_version) from tm_api_test where enable in (1,2,3) and api_test_index =
        t.api_test_index) as doc_version,
        (select exists (select 1 from tm_scene_api_relation where api_test_index = t.api_test_index)) as scene_related,
        (select group_concat(distinct tag_name) from qc_tag where business_code = t.api_code) as tag_name
        from tm_api_test t
        left join qc_tag qt on qt.business_code = t.api_code
        where not exists (select 1 from tm_api_test where t.api_test_index = api_test_index and t.gmt_modified
        <![CDATA[ < ]]> gmt_modified and enable in (1,2,3))
        and t.product_code = #{productCode}
        and t.enable in (1, 2, 3)
        <if test="appIdList != null and appIdList.size() > 0">
            and t.app_id in
            <foreach collection="appIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="apiTypeList != null and apiTypeList.size() > 0">
            and t.api_type in
            <foreach collection="apiTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="reqMethodList != null and reqMethodList.size() > 0">
            and t.req_method in
            <foreach collection="reqMethodList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="enableNumList != null and enableNumList.size() > 0">
            and t.enable in
            <foreach collection="enableNumList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="docVersionList != null and docVersionList.size() > 0">
            and t.doc_version in
            <foreach collection="docVersionList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="operatorNameList != null and operatorNameList.size() > 0">
            and t.modifier in
            <foreach collection="operatorNameList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTime !=null and endTime != null">
            and t.gmt_modified between #{startTime} and #{endTime}
        </if>
        <if test="apiDesc != null and apiDesc !='' ">
            and t.api_desc like CONCAT('%',#{apiDesc},'%') ESCAPE '/'
        </if>
        <if test="nameOrAddress != null and nameOrAddress !='' ">
            and (t.api_name like concat('%',#{nameOrAddress},'%') escape '/' or t.api_address like
            concat('%',#{nameOrAddress},'%') escape '/')
        </if>
        <if test="sceneRelatedList != null and sceneRelatedList.size() == 1 and true in sceneRelatedList">
            and exists (select 1 from tm_scene_api_relation where api_test_index = t.api_test_index)
        </if>
        <if test="sceneRelatedList != null and sceneRelatedList.size() == 1 and false in sceneRelatedList">
            and not exists (select 1 from tm_scene_api_relation where api_test_index = t.api_test_index)
        </if>
        <if test="tagList != null and tagList.size() > 0">
            and qt.tag_name in
            <foreach collection="tagList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by t.gmt_modified desc
    </select>

    <resultMap id="ApiSampleCaseMap" type="com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiSampleCaseVO">
        <id column="api_code" jdbcType="VARCHAR" property="apiCode"/>
        <result column="api_name" jdbcType="VARCHAR" property="apiName"/>
        <result column="req_method" jdbcType="VARCHAR" property="reqMethod"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.RequestMethodTypeHandler"/>
        <result column="api_data" jdbcType="VARCHAR" property="apiData"/>
        <result column="doc_id" jdbcType="BIGINT" property="docId"/>
        <result column="doc_product_code" jdbcType="VARCHAR" property="docProductCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="api_type" jdbcType="VARCHAR" property="apiType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.ApiTypeHandler"/>
        <result column="api_address" jdbcType="VARCHAR" property="apiAddress"/>
        <result column="doc_version" jdbcType="VARCHAR" property="docVersion"/>
    </resultMap>

    <select id="selectUniqueApiWithSampleCase" resultMap="ApiSampleCaseMap">
        select
            t.api_code,
            t.api_name,
            t.req_method,
            t.doc_id,
            t.product_code,
            t.doc_product_code,
            t.app_id,
            t.api_type,
            t.api_address,
            t.doc_version,
            t.api_data
        from tm_api_test t
        <choose>
            <when test="apiCode != null and apiCode != ''">
                where t.api_code = #{apiCode}
            </when>
            <otherwise>
                where not exists (select 1 from tm_api_test where t.api_test_index = api_test_index and t.gmt_modified <![CDATA[ < ]]> gmt_modified)
                and t.enable in (1, 3)
                <if test="productCode != null and productCode != ''">
                    and t.product_code = #{productCode}
                </if>
                <if test="nameOrAddress != null and nameOrAddress !=''">
                    and (t.api_name like concat('%',#{nameOrAddress},'%') escape '/' or t.api_address like concat('%',#{nameOrAddress},'%') escape '/')
                </if>
                <if test="apiType != null">
                    and t.api_type = #{apiType, typeHandler=com.zto.devops.qc.infrastructure.dao.typehandler.ApiTypeHandler}
                </if>
                <if test="apiCodeList != null and apiCodeList.size() > 0">
                    and t.api_code in
                    <foreach collection="apiCodeList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="selectApiCode" resultType="java.lang.String">
        select
        t.api_code
        from tm_api_test t
        where t.enable in (1, 3)
        <if test="productCode != null and productCode != ''">
            and t.product_code = #{productCode}
        </if>
        <if test="nameOrAddress != null and nameOrAddress !=''">
            and (t.api_name like concat('%',#{nameOrAddress},'%') escape '/' or t.api_address like
            concat('%',#{nameOrAddress},'%') escape '/')
        </if>
        <if test="apiType != null">
            and t.api_type = #{apiType, typeHandler=com.zto.devops.qc.infrastructure.dao.typehandler.ApiTypeHandler}
        </if>
    </select>
</mapper>