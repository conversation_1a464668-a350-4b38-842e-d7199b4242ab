<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.ExecResultMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.ExecResultEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="code" jdbcType="VARCHAR" property="code"/>
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="business_domain" jdbcType="VARCHAR" property="businessDomain" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.BusinessDomainHandler"/>
    <result column="execute_result" jdbcType="VARCHAR" property="executeResult" />
  </resultMap>
</mapper>