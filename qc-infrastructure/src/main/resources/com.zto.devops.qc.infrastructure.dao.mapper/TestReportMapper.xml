<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TestReportMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TestReportEntity">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="plan_code" jdbcType="VARCHAR" property="planCode"/>
        <result column="report_type" jdbcType="VARCHAR" property="reportType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.ReportTypeHandler"/>
        <result column="version_code" jdbcType="VARCHAR" property="versionCode"/>
        <result column="version_name" jdbcType="VARCHAR" property="versionName"/>
        <result column="test_result" jdbcType="VARCHAR" property="testResult"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestResultHandler"/>
        <result column="actual_presentation_date" jdbcType="TIMESTAMP" property="actualPresentationDate"/>
        <result column="actual_approval_exit_date" jdbcType="TIMESTAMP" property="actualApprovalExitDate"/>
        <result column="actual_publish_date" jdbcType="TIMESTAMP" property="actualPublishDate"/>
        <result column="actual_test_start" jdbcType="TIMESTAMP" property="actualTestStart"/>
        <result column="actual_test_end" jdbcType="TIMESTAMP" property="actualTestEnd"/>
        <result column="check_start_date" jdbcType="TIMESTAMP" property="checkStartDate"/>
        <result column="check_end_date" jdbcType="TIMESTAMP" property="checkEndDate"/>
        <result column="update_test_result_date" jdbcType="TIMESTAMP" property="updateTestResultDate"/>
        <result column="auto_test_result" jdbcType="VARCHAR" property="autoTestResult"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AutoExecuteResultHandler"/>
        <result column="security_user_id" jdbcType="BIGINT" property="securityUserId"/>
        <result column="security_user_name" jdbcType="VARCHAR" property="securityUserName"/>
        <result column="security_test_result" jdbcType="VARCHAR" property="securityTestResult"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.SecurityTestResultHandler"/>
        <result column="check_type" jdbcType="VARCHAR" property="checkType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.CheckTypeHandler"/>
        <result column="developer_count" jdbcType="INTEGER" property="developerCount"/>
        <result column="tester_count" jdbcType="INTEGER" property="testerCount"/>
        <result column="plan_smoke_case" jdbcType="INTEGER" property="planSmokeCase"/>
        <result column="first_permit_smoke" jdbcType="INTEGER" property="firstPermitSmoke"/>
        <result column="as_planed_online" jdbcType="TINYINT" property="asPlanedOnline"/>
        <result column="delay" jdbcType="TINYINT" property="delay"/>
        <result column="summary" jdbcType="VARCHAR" property="summary"/>
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestPlanStatusHandler"/>
        <result column="preview" jdbcType="LONGVARCHAR" property="preview"/>
    </resultMap>
    <!-- pageQuery -->
    <select id="pageQuery" resultType="com.zto.devops.qc.client.model.report.entity.ReportVO">
        select
        distinct qr.code,
        qr.name,
        qr.plan_code as planCode,
        qt.product_code as productCode,
        qt.product_name as productName,
        qr.version_code as versionCode,
        qr.version_name as versionName,
        qr.report_type as reportType,
        qr.gmt_create as gmtCreate,
        qr.gmt_modified as gmtModified,
        qr.modifier as creator,
        qr.modifier_id as creatorId,
        ma.start_date,
        ma.approval_exit_date,
        ma.presentation_date,
        ma.publish_date,
        qr.actual_presentation_date,
        qr.actual_test_start,
        qr.actual_permit_date,
        qr.check_start_date,
        qr.check_end_date,
        qr.plan_smoke_case,
        qr.first_permit_smoke,
        qr.test_result,
        qr.delay,
        qr.check_type,
        qr.update_test_result_date,
        qr.developer_count,
        qr.tester_count,
        qr.actual_approval_exit_date,
        qr.actual_publish_date,
        qr.as_planed_online
        from qc_test_report qr
        left join qc_test_plan qt on qt.code=qr.plan_code
        left join qc_test_plan_main ma on qt.code = ma.test_plan_code
        where qr.enable = true
        <if test="codeOrName!=null and codeOrName!=''">
            and (qr.name like CONCAT('%',#{codeOrName},'%') or qr.code = #{codeOrName})
        </if>
        <if test="gmtCreateStart !=null and gmtCreateEnd != null">
            and #{gmtCreateEnd} >= qr.gmt_create and qr.gmt_create >= #{gmtCreateStart}
        </if>
        <if test="status !=null ">
            and qr.status=#{status}
        </if>
        <if test="versionCode !=null ">
            and qr.version_code=#{versionCode}
        </if>
        <if test="status.equals(@com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum@DRAFT)">
          and qr.creator_id = #{transactor.userId}
        </if>
        <if test="creatorId !=null ">
            and qr.creator_id = #{creatorId}
        </if>
        <if test="reportTypes!=null">
            and qr.report_type in
            <foreach collection="reportTypes" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="productCodes!=null">
            and qt.product_code in
            <foreach collection="productCodes" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="reportUserIds!=null">
            and qr.modifier_id in
            <foreach collection="reportUserIds" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="orderField!=null and orderField!=''">
                order by qr.#{orderField} #{orderType}
            </when>
            <otherwise>
                order by qr.gmt_create desc
            </otherwise>
        </choose>
    </select>
    <select id="versionReportQuery" resultType="com.zto.devops.qc.client.model.report.entity.ReportVO">
        select
        distinct qr.code,
        qr.name,
        qr.plan_code as planCode,
        qt.product_code as productCode,
        qt.product_name as productName,
        qr.version_code as versionCode,
        qr.version_name as versionName,
        qr.report_type as reportType,
        qr.gmt_create as gmtCreate,
        qr.gmt_modified as gmtModified,
        qr.creator as creator,
        qr.creator_id as creatorId
        from qc_test_report qr left join qc_test_plan qt on qt.code=qr.plan_code
        left join qc_test_plan_main qtm on qtm.test_plan_code=qr.plan_code
        where qr.enable = true and qr.status !='DRAFT'
        <if test="versionCode!=null and versionCode!=''">
            and qtm.version_code=#{versionCode}
        </if>
        order by qr.gmt_create desc
    </select>


    <select id="selectTestPlanMainByProductCode" resultType="com.zto.devops.qc.client.model.report.entity.ReportVO">
        select *
        from qc_test_report re
                 left join qc_test_plan qt on qt.code = re.plan_code
        where re.report_type in ('SIMPLE_PROCESS', 'CHECED_TEST')
          and re.status = 'NORMAL'
          and qt.product_code = #{productCode}
    </select>

    <select id="selectByVersionCode" resultType="com.zto.devops.qc.client.model.report.entity.ReportVO">
        select *
        from qc_test_report re
                 left join qc_test_plan qt on qt.code = re.plan_code
        where re.report_type in ('SIMPLE_PROCESS', 'CHECED_TEST')
          and qt.status = 'NORMAL'
          and re.version_code = #{versionCode}
    </select>

    <select id="getSimpleReportVOByVersionCode" resultType="com.zto.devops.qc.client.model.report.entity.RelatedBaseVO">
        select qr.code,
               qr.name,
               qr.report_type  as type,
               qr.version_code as versionCode
        from qc_test_report qr
        where qr.enable = true
          and qr.status = 'NORMAL'
          AND qr.version_code = #{versionCode}
    </select>

    <select id="getSimpleReportVOByQuery" resultType="com.zto.devops.qc.client.model.report.entity.RelatedBaseVO">
        select
        qr.code,
        qr.name,
        qr.report_type as type,
        qr.version_code as versionCode,
        tp.product_code as productCode
        from qc_test_report qr left join qc_test_plan tp on tp.code=qr.plan_code
        where qr.enable=true and qr.status='NORMAL' AND qr.version_code=#{versionCode}
        <if test="reportType !=null and reportType!=''">
            and qr.report_type!=#{reportType}
        </if>
    </select>
    <!-- queryVersionCodesByProductCode -->
    <select id="queryVersionCodesByProductCode" resultType="java.lang.String">
        (
            select qtr.version_code as versionCode
            from qc_test_report qtr
                     left join qc_test_plan qtp on
                qtp.code = qtr.plan_code
            where qtp.product_code = #{productCode}
              and qtr.enable = true
              and qtr.status != 'NORMAL'
    and (qtr.report_type = 'SIMPLE_PROCESS'
    or qtr.report_type = 'CHECED_TEST'))
        union all
        (
            select version_code as versionCode
            from qc_test_plan qtp
                     left join qc_test_plan_main qtpm on
                qtp.code = qtpm.test_plan_code
            where qtp.enable = true
              and qtp.product_code = #{productCode}
              and qtp.status != 'NORMAL')
    </select>

    <select id="selectBaseInfoVOByCode" resultType="com.zto.devops.qc.client.model.report.entity.BaseReportInfoVO">
        select qtr.code,
               qtr.version_code,            as versionCode,
               qtr.version_name             as versionName,
               qtr.name,
               qtr.plan_code                as planCode,
               qtp.plan_name                as planName,
               qtp.product_code             as productCode,
               qtp.product_name             as productName,
               qtpm.product_director_id     as productOwnerId,
               qtpm.product_director_name   as productOwnerName,
               qtr.creator                  as reportUserName,
               qtr.creator_id               as reportUserId,
               qtr.test_result              as testResult,
               qtpm.presentation_date   as PresentationDate,
               qtr.actual_presentation_date as actualPresentationDate,
               qtpm.approval_exit_date       as approvalExitDate,
               qtr.actual_approval_exit_date       as actualApprovalExitDate,
               qtp.publish_date         as publishDate,
               qtr.actual_publish_date       as actualPublish_date,
               qtr.actual_test_start        as actualTestStart,
               qtr.actual_test_end          as actualTestEnd,
               qtr.check_end_date           as checkEndDate,
               qtr.check_start_date         as checkStartDate,
               qtr.summary,
               qtr.auto_test_result         as autoTestResult,
               qtr.security_user_id         as securityUserId,
               qtr.security_user_name       as securityUserName,
               qtr.security_test_result     as securityTestResult,
               qtr.status                   as status,
               qtr.delay,
               qtp.dept_id                  as deptId,
               qtp.dept_name                as deptName
        from qc_test_report qtr
                 left join qc_test_plan qtp on
            qtr.plan_code = qtp.code
                 left join qc_test_plan_main qtpm on
            qtpm.test_plan_code = qtp.code
        where qtr.code = #{code}
    </select>


    <select id="getPlanCodeAndTypeOne" resultMap="BaseResultMap">
        select *
        from qc_test_report t
        where t.enable = 1
          and t.status = 'NORMAL'
          and t.report_type = #{reportType}
          and t.plan_code = #{planCode}
        order by id desc limit 1
    </select>

  <select id="getVersionCodeAndTypeOne" resultMap="BaseResultMap">
        select *
        from qc_test_report t
        where t.enable = 1
          and t.status = 'NORMAL'
          and t.report_type = #{reportType}
          and t.version_code = #{versionCode}
        order by id desc limit 1
    </select>

  <select id="getTestReportByResult" resultType="com.zto.devops.qc.client.model.report.entity.ReportTestResultMsgVO">
        select
         t.creator_id as userId,
         t.code as reportCode,
         t.name as reportName,
         t.update_test_result_date as updateTestResultDate
        from qc_test_report t
        where t.enable = true
          and t.status = 'NORMAL'
          and t.test_result = 'OTHER'
          and t.report_type in ('SIMPLE_PROCESS','ONLINE_SMOKE')
          and t.update_test_result_date  <![CDATA[ < ]]> #{updateTestResultDate}
    </select>

    <select id="selectTransferList"
            resultType="com.zto.devops.qc.client.model.testmanager.email.entity.TestReportDetailVO">
        select
        qtr.report_type  as reportType,
        qtr.code  as reportCode,
        qtr.name  as reportName,
        qtr.plan_code as relatePlanCode,
        qtp.plan_name as relatePlanName,
        qtp.product_code as productCode,
        qtp.product_name as productName,
        qtp.version_code as versionCode,
        qtp.version_name as versionName,
        qtr.preview as preview,
        qtr.gmt_create as gmtCreate,
        qtr.gmt_modified as gmtModified,
        qtr.creator_id as creatorId,
        qtr.creator as creator,
        qtr.modifier_id as modifierId,
        qtr.modifier as modifier
        from
        qc_test_report qtr
        left join qc_test_plan qtp on qtr.plan_code = qtp.code
        where
        1 = 1
        and qtr.preview != ''
        and qtr.enable = 1
        and qtp.`type` in ('TEST_PLAN','INTEGRATION_TEST','MOBILE_SPECIAL')
        <if test="begin != null and end != null">
            and qtr.gmt_create BETWEEN #{begin} and #{end}
        </if>
        order by qtr.gmt_create
    </select>

</mapper>