<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TransitionNodeMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TransitionNodeEntity">
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="domain" jdbcType="VARCHAR" property="domain" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler"/>
    <result column="reason" jdbcType="VARCHAR" property="reason" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.ReasonHandler"/>
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="cur_status" jdbcType="VARCHAR" property="curStatus" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="next_status" jdbcType="VARCHAR" property="nextStatus" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
  </resultMap>

  <select id="queryLastTransitionNodeByVersionCode" resultMap="BaseResultMap" parameterType="com.zto.devops.qc.client.model.issue.query.FindIssueByVersionCodeQuery">
    SELECT tn.* FROM qc_issue i
    join qc_transition_node tn
    on (i.code = tn.business_code)
    where i.enable = 1 and tn.enable =1 and i.find_version_code = #{versionCode} and i.status = 'CLOSED' and tn.next_status = #{nextStatus}
  </select>

  <select id="selectIssueReopenList" resultMap="BaseResultMap">
    SELECT * FROM  qc_transition_node
    where enable = 1 and domain = 'ISSUE'
    and business_code in
    <foreach collection="businessCodes" item="businessCode" index="index" separator="," open="(" close=")">
      #{businessCode}
    </foreach>
    and (
       (cur_status='TESTING' and next_status='WAIT_FIX') or (cur_status='TESTING' and next_status='FIXING')
       or (cur_status='CLOSED' and next_status='WAIT_FIX') or (cur_status='CLOSED' and next_status='FIXING')
       or (cur_status='REJECTED' and next_status='WAIT_FIX') or (cur_status='REJECTED' and next_status='FIXING')
    )
  </select>

  <select id="countInvalidCount" resultType="java.lang.Integer">
    select count(distinct qi.code)
    from qc_issue qi
           left join qc_transition_node qtn on qi.code = qtn.business_code
    where 1 = 1
      and (qtn.cur_status = 'REJECTED' and qtn.next_status = 'CLOSED')
      and qtn.reason in ('NO_ISSUE', 'REPEAT_SUBMIT')
      and (qi.find_version_code = #{versionCode} or qi.fix_version_code = #{versionCode})
    group by qi.code
  </select>

  <select id="countInvalidUiTestCount" resultType="java.lang.Integer">
    select count(distinct qi.code)
    from qc_issue qi
           left join qc_transition_node qtn on qi.code = qtn.business_code
    where 1 = 1 and qi.type='USER_EXPERIENCE_BUG'
      and (qtn.cur_status = 'REJECTED' and qtn.next_status = 'CLOSED')
      and qtn.reason in ('NO_ISSUE', 'REPEAT_SUBMIT')
      and (qi.find_version_code = #{versionCode} or qi.fix_version_code = #{versionCode})
    group by qi.code
  </select>

  <select id="selectContentByIssueList" resultMap="BaseResultMap">
    select tn.code,tn.business_code,tn.content from qc_transition_node tn where tn.business_code in
    <foreach collection="businessCodes" item="businessCode" index="index" separator="," open="(" close=")">
      #{businessCode}
    </foreach>
    and tn.enable =1
    order by tn.gmt_create desc limit 1
  </select>
</mapper>