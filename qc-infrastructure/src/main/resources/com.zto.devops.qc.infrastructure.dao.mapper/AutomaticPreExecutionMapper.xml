<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.AutomaticPreExecutionMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.AutomaticPreExecutionEntity">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="pre_code" jdbcType="VARCHAR" property="preCode" />
        <result column="scheduler_code" jdbcType="VARCHAR" property="schedulerCode" />
        <result column="scheduler_name" jdbcType="VARCHAR" property="schedulerName" />
        <result column="execute_time" jdbcType="TIMESTAMP" property="executeTime" />
        <result column="pre_status" jdbcType="VARCHAR" property="preStatus"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.SchedulerPreExecuteStatusHandler"/>
        <result column="enable" jdbcType="BIT" property="enable" />
        <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
        <result column="modifier" jdbcType="VARCHAR" property="modifier" />
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    </resultMap>

    <sql id="Base_Column_List">
        id, pre_code, scheduler_code, scheduler_name, execute_time, pre_status, enable,
        creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified
    </sql>

    <select id="selectByExecutionTime" resultType="com.zto.devops.qc.infrastructure.dao.entity.AutomaticPreExecutionEntity">
        select <include refid="Base_Column_List"/>
        from tm_pre_execution_scheduler
        where execute_time <![CDATA[ <= ]]> #{currentTime}
        and enable = true
        and pre_status = 'INITIAL'
    </select>
</mapper>
