<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.CommentMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.CommentEntity">
    <id column="code" jdbcType="VARCHAR" property="code" />
    <result column="domain" jdbcType="VARCHAR" property="domain" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler"/>
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="top_replied_code" jdbcType="VARCHAR" property="topRepliedCode" />
    <result column="replied_code" jdbcType="VARCHAR" property="repliedCode" />
    <result column="replied_user_name" jdbcType="VARCHAR" property="repliedUserName" />
    <result column="replied_user_id" jdbcType="BIGINT" property="repliedUserId" />
    <result column="level" jdbcType="TINYINT" property="level" />
    <result column="content" jdbcType="VARCHAR" property="content" />
  </resultMap>
</mapper>