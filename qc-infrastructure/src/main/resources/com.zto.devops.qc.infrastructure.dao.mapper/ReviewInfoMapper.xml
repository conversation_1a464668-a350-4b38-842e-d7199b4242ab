<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.ReviewInfoMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.ReviewInfoEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="code" jdbcType="VARCHAR" property="code"/>
    <result column="report_code" jdbcType="VARCHAR" property="reportCode" />
    <result column="review_date" jdbcType="DATE" property="reviewDate" />
    <result column="review_type" jdbcType="VARCHAR" property="reviewType" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.ReviewTypeHandler"/>
    <result column="record_user_id" jdbcType="BIGINT" property="recordUserId" />
    <result column="record_user_name" jdbcType="VARCHAR" property="recordUserName" />
    <result column="product_review_users" jdbcType="VARCHAR" property="productReviewUsers" />
    <result column="develop_review_users" jdbcType="VARCHAR" property="developReviewUsers" />
    <result column="test_review_users" jdbcType="VARCHAR" property="testReviewUsers" />
  </resultMap>
</mapper>