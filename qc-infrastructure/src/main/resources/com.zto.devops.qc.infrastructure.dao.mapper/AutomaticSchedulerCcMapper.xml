<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.AutomaticSchedulerCcMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.AutomaticSchedulerCcEntity">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="scheduler_code" jdbcType="VARCHAR" property="schedulerCode" />
        <result column="cc_id" jdbcType="BIGINT" property="ccId" />
        <result column="cc_name" jdbcType="VARCHAR" property="ccName" />
        <result column="enable" jdbcType="BIT" property="enable" />
        <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
        <result column="modifier" jdbcType="VARCHAR" property="modifier" />
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    </resultMap>

    <insert id="batchInsert">
        INSERT INTO tm_automatic_scheduler_cc
        (scheduler_code, cc_id, cc_name, creator_id, creator, modifier_id, modifier)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.schedulerCode}, #{item.ccId}, #{item.ccName}, #{item.creatorId}, #{item.creator},
            #{item.modifierId}, #{item.modifier})
        </foreach>
    </insert>
</mapper>