<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.ApiTestVariableMapper">

    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.ApiTestVariableEntity">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="link_code" jdbcType="VARCHAR" property="linkCode"/>
        <result column="variable_code" jdbcType="VARCHAR" property="variableCode"/>
        <result column="variable_name" jdbcType="VARCHAR" property="variableName"/>
        <result column="variable_key" jdbcType="VARCHAR" property="variableKey"/>
        <result column="variable_value" jdbcType="VARCHAR" property="variableValue"/>
        <result column="variable_status" jdbcType="VARCHAR" property="variableStatus"/>
        <result column="type" jdbcType="VARCHAR" property="type"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.ApiTestVariableTypeHandler"/>
        <result column="usage_type" jdbcType="INTEGER" property="usageType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.ApiTestVariableUsageTypeHandler"/>
        <result column="scene_type" jdbcType="INTEGER" property="sceneType"/>
        <result column="required_status" jdbcType="BOOLEAN" property="requiredStatus"/>
        <result column="enable" jdbcType="BOOLEAN" property="enable"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="DATE" property="gmtModified"/>
        <result column="gmt_create" jdbcType="DATE" property="gmtCreate"/>
        <result column="variable_desc" jdbcType="VARCHAR" property="variableDesc"/>
        <result column="login_valid_time" jdbcType="INTEGER" property="loginValidTime"/>
        <result column="sub_variable_type" jdbcType="INTEGER" property="subVariableType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.ApiTestSubVariableTypeHandler"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, product_code, product_name, link_code, variable_code, variable_name, variable_key,
        variable_value, variable_status, type, enable, creator_id, creator, gmt_create,
        modifier_id, modifier, gmt_modified, variable_desc, login_valid_time, sub_variable_type
    </sql>

    <select id="getUniqueApiTestVariable"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        FROM tm_api_test_variable
        WHERE enable = 1
        <if test="variableCode != null and variableCode !='' ">
            and variable_code != #{variableCode}
        </if>
        AND product_code = #{productCode}
        AND link_code = #{linkCode}
        AND variable_key = #{variableKey}
    </select>

    <select id="getApiVariableByProductCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        FROM tm_api_test_variable
        WHERE enable = 1
        and type = #{type}
        AND product_code = #{productCode}
    </select>

    <select id="getUniqueDbVariable" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        FROM tm_api_test_variable
        WHERE enable = 1
        AND product_code = #{productCode}
        AND scene_type = #{sceneType}
        AND variable_key = #{variableKey}
        AND type = #{variableType}
        <if test="businessCode != null and businessCode != '' ">
            AND link_code = #{businessCode}
        </if>
        limit 1
    </select>

    <insert id="batchInsert">
        insert into tm_api_test_variable (
        product_code, product_name, link_code, variable_code, variable_name, variable_key, variable_value, type,
        variable_status, enable,
        creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified,
        variable_desc,scene_type,usage_type,required_status,
        sub_variable_type, login_valid_time
        ) values
        <foreach collection="list" item="item" separator=",">
            (#{item.productCode}, #{item.productName}, #{item.linkCode}, #{item.variableCode},#{item.variableName},
            #{item.variableKey}, #{item.variableValue},
            #{item.type, typeHandler=com.zto.devops.qc.infrastructure.dao.typehandler.ApiTestVariableTypeHandler},
            #{item.variableStatus}, #{item.enable},
            #{item.creatorId}, #{item.creator}, #{item.gmtCreate}, #{item.modifierId}, #{item.modifier}, #{item.gmtModified},
            #{item.variableDesc},
            #{item.sceneType},
            #{item.usageType,jdbcType=INTEGER,typeHandler=com.zto.devops.qc.infrastructure.dao.typehandler.ApiTestVariableUsageTypeHandler},
            #{item.requiredStatus},
            #{item.subVariableType,jdbcType=INTEGER,typeHandler=com.zto.devops.qc.infrastructure.dao.typehandler.ApiTestSubVariableTypeHandler},
            #{item.loginValidTime}
            )
        </foreach>
    </insert>

</mapper>
