<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TmTestPlanCaseMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanCaseEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="plan_code" jdbcType="VARCHAR" property="planCode"/>
        <result column="case_type" jdbcType="VARCHAR" property="caseType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseTypeHandler"/>
        <result column="test_stage" jdbcType="VARCHAR" property="testStage"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanStageHandler"/>
        <result column="case_code" jdbcType="VARCHAR" property="caseCode"/>
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseStatusHandler"/>
        <result column="enable" jdbcType="BIT" property="enable"/>
        <result column="executor_id" jdbcType="BIGINT" property="executorId"/>
        <result column="executor" jdbcType="VARCHAR" property="executor"/>
        <result column="result_path" jdbcType="VARCHAR" property="resultPath"/>
        <result column="log_path" jdbcType="VARCHAR" property="logPath"/>
        <result column="result_comment" jdbcType="VARCHAR" property="resultComment" />
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>

    <resultMap id="TestPlanCaseVO" type="com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanCaseVO">
        <result column="version_code" jdbcType="VARCHAR" property="versionCode" />
        <result column="plan_code" jdbcType="VARCHAR" property="planCode" />
        <result column="case_code" jdbcType="VARCHAR" property="caseCode" />
        <result column="test_stage" jdbcType="VARCHAR" property="testStage"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanStageHandler" />
        <result column="testcase_status" jdbcType="VARCHAR" property="testcaseStatus"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcaseStatusTypeHandler"/>
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseStatusHandler"/>
        <result column="priority" jdbcType="VARCHAR" property="priority"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcasePriorityTypeHandler" />
        <result column="duty_user_id" jdbcType="BIGINT" property="dutyUserId" />
        <result column="duty_user" jdbcType="VARCHAR" property="dutyUser"/>
        <result column="executor_id" jdbcType="BIGINT" property="executorId"/>
        <result column="executor" jdbcType="VARCHAR" property="executor"/>
        <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
        <result column="name" jdbcType="VARCHAR" property="caseName" />
        <result column="case_type" jdbcType="VARCHAR" property="type"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcaseTypeTypeHandler"/>
        <result column="path" jdbcType="VARCHAR" property="casePath" />
        <result column="testcase_module_path" jdbcType="VARCHAR" property="testcaseModulePath" />
        <result column="attribute" jdbcType="VARCHAR" property="caseAttribute"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcaseAttributeTypeHandler" />
        <result column="node_type" jdbcType="VARCHAR" property="nodeType" />
        <result column="enable" jdbcType="BIT" property="enable"/>
        <result column="operate_case_code" jdbcType="VARCHAR" property="operateCaseCode" />
        <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
        <result column="issue_count" jdbcType="INTEGER" property="issueCount" />
        <result column="not_fix_issue_count" jdbcType="INTEGER" property="notFixIssueCount" />
        <result column="execute_num" jdbcType="INTEGER" property="executeNum" />
        <result column="result_comment" jdbcType="VARCHAR" property="resultComment" />
    </resultMap>

    <select id="testPlanCaseList" parameterType="com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseQuery"
            resultMap="TestPlanCaseVO">
        select
            tc.version_code  as versionCode,
            pc.plan_code,
            pc.case_code,
            pc.test_stage,
            tc.status as testcase_status,
            pc.status,
            tc.priority,
            tc.duty_user_id,
            tc.duty_user,
            pc.executor_id ,
            pc.executor,
            tc.parent_code,
            tc.name,
            tc.type,
            tc.path,
            tc.testcase_module_path,
            tc.attribute,
            tc.node_type,
            pc.enable,
            pc.operate_case_code,
            tc.interface_name,
            pc.result_comment,
            (
                select count(distinct iss.code) from tm_testcase_relation tcr
                left join qc_issue iss on iss.code = tcr.business_code
                where tcr.testcase_code = pc.case_code and tcr.domain = 'ISSUE'
                and (iss.find_version_code = tp.version_code or iss.fix_version_code = tp.version_code)
            ) as issue_count,
            (
                select count(distinct iss.code) from tm_testcase_relation tcr
                left join qc_issue iss on iss.code = tcr.business_code
                where tcr.testcase_code = pc.case_code and tcr.domain = 'ISSUE'
                and iss.`status` not in ('CLOSED', 'REMOVED')
                and (iss.find_version_code = tp.version_code or iss.fix_version_code = tp.version_code)
            ) as not_fix_issue_count,
            (
                select count(*) from tm_testcase_execute_record as tter
                where tter.enable = 1 and tter.testcase_code = tc.code and tter.test_plan_code = pc.plan_code
                and tter.test_stage = pc.test_stage
            ) as execute_num
        from tm_test_plan_case pc
        left join tm_test_plan tp on tp.code = pc.plan_code
        left join tm_testcase tc on pc.case_code = tc.code
        left join tm_automatic_source_record asr on asr.code = tc.automatic_source_code
        where tc.attribute='TESTCASE' and pc.plan_code= #{planCode}
        <if test="caseCodeList != null and caseCodeList.size() > 0">
            and pc.case_code in
            <foreach collection="caseCodeList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="testPlanStageList != null and testPlanStageList.size() > 0">
            and pc.test_stage in
            <foreach collection="testPlanStageList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="parentCode != null and parentCode != ''">
            and (tc.path like "%" #{parentCode} "%" or tc.testcase_module_path like "%" #{parentCode} "%")
        </if>
        <if test="parentCode == ''">
            and tc.parent_code = #{parentCode}
        </if>
        <if test="versionCode != null and versionCode != ''">
            and tc.version_code = #{versionCode}
        </if>
        <if test="search != null and search !='' ">
            and (<![CDATA[
                tc.code like "%" #{search} "%"
                or tc.name like "%" #{search} "%"
                or tc.interface_name like "%" #{search} "%"
            ]]>)
        </if>
        <if test="caseTypeList != null and caseTypeList.size() > 0">
            and pc.case_type in
            <foreach collection="caseTypeList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="setCoreList != null and setCoreList.size() > 0">
            and tc.set_core in
            <foreach collection="setCoreList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and pc.status in
            <foreach collection="statusList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="executorIdList != null and executorIdList.size() > 0">
            and pc.executor_id in
            <foreach collection="executorIdList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="priorityList != null and priorityList.size() > 0">
            and tc.priority in
            <foreach collection="priorityList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="nodeTypeList != null and nodeTypeList.size() > 0">
            and tc.node_type in
            <foreach collection="nodeTypeList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="automaticTypeList != null and automaticTypeList.size() > 0">
            and asr.type in
            <foreach collection="automaticTypeList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="tagList != null and tagList.size() > 0">
            and tc.code in (
            select business_code from qc_tag where enable = 1 and domain = 'TESTCASE' and tag_name in
            <foreach collection="tagList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        order by tc.id
    </select>

    <resultMap id="PlanCaseVO" type="com.zto.devops.qc.client.model.testmanager.cases.entity.PlanCaseVO">
        <result column="plan_code" jdbcType="VARCHAR" property="planCode"/>
        <result column="case_code" jdbcType="VARCHAR" property="caseCode"/>
        <result column="test_stage" jdbcType="VARCHAR" property="testStage"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanStageHandler"/>
        <result column="executor_id" jdbcType="BIGINT" property="executorId"/>
        <result column="executor" jdbcType="VARCHAR" property="executor"/>
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseStatusHandler"/>
        <result column="operate_case_code" jdbcType="VARCHAR" property="operateCaseCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode" />
        <result column="version_code" jdbcType="VARCHAR" property="versionCode" />
        <result column="plan_director_id" jdbcType="BIGINT" property="planDirectorId"/>
        <result column="plan_status" jdbcType="VARCHAR" property="planStatus"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanStatusHandler"/>
        <result column="stage_status" jdbcType="CHAR" property="stageStatus"
                typeHandler="com.zto.devops.framework.infrastructure.dao.handler.MapJsonTypeHandler"/>
        <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="type" jdbcType="VARCHAR" property="type"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcaseTypeTypeHandler" />
        <result column="priority" jdbcType="VARCHAR" property="priority"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcasePriorityTypeHandler" />
        <result column="precondition" jdbcType="VARCHAR" property="precondition" />
        <result column="comment" jdbcType="VARCHAR" property="comment" />
        <result column="enable" jdbcType="BOOLEAN" property="enable" />
        <result column="node_type" jdbcType="VARCHAR" property="nodeType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticNodeTypeHandler" />
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
        <result column="modifier" jdbcType="VARCHAR" property="modifier" />
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
        <result column="path" jdbcType="VARCHAR" property="path" />
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="duty_user_id" jdbcType="BIGINT" property="dutyUserId" />
        <result column="testcase_status" jdbcType="VARCHAR" property="testcaseStatus"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcaseStatusTypeHandler"/>
        <result column="automatic_source_type" jdbcType="VARCHAR" property="automaticRecordType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticRecordTypeHandler"/>
        <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
        <result column="result_comment" jdbcType="VARCHAR" property="resultComment" />
    </resultMap>

    <select id="selectPlanCase" resultMap="PlanCaseVO">
        SELECT
            t1.plan_code,
            t1.case_code,
            t1.test_stage,
            t1.executor_id,
            t1.executor,
            t1.status,
            t1.operate_case_code,
            t1.result_comment,
            t2.product_code,
            t2.version_code,
            t2.plan_director_id,
            t2.status AS plan_status,
            t2.stage_status,
            t3.parent_code,
            t3.name,
            t3.type,
            t3.status AS testcase_status,
            t3.priority,
            t3.node_type,
            t3.precondition,
            t3.comment,
            t3.path,
            t3.duty_user_id,
            t3.enable,
            t3.creator_id,
            t3.modifier_id,
            t3.modifier,
            t3.gmt_modified,
            t3.interface_name,
            t4.type as automatic_source_type
        FROM tm_test_plan_case t1
        INNER JOIN tm_test_plan t2 ON t2.code = t1.plan_code
        INNER JOIN tm_testcase t3 ON t3.code = t1.case_code
        LEFT JOIN tm_automatic_source_record t4 on t3.automatic_source_code = t4.code
        WHERE t1.plan_code = #{planCode}
            AND t1.case_code = #{caseCode}
            AND t1.test_stage = #{testStage}
    </select>

    <select id="selectDistinctListByPlanCode" resultType="com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanCaseEntity">
        select
        any_value(ttpc.id) as id,
        any_value(ttpc.plan_code) as planCode,
        any_value(ttpc.case_code) as caseCode,
        any_value(ttpc.case_type) as caseType ,
        any_value( ttpc .test_stage ) as testStage,
        any_value(ttpc.status) as status
        from
        tm_test_plan_case ttpc
        where
        ttpc.plan_code = #{planCode}
    </select>

    <update id="updateByAutomaticTaskCode">
        UPDATE
            tm_test_plan_case t1,
            tm_testcase_execute_record t2
        SET
            t1.status = t2.result,
            t1.executor_id = t2.creator_id,
            t1.executor = t2.creator,
            t1.modifier_id = t2.modifier_id,
            t1.modifier = t2.modifier,
            t1.gmt_modified = t2.gmt_modified
        WHERE t1.plan_code = t2.test_plan_code
            AND t1.test_stage = t2.test_stage
            AND t1.case_code = t2.testcase_code
            AND t2.automatic_task_code = #{automaticTaskCode}
    </update>

    <select id="selectOperateCaseCodeByTaskCode" resultType="java.lang.String">
        SELECT
            operate_case_code
        FROM tm_test_plan_case t1, tm_testcase_execute_record t2
        WHERE t1.plan_code = t2.test_plan_code
            AND t1.test_stage = t2.test_stage
            AND t1.case_code = t2.testcase_code
            AND t2.automatic_task_code = #{automaticTaskCode}
    </select>

    <delete id="deleteByIdList">
        delete from tm_test_plan_case
        where id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <select id="selectOperateCaseCodeByCaseCode" resultType="java.lang.String">
        SELECT
            operate_case_code
        FROM tm_test_plan_case
        WHERE case_code = #{caseCode}
    </select>

    <select id="selectOperateCaseCodeByVersionCode" resultType="java.lang.String">
        SELECT
            DISTINCT t1.operate_case_code
        FROM tm_test_plan_case t1, tm_test_plan t2
        WHERE t2.version_code = #{versionCode}
            AND t1.case_code IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <insert id="batchSave">
        INSERT INTO `lbd_qc`.`tm_test_plan_case` (`plan_code`, `case_type`, `test_stage`,
                                                  `case_code`, `status`, `creator_id`,
                                                  `creator`, `operate_case_code`,
                                                  `executor_id`, `executor`) VALUES
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.planCode}, #{item.caseType},#{item.testStage},
            #{item.caseCode},#{item.status}, #{item.creatorId},
            #{item.creator},#{item.operateCaseCode},
            #{item.creatorId},#{item.creator})
        </foreach>
    </insert>

    <select id="selectTestcaseCodeList" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.SimpleTestcaseVO">
        select
            pc.case_code as code,
            tc.path
        from tm_test_plan_case pc
        left join tm_testcase tc on pc.case_code = tc.code
        left join tm_automatic_source_record asr on asr.code = tc.automatic_source_code
        where pc.plan_code = #{planCode} and tc.attribute='TESTCASE'
        <if test="parentCode != null and parentCode !=''">
            and (tc.path like CONCAT('%',#{parentCode},'%') or tc.testcase_module_path like CONCAT('%',#{parentCode},'%'))
        </if>
        <if test="versionCode != null and versionCode !=''">
            and tc.version_code = #{versionCode}
        </if>
        <if test="parentCode == ''">
            and tc.parent_code = ''
        </if>
        <if test="search != null and search !='' ">
            and (
                tc.code like CONCAT('%',#{search},'%')
                or tc.name like CONCAT('%',#{search},'%')
                or tc.interface_name like CONCAT('%',#{search},'%')
            )
        </if>
        <if test="testPlanStageList != null and testPlanStageList.size() > 0">
            and pc.test_stage in
            <foreach collection="testPlanStageList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="caseTypeList != null and caseTypeList.size() > 0">
            and pc.case_type in
            <foreach collection="caseTypeList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and pc.status in
            <foreach collection="statusList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="executorIdList != null and executorIdList.size() > 0">
            and pc.executor_id in
            <foreach collection="executorIdList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="priorityList != null and priorityList.size() > 0">
            and tc.priority in
            <foreach collection="priorityList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="nodeTypeList != null and nodeTypeList.size() > 0">
            and tc.node_type in
            <foreach collection="nodeTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="automaticTypeList != null and automaticTypeList.size() > 0">
            and asr.type in
            <foreach collection="automaticTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="tagList != null and tagList.size() > 0">
            and pc.case_code in (
            select business_code from qc_tag where enable = 1 and domain = 'TESTCASE' and tag_name in
            <foreach collection="tagList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="setCoreList != null and setCoreList.size() > 0">
            and tc.set_core in
            <foreach collection="setCoreList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectAllPlanCasePath" resultType="com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanCaseVO">
        select
            tc.code,
            tc.version_code as versionCode,
            tc.path as casePath,
            tc.testcase_module_path AS testcaseModulePath
        from tm_test_plan_case pc
        inner join tm_testcase tc on pc.case_code = tc.code
        where pc.plan_code= #{planCode}
        <if test="setCore != null">
            and tc.set_core=#{setCore}
        </if>
        <if test="testPlanStageList != null and testPlanStageList.size() > 0">
            and pc.test_stage in
            <foreach collection="testPlanStageList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="caseTypeList != null and caseTypeList.size() > 0">
            and pc.case_type in
            <foreach collection="caseTypeList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectPlanCaseModuleList" resultType="com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanCaseVO">
        select t1.*, t2.sort from (
        select
            tc.id,
            tc.code as caseCode,
            tc.version_code as versionCode,
            tc.parent_code as parentCode,
            tc.name as caseName,
            tc.attribute as caseAttribute,
            tc.type,
            tc.enable,
            null as automaticRecordType,
            tc.gmt_create,
            tc.path as casePath
        from tm_testcase tc
        where tc.product_code = #{productCode}
        <if test="caseTypeList != null and caseTypeList.size() > 0">
            and tc.type in
            <foreach collection="caseTypeList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
            and tc.attribute = 'MODULE'
        <if test="setCore != null">
            and tc.set_core = #{setCore}
        </if>
            and tc.automatic_source_code = ''
        <if test="isAuto != null and isAuto == true">
            union all
            select
                asr.id,
                asr.code as caseCode,
                '' as versionCode,
                ifnull(asr.testcase_code, '') as parentCode,
                asr.name as caseName,
                'MODULE' as caseAttribute,
                'SOURCERECORD' as type,
                asr.enable,
                asr.type as automaticRecordType,
                asr.gmt_create,
                null as casePath
            from tm_automatic_source_record asr
            where asr.product_code = #{productCode}
        </if>
        ) t1
        left join tm_testcase_sort t2 on t2.code = t1.caseCode
        order by ifnull(t2.sort,99999), t1.gmt_create, t1.id
    </select>

    <select id="selectPlanCaseAutomaticModuleList" resultType="com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanCaseVO">
        select
            tc.id as id,
            tc.code as caseCode,
            case
                when tc.parent_code = '' then tc.automatic_source_code
                else tc.parent_code
            end as parentCode,
            tc.name as caseName,
            tc.attribute as caseAttribute,
            tc.type,
            tc.enable,
            tc.node_type as nodeType,
            tc.status as testcaseStatus,
            tc.path as casePath
        from tm_testcase tc
        where tc.product_code = #{productCode}
            and tc.type = 'AUTO'
            and tc.attribute = 'MODULE'
            and tc.automatic_source_code != ''
    </select>

    <select id="findStatusList" resultType="com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum">
        select
            status
        from tm_test_plan_case
        where 1 = 1
          and plan_code = #{planCode}
        <if test="queryDelete != null and queryDelete == true">
            and enable = 1
        </if>
        <if test="testStage != null">
            and test_stage = #{testStage}
        </if>
    </select>

    <resultMap id="PlanCaseByCodeListVO" type="com.zto.devops.qc.client.model.testmanager.plan.entity.DeletePlanCaseVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_code" jdbcType="VARCHAR" property="caseCode"/>
        <result column="plan_code" jdbcType="VARCHAR" property="planCode"/>
        <result column="plan_status" jdbcType="VARCHAR" property="planStatus" />
        <result column="plan_stage_status" jdbcType="CHAR" property="planStageStatus"
                typeHandler="com.zto.devops.framework.infrastructure.dao.handler.MapJsonTypeHandler"/>
        <result column="test_stage" jdbcType="VARCHAR" property="testStage"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanStageHandler"/>
    </resultMap>

    <select id="selectPlanCaseByCodeList" resultMap="PlanCaseByCodeListVO" >
        SELECT
            ttpc.id ,
            ttpc.case_code ,
            ttpc.plan_code ,
            ttp.status AS plan_status,
            ttp.stage_status AS plan_stage_status,
            ttpc.test_stage
        from tm_test_plan_case ttpc
                 INNER JOIN tm_test_plan ttp ON ttp.code = ttpc.plan_code
        WHERE ttpc.enable = 1
        AND ttpc.case_code  IN
        <foreach collection="testCaseCodeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectSimplePlanCase" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.PlanCaseVO">
        select
            t1.case_code as caseCode,
            t2.parent_code as parentCode,
            t2.path
        from tm_test_plan_case t1
        left join tm_testcase t2 on t2.code = t1.case_code
        where t1.plan_code = #{planCode} and t1.test_stage = #{testStage} and t2.type = #{testcaseType}
        <if test="priorityList != null and priorityList.size() > 0">
            and t2.priority in
            <foreach collection="priorityList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="tagList != null and tagList.size() > 0">
            and t2.code in (
                select business_code from qc_tag where enable = 1 and domain = 'TESTCASE' and tag_name in
                <foreach collection="tagList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            )
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and t1.status in
            <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="executorIdList != null and executorIdList.size() > 0">
            and t1.executor_id in
            <foreach collection="executorIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="codeOrTitle != null and codeOrTitle != ''">
            and (t1.code like "%" #{codeOrTitle} "%" or t1.name like "%" #{codeOrTitle} "%" ESCAPE '/')
        </if>
        <if test="setCore != null">
            and t2.set_core = #{setCore}
        </if>
        order by t2.id
    </select>

    <select id="selectVersionCodeByPlanCode" resultType="java.lang.String">
        select distinct tt.version_code
        from tm_test_plan_case ttpc
                 left join tm_testcase tt on ttpc.case_code = tt.code
        where ttpc.plan_code = #{planCode}
          and tt.enable = true
          and ttpc.enable = true
    </select>

</mapper>
