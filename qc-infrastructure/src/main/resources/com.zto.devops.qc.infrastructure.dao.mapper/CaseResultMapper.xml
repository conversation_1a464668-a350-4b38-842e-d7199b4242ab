<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.CaseResultMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.CaseResultEntity">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="code" jdbcType="VARCHAR" property="code"/>
        <result column="report_code" jdbcType="VARCHAR" property="reportCode"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="total_case_count" jdbcType="INTEGER" property="totalCaseCount"/>
        <result column="rounds" jdbcType="INTEGER" property="rounds"/>
        <result column="execute_case_count" jdbcType="INTEGER" property="executeCaseCount"/>
        <result column="access_case_count" jdbcType="INTEGER" property="accessCaseCount"/>
        <result column="failed_case_count" jdbcType="INTEGER" property="failedCaseCount"/>
        <result column="blocked_case_count" jdbcType="INTEGER" property="blockedCaseCount"/>
    </resultMap>

    <select id="selectByReportCode" resultMap="BaseResultMap">
        select *
        from qc_case_detail t
        where t.report_code = #{reportCode} and t.enable=true
    </select>
</mapper>