<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TmCaseExecuteResultMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TmCaseExecuteResultEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="code" jdbcType="VARCHAR" property="code"/>
    <result column="report_code" jdbcType="VARCHAR" property="reportCode" />
    <result column="frog_plan_code" jdbcType="VARCHAR" property="frogPlanCode" />
    <result column="frog_plan_name" jdbcType="VARCHAR" property="frogPlanName" />
    <result column="total_num" jdbcType="INTEGER" property="totalNum" />
    <result column="plan_num" jdbcType="INTEGER" property="planNum" />
    <result column="pass_num" jdbcType="INTEGER" property="passNum" />
    <result column="case_result" jdbcType="VARCHAR" property="caseResult" />
  </resultMap>

  <select id="selectByReportCode" resultMap="BaseResultMap">
    select *
    from qc_case_execute_result t
    where t.report_code = #{reportCode} and t.enable=true
  </select>
</mapper>