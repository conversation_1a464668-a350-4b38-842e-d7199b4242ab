<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TestcaseRelationMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TestcaseRelationEntity">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="testcase_code" property="testcaseCode" jdbcType="VARCHAR" />
        <result column="business_code" property="businessCode" jdbcType="VARCHAR" />
        <result column="domain" property="domain" jdbcType="VARCHAR"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler" />
        <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
        <result column="creator" property="creator" jdbcType="VARCHAR" />
        <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
        <result column="modifier_id" property="modifierId" jdbcType="BIGINT" />
        <result column="modifier" property="modifier" jdbcType="VARCHAR" />
        <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="TestcaseIssueVO" type="com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseIssueVO">
        <result column="code" property="code" jdbcType="VARCHAR" />
        <result column="title" property="title" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.StatusHandler" />
        <result column="handle_user_id" property="handleUserId" jdbcType="BIGINT" />
        <result column="handle_user_name" property="handleUserName" jdbcType="VARCHAR" />
    </resultMap>

    <select id="selectIssueByTestcaseCode" resultMap="TestcaseIssueVO">
        SELECT
            t2.code,
            t2.title,
            t2.status,
            t2.handle_user_id,
            t2.handle_user_name
        FROM tm_testcase_relation t1
        INNER JOIN qc_issue t2 ON t2.code = t1.business_code
        WHERE t1.testcase_code = #{code} AND t1.domain = #{domain}
        AND t2.enable = true
        ORDER BY t1.gmt_create DESC
    </select>

    <select id="getCaseNameList"  resultType="com.zto.devops.qc.client.model.issue.entity.CaseVO" >
        select distinct tt.code as caseCode,
                IFNULL(concat(tt2.name,'---', tt.name),tt.name) as caseName
        from tm_testcase_relation ttr
                 left join tm_testcase tt on tt.code = ttr.testcase_code
                 left join tm_testcase tt2 on tt2.code = tt.parent_code
        where tt.enable = 1 and ttr.business_code = #{code}
    </select>
    <select id="selectTestCaseByCodeList"
            resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseByBusinessCodeVO">
        SELECT
            t2.code,
            t2.name as title,
            t2.status,
            t2.duty_user,
            t2.duty_user_id
        FROM tm_testcase_relation t1
        INNER JOIN tm_testcase t2 ON t2.code = t1.testcase_code
        WHERE t1.domain = #{domain}
        <if test="null != codeList and codeList.size > 0">
            and t1.business_code in
            <foreach collection="codeList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.gmt_create DESC
    </select>

</mapper>
