<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.ReviewOpinionMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.ReviewOpinionEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="code" jdbcType="VARCHAR" property="code"/>
    <result column="report_code" jdbcType="VARCHAR" property="reportCode" />
    <result column="serial_id" jdbcType="VARCHAR" property="serialId" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="owner_user_id" jdbcType="BIGINT" property="ownerUserId" />
    <result column="owner_user_name" jdbcType="VARCHAR" property="ownerUserName" />
    <result column="dead_line_date" jdbcType="DATE" property="deadLineDate" />
  </resultMap>
</mapper>