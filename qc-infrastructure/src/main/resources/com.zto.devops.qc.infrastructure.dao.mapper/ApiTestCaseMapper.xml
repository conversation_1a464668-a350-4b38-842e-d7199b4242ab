<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.ApiTestCaseMapper">

    <select id="queryApiTestCasePage"
            resultType="com.zto.devops.qc.client.model.testmanager.apitest.entity.PageApiTestCaseVO">
        select
        any_value(tatc.case_code) as caseCode,
        any_value(tatc.case_name) as caseName,
        any_value(tat.api_code) as apiCode,
        any_value(tat.api_name) as apiName,
        any_value(tat.api_address) as apiAddress,
        any_value(tatc.case_type) as caseTypeNum,
        any_value(tatc.enable) as enableNum,
        any_value(tatc.latest_task_id) as taskCode,
        any_value(task.start_time) as executeTime,
        TIMESTAMPDIFF(second,min(task.start_time),max(task.finish_time)) as duration,
        any_value(tatc.latest_execute_result) as testResultStr,
        any_value(tat.doc_version) as docVersion,
        any_value(tat.api_type) as apiType,
        (select group_concat(distinct qt2.tag_name) from qc_tag qt2 where qt2.business_code = tatc.case_code) as tagStr
        from
        tm_api_test_case tatc
        left join tm_api_test tat on tatc.api_code = tat.api_code
        left join tm_automatic_task task on tatc.latest_task_id = task.code
        left join qc_tag qt on tatc.case_code = qt.business_code
        where 1=1
        and tatc.enable != false
        and tatc.product_code = #{productCode}
        <if test="apiCode != null and apiCode != ''">
            and tatc.api_code = #{apiCode}
        </if>
        <if test="caseName != null and caseName !='' ">
            and tatc.case_name like CONCAT('%',#{caseName},'%') ESCAPE '/'
        </if>
        <if test="startTime != null and endTime != null  ">
            and task.start_time between #{startTime} and #{endTime}
        </if>
        <if test="tagList != null and tagList.size() > 0">
            and qt.tag_name in
            <foreach collection="tagList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="apiTypeList != null and apiTypeList.size() > 0">
            and tat.api_type in
            <foreach collection="apiTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="typeNumList != null and typeNumList.size() > 0">
            and tatc.case_type in
            <foreach collection="typeNumList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="apiNameList != null and apiNameList.size() > 0">
            and tat.api_name in
            <foreach collection="apiNameList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="enableNumList != null and enableNumList.size() > 0">
            and tatc.enable in
            <foreach collection="enableNumList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="apiAddressList != null and apiAddressList.size() > 0">
            and tat.api_address in
            <foreach collection="apiAddressList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="testResultList != null and testResultList.size() > 0">
            and tatc.latest_execute_result in
            <foreach collection="testResultList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="docVersionList != null and docVersionList.size() > 0">
            and tat.doc_version in
            <foreach collection="docVersionList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by tatc.case_code
        order by any_value(tatc.gmt_modified) desc
    </select>
    <select id="queryApiCaseEnable"
            resultType="com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestCaseEnableVO">
        select
        tatc.case_code as caseCode,
        group_concat(tatc.enable) as enableStr,
        group_concat(tatc.status) as statusStr
        from
        tm_api_test_case tatc
        where
        tatc.enable != false
        and tatc.product_code = #{productCode}
        and case_code in
        <foreach collection="codeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="status != null and status != ''">
            and tatc.status = #{status}
        </if>
        group by tatc.case_code
    </select>

    <select id="statApiCaseExecuteResult"
            resultType="com.zto.devops.qc.client.model.testmanager.apitest.entity.StatApiCaseExecuteResultVO">
        select group_concat(distinct tter.`result`) as executeResultMerge,
        tac.parent_case_code                      as parentCode
        from tm_testcase_execute_record tter
        left join tm_api_test_case tac on tter.testcase_code = tac.case_code
        where 1 = 1
        and tter.automatic_task_code = #{taskCode}
        group by tac.parent_case_code
    </select>

    <select id="selectApiTestCaseListByCode"
            resultType="com.zto.devops.qc.client.model.testmanager.apitest.entity.CountApiTestCaseChildrenVO">
        select
        tatc.parent_case_code caseCode,
        tatc.status status,
        count(distinct tatc.case_code) countNum
        from
        tm_api_test_case tatc
        where
        1 = 1
        and tatc.product_code = #{productCode}
        and tatc.status = 'publish'
        and tatc.case_type in (2,3)
        and tatc.enable != false
        and tatc.parent_case_code in
        <foreach collection="codeSet" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by tatc.parent_case_code
    </select>
    <select id="queryApiTestCaseExecuteDetail"
            resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.ApiTestCaseExecuteDetailTiledVO">
        select
        tter.automatic_task_code as automaticTaskCode,
        tat.api_code as apiCode,
        tat.api_name as apiName ,
        tatc2.case_code as parentCode ,
        tatc2.case_name as parentName ,
        tatc.case_code as caseCode,
        tatc.case_name as caseName,
        tatc.case_type as caseTypeNum,
        tatc.enable as enable ,
        tter.`result` as `result`,
        tter.exec_log_file as execLogFile,
        tter.report_file as reportFile ,
        tter.creator_id as executorId,
        tter.creator as executor,
        tter.start_time as startTime ,
        tter.finish_time as finishTime
        from
        tm_testcase_execute_record tter
        left join tm_api_test_case tatc on tter.testcase_code = tatc.case_code
        left join tm_api_test tat on tatc.api_code = tat.api_code
        left join tm_api_test_case tatc2 on tatc.parent_case_code = tatc2.case_code
        where
        1 = 1
        and tatc.status = 'publish'
        and tatc2.status = 'publish'
        and tatc.product_code = #{productCode}
        and tatc2.product_code = #{productCode}
        and tter.automatic_task_code in
        <foreach collection="taskCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="executorIdList != null and executorIdList.size() > 0">
            AND tter.creator_id IN
            <foreach collection="executorIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="status != null and status.size() > 0">
            AND tter.result IN
            <foreach collection="status" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTimeBegin != null ">
            AND tter.start_time > #{startTimeBegin}
        </if>
        <if test="startTimeEnd != null ">
            AND tter.start_time  <![CDATA[ < ]]>  #{startTimeEnd}
        </if>
        <if test="finishTimeBegin != null ">
            AND tter.finish_time > #{finishTimeBegin}
        </if>
        <if test="finishTimeEnd != null ">
            AND tter.finish_time  <![CDATA[ < ]]>  #{finishTimeEnd}
        </if>
    </select>

    <insert id="batchInsert" parameterType="list" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tm_api_test_case (
        case_code,
        case_name,
        product_code,
        api_code,
        status,
        case_type,
        case_req_data,
        parent_case_code,
        latest_task_id,
        related_api_address,
        related_api_name,
        api_type,
        doc_version,
        creator_id,
        creator,
        gmt_create,
        modifier_id,
        modifier,
        gmt_modified,
        automatic_source_code
        )
        VALUES
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.caseCode},
            #{item.caseName},
            #{item.productCode},
            #{item.apiCode},
            #{item.status},
            #{item.caseType},
            #{item.caseReqData},
            #{item.parentCaseCode},
            #{item.latestTaskId},
            #{item.relatedApiAddress},
            #{item.relatedApiName},
            #{item.apiType},
            #{item.docVersion},
            #{item.creatorId},
            #{item.creator},
            #{item.gmtCreate},
            #{item.modifierId},
            #{item.modifier},
            #{item.gmtModified},
            #{item.automaticSourceCode}
            )
        </foreach>
    </insert>
</mapper>