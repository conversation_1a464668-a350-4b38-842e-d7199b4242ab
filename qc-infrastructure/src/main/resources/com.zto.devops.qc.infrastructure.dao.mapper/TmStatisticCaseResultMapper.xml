<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TmStatisticCaseResultMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TmStatisticCaseResultEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="code" jdbcType="VARCHAR" property="code"/>
    <result column="report_code" jdbcType="VARCHAR" property="reportCode" />
    <result column="frog_plan_code" jdbcType="VARCHAR" property="frogPlanCode" />
    <result column="frog_plan_name" jdbcType="VARCHAR" property="frogPlanName" />
    <result column="num" jdbcType="BIGINT" property="num" />
    <result column="ratio" jdbcType="DOUBLE" property="ratio" />
    <result column="result" jdbcType="VARCHAR" property="result" />
  </resultMap>

  <!--<select id="selectByReportCode" resultMap="BaseResultMap">-->
    <!--select *-->
    <!--from qc_case_execute_result t-->
    <!--where t.report_code = #{reportCode} and t.enable=true-->
  <!--</select>-->
</mapper>