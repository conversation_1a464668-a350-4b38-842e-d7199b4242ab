<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.CaseDetailMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.CaseDetailEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="code" jdbcType="VARCHAR" property="code"/>
    <result column="serial_id" jdbcType="VARCHAR" property="serialId" />
    <result column="frog_plan_code" jdbcType="VARCHAR" property="frogPlanCode" />
    <result column="report_code" jdbcType="VARCHAR" property="reportCode" />
    <result column="frog_serial_id" jdbcType="VARCHAR" property="frogSerialId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="execute_result" jdbcType="VARCHAR" property="executeResult" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.CaseExecuteResultHandler"/>
  </resultMap>

  <select id="selectByReportCode" resultMap="BaseResultMap">
    select *
    from qc_case_detail t
    where t.report_code = #{reportCode} and t.enable=true
  </select>
</mapper>