<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.AutomaticSchedulerCaseMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.AutomaticSchedulerCaseEntity">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="scheduler_code" jdbcType="VARCHAR" property="schedulerCode" />
        <result column="case_code" jdbcType="VARCHAR" property="caseCode" />
        <result column="enable" jdbcType="BIT" property="enable" />
        <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
        <result column="modifier" jdbcType="VARCHAR" property="modifier" />
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    </resultMap>

    <select id="selectAllSchedulerCasePath" resultType="com.zto.devops.qc.client.model.testmanager.scheduler.entity.SchedulerCaseVO">
        select
            tc.code,
            tc.path as casePath,
            tc.testcase_module_path AS testcaseModulePath
        from tm_automatic_scheduler_case sc
        inner join tm_testcase tc on sc.case_code = tc.code
        where sc.scheduler_code= #{schedulerCode}
    </select>

    <select id="selectSchedulerCaseModuleList" resultType="com.zto.devops.qc.client.model.testmanager.scheduler.entity.SchedulerCaseVO">
        select t1.*, t2.sort from (
        select
        tc.id,
        tc.code as caseCode,
        tc.parent_code as parentCode,
        tc.name as caseName,
        tc.attribute as caseAttribute,
        tc.type,
        tc.enable,
        null as automaticRecordType,
        tc.gmt_create
        from tm_testcase tc
        where 1=1
        and tc.product_code = #{productCode}
        and tc.type = 'AUTO'
        and tc.attribute = 'MODULE'
        and tc.automatic_source_code = ''
        union all
        select
        asr.id,
        asr.code as caseCode,
        ifnull(asr.testcase_code, '') as parentCode,
        asr.name as caseName,
        'MODULE' as caseAttribute,
        'SOURCERECORD' as type,
        asr.enable,
        asr.type as automaticRecordType,
        asr.gmt_create
        from tm_automatic_source_record asr
        where asr.product_code = #{productCode}
        ) t1
        left join tm_testcase_sort t2 on t2.code = t1.caseCode
        ORDER BY ifnull(t2.sort,99999), t1.gmt_create, t1.id
    </select>

    <select id="selectSchedulerCaseList" resultType="com.zto.devops.qc.client.model.testmanager.scheduler.entity.SchedulerCaseVO"
            parameterType="com.zto.devops.qc.client.model.testmanager.scheduler.query.ListSchedulerCaseQuery">
        select
        tc.id,
        sc.scheduler_code as schedulerCode,
        sc.case_code as caseCode,
        tc.status as testcaseStatus,
        tc.priority,
        if(tc.parent_code != '' , tc.parent_code, 'NO_GROUP') as parentCode,
        tc.name as caseName,
        tc.type,
        tc.path as casePath,
        tc.attribute as caseAttribute,
        tc.node_type as nodeType,
        sc.enable,
        tc.testcase_module_path as testcaseModulePath,
        tc.sort,
        tc.interface_name as interfaceName
        from tm_automatic_scheduler_case sc
        left join tm_testcase tc on sc.case_code=tc.code
        left join tm_automatic_source_record asr on asr.code = tc.automatic_source_code
        left join tm_testcase_execute_record r on r.testcase_code=sc.case_code and r.automatic_task_code in (
        select code from tm_automatic_task where task_id = (
        SELECT task_id FROM lbd_qc.tm_automatic_task
        where scheduler_code= #{schedulerCode} order by id desc limit 1
        ))
        where 1=1
        and sc.scheduler_code= #{schedulerCode}
        and tc.attribute='TESTCASE'
        <if test="parentCode != null and parentCode !='' ">
            and (tc.path like CONCAT('%',#{parentCode},'%') or tc.testcase_module_path like CONCAT('%',#{parentCode},'%'))
        </if>
        <if test="search != null and search !='' ">
            and (
            tc.code like CONCAT('%',#{search},'%')
            or tc.name like CONCAT('%',#{search},'%') ESCAPE '/'
            or tc.interface_name like CONCAT('%',#{search},'%')
            )
        </if>
        <if test="caseCodeList != null and caseCodeList.size() > 0">
            and sc.case_code in
            <foreach collection="caseCodeList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="priorityList != null and priorityList.size() > 0">
            and tc.priority in
            <foreach collection="priorityList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="nodeTypeList != null and nodeTypeList.size() > 0">
            and tc.node_type in
            <foreach collection="nodeTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="automaticTypeList != null and automaticTypeList.size() > 0">
            and asr.type in
            <foreach collection="automaticTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and r.result in
            <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by tc.id
    </select>

    <select id="selectSchedulerCaseByParentCodeList" resultType="com.zto.devops.qc.client.model.testmanager.scheduler.entity.SchedulerCaseVO"
            parameterType="com.zto.devops.qc.client.model.testmanager.scheduler.query.ListSchedulerCaseQuery">
        select
        tc.id,
        sc.scheduler_code as schedulerCode,
        sc.case_code as caseCode,
        tc.status as testcaseStatus,
        tc.priority,
        if(tc.parent_code != '' , tc.parent_code, 'NO_GROUP') as parentCode,
        tc.name as caseName,
        tc.type,
        tc.path as casePath,
        tc.attribute as caseAttribute,
        tc.node_type as nodeType,
        sc.enable,
        tc.testcase_module_path as testcaseModulePath,
        tc.sort,
        tc.interface_name as interfaceName,
        r.result
        from tm_automatic_scheduler_case sc
        left join tm_testcase tc on sc.case_code=tc.code
        left join tm_testcase_execute_record r on r.testcase_code=sc.case_code and r.automatic_task_code in (
        select code from tm_automatic_task where task_id = (
        SELECT task_id FROM lbd_qc.tm_automatic_task
        where scheduler_code= #{schedulerCode} order by id desc limit 1
        ))
        where 1=1
        and sc.scheduler_code= #{schedulerCode}
        and tc.attribute='TESTCASE'
        <if test="parentCode != null and parentCode !='' ">
            and (tc.parent_code = #{parentCode} or (tc.parent_code = '' and tc.automatic_source_code = #{parentCode}))
        </if>
        <if test="search != null and search !='' ">
            and (
            tc.code like CONCAT('%',#{search},'%')
            or tc.name like CONCAT('%',#{search},'%') ESCAPE '/'
            or tc.interface_name like CONCAT('%',#{search},'%')
            )
        </if>
        <if test="caseCodeList != null and caseCodeList.size() > 0">
            and sc.case_code in
            <foreach collection="caseCodeList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="priorityList != null and priorityList.size() > 0">
            and tc.priority in
            <foreach collection="priorityList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="nodeTypeList != null and nodeTypeList.size() > 0">
            and tc.node_type in
            <foreach collection="nodeTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="automaticTypeList != null and automaticTypeList.size() > 0">
            and asr.type in
            <foreach collection="automaticTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and r.result in
            <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by tc.id
    </select>

    <select id="selectSchedulerCaseAutomaticModuleList" resultType="com.zto.devops.qc.client.model.testmanager.scheduler.entity.SchedulerCaseVO">
        select
        tc.id as id,
        tc.code as caseCode,
        case
        when tc.parent_code = '' then tc.automatic_source_code
        else tc.parent_code
        end as parentCode,
        tc.name as caseName,
        tc.attribute as caseAttribute,
        tc.type,
        tc.enable,
        tc.node_type as nodeType,
        tc.priority as priority,
        tc.status as testcaseStatus
        from tm_testcase tc
        where tc.product_code = #{productCode}
        and tc.type = 'AUTO'
        and tc.attribute = 'MODULE'
        and tc.automatic_source_code != ''
    </select>

    <select id="selectNotIn" resultType="java.lang.String">
        select
        tt.code
        from
        tm_testcase tt
        left join (select * from tm_automatic_scheduler_case where scheduler_code = #{schedulerCode})
        tasc on tt.code = tasc.case_code
        where
        1 = 1
        and tasc.id is null
        and tt.product_code =#{productCode}
        and tt.`type` = 'AUTO'
        and tt.`attribute` = 'TESTCASE'
        and tt.set_core = false
        and tt.enable = true
        <if test="list != null and list.size()>0">
            and tt.code in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="insertBatch">
        insert into
        tm_automatic_scheduler_case (
        scheduler_code,
        case_code,
        enable,
        creator_id,
        creator,
        modifier_id,
        modifier,
        gmt_create,
        gmt_modified
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.schedulerCode}, #{item.caseCode},#{item.enable},
            #{item.creatorId}, #{item.creator},
            #{item.modifierId}, #{item.modifier},
            #{item.gmtCreate}, #{item.gmtModified}
            )
        </foreach>
    </insert>

    <select id="selectTestcaseCodeList" resultType="java.lang.String">
        select
        sc.case_code as code
        from tm_automatic_scheduler_case sc
        left join tm_testcase tc on sc.case_code = tc.code
        left join tm_automatic_source_record asr on asr.code = tc.automatic_source_code
        where sc.scheduler_code = #{schedulerCode} and tc.attribute='TESTCASE'
        <if test="parentCode != null and parentCode !=''">
            and (tc.path like CONCAT('%',#{parentCode},'%') or tc.testcase_module_path like CONCAT('%',#{parentCode},'%'))
        </if>
        <if test="parentCode == ''">
            and tc.parent_code = ''
        </if>
        <if test="search != null and search !='' ">
            and (
            tc.code like CONCAT('%',#{search},'%')
            or tc.name like CONCAT('%',#{search},'%')
            or tc.interface_name like CONCAT('%',#{search},'%')
            )
        </if>
        <if test="priorityList != null and priorityList.size() > 0">
            and tc.priority in
            <foreach collection="priorityList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="nodeTypeList != null and nodeTypeList.size() > 0">
            and tc.node_type in
            <foreach collection="nodeTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="automaticTypeList != null and automaticTypeList.size() > 0">
            and asr.type in
            <foreach collection="automaticTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>