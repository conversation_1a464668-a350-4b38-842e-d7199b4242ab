<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TestcaseStepMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TestcaseStepEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="testcase_code" jdbcType="VARCHAR" property="testcaseCode" />
    <result column="step_desc" jdbcType="VARCHAR" property="stepDesc" />
    <result column="expect_result" jdbcType="VARCHAR" property="expectResult" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, testcase_code, step_desc, expect_result, sort, creator_id, creator, gmt_create,
    modifier_id, modifier, gmt_modified
  </sql>

  <insert id="saveBatch" parameterType="list" keyProperty="id" useGeneratedKeys="true">
    INSERT INTO tm_testcase_step (
    testcase_code,
    step_desc,
    expect_result,
    sort,
    creator_id,
    creator,
    modifier_id,
    modifier)
    VALUES
    <foreach collection="list" separator="," item="item" index="index">
      (
        #{item.testcaseCode},
        #{item.stepDesc},
        #{item.expectResult},
        #{item.sort},
        #{item.creatorId},
        #{item.creator},
        #{item.modifierId},
        #{item.modifier}
      )
    </foreach>
  </insert>
</mapper>
