<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.SceneDatabaseAuthorizeMapper">

    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.SceneDatabaseAuthorizeEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="authorize_code" jdbcType="VARCHAR" property="authorizeCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="authorize_product_code" jdbcType="VARCHAR" property="authorizeProductCode"/>
        <result column="authorize_product_name" jdbcType="VARCHAR" property="authorizeProductName"/>
        <result column="db_id" jdbcType="BIGINT" property="dbId"/>
        <result column="db_name" jdbcType="VARCHAR" property="dbName"/>
        <result column="physic_db_name" jdbcType="VARCHAR" property="physicDbName"/>
        <result column="enable" jdbcType="BIT" property="enable"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
    </resultMap>

    <select id="queryAuthorizeList" resultMap="BaseResultMap">
        select authorize_product_code, authorize_product_name, group_concat(distinct db_name) as dbName
        from tm_scene_database_authorize
        where product_code = #{productCode}
        and enable = 1
        group by authorize_product_code, authorize_product_name
    </select>
</mapper>
