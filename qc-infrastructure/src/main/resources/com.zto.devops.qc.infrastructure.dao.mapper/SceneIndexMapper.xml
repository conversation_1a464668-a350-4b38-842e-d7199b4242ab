<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.SceneIndexMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.SceneIndexEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="scene_index_code" jdbcType="VARCHAR" property="sceneIndexCode"/>
        <result column="scene_index_name" jdbcType="VARCHAR" property="sceneIndexName"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="parent_code" jdbcType="VARCHAR" property="parentCode"/>
        <result column="scene_index_type" jdbcType="TINYINT" property="sceneIndexType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.SceneIndexTypeHandler"/>
        <result column="testcase_code" jdbcType="VARCHAR" property="testcaseCode"/>
        <result column="enable" jdbcType="BIT" property="enable"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <resultMap id="SceneIndexVOMap" type="com.zto.devops.qc.client.model.testmanager.apitest.entity.SceneIndexVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="scene_index_code" jdbcType="VARCHAR" property="sceneIndexCode"/>
        <result column="scene_index_name" jdbcType="VARCHAR" property="sceneIndexName"/>
        <result column="scene_info_desc" jdbcType="VARCHAR" property="sceneInfoDesc"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="parent_code" jdbcType="VARCHAR" property="parentCode"/>
        <result column="scene_index_type" jdbcType="TINYINT" property="sceneIndexType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.SceneIndexTypeHandler"/>
        <result column="testcase_code" jdbcType="VARCHAR" property="testcaseCode"/>
        <result column="enable" jdbcType="BIT" property="sceneEnable"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="scene_info_enable" jdbcType="TINYINT" property="sceneInfoEnable"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.SceneInfoEnableTypeHandler"/>
        <result column="automatic_source_code" jdbcType="VARCHAR" property="automaticSourceCode"/>
        <result column="share_status" jdbcType="TINYINT" property="shareStatus"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , scene_index_code, scene_index_name, product_code, parent_code, scene_index_type,
        enable, creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified
    </sql>

    <insert id="batchInsert">
        insert into tm_scene_index (
        scene_index_code, scene_index_name, product_code, parent_code, scene_index_type,
        enable, creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified
        ) values
        <foreach collection="list" item="item" separator=",">
            (#{item.sceneIndexCode}, #{item.sceneIndexName}, #{item.productCode}, #{item.parentCode},
            #{item.sceneIndexType, typeHandler=com.zto.devops.qc.infrastructure.dao.typehandler.SceneIndexTypeHandler},
            #{item.enable},#{item.creatorId}, #{item.creator}, #{item.gmtCreate}, #{item.modifierId},
            #{item.modifier},#{item.gmtModified})
        </foreach>
    </insert>

    <select id="queryAllSceneModule" resultMap="SceneIndexVOMap">
        select t.id,t.product_code, t.scene_index_code, t.scene_index_type, t.parent_code,
        (case t.scene_index_type when 2 then MAX(ts.scene_name) else t.scene_index_name end) as sceneIndexName,
        (case t.scene_index_type when 2 then MAX(ts.enable) else null end) as sceneEnable,
        (case t.scene_index_type when 2 then MAX(ts.scene_info_desc) else null end) as sceneInfoDesc,
        (case t.scene_index_type when 2 then MAX(ts.gmt_create) else t.gmt_create end) as gmtCreate,
        (case t.scene_index_type when 2 then MAX(ts.creator_id) else t.creator_id end) as creatorId,
        (case t.scene_index_type when 2 then MAX(ts.gmt_modified) else t.gmt_modified end) as gmtModified,
        (case t.scene_index_type when 2 then MAX(ts.share_status) else 0 end) as shareStatus
        from tm_scene_index t
        left join tm_scene_info ts on t.scene_index_code = ts.scene_code
        where 1 = 1
        <if test="shareProductFlag == null or shareProductFlag == false">
            AND t.product_code = #{productCode}
        </if>
        <if test="onlyModule != null and onlyModule == true">
            and t.scene_index_type = 1
        </if>
        <if test="onlyModule != null and onlyModule == true">
            and t.scene_index_type = 1
        </if>
        <if test="shareStatus != null and shareStatus == true">
            and ts.share_status = #{shareStatus}
        </if>
        <if test="status != null and status.size() > 0">
            and ( ts.enable in
            <foreach collection="status" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
            or ts.enable is null )
        </if>
        <choose>
            <when test="sceneType !=null and sceneType !='' and sceneType == 'CREATE' ">
                and t.scene_type = 2
            </when>
            <otherwise>
                and t.scene_type = 1
            </otherwise>
        </choose>
        and t.enable != 0
        group by t.scene_index_code
    </select>

    <select id="queryModuleByParentCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tm_scene_index
        where scene_index_type = 1
        and enable = 1
        and parent_code = #{parentCode}
    </select>

    <select id="selectSceneIndexModuleInfo" resultMap="SceneIndexVOMap">
        select t1.scene_index_code as                                                   parent_code,
               t2.scene_index_code,
               t2.scene_index_type,
               (select enable from tm_scene_info where scene_code = t2.scene_index_code limit 1) as scene_info_enable
        from tm_scene_index t1
            left join tm_scene_index t2
        on t2.parent_code = t1.scene_index_code and t2.enable = 1
        where t1.product_code = #{productCode} and t1.enable = 1 and t1.scene_index_type = 1
    </select>

    <select id="selectSimpleSceneIndex" resultMap="SceneIndexVOMap">
        select
        scene_index_code, scene_index_name, parent_code, product_code, scene_index_type,
        (select automatic_source_code from tm_scene_info
        where scene_code = t.scene_index_code and status = 'publish' and enable in (2, 3) limit 1
        ) as automatic_source_code
        from tm_scene_index t
        where enable = 1
        <if test="productCode != null and productCode != ''">
            and product_code = #{productCode}
        </if>
    </select>

    <select id="selectSharedPreData" resultMap="SceneIndexVOMap">
        select
            t2.scene_index_code,
            max(t1.scene_name) as scene_index_name,
            t2.product_code,
            case t2.parent_code when 'ALL' then t2.product_code else t2.parent_code end as parent_code,
            t2.scene_index_type
        from tm_scene_info t1
        left join tm_scene_index t2 on t2.scene_index_code = t1.scene_code
        where t1.scene_type = 2 and t1.enable = 2 and t1.share_status = 1
        <if test="productCode != null and productCode != ''">
            <if test="shareProductFlag == null or shareProductFlag == false">
                AND t1.product_code = #{productCode}
            </if>
        </if>
        group by t1.scene_code
        order by MAX(t1.gmt_modified) desc
    </select>

    <select id="selectSharedPreDataModuleByProduct" resultMap="SceneIndexVOMap">
        select
            t.scene_index_code,
            t.scene_index_name,
            t.product_code,
            case t.parent_code when 'ALL' then t.product_code else t.parent_code end as parent_code,
            t.scene_index_type
        from tm_scene_index t
        where t.scene_type = 2 and t.enable = 1 and t.scene_index_type = 1
        and t.product_code in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by t.gmt_modified desc
    </select>
</mapper>
