<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.QcNoticeResultMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.QcNoticeResultEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_avatar" jdbcType="VARCHAR" property="userAvatar" />
    <result column="user_station" jdbcType="VARCHAR" property="userStation" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="notice_type" jdbcType="VARCHAR" property="noticeType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="fail_send_reson" jdbcType="VARCHAR" property="failSendReson" />
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="email" jdbcType="VARCHAR" property="email" />
  </resultMap>
</mapper>