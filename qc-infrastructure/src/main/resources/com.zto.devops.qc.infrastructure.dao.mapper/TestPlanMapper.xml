<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TestPlanMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TestPlanEntity">
        <id column="code" jdbcType="VARCHAR" property="code"/>
        <result column="plan_name" jdbcType="VARCHAR" property="planName"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_source" jdbcType="VARCHAR" property="productSource"/>
        <result column="type" jdbcType="VARCHAR" property="type"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestPlanTypeHandler"/>
        <result column="plan_code" jdbcType="VARCHAR" property="planCode"/>
        <result column="test_director_id" jdbcType="BIGINT" property="testDirectorId"/>
        <result column="test_director_name" jdbcType="VARCHAR" property="testDirectorName"/>
        <result column="product_director_id" jdbcType="BIGINT" property="productDirectorId" />
        <result column="product_director_name" jdbcType="VARCHAR" property="productDirectorName" />
        <result column="edit_no" jdbcType="INTEGER" property="editNo"/>
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestPlanStatusHandler"/>
        <result column="preview" jdbcType="LONGVARCHAR" property="preview"/>
        <result column="dept_id" jdbcType="BIGINT" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="json" jdbcType="LONGVARCHAR" property="json"/>
        <result column="version_code" jdbcType="VARCHAR" property="versionCode" />
        <result column="version_name" jdbcType="VARCHAR" property="versionName" />
    </resultMap>
    <!-- !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! -->
    <!-- test_plan 的to_test_date 提测日期 approval_out_date  准出日期      已废弃 未删除  -->

    <sql id="Base_Column_List">
        id, code, plan_name, product_code, product_name, type, plan_code, test_director_id,
    test_director_name, creator_id, creator, gmt_create,
    modifier_id, modifier, gmt_modified, enable, edit_no, status, dept_id, dept_name,
    version_code, version_name, product_director_id, product_director_name, product_source,
       preview, json
    </sql>


    <select id="selectTestPlanList" resultType="com.zto.devops.qc.infrastructure.dao.entity.TestPlanEntity"
            parameterType="com.zto.devops.qc.client.model.testPlan.query.TestPlanListQuery">
        select q.id,q.code,q.plan_name,q.product_code,q.product_name,q.type,q.plan_code,q.test_director_id,
        q.test_director_name,q.creator_id,q.creator,q.gmt_create,
        q.modifier_id,q.modifier,q.gmt_modified,q.enable,q.edit_no,q.status,q.dept_id,q.dept_name,
        q.version_code,q.version_name,q.product_director_id,q.product_director_name,q.product_source,
        q.preview,q.json
        from
        qc_test_plan q left join qc_test_plan_main t on q.code = t.test_plan_code
        where q.enable = true
        <if test="title!=null and title!=''">
            and (q.plan_name like CONCAT('%',#{title},'%') or q.code = #{title})
        </if>
        <if test="productCode!=null and productCode!=''">
            and q.product_code = #{productCode}
        </if>
        <if test="versionCode!=null and versionCode!=''">
            and q.version_code = #{versionCode}
        </if>
        <if test="types!=null">
            and q.type in
            <foreach collection="types" item="item" open="(" separator=","
              close=")">
                #{item}
            </foreach>
        </if>
        <if test="productCodes!=null">
            and q.product_code in
            <foreach collection="productCodes" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="testDirectorId!=null  and testDirectorId!=''">
            and q.test_director_id = #{testDirectorId}
        </if>
        <if test="createTimeStart != null ">
            and q.gmt_create > #{createTimeStart}
        </if>
        <if test="createTimeEnd != null ">
            and q.gmt_create  <![CDATA[ < ]]>  #{createTimeEnd}
        </if>
        <if test="status != null  and status!=''">
            and q.status =#{status}
        </if>
        <if test="status == 'DRAFT' ">
            and q.creator_id = #{transactor.userId}
        </if>
        <if test="creatorId != null ">
            and q.creator_id = #{creatorId}
        </if>

        <choose>
            <when test="orderField!=null and orderField!=''">
                order by #{orderField} #{orderType}, q.id desc
            </when>
            <otherwise>
                order by q.id desc
            </otherwise>
        </choose>
    </select>
    <select id="versionPlanQuery" resultType="com.zto.devops.qc.client.model.testPlan.query.VersionPlanVO">
        select
        distinct tp.code,
        tp.plan_name as planName,
        tp.type,
        tp.product_code as productCode,
        tp.version_code as versionCode,
        tp.test_director_id as testDirectorId,
        tp.test_director_name as testDirectorName,
        tp.gmt_create as gmtCreate,
        tp.gmt_modified as gmtModified,
        tp.creator as creator,
        tp.creator_id as creatorId
        from qc_test_plan tp
        where tp.enable = true and tp.status = 'NORMAL'
        <if test="versionCode!=null and versionCode!=''">
            and tp.version_code=#{versionCode}
        </if>
        order by tp.gmt_create desc
    </select>

    <select id="getSimplePlanVOByQuery" resultType="com.zto.devops.qc.client.model.report.entity.RelatedBaseVO">
        SELECT tp.code,
               tp.plan_name as name,
               tp.type,
               tp.version_code as versionCode,
               tp.product_code as productCode
        FROM qc_test_plan tp
        WHERE tp.enable = true
        AND tp.status = 'NORMAL'
          AND tp.version_code = #{versionCode}
        <if test="planType !=null and planType!=''">
            and tp.type!=#{planType}
        </if>
    </select>

    <select id="getTestPlanWithoutSafePlan" resultType="com.zto.devops.qc.client.model.report.entity.SafeTestPlanMsgVO">
        SELECT tp.code      as planCode,
               tp.plan_name as planName,
               tp.test_director_id as userId
        FROM qc_test_plan tp
        inner JOIN qc_test_plan_main pl ON tp.code = pl.test_plan_code
        WHERE tp.enable = true
          AND tp.status = 'NORMAL'
          AND tp.type = 'TEST_PLAN'
          AND pl.approval_exit_date <![CDATA[ < ]]> #{approvalExitDate}
          AND tp.code not in
        (SELECT tp.plan_code
         FROM qc_test_plan tp
         WHERE tp.enable = true
           AND tp.status = 'NORMAL'
           AND tp.type = 'SAFETY_TEST')
    </select>

    <select id="getSimplePlanVOByVersionCode" resultType="com.zto.devops.qc.client.model.report.entity.RelatedBaseVO">
        SELECT tp.code,
               tp.plan_name    as name,
               tp.type,
               pl.version_code as versionCode
        FROM qc_test_plan tp
                 LEFT JOIN qc_test_plan_main pl ON tp.code = pl.test_plan_code
        WHERE tp.enable = true
          and tp.status = 'NORMAL'
          AND pl.version_code = #{versionCode}
    </select>

    <select id="selectByCode" resultMap="BaseResultMap">
        SELECT *
        FROM qc_test_plan t
        where t.code = #{planCode}

    </select>


    <select id="selectCommonListByReq"
            resultType="com.zto.devops.qc.client.model.testmanager.email.entity.TestPlanDetailVO">
        select
        qtp.code as planCode,
        qtp.plan_name as planName,
        qtp.type as planType,
        qtp.product_code as productCode,
        qtp.product_name as productName,
        qtp.version_code as versionCode,
        qtp.version_name as versionName,
        qtpm.presentation_date as planPresentationDate,
        qtpm.approval_exit_date as planApprovalExitDate,
        qtp.preview as preview,
        qtp.gmt_create as gmtCreate,
        qtp.gmt_modified as gmtModified,
        qtp.creator_id as creatorId,
        qtp.creator as creator,
        qtp.modifier_id as modifierId,
        qtp.modifier as modifier
        from
        qc_test_plan qtp
        left join qc_test_plan_main qtpm on qtp.code = qtpm.test_plan_code
        where
        1=1
        and qtp.preview != ''
        and qtp.enable = 1
        and qtpm.test_plan_code != ''
        and qtp.`type` ='TEST_PLAN'
        <if test="begin != null and end != null">
            and qtp.gmt_create BETWEEN #{begin} and #{end}
        </if>
        order by qtp.gmt_create
    </select>

    <select id="selectOtherListByReq"
            resultType="com.zto.devops.qc.client.model.testmanager.email.entity.TestPlanDetailVO">
        select
        qtp.code as planCode,
        qtp.plan_name as planName,
        qtp.type as planType,
        qtp.product_code as productCode,
        qtp.product_name as productName,
        qtp.version_code as versionCode,
        qtp.version_name as versionName,
        qtpm.presentation_date as planPresentationDate,
        qtpm.approval_exit_date as planApprovalExitDate,
        qtp.preview as preview,
        qtp.gmt_create as gmtCreate,
        qtp.gmt_modified as gmtModified,
        qtp.creator_id as creatorId,
        qtp.creator as creator,
        qtp.modifier_id as modifierId,
        qtp.modifier as modifier
        from
        qc_test_plan qtp
        left join qc_test_plan_main qtpm on qtp.plan_code = qtpm.test_plan_code
        where
        1=1
        and qtp.preview != ''
        and qtp.enable = 1
        and qtpm.test_plan_code != ''
        and qtp.`type` !='TEST_PLAN'
        <if test="begin != null and end != null">
            and qtp.gmt_create BETWEEN #{begin} and #{end}
        </if>
        order by qtp.gmt_create
    </select>
</mapper>