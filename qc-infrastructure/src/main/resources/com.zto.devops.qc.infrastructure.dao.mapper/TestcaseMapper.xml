<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TestcaseMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TestcaseEntity">
        <id column="code" jdbcType="VARCHAR" property="code" />
        <result column="product_code" jdbcType="VARCHAR" property="productCode" />
        <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="attribute" jdbcType="VARCHAR" property="attribute"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcaseAttributeTypeHandler" />
        <result column="type" jdbcType="VARCHAR" property="type"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcaseTypeTypeHandler" />
        <result column="priority" jdbcType="VARCHAR" property="priority"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcasePriorityTypeHandler" />
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcaseStatusTypeHandler"/>
        <result column="precondition" jdbcType="VARCHAR" property="precondition" />
        <result column="duty_user_id" jdbcType="BIGINT" property="dutyUserId" />
        <result column="duty_user" jdbcType="VARCHAR" property="dutyUser" />
        <result column="comment" jdbcType="VARCHAR" property="comment" />
        <result column="abandon_reason" jdbcType="VARCHAR" property="abandonReason"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestcaseAbandonReasonTypeHandler"/>
        <result column="automatic_source_code" jdbcType="VARCHAR" property="automaticSourceCode" />
        <result column="node_type" jdbcType="VARCHAR" property="nodeType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticNodeTypeHandler" />
        <result column="sort" jdbcType="INTEGER" property="sort" />
        <result column="layer" jdbcType="INTEGER" property="layer" />
        <result column="path" jdbcType="VARCHAR" property="path" />
        <result column="enable" jdbcType="BIT" property="enable" />
        <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
        <result column="modifier" jdbcType="VARCHAR" property="modifier" />
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
        <result column="node_type_path" jdbcType="VARCHAR" property="nodeTypePath" />
        <result column="testcase_module_path" jdbcType="VARCHAR" property="testcaseModulePath" />
        <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, code, product_code, parent_code, name, attribute, type, priority, status, precondition,
        duty_user_id, duty_user, comment, automatic_source_code, node_type, sort, layer, path,
        enable, creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified, node_type_path,
        testcase_module_path, interface_name
    </sql>

    <insert id="saveBatch" parameterType="list" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tm_testcase (
        code,
        product_code,
        parent_code,
        name,
        attribute,
        type,
        priority,
        status,
        precondition,
        duty_user_id,
        duty_user,
        comment,
        sort,
        path,
        creator_id,
        creator,
        modifier_id,
        modifier,
        automatic_source_code,
        version_code,
        set_core)
        VALUES
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.code},
            #{item.productCode},
            #{item.parentCode},
            #{item.name},
            #{item.attribute},
            #{item.type},
            #{item.priority},
            #{item.status},
            #{item.precondition},
            #{item.dutyUserId},
            #{item.dutyUser},
            #{item.comment},
            #{item.sort},
            #{item.path},
            #{item.creatorId},
            #{item.creator},
            #{item.modifierId},
            #{item.modifier},
            #{item.automaticSourceCode},
            #{item.versionCode},
            #{item.setCore})
        </foreach>
    </insert>

    <select id="selectTestCaseModuleList" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.ListTestcaseVO">
        select t1.*, t2.sort from (
        select
        tc.code,
        tc.product_code productCode,
        tc.parent_code parentCode,
        tc.name,
        tc.attribute,
        tc.type,
        tc.status,
        tc.automatic_source_code automaticSourceCode,
        case tc.node_type when '' then null else tc.node_type end as nodeType,
        tc.creator_id creatorId,
        tc.creator,
        tc.gmt_create gmtCreate,
        tc.modifier_id modifierId,
        tc.modifier,
        tc.gmt_modified gmtModified,
        tc.id,
        tc.path,
        tc.version_code versionCode,
        tc.set_core setCore,
        null automaticSourceType,
        #{sceneTopModuleCode} sceneTopModuleCode,
        tc.interface_name interfaceName
        from tm_testcase tc
        where tc.enable = 1 and tc.product_code=#{productCode} and tc.type=#{type} and tc.attribute='MODULE'
        <if test="planCaseVersionCodeList != null and planCaseVersionCodeList.size() > 0">
            and tc.version_code in
            <foreach collection="planCaseVersionCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="versionCode != null and versionCode != '' ">
            and tc.version_code=#{versionCode}
        </if>
        <if test="setCore != null">
            and tc.set_core=#{setCore}
        </if>
        <if test="parentCode != null">
            and tc.parent_code=#{parentCode}
        </if>
        <if test="planPattern != null and planPattern == true">
            and tc.status = 'NORMAL'
        </if>
        <if test="automaticSourceCode != null">
            and tc.automatic_source_code = #{automaticSourceCode}
        </if>
        <if test="type.name() == 'AUTO' and (automaticSourceCode == null or automaticSourceCode == '')">
            union all
            select
            asr.code,
            asr.product_code productCode,
            asr.testcase_code parentCode,
            asr.name,
            'MODULE' attribute,
            'SOURCERECORD' type,
            null status,
            asr.code automaticSourceCode,
            null nodeType,
            asr.creator_id creatorId,
            asr.creator,
            asr.gmt_create gmtCreate,
            asr.modifier_id modifierId,
            asr.modifier,
            asr.gmt_modified gmtModified,
            asr.id,
            null path,
            null versionCode,
            null setCore,
            asr.type automaticSourceType,
            '' sceneTopModuleCode,
            '' interfaceName
            from tm_automatic_source_record asr
            where asr.enable = 1 and asr.product_code = #{productCode}
            <choose>
                <when test="parentCode != null and parentCode != ''">
                    and asr.testcase_code = #{parentCode}
                </when>
                <otherwise>
                    and (asr.testcase_code = '' or asr.testcase_code is null)
                </otherwise>
            </choose>
            <if test="automaticTypeList != null and automaticTypeList.size() > 0">
                and asr.type in
                <foreach collection="automaticTypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>
        ) t1
        left join tm_testcase_sort t2 on t2.code = t1.code
        <if test="type.name() == 'AUTO' and factoryPattern != true">
            where t1.code not in (
                select code from tm_testcase where 1=1 and name = '草稿' and enable = 1 and parent_code = (
                    select code from tm_testcase where product_code=#{productCode} and attribute = 'MODULE'
                    and type = 'AUTO' and name = '用例工厂' and parent_code = '' and enable = 1
                )
            )
        </if>
        <choose>
            <when test="automaticSourceCode != null and automaticSourceCode != ''">
                order by t1.id
            </when>
            <otherwise>
                <if test="type.name() == 'MANUAL'">
                    order by ifnull(t2.sort,99999), t1.gmtCreate, t1.id
                </if>
                <if test="type.name() == 'AUTO'">
                    order by ifnull(t2.sort,99999), t1.gmtCreate desc, t1.id desc
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="selectAllTestCasePath" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.SimpleTestcaseVO">
        select
            tc1.code,
            tc1.path,
            tc1.testcase_module_path AS testcaseModulePath
        from tm_testcase tc1
        left join tm_automatic_source_record asr on asr.code = tc1.automatic_source_code
        where tc1.enable = 1
        and tc1.product_code=#{productCode}
        and tc1.type=#{type}
        and tc1.attribute='TESTCASE'
        <if test="type.name() == 'AUTO' and factoryPattern != true">
            and asr.code not in (select automatic_source_code from tm_scene_info where status = 'edit')
        </if>
        <if test="automaticSourceCode != null and automaticSourceCode != '' ">
            and asr.code = #{automaticSourceCode}
        </if>
        <if test="setCore != null">
            and tc1.set_core=#{setCore}
        </if>
        <if test="planCaseVersionCodeList != null and planCaseVersionCodeList.size() > 0">
            and tc1.version_code in
            <foreach collection="planCaseVersionCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="versionCode != null and versionCode != '' ">
            and tc1.version_code=#{versionCode}
        </if>
        <if test="codeOrTitle != null and codeOrTitle !=''">
            and (
                tc1.code like CONCAT('%',#{codeOrTitle},'%')
                or tc1.name like CONCAT('%',#{codeOrTitle},'%') ESCAPE '/'
                or tc1.interface_name like CONCAT('%',#{codeOrTitle},'%')
            )
        </if>
        <if test="priorityList != null and priorityList.size()>0">
            and tc1.priority in
            <foreach collection="priorityList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size()>0">
            and tc1.status in
            <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="tagList != null and tagList.size > 0">
            and tc1.code in (
                select t.business_code from qc_tag t where t.enable=1 and t.domain = 'TESTCASE'
                and t.tag_name in
                <foreach collection="tagList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            )
        </if>
        <if test="dutyUserList != null and dutyUserList.size()>0">
            and tc1.duty_user_id in
            <foreach collection="dutyUserList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="creatorList != null and creatorList.size()>0">
            and tc1.creator_id in
            <foreach collection="creatorList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="isHeartList != null and isHeartList.size()>0">
            and tc1.set_heart in
            <foreach collection="isHeartList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="createTimeBegin != null">
            and tc1.gmt_create <![CDATA[ >= ]]> #{createTimeBegin}
        </if>
        <if test="createTimeEnd != null">
            and tc1.gmt_create <![CDATA[ <= ]]> #{createTimeEnd}
        </if>
        <if test="modifyTimeBegin != null">
            and tc1.gmt_modified <![CDATA[ >= ]]> #{modifyTimeBegin}
        </if>
        <if test="modifyTimeEnd != null">
            and tc1.gmt_modified <![CDATA[ <= ]]> #{modifyTimeEnd}
        </if>
        <if test="nodeTypeList != null and nodeTypeList.size()>0">
            and tc1.node_type in
            <foreach collection="nodeTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="planPattern != null and planPattern == true">
            and tc1.status = 'NORMAL' and tc1.code not in (
                select pc1.case_code from tm_test_plan_case pc1
                where pc1.enable = 1 and pc1.plan_code = #{planCode}
                and pc1.test_stage =#{testStage} and pc1.case_type = #{type}
            )
        </if>
        <if test="planPattern == null and planCode != null and planCode != ''">
            and tc1.code in (
                select pc1.case_code from tm_test_plan_case pc1
                where pc1.enable = 1 and pc1.plan_code = #{planCode}
                and pc1.test_stage =#{testStage} and pc1.case_type = #{type}
            )
        </if>
        <if test="automaticTypeList != null and automaticTypeList.size() > 0">
            and asr.type in
            <foreach collection="automaticTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="pageTestcase" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.ListTestcaseVO">
        select
            tc.id as id,
            tc.version_code versionCode,
            tc.set_core setCore,
            tc.code,
            tc.product_code productCode,
            if(tc.parent_code != '' , tc.parent_code, 'NO_GROUP') parentCode,
            tc.name,
            tc.attribute,
            tc.type,
            tc.status,
            tc.priority,
            tc.precondition,
            tc.duty_user_id dutyUserId,
            tc.duty_user dutyUser,
            tc.comment,
            tc.automatic_source_code automaticSourceCode,
            tc.layer,
            tc.path,
            tc.creator_id creatorId,
            tc.creator,
            tc.gmt_create gmtCreate,
            tc.modifier_id modifierId,
            tc.modifier,
            tc.gmt_modified gmtModified,
            tc.set_heart setHeart,
            tc.interface_name interfaceName,
            case tc.node_type when '' then NULL else tc.node_type end as nodeType,
            (select count(*) from tm_testcase_execute_record tcr where tcr.testcase_code=tc.code) executeNum
        from tm_testcase tc
        left join tm_automatic_source_record asr on asr.code = tc.automatic_source_code
        where tc.enable = 1 and tc.product_code=#{productCode} and tc.type=#{type} and tc.attribute='TESTCASE'
        <if test="setCore != null">
            and tc.set_core=#{setCore}
        </if>
        <if test="versionCode != null and versionCode != '' ">
            and tc.version_code=#{versionCode}
        </if>
        <if test="parentCode != null">
            and tc.parent_code=#{parentCode}
        </if>
        <if test="planPattern != null and planPattern == true ">
            and tc.status = 'NORMAL' and tc.code not in (
                select pc.case_code from tm_test_plan_case pc
                where pc.enable = 1 and pc.plan_code=#{planCode}
                and pc.test_stage = #{testStage} and pc.case_type = #{type}
            )
        </if>
        <if test="planPattern == null and planCode != null and planCode != ''">
            and tc.code in (
                select pc.case_code from tm_test_plan_case pc
                where pc.enable = 1 and pc.plan_code=#{planCode}
                and pc.test_stage = #{testStage} and pc.case_type = #{type}
            )
        </if>
        <if test="isHeartList != null and isHeartList.size()>0">
            and tc.set_heart in
            <foreach collection="isHeartList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="automaticTypeList != null and automaticTypeList.size() > 0">
            and asr.type in
            <foreach collection="automaticTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="automaticSourceCode != null and automaticSourceCode != '' ">
            and tc.automatic_source_code=#{automaticSourceCode}
        </if>
        <include refid="base_sql"/>
        order by tc.id
    </select>

    <select id="selectAllTestCaseByParentCode" resultType="com.zto.devops.qc.client.model.dto.TestcaseEntityDO">
        select tt.code,
               tt.product_code,
               tt.parent_code,
               tt.name,
               tt.attribute,
               tt.type,
               tt.priority,
               tt.status,
               tt.precondition,
               tt.duty_user_id,
               tt.duty_user,
               tt.comment,
               tt.path,
               tt.enable,
               tt.creator_id,
               tt.creator,
               tt.modifier_id,
               tt.modifier,
               tt.set_core,
               tt.version_code
        <if test="planCode != null and planCode != '' ">
            ,tp.status as execution_status
        </if>
        from tm_testcase tt
        left join tm_test_plan_case tp on tp.case_code = tt.code
        where 1=1
        and tt.enable = 1
        <if test="productCode != null and productCode != '' ">
            and tt.product_code = #{productCode}
        </if>
        <if test="versionCode != null and versionCode != '' ">
            and tt.version_code = #{versionCode}
        </if>
        <if test="testcaseAttributeString != null and testcaseAttributeString != '' ">
            and tt.attribute = #{testcaseAttributeString}
        </if>
        <if test="setCore != null">
            and tt.set_core = #{setCore}
        </if>
        <if test="typeString != null and typeString != '' ">
            and tt.type = #{typeString}
        </if>
        <if test="planCode != null and planCode != '' ">
            and (
            (tt.attribute = "TESTCASE" and tp.plan_code = #{planCode}) or
            (tt.attribute = "MODULE" and tp.plan_code is null)
            )
        </if>
        <if test="testStageString != null and testStageString != ''">
            and (tp.test_stage = #{testStageString} or tp.test_stage is null)
        </if>
        and tt.path like CONCAT('%', #{parentCode}, '%')
        group by tt.code
        <if test="planCode != null and planCode != '' ">
            ,tp.status
        </if>
    </select>

    <select id="countNoGroupTestcase" resultType="long">
        select count(*)
        from tm_testcase tc1 where tc1.enable = 1
        and tc1.parent_code=''
        and tc1.product_code=#{productCode}
        and tc1.type=#{type}
        and tc1.attribute='TESTCASE'
        <if test="setCore != null">
            and tc1.set_core=#{setCore}
        </if>
        <if test="versionCode != null and versionCode != '' ">
            and tc1.version_code=#{versionCode}
        </if>
        <if test="codeOrTitle != null and codeOrTitle !=''">
            and (
                tc1.code like CONCAT('%',#{codeOrTitle},'%')
                or tc1.name like CONCAT('%',#{codeOrTitle},'%') ESCAPE '/'
                or tc1.interface_name like CONCAT('%',#{codeOrTitle},'%')
            )
        </if>
        <if test="priorityList != null and priorityList.size()>0">
            and tc1.priority in
            <foreach collection="priorityList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size()>0">
            and tc1.status in
            <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="tagList != null and tagList.size > 0">
            and tc1.code in (
                select t.business_code from qc_tag t where t.enable=1 and t.domain = 'TESTCASE'
                and t.tag_name in
                <foreach collection="tagList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            )
        </if>
        <if test="dutyUserList != null and dutyUserList.size()>0">
            and tc1.duty_user_id in
            <foreach collection="dutyUserList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="creatorList != null and creatorList.size()>0">
            and tc1.creator_id in
            <foreach collection="creatorList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="createTimeBegin != null">
            and tc1.gmt_create <![CDATA[ >= ]]> #{createTimeBegin}
        </if>
        <if test="createTimeEnd != null">
            and tc1.gmt_create <![CDATA[ <= ]]> #{createTimeEnd}
        </if>
        <if test="modifyTimeBegin != null">
            and tc1.gmt_modified <![CDATA[ >= ]]> #{modifyTimeBegin}
        </if>
        <if test="modifyTimeEnd != null">
            and tc1.gmt_modified <![CDATA[ <= ]]> #{modifyTimeEnd}
        </if>
        <if test="planPattern != null and planPattern == true">
            and tc1.status = 'NORMAL' and tc1.code not in (
                select pc1.case_code from tm_test_plan_case pc1
                where pc1.enable = 1 and pc1.plan_code = #{planCode}
                and pc1.test_stage =#{testStage} and pc1.case_type = #{type}
            )
        </if>
        <if test="planPattern == null and planCode != null and planCode != ''">
            and tc1.code in (
                select pc1.case_code from tm_test_plan_case pc1
                where pc1.enable = 1 and pc1.plan_code = #{planCode}
                and pc1.test_stage =#{testStage} and pc1.case_type = #{type}
            )
        </if>
    </select>

    <sql id="base_sql">
        <if test="priorityList != null and priorityList.size()>0">
            and tc.priority in
            <foreach collection="priorityList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size()>0">
            and tc.status in
            <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="tagList != null and tagList.size > 0">
            and tc.code in (
            select t.business_code from qc_tag t where t.enable=1 and t.domain = 'TESTCASE'
            and t.tag_name in
            <foreach collection="tagList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="dutyUserList != null and dutyUserList.size()>0">
            and tc.duty_user_id in
            <foreach collection="dutyUserList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="creatorList != null and creatorList.size()>0">
            and tc.creator_id in
            <foreach collection="creatorList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="createTimeBegin != null">
            and tc.gmt_create <![CDATA[ >= ]]> #{createTimeBegin}
        </if>
        <if test="createTimeEnd != null">
            and tc.gmt_create <![CDATA[ <= ]]> #{createTimeEnd}
        </if>
        <if test="modifyTimeBegin != null">
            and tc.gmt_modified <![CDATA[ >= ]]> #{modifyTimeBegin}
        </if>
        <if test="modifyTimeEnd != null">
            and tc.gmt_modified <![CDATA[ <= ]]> #{modifyTimeEnd}
        </if>
        <if test="nodeTypeList != null and nodeTypeList.size()>0">
            and tc.node_type in
            <foreach collection="nodeTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="codeOrTitle != null and codeOrTitle !=''">
            and (
                tc.code like CONCAT('%',#{codeOrTitle},'%')
                or tc.name like CONCAT('%',#{codeOrTitle},'%') ESCAPE '/'
                or tc.interface_name like CONCAT('%',#{codeOrTitle},'%')
            )
        </if>
    </sql>

    <select id="selectTestcaseCodeList" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.SimpleTestcaseVO">
        select
            a.code,
            a.path
        from (
            SELECT
                t1.code,
                t1.path,
                t2.testcase_code
            FROM tm_testcase t1
            LEFT JOIN tm_automatic_source_record t2 ON t2.code = t1.automatic_source_code
            WHERE t1.enable = 1
                AND t1.product_code = #{productCode}
                AND t1.type = #{type}
                AND t1.attribute = 'TESTCASE'
            <if test="setCoreList != null and setCoreList.size() > 0">
                AND t1.set_core IN
                <foreach collection="setCoreList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="versionCode != null and versionCode != ''">
                AND t1.version_code = #{versionCode}
            </if>
            <if test="parentCode != null and parentCode != ''">
                AND (t1.path LIKE "%" #{parentCode} "%" OR t1.testcase_module_path LIKE "%" #{parentCode} "%")
            </if>
            <if test="parentCode == ''">
                AND t1.parent_code = ''
            </if>
            <if test="codeOrTitle != null and codeOrTitle != ''">
                AND (
                    t1.code LIKE CONCAT('%',#{codeOrTitle},'%')
                    OR t1.name LIKE CONCAT('%',#{codeOrTitle},'%') ESCAPE '/'
                    OR t1.interface_name LIKE CONCAT('%',#{codeOrTitle},'%')
                )
            </if>
            <if test="priorityList != null and priorityList.size() > 0">
                AND t1.priority IN
                <foreach collection="priorityList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND t1.status IN
                <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="tagList != null and tagList.size() > 0">
                AND t1.code IN (
                    SELECT business_code FROM qc_tag
                    WHERE enable = 1 AND domain = 'TESTCASE' AND tag_name IN
                    <foreach collection="tagList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                )
            </if>
            <if test="dutyUserList != null and dutyUserList.size() > 0">
                AND t1.duty_user_id IN
                <foreach collection="dutyUserList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="creatorList != null and creatorList.size() > 0">
                AND t1.creator_id IN
                <foreach collection="creatorList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="createTimeBegin != null">
                AND t1.gmt_create <![CDATA[ >= ]]> #{createTimeBegin}
            </if>
            <if test="createTimeEnd != null">
                AND t1.gmt_create <![CDATA[ <= ]]> #{createTimeEnd}
            </if>
            <if test="modifyTimeBegin != null">
                AND t1.gmt_modified <![CDATA[ >= ]]> #{modifyTimeBegin}
            </if>
            <if test="modifyTimeEnd != null">
                AND t1.gmt_modified <![CDATA[ <= ]]> #{modifyTimeEnd}
            </if>
            <if test="planPattern != null and planPattern == true">
                AND t1.code NOT IN (
                    SELECT case_code FROM tm_test_plan_case
                    WHERE plan_code = #{planCode} AND test_stage = #{testStage} AND case_type = #{type}
                )
            </if>
            <if test="nodeTypeList != null and nodeTypeList.size() > 0">
                AND t1.node_type IN
                <foreach collection="nodeTypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="isHeart != null and isHeart.size() > 0">
                AND t1.set_heart IN
                <foreach collection="isHeart" item="item" open="(" close=")" separator=",">
                    #{item.value}
                </foreach>
            </if>
            <if test="automaticTypeList != null and automaticTypeList.size() > 0">
                AND t2.type IN
                <foreach collection="automaticTypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        ) a
        <if test="type.name() == 'SOURCERECORD' or type.name() == 'AUTO'">
                left join tm_testcase t on t.code = a.testcase_code
                where 1=1
                and (t.name != '草稿' or t.name is null)
        </if>
    </select>

    <select id="selectCountCreatorOrDutyUser" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM tm_testcase
        WHERE enable = 1 AND attribute = 'TESTCASE'
            AND code IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
            AND (creator_id = #{userId} OR duty_user_id = #{userId})
    </select>

    <update id="updateList">
        <foreach collection="list" item="item" separator=";"  index="index">
            update tm_testcase
            set enable = #{item.enable},
            gmt_modified =  #{item.gmtModified},
            modifier_id =  #{item.modifierId},
            modifier =  #{item.modifier}
            where  code = #{item.code}
        </foreach>
    </update>

    <resultMap id="CheckTestcaseStatusVO" type="com.zto.devops.qc.client.model.testmanager.cases.entity.CheckTestcaseStatusVO">
        <result column="total" property="total" jdbcType="INTEGER" />
        <result column="normal" property="normal" jdbcType="INTEGER" />
        <result column="disable" property="disable" jdbcType="INTEGER" />
    </resultMap>

    <select id="selectCountTestcaseStatus" resultMap="CheckTestcaseStatusVO">
        SELECT
            COUNT(*) AS total,
            COUNT( CASE  WHEN status = 'NORMAL' and enable = 1 THEN 1 END) AS normal,
            COUNT( CASE WHEN status = 'DISABLE' and enable = 1 THEN 1 WHEN enable = 0 THEN 1 END) AS disable
        FROM tm_testcase
        WHERE 1=1
        AND attribute = 'TESTCASE'
        AND code IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <insert id="batchInsertCase" parameterType="list">
        <foreach collection="list" separator=";" item="item" index="index">
            INSERT INTO `lbd_qc`.`tm_testcase`
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.code != null">
                    code,
                </if>
                <if test="item.productCode != null">
                    product_code,
                </if>
                <if test="item.parentCode != null">
                    parent_code,
                </if>
                <if test="item.name != null">
                    name,
                </if>
                <if test="item.attribute != null">
                    attribute,
                </if>
                <if test="item.type != null">
                    type,
                </if>
                <if test="item.priority != null">
                    priority,
                </if>
                <if test="item.status != null">
                    status,
                </if>
                <if test="item.precondition != null">
                    precondition,
                </if>
                <if test="item.dutyUserId != null">
                    duty_user_id,
                </if>
                <if test="item.dutyUser != null">
                    duty_user,
                </if>
                <if test="item.comment != null">
                    comment,
                </if>
                <if test="item.abandonReason != null">
                    abandon_reason,
                </if>
                <if test="item.nodeType != null">
                    node_type,
                </if>
                <if test="item.path != null">
                    path,
                </if>
                <if test="item.creatorId != null">
                    creator_id,
                </if>
                <if test="item.creator != null">
                    creator,
                </if>
                <if test="item.gmtCreate != null">
                    gmt_create,
                </if>
                <if test="item.automaticSourceCode != null">
                    automatic_source_code,
                </if>
                <if test="item.testcaseModulePath != null">
                    testcase_module_path,
                </if>
                <if test="item.nodeTypePath != null">
                    node_type_path,
                </if>
                <if test="item.interfaceName != null">
                    interface_name,
                </if>
                <if test="item.setCore != null">
                    set_core,
                </if>
                <if test="item.versionCode != null">
                    version_code,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.code != null">
                    #{item.code,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null">
                    #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.parentCode != null">
                    #{item.parentCode,jdbcType=VARCHAR},
                </if>
                <if test="item.name != null">
                    #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.attribute != null">
                    #{item.attribute,jdbcType=VARCHAR},
                </if>
                <if test="item.type != null">
                    #{item.type,jdbcType=VARCHAR},
                </if>
                <if test="item.priority != null">
                    #{item.priority,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null">
                    #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.precondition != null">
                    #{item.precondition,jdbcType=VARCHAR},
                </if>
                <if test="item.dutyUserId != null">
                    #{item.dutyUserId,jdbcType=BIGINT},
                </if>
                <if test="item.dutyUser != null">
                    #{item.dutyUser,jdbcType=VARCHAR},
                </if>
                <if test="item.comment != null">
                    #{item.comment,jdbcType=VARCHAR},
                </if>
                <if test="item.abandonReason != null">
                    #{item.abandonReason,jdbcType=VARCHAR},
                </if>
                <if test="item.nodeType != null">
                    #{item.nodeType,jdbcType=VARCHAR},
                </if>
                <if test="item.path != null">
                    #{item.path,jdbcType=VARCHAR},
                </if>
                <if test="item.creatorId != null">
                    #{item.creatorId,jdbcType=BIGINT},
                </if>
                <if test="item.creator != null">
                    #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.gmtCreate != null">
                    #{item.gmtCreate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.automaticSourceCode != null">
                    #{item.automaticSourceCode,jdbcType=VARCHAR},
                </if>
                <if test="item.testcaseModulePath != null">
                    #{item.testcaseModulePath,jdbcType=VARCHAR},
                </if>
                <if test="item.nodeTypePath != null">
                    #{item.nodeTypePath,jdbcType=VARCHAR},
                </if>
                <if test="item.interfaceName != null">
                    #{item.interfaceName,jdbcType=VARCHAR},
                </if>
                <if test="item.setCore != null">
                    #{item.setCore,jdbcType=INTEGER},
                </if>
                <if test="item.versionCode != null">
                    #{item.versionCode,jdbcType=VARCHAR},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="selectHeartCaseList" resultType="java.lang.String">
        select
        distinct thc.case_code
        from
        tm_testcase tt
        left join tm_heart_case thc on tt.code = thc.case_code
        where
        thc.case_code is not null
        and tt.enable = true
        <if test="list != null and list.size()>0">
            and tt.code in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectNotHeartCaseList" resultType="java.lang.String">
        select
        distinct tt.code
        from
        tm_testcase tt
        left join tm_heart_case thc on tt.code = thc.case_code
        where
        thc.case_code is null
        and tt.enable = true
        <if test="list != null and list.size()>0">
            and tt.code in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectCaseStatusNum" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.CaseStatusNumVO">
        select
            count(CASE WHEN tt.status = 'DISABLE' THEN 1 ELSE NULL END) disableNum,
            count(CASE WHEN tt.enable = 0 THEN 1 ELSE NULL END)  removedNum
        from tm_testcase tt
        where 1=1
        <if test="list != null and list.size()>0">
            and tt.code in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectChildCaseCountBy" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM tm_testcase
        WHERE enable = 1 and attribute = 'TESTCASE'
        AND (automatic_source_code = #{code} OR path LIKE "%" #{code} "%" OR testcase_module_path LIKE "%" #{code} "%")
        <if test="setCore != null">
            and tc.set_core=#{setCore}
        </if>
        <if test="versionCode != null and versionCode != '' ">
            and tc.version_code=#{versionCode}
        </if>
        <if test="parentCode != null">
            and tc.parent_code=#{parentCode}
        </if>
        <include refid="base_sql"/>
    </select>

    <select id="selectListAndPlanName" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseVO">
        SELECT
            * , (SELECT GROUP_CONCAT(DISTINCT (n.plan_name) SEPARATOR ',')
            from tm_test_plan n left join tm_test_plan_case c on n.code =c.plan_code
            WHERE n.status in ("NOT_STARTED","IN_PROGRESS") and c.case_code = s.code ) plan_name
        FROM
            tm_testcase s
        where s.automatic_source_code = #{automaticSourceCode} and s.enable = 1
    </select>

    <select id="selectDirectTestcaseCount" resultType="integer">
        SELECT
        count(1)
        FROM
        tm_testcase t
        WHERE
        t.product_code = #{productCode}
        AND t.parent_code = #{parentCode}
        AND t.attribute = #{attribute}
        AND t.type = #{type}
    </select>

    <select id="selectTestcaseModulePath" resultType="java.lang.String">
        SELECT
        path
        FROM
        tm_testcase tc
        WHERE
        attribute = #{attribute}
        and enable = true
        and type = #{type}
        and path LIKE CONCAT('%',#{oldPath},'%')
        ORDER BY LENGTH(path) DESC
        LIMIT 1
    </select>

    <update id="replacePath">
        UPDATE tm_testcase SET
            path = REPLACE(path, #{oldPath}, #{newPath})
        WHERE path LIKE #{oldPath} "%"
    </update>

    <update id="replaceTestcaseModulePath">
        UPDATE tm_testcase SET
            testcase_module_path = REPLACE(testcase_module_path, #{oldPath}, #{newPath})
        WHERE testcase_module_path LIKE #{oldPath} "%"
    </update>

    <select id="selectMaxLengthPathByPath" resultType="java.lang.String">
        SELECT
            path
        FROM tm_testcase
        WHERE enable = 1 AND attribute = 'MODULE' AND path like #{path} "%"
        ORDER BY CHAR_LENGTH(path) DESC
        LIMIT 1
    </select>

    <select id="selectModuleCodeList" resultType="java.lang.String">
        select
            code
        from tm_testcase
        where enable = 1
            and product_code=#{productCode}
            and type='AUTO'
            and attribute='MODULE'
        <if test="nodeTypeList != null and nodeTypeList.size() > 0">
            and node_type in
            <foreach collection="nodeTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="planPattern != null and planPattern == true">
            and status = 'NORMAL'
        </if>
    </select>

    <select id="selectDistinctCaseNameByCodeList" resultType="java.lang.String">
        select
        distinct tt.name
        from tm_testcase tt
        where 1=1
          and tt.`type`  = 'MANUAL'
          and tt.`attribute` = 'testcase'
          and tt.set_core = #{setCore}
          and tt.enable = true
        <if test="list!=null">
            and tt.code in
            <foreach collection="list" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectGroupByCaseCodeList" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseParentInfoVO">
        select
        distinct tt.code ,
        tt.parent_code parentCode,
        tt2.name parentName,
        tt2.`path` parentPath
        from
        tm_testcase tt
        left join tm_testcase tt2 on tt.parent_code = tt2.code
        where
        1 = 1
        and tt.enable = true
        and tt2.enable = true
        and tt.product_code = #{productCode}
        and tt2.product_code =#{productCode}
        and tt.version_code = #{versionCode}
        and tt2.version_code = #{versionCode}
        and tt.`type` = #{type}
        and tt2.`type` = #{type}
        and tt.`attribute` = 'testcase'
        and tt2.`attribute` = 'module'
        and tt.set_core = #{setCore}
        and tt2.set_core = #{setCore}
        <if test="list!=null">
            and tt.code in
            <foreach collection="list" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectSameNameModule" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.SameNameModuleVO">
        select length(tt2.path) as pathLength,
               tt.version_code  as versionCode,
               tt.code          as moduleCode,
               tt.name          as moduleName,
               tt2.code         as parentModuleCode,
               tt2.name         as parentModuleName
        from tm_testcase tt
                 left join tm_testcase tt2 on find_in_set(tt2.code, replace(tt.`path`, '.', ',')) or tt.code = tt2.code
        where 1 = 1
          and tt.code = #{parentCode}
          and tt.product_code = #{productCode}
          and tt2.product_code = #{productCode}
          and tt.name = #{moduleName}
          and tt.`type` = 'MANUAL'
          and tt2.`type` = 'MANUAL'
          and tt.set_core = false
          and tt2.set_core = false
          and tt.`attribute` = 'module'
          and tt2.`attribute` = 'module'
          and tt.enable = true
          and tt2.enable = true
          and tt.version_code = #{versionCode}
        order by tt.version_code, length(tt2.path)
    </select>

    <select id="selectTestCaseModule" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.ListTestcaseVO">
        select
        tc.code,
        tc.product_code productCode,
        tc.parent_code parentCode,
        tc.name,
        tc.attribute,
        tc.type,
        tc.status,
        tc.automatic_source_code automaticSourceCode,
        case tc.node_type when '' then null else tc.node_type end as nodeType,
        tc.creator_id creatorId,
        tc.creator,
        tc.gmt_create gmtCreate,
        tc.modifier_id modifierId,
        tc.modifier,
        tc.gmt_modified gmtModified,
        tc.id,
        tc.path,
        tc.version_code versionCode,
        tc.set_core setCore
        from tm_testcase tc
        where tc.enable = 1 and tc.product_code=#{productCode} and tc.type=#{type} and tc.attribute='MODULE'
        <if test="code != null and code != '' ">
            and tc.code=#{code}
        </if>
        <if test="versionCode != null and versionCode != '' ">
            and tc.version_code=#{versionCode}
        </if>
        <if test="setCore != null">
            and tc.set_core=#{setCore}
        </if>
        <if test="parentCode != null">
            and tc.parent_code=#{parentCode}
        </if>
        <if test="planPattern != null and planPattern == true">
            and tc.status = 'NORMAL'
        </if>
        <if test="automaticSourceCode != null">
            and tc.automatic_source_code = #{automaticSourceCode}
        </if>
    </select>

    <select id="selectModuleByNameAndPath" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.SameNameModuleVO">
        select length(tt.path) as pathLength,
               tt.version_code as versionCode,
               tt.code         as moduleCode,
               tt.name         as moduleName
        from tm_testcase tt
                 left join tm_testcase tt2 on tt.parent_code = tt2.code
        where 1 = 1
          and tt.enable = true
          and length(tt.path) = #{pathLength}
          and tt.product_code = #{productCode}
          and tt.version_code = #{versionCode}
          and tt.name = #{moduleName}
          and (tt2.name = #{parentModuleName} or tt2.id is null)
          and tt.`type` = 'MANUAL'
          and tt.`attribute` = 'MODULE'
          and tt.set_core = false
        order by tt.gmt_modified limit 1
    </select>

    <select id="selectCoreModuleByNameAndPath" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.SameNameModuleVO">
        select length(tt.path) as pathLength,
               tt.version_code as versionCode,
               tt.code         as moduleCode,
               tt.name         as moduleName
        from tm_testcase tt
                 left join tm_testcase tt2 on tt.parent_code = tt2.code
        where 1 = 1
          and tt.enable = true
          and length(tt.path) = #{pathLength}
          and tt.product_code = #{productCode}
          and find_in_set(#{versionModuleCode}, replace(tt.`path`, '.', ','))
          and tt.name = #{moduleName}
          and (tt2.name = #{parentModuleName} or tt2.id is null)
          and tt.`type` = 'MANUAL'
          and tt.`attribute` = 'MODULE'
          and tt.set_core = true
        order by tt.gmt_modified limit 1
    </select>

    <select id="selectRelatedPlanList" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.RelatedCasePlanVO">
        select distinct ttp.code as planCode,
        ttp.plan_name as planName,
        ttp.`type` as planType,
        ttpc.case_code as caseCode
        from tm_test_plan_case ttpc
        left join tm_test_plan ttp on ttpc.plan_code = ttp.code
        where ttpc.enable = 1
        and ttp.enable = 1
        <if test="list != null and list.size()>0">
            and ttpc.case_code in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectModuleInfoByName" resultType="com.zto.devops.qc.client.model.dto.TestcaseEntityDO">
        select t.code, t.name, t.parent_code as parentCode, t.path
        from tm_testcase t
        where t.parent_code = #{parentCode}
        and t.product_code = #{productCode}
        and t.version_code = #{versionCode}
        and t.type = #{type}
        and t.attribute = #{attribute}
        and t.name = #{name}
        and t.node_type = ''
        and t.enable = true
    </select>

    <select id="getParentCodeListByCaseCodeList" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.TestCasePathVO">
        select
        tt.parent_code as parentCode ,
        any_value(tt.`path`) as path
        from tm_testcase tt
        where
        tt.enable = true
        and tt.parent_code != ''
        and tt.path != ''
        <if test="list != null and list.size()>0">
            and tt.code in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by tt.parent_code
        order by length(any_value(tt.`path`)) desc
    </select>

    <select id="countByParentCodeList" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.CountCaseVO">
        select
        tt.code as moduleCode,
        length(tt.path) as length,
        count(distinct if(tt2.`attribute` = 'testcase', tt2.code, null)) as caseNum,
        count(distinct if(tt2.`attribute` = 'module', tt2.code, null)) as moduleNum
        from
        tm_testcase tt
        left join tm_testcase tt2 on tt.code = tt2.parent_code
        where
        1 = 1
        and tt2.enable = true
        and tt2.`type` = 'MANUAL'
        and tt2.set_core = false
        and tt2.product_code =  #{productCode}
        and tt2.version_code =  #{versionCode}
        <if test="list != null and list.size()>0">
            and tt.code in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by tt.code
        order by length(tt.path) desc
    </select>

    <select id="selectOneByModuleNameAndParentCode" resultMap="BaseResultMap">
        select *
        from tm_testcase tt
        where tt.`attribute` = 'MODULE'
          and tt.`type` = 'MANUAL'
          and tt.parent_code = #{parentCode}
          and tt.path = #{parentCode}
          and tt.name = #{moduleName}
          and tt.enable = true
        order by id desc limit 1
    </select>

    <select id="countNoGroupTestcaseByVersionCodeList"
            resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.CountNoGroupCaseVO">
        select tc1.version_code as versionCode ,
        count(*) as caseCount
        from tm_testcase tc1 where tc1.enable = 1
        and tc1.parent_code=''
        and tc1.product_code=#{productCode}
        and tc1.type=#{type}
        and tc1.attribute='TESTCASE'
        <if test="planCaseVersionCodeList != null and planCaseVersionCodeList.size() > 0">
            and tc1.version_code in
            <foreach collection="planCaseVersionCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="setCore != null">
            and tc1.set_core=#{setCore}
        </if>
        <if test="versionCode != null and versionCode != '' ">
            and tc1.version_code=#{versionCode}
        </if>
        <if test="codeOrTitle != null and codeOrTitle !=''">
            and (
            tc1.code like CONCAT('%',#{codeOrTitle},'%')
            or tc1.name like CONCAT('%',#{codeOrTitle},'%') ESCAPE '/'
            or tc1.interface_name like CONCAT('%',#{codeOrTitle},'%')
            )
        </if>
        <if test="priorityList != null and priorityList.size()>0">
            and tc1.priority in
            <foreach collection="priorityList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size()>0">
            and tc1.status in
            <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="tagList != null and tagList.size > 0">
            and tc1.code in (
            select t.business_code from qc_tag t where t.enable=1 and t.domain = 'TESTCASE'
            and t.tag_name in
            <foreach collection="tagList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="dutyUserList != null and dutyUserList.size()>0">
            and tc1.duty_user_id in
            <foreach collection="dutyUserList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="creatorList != null and creatorList.size()>0">
            and tc1.creator_id in
            <foreach collection="creatorList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="createTimeBegin != null">
            and tc1.gmt_create <![CDATA[ >= ]]> #{createTimeBegin}
        </if>
        <if test="createTimeEnd != null">
            and tc1.gmt_create <![CDATA[ <= ]]> #{createTimeEnd}
        </if>
        <if test="modifyTimeBegin != null">
            and tc1.gmt_modified <![CDATA[ >= ]]> #{modifyTimeBegin}
        </if>
        <if test="modifyTimeEnd != null">
            and tc1.gmt_modified <![CDATA[ <= ]]> #{modifyTimeEnd}
        </if>
        <if test="planPattern != null and planPattern == true">
            and tc1.status = 'NORMAL' and tc1.code not in (
            select pc1.case_code from tm_test_plan_case pc1
            where pc1.enable = 1 and pc1.plan_code = #{planCode}
            and pc1.test_stage =#{testStage} and pc1.case_type = #{type}
            )
        </if>
        <if test="planPattern == null and planCode != null and planCode != ''">
            and tc1.code in (
            select pc1.case_code from tm_test_plan_case pc1
            where pc1.enable = 1 and pc1.plan_code = #{planCode}
            and pc1.test_stage =#{testStage} and pc1.case_type = #{type}
            )
        </if>
        group by tc1.version_code
    </select>

    <select id="selectTopModuleCodeByName" resultType="java.lang.String">
        select tt.code
        from tm_testcase tt
        where 1 = 1
          and tt.product_code = #{productCode}
          and tt.name = #{moduleName}
          and tt.enable = true
          and tt.`attribute` = 'MODULE'
          and tt.`type` = 'AUTO'
          and tt.parent_code = ''
        order by id desc limit 1
    </select>

    <select id="selectSceneDefaultModule" resultMap="BaseResultMap">
        select tt.*
        from tm_testcase tt
                 left join tm_testcase tt2 on tt.parent_code = tt2.code
        where 1 = 1
          and tt.name = #{secondModuleName}
          and tt2.name = #{topModuleName}
          and tt2.parent_code = ''
          and tt.enable = true
          and tt.enable = true
          and tt.product_code = #{productCode}
          and tt2.product_code = #{productCode} limit 1
    </select>
</mapper>
