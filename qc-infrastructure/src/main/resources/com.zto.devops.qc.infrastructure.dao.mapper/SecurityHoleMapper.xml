<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.SecurityHoleMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.SecurityHoleEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="code" jdbcType="VARCHAR" property="code"/>
    <result column="report_code" jdbcType="VARCHAR" property="reportCode" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="harm" jdbcType="VARCHAR" property="harm" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="sec_id" jdbcType="VARCHAR" property="secId" />
  </resultMap>
</mapper>