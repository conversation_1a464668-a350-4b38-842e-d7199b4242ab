<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.CoveragePublishMapper">

    <sql id="Base_Column_List">
        id, app_type, version_code, version_name, app_id,
        commit_id, branch_name, enable, git_url, deployment_identity, service_name,
        port, package_name, gmt_create, gmt_modified, output_file_name, flow_lane_type
    </sql>

    <select id="getLatestPublishRecord" resultType="com.zto.devops.qc.infrastructure.dao.entity.CoveragePublishEntity">
        select
        <include refid="Base_Column_List"/>
        from qc_coverage_publish p
        inner join (SELECT
        MAX(id) maxId
        FROM
        qc_coverage_publish
        WHERE 1=1
        <if test="versionCodes != null and versionCodes.size > 0">
            and version_code in
            <foreach collection="versionCodes" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="branchName != null and branchName != ''">
            and branch_name = #{branchName}
        </if>
        <if test="appIds != null">
            and app_id in
            <foreach collection="appIds" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="envName != null and envName != ''">
            and env_name = #{envName}
        </if>
        GROUP BY app_id) t on t.maxId = p.id
    </select>

    <select id="getLatestPublishRecordByProductCode" resultType="com.zto.devops.qc.infrastructure.dao.entity.CoveragePublishEntity">
        select
        id,
        app_type,
        a.version_code,
        a.version_name,
        app_id,
        commit_id,
        branch_name,
        enable,
        git_url,
        deployment_identity,
        service_name,
        port,
        package_name,
        gmt_create,
        gmt_modified,
        flow_lane_type
        FROM
        (SELECT
        *
        FROM
        qc_coverage_publish
        WHERE
        id IN (SELECT
        MAX(id)
        FROM
        qc_coverage_publish
        WHERE
        1 = 1
        <if test="envName != null and envName != ''">
            and env_name = #{envName}
        </if>
        GROUP BY app_id)) a
        INNER JOIN
        (SELECT DISTINCT(version_code)
        FROM
        qc_coverage_record
        WHERE 1=1
        <if test="productCode != null and productCode != ''">
            and product_code = #{productCode}
        </if>
        AND enable = 1) r ON r.version_code = a.version_code
        WHERE 1=1
        <if test="versionCodes != null and versionCodes.size > 0">
            and a.version_code in
            <foreach collection="versionCodes" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="appIds != null">
            and a.app_id in
            <foreach collection="appIds" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getLatestPublishRecordByEntity"
            resultType="com.zto.devops.qc.infrastructure.dao.entity.CoveragePublishEntity">
        select
        <include refid="Base_Column_List"/>
        from qc_coverage_publish p
        inner join (SELECT
        MAX(id) maxId
        FROM
        qc_coverage_publish
        WHERE 1=1
        AND flow_lane_type in ('FLOW_TEST','FLOW_PROD')
        <if test="versionCode != null and versionCode != ''">
            and version_code = #{versionCode}
        </if>
        <if test="branchName != null and branchName != ''">
            and branch_name = #{branchName}
        </if>
        <if test="appId != null and appId != ''">
            and app_id = #{appId}
        </if>
        GROUP BY app_id) t on t.maxId = p.id
    </select>

    <select id="getLatestProdPublish" resultType="com.zto.devops.qc.infrastructure.dao.entity.CoveragePublishEntity">
        select
        <include refid="Base_Column_List"/>
        from qc_coverage_publish
        where 1 = 1
        <if test="appId != null and appId != ''">
            and app_id = #{appId}
        </if>
        <if test="branchName != null and branchName != ''">
            and branch_name = #{branchName}
        </if>
        <if test="versionCode != null and versionCode != ''">
            and version_code != #{versionCode}
        </if>
        <if test="commitId != null and commitId != ''">
            and commit_id != #{commitId}
        </if>
        <if test="gmtCreate != null">
            and gmt_create <![CDATA[ < ]]> #{gmtCreate}
        </if>
        order by id desc
        limit 1
    </select>

    <select id="getFirstBranchPublish"
            resultType="com.zto.devops.qc.infrastructure.dao.entity.CoveragePublishEntity">
        select
        <include refid="Base_Column_List"/>
        from qc_coverage_publish
        where 1 = 1
        <if test="appId != null and appId != ''">
            and app_id = #{appId}
        </if>
        <if test="branchName != null and branchName != ''">
            and branch_name = #{branchName}
        </if>
        <if test="versionCode != null and versionCode != ''">
            and version_code = #{versionCode}
        </if>
        <if test="commitId != null and commitId != ''">
            and commit_id != #{commitId}
        </if>
        order by id asc
        limit 1
    </select>

    <select id="getPublishRecordByCommitId" resultType="com.zto.devops.qc.infrastructure.dao.entity.CoveragePublishEntity">
        select
        <include refid="Base_Column_List"/>
        from qc_coverage_publish
        where 1 = 1
        <if test="appId != null and appId != ''">
            and app_id = #{appId}
        </if>
        <if test="branchName != null and branchName != ''">
            and branch_name = #{branchName}
        </if>
        <if test="versionCode != null and versionCode != ''">
            and version_code = #{versionCode}
        </if>
        <if test="commitId != null and commitId != ''">
            and commit_id = #{commitId}
        </if>
        order by id asc
    </select>

    <select id="getMiddlePublishEntity" resultType="com.zto.devops.qc.infrastructure.dao.entity.CoveragePublishEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        qc_coverage_publish
        WHERE 1 = 1
        AND flow_lane_type in ('FLOW_TEST','FLOW_PROD','')
        <if test="appId != null and appId != ''">
            and app_id = #{appId}
        </if>
        <if test="versionCode != null and versionCode != ''">
            and version_code = #{versionCode}
        </if>
        <if test="branchName != null and branchName != ''">
            and branch_name = #{branchName}
        </if>
        AND gmt_create <![CDATA[ < ]]> (SELECT
        gmt_create
        FROM
        qc_coverage_publish
        WHERE
        1 = 1
        <if test="commitId != null and commitId != ''">
            AND commit_id = #{commitId}
        </if>
        <if test="appId != null and appId != ''">
            and app_id = #{appId}
        </if>
        ORDER BY id DESC
        LIMIT 1)
        AND gmt_create >= (SELECT
        gmt_create
        FROM
        qc_coverage_publish
        WHERE
        1 = 1
        <if test="firstBranchCommitId != null and firstBranchCommitId != ''">
            AND commit_id = #{firstBranchCommitId}
        </if>
        <if test="appId != null and appId != ''">
            and app_id = #{appId}
        </if>
        ORDER BY id DESC
        LIMIT 1)
        ORDER BY id DESC;
    </select>

    <select id="getPublishRecordLastDay" resultType="com.zto.devops.qc.infrastructure.dao.entity.CoveragePublishEntity">
        select
        version_code, branch_name, app_id
        from qc_coverage_publish
        where gmt_create >=  #{startTime}
        and gmt_create <![CDATA[ <= ]]> #{endTime}
        group by version_code, branch_name, app_id
    </select>
</mapper>
