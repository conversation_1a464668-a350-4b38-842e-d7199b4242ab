<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.SceneInfoMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.SceneInfoEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="scene_code" jdbcType="VARCHAR" property="sceneCode"/>
        <result column="scene_name" jdbcType="VARCHAR" property="sceneName"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="automatic_source_code" jdbcType="VARCHAR" property="automaticSourceCode"/>
        <result column="scene_version" jdbcType="INTEGER" property="sceneVersion"/>
        <result column="scene_info_desc" jdbcType="VARCHAR" property="sceneInfoDesc"/>
        <result column="scene_oss_path" jdbcType="VARCHAR" property="sceneOssPath"/>
        <result column="scene_oss_file" jdbcType="VARCHAR" property="sceneOssFile"/>
        <result column="scene_back_data_md5" jdbcType="VARCHAR" property="sceneBackDataMd5"/>
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.SceneInfoStatusTypeHandler"/>
        <result column="step_record" jdbcType="VARCHAR" property="stepRecord"/>
        <result column="enable" jdbcType="TINYINT" property="enable"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.SceneInfoEnableTypeHandler"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="scene_type" jdbcType="TINYINT" property="sceneType"/>
        <result column="share_status" jdbcType="TINYINT" property="shareStatus"/>
        <result column="scene_back_data" jdbcType="VARCHAR" property="sceneBackData"/>
    </resultMap>

    <resultMap id="PageResultMap" type="com.zto.devops.qc.client.model.dto.SceneInfoEntityDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="scene_code" jdbcType="VARCHAR" property="sceneCode"/>
        <result column="scene_name" jdbcType="VARCHAR" property="sceneName"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="automatic_source_code" jdbcType="VARCHAR" property="automaticSourceCode"/>
        <result column="scene_version" jdbcType="INTEGER" property="sceneVersion"/>
        <result column="scene_info_desc" jdbcType="VARCHAR" property="sceneInfoDesc"/>
        <result column="scene_oss_path" jdbcType="VARCHAR" property="sceneOssPath"/>
        <result column="scene_oss_file" jdbcType="VARCHAR" property="sceneOssFile"/>
        <result column="scene_back_data_md5" jdbcType="VARCHAR" property="sceneBackDataMd5"/>
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.SceneInfoStatusTypeHandler"/>
        <result column="step_record" jdbcType="VARCHAR" property="stepRecord"/>
        <result column="enable" jdbcType="TINYINT" property="enable"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.SceneInfoEnableTypeHandler"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="tag_name" jdbcType="VARCHAR" property="tagName"/>
        <result column="scene_type" jdbcType="TINYINT" property="sceneType"/>
        <result column="share_status" jdbcType="TINYINT" property="shareStatus"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , scene_code, scene_name, product_code, automatic_source_code, scene_version, scene_info_desc,
        scene_oss_path, scene_oss_file, scene_back_data_md5, status, step_record, enable, creator_id, creator,
        gmt_create, modifier_id, modifier, gmt_modified, scene_type, share_status, scene_back_data
    </sql>

    <select id="pageQuerySceneList" resultMap="PageResultMap">
        select t.id, t.scene_code, t.scene_name, t.product_code, t.automatic_source_code,
        t.scene_version,t.scene_info_desc, t.status, t.step_record, t.enable,t.scene_type,
        t.creator_id, t.creator, t.gmt_create, t.modifier_id, t.modifier,
        t.gmt_modified, t.tag_name, t.share_status,t.parent_code
        from (
        select
        MAX(t.id) as id,
        MAX(t.scene_code) as scene_code,
        MAX(t.scene_name) as scene_name,
        MAX(t.product_code) as product_code,
        MAX(tsi.automatic_source_code) as automatic_source_code,
        MAX(t.scene_version) as scene_version,
        MAX(t.scene_info_desc) as scene_info_desc,
        MAX(t.status) as status,
        MAX(t.step_record) as step_record,
        MAX(t.scene_type) as scene_type,
        MAX(t.enable) as enable,
        MAX(t.creator_id) as creator_id,
        MAX(t.creator) as creator,
        MAX(t.gmt_create) as gmt_create,
        MAX(t.modifier_id) as modifier_id,
        MAX(t.modifier) as modifier,
        MAX(t.gmt_modified) as gmt_modified,
        MAX(t.share_status) as share_status,
        MAX(tin.parent_code) as parent_code,
        GROUP_CONCAT(DISTINCT qt.tag_name) as tag_name
        from tm_scene_info t
        left join qc_tag qt on qt.business_code = t.scene_code
        left join tm_scene_index tin on tin.scene_index_code = t.scene_code
        left join (
        select max(tsi.scene_code) as scene_code, max(tsi.automatic_source_code) as automatic_source_code
        from tm_scene_info tsi
        where tsi.status = "publish"
        <if test="shareProductFlag == null or shareProductFlag == false">
            and tsi.product_code = #{productCode}
        </if>
        group by scene_code
        ) tsi on tsi.scene_code = t.scene_code
        left join tm_scene_api_relation sar on sar.scene_code = t.scene_code
        where t.status = "edit"
        and t.enable != 0
        and tin.enable != 0
        <if test="shareProductFlag == null or shareProductFlag == false">
            AND t.product_code = #{productCode}
        </if>
        <if test="shareStatus != null">
            and t.share_status = #{shareStatus}
        </if>
        <if test="apiReq != null">
            <if test="apiReq.apiType != null">
                and sar.api_type =
                #{apiReq.apiType, typeHandler=com.zto.devops.qc.infrastructure.dao.typehandler.ApiTypeHandler}
            </if>
            <if test="apiReq.appId != null and apiReq.appId != ''">
                and sar.app_id = #{apiReq.appId}
            </if>
            <if test="apiReq.apiAddress != null and apiReq.apiAddress != ''">
                and sar.api_address = #{apiReq.apiAddress}
            </if>
        </if>
        group by t.scene_code ) t
        where 1=1
        <if test="enableList != null and enableList.size() > 0">
            and t.enable in
            <foreach collection="enableList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="parentCode != null">
            and t.parent_code = #{parentCode}
        </if>
        <if test="sceneName != null and sceneName != ''">
            and t.scene_name like CONCAT('%',#{sceneName},'%')
        </if>
        <if test="sceneInfoDesc != null and sceneInfoDesc != ''">
            and t.scene_info_desc like CONCAT('%',#{sceneInfoDesc},'%')
        </if>
        <if test="sceneCodeList != null and sceneCodeList.size() > 0">
            and t.scene_code in
            <foreach collection="sceneCodeList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="modifierIdList != null and modifierIdList.size() > 0">
            and t.modifier_id in
            <foreach collection="modifierIdList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="creatorIdList != null and creatorIdList.size() > 0">
            and creator_id in
            <foreach collection="creatorIdList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="gmtModifiedStart != null and gmtModifiedEnd != null">
            and t.gmt_modified between (#{gmtModifiedStart}) AND (#{gmtModifiedEnd})
        </if>
        <if test="relatedWithMe != null and relatedWithMe.size > 0">
            <trim prefix="and (" suffix=")" prefixOverrides="OR">
                <if test="relatedWithMe.contains(@com.zto.devops.qc.client.enums.testmanager.apitest.SceneRelatedWithMeEnum@CREATOR)">
                    OR creator_id = #{currentUserId}
                </if>
                <if test="relatedWithMe.contains(@com.zto.devops.qc.client.enums.testmanager.apitest.SceneRelatedWithMeEnum@MODIFIER)">
                    OR t.modifier_id = #{currentUserId}
                </if>
            </trim>
        </if>
        <if test="tagNameList != null and tagNameList.size > 0">
            <trim prefix="and (" suffix=")" prefixOverrides="OR">
                <foreach collection="tagNameList" index="index" item="item">
                    OR t.tag_name like CONCAT('%',#{item},'%')
                </foreach>
            </trim>
        </if>
        ORDER BY
        t.gmt_modified DESC

    </select>

    <select id="queryLatestEditSceneInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM
        tm_scene_info
        where 1=1
        and scene_code = #{sceneCode}
        and status = 'edit'
        order by scene_version desc
        limit 1
    </select>

    <select id="querySceneByProductCodeAndSceneType" resultMap="BaseResultMap">
        SELECT t.scene_code, MAX(t.scene_name) as sceneName, MAX(t.product_code) as productCode ,MAX(t.scene_type) as
        sceneType
        FROM tm_scene_info t
        WHERE t.enable != 0
        <if test="productCode != null and productCode != ''">
            AND t.product_code = #{productCode}
        </if>
        <if test="sceneType != null and sceneType != ''">
            AND t.scene_type = #{sceneType}
        </if>
        GROUP BY t.scene_code
    </select>
</mapper>
