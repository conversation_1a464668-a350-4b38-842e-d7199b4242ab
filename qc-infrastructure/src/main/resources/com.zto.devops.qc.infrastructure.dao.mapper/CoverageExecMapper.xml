<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.CoverageExecMapper">

    <sql id="Base_Column_List">
        id, version_code, app_id, branch_name, commit_id, bucket_name, exec_path, exec_name,
        creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified, flow_lane_type
    </sql>

    <select id="getExecList"
            resultType="com.zto.devops.qc.infrastructure.dao.entity.CoverageExecEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_coverage_exec r
        WHERE 1=1
        AND flow_lane_type in ('FLOW_TEST','FLOW_PROD','')
        <if test="versionCode != null and versionCode != ''">
            AND version_code = #{versionCode}
        </if>
        <if test="appId != null and appId != ''">
            AND app_id = #{appId}
        </if>
        <if test="commitId != null and commitId != ''">
            and commit_id = #{commitId}
        </if>
        <if test="diffType != null">
            AND (diff_type = #{diffType})
        </if>
        order by id desc
    </select>

    <select id="getMiddleExecList"
            resultType="com.zto.devops.qc.infrastructure.dao.entity.CoverageExecEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_coverage_exec r
        WHERE 1=1
        AND app_id = #{appId}
        AND version_code = #{versionCode}
        AND branch_name = #{branchName}
        AND flow_lane_type in ('FLOW_TEST','FLOW_PROD','')
        <if test="list != null">
            and commit_id in
            <foreach collection="list" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="diffType != null">
            AND (diff_type = #{diffType})
        </if>
    </select>

</mapper>