<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TestFunctionPointMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TestFunctionPointEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <id column="code" jdbcType="VARCHAR" property="code" />
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="function_point" jdbcType="VARCHAR" property="functionPoint" />
    <result column="director_id" jdbcType="BIGINT" property="directorId" />
    <result column="director_name" jdbcType="VARCHAR" property="directorName" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="number" jdbcType="VARCHAR" property="number" />
  </resultMap>
  <sql id="Base_Column_List">
    id, code, business_code, type, function_point, director_id, director_name, creator_id, 
    creator, gmt_create, modifier_id, modifier, gmt_modified, enable,number
  </sql>

</mapper>