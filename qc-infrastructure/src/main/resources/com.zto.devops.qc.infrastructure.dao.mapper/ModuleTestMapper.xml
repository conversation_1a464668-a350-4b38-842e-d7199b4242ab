<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.ModuleTestMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.ModuleTestEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="code" jdbcType="VARCHAR" property="code"/>
    <result column="report_code" jdbcType="VARCHAR" property="reportCode" />
    <result column="test_type" jdbcType="VARCHAR" property="testType" />
    <result column="test_result" jdbcType="TINYINT" property="testResult" />
    <result column="valid_issue_count" jdbcType="INTEGER" property="validIssueCount" />
    <result column="legacy_issue_count" jdbcType="INTEGER" property="legacyIssueCount" />
    <result column="report_user_id" jdbcType="BIGINT" property="reportUserId" />
    <result column="report_user_name" jdbcType="VARCHAR" property="reportUserName" />
  </resultMap>

  <select id="selectByReportCode" resultMap="BaseResultMap">
    select *
    from qc_module_test t
    where t.report_code = #{reportCode} and t.enable=true
  </select>
</mapper>