<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.IssueMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.IssueEntity">
        <id column="code" jdbcType="VARCHAR" property="code"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.StatusHandler"/>
        <result column="priority" jdbcType="VARCHAR" property="priority"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.PriorityHandler"/>
        <result column="root_cause" jdbcType="VARCHAR" property="rootCause"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.RootCauseHandler"/>
        <result column="type" jdbcType="VARCHAR" property="type"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.IssueTypeHandler"/>
        <result column="test_method" jdbcType="VARCHAR" property="testMethod"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TestMethodHandler"/>
        <result column="find_stage" jdbcType="VARCHAR" property="findStage"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.FindStageHandler"/>
        <result column="find_env" jdbcType="VARCHAR" property="findEnv"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.FindEnvHandler"/>
        <result column="repetition_rate" jdbcType="VARCHAR" property="repetitionRate"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.RepetitionRateHandler"/>
        <result column="find_time" jdbcType="TIMESTAMP" property="findTime"/>
        <result column="reopen_time" jdbcType="TIMESTAMP" property="reopenTime"/>
        <result column="reopen" jdbcType="INTEGER" property="reopen" />
        <result column="start_fix_time" jdbcType="TIMESTAMP" property="startFixTime"/>
        <result column="delay_fix_time" jdbcType="TIMESTAMP" property="delayFixTime"/>
        <result column="deliver_time" jdbcType="TIMESTAMP" property="deliverTime"/>
        <result column="reject_time" jdbcType="TIMESTAMP" property="rejectTime"/>
        <result column="close_time" jdbcType="TIMESTAMP" property="closeTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="requirement_code" jdbcType="VARCHAR" property="requirementCode"/>
        <result column="requirement_name" jdbcType="VARCHAR" property="requirementName"/>
        <result column="requirement_level" jdbcType="VARCHAR" property="requirementLevel"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.RequirementLevelHandler"/>
        <result column="find_version_code" jdbcType="VARCHAR" property="findVersionCode"/>
        <result column="find_version_name" jdbcType="VARCHAR" property="findVersionName"/>
        <result column="fix_version_code" jdbcType="VARCHAR" property="fixVersionCode"/>
        <result column="fix_version_name" jdbcType="VARCHAR" property="fixVersionName"/>
        <result column="find_user_id" jdbcType="BIGINT" property="findUserId"/>
        <result column="find_user_name" jdbcType="VARCHAR" property="findUserName"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="handle_user_id" jdbcType="BIGINT" property="handleUserId"/>
        <result column="handle_user_name" jdbcType="VARCHAR" property="handleUserName"/>
        <result column="develop_user_id" jdbcType="BIGINT" property="developUserId"/>
        <result column="develop_user_name" jdbcType="VARCHAR" property="developUserName"/>
        <result column="test_user_id" jdbcType="BIGINT" property="testUserId"/>
        <result column="test_user_name" jdbcType="VARCHAR" property="testUserName"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="actual_working_hours" jdbcType="DOUBLE" property="actualWorkingHours"/>
        <result column="sprint_code" jdbcType="VARCHAR" property="sprintCode"/>
        <result column="sprint_name" jdbcType="VARCHAR" property="sprintName"/>
        <result column="version_confirm" jdbcType="VARCHAR" property="versionConfirm"/>
        <result column="examination" jdbcType="TINYINT" property="examination"/>
        <result column="test_omission" jdbcType="TINYINT" property="testOmission"/>
        <result column="code_defect" jdbcType="TINYINT" property="codeDefect"/>
        <result column="test_omission_version" jdbcType="VARCHAR" property="testOmissionVersion"/>
        <result column="code_defect_version" jdbcType="VARCHAR" property="codeDefectVersion"/>
        <result column="application_type" jdbcType="VARCHAR" property="applicationType"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.IssueApplicationTypeHandler"/>
        <result column="plan_start_date" jdbcType="DATE" property="planStartDate"/>
        <result column="plan_end_date" jdbcType="DATE" property="planEndDate"/>
        <result column="msg_code" jdbcType="VARCHAR" property="msgCode"/>
    </resultMap>

    <select id="simpleQuery" resultType="com.zto.devops.qc.client.model.issue.entity.IssueVO">
        select qi.code,
        qi.title,
        qi.status,
        qi.priority,
        qi.root_cause as rootCause,
        qi.type,
        qi.test_method as testMethod,
        qi.find_stage as findStage,
        qi.find_env as findEnv,
        qi.repetition_rate as repetitionRate,
        qi.find_time as findTime,
        qi.reopen_time as reopenTime,
        qi.start_fix_time as startFixTime,
        qi.delay_fix_time as delayFixTime,
        qi.deliver_time as deliverTime,
        qi.reject_time as rejectTime,
        qi.close_time as closeTime,
        qi.update_time as updateTime,
        qi.product_code as productCode,
        qi.product_name as productName,
        qi.requirement_code as requirementCode,
        qi.requirement_name as requirementName,
        qi.requirement_level as requirementLevel,
        qi.find_version_code as findVersionCode,
        qi.find_version_name as findVersionName,
        qi.fix_version_code as fixVersionCode,
        qi.fix_version_name as fixVersionName,
        qi.find_user_id as findUserId,
        qi.find_user_name as findUserName,
        qi.handle_user_id as handleUserId,
        qi.handle_user_name as handleUserName,
        qi.develop_user_id as developUserId,
        qi.develop_user_name as developUserName,
        qi.test_user_id as testUserId,
        qi.test_user_name as testUserName,
        qi.description,
        qi.gmt_create as gmtCreate,
        qi.creator_id as creatorId,
        qi.creator as creator,
        qi.gmt_modified as gmtModified,
        qi.modifier updateUserName,
        qi.modifier_id as updateUserId,
        qi.actual_working_hours as actualWorkingHours,
        qi.version_confirm as versionConfirm,
        qi.sprint_code as sprintCode,
        qi.sprint_name as sprintName,
        qi.plan_start_date as planStartDate,
        qi.plan_end_date as planEndDate,
        qi.test_omission_version as testOmissionVersion,
        qi.code_defect_version as codeDefectVersion,
        qi.application_type,
        qi.reopen
        from qc_issue qi
        <if test="tagName != null and tagName.size > 0">
            left join (select code,tag_name, business_code from qc_tag where enable = 1) tag
            ON qi.code = tag.business_code
        </if>
        where qi.enable = true
        <include refid="base_sql"></include>
        order by qi.gmt_create desc
    </select>

    <select id="selectIssueCodeList" resultType="string">
        select qi.code from qc_issue qi
        <if test="tagName != null and tagName.size > 0">
            left join (select code,tag_name, business_code from qc_tag where enable = 1) tag
            ON qi.code = tag.business_code
        </if>
        where qi.enable = 1
        <include refid="base_sql"></include>
        <if test="tagName != null and tagName.size > 0">
            group by qi.`code`
        </if>
        <choose>
            <when test="orderField!=null and orderField!='' and orderField == @<EMAIL>()">
                order by
                CASE WHEN priority = 'URGENCY' THEN 4
                WHEN priority = 'HIGH' THEN 3
                WHEN priority = 'MIDDLE' THEN 2
                WHEN priority = 'LOW' THEN 1
                ELSE 0 END <include refid="orderType" />, qi.id desc
            </when>
            <when test="orderField!=null and orderField!='' and orderField != @<EMAIL>()">
                order by <include refid="orderField" /> <include refid="orderType" />, qi.id desc
            </when>
            <otherwise>
                order by qi.id desc
            </otherwise>
        </choose>
    </select>

    <select id="listStatusByDefinedQuery" resultType="string">
        select qi.status
        from qc_issue qi
        <if test="tagName != null and tagName.size > 0">
            left join (select code,tag_name, business_code from qc_tag where enable = 1) tag
            ON qi.code = tag.business_code
        </if>
        where qi.enable = true
        <include refid="base_sql"></include>
        <if test="tagName != null and tagName.size > 0">
            group by qi.`code`
        </if>
    </select>

    <select id="selectByDefinedQuery" resultType="com.zto.devops.qc.client.model.issue.entity.IssueVO">
        select qi.code,
        qi.title,
        qi.status,
        qi.priority,
        qi.root_cause as rootCause,
        qi.type,
        qi.test_method as testMethod,
        qi.find_stage as findStage,
        qi.find_env as findEnv,
        qi.repetition_rate as repetitionRate,
        qi.find_time as findTime,
        qi.reopen_time as reopenTime,
        qi.start_fix_time as startFixTime,
        qi.delay_fix_time as delayFixTime,
        qi.deliver_time as deliverTime,
        qi.reject_time as rejectTime,
        qi.close_time as closeTime,
        qi.update_time as updateTime,
        qi.product_code as productCode,
        qi.product_name as productName,
        qi.requirement_code as requirementCode,
        qi.requirement_name as requirementName,
        qi.requirement_level as requirementLevel,
        qi.find_version_code as findVersionCode,
        qi.find_version_name as findVersionName,
        qi.fix_version_code as fixVersionCode,
        qi.fix_version_name as fixVersionName,
        qi.find_user_id as findUserId,
        qi.find_user_name as findUserName,
        qi.handle_user_id as handleUserId,
        qi.handle_user_name as handleUserName,
        qi.develop_user_id as developUserId,
        qi.develop_user_name as developUserName,
        qi.test_user_id as testUserId,
        qi.test_user_name as testUserName,
        qi.description,
        qi.gmt_create as gmtCreate,
        qi.creator_id as creatorId,
        qi.creator as creator,
        qi.gmt_modified as gmtModified,
        qi.modifier updateUserName,
        qi.modifier_id as updateUserId,
        qi.actual_working_hours as actualWorkingHours,
        qi.version_confirm as versionConfirm,
        qi.sprint_code as sprintCode,
        qi.sprint_name as sprintName,
        GROUP_CONCAT(DISTINCT tag.tag_name) as tagName,
        max(tn.reason) as refuseReason,
        timestampdiff(hour,qi.gmt_create,now()) as warnDay,
        qi.examination as examination,
        qi.test_omission as testOmission,
        qi.code_defect as codeDefect,
        qi.test_omission_version as testOmissionVersion,
        qi.code_defect_version as codeDefectVersion,
        qi.application_type,
        qi.reopen,
        (SELECT GROUP_CONCAT(c.user_name SEPARATOR ',') from qc_relevant_user c WHERE c.type = 'CC'  and c.enable = 1 and c.business_code = qi.code group by c.business_code ) cc_user_name
        from qc_issue qi
        left join (select code,tag_name, business_code from qc_tag where enable = 1) tag
        ON qi.code = tag.business_code
        left join (
        SELECT a.business_code, a.reason FROM qc_transition_node a where a.next_status = 'REJECTED' and a.enable = 1
          and not exists (SELECT 1 FROM qc_transition_node b where b.next_status = 'REJECTED' and b.enable = 1 and a.business_code = b.business_code and a.gmt_create &lt; b.gmt_create)
        ) tn
        ON qi.code = tn.business_code
        where qi.enable = true
        <include refid="base_sql"></include>
        group by qi.`code`
        <choose>
            <when test="orderField!=null and orderField!='' and orderField == @<EMAIL>()">
                order by
                CASE WHEN priority = 'URGENCY' THEN 4
                WHEN priority = 'HIGH' THEN 3
                WHEN priority = 'MIDDLE' THEN 2
                WHEN priority = 'LOW' THEN 1
                ELSE 0 END <include refid="orderType" />, qi.id desc
            </when>
            <when test="orderField!=null and orderField!='' and orderField != @<EMAIL>()">
                order by qi.<include refid="orderField" /> <include refid="orderType" />, qi.id desc
            </when>
            <otherwise>
                order by qi.id desc
            </otherwise>
        </choose>
    </select>

    <sql id="base_sql">
        <if test="validFlag!=null">
            and qi.is_valid = #{validFlag}
        </if>
        <if test="issueCodeList != null and issueCodeList.size > 0  ">
            and qi.code in
            <foreach collection="issueCodeList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="codeOrTitle!=null and codeOrTitle!=''">
            and (qi.title like "%"#{codeOrTitle}"%" or qi.code like "%"#{codeOrTitle}"%")
        </if>
        <if test="statusList!=null  and  statusList.size > 0">
            and qi.status in
            <foreach collection="statusList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="priorityList!=null and priorityList.size > 0">
            and qi.priority in
            <foreach collection="priorityList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="rootCauseList!=null and rootCauseList.size > 0">
            and qi.root_cause in
            <foreach collection="rootCauseList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="issueTypeList!=null and issueTypeList.size > 0">
            and qi.type in
            <foreach collection="issueTypeList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="testMethodList!=null and testMethodList.size > 0">
            and qi.test_method in
            <foreach collection="testMethodList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="repetitionRateList!=null and repetitionRateList.size > 0">
            and qi.repetition_rate in
            <foreach collection="repetitionRateList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="findStageList!=null and findStageList.size > 0">
            and qi.find_stage in
            <foreach collection="findStageList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="findEnvList!=null and findEnvList.size > 0 ">
            and qi.find_env in
            <foreach collection="findEnvList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="handleUserIdList!=null and handleUserIdList.size() > 0">
            and qi.handle_user_id in
            <foreach collection="handleUserIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="developUserIdList!=null and developUserIdList.size() > 0">
            and qi.develop_user_id in
            <foreach collection="developUserIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="testUserIdList!=null and testUserIdList.size() > 0">
            and qi.test_user_id in
            <foreach collection="testUserIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="findVersionList!=null and findVersionList.size() > 0">
            and qi.find_version_code in
            <foreach collection="findVersionList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="fixVersionList!=null and fixVersionList.size>0">
            and qi.fix_version_code in
            <foreach collection="fixVersionList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="fixOrFindVersionList!=null  and  fixOrFindVersionList.size>0">
            and ( qi.fix_version_code in
            <foreach collection="fixOrFindVersionList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
            or qi.find_version_code in
            <foreach collection="fixOrFindVersionList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>

            )

        </if>
        <if test=" modifierId != null and  modifierId.size > 0">
            and qi.modifier_id in
            <foreach collection="modifierId" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="relatedRequireList!=null and relatedRequireList.size > 0">
            and qi.requirement_code in
            <foreach collection="relatedRequireList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="relatedProductList!=null and relatedProductList.size > 0 ">
            and qi.product_code in
            <foreach collection="relatedProductList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="sprintCode!=null and sprintCode.size > 0">
            and qi.sprint_code in
            <foreach collection="sprintCode" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="findUserIdList!=null and findUserIdList.size > 0">
            and qi.find_user_id in
            <foreach collection="findUserIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="versionConfirm!=null and versionConfirm.size > 0">
            and qi.version_confirm in
            <foreach collection="versionConfirm" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="createTimeEnd !=null and createTimeStart != null">
            and #{createTimeEnd} >= qi.gmt_create and qi.gmt_create >= #{createTimeStart}
        </if>
        <if test="closeTimeStart != null and closeTimeEnd != null">
            and #{closeTimeEnd} >= qi.close_time and qi.close_time >= #{closeTimeStart}
        </if>
        <if test="gmtModifiedEnd != null and gmtModifiedStart != null">
            and #{gmtModifiedEnd} >= qi.gmt_modified and qi.gmt_modified >= #{gmtModifiedStart}
        </if>
        <if test="fixVersionIsNull != null and fixVersionIsNull == true ">
            and qi.fix_version_code= 'NotAssociated'
        </if>
        <if test="relatedList != null and relatedList.size > 0">
            <trim prefix="and (" suffix=")" prefixOverrides="OR">
                <if test="relatedList.contains(@com.zto.devops.qc.client.enums.issue.RelatedToMeEnum@CURRENT_HANDLE_USER)">
                    OR qi.code in (select business_code as code from qc_relevant_user where enable=true and
                    user_id=#{currentUserId} and type="CURRENT_HANDLE_USER")
                </if>
                <if test="relatedList.contains(@com.zto.devops.qc.client.enums.issue.RelatedToMeEnum@CREATOR)">
                    OR qi.code in (select business_code as code from qc_relevant_user where enable=true and
                    user_id=#{currentUserId} and type="CREATOR")
                </if>
                <if test="relatedList.contains(@com.zto.devops.qc.client.enums.issue.RelatedToMeEnum@HANDLED_USER) ">
                    OR qi.code in (select business_code as code from qc_relevant_user where enable=true and
                    user_id=#{currentUserId} and type="HANDLED_USER")
                </if>
                <if test="relatedList.contains(@com.zto.devops.qc.client.enums.issue.RelatedToMeEnum@CC) ">
                    OR qi.code in (select business_code as code from qc_relevant_user where enable=true and
                    user_id=#{currentUserId} and type="CC")
                </if>
            </trim>
        </if>
        <if test="examination != null and examination.size > 0">
            and qi.examination in
            <foreach collection="examination" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="testOmission != null and testOmission.size > 0">
            and qi.test_omission in
            <foreach collection="testOmission" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="codeDefect != null and codeDefect.size > 0">
            and qi.code_defect in
            <foreach collection="codeDefect" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="tagName != null and tagName.size > 0">
            <choose>
                <when test="tagName.size>1">
                    and tag.tag_name in
                    <foreach collection="tagName" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and tag.tag_name like CONCAT('%',#{tagName[0]},'%')
                </otherwise>
            </choose>
        </if>
        <if test="refuseReasonList != null and refuseReasonList.size > 0">
            and exists (
            SELECT 1 FROM qc_transition_node a where a.next_status = 'REJECTED' and a.enable = 1 and
            qi.code = a.business_code and
            a.reason in
            <foreach collection="refuseReasonList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
            and not exists (SELECT 1 FROM qc_transition_node b where b.next_status = 'REJECTED' and b.enable = 1 and a.business_code = b.business_code and a.gmt_create &lt;  b.gmt_create)
            )
        </if>
        <if test="applicationTypeList!=null and applicationTypeList.size >0">
            and qi.application_type in
            <foreach collection="applicationTypeList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="ccUserIdList != null and ccUserIdList.size >0 ">
            and qi.code in (
            select c.business_code from qc_relevant_user c where
            c.user_id in
            <foreach collection="ccUserIdList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
            and
            enable=1 and type='CC'
            )
        </if>
        <if test="versionType!=null and versionType == 'URGENT_TYPE'">
            and  ( qi.find_env = 'PRODUCT_EVN' or qi.find_stage = 'OPERATE_STAGE')
        </if>
    </sql>


    <select id="queryIssueGroupByHandle" resultType="java.util.Map">
        select handle_user_id,count(code)
        from qc_issue where enable=1
        <if test="status=='WAIT_FIX'">
            AND status in ("WAIT_FIX","FIXING")
        </if>
        <if test="status=='TESTING'">
            AND status in ("TESTING","REJECTED")
        </if>
        AND test_user_id != develop_user_id
        AND code like 'ISS%'
        group by handle_user_id
    </select>

    <select id="selectFindVersionCode" resultType="java.lang.String">
        SELECT distinct find_version_code
        FROM qc_issue
        WHERE ENABLE = 1
          AND CODE LIKE 'ISS%';
    </select>

    <select id="selectDevopsCreateIssue" resultType="com.zto.devops.qc.client.model.issue.entity.IssueVO">
        select * from qc_issue t where
        t.gmt_create>'2021-08-26'
        and t.code like '%ISS%';
    </select>

    <select id="findUnClosedByVersionCodes" resultType="com.zto.devops.qc.client.model.issue.entity.IssueVO">
        select * from qc_issue where enable = 1 and  status != 'CLOSED' and
        find_version_code in
        <foreach collection="versionCodes" item="item" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
        union
        select * from qc_issue where enable = 1 and  status != 'CLOSED' and
        fix_version_code in
        <foreach collection="versionCodes" item="item" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
    </select>

    <!--<select id="selectIssueByVersionCode" resultType="com.zto.lbd.qc.issue.entity.IssueVO">-->
        <!--SELECT * FROM qc_issue WHERE enable=1-->
        <!--<choose>-->
            <!--<when test="findVersionCode != null">-->
                <!--AND (find_version_code=#{findVersionCode}-->
                <!--<if test="fixVersionCode != null and fixVersionCode != ''">-->
                    <!--OR fix_version_code=#{fixVersionCode}-->
                <!--</if>-->
                <!--)-->
            <!--</when>-->
            <!--<otherwise>-->
                <!--<if test="fixVersionCode != null">-->
                    <!--AND fix_version_code=#{fixVersionCode}-->
                <!--</if>-->
            <!--</otherwise>-->
        <!--</choose>-->
        <!--LIMIT 2000-->
    <!--</select>-->

    <select id="selectRelatedMatterStatusCount" parameterType="list" resultType="com.zto.devops.project.client.model.requirement.entity.RelatedMatterStatusCountVO">
        select
        qi.requirement_code as business_code,
        qi.status,
        count(1) as `count`
        from
        qc_issue qi
        <trim prefix="WHERE" prefixOverrides="and | or">
            <if test="requirementCodeList != null and requirementCodeList.size > 0">
                and qi.requirement_code in
                <foreach collection="requirementCodeList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            and qi.enable = 1
        </trim>
        group by qi.requirement_code,qi.status
    </select>

    <sql id="orderField">
        <choose>
            <when test="orderField == @com.zto.devops.qc.client.enums.issue.IssueFieldEnum@start_fix_time.name()">
                start_fix_time
            </when>
            <when test="orderField == @com.zto.devops.qc.client.enums.issue.IssueFieldEnum@delay_fix_time.name()">
                delay_fix_time
            </when>
            <when test="orderField == @com.zto.devops.qc.client.enums.issue.IssueFieldEnum@reject_time.name()">
                reject_time
            </when>
            <when test="orderField == @com.zto.devops.qc.client.enums.issue.IssueFieldEnum@deliver_time.name()">
                deliver_time
            </when>
            <when test="orderField == @com.zto.devops.qc.client.enums.issue.IssueFieldEnum@close_time.name()">
                close_time
            </when>
            <when test="orderField == @com.zto.devops.qc.client.enums.issue.IssueFieldEnum@reopen_time.name()">
                reopen_time
            </when>
            <when test="orderField == @com.zto.devops.qc.client.enums.issue.IssueFieldEnum@gmt_create.name()">
                gmt_create
            </when>
            <when test="orderField == @com.zto.devops.qc.client.enums.issue.IssueFieldEnum@update_time.name()">
                update_time
            </when>
            <when test="orderField == @com.zto.devops.qc.client.enums.issue.IssueFieldEnum@gmt_modified.name()">
                gmt_modified
            </when>
        </choose>
    </sql>

    <sql id="orderType">
        <choose>
            <when test="orderType == 'ASC'">
                asc
            </when>
            <when test="orderType == 'asc'">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </sql>

    <select id="listIssueByHandleUserId" resultType="com.zto.devops.qc.client.model.issue.entity.IssueVO">
        select * from qc_issue where enable = 1 and  status != 'CLOSED' and
        (handle_user_id = #{handleUserId} or develop_user_id = #{handleUserId} or test_user_id = #{handleUserId})
    </select>

    <select id="queryFixIssueGroupByDevelopUserId" resultType="com.zto.devops.qc.client.model.issue.entity.CountUserIssueNumVO">
        select
        develop_user_id     as handleUserId,
        count(code)         as countNum
        from qc_issue
        where
        enable = true
        AND find_user_id != develop_user_id
        AND code like 'ISS%'
        <if test="list != null and list.size > 0  ">
            AND status in
            <foreach collection="list" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by develop_user_id
    </select>

    <select id="queryTestIssueGroupByTestUserId" resultType="com.zto.devops.qc.client.model.issue.entity.CountUserIssueNumVO">
        select
        test_user_id    as handleUserId,
        count(code)     as countNum
        from qc_issue
        where
        enable = true
        AND code like 'ISS%'
        <if test="list != null and list.size > 0  ">
            AND status in
            <foreach collection="list" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by test_user_id
    </select>
</mapper>