<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.AutomaticSourceLogTestcaseMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceLogTestcaseEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="attribute" jdbcType="VARCHAR" property="attribute" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="priority" jdbcType="VARCHAR" property="priority" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="precondition" jdbcType="VARCHAR" property="precondition" />
    <result column="duty_user_id" jdbcType="BIGINT" property="dutyUserId" />
    <result column="duty_user" jdbcType="VARCHAR" property="dutyUser" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="automatic_source_log_code" jdbcType="VARCHAR" property="automaticSourceLogCode" />
    <result column="node_type" jdbcType="VARCHAR" property="nodeType" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="layer" jdbcType="INTEGER" property="layer" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="node_type_path" jdbcType="VARCHAR" property="nodeTypePath" />
    <result column="flag" jdbcType="VARCHAR" property="flag" />
    <result column="testcase_code" jdbcType="VARCHAR" property="testcaseCode" />
    <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, code, product_code, parent_code, name, attribute, type, priority, status, precondition, 
    duty_user_id, duty_user, comment, automatic_source_log_code, node_type, sort, layer, 
    path, enable, creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified, 
    node_type_path, flag,testcase_code, interface_name
  </sql>


  <insert id="insertList" parameterType="list">
    <foreach collection="list" separator=";" item="item" index="index">
      insert into tm_automatic_source_log_testcase
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.code != null">
          code,
        </if>
        <if test="item.productCode != null">
          product_code,
        </if>
        <if test="item.parentCode != null">
          parent_code,
        </if>
        <if test="item.name != null">
          name,
        </if>
        <if test="item.attribute != null">
          attribute,
        </if>
        <if test="item.type != null">
          type,
        </if>
        <if test="item.priority != null">
          priority,
        </if>
        <if test="item.status != null">
          status,
        </if>
        <if test="item.precondition != null">
          precondition,
        </if>
        <if test="item.dutyUserId != null">
          duty_user_id,
        </if>
        <if test="item.dutyUser != null">
          duty_user,
        </if>
        <if test="item.comment != null">
          comment,
        </if>
        <if test="item.automaticSourceLogCode != null">
          automatic_source_log_code,
        </if>
        <if test="item.nodeType != null">
          node_type,
        </if>
        <if test="item.sort != null">
          sort,
        </if>
        <if test="item.layer != null">
          layer,
        </if>
        <if test="item.path != null">
          path,
        </if>
        <if test="item.enable != null">
          enable,
        </if>
        <if test="item.creatorId != null">
          creator_id,
        </if>
        <if test="item.creator != null">
          creator,
        </if>
        <if test="item.gmtCreate != null">
          gmt_create,
        </if>
        <if test="item.modifierId != null">
          modifier_id,
        </if>
        <if test="item.modifier != null">
          modifier,
        </if>
        <if test="item.gmtModified != null">
          gmt_modified,
        </if>
        <if test="item.nodeTypePath != null">
          node_type_path,
        </if>
        <if test="item.flag != null">
          flag,
        </if>
        <if test="item.testcaseCode != null">
          testcase_code,
        </if>
        <if test="item.interfaceName != null">
          interface_name,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.code != null">
          #{item.code,jdbcType=VARCHAR},
        </if>
        <if test="item.productCode != null">
          #{item.productCode,jdbcType=VARCHAR},
        </if>
        <if test="item.parentCode != null">
          #{item.parentCode,jdbcType=VARCHAR},
        </if>
        <if test="item.name != null">
          #{item.name,jdbcType=VARCHAR},
        </if>
        <if test="item.attribute != null">
          #{item.attribute,jdbcType=VARCHAR},
        </if>
        <if test="item.type != null">
          #{item.type,jdbcType=VARCHAR},
        </if>
        <if test="item.priority != null">
          #{item.priority,jdbcType=VARCHAR},
        </if>
        <if test="item.status != null">
          #{item.status,jdbcType=VARCHAR},
        </if>
        <if test="item.precondition != null">
          #{item.precondition,jdbcType=VARCHAR},
        </if>
        <if test="item.dutyUserId != null">
          #{item.dutyUserId,jdbcType=BIGINT},
        </if>
        <if test="item.dutyUser != null">
          #{item.dutyUser,jdbcType=VARCHAR},
        </if>
        <if test="item.comment != null">
          #{item.comment,jdbcType=VARCHAR},
        </if>
        <if test="item.automaticSourceLogCode != null">
          #{item.automaticSourceLogCode,jdbcType=VARCHAR},
        </if>
        <if test="item.nodeType != null">
          #{item.nodeType,jdbcType=VARCHAR},
        </if>
        <if test="item.sort != null">
          #{item.sort,jdbcType=INTEGER},
        </if>
        <if test="item.layer != null">
          #{item.layer,jdbcType=INTEGER},
        </if>
        <if test="item.path != null">
          #{item.path,jdbcType=VARCHAR},
        </if>
        <if test="item.enable != null">
          #{item.enable,jdbcType=BIT},
        </if>
        <if test="item.creatorId != null">
          #{item.creatorId,jdbcType=BIGINT},
        </if>
        <if test="item.creator != null">
          #{item.creator,jdbcType=VARCHAR},
        </if>
        <if test="item.gmtCreate != null">
          #{item.gmtCreate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.modifierId != null">
          #{item.modifierId,jdbcType=BIGINT},
        </if>
        <if test="item.modifier != null">
          #{item.modifier,jdbcType=VARCHAR},
        </if>
        <if test="item.gmtModified != null">
          #{item.gmtModified,jdbcType=TIMESTAMP},
        </if>
        <if test="item.nodeTypePath != null">
          #{item.nodeTypePath,jdbcType=VARCHAR},
        </if>
        <if test="item.flag != null">
          #{item.flag,jdbcType=VARCHAR},
        </if>
        <if test="item.testcaseCode != null">
          #{item.testcaseCode,jdbcType=VARCHAR},
        </if>
        <if test="item.interfaceName != null">
          #{item.interfaceName,jdbcType=VARCHAR},
        </if>
      </trim>
    </foreach>
  </insert>

  <select id="selectListAndPlanNameLog" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticSourceLogTestcaseVO">
    SELECT
      *,
      (
        SELECT
          GROUP_CONCAT( DISTINCT ( n.plan_name ) SEPARATOR ',' )
        FROM
          tm_test_plan n
            LEFT JOIN tm_test_plan_case c ON n.CODE = c.plan_code
        WHERE
          n.STATUS IN ( "NOT_STARTED", "IN_PROGRESS" ) and c.case_code = s.testcase_code) plan_name
    FROM
      tm_automatic_source_log_testcase s
    WHERE
      s.automatic_source_log_code = #{logCode}
      AND s.ENABLE = 1 and s.flag = #{flag}
  </select>

</mapper>