<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TmApiConfigMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.ApiGlobalConfigurationEntity">
        <result column="api_config_code" jdbcType="VARCHAR" property="apiConfigCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="api_config_type" jdbcType="INTEGER" property="apiConfigType"/>
        <result column="api_config_scope" jdbcType="INTEGER" property="apiConfigScope"/>
        <result column="api_config_value" jdbcType="VARCHAR" property="apiConfigValue"/>
        <result column="api_config_assert" jdbcType="VARCHAR" property="apiConfigAssert"/>
        <result column="enable" jdbcType="BIGINT" property="enable"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>


    <insert id="batchInsert">
        insert into tm_api_config (
        api_config_code, product_code, api_config_type, api_config_scope, api_config_value, api_config_assert,enable,
        creator_id, creator, gmt_create
        ) values
        <foreach collection="list" item="item" separator=",">
            (#{item.apiConfigCode}, #{item.productCode}, #{item.apiConfigType}, #{item.apiConfigScope}, #{item.apiConfigValue},
            #{item.apiConfigAssert}, #{item.enable},#{item.creatorId}, #{item.creator}, #{item.gmtCreate})
        </foreach>
    </insert>

</mapper>