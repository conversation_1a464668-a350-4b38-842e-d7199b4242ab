<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.IssueBackupMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.IssueBackupEntity">
    <id column="code" jdbcType="VARCHAR" property="code" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="status" jdbcType="VARCHAR" property="status" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.StatusHandler"/>
    <result column="priority" jdbcType="VARCHAR" property="priority" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.PriorityHandler"/>
    <result column="root_cause" jdbcType="VARCHAR" property="rootCause" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.RootCauseHandler"/>
    <result column="type" jdbcType="VARCHAR" property="type" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.IssueTypeHandler"/>
    <result column="test_method" jdbcType="VARCHAR" property="testMethod" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.TestMethodHandler"/>
    <result column="find_stage" jdbcType="VARCHAR" property="findStage" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.FindStageHandler"/>
    <result column="find_env" jdbcType="VARCHAR" property="findEnv" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.FindEnvHandler"/>
    <result column="repetition_rate" jdbcType="VARCHAR" property="repetitionRate" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.RepetitionRateHandler"/>
    <result column="find_time" jdbcType="TIMESTAMP" property="findTime" />
    <result column="reopen_time" jdbcType="TIMESTAMP" property="reopenTime" />
    <result column="start_fix_time" jdbcType="TIMESTAMP" property="startFixTime" />
    <result column="delay_fix_time" jdbcType="TIMESTAMP" property="delayFixTime" />
    <result column="deliver_time" jdbcType="TIMESTAMP" property="deliverTime" />
    <result column="reject_time" jdbcType="TIMESTAMP" property="rejectTime" />
    <result column="close_time" jdbcType="TIMESTAMP" property="closeTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="requirement_code" jdbcType="VARCHAR" property="requirementCode" />
    <result column="requirement_name" jdbcType="VARCHAR" property="requirementName" />
    <result column="requirement_level" jdbcType="VARCHAR" property="requirementLevel" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.RequirementLevelHandler"/>
    <result column="find_version_code" jdbcType="VARCHAR" property="findVersionCode" />
    <result column="find_version_name" jdbcType="VARCHAR" property="findVersionName" />
    <result column="fix_version_code" jdbcType="VARCHAR" property="fixVersionCode" />
    <result column="fix_version_name" jdbcType="VARCHAR" property="fixVersionName" />
    <result column="find_user_id" jdbcType="BIGINT" property="findUserId" />
    <result column="find_user_name" jdbcType="VARCHAR" property="findUserName" />
    <result column="update_user_id" jdbcType="BIGINT" property="updateUserId" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="handle_user_id" jdbcType="BIGINT" property="handleUserId" />
    <result column="handle_user_name" jdbcType="VARCHAR" property="handleUserName" />
    <result column="develop_user_id" jdbcType="BIGINT" property="developUserId" />
    <result column="develop_user_name" jdbcType="VARCHAR" property="developUserName" />
    <result column="test_user_id" jdbcType="BIGINT" property="testUserId" />
    <result column="test_user_name" jdbcType="VARCHAR" property="testUserName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="actual_working_hours" jdbcType="DOUBLE" property="actualWorkingHours" />
    <result column="sprint_code" jdbcType="VARCHAR" property="sprintCode" />
    <result column="sprint_name" jdbcType="VARCHAR" property="sprintName" />
    <result column="version_confirm" jdbcType="VARCHAR" property="versionConfirm" />
  </resultMap>
</mapper>