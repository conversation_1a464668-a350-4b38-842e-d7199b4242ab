<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.AutomaticSourceRecordMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceRecordEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="type" jdbcType="VARCHAR" property="type" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticRecordTypeHandler"/>
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="add_case_no" jdbcType="INTEGER" property="addCaseNo" />
    <result column="update_case_no" jdbcType="INTEGER" property="updateCaseNo" />
    <result column="delete_case_no" jdbcType="INTEGER" property="deleteCaseNo" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="status" jdbcType="VARCHAR" property="status" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticStatusHandler"/>
    <result column="data_file_address" jdbcType="VARCHAR" property="dataFileAddress" />
    <result column="extend_jar_address" jdbcType="VARCHAR" property="extendJarAddress" />
    <result column="third_jar_address" jdbcType="VARCHAR" property="thirdJarAddress" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="bucket_name" jdbcType="VARCHAR" property="bucketName" />
    <result column="fail_information" jdbcType="VARCHAR" property="failInformation" />
    <result column="person_liable_id" jdbcType="BIGINT" property="personLiableId" />
    <result column="person_liable" jdbcType="VARCHAR" property="personLiable" />
    <result column="testcase_code" jdbcType="VARCHAR" property="testcaseCode" />
    <result column="work_space" jdbcType="VARCHAR" property="workSpace" />
    <result column="branch" jdbcType="VARCHAR" property="branch" />
    <result column="commit_id" jdbcType="VARCHAR" property="commitId" />
    <result column="scan_directory" jdbcType="VARCHAR" property="scanDirectory" />
    <result column="error_log_file" jdbcType="VARCHAR" property="errorLogFile" />
  </resultMap>
  <sql id="Base_Column_List">
    id, code, product_code, name, comment, address, type, enable, add_case_no, update_case_no,
    delete_case_no, creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified,status,
    data_file_address,extend_jar_address,third_jar_address,file_name,bucket_name,fail_information,
    person_liable_id,person_liable,testcase_code,work_space,branch,commitId,scan_directory,error_log_file
  </sql>

  <select id="selectAutomaticSourceStatus" resultType="com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceRecordEntity">
    SELECT r.* ,(select status from tm_automatic_task where automatictc_source_code = r.code order by gmt_create desc limit 1) as status FROM tm_automatic_source_record r
        where r.product_code = #{productCode} and r.enable = 1
  </select>

  <select id="selectAnalysisAutomaticRecordAbort"
          resultType="com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceRecordEntity">
    SELECT  tr.code,
            tr.product_code,
            tr.modifier_id,
            tr.modifier
    FROM tm_automatic_source_record tr
    WHERE tr.enable = 1
    <if test="recordAbortSecond != null and recordAbortSecond != ''">
      AND now() >SUBDATE(tr.gmt_modified,interval -#{recordAbortSecond} second)
    </if>
    <if test="typeList != null and typeList.size() > 0">
      AND tr.status IN
      <foreach collection="typeList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
  </select>
  <select id="selectByTestcaseCode" resultType="java.lang.String">
    SELECT code FROM tm_automatic_source_record WHERE enable = 1 AND testcase_code = #{testcaseCode}
  </select>

  <select id="selectByTestcaseCodeList" resultType="java.lang.String">
    SELECT code FROM tm_automatic_source_record WHERE enable = 1 AND testcase_code IN
    <foreach collection="testcaseCodeList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectWithTestPlanCaseVO" resultType="com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanCaseVO">
    SELECT
      id,
      code AS caseCode,
      IFNULL(testcase_code, '') AS parentCode,
      name as caseName,
      'SOURCERECORD' AS type,
      enable,
      type as automaticRecordType
    FROM tm_automatic_source_record
    WHERE product_code = #{productCode}
    <if test="testcaseCode != null and testcaseCode != ''">
      AND testcase_code = #{testcaseCode}
    </if>
    <if test="testcaseCode == ''">
      AND (testcase_code = '' OR testcase_code IS NULL)
    </if>
    <if test="typeList != null and typeList.size() > 0">
      AND type IN
      <foreach collection="typeList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    ORDER BY id DESC
  </select>

  <select id="selectRelatedPlanName" resultType="java.lang.String">
    select distinct
      t3.plan_name
    from tm_testcase t1
    left join tm_test_plan_case t2 on t2.case_code = t1.code
    left join tm_test_plan t3 on t3.code = t2.plan_code
    where t1.enable = 1
      and t1.attribute = 'TESTCASE'
      and t1.automatic_source_code = #{automaticSourceCode}
      and t2.enable = 1
      and t3.enable = 1
      and t3.status in ('NOT_STARTED', 'IN_PROGRESS', 'RESTART')
  </select>

  <select id="selectRelatedTaskName" resultType="java.lang.String">
    select distinct
      t3.scheduler_name
    from tm_testcase t1
    left join tm_automatic_scheduler_case t2 on t2.case_code = t1.code
    left join tm_automatic_scheduler t3 on t3.scheduler_code = t2.scheduler_code
    where t1.enable = 1
      and t1.attribute = 'TESTCASE'
      and t1.automatic_source_code = #{automaticSourceCode}
      and t2.enable = 1
      and t3.enable = 1
      and t3.switch = 1
  </select>
</mapper>
