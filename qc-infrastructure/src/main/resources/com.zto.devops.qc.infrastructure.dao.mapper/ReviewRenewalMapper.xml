<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.ReviewRenewalMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.ReviewRenewalEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="code" jdbcType="VARCHAR" property="code"/>
    <result column="report_code" jdbcType="VARCHAR" property="reportCode" />
    <result column="serial_id" jdbcType="VARCHAR" property="serialId" />
    <result column="review_before" jdbcType="VARCHAR" property="reviewBefore" />
    <result column="review_after" jdbcType="VARCHAR" property="reviewAfter" />
    <result column="renewal_content" jdbcType="VARCHAR" property="renewalContent" />
  </resultMap>
</mapper>