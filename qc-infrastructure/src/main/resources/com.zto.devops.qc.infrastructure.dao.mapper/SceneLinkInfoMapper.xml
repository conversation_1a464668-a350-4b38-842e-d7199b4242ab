<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.SceneLinkInfoMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.SceneLinkInfoEntity">
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="scene_code" jdbcType="VARCHAR" property="sceneCode"/>
        <result column="scene_version" jdbcType="INTEGER" property="sceneVersion"/>
        <result column="link_map_code" jdbcType="VARCHAR" property="linkMapCode"/>
        <result column="enable" jdbcType="BIT" property="enable"/>
        <result column="link_component_code" jdbcType="VARCHAR" property="linkComponentCode"/>
        <result column="link_component_name" jdbcType="VARCHAR" property="linkComponentName"/>
        <result column="link_component_type" jdbcType="VARCHAR" property="linkComponentType"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="sequence_number" jdbcType="INTEGER" property="sequenceNumber"/>
    </resultMap>

<!--    <sql id="Base_Column_List">-->
<!--        id, product_code, scene_code, scene_version, link_map_code, enable, link_component_code,link_component_name,link_component_type,-->
<!--        creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified, sequence_number-->
<!--    </sql>-->

    <insert id="batchInsert">
        insert into tm_scene_link_info (
            product_code, scene_code, scene_version, link_map_code, enable, link_component_code,link_component_name,link_component_type,
            creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified, sequence_number
        ) values
        <foreach collection="list" item="item" separator=",">
            (#{item.productCode}, #{item.sceneCode}, #{item.sceneVersion}, #{item.linkMapCode}, #{item.enable},
            #{item.linkComponentCode}, #{item.linkComponentName}, #{item.linkComponentType},
            #{item.creatorId}, #{item.creator}, #{item.gmtCreate}, #{item.modifierId}, #{item.modifier}, #{item.gmtModified}, #{item.sequenceNumber})
        </foreach>
    </insert>

    <select id = "selectLinkCodes" resultType="java.lang.String">
        select distinct link_map_code from tm_scene_link_info where scene_code=#{sceneCode} and scene_version=#{sceneVersion};
    </select>

<!--    <select id = "selectLinkInfos" resultMap="BaseResultMap">-->
<!--        select-->
<!--            *-->
<!--        from-->
<!--            tm_scene_link_info tli-->
<!--        where-->
<!--            1=1-->
<!--        and tli.scene_code=#{sceneCode} and tli.scene_version=#{sceneVersion}-->
<!--        <if test="linkMapCodes != null and linkMapCodes.size() > 0">-->
<!--            and tli.link_map_code in-->
<!--            <foreach collection="linkMapCodes" item="item" open="(" close=")" separator=",">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </select>-->

    <select id = "selectLinkInfos" resultType="com.zto.devops.qc.infrastructure.dao.entity.SceneLinkInfoEntity">
        select
            product_code, scene_code, scene_version, link_map_code, enable, link_component_code,link_component_name,link_component_type,
            creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified, sequence_number
        from
        tm_scene_link_info
        where link_map_code=#{linkMapCode}
        order by sequence_number;
    </select>

</mapper>