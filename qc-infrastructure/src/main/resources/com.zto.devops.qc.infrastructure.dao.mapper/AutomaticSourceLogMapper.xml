<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.AutomaticSourceLogMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceLogEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="automatic_source_code" jdbcType="VARCHAR" property="automaticSourceCode" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="type" jdbcType="VARCHAR" property="type" typeHandler = "com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticRecordTypeHandler"/>
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="bucket_name" jdbcType="VARCHAR" property="bucketName" />
    <result column="fail_information" jdbcType="VARCHAR" property="failInformation" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="commit_id" jdbcType="VARCHAR" property="commitId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, code, product_code, automatic_source_code, creator_id, creator, gmt_create, modifier_id, 
    modifier, gmt_modified,address,type,fileName,bucketName,failInformation,enable,status,commit_id
  </sql>
</mapper>