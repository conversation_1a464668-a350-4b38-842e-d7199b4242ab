<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.MethodDependenceMapper">

    <insert id="batchSave" parameterType="list">
        INSERT INTO `lbd_qc`.`qc_method_dependence` (
        method_code, product_code, product_name, app_id, version_code, version_name,
        commit_id, entry_method_code, entry_method_type, parent_method_code,
        full_class_name, method_name, method_desc, method_type, method_annotation,
        method_level, method_sort, method_parameter_str, interface_full_class_name,
        zcat_metric_key, creator_id, creator, gmt_create
        ) VALUES
        <foreach collection="list" separator="," item="item" index="index">
            (
            #{item.methodCode,jdbcType=VARCHAR},
            #{item.productCode,jdbcType=VARCHAR},
            #{item.productName,jdbcType=VARCHAR},
            #{item.appId,jdbcType=VARCHAR},
            #{item.versionCode,jdbcType=VARCHAR},
            #{item.versionName,jdbcType=VARCHAR},
            #{item.commitId,jdbcType=VARCHAR},
            #{item.entryMethodCode,jdbcType=VARCHAR},
            #{item.entryMethodType,jdbcType=VARCHAR},
            #{item.parentMethodCode,jdbcType=VARCHAR},
            #{item.fullClassName,jdbcType=VARCHAR},
            #{item.methodName,jdbcType=VARCHAR},
            #{item.methodDesc,jdbcType=VARCHAR},
            #{item.methodType,jdbcType=VARCHAR},
            #{item.methodAnnotation,jdbcType=VARCHAR},
            #{item.methodLevel,jdbcType=INTEGER},
            #{item.methodSort,jdbcType=INTEGER},
            #{item.methodParameterStr,jdbcType=VARCHAR},
            #{item.interfaceFullClassName,jdbcType=VARCHAR},
            #{item.zcatMetricKey,jdbcType=VARCHAR},
            #{item.creatorId,jdbcType=BIGINT},
            #{item.creator,jdbcType=VARCHAR},
            #{item.gmtCreate,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <delete id="batchDelete">
        DELETE
        FROM
        `lbd_qc`.`qc_method_dependence`
        WHERE
        version_code = #{versionCode}
        <if test="appId != null and appId != ''">
            AND app_id = #{appId}
        </if>
        LIMIT #{limit}
    </delete>
</mapper>
