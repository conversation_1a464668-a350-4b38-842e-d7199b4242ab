<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TestcaseSortMapper">

    <select id="selectSortedCodeList" resultType="java.lang.String">
        SELECT t1.code FROM (
            SELECT
                id,
                code,
                gmt_create
            FROM tm_testcase
            WHERE enable = 1 AND automatic_source_code = '' AND parent_code = #{parentCode}
                AND product_code = #{productCode} AND type = #{type} AND attribute = 'MODULE'
        <if test="type.name() == 'AUTO'">
            UNION ALL
            SELECT
                id,
                code,
                gmt_create
            FROM tm_automatic_source_record
            WHERE enable = 1 AND product_code = #{productCode}
            <choose>
                <when test="parentCode != null and parentCode != ''">
                    AND testcase_code = #{parentCode}
                </when>
                <otherwise>
                    AND (testcase_code IS NULL OR testcase_code = '')
                </otherwise>
            </choose>
        </if>
        ) t1
        LEFT JOIN tm_testcase_sort t2 ON t2.code = t1.code
        ORDER BY ifnull(t2.sort,99999), t1.gmt_create, t1.id
    </select>

    <delete id="deleteOldSorted">
        DELETE FROM tm_testcase_sort
        WHERE code IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <insert id="insertNewSorted">
        INSERT INTO tm_testcase_sort (code, sort) VALUES
        <foreach collection="list" item="item" separator="," index="index">
            (#{item}, #{index})
        </foreach>
    </insert>

    <select id="selectSortedPlanCaseByCode" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.PlanCaseVO">
        SELECT
            t1.code AS caseCode,
            t1.parent_code AS parentCode
        FROM tm_testcase t1
        LEFT JOIN tm_testcase_sort t2 ON t1.code = t2.code
        WHERE t1.code IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ORDER BY ifnull(t2.sort,99999), t1.gmt_create, t1.id
    </select>

</mapper>