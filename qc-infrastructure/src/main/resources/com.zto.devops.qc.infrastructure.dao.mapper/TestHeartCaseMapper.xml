<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TestHeartCaseMapper">
    <select id="selectHeartByQuery" resultType="com.zto.devops.qc.infrastructure.dao.entity.TestHeartCaseEntity">
        select thc.case_code, thc.app_id, thc.creator_id, thc.creator, thc.modifier_id,
        thc.modifier, tt.name, tt.node_type, thc.warn_time, thc.app_name as appName,
        thc.app_code as appCode
        from tm_heart_case thc
        left join tm_testcase tt on tt.code = thc.case_code
        where thc.enable = true
        and tt.set_heart = true
        and tt.enable = true
        and tt.product_code = #{productCode}
        <if test="codeOrTitle!=null and codeOrTitle!=''">
            and (tt.name like "%"#{codeOrTitle}"%" or tt.code like "%"#{codeOrTitle}"%")
        </if>
        <if test="appIdList!=null  and  appIdList.size > 0">
            and thc.app_id in
            <foreach collection="appIdList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="nodeTypeList!=null  and  nodeTypeList.size > 0">
            and tt.node_type in
            <foreach collection="nodeTypeList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="modifierList!=null  and  modifierList.size > 0">
            and thc.modifier_id in
            <foreach collection="modifierList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        order by thc.gmt_create desc
    </select>
    <select id="selectRunLogByCode" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.HeartRecordInfoVO">
        SELECT
            t1.testcase_code as caseCode,
            t1.automatic_task_code,
            t1.result,
            t2.name as caseName
        FROM tm_testcase_execute_record t1
        LEFT JOIN tm_testcase t2 ON t2.code = t1.testcase_code
        LEFT JOIN tm_automatic_task t3 on t3.code = t1.automatic_task_code
        WHERE t1.enable = true
        AND t1.testcase_code = #{caseCode}
        AND t3.trig_mode = #{trigMode}
        <if test="resultList!=null  and  resultList.size > 0">
            AND t1.result IN
            <foreach collection="resultList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.start_time desc
        limit 1
    </select>
    <select id="selectAllUserByCode" resultType="com.zto.devops.qc.client.model.report.entity.SendUserInfoVO">
        select user_id, user_name
        from tm_heart_relation_user
        where case_code = #{code}
    </select>
    <select id="selectAllByCode" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.TestHeartCaseVO">
        select thc.case_code, thc.app_id, thc.app_name as appIdName, thc.app_code as appIdCode,
        thc.creator_id, thc.creator, thc.modifier_id, thc.modifier, tt.name, tt.node_type, thc.warn_time
        from tm_heart_case thc
        left join tm_testcase tt on tt.code = thc.case_code
        where thc.enable = true
        and thc.case_code = #{code}
    </select>
    <select id="selectAllHeartCase" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.TestHeartCaseVO">
        select thc.case_code, thc.app_id, thc.app_name as appIdName, thc.app_code as appIdCode,
        thc.creator_id, thc.creator, thc.modifier_id, thc.modifier, tt.name, tt.node_type, thc.warn_time
        from tm_heart_case thc
        left join tm_testcase tt on tt.code = thc.case_code
        where thc.enable = true
        <if test="productCode != null and productCode != ''">
            and tt.product_code = #{productCode}
        </if>
        <if test="userId != null">
            and (thc.creator_id = #{userId} or thc.modifier_id = #{userId})
        </if>
    </select>
    <select id="selectHeartCaseDetail" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.HeartCaseDetailVO">
        select thc.case_code, thc.app_id, thc.app_name as appIdName, thc.app_code as appIdCode,
        thc.modifier_id, thc.modifier, thc.enable, thc.creator_id, thc.creator, tt.product_code,
        tt.parent_code, tt.name as caseName, tt.type, tt.status AS testcaseStatus,
        tt.priority, tt.node_type, tt.precondition, tt.comment, tt.path, tt.duty_user_id
        from tm_heart_case thc
        left join tm_testcase tt on tt.code = thc.case_code
        where thc.enable = true
        and thc.case_code = #{code}
    </select>
    <select id="selectLastHeartResult" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteCaseVO">
        SELECT
            t1.result_file,
            t1.report_file,
            t1.exec_log_file,
            t2.task_id
        FROM tm_testcase_execute_record t1
        LEFT JOIN tm_automatic_task t2 ON t2.code = t1.automatic_task_code
        WHERE t1.testcase_code = #{caseCode}
        AND t2.trig_mode = #{trigMode}
        <if test="resultList!=null  and  resultList.size > 0">
            and t1.result in
            <foreach collection="resultList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.gmt_create DESC
        LIMIT 1
    </select>
    <delete id="deleteHeartUserByCode">
        delete from tm_heart_relation_user
        where case_code = #{code}
        <if test="userId!=null">
            and user_id = #{userId}
        </if>
    </delete>
    <insert id="addHeartRelatedUser" parameterType="com.zto.devops.qc.infrastructure.dao.entity.TestHeartRelatedUserEntity">
        insert into tm_heart_relation_user
        (case_code, user_id, user_name, creator_id, creator, modifier_id, modifier)
        values
        (#{caseCode}, #{userId}, #{userName}, #{creatorId}, #{creator},
        #{modifierId}, #{modifier})
    </insert>
    <update id="updateHeartCaseByCaseCode" parameterType="com.zto.devops.qc.infrastructure.dao.entity.TestHeartCaseEntity">
        update tm_heart_case
        <trim prefix="set" suffixOverrides=",">
            <if test="appId != null and appId != ''">
                app_id = #{appId},
            </if>
            <if test="appName != null and appName != ''">
                app_name = #{appName},
            </if>
            <if test="appCode != null and appCode != ''">
                app_code = #{appCode},
            </if>
            <if test="warnTime != null">
                warn_time = #{warnTime},
            </if>
            <if test="modifierId != null">
                modifier_id = #{modifierId},
            </if>
            <if test="modifier != null and modifier != ''">
                modifier = #{modifier},
            </if>
            gmt_modified = now()
            where case_code = #{caseCode}
        </trim>
    </update>
    <select id="selectHeartTaskByQuery" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.ListExecuteCaseVO">
        SELECT tr.testcase_code as code, tr.automatic_task_code, tr.result, tr.modifier_id, tr.modifier,
            tr.start_time, tr.finish_time, tr.exec_log_file, tr.report_file, tt.name,
            tt.attribute, tt.parent_code, tt.path, tt.sort, tt.enable
        FROM tm_testcase_execute_record tr
        LEFT JOIN tm_testcase tt ON tt.code = tr.testcase_code
        WHERE tr.automatic_task_code = #{automaticTaskCode}
        <if test="statusList!=null  and  statusList.size > 0">
            and tr.result in
            <foreach collection="statusList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTimeBegin!=null and startTimeEnd!=null">
            AND tr.start_time <![CDATA[ >= ]]> #{startTimeBegin}
            AND tr.start_time <![CDATA[ <= ]]> #{startTimeEnd}
        </if>
        <if test="finishTimeBegin!=null and finishTimeEnd!=null">
            AND tr.finish_time <![CDATA[ >= ]]> #{finishTimeBegin}
            AND tr.finish_time <![CDATA[ <= ]]> #{finishTimeEnd}
        </if>
        ORDER BY tt.sort, tt.gmt_create
    </select>
    <update id="updateHeartCaseEnable" parameterType="com.zto.devops.qc.infrastructure.dao.entity.TestHeartCaseEntity">
        update tm_heart_case
        <trim prefix="set" suffixOverrides=",">
            <if test="enable != null">
                enable = #{enable},
            </if>
            <if test="modifierId != null">
                modifier_id = #{modifierId},
            </if>
            <if test="modifier != null and modifier != ''">
                modifier = #{modifier},
            </if>
            gmt_modified = now()
            where case_code = #{caseCode}
        </trim>
    </update>

    <insert id="batchInsertHeartCase">
        insert into tm_heart_case
        (case_code,
        app_id,
        enable,
        creator_id,
        creator,
        gmt_create,
        modifier_id,
        modifier,
        gmt_modified,
        app_name,
        app_code)
        VALUES
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.caseCode},
            #{item.appId},
            #{item.enable},
            #{item.creatorId},
            #{item.creator},
            #{item.gmtCreate},
            #{item.modifierId},
            #{item.modifier},
            #{item.gmtModified},
            #{item.appName},
            #{item.appCode}
            )
        </foreach>
    </insert>

    <update id="batchUpdateHeartCaseEnable">
        update
        tm_heart_case
        set
        <if test="appId != null and appId != ''">
            app_id = #{appId},
        </if>
        <if test="appName != null and appName != ''">
            app_name = #{appName},
        </if>
        <if test="appCode != null and appCode != ''">
            app_code = #{appCode},
        </if>
        enable = #{enable},
        modifier_id = #{modifierId} ,
        modifier = #{modifier} ,
        gmt_modified = now()
        where
        1=1
        <if test="list != null and list.size > 0">
            and case_code in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="selectListByUserIdAndCaseCodeList" resultType="java.lang.String">
        select
        thc.case_code
        from tm_heart_case thc
        where
        (thc.creator_id = #{userId}
        or thc.modifier_id = #{userId})
        <if test="list != null and list.size > 0">
            and thc.case_code in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countByUserIdAndCaseCodeList" resultType="java.lang.Long">
        select
        count(thc.case_code)
        from tm_heart_case thc
        where
        (thc.creator_id = #{userId}
        or thc.modifier_id = #{userId})
        <if test="list != null and list.size > 0">
            and thc.case_code in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByCaseCode" resultType="com.zto.devops.qc.infrastructure.dao.entity.TestHeartCaseEntity">
        select
        id,case_code,app_id,app_name,app_code,warn_time,creator,creator_id,modifier,modifier_id
        from tm_heart_case thc
        where 1=1
        AND enable = 1
        AND case_code = #{caseCode}
    </select>
    <update id="updateOverTimeHeartCase" parameterType="com.zto.devops.qc.client.model.testmanager.cases.entity.ModifyOverTimeHeartResultVO">
        update tm_testcase_execute_record tt
        left join tm_automatic_task ta on ta.code = tt.automatic_task_code
        set tt.result = #{result},
        tt.modifier_id = #{modifierId},
        tt.modifier = #{modifier},
        tt.gmt_modified = #{gmtModified}
        <include refid="base_sql"></include>
    </update>
    <select id="selectOverTimeTask" resultType="java.lang.String">
        select distinct ta.code
        from tm_testcase_execute_record tt
        left join tm_automatic_task ta on ta.code = tt.automatic_task_code
        <include refid="base_sql"></include>
    </select>
    <sql id="base_sql">
        where ta.trig_mode = #{trigMode}
        and TIMESTAMPDIFF(second, tt.start_time, #{nowTime}) <![CDATA[ > ]]> #{overTimeStart}
        and TIMESTAMPDIFF(second, tt.start_time, #{nowTime}) <![CDATA[ <= ]]> #{overTimeEnd}
        <if test="statusList!=null  and  statusList.size > 0">
            and tt.result in
            <foreach collection="statusList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <update id="updateOverTimeHeartTask" parameterType="com.zto.devops.qc.client.model.testmanager.cases.entity.ModifyOverTimeHeartResultVO">
        update tm_automatic_task
        set status = #{taskStatus},
        modifier_id = #{modifierId},
        modifier = #{modifier},
        gmt_modified = #{gmtModified}
        where 1=1
        <if test="codeList!=null  and  codeList.size > 0">
            and code in
            <foreach collection="codeList" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>
