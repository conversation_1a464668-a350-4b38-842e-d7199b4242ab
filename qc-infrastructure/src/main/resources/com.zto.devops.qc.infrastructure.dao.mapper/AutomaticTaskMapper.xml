<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.AutomaticTaskMapper">
    <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.AutomaticTaskEntity">
        <id column="code" jdbcType="VARCHAR" property="code"/>
        <result column="scheduler_code" jdbcType="VARCHAR" property="schedulerCode"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="build_id" jdbcType="VARCHAR" property="buildId"/>
        <result column="automatic_source_code" jdbcType="VARCHAR" property="automaticSourceCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="source_address" jdbcType="VARCHAR" property="sourceAddress"/>
        <result column="filename" jdbcType="VARCHAR" property="filename"/>
        <result column="type" jdbcType="VARCHAR" property="type"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticRecordTypeHandler"/>
        <result column="execute_mode" jdbcType="VARCHAR" property="executeMode"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticExecuteModeTypeHandler"/>
        <result column="env" jdbcType="VARCHAR" property="env"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticStatusHandler"/>
        <result column="result_file" jdbcType="VARCHAR" property="resultFile"/>
        <result column="report_file" jdbcType="VARCHAR" property="reportFile"/>
        <result column="exec_log_file" jdbcType="VARCHAR" property="execLogFile"/>
        <result column="comment" jdbcType="VARCHAR" property="comment"/>
        <result column="test_plan_code" jdbcType="VARCHAR" property="testPlanCode"/>
        <result column="test_stage" jdbcType="VARCHAR" property="testStage"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanStageHandler"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime"/>
        <result column="enable" jdbcType="BIT" property="enable"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="case_file" jdbcType="VARCHAR" property="caseFile"/>
        <result column="version_code" jdbcType="VARCHAR" property="versionCode"/>
        <result column="trig_mode" jdbcType="VARCHAR" property="trigMode"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticTrigModeTypeHandler"/>
        <result column="coverage_flag" jdbcType="BIT" property="coverageFlag"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , code, task_id, build_id, automatic_source_code, product_code, source_address,
    filename, type, execute_mode, env, content, status, result_file, report_file,
    exec_log_file, comment, test_plan_code, test_stage, start_time, finish_time,
    enable, creator_id, creator, gmt_create, modifier_id, modifier, gmt_modified,
    case_file, version_code, trig_mode, coverage_flag
    </sql>

    <resultMap id="AutomaticTaskVO" type="com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTaskVO">
        <result column="scheduler_code" jdbcType="VARCHAR" property="schedulerCode"/>
        <result column="scheduler_name" jdbcType="VARCHAR" property="schedulerName"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticStatusHandler"/>
        <result column="report_file" jdbcType="VARCHAR" property="reportFile"/>
        <result column="exec_log_file" jdbcType="VARCHAR" property="execLogFile"/>
        <result column="test_plan_code" jdbcType="VARCHAR" property="testPlanCode"/>
        <result column="test_stage" jdbcType="VARCHAR" property="testStage"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanStageHandler"/>
        <result column="creator_id" jdbcType="BIGINT" property="executorId"/>
        <result column="creator" jdbcType="VARCHAR" property="executor"/>
        <result column="modifier_id" jdbcType="BIGINT" property="stopUserId"/>
        <result column="modifier" jdbcType="VARCHAR" property="stopUser"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime"/>
        <result column="total" jdbcType="INTEGER" property="totalCount"/>
        <result column="success" jdbcType="INTEGER" property="successCount"/>
        <result column="failed" jdbcType="INTEGER" property="failedCount"/>
        <result column="plan_name" jdbcType="VARCHAR" property="testPlanName"/>
        <result column="version_code" jdbcType="VARCHAR" property="versionCode"/>
        <result column="trig_mode" jdbcType="VARCHAR" property="trigMode"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticTrigModeTypeHandler"/>
        <result column="env" jdbcType="VARCHAR" property="env"/>
        <result column="name" jdbcType="VARCHAR" property="automaticRecord"/>
        <result column="testcase_code" jdbcType="VARCHAR" property="testcaseCode"/>
        <result column="result" jdbcType="VARCHAR" property="testcaseResult"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseStatusHandler"/>
        <result column="comment" jdbcType="VARCHAR" property="comment"/>
        <result column="error_log_file" jdbcType="VARCHAR" property="errorLogFile"/>
        <result column="build_id" jdbcType="VARCHAR" property="buildId"/>
        <result column="api_code" jdbcType="VARCHAR" property="apiCode"/>
        <result column="type" jdbcType="VARCHAR" property="type"
                typeHandler="com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticRecordTypeHandler"/>
    </resultMap>

    <select id="selectAbortAutomaticTasks" parameterType="integer" resultType="string">
        SELECT code
        FROM tm_automatic_task
        WHERE status IN ('IN_PROGRESS', 'SUBMITTED', 'NOT_STARTED')
          AND TIMESTAMPDIFF(MINUTE, gmt_create, NOW()) > #{duration}
    </select>

    <select id="selectAutomaticParentTaskIdList" resultType="java.lang.String">
        SELECT
        distinct t1.task_id
        FROM tm_automatic_task t1
        WHERE t1.product_code = #{productCode}
        <if test="executeEnvList != null and executeEnvList.size() > 0">
            AND t1.env IN
            <foreach collection="executeEnvList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="automaticCodeList != null and automaticCodeList.size() > 0">
            AND t1.automatic_source_code IN
            <foreach collection="automaticCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size() > 0">
            AND t1.status IN
            <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="versionCodeList != null and versionCodeList.size() > 0">
            AND t1.version_code IN
            <foreach collection="versionCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="testPlanCodeList != null and testPlanCodeList.size() > 0">
            AND t1.test_plan_code IN
            <foreach collection="testPlanCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="trigModeList != null and trigModeList.size() > 0">
            AND t1.trig_mode IN
            <foreach collection="trigModeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="executorIdList != null and executorIdList.size() > 0">
            AND t1.creator_id IN
            <foreach collection="executorIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTimeBegin != null">
            AND t1.start_time <![CDATA[ >= ]]> #{startTimeBegin}
        </if>
        <if test="startTimeEnd != null">
            AND t1.start_time <![CDATA[ <= ]]> #{startTimeEnd}
        </if>
        <if test="finishTimeBegin != null">
            AND t1.finish_time <![CDATA[ >= ]]> #{finishTimeBegin}
        </if>
        <if test="finishTimeEnd != null">
            AND t1.finish_time <![CDATA[ <= ]]> #{finishTimeEnd}
        </if>
        <if test="taskId != null and taskId != ''">
            AND (t1.task_id LIKE "%" #{taskId} "%" or t1.code LIKE "%" #{taskId} "%")
        </if>
        <if test="schedulerCode != null and schedulerCode != ''">
            AND t1.scheduler_code = #{schedulerCode}
        </if>
        ORDER BY any_value(t1.gmt_create) DESC
    </select>

    <select id="selectAutomaticTaskListByTaskId" resultMap="AutomaticTaskVO">
        select
        tas.scheduler_code ,
        tas.scheduler_name ,
        tat.code,
        tat.task_id,
        ifnull(tasr.name,tatc.related_api_name) name,
        tat.status,
        tat.report_file,
        tat.exec_log_file,
        tat.test_plan_code,
        ttp.plan_name,
        tat.test_stage,
        tat.creator_id,
        tat.creator,
        tat.modifier_id ,
        tat.modifier ,
        tat.start_time,
        tat.finish_time,
        tat.version_code,
        tat.trig_mode,
        tat.env,
        tat.comment,
        tat.error_log_file,
        tter.testcase_code,
        tter.result,
        tat.build_id,
        tat.type
        from
        tm_automatic_task tat
        left join tm_automatic_source_record tasr on tat.automatic_source_code = tasr.code
        left join tm_test_plan ttp on ttp.code = tat.test_plan_code
        left join tm_testcase_execute_record tter on tter.automatic_task_code = tat.code
        left join tm_automatic_scheduler tas on tat.scheduler_code = tas.scheduler_code
        left join tm_api_test_case tatc on tat.automatic_source_code = tatc.case_code and tatc.status = 'publish'
        where
        1=1
        <if test=" taskIdList != null and taskIdList.size() > 0">
            and tat.task_id in
            <foreach collection="taskIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by tat.gmt_create desc
    </select>

    <select id="selectListByTaskId" resultMap="BaseResultMap">
        select tat.code,
               tat.status,
               tat.scheduler_code,
               tat.start_time
        from tm_automatic_task tat
        where 1 = 1
          and tat.enable = true
          and tat.task_id = #{taskId}
    </select>

    <select id="listExecuteEnv" resultType="com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteTagVO">
        select distinct tat.env as env
        from tm_automatic_task tat
        where 1=1
        and tat.env is not null
        and tat.env != ''
        and tat.product_code = #{productCode}
        <if test="keyWord != null and keyWord != ''">
            and tat.env like CONCAT('%',#{keyWord},'%')
        </if>
    </select>

    <select id="selectApiTestWaitTasks" resultType="java.lang.String">
        select code from tm_automatic_task
        where status = 'NOT_STARTED' and trig_mode in ('SINGLE_API_MANUAL', 'SINGLE_API_AUTO')
        and TIMESTAMPDIFF(MINUTE, gmt_create, NOW()) <![CDATA[ < ]]> #{duration}
        order by gmt_create
    </select>
</mapper>
