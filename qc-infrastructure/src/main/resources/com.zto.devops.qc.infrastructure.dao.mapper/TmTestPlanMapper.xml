<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.devops.qc.infrastructure.dao.mapper.TmTestPlanMapper">
  <resultMap id="BaseResultMap" type="com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanEntity">
    <id column="code" jdbcType="VARCHAR" property="code" />
    <result column="dept_id" jdbcType="BIGINT" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="plan_name" jdbcType="VARCHAR" property="planName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="version_code" jdbcType="VARCHAR" property="versionCode" />
    <result column="version_name" jdbcType="VARCHAR" property="versionName" />
    <result column="test_strategy" jdbcType="VARCHAR" property="testStrategy" />
    <result column="developer_num" jdbcType="INTEGER" property="developerNum" />
    <result column="tester_num" jdbcType="INTEGER" property="testerNum" />
    <result column="access_date" jdbcType="TIMESTAMP" property="accessDate" />
    <result column="access_date_partition" jdbcType="VARCHAR" property="accessDatePartition" />
    <result column="permit_date" jdbcType="TIMESTAMP" property="permitDate" />
    <result column="permit_date_partition" jdbcType="VARCHAR" property="permitDatePartition" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="publish_date" jdbcType="TIMESTAMP" property="publishDate" />
    <result column="product_director_id" jdbcType="BIGINT" property="productDirectorId" />
    <result column="product_director_name" jdbcType="VARCHAR" property="productDirectorName" />
    <result column="test_director_id" jdbcType="BIGINT" property="testDirectorId" />
    <result column="test_director_name" jdbcType="VARCHAR" property="testDirectorName" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
    <result column="relation_plan_code" jdbcType="VARCHAR" property="relationPlanCode" />
    <result column="edit_no" jdbcType="INTEGER" property="editNo" />
    <result column="product_source" jdbcType="VARCHAR" property="productSource" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="stage_status" jdbcType="CHAR" property="stageStatus"
            typeHandler="com.zto.devops.framework.infrastructure.dao.handler.MapJsonTypeHandler"/>
  </resultMap>
  <select id="selectTestPlanList" parameterType="com.zto.devops.qc.client.model.testmanager.plan.query.PlanListQuery" resultType="com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO">

    select *, ( SELECT t2.plan_name  FROM tm_test_plan t2 where t2.code  = t.relation_plan_code)  as relationPlanName
    from tm_test_plan t
    where t.enable = 1
    <if test="planName != null and planName != '' ">
      and t.plan_name like CONCAT('%',#{planName},'%')
    </if>
    <if test="status != null and status.size > 0">
      and t.status in
      <foreach collection="status" separator="," index="index" item="item" open="(" close=")">
        #{item}
      </foreach>
    </if>
    <if test="type != null and type.size > 0">
      and t.type in
      <foreach collection="type" separator="," index="index" item="item" open="(" close=")">
        #{item}
      </foreach>
    </if>
    <if test="productCode != null ">
      and t.product_code = #{productCode}
    </if>
    <if test="productCodes!=null and productCodes.size > 0">
      and t.product_code in
      <foreach collection="productCodes" item="item" open="(" separator=","
               close=")">
        #{item}
      </foreach>
    </if>
    <if test="versionCode!=null and versionCode!=''">
      and t.version_code = #{versionCode}
    </if>
    <if test="accessTimeStart != null and accessTimeEnd != null">
      and t.access_date >= #{accessTimeStart}
      and t.access_date  <![CDATA[ < ]]>  #{accessTimeEnd}
    </if>
    <if test="permitTimeStart != null and permitTimeEnd != null">
      and t.permit_date >= #{permitTimeStart}
      and t.permit_date  <![CDATA[ < ]]>  #{permitTimeEnd}
    </if>
    <if test="planDirectorId != null and planDirectorId.size > 0">
      and t.plan_director_id in
      <foreach collection="planDirectorId" separator="," index="index" item="item" open="(" close=")">
        #{item}
      </foreach>
    </if>
    <if test="createTimeStart != null and createTimeEnd != null">
      and t.gmt_create >= #{createTimeStart}
      and t.gmt_create  <![CDATA[ < ]]>  #{createTimeEnd}
    </if>
    <if test="updateTimeStart != null and updateTimeEnd != null">
      and t.gmt_modified >= #{updateTimeStart}
      and t.gmt_modified  <![CDATA[ < ]]>  #{updateTimeEnd}
    </if>
    <if test="modifierId != null and modifierId.size > 0">
      and t.modifier_id in
      <foreach collection="modifierId" separator="," index="index" item="item" open="(" close=")">
        #{item}
      </foreach>
    </if>
    <choose>
      <when test="orderField!=null and orderField!=''">
        order by #{orderField} #{orderType}, id desc
      </when>
      <otherwise>
        order by id desc
      </otherwise>
    </choose>
  </select>

  <select id="selectByCode" resultMap="BaseResultMap">
    SELECT * FROM tm_test_plan t where t.enable = 1 and t.code = #{planCode}
  </select>

  <select id="selectByCD" parameterType="java.util.Map" resultMap="BaseResultMap">
    SELECT * FROM tm_test_plan t where t.enable = 1 and t.product_code = #{productCode} and t.version_code = #{versionCode} and type = #{type}
  </select>

  <select id="selectPageTestPlan" resultType="com.zto.devops.qc.client.model.testmanager.plan.entity.PageTestPlanBaseVO">
    select code as planCode, plan_name from tm_test_plan where enable = 1 and type = #{type}
    <if test="planName != null and planName != '' ">
      and plan_name like CONCAT('%',#{planName},'%')
    </if>
    <if test="productCode != null and productCode != '' ">
      and product_code = #{productCode}
    </if>
  </select>

  <select id="selectTestPlanByCaseCode" resultType="com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO">
    SELECT * FROM tm_test_plan s left join tm_test_plan_case c on s.code = c.plan_code
    where s.status in ("NOT_STARTED","IN_PROGRESS")  and  c.enable = 1 and c.case_code = #{caseCode}

  </select>

  <select id="selectVersionPlanList"
          parameterType="com.zto.devops.qc.client.model.testmanager.plan.query.VersionPlanQuery"
          resultType="com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanEntity">
    select
    code,status,version_code,type,test_director_id,
    test_director_name,plan_name,product_code,
    plan_director_id,plan_director_name,
    gmt_create,gmt_modified
    from tm_test_plan t
    where t.enable = 1
    <if test="versionCode != null and versionCode != '' ">
      and t.version_code = #{versionCode}
    </if>
    order by id
  </select>

</mapper>
