package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.SceneLinkInfoEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.SceneLinkInfoEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SceneLinkInfoEntityConverter {

    List<SceneLinkInfoEntity> convertSceneLinkInfoEntity(List<SceneLinkInfoEntityDO> list);
    SceneLinkInfoEntity convertSceneLinkInfoEntity(SceneLinkInfoEntityDO entityDO);

    List<SceneLinkInfoEntityDO> convertSceneLinkInfoEntityDO(List<SceneLinkInfoEntity> list);

    SceneLinkInfoEntityDO convertSceneLinkInfoEntityDO(SceneLinkInfoEntity entityDO);

}
