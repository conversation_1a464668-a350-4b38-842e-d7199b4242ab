package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.framework.infrastructure.dao.BaseMapper;
import com.zto.devops.qc.infrastructure.dao.entity.RelevantUserEntity;
import com.zto.devops.qc.client.model.parameter.TaskBaseParameter;
import com.zto.devops.qc.client.model.parameter.TaskResultParameter;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RelevantUserMapper extends BaseMapper<RelevantUserEntity> {
    List<TaskResultParameter> queryMyIssue(TaskBaseParameter taskBaseParameter);

    List<RelevantUserEntity> findByBusinessCode(@Param("businessCode") String businessCode);

    List<RelevantUserEntity> queryMyTaskTotal(@Param("userId")Long userId, @Param("type")List<String> types, @Param("issueStatuses")List<String> issueStatuses,@Param("productCode")String productCode);

    Long countUserTypeIssueCount(@Param("userId") String userId,@Param("status") List<String> status);

    List<TaskResultParameter> queryMyObj(TaskBaseParameter taskBaseParameter);
}