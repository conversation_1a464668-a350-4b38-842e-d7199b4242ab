package com.zto.devops.qc.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * API签名鉴权配置类
 * 
 * <AUTHOR>
 * @since 2025-08-14
 */
@Data
@Component
@ConfigurationProperties(prefix = "qc.api.sign.auth")
public class ApiSignAuthConfig {
    
    /**
     * 是否启用API签名鉴权
     * 默认启用
     */
    private boolean enabled = true;
    
    /**
     * 签名默认有效期（秒）
     * 默认300秒（5分钟）
     */
    private long defaultExpireSeconds = 300;
    
    /**
     * 允许的时间偏差（秒）
     * 用于处理客户端和服务端时间不同步
     * 默认60秒
     */
    private long defaultTimeSkewSeconds = 60;
    
    /**
     * Nonce缓存过期时间（秒）
     * 用于防重放攻击，应该大于等于签名有效期
     * 默认600秒（10分钟）
     */
    private long nonceCacheExpireSeconds = 600;
    
    /**
     * 是否记录鉴权日志
     * 默认true
     */
    private boolean logEnabled = true;

    /**
     * Redis缓存前缀
     */
    private String redisCachePrefix = "qc:api:sign:auth";
}
