package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TmDelayAcceptRecordEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.TmDelayAcceptRecordEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TmDelayAcceptRecordEntityConverter {
    TmDelayAcceptRecordEntityDO convert2DO(TmDelayAcceptRecordEntity tmDelayAcceptRecordEntity);

    List<TmDelayAcceptRecordEntityDO> convert2DO(List<TmDelayAcceptRecordEntity> tmDelayAcceptRecordEntity);

    TmDelayAcceptRecordEntity convert2Entity(TmDelayAcceptRecordEntityDO tmDelayAcceptRecordEntityDO);
}
