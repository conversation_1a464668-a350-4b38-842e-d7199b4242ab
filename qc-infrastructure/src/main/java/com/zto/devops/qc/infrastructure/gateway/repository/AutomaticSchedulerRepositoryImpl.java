package com.zto.devops.qc.infrastructure.gateway.repository;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.scheduler.SchedulerPreExecuteStatus;
import com.zto.devops.qc.client.model.dto.AutomaticPreExecutionEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticSchedulerEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListAutomaticTaskCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageTestcaseQuery;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.*;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.*;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.*;
import com.zto.devops.qc.domain.gateway.repository.AutomaticSchedulerRepository;
import com.zto.devops.qc.domain.gateway.util.CronUtilService;
import com.zto.devops.qc.domain.util.DateUtil;
import com.zto.devops.qc.infrastructure.converter.AutomaticSchedulerEntityConverter;
import com.zto.devops.qc.infrastructure.converter.TestcaseEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.*;
import com.zto.devops.qc.infrastructure.dao.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AutomaticSchedulerRepositoryImpl implements AutomaticSchedulerRepository {

    @Autowired
    private AutomaticSchedulerMapper automaticSchedulerMapper;

    @Autowired
    private AutomaticSchedulerCcMapper automaticSchedulerCcMapper;

    @Autowired
    private AutomaticSchedulerCaseMapper automaticSchedulerCaseMapper;

    @Autowired
    private AutomaticPreExecutionMapper automaticPreExecutionMapper;

    @Autowired
    private AutomaticSchedulerEntityConverter automaticSchedulerEntityConverter;

    @Autowired
    private AutomaticSourceRecordMapper automaticSourceRecordMapper;

    @Autowired
    private TestcaseEntityConverter testcaseEntityConverter;

    @Autowired
    private TestcaseMapper testcaseMapper;

    @Autowired
    private CronUtilService cronUtilService;

    private final Executor executor = Executors.newCachedThreadPool();

    @Override
    public void add(AutomaticSchedulerAddedEvent event) {
        log.info("AutomaticSchedulerAddedEvent >>> {}", event);
        checkSchedulerName(event.getProductCode(), event.getSchedulerName(), null);
        AutomaticSchedulerEntity entity = automaticSchedulerEntityConverter.converter(event);
        entity.setSwitchFlag(Boolean.TRUE);
        entity.preCreate(event);
        automaticSchedulerMapper.insertSelective(entity);
        if (CollectionUtils.isNotEmpty(event.getCcList())) {
            List<AutomaticSchedulerCcEntity> list = event.getCcList().stream()
                    .map(user -> {
                        AutomaticSchedulerCcEntity ccEntity = new AutomaticSchedulerCcEntity();
                        ccEntity.setSchedulerCode(event.getSchedulerCode());
                        ccEntity.setCcId(user.getUserId());
                        ccEntity.setCcName(user.getUserName());
                        ccEntity.preCreate(event);
                        return ccEntity;
                    }).collect(Collectors.toList());
            automaticSchedulerCcMapper.batchInsert(list);
        }
        executor.execute(() -> addPreExecutionScheduler(entity));
    }

    private void checkSchedulerName(String productCode, String schedulerName, String schedulerCode) {
        Example coutExample = new Example(AutomaticSchedulerEntity.class);
        Example.Criteria criteria = coutExample.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("productCode", productCode)
                .andEqualTo("schedulerName", schedulerName);
        if (StringUtils.isNotEmpty(schedulerCode)) {
            criteria.andNotEqualTo("schedulerCode", schedulerCode);
        }
        int count = automaticSchedulerMapper.selectCountByExample(coutExample);
        if (count > 0) {
            throw new ServiceException("任务名称不可重复，请重新编辑名称后再提交！");
        }
    }

    @Override
    public void updateOssTag(String productCode, String executeSpaceCode, String ossTag) {
        if (StringUtil.isBlank(productCode) || StringUtil.isBlank(executeSpaceCode)) {
            return;
        }
        Example example = new Example(AutomaticSchedulerEntity.class);
        example.createCriteria().andEqualTo("productCode", productCode).andEqualTo("executeSpaceCode", executeSpaceCode);
        AutomaticSchedulerEntity entity = new AutomaticSchedulerEntity();
        entity.setExecuteTag(ossTag);
        automaticSchedulerMapper.updateByExampleSelective(entity, example);

    }

    @Override
    public void edit(AutomaticSchedulerEditedEvent event) {

        log.info("AutomaticSchedulerEditedEvent >>> {}", event);
        checkSchedulerName(event.getProductCode(), event.getSchedulerName(), event.getSchedulerCode());
        AutomaticSchedulerEntity entity = automaticSchedulerEntityConverter.converter(event);
        entity.preUpdate(event);
        automaticSchedulerMapper.updateByPrimaryKeySelective(entity);
        if (null != event.getCcList()) {
            Example ccExample = new Example(AutomaticSchedulerCcEntity.class);
            ccExample.createCriteria().andEqualTo("schedulerCode", event.getSchedulerCode());
            automaticSchedulerCcMapper.deleteByExample(ccExample);
            if (CollectionUtils.isNotEmpty(event.getCcList())) {
                List<AutomaticSchedulerCcEntity> list = event.getCcList().stream()
                        .map(user -> {
                            AutomaticSchedulerCcEntity ccEntity = new AutomaticSchedulerCcEntity();
                            ccEntity.setSchedulerCode(event.getSchedulerCode());
                            ccEntity.setCcId(user.getUserId());
                            ccEntity.setCcName(user.getUserName());
                            ccEntity.preCreate(event);
                            return ccEntity;
                        }).collect(Collectors.toList());
                automaticSchedulerCcMapper.batchInsert(list);
            }
        }
        executor.execute(() -> {
            if (Boolean.FALSE.equals(event.getSwitchFlag())) {
                deletePreScheduler(event.getSchedulerCode());
            } else {
                addPreExecutionScheduler(entity);
            }
        });

    }

    @Override
    public void delete(AutomaticSchedulerDeletedEvent event) {
        log.info("AutomaticSchedulerDeletedEvent >>> {}", event);
        AutomaticSchedulerEntity entity = new AutomaticSchedulerEntity();
        entity.setSchedulerCode(event.getSchedulerCode());
        entity.setEnable(Boolean.FALSE);
        entity.preUpdate(event);
        automaticSchedulerMapper.updateByPrimaryKeySelective(entity);
        executor.execute(() -> {
            Example ccExample = new Example(AutomaticSchedulerCcEntity.class);
            ccExample.createCriteria().andEqualTo("schedulerCode", event.getSchedulerCode());
            automaticSchedulerCcMapper.deleteByExample(ccExample);
            Example caseExample = new Example(AutomaticSchedulerCaseEntity.class);
            caseExample.createCriteria().andEqualTo("schedulerCode", event.getSchedulerCode());
            automaticSchedulerCaseMapper.deleteByExample(caseExample);
            deletePreScheduler(event.getSchedulerCode());
        });
    }

    @Override
    public void addCases(AddSchedulerCasesEvent event) {

        log.info("AddSchedulerCasesEvent >>> {}", event);

        //校验定时任务
        Example example = new Example(AutomaticSchedulerEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("schedulerCode", event.getSchedulerCode())
                .andEqualTo("enable", Boolean.TRUE);
        AutomaticSchedulerEntity entity = automaticSchedulerMapper.selectOneByExample(example);
        if (null == entity) {
            throw new ServiceException("定时任务不存在");
        }

        //过滤已关联用例
        List<String> caseCodeList =
                automaticSchedulerCaseMapper.selectNotIn(event.getProductCode(), event.getSchedulerCode(), event.getCaseCodeList());
        if (CollectionUtils.isEmpty(caseCodeList)) {
            return;
        }

        //添加用例关联关系
        List<AutomaticSchedulerCaseEntity> caseEntityList = new ArrayList<>(caseCodeList.size());
        caseCodeList.forEach(code -> {
            AutomaticSchedulerCaseEntity caseEntity = new AutomaticSchedulerCaseEntity();
            caseEntity.setSchedulerCode(event.getSchedulerCode());
            caseEntity.setCaseCode(code);
            caseEntity.setEnable(Boolean.TRUE);
            caseEntity.preCreate(event);
            caseEntityList.add(caseEntity);
        });
        automaticSchedulerCaseMapper.insertBatch(caseEntityList);

        //更新定时任务
        AutomaticSchedulerEntity toUpdateEntity = new AutomaticSchedulerEntity();
        toUpdateEntity.setSchedulerCode(event.getSchedulerCode());
        toUpdateEntity.preUpdate(event);
        automaticSchedulerMapper.updateByPrimaryKeySelective(toUpdateEntity);

    }

    @Override
    public void removeCases(RemoveSchedulerCasesEvent event) {

        log.info("RemoveSchedulerCasesEvent >>> {}", event);

        //校验定时任务
        Example example = new Example(AutomaticSchedulerEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("schedulerCode", event.getSchedulerCode())
                .andEqualTo("enable", Boolean.TRUE);
        AutomaticSchedulerEntity entity = automaticSchedulerMapper.selectOneByExample(example);
        if (null == entity) {
            throw new ServiceException("定时任务不存在");
        }

        //删除用例关联关系
        Example caseExample = new Example(AutomaticSchedulerCaseEntity.class);
        caseExample.createCriteria().andIn("caseCode", event.getCaseCodeList())
                .andEqualTo("schedulerCode", event.getSchedulerCode());
        automaticSchedulerCaseMapper.deleteByExample(caseExample);

        //更新定时任务
        AutomaticSchedulerEntity toUpdateEntity = new AutomaticSchedulerEntity();
        toUpdateEntity.setSchedulerCode(event.getSchedulerCode());
        toUpdateEntity.preUpdate(event);
        automaticSchedulerMapper.updateByPrimaryKeySelective(toUpdateEntity);

    }

    @Override
    public List<TestcaseVO> execute(AutomaticSchedulerExecutionEvent event) {

        AutomaticSchedulerEntity entity = automaticSchedulerMapper.selectByPrimaryKey(event.getSchedulerCode());
        if (null == entity || (!entity.getSwitchFlag() && AutomaticTaskTrigModeEnum.SCHEDULER_RUN.equals(event.getTrigMode()))) {
            log.info("无定时任务或定时任务未开启！schedulerCode:{}", event.getSchedulerCode());
            return null;
        }
        if (AutomaticTaskTrigModeEnum.SCHEDULER_RUN.equals(event.getTrigMode()) && Arrays.asList(
                        AutomaticStatusEnum.NOT_STARTED, AutomaticStatusEnum.SUBMITTED, AutomaticStatusEnum.IN_PROGRESS)
                .contains(entity.getExecuteResult())) {
            log.info("定时任务未结束，此次不执行！schedulerCode:{}", event.getSchedulerCode());
            return null;
        }
        event.setExecuteEnv(entity.getExecuteEnv());
        event.setExecuteTag(entity.getExecuteTag());
        event.setCoverageFlag(entity.getCoverageFlag());
        log.info("AutomaticSchedulerExecutionEvent >>> {}", JSON.toJSONString(event));
        List<String> caseCodes = getCaseCodesBySchedulerCode(event.getSchedulerCode());
        if (CollectionUtil.isEmpty(caseCodes)) {
            log.info("定时任务无关联用例！");
            return null;
        }
        List<TestcaseVO> testcaseVOList = getTestcaseByCaseCodes(caseCodes);
        if (CollectionUtils.isEmpty(testcaseVOList)) {
            log.info("无关联用例需要执行！");
            return null;
        }
        return testcaseVOList;

    }

    @Override
    public List<String> getCaseCodesBySchedulerCode(String schedulerCode) {
        Example caseExample = new Example(AutomaticSchedulerCaseEntity.class);
        caseExample.createCriteria().andEqualTo("schedulerCode", schedulerCode)
                .andEqualTo("enable", true);
        List<AutomaticSchedulerCaseEntity> list = automaticSchedulerCaseMapper.selectByExample(caseExample);
        if (CollectionUtil.isEmpty(list)) {
            return CollectionUtil.newEmptyList();
        }
        return list.stream().map(AutomaticSchedulerCaseEntity::getCaseCode).collect(Collectors.toList());
    }

    @Override
    public List<TestcaseVO> getTestcaseByCaseCodes(List<String> caseCodes) {
        ListAutomaticTaskCaseQuery query = new ListAutomaticTaskCaseQuery();
        query.setTestcaseCodeList(caseCodes);
        List<TestcaseVO> list = query(query);
        if (CollectionUtils.isEmpty(list)) {
            return CollectionUtil.newEmptyList();
        }
        return list;
    }

    @Override
    public void updatePreExecution(AutomaticPreExecutionUpdateEvent updateEvent) {

        AutomaticPreExecutionEntity entity = new AutomaticPreExecutionEntity();
        entity.setPreStatus(SchedulerPreExecuteStatus.EXECUTED);
        Example example = new Example(AutomaticPreExecutionEntity.class);
        example.createCriteria().andEqualTo("schedulerCode", updateEvent.getSchedulerCode())
                .andEqualTo("enable", true);
        automaticPreExecutionMapper.updateByExampleSelective(entity, example);
        // 添加下一次定时任务
        executor.execute(() -> addNextPreExecutionScheduler(updateEvent.getSchedulerCode()));

    }

    @Override
    public AutomaticSchedulerDetailVO query(SchedulerDetailQuery query) {
        AutomaticSchedulerDetailVO result = new AutomaticSchedulerDetailVO();
        //基础信息
        AutomaticSchedulerEntity entity = automaticSchedulerMapper.selectBySchedulerCode(query.getSchedulerCode());
        if (null == entity) {
            return null;
        }
        BeanUtils.copyProperties(entity, result);

        //消息抄送人
        List<User> ccList = new ArrayList<>();
        Example example = new Example(AutomaticSchedulerCcEntity.class);
        example.createCriteria().andEqualTo("schedulerCode", query.getSchedulerCode())
                .andEqualTo("enable", Boolean.TRUE);
        List<AutomaticSchedulerCcEntity> ccEntities = automaticSchedulerCcMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(ccEntities)) {
            ccEntities.forEach(cc -> {
                User user = new User();
                user.setUserId(cc.getCcId());
                user.setUserName(cc.getCcName());
                ccList.add(user);
            });
        }
        result.setCcList(ccList);
        return result;
    }

    @Override
    public List<SchedulerCaseVO> query(ListSchedulerCaseQuery query) {
        if (StringUtil.isEmpty(query.getProductCode())) {
            throw new ServiceException("产品code不能为空");
        }
        if (StringUtil.isEmpty(query.getSchedulerCode())) {
            throw new ServiceException("定时任务code不能为空");
        }
        String reqParentCode = query.getParentCode();
        List<AutomaticNodeTypeEnum> nodeTypeList = query.getNodeTypeList();
        buildPageListSchedulerCaseQuery(query);
        List<SchedulerCaseVO> schedulerCaseVOS = automaticSchedulerCaseMapper.selectSchedulerCaseList(query);
        List<SchedulerCaseVO> topCaseList =
                schedulerCaseVOS.parallelStream().filter(e -> StringUtil.isEmpty(e.getCasePath()) && StringUtil.isEmpty(e.getTestcaseModulePath())).collect(Collectors.toList());
        if (StringUtil.isNotEmpty(reqParentCode) && TestcaseGroupTypeEnum.NO_GROUP.name().equals(reqParentCode)) {
            return topCaseList;
        }
        if (Boolean.TRUE.equals(query.getFilterModule())) {
            PageTestcaseQuery pageTestcaseQuery = new PageTestcaseQuery();
            pageTestcaseQuery.setProductCode(query.getProductCode());
            pageTestcaseQuery.setNodeTypeList(nodeTypeList);
            List<String> moduleCodeList = testcaseMapper.selectModuleCodeList(pageTestcaseQuery);
            query.setFilterModuleCodeList(moduleCodeList);
            schedulerCaseVOS.removeIf(item -> moduleCodeList.stream().noneMatch(s -> StringUtils.defaultString(item.getCasePath()).contains(s)));
        }
        SchedulerModuleListQuery moduleQuery = new SchedulerModuleListQuery();
        moduleQuery.setProductCode(query.getProductCode());
        moduleQuery.setParentCode(query.getParentCode());
        moduleQuery.setAutomaticTypeList(query.getAutomaticTypeList());
        List<SchedulerCaseVO> schedulerCaseVOList = automaticSchedulerCaseMapper.selectSchedulerCaseModuleList(moduleQuery);
        if (TestcaseTypeEnum.SOURCERECORD.equals(query.getType())) {
            moduleQuery.setAutomaticSourceCode(query.getParentCode());
            moduleQuery.setParentCode(StringUtils.EMPTY);
        }
        schedulerCaseVOList.addAll(automaticSchedulerCaseMapper.selectSchedulerCaseAutomaticModuleList(moduleQuery));
        List<SchedulerCaseVO> childCaseList =
                schedulerCaseVOS.stream().filter(e -> StringUtil.isNotEmpty(e.getCasePath()) || StringUtil.isNotEmpty(e.getTestcaseModulePath())).collect(Collectors.toList());
        schedulerCaseVOList = schedulerCaseVOList.parallelStream().peek(t -> {
            long childrenNum =
                    childCaseList.stream().filter(c -> c.getCasePath().contains(t.getCaseCode()) || StringUtils.defaultString(c.getTestcaseModulePath()).contains(t.getCaseCode())).count();
            t.setChildrenNum((int) childrenNum);
        }).filter(t -> t.getChildrenNum() > 0).collect(Collectors.toList());
        List<SchedulerCaseVO> treeList = buildModuleTreeByRoot(query.getParentCode(), schedulerCaseVOList);
        if (StringUtil.isNotEmpty(reqParentCode) && !TestcaseGroupTypeEnum.ALL.name().equals(reqParentCode) && !TestcaseGroupTypeEnum.NO_GROUP.name().equals(reqParentCode)) {
            List<SchedulerCaseVO> schedulerCaseList = automaticSchedulerCaseMapper.selectSchedulerCaseByParentCodeList(query);
            if (Boolean.TRUE.equals(query.getFilterModule())) {
                schedulerCaseList.removeIf(item -> query.getFilterModuleCodeList().stream().noneMatch(s -> StringUtils.defaultString(item.getCasePath()).contains(s)));
            }
            treeList.addAll(schedulerCaseList);
        }
        int moduleControllerCount = 0;
        if (StringUtils.isNotEmpty(query.getParentCode())) {
            TestcaseEntity pEntity = testcaseMapper.selectByPrimaryKey(query.getParentCode());
            if (null != pEntity && StringUtil.isNotEmpty(pEntity.getPath())) {
                List<String> codeList = new ArrayList<>(Arrays.asList(pEntity.getPath().split("\\.")));
                codeList.add(query.getParentCode());
                Example example = new Example(TestcaseEntity.class);
                example.createCriteria()
                        .andEqualTo("enable", true)
                        .andEqualTo("nodeType", AutomaticNodeTypeEnum.ModuleController)
                        .andIn("code", codeList);
                moduleControllerCount = testcaseMapper.selectCountByExample(example);
            }
        }
        Boolean enableChecked = moduleControllerCount == 0;
        treeList.forEach(t -> t.setEnableChecked(enableChecked));
        return treeList;
    }

    @Override
    public List<SchedulerCaseVO> query(SchedulerModuleListQuery query) {
        if (StringUtil.isEmpty(query.getSchedulerCode())) {
            throw new ServiceException("定时任务code不能为空");
        }
        if (StringUtil.isEmpty(query.getProductCode())) {
            throw new ServiceException("产品code不能为空");
        }
        List<SchedulerCaseVO> schedulerCaseVOS = automaticSchedulerCaseMapper.selectAllSchedulerCasePath(query);
        if (CollectionUtils.isEmpty(schedulerCaseVOS)) {
            SchedulerCaseVO vo = new SchedulerCaseVO();
            vo.setCaseName(TestcaseGroupTypeEnum.ALL.getDesc());
            vo.setChildrenNum(0);
            vo.setCaseCode(TestcaseGroupTypeEnum.ALL.name());
            vo.setChildren(Collections.emptyList());
            return Collections.singletonList(vo);
        }
        List<SchedulerCaseVO> schedulerCaseModuleVOS = automaticSchedulerCaseMapper.selectSchedulerCaseModuleList(query);
        schedulerCaseModuleVOS.addAll(automaticSchedulerCaseMapper.selectSchedulerCaseAutomaticModuleList(query));

        schedulerCaseModuleVOS.removeIf(t -> schedulerCaseVOS.stream()
                .noneMatch(c -> c.getCasePath().contains(t.getCaseCode())
                        || StringUtils.defaultString(c.getTestcaseModulePath()).contains(t.getCaseCode())));


        schedulerCaseModuleVOS.forEach(t -> {
            List<SchedulerCaseVO> children = schedulerCaseModuleVOS.parallelStream()
                    .filter(child -> child.getParentCode().equals(t.getCaseCode()))
                    .collect(Collectors.toList());
            t.setChildren(children);
            long childrenNum = schedulerCaseVOS.parallelStream()
                    .filter(c -> c.getCasePath().contains(t.getCaseCode())
                            || StringUtils.defaultString(c.getTestcaseModulePath()).contains(t.getCaseCode())
                    ).count();
            t.setChildrenNum((int) childrenNum);
        });

        List<SchedulerCaseVO> treeList = schedulerCaseModuleVOS.parallelStream()
                .filter(t -> StringUtils.EMPTY.equals(t.getParentCode()))
                .collect(Collectors.toList());

        int topChildrenNum = (int) schedulerCaseVOS.parallelStream()
                .filter(t -> StringUtils.isEmpty(t.getCasePath()) && StringUtils.isEmpty(t.getTestcaseModulePath()))
                .count();

        if (topChildrenNum > 0 && query.getCaseTypeList().contains(TestcaseTypeEnum.MANUAL)) {
            SchedulerCaseVO topCaseGroup = new SchedulerCaseVO();
            topCaseGroup.setCaseName("未分组用例");
            topCaseGroup.setChildrenNum(topChildrenNum);
            topCaseGroup.setCaseCode(TestcaseGroupTypeEnum.NO_GROUP.name());
            topCaseGroup.setCaseAttribute(TestcaseAttributeEnum.MODULE);
            treeList.add(0, topCaseGroup);
        }

        SchedulerCaseVO vo = new SchedulerCaseVO();
        vo.setCaseName(TestcaseGroupTypeEnum.ALL.getDesc());
        vo.setChildrenNum(schedulerCaseVOS.size());
        vo.setCaseCode(TestcaseGroupTypeEnum.ALL.name());
        vo.setChildren(treeList);
        return Collections.singletonList(vo);
    }

    @Override
    public List<ProductSchedulerVO> query(ProductSchedulerQuery query) {
        List<ProductSchedulerVO> voList = automaticSchedulerMapper.selectByProductCode(query.getProductCode(), query.getSchedulerName());
        return voList;
    }

    @Override
    public List<String> query(SchedulerCaseCodeListQuery query) {
        if (TestcaseGroupTypeEnum.NO_GROUP.name().equals(query.getParentCode())) {
            query.setParentCode("");
        }
        if (TestcaseGroupTypeEnum.ALL.name().equals(query.getParentCode())) {
            query.setParentCode(null);
        }
        List<AutomaticNodeTypeEnum> moduleNodeTypeList;
        if (CollectionUtils.isNotEmpty(query.getNodeTypeList())) {
            moduleNodeTypeList = query.getNodeTypeList().stream()
                    .filter(n -> TestcaseAttributeEnum.MODULE.equals(n.getAttribute()))
                    .collect(Collectors.toList());
            if (query.getNodeTypeList().contains(AutomaticNodeTypeEnum.OTHER)) {
                moduleNodeTypeList.addAll(Arrays.asList(AutomaticNodeTypeEnum.TestPlan, AutomaticNodeTypeEnum.ThreadGroup));
            }
            query.setNodeTypeList(query.getNodeTypeList().stream()
                    .filter(n -> TestcaseAttributeEnum.TESTCASE.equals(n.getAttribute()))
                    .collect(Collectors.toList()));
        }
        List<String> caseCodeList = automaticSchedulerCaseMapper.selectTestcaseCodeList(query);
        return caseCodeList;
    }

    @Override
    public PageSchedulerVO query(PageSchedulerQuery query) {
        com.github.pagehelper.Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        List<AutomaticSchedulerVO> voList = automaticSchedulerMapper.selectList(query);
        return PageSchedulerVO.builder().list(voList).total(page.getTotal()).build();
    }

    @Override
    public List<TestcaseVO> query(ListAutomaticTaskCaseQuery query) {
        log.info("ListAutomaticTaskCaseQuery >>> {}", query.getTestcaseCodeList());
        Example example1 = new Example(TestcaseEntity.class);
        example1.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("status", TestcaseStatusEnum.NORMAL)
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE)
                .andEqualTo("type", TestcaseTypeEnum.AUTO)
                .andIn("code", query.getTestcaseCodeList());
        example1.selectProperties("code", "path", "automaticSourceCode", "setHeart");
        List<TestcaseEntity> testcaseList = testcaseMapper.selectByExample(example1);
        if (CollectionUtils.isEmpty(testcaseList)) {
            return Collections.emptyList();
        }
        List<String> moduleCodeList = testcaseList.parallelStream()
                .filter(en -> StringUtils.isNotEmpty(en.getPath()))
                .flatMap(en -> Arrays.stream(en.getPath().split("\\.")))
                .distinct().collect(Collectors.toList());
        List<String> filterList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(moduleCodeList)) {
            Example example2 = new Example(TestcaseEntity.class);
            example2.createCriteria().andIn("code", moduleCodeList);
            example2.selectProperties("code", "enable", "status");
            List<TestcaseEntity> list = testcaseMapper.selectByExample(example2);
            filterList.addAll(list.parallelStream()
                    .filter(en -> !en.getEnable() || TestcaseStatusEnum.DISABLE.equals(en.getStatus()))
                    .map(TestcaseEntity::getCode)
                    .collect(Collectors.toList()));
        }
        List<String> automaticSourceCodeList = testcaseList.parallelStream()
                .map(TestcaseEntity::getAutomaticSourceCode)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(automaticSourceCodeList)) {
            Example example3 = new Example(AutomaticSourceRecordEntity.class);
            example3.createCriteria().andEqualTo("enable", Boolean.FALSE).andIn("code", automaticSourceCodeList);
            example3.selectProperties("code");
            List<AutomaticSourceRecordEntity> list = automaticSourceRecordMapper.selectByExample(example3);
            filterList.addAll(list.parallelStream().map(AutomaticSourceRecordEntity::getCode).collect(Collectors.toList()));
        }
        return testcaseList.parallelStream()
                .filter(en -> !filterList.contains(en.getAutomaticSourceCode())
                        && Arrays.stream(en.getPath().split("\\.")).noneMatch(filterList::contains))
                .map(en -> testcaseEntityConverter.converter(en))
                .collect(Collectors.toList());
    }

    private List<SchedulerCaseVO> buildModuleTreeByRoot(String root, List<SchedulerCaseVO> treeNodes) {

        List<SchedulerCaseVO> trees = new ArrayList<>();
        if (StringUtil.isNotEmpty(root)) {
            for (SchedulerCaseVO treeNode : treeNodes) {
                if (root.equals(treeNode.getParentCode())) {
                    trees.add(findChildren(treeNode, treeNodes));
                }
            }
            return trees;
        }
        for (SchedulerCaseVO treeNode : treeNodes) {
            if (StringUtil.isEmpty(treeNode.getParentCode())) {
                trees.add(findChildren(treeNode, treeNodes));
            }
        }
        return trees;
    }

    private SchedulerCaseVO findChildren(SchedulerCaseVO treeNode, List<SchedulerCaseVO> treeNodes) {
        for (SchedulerCaseVO it : treeNodes) {
            if (treeNode.getCaseCode().equals(it.getParentCode())) {
                if (treeNode.getChildren() == null) {
                    treeNode.setChildren(Collections.emptyList());
                }
                treeNode.getChildren().add(findChildren(it, treeNodes));
            }
        }
        return treeNode;
    }

    private void buildPageListSchedulerCaseQuery(ListSchedulerCaseQuery query) {
        if (StringUtils.isNotBlank(query.getSearch())) {
            String search =
                    query.getSearch().replaceAll("/", "//").replaceAll("%", "/%").replaceAll("_", "/_");
            query.setSearch(search);
        }
        if (CollectionUtils.isNotEmpty(query.getNodeTypeList())) {
            if (query.getNodeTypeList().contains(AutomaticNodeTypeEnum.OTHER)) {
                query.getNodeTypeList().addAll(Arrays.asList(AutomaticNodeTypeEnum.TestPlan, AutomaticNodeTypeEnum.ThreadGroup));
            }
            if (query.getNodeTypeList().stream().anyMatch(n -> TestcaseAttributeEnum.MODULE.equals(n.getAttribute()))) {
                query.setFilterModule(true);
            }
            query.setNodeTypeList(query.getNodeTypeList().stream().filter(n -> TestcaseAttributeEnum.TESTCASE.equals(n.getAttribute())).collect(Collectors.toList()));
        }
        if (TestcaseGroupTypeEnum.ALL.name().equals(query.getParentCode()) || TestcaseGroupTypeEnum.NO_GROUP.name().equals(query.getParentCode())) {
            query.setParentCode("");
        }
        if (CollectionUtils.isNotEmpty(query.getStatusList()) && query.getStatusList().contains(TestPlanCaseStatusEnum.PASSED)) {
            query.getStatusList().add(TestPlanCaseStatusEnum.SUCCESS);
        }
    }

    @Override
    public void updateByPrimaryKeySelective(AutomaticSchedulerEntityDO entityDO) {
        AutomaticSchedulerEntity entity = automaticSchedulerEntityConverter.covert2Entity(entityDO);
        automaticSchedulerMapper.updateByPrimaryKeySelective(entity);
    }

    private void addPreExecutionScheduler(AutomaticSchedulerEntity entity) {
        deletePreScheduler(entity.getSchedulerCode());
        AutomaticPreExecutionEntity preEntity = new AutomaticPreExecutionEntity();
        preEntity.setPreCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
        preEntity.setSchedulerCode(entity.getSchedulerCode());
        preEntity.setSchedulerName(entity.getSchedulerName());
        preEntity.setPreStatus(SchedulerPreExecuteStatus.INITIAL);
        preEntity.setExecuteTime(cronUtilService.nextTime(entity.getCrontab(), new Date()));
        preEntity.setCreator(entity.getCreator());
        preEntity.setCreatorId(entity.getCreatorId());
        preEntity.setModifier(entity.getModifier());
        preEntity.setModifierId(entity.getModifierId());
        automaticPreExecutionMapper.insertSelective(preEntity);
    }

    private void deletePreScheduler(String schedulerCode) {
        Example example = new Example(AutomaticPreExecutionEntity.class);
        example.createCriteria().andEqualTo("schedulerCode", schedulerCode)
                .andEqualTo("preStatus", SchedulerPreExecuteStatus.INITIAL);
        AutomaticPreExecutionEntity deleteEntity = new AutomaticPreExecutionEntity();
        deleteEntity.setEnable(false);
        automaticPreExecutionMapper.updateByExampleSelective(deleteEntity, example);
    }

    private void addNextPreExecutionScheduler(String schedulerCode) {
        AutomaticSchedulerEntity scheduler = automaticSchedulerMapper.selectByPrimaryKey(schedulerCode);
        try {
            addPreExecutionScheduler(scheduler);
        } catch (Exception e) {
            log.warn("添加下一次定时任务异常。schedulerCode : {}", schedulerCode);
            e.printStackTrace();
        }
    }

    @Override
    public List<AutomaticPreExecutionEntityDO> getCodeListFromPreExecute(Date currentTime) {
        Example example = new Example(AutomaticPreExecutionEntity.class);
        example.createCriteria()
                .andBetween("executeTime", DateUtil.getEndTimeOfBeforeDay(currentTime), currentTime)
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("preStatus", SchedulerPreExecuteStatus.INITIAL.name());
        List<AutomaticPreExecutionEntity> entityList = automaticPreExecutionMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(entityList)) {
            return CollectionUtil.newEmptyList();
        }
        return automaticSchedulerEntityConverter.converterList(entityList);
    }

    @Override
    public AutomaticSchedulerEntityDO selectByPrimaryKey(String code) {
        AutomaticSchedulerEntity entity = automaticSchedulerMapper.selectByPrimaryKey(code);
        return automaticSchedulerEntityConverter.converter(entity);
    }

    @Override
    public List<String> selectSchedulerByCaseCodes(List<String> caseCodes) {
        Example example = new Example(AutomaticSchedulerCaseEntity.class);
        example.createCriteria().andIn("caseCode", caseCodes);
        List<AutomaticSchedulerCaseEntity> list = automaticSchedulerCaseMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(AutomaticSchedulerCaseEntity::getSchedulerCode).distinct().collect(Collectors.toList());
    }

    @Override
    public void deleteSchedulerCaseByCodes(List<String> caseCodes) {
        Example example = new Example(AutomaticSchedulerCaseEntity.class);
        example.createCriteria().andIn("caseCode", caseCodes);
        automaticSchedulerCaseMapper.deleteByExample(example);
    }

    @Override
    public void batchInsert(List<SchedulerCaseVO> newCaseList) {
        List<AutomaticSchedulerCaseEntity> caseEntityList = new ArrayList<>();
        Date date = new Date();
        newCaseList.forEach(caseVO -> {
            AutomaticSchedulerCaseEntity caseEntity = new AutomaticSchedulerCaseEntity();
            caseEntity.setSchedulerCode(caseVO.getSchedulerCode());
            caseEntity.setCaseCode(caseVO.getCaseCode());
            caseEntity.setEnable(Boolean.TRUE);
            caseEntity.setCreator(caseVO.getCreator());
            caseEntity.setCreatorId(caseVO.getCreatorId());
            caseEntity.setModifier(caseVO.getModifier());
            caseEntity.setModifierId(caseVO.getModifierId());
            caseEntity.setGmtCreate(date);
            caseEntityList.add(caseEntity);
        });
        automaticSchedulerCaseMapper.insertBatch(caseEntityList);
    }
}
