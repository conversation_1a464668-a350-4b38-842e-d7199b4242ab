package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.client.simple.HasTransactor;
import com.zto.devops.framework.client.simple.User;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 场景/造数调试记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-18
 */
@Data
@Table(name = "tm_scene_debug_record")
public class TmSceneDebugRecordEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @Id
    private Long id;

    /**
     * code
     */
    private String recordCode;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 一站式产品编码
     */
    private String productCode;

    /**
     * 场景code
     */
    private String sceneCode;

    /**
     * 桶名称
     */
    private String bucketName;

    /**
     * 调试信息oss地址
     */
    private String logOssPath;

    /**
     * 是否删除
     */
    private Boolean enable;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新人id
     */
    private Long modifierId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModified;

    public void preCreate(HasTransactor hasTransactor) {
        User user = hasTransactor.getTransactor();
        if (null != user) {
            this.setCreatorId(user.getUserId());
            this.setCreator(user.getUserName());
            this.setGmtCreate(new Date());
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }

    public void preUpdate(HasTransactor hasTransactor) {
        User user = hasTransactor.getTransactor();
        if (null != user) {
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }

}

