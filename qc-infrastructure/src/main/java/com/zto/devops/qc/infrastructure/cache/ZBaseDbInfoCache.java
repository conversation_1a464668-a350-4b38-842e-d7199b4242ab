package com.zto.devops.qc.infrastructure.cache;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.qc.domain.gateway.zbase.ZbaseService;
import com.zto.devops.qc.infrastructure.config.QcRunnerConfig;
import com.zto.titans.common.util.RSAUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class ZBaseDbInfoCache {

    @Autowired
    private QcRunnerConfig runnerConfig;
    @Autowired
    private ZbaseService zbaseService;

    @Cacheable(value = "testCaseDbInfo", key = "'ZBase_Db_Info_'+#dbId")
    public JSONObject queryDbConfig(String dbId) {
        JSONObject physicalDbInfo = zbaseService.queryPhysicalDbInfo(dbId);
        if (Objects.isNull(physicalDbInfo)) {
            log.error("获取数据库物理信息异常.{}", dbId);
            throw new ServiceException("获取数据库物理信息异常");
        }
        JSONObject dbAccountInfo = zbaseService.queryDbAccountInfo(physicalDbInfo);
        if (Objects.isNull(dbAccountInfo) || !"SYS000".equals(dbAccountInfo.getString("statusCode"))) {
            log.error("获取数据库账户信息异常.{}, {}", physicalDbInfo, dbAccountInfo);
            throw new ServiceException("获取数据库账户信息异常");
        }
        log.info("dbAccountInfo >>> {}", dbAccountInfo.toJSONString());
        JSONObject accountInfo = dbAccountInfo.getJSONObject("result");
        if (Objects.isNull(accountInfo)) {
            String errorMsg = String.format("根据ip: %s 端口: %s, 物理库: %s, 未获取到应用账户，请联系DBA",
                    dbAccountInfo.getOrDefault("ip", ""),
                    dbAccountInfo.getOrDefault("port", ""),
                    dbAccountInfo.getOrDefault("schema", ""));
            throw new ServiceException(errorMsg);
        }

        JSONObject jdbcConfig = new JSONObject();
        String password = accountInfo.getString("password");
        String encodePassword = RSAUtils.RSAEncode(password, runnerConfig.getDbPublicKey());
        jdbcConfig.put("password", encodePassword);
        jdbcConfig.put("username", accountInfo.getString("username"));
        jdbcConfig.put("schema", dbAccountInfo.getString("schema"));
        jdbcConfig.put("port", dbAccountInfo.getString("port"));
        jdbcConfig.put("ip", dbAccountInfo.getString("ip"));
        jdbcConfig.put("dbType", dbAccountInfo.getString("dbType").toLowerCase());
        return jdbcConfig;
    }
}
