package com.zto.devops.qc.infrastructure.config;

import org.gitlab4j.api.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
public class GitlabConfig {

    @Bean("gitLabApi")
    public GitLabApi gitLabApi(GitlabProperties properties) {
        return new GitLabApi(GitLabApi.ApiVersion.V4, properties.getSeverUrl(), properties.getAccessToken());
    }

    @Bean
    @DependsOn("gitLabApi")
    public RepositoryApi repositoryApi(GitLabApi gitLabApi) {
        return gitLabApi.getRepositoryApi();
    }

    @Bean
    @DependsOn("gitLabApi")
    public ApplicationsApi applicationsApi(GitLabApi gitLabApi) {
        return gitLabApi.getApplicationsApi();
    }

    @Bean
    @DependsOn("gitLabApi")
    public CommitsApi commitsApi(GitLabApi gitLabApi) {
        return gitLabApi.getCommitsApi();
    }

    @Bean
    @DependsOn("gitLabApi")
    public EventsApi eventsApi(GitLabApi gitLabApi) {
        return gitLabApi.getEventsApi();
    }

    @Bean
    @DependsOn("gitLabApi")
    public ImportExportApi importExportApi(GitLabApi gitLabApi) {
        return gitLabApi.getImportExportApi();
    }

    @Bean
    @DependsOn("gitLabApi")
    public MergeRequestApi mergeRequestApi(GitLabApi gitLabApi) {
        return gitLabApi.getMergeRequestApi();
    }

    @Bean
    @DependsOn("gitLabApi")
    public ProjectApi projectApi(GitLabApi gitLabApi) {
        return gitLabApi.getProjectApi();
    }

    @Bean
    @DependsOn("gitLabApi")
    public TagsApi tagsApi(GitLabApi gitLabApi) {
        return gitLabApi.getTagsApi();
    }

    @Bean
    @DependsOn("gitLabApi")
    public ProtectedBranchesApi branchesApi(GitLabApi gitLabApi) {
        return gitLabApi.getProtectedBranchesApi();
    }

    @Bean
    @DependsOn("gitLabApi")
    public UserApi userApi(GitLabApi gitLabApi) {
        return gitLabApi.getUserApi();
    }
}
