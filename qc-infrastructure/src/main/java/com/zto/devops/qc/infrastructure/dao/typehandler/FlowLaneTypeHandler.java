package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import com.zto.devops.qc.client.enums.rpc.FlowLaneTypeEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

/**
 * <AUTHOR>
 * @create 2023/06/25 16:40
 */
@MappedJdbcTypes(value = JdbcType.VARCHAR, includeNullJdbcType = true)
public class FlowLaneTypeHandler extends BaseEnumTypeHandler<FlowLaneTypeEnum> {
    public FlowLaneTypeHandler() {
        super(FlowLaneTypeEnum.class);
    }
}
