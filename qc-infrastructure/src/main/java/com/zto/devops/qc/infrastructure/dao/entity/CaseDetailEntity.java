package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.report.CaseExecuteResult;
import com.zto.devops.qc.infrastructure.dao.typehandler.CaseExecuteResultHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "qc_case_detail")
public class CaseDetailEntity extends BaseEntity {
    /**
     * 计划code
     */
    @Column(name = "frog_plan_code")
    private String frogPlanCode;

    @Column(name = "frog_plan_name")
    private String frogPlanName;
    /**
     * 报告code
     */
    @Column(name = "report_code")
    private String reportCode;

    /**
     * 用例code
     */
    @Id
    private String code;


    /**
     * 编号id
     */
    @Column(name = "frog_serial_id")
    private String frogSerialId;

    /**
     * 编号
     */
    @Column(name = "serial_id")
    private String serialId;
    /**
     * 名称
     */
    private String name;

    /**
     * 执行结果 未执行；通过；失败；阻塞；跳过
     */
    @Column(name = "execute_result")
    @ColumnType(typeHandler = CaseExecuteResultHandler.class)
    private CaseExecuteResult executeResult;

    private static final long serialVersionUID = 1L;

    public CaseExecuteResult getExecuteResult() {
        if(this.executeResult==null){
            return CaseExecuteResult.UNKNOWN;
        }
        return executeResult;
    }
}