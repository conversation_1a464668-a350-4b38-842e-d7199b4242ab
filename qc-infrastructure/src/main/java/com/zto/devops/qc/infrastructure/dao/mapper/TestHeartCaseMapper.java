package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindHeartCaseByUserQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageHeartCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageHeartRunDetailQuery;
import com.zto.devops.qc.infrastructure.dao.entity.TestHeartCaseEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TestHeartRelatedUserEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TestHeartCaseMapper extends Mapper<TestHeartCaseEntity> {

    List<TestHeartCaseEntity> selectHeartByQuery(PageHeartCaseQuery query);

    HeartRecordInfoVO selectRunLogByCode(HeartResultGetVO resultGetVO);

    List<SendUserInfoVO> selectAllUserByCode(String code);

    TestHeartCaseVO selectAllByCode(String code);

    List<TestHeartCaseVO> selectAllHeartCase(FindHeartCaseByUserQuery query);

    void deleteHeartUserByCode(@Param("code") String code, @Param("userId") Long userId);

    void addHeartRelatedUser(TestHeartRelatedUserEntity entity);

    void updateHeartCaseByCaseCode(TestHeartCaseEntity entity);

    List<ListExecuteCaseVO> selectHeartTaskByQuery(PageHeartRunDetailQuery query);

    void updateHeartCaseEnable(TestHeartCaseEntity entity);

    HeartCaseDetailVO selectHeartCaseDetail(String caseCode);

    ExecuteCaseVO selectLastHeartResult(LastHeartResultVO resultVO);

    /**
     * 批量新增心跳用例记录
     *
     * @param entityList {@link TestHeartCaseEntity}
     */
    void batchInsertHeartCase(List<TestHeartCaseEntity> entityList);

    /**
     * 批量更新心跳用例记录状态
     *
     * @param enable
     * @param modifierId
     * @param modifier
     * @param list
     */
    void batchUpdateHeartCaseEnable(@Param("enable") Boolean enable, @Param("modifierId") String modifierId,
                                    @Param("modifier") String modifier, @Param("list") List<String> list,
                                    @Param("appCode") String appCode, @Param("appId") String appId, @Param("appName") String appName);

    /**
     * 筛选当前用例code中，当前用户创建或更新的心跳用例
     *
     * @param userId       用户id
     * @param caseCodeList code集合
     * @return {@link TestHeartCaseVO}
     */
    List<String> selectListByUserIdAndCaseCodeList(@Param("userId") Long userId, @Param("list") List<String> caseCodeList);


    /**
     * 筛选当前用例code中，统计当前用户创建或更新的心跳用例数
     *
     * @param userId       用户id
     * @param caseCodeList code集合
     */
    Long countByUserIdAndCaseCodeList(@Param("userId") Long userId, @Param("list") List<String> caseCodeList);

    TestHeartCaseEntity selectByCaseCode(String caseCode);

    void updateOverTimeHeartCase(ModifyOverTimeHeartResultVO resultVO);

    List<String> selectOverTimeTask(ModifyOverTimeHeartResultVO resultVO);

    void updateOverTimeHeartTask(ModifyOverTimeHeartResultVO resultVO);
}
