package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.framework.infrastructure.dao.BaseMapper;
import com.zto.devops.qc.client.enums.report.ReportType;
import com.zto.devops.qc.client.model.report.entity.BaseReportInfoVO;
import com.zto.devops.qc.client.model.report.entity.RelatedBaseVO;
import com.zto.devops.qc.client.model.report.entity.ReportTestResultMsgVO;
import com.zto.devops.qc.client.model.report.entity.ReportVO;
import com.zto.devops.qc.client.model.report.query.PageReportQuery;
import com.zto.devops.qc.client.model.report.query.RelatedQuery;
import com.zto.devops.qc.client.model.testmanager.email.entity.TestReportDetailVO;
import com.zto.devops.qc.infrastructure.dao.entity.TestReportEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TestReportMapper extends BaseMapper<TestReportEntity> {

    List<ReportVO> pageQuery(PageReportQuery query);

    List<ReportVO> versionReportQuery(@Param("versionCode") String versionCode);

    List<ReportVO> selectTestPlanMainByProductCode(String productCode);

    List<ReportVO> selectByVersionCode(String versionCode);

    List<RelatedBaseVO> getSimpleReportVOByVersionCode(@Param("versionCode") String versionCode);

    List<String> queryVersionCodesByProductCode(@Param("productCode") String productCode);

    BaseReportInfoVO selectBaseInfoVOByCode(@Param("code") String code);

    List<RelatedBaseVO> getSimpleReportVOByQuery(RelatedQuery query);

    TestReportEntity getPlanCodeAndTypeOne(@Param("planCode") String planCode, @Param("reportType") ReportType reportType);

    TestReportEntity getVersionCodeAndTypeOne(@Param("versionCode") String versionCode, @Param("reportType") ReportType reportType);

    List<ReportTestResultMsgVO> getTestReportByResult(@Param("updateTestResultDate") Date updateTestResultDate);

    List<TestReportDetailVO> selectTransferList(@Param("begin") Date begin, @Param("end") Date end);
}