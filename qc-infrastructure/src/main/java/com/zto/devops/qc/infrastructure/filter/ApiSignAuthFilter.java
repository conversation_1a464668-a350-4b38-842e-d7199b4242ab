package com.zto.devops.qc.infrastructure.filter;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.dubbo.common.extension.Activate;
import com.alibaba.dubbo.rpc.*;
import com.alibaba.fastjson.JSON;
import com.zto.devops.qc.infrastructure.auth.SignAuthContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * API签名鉴权Dubbo过滤器
 * 用于在Dubbo调用过程中传递和提取签名参数
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Slf4j
@Activate(group = {Constants.PROVIDER}, order = -9000)
public class ApiSignAuthFilter implements Filter {

    // Dubbo attachment中的签名参数key
    private static final String ACCESS_KEY = "X-API-ACCESS-KEY";
    private static final String SIGNATURE = "X-API-SIGNATURE";
    private static final String TIMESTAMP = "X-API-SIGNATURE";
    private static final String NONCE = "X-API-NONCE";
    private static final String HTTP_METHOD = "X-API-HTTP-METHOD";
    private static final String REQUEST_URI = "X-API-REQUEST-URI";
    private static final String REQUEST_BODY = "X-API-REQUEST-BODY";

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        try {
            // 从Dubbo attachment中提取签名参数
            extractSignAuthContext(invocation);

            // 继续执行调用
            return invoker.invoke(invocation);
        } finally {
            // 清理上下文
            SignAuthContext.clearContext();
        }
    }

    /**
     * 从Dubbo attachment中提取签名参数并设置到上下文
     */
    private void extractSignAuthContext(Invocation invocation) {
        Map<String, String> attachments = invocation.getAttachments();
        if (attachments == null) {
            return;
        }

        String accessKey = attachments.get(ACCESS_KEY);
        String signature = attachments.get(SIGNATURE);
        String timestampStr = attachments.get(TIMESTAMP);
        String nonce = attachments.get(NONCE);
        String httpMethod = attachments.get(HTTP_METHOD);
        String requestUri = attachments.get(REQUEST_URI);
        String requestBody = attachments.get(REQUEST_BODY);

        // 如果没有签名参数，说明不需要签名鉴权
        if (StringUtils.isBlank(accessKey) || StringUtils.isBlank(signature)) {
            return;
        }

        try {
            long timestamp = Long.parseLong(timestampStr);

            SignAuthContext context = new SignAuthContext();
            context.setAccessKey(accessKey);
            context.setSignature(signature);
            context.setTimestamp(timestamp);
            context.setNonce(nonce);
            context.setHttpMethod(httpMethod);
            context.setRequestUri(requestUri);
            context.setRequestBody(requestBody);

            SignAuthContext.setContext(context);

            log.debug("提取签名鉴权参数: accessKey={}, method={}, uri={}",
                    accessKey, httpMethod, requestUri);
        } catch (NumberFormatException e) {
            log.warn("解析时间戳失败: {}", timestampStr);
        }
    }

    /**
     * 设置签名参数到Dubbo attachment（用于客户端调用）
     */
    public static void setSignAuthAttachments(String accessKey,
                                              String signature,
                                              long timestamp,
                                              String nonce,
                                              String httpMethod,
                                              String requestUri,
                                              Object requestBody) {
        RpcContext context = RpcContext.getContext();
        context.setAttachment(ACCESS_KEY, accessKey);
        context.setAttachment(SIGNATURE, signature);
        context.setAttachment(TIMESTAMP, String.valueOf(timestamp));
        context.setAttachment(NONCE, nonce);
        context.setAttachment(HTTP_METHOD, httpMethod);
        context.setAttachment(REQUEST_URI, requestUri);

        if (requestBody != null) {
            context.setAttachment(REQUEST_BODY, JSON.toJSONString(requestBody));
        }

        log.debug("设置签名鉴权参数到Dubbo attachment: accessKey={}", accessKey);
    }
}
