package com.zto.devops.qc.infrastructure.auth;

import lombok.Data;

/**
 * 签名鉴权上下文
 * 用于在Dubbo调用过程中传递签名参数
 * 
 * <AUTHOR>
 * @since 2025-08-14
 */
@Data
public class SignAuthContext {
    
    /**
     * 访问密钥ID
     */
    private String accessKey;
    
    /**
     * 签名
     */
    private String signature;
    
    /**
     * 时间戳
     */
    private long timestamp;
    
    /**
     * 随机数
     */
    private String nonce;
    
    /**
     * HTTP方法
     */
    private String httpMethod;
    
    /**
     * 请求URI
     */
    private String requestUri;
    
    /**
     * 请求体（JSON字符串）
     */
    private String requestBody;
    
    // ThreadLocal存储当前线程的签名上下文
    private static final ThreadLocal<SignAuthContext> CONTEXT_HOLDER = new ThreadLocal<>();
    
    /**
     * 设置当前线程的签名上下文
     */
    public static void setContext(SignAuthContext context) {
        CONTEXT_HOLDER.set(context);
    }
    
    /**
     * 获取当前线程的签名上下文
     */
    public static SignAuthContext getContext() {
        return CONTEXT_HOLDER.get();
    }
    
    /**
     * 清除当前线程的签名上下文
     */
    public static void clearContext() {
        CONTEXT_HOLDER.remove();
    }
    
    /**
     * 构造授权头字符串
     */
    public String toAuthorizationHeader() {
        return SignatureUtils.buildAuthorizationHeader(accessKey, signature, timestamp, nonce);
    }
}
