package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.infrastructure.dao.entity.AttachmentEntity;
import org.mapstruct.Mapper;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring")
public interface AttachmentVOSConverter {

    AttachmentVO convert(AttachmentEntity entity);

    List<AttachmentVO> convert(Collection<AttachmentEntity> entity);

    AttachmentEntity convertVO(AttachmentVO vo);

    List<AttachmentEntity> convertVOList(List<AttachmentVO> vos);
}
