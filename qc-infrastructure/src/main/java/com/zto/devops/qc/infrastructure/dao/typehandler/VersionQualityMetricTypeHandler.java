package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import com.zto.devops.qc.client.enums.testmanager.quality.VersionQualityMetricTypeEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

@MappedJdbcTypes(value = JdbcType.VARCHAR, includeNullJdbcType = true)
public class VersionQualityMetricTypeHandler extends BaseEnumTypeHandler<VersionQualityMetricTypeEnum> {
    public VersionQualityMetricTypeHandler() {
        super(VersionQualityMetricTypeEnum.class);
    }
}
