package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TestcaseRelationEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseRelationVO;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseRelationEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TestcaseRelationEntityConverter {

    TestcaseRelationEntity convert(TestcaseRelationVO vo);

    List<TestcaseRelationEntity> convertList(List<TestcaseRelationVO> vo);

    TestcaseRelationEntityDO convertDO(TestcaseRelationEntity relationEntity);

    List<TestcaseRelationEntityDO> convertDOList(List<TestcaseRelationEntity> relationEntityList);
}
