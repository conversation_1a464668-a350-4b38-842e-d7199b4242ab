package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseEnableEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedJdbcTypes(value = JdbcType.INTEGER, includeNullJdbcType = true)
public class ApiCaseEnableTypeHandler extends BaseTypeHandler<ApiCaseEnableEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, ApiCaseEnableEnum apiCaseEnableEnum, JdbcType jdbcType) throws SQLException {
        if (null == apiCaseEnableEnum) {
            preparedStatement.setInt(i, 1);
        } else {
            preparedStatement.setInt(i, apiCaseEnableEnum.getCode());
        }
    }

    @Override
    public ApiCaseEnableEnum getNullableResult(ResultSet resultSet, String s) throws SQLException {
        int result = resultSet.getInt(s);
        return ApiCaseEnableEnum.codeOf(result);
    }

    @Override
    public ApiCaseEnableEnum getNullableResult(ResultSet resultSet, int i) throws SQLException {
        int result = resultSet.getInt(i);
        return ApiCaseEnableEnum.codeOf(result);
    }

    @Override
    public ApiCaseEnableEnum getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        int result = callableStatement.getInt(i);
        return ApiCaseEnableEnum.codeOf(result);
    }
}
