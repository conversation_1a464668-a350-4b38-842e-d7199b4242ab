package com.zto.devops.qc.infrastructure.auth;

import com.alibaba.fastjson.JSON;
import com.zto.devops.qc.infrastructure.filter.ApiSignAuthFilter;
import lombok.extern.slf4j.Slf4j;

/**
 * API签名鉴权客户端工具类
 * 用于脚本调用时生成签名并设置到Dubbo attachment
 * 
 * <AUTHOR>
 * @since 2025-08-14
 */
@Slf4j
public class ApiSignAuthClient {
    
    /**
     * 为Dubbo调用设置签名鉴权参数
     * 
     * @param accessKey 访问密钥ID
     * @param secretKey 访问密钥Secret
     * @param httpMethod HTTP方法
     * @param requestUri 请求URI
     * @param requestBody 请求体
     */
    public static void setSignAuthForDubboCall(String accessKey, String secretKey, 
                                             String httpMethod, String requestUri, Object requestBody) {
        try {
            // 生成时间戳和nonce
            long timestamp = System.currentTimeMillis();
            String nonce = SignatureUtils.generateNonce();
            
            // 生成签名
            String signature = SignatureUtils.generateSignature(
                accessKey, secretKey, httpMethod, requestUri, timestamp, nonce, requestBody
            );
            
            // 设置到Dubbo attachment
            ApiSignAuthFilter.setSignAuthAttachments(
                accessKey, signature, timestamp, nonce, httpMethod, requestUri, requestBody
            );
            
            log.info("设置API签名鉴权参数成功: accessKey={}, method={}, uri={}", 
                    accessKey, httpMethod, requestUri);
        } catch (Exception e) {
            log.error("设置API签名鉴权参数失败", e);
            throw new RuntimeException("设置API签名鉴权参数失败", e);
        }
    }
    
    /**
     * 脚本调用executeSceneDataCenter的示例
     */
    public static void exampleExecuteSceneDataCenter() {
        // 配置密钥信息
        String accessKey = "AK_SCRIPT_TEST_001";
        String secretKey = "sk_test_script_scene_data_center_secret_key_001";
        
        // 请求参数
        String httpMethod = "POST";
        String requestUri = "/tm/apitest/executeSceneDataCenter";
        
        // 请求体
        Object requestBody = JSON.parseObject("{\"sceneCode\":\"SNF979388218212352000\",\"productCode\":\"399\"}");
        
        // 设置签名鉴权参数
        setSignAuthForDubboCall(accessKey, secretKey, httpMethod, requestUri, requestBody);
        
        // 然后进行Dubbo调用
        // SceneRecordService sceneRecordService = ...;
        // Result<String> result = sceneRecordService.executeSceneDataCenter(req);
        
        log.info("脚本调用executeSceneDataCenter示例完成");
    }
    
    /**
     * 脚本调用querySceneTaskResult的示例
     */
    public static void exampleQuerySceneTaskResult() {
        // 配置密钥信息
        String accessKey = "AK_SCRIPT_TEST_001";
        String secretKey = "sk_test_script_scene_data_center_secret_key_001";
        
        // 请求参数
        String httpMethod = "POST";
        String requestUri = "/tm/apitest/querySceneTaskResult";
        
        // 请求体
        Object requestBody = JSON.parseObject("{\"taskId\":\"task_123456\"}");
        
        // 设置签名鉴权参数
        setSignAuthForDubboCall(accessKey, secretKey, httpMethod, requestUri, requestBody);
        
        // 然后进行Dubbo调用
        // SceneRecordService sceneRecordService = ...;
        // Result<SceneTaskResultResp> result = sceneRecordService.querySceneTaskResult(req);
        
        log.info("脚本调用querySceneTaskResult示例完成");
    }
}
