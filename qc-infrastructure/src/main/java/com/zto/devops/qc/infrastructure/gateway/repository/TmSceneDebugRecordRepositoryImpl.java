package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.qc.client.model.dto.TmSceneDebugRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.SaveDebugInfoCommand;
import com.zto.devops.qc.domain.gateway.repository.TmSceneDebugRecordRepository;
import com.zto.devops.qc.infrastructure.converter.TmSceneDebugRecordEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.TmSceneDebugRecordEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TmSceneDebugRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Component
@Slf4j
public class TmSceneDebugRecordRepositoryImpl implements TmSceneDebugRecordRepository {

    @Autowired
    private TmSceneDebugRecordMapper tmSceneDebugRecordMapper;

    @Autowired
    private TmSceneDebugRecordEntityConverter coverter;

    @Override
    public void insertSelective(SaveDebugInfoCommand command) {
        TmSceneDebugRecordEntity entity = buildBasicInfo(command);
        entity.preCreate(command);
        tmSceneDebugRecordMapper.insertSelective(entity);
    }

    @Override
    public void updateSelective(SaveDebugInfoCommand command) {
        TmSceneDebugRecordEntity entity = buildBasicInfo(command);
        entity.preUpdate(command);
        tmSceneDebugRecordMapper.updateByPrimaryKeySelective(entity);
    }

    private TmSceneDebugRecordEntity buildBasicInfo(SaveDebugInfoCommand command) {
        TmSceneDebugRecordEntity entity = new TmSceneDebugRecordEntity();
        entity.setRecordCode(command.getRecordCode());
        entity.setProductCode(command.getProductCode());
        entity.setSceneCode(command.getSceneCode());
        entity.setTaskId(command.getTaskId());
        entity.setBucketName(command.getBucketName());
        entity.setLogOssPath(command.getLogOssPath());
        return entity;
    }

    @Override
    public TmSceneDebugRecordEntityDO selectOne(String productCode, String sceneCode) {
        Example selectExample = new Example(TmSceneDebugRecordEntity.class);
        selectExample.createCriteria()
                .andEqualTo("productCode", productCode)
                .andEqualTo("sceneCode", sceneCode);
        selectExample.orderBy("id").desc();
        List<TmSceneDebugRecordEntity> entityList = tmSceneDebugRecordMapper.selectByExample(selectExample);
        return CollectionUtils.isEmpty(entityList) ? new TmSceneDebugRecordEntityDO() : coverter.convert(entityList.get(0));
    }

    @Override
    public TmSceneDebugRecordEntityDO selectOneByTaskId(String taskId) {
        Example selectExample = new Example(TmSceneDebugRecordEntity.class);
        selectExample.createCriteria()
                .andEqualTo("taskId", taskId);
        selectExample.orderBy("id").desc();
        List<TmSceneDebugRecordEntity> entityList = tmSceneDebugRecordMapper.selectByExample(selectExample);
        return CollectionUtils.isEmpty(entityList) ? new TmSceneDebugRecordEntityDO() : coverter.convert(entityList.get(0));
    }

    @Override
    public void updateOssPathByTaskId(String taskId, String logOssPath) {
        Example example = new Example(TmSceneDebugRecordEntity.class);
        example.createCriteria().andEqualTo("taskId", taskId);
        TmSceneDebugRecordEntity entity = new TmSceneDebugRecordEntity();
        entity.setLogOssPath(logOssPath);
        tmSceneDebugRecordMapper.updateByExampleSelective(entity, example);
    }
}
