package com.zto.devops.qc.infrastructure.dao.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;

/**
 * <AUTHOR>
@Data
@Table(name = "qc_mobile_special")
public class MobileSpecialEntity  implements Serializable {
	    /**
     * 自增ID
     */
    private Long id;

    /**
     * 业务主键
     */
    @Id
    private String code;
    /**
     * 测试主表code
     */
    private String testPlanMainCode;

    /**
     * 创建人编码
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 是否删除 1 未删除, 0 已删除
     */
    private Boolean enable;

    /**
     * 备注
     */
    private String remarks;

    private String testPlanCode;

    private static final long serialVersionUID = 1L;

  
}