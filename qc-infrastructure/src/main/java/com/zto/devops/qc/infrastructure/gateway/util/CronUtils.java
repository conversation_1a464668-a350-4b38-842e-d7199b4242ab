package com.zto.devops.qc.infrastructure.gateway.util;

import cn.hutool.cron.CronException;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.domain.gateway.util.CronUtilService;
import com.zto.devops.qc.domain.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.quartz.CronExpression;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class CronUtils implements CronUtilService {

    @Override
    public void checkCrontab(String crontab) {
        if (StringUtil.isEmpty(crontab)) {
            throw new ServiceException("cron表达式不能为空！");
        }
        isContainSecond(crontab);
        try {
            List<String> dateList = nextExecuteTimes(crontab, new Date(), 3);
            if (CollectionUtils.isNotEmpty(dateList)) {
                if (dateList.size() > 1) {
                    long diff = DateUtil.parseDateTime(dateList.get(1)).getTime() - DateUtil.parseDateTime(dateList.get(0)).getTime();
                    if (diff < 1000 * 60 * 30) {
                        throw new ServiceException("运行时间间隔不能小于30分钟！");
                    }
                }
                if (dateList.size() > 2) {
                    long diff = DateUtil.parseDateTime(dateList.get(2)).getTime() - DateUtil.parseDateTime(dateList.get(1)).getTime();
                    if (diff < 1000 * 60 * 30) {
                        throw new ServiceException("运行时间间隔不能小于30分钟！");
                    }
                }
            }
        } catch (CronException e) {
            throw new ServiceException("cron表达式有误，无法解析，请填写正确的cron表达式！", e);
        } catch (RuntimeException e) {
            throw new ServiceException("cron表达式有误，无法解析，请填写正确的cron表达式！", e);
        }
    }

    private static void isContainSecond(String crontab) {
        if (StringUtil.isEmpty(crontab)) {
            throw new ServiceException("cron表达式不能为空！");
        }
        String[] cronArrays = crontab.split(" ");
        if (null == cronArrays || cronArrays.length == 0) {
            throw new ServiceException("cron表达式不能为空！");
        }
        if (cronArrays.length == 7) {
            throw new ServiceException("cron表达式不支持配置年！");
        }
        if (cronArrays.length == 6 && !cronArrays[0].equals("0") && !cronArrays[0].equals("00")) {
            throw new ServiceException("cron表达式不支持配置秒，请配置0或00！");
        }
    }

    /**
     * 获取下一次的执行时间
     *
     * @param cron
     * @return
     * @throws ParseException
     */
    @Override
    public Date nextTime(String cron, Date date) {
        CronExpression cronExpression;
        try {
            cronExpression = new CronExpression(cron);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        Date nextTime = cronExpression.getNextValidTimeAfter(date);
        return nextTime;
    }

    /**
     * 获取n个下次执行时间,如果不足n次,则返回实际次数
     *
     * @param cron
     * @param date
     * @param n
     * @return
     * @throws ParseException
     */
    public List<String> nextExecuteTimes(String cron, Date date, int n) {
        List<String> nextTimes = new ArrayList<>();
        Date nextTime = date;
        for (int i = 0; i < n; i++) {
            Date date1 = nextTime(cron, nextTime);
            if (null == date1) {
                break;
            }
            nextTimes.add(DateUtil.formatDate(date1, DateUtil.DATETIME_FORMAT));
            nextTime = date1;
        }
        return nextTimes;
    }

}
