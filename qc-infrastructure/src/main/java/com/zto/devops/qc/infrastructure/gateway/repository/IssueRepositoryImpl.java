package com.zto.devops.qc.infrastructure.gateway.repository;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.zto.devops.framework.client.dto.Page;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.qc.client.model.dto.IssueEntityDO;
import com.zto.devops.qc.client.model.dto.StatisticsVersionIssueEntityDO;
import com.zto.devops.qc.client.model.dto.TransitionNodeEntityDO;
import com.zto.devops.qc.client.model.issue.entity.*;
import com.zto.devops.qc.client.model.issue.event.*;
import com.zto.devops.qc.client.model.issue.query.*;
import com.zto.devops.qc.client.model.parameter.IssueQueryParameter;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.devops.qc.client.model.report.entity.IssueNumStatisticsVO;
import com.zto.devops.qc.domain.converter.IssueVOConverter;
import com.zto.devops.qc.domain.gateway.repository.IIssueRepository;
import com.zto.devops.qc.domain.gateway.repository.IRelevantUserRepository;
import com.zto.devops.qc.domain.gateway.repository.TagRepository;
import com.zto.devops.qc.domain.gateway.repository.TransitionNodeRepository;
import com.zto.devops.qc.domain.model.*;
import com.zto.devops.qc.infrastructure.converter.*;
import com.zto.devops.qc.infrastructure.dao.entity.*;
import com.zto.devops.qc.infrastructure.dao.mapper.*;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/3/17
 * @Version 1.0
 */
@Component
@Slf4j
public class IssueRepositoryImpl implements IIssueRepository {

    @Autowired
    private IssueMapper issueMapper;

    @Autowired
    private IssueDOConvertor issueDOConvertor;

    @Autowired
    private IssueVOConverter issueVOConverter;

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Autowired
    private TagMapper tagMapper;

    @Autowired
    private TagRepository tagRepository;

    @Autowired
    private IRelevantUserRepository iRelevantUserRepository;

    @Autowired
    private TransitionNodeRepository transitionNodeRepository;

    @Autowired
    private CommentMapper commentMapper;

    @Autowired
    private IssueConvertor convertor;

    @Autowired
    private TagVOConverter tagconvertor;

    @Autowired
    private RelevantUserMapper relevantUserMapper;

    @Autowired
    private TransitionNodeMapper transitionNodeMapper;

    @Autowired
    private IssueEntityConverter issueEntityConverter;

    @Autowired
    private RelevantUserEntityConverter relevantUserEntityConverter;

    @Autowired
    private StatisticsVersionIssueMapper statisticsVersionIssueMapper;

    private final static IssueEntityConverter ISSUE_ENTITY_CONVERTER = IssueEntityConverter.INSTANCE;

    private final static ExecutorService EXECUTOR_SERVICE = Executors.newFixedThreadPool(9);

    @Override
    public Integer selectCountByRequirementCode(String requirementCode) {
        Example example = new Example(IssueEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", true);
        criteria.andEqualTo("requirementCode", requirementCode);
        Integer count = issueMapper.selectCountByExample(example);
        return count;
    }

    @Override
    public Page<IssueVO> pageIssueQuery(PageIssueQuery query) {
        //项目域编辑版本，根据发现版本查缺陷
        IssueQueryParameter parameter = issueDOConvertor.convert(query);
        com.github.pagehelper.Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        List<String> codeList = issueMapper.selectIssueCodeList(parameter);
        List<IssueVO> issueList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(codeList)) {
            IssueQueryParameter issueQueryParameter = new IssueQueryParameter();
            issueQueryParameter.setIssueCodeList(codeList);
            issueQueryParameter.setOrderField(query.getOrderField());
            issueQueryParameter.setOrderType(query.getOrderType());
            issueList = doSelectByDefinedQuery(issueQueryParameter);
        }

        Page<IssueVO> result = new Page<>();
        result.setList(issueList);
        result.setTotal(page.getTotal());
        return result;
    }

    @Override
    public List<RelatedMatterStatusCountVO> selectRelatedMatterStatusCount(List<String> requirementCodeList) {
        return issueDOConvertor.covertRelatedMatterStatusCountVO(issueMapper.selectRelatedMatterStatusCount(requirementCodeList));
    }

    @Override
    public List<IssueVO> listIssueForVersionQuery(ListIssueForVersionQuery query) {
        List<IssueVO> issues = new ArrayList<>();
        IssueQueryParameter issueQueryParameter =  new IssueQueryParameter();
        if (CollectionUtil.isNotEmpty(query.getTagName())){
            issueQueryParameter.setTagName(query.getTagName());
        }
        if (StringUtil.isNotEmpty(query.getFindVersionCode())) {
            //issueQueryParameter = new IssueQueryParameter();
            issueQueryParameter.setFindVersionList(Arrays.asList(query.getFindVersionCode()));
            List<IssueVO> tempIssues = issueMapper.simpleQuery(issueQueryParameter);
            if (CollectionUtil.isNotEmpty(tempIssues)) {
                issues.addAll(tempIssues);
            }
        }
        if (StringUtil.isNotEmpty(query.getFixVersionCode())) {
            //issueQueryParameter = new IssueQueryParameter();
            issueQueryParameter.setFixVersionList(Arrays.asList(query.getFixVersionCode()));
            List<IssueVO> tempIssues = issueMapper.simpleQuery(issueQueryParameter);
            if (CollectionUtil.isNotEmpty(tempIssues)) {
                issues.addAll(tempIssues);
            }
        }

        if (CollectionUtil.isNotEmpty(query.getRequirementCodeList())) {
            List<List<String>> codePartition = Lists.partition(new ArrayList(query.getRequirementCodeList()), 200);
            for (List<String> codes : codePartition) {
                if (CollectionUtil.isNotEmpty(codes)) {
                    //issueQueryParameter = new IssueQueryParameter();
                    issueQueryParameter.setRelatedRequireList(codes);
                    List<IssueVO> tempIssues = issueMapper.simpleQuery(issueQueryParameter);
                    if (CollectionUtil.isNotEmpty(tempIssues)) {
                        issues.addAll(tempIssues);
                    }
                }
            }
        }
        return issues;
    }

    @Override
    public Page<String> pageIssueCodeThingQuery(PageIssueThingQuery query) {
        Page<String> result = new Page<>();
        com.github.pagehelper.Page pager = PageHelper.startPage(query.getPage(), query.getSize());
        List<String> codeList = issueMapper.selectIssueCodeList(issueDOConvertor.covertPageIssueThingQuery(query));
        result.setList(codeList);
        result.setTotal(pager.getTotal());
        return result;
    }

    @Override
    public List<IssueBaseVO> listBySprintQuery(ListBySprintQuery query) {
        Example example = new Example(IssueEntity.class);
        Example.Criteria criteria = example.createCriteria();
        if (CollectionUtil.isNotEmpty(query.getProductCode())) {
            criteria.andIn("productCode", query.getProductCode());
        }
        if (CollectionUtil.isNotEmpty(query.getSprintCode())) {
            criteria.andIn("sprintCode", query.getSprintCode());
        }
        if (CollectionUtil.isNotEmpty(query.getCode())) {
            criteria.andIn("code", query.getCode());
        }
        if (CollectionUtil.isNotEmpty(query.getDockingUserIdList())) {
            criteria.andIn("handleUserId", query.getDockingUserIdList());
        }
        criteria.andEqualTo("enable", true);
        List<IssueEntity> issueEntities = issueMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(issueEntities)) {
            return new ArrayList<>();
        }
        return issueDOConvertor.convertIssueBaseVOList(issueEntities);
    }

    @Override
    public List<IssueEntityDO> selectBySimpleIssueQuery(SimpleIssueQuery query) {
        Example example = new Example(IssueEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", true);
        if (CollectionUtil.isNotEmpty(query.getProductCode())) {
            criteria.andIn("productCode", query.getProductCode());
        }
        if (CollectionUtil.isNotEmpty(query.getSprintCode())) {
            criteria.andIn("sprintCode", query.getSprintCode());
        }
        if (CollectionUtil.isNotEmpty(query.getRequirementCode())) {
            criteria.andIn("requirementCode", query.getRequirementCode());
        }

        if (CollectionUtil.isNotEmpty(query.getCurrentHandler())) {
            criteria.andIn("handleUserId", query.getCurrentHandler());
        }
        if (query.getCreateStart() != null && query.getCreateEnd() != null) {
            criteria.andGreaterThanOrEqualTo("gmtCreate", query.getCreateStart())
                    .andLessThanOrEqualTo("gmtCreate", query.getCreateEnd());
        }
        if (CollectionUtil.isNotEmpty(query.getCode())) {
            criteria.andIn("code", query.getCode());
        }
        if (CollectionUtil.isNotEmpty(query.getPriority())) {
            criteria.andIn("priority", query.getPriority());
        }
        if (CollectionUtil.isNotEmpty(query.getStatus())) {
            criteria.andIn("status", query.getStatus());
        }


        if (CollectionUtil.isNotEmpty(query.getFindVersionCodeList())) {
            criteria.andIn("findVersionCode", query.getFindVersionCodeList());
        }

        if (CollectionUtil.isNotEmpty(query.getVersionCodeList())) {
            criteria.andIn("fixVersionCode", query.getVersionCodeList());
        }
        Example.Criteria c2 = example.createCriteria();

        if (StringUtil.isNotBlank(query.getCodeOrTitle())) {
            c2.orLike("code", "%" + query.getCodeOrTitle() + "%")
                    .orLike("title", "%" + query.getCodeOrTitle() + "%");
        }
        example.and(c2);

        List<IssueEntity> issueEntities = issueMapper.selectByExample(example);
        return issueDOConvertor.convertDOList(issueEntities);
    }


    @Override
    public List<IssueVO> selectByDefinedQuery(IssueQueryParameter parameter) {
        //项目域删除版本时，如有关联缺陷，版本不可以删除
        List<String> codeList = issueMapper.selectIssueCodeList(parameter);
        List<IssueVO> issueVOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(codeList)) {
            parameter.setIssueCodeList(codeList);
            issueVOList = doSelectByDefinedQuery(parameter);
        }
        return issueVOList;
    }

    @Override
    public IssueEntityDO findIssueByCode(String code) {
        Example issueExample = new Example(IssueEntity.class);
        issueExample.createCriteria().andEqualTo("code", code).andEqualTo("enable", true);
        IssueEntity issueEntity = issueMapper.selectOneByExample(issueExample);
        if (issueEntity == null) {
            log.error("没有对应的缺陷数据 ，issueCode = {}", code);
            return null;
        }
        return issueDOConvertor.convertDO(issueEntity);
    }


    @Override
    public List<IssueVO> listUnClosedIssueByVersionCodes(List<String> versionCodes) {
        return issueMapper.findUnClosedByVersionCodes(versionCodes);
    }

    @Override
    public List<IssueVO> listUnClosedIssueByFixedVersionCodes(List<String> versionCodes) {
        if (CollectionUtil.isEmpty(versionCodes)) {
            return Collections.emptyList();
        }

        List<IssueVO> result = new ArrayList<>();
        List<List<String>> partition = Lists.partition(versionCodes, 200);
        for (List<String> strings : partition) {
            Example issueExample = new Example(IssueEntity.class);
            issueExample.createCriteria().andIn("fixVersionCode", strings).andEqualTo("enable", true)
                .andNotEqualTo("status", IssueStatus.CLOSED.name());
            result.addAll(issueDOConvertor.convertVOList(issueMapper.selectByExample(issueExample)));
        }
        return result;
    }

    @Override
    public List<IssueLegacyVO> listIssueLegacy(IssueLegacyListQuery query) {
        List<IssueEntity> entityList = findIssueListByFindOrFix(query.getIssueTestMethodList(),
                query.getFilterIssueTestMethodList(),
                query.getBusinessCode(),
                IssueStatus.CLOSED);
        return issueDOConvertor.convertIssueLegacyVOList(entityList);
    }

    /**
     * 根据缺陷发现或者修复版本统计缺陷数据
     *
     * @param issueTestMethodList       包含测试方法
     * @param filterIssueTestMethodList 需要过滤测试方法
     * @param businessCode              版本code
     * @param filterStatus              需要过滤的缺陷状态
     * @return {@link IssueEntity}
     */
    private List<IssueEntity> findIssueListByFindOrFix(List<IssueTestMethod> issueTestMethodList,
                                                       List<IssueTestMethod> filterIssueTestMethodList,
                                                       String businessCode,
                                                       IssueStatus filterStatus) {
        Example example = new Example(IssueEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", true);

        if (CollectionUtil.isNotEmpty(issueTestMethodList)) {
            criteria.andIn("testMethod", issueTestMethodList);
        }

        if (CollectionUtil.isNotEmpty(filterIssueTestMethodList)) {
            criteria.andNotIn("testMethod", filterIssueTestMethodList);
        }

        if (null != filterStatus) {
            criteria.andNotEqualTo("status", IssueStatus.CLOSED);
        }

        Example.Criteria criteria2 = example.createCriteria();
        criteria2.orEqualTo("findVersionCode", businessCode)
                .orEqualTo("fixVersionCode", businessCode);
        example.and(criteria2);

        return issueMapper.selectByExample(example);
    }

    @Override
    public IssueNumStatisticsVO getIssueNumStatistic(IssueNumStatisticQuery query) {
        IssueNumStatisticsVO result = IssueNumStatisticsVO.init();
        if (null == query || StringUtil.isBlank(query.getBusinessCode())) {
            return IssueNumStatisticsVO.init();
        }
        //缺陷总数
        List<IssueEntity> entityList = findIssueListByFindOrFix(query.getIssueTestMethodList(),
                query.getFilterIssueTestMethodList(),
                query.getBusinessCode(), null);
        if (CollectionUtil.isEmpty(entityList)) {
            return result;
        }
        result.setIssueCount(entityList.size());

        // 有效缺陷
        Integer invalidIssueCount = getInvalidIssueCount(query.getBusinessCode());
        result.setValidIssueCount(result.getIssueCount() - invalidIssueCount);

        // 遗留缺陷
        List<IssueEntity> legacyIssueList = entityList.stream().filter(entity -> (!IssueStatus.CLOSED.equals(entity.getStatus()))).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(legacyIssueList)) {
            return result;
        }
        result.setLegacyIssueCount(legacyIssueList.size());

        // 遗留P0、P1、P2缺陷
        List<IssueEntity> highLegacyIssueList = legacyIssueList.stream().filter(
                entity -> (IssuePriority.URGENCY.equals(entity.getPriority())
                        || IssuePriority.HIGH.equals(entity.getPriority())
                        || IssuePriority.MIDDLE.equals(entity.getPriority()))
        ).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(highLegacyIssueList)) {
            return result;
        }
        result.setLegacyIssueHigh(CollectionUtil.isNotEmpty(highLegacyIssueList) ? highLegacyIssueList.size() : 0);
        return result;
    }

    @Override
    public IssueNumStatisticsVO getUiTestIssueNumStatistic(IssueNumStatisticQuery query) {
        IssueNumStatisticsVO result = IssueNumStatisticsVO.init();
        if (null == query || StringUtil.isBlank(query.getBusinessCode())) {
            return IssueNumStatisticsVO.init();
        }
        //缺陷总数
        List<IssueEntity> totalEntityList = findIssueListByFindOrFix(query.getIssueTestMethodList(),
                query.getFilterIssueTestMethodList(),
                query.getBusinessCode(), null);
        if (CollectionUtil.isEmpty(totalEntityList)) {
            return result;
        }
        //用户体验缺陷
        List<IssueEntity> uiTestEntityList = totalEntityList.stream().filter(t->IssueType.USER_EXPERIENCE_BUG.equals(t.getType())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(uiTestEntityList)) {
            return result;
        }

        result.setIssueCount(uiTestEntityList.size());
        // 有效用户体验缺陷
        Integer invalidIssueCount = getInvalidUiTestIssueCount(query.getBusinessCode());
        result.setValidIssueCount(result.getIssueCount() - invalidIssueCount);

        // 遗留用户体验缺陷
        List<IssueEntity> legacyIssueList = uiTestEntityList.stream().filter(entity -> (!IssueStatus.CLOSED.equals(entity.getStatus()))).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(legacyIssueList)) {
            return result;
        }
        result.setLegacyIssueCount(legacyIssueList.size());

        // 遗留P0、P1、P2用户体验缺陷
        List<IssueEntity> highLegacyIssueList = legacyIssueList.stream().filter(
                entity -> (IssuePriority.URGENCY.equals(entity.getPriority())
                        || IssuePriority.HIGH.equals(entity.getPriority())
                        || IssuePriority.MIDDLE.equals(entity.getPriority()))
        ).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(highLegacyIssueList)) {
            return result;
        }
        result.setLegacyIssueHigh(CollectionUtil.isNotEmpty(highLegacyIssueList) ? highLegacyIssueList.size() : 0);
        return result;
    }

    @Override
    public Issue loadFormDb(String aggregateIdentifier) {
        IssueEntity issueEntity = issueMapper.selectByPrimaryKey(aggregateIdentifier);
        Issue issue = convertor.convert(issueEntity);
        //附件
        CompletableFuture<List<Attachment>> attachmentCompletableFuture = CompletableFuture.supplyAsync(() -> {
            Example attachmentExample = new Example(AttachmentEntity.class);
            attachmentExample.createCriteria().andEqualTo("businessCode", aggregateIdentifier)
                    .andEqualTo("enable", true);
            List<AttachmentEntity> attachmentEntityList = attachmentMapper.selectByExample(attachmentExample);
            return convertor.convertList(attachmentEntityList);
        }, EXECUTOR_SERVICE).whenComplete((i, k) -> issue.setAttachments(i));
        //tag
        CompletableFuture<List<Tag>> tagCompletableFuture = CompletableFuture.supplyAsync(() -> {
            Example attachmentExample = new Example(AttachmentEntity.class);
            attachmentExample.createCriteria().andEqualTo("businessCode", aggregateIdentifier)
                    .andEqualTo("enable", true);
            List<TagEntity> tagEntityList = tagMapper.selectByExample(attachmentExample);
            return tagconvertor.convertList(tagEntityList);
        }, EXECUTOR_SERVICE).whenComplete((i, k) -> issue.setTags(i));

        //评论
        CompletableFuture<Set<Comment>> commentCompletableFuture = CompletableFuture.supplyAsync(() -> {
            Example comment = new Example(CommentEntity.class);
            comment.createCriteria().andEqualTo("businessCode", aggregateIdentifier).andEqualTo("enable", true);
            List<CommentEntity> commentEntities = commentMapper.selectByExample(comment);
            return convertor.convertCommentList(commentEntities);
        }, EXECUTOR_SERVICE).whenComplete((i, k) -> issue.setComments(i));

        //干系人
        CompletableFuture<Set<RelevantUser>> relevantCompletableFuture = CompletableFuture.supplyAsync(() -> {
            Example comment = new Example(CommentEntity.class);
            comment.createCriteria().andEqualTo("businessCode", aggregateIdentifier).andEqualTo("enable", true);
            List<RelevantUserEntity> relevantUserEntities = relevantUserMapper.selectByExample(comment);
            return relevantUserEntityConverter.convertList(relevantUserEntities);
        }, EXECUTOR_SERVICE).whenComplete((i, k) -> {
            issue.setRelevantUsers(i);
            Set<RelevantUser> currentHandler = i.stream().filter(v -> v.getType().equals(RelevantUserTypeEnum.CURRENT_HANDLE_USER)).collect(Collectors.toSet());
            issue.setCurrentHandler(currentHandler);
        });

        CompletableFuture allFuture = CompletableFuture.allOf(attachmentCompletableFuture, tagCompletableFuture,
                relevantCompletableFuture, commentCompletableFuture);
        allFuture.join();
        return issue;
    }


    @Override
    public void addIssue(IssueAddedEvent event) {
        IssueEntity entity = issueEntityConverter.convert(event);
        entity.preCreate(event);
        issueMapper.insertSelective(entity);
    }

    @Override
    public void updateIssue(IssueDelayFixedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
        entity.setHandleUserId(event.getTransactor().getUserId());
        entity.setHandleUserName(event.getTransactor().getUserName());
        entity.setGmtModified(event.getOccurred());
        entity.setUpdateTime(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setDelayFixTime(event.getDelayFixTime());
        entity.setFixVersionCode(event.getFixVersion().getCode());
        entity.setFixVersionName(event.getFixVersion().getName());

        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateIssue(IssueBackToRepairedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
        entity.setGmtModified(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        if (null != event.getHandler()) {
            entity.setHandleUserName(event.getHandler().getUserName());
            entity.setHandleUserId(event.getHandler().getUserId());
        }
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateIssue(IssueCirculationedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());

        if (null != event.getTestId()) {
            entity.setTestUserId(event.getTestId());
            entity.setTestUserName(event.getTestName());
        }
        if (null != event.getDevelopId()) {
            entity.setDevelopUserId(event.getDevelopId());
            entity.setDevelopUserName(event.getDevelopName());
        }
        if (null != event.getHandler()) {
            entity.setHandleUserName(event.getHandler().getUserName());
            entity.setHandleUserId(event.getHandler().getUserId());
        }

        entity.setGmtModified(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateIssue(IssueConfirmClosedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
        entity.setGmtModified(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setCloseTime(event.getCloseTime());
        entity.setIsValid(event.getIsValid());

        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public PageIssueVO pageIssue(IssueQueryParameter parameter, int pageNum, int size) {
        PageIssueVO listIssueVO = new PageIssueVO();
        try {
            log.info("IssueQueryParameter" + JsonUtil.toJSON(parameter));
            com.github.pagehelper.Page<Object> page = PageHelper.startPage(pageNum, size);
            List<String> codeList = issueMapper.selectIssueCodeList(parameter);
            List<IssueVO> issueVOList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(codeList)) {
                parameter.setIssueCodeList(codeList);
                issueVOList = doSelectByDefinedQuery(parameter);
            }
            listIssueVO.setList(issueVOList);
            listIssueVO.setTotal(page.getTotal());
        } catch (Exception e) {
            log.error("PageIssueQuery is error {}", e.toString());
        }
        return listIssueVO;
    }

    @Override
    public List<IssueVO> findIssueVOList(PageLaneIssueQuery query, IssueQueryParameter parameter) {
        List<IssueVO> tempIssueVOList = null;
        com.github.pagehelper.Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        List<String> codeList = issueMapper.selectIssueCodeList(parameter);
        parameter.setStatusList(query.getStatusList());
        if (CollectionUtil.isNotEmpty(codeList)) {
            IssueQueryParameter issueQueryParameter = new IssueQueryParameter();
            issueQueryParameter.setIssueCodeList(codeList);
            issueQueryParameter.setOrderField(query.getOrderField());
            issueQueryParameter.setOrderType(query.getOrderType());
            tempIssueVOList = doSelectByDefinedQuery(issueQueryParameter);
        }
        return tempIssueVOList;
    }

    public List<IssueVO> doSelectByDefinedQuery(IssueQueryParameter issueQueryParameter) {
        List<IssueVO> result = findByIssueCodeList(issueQueryParameter);
        if (CollectionUtil.isEmpty(result)) {
            return result;
        }

        //tagName
        List<TagVO> tagVOList = tagRepository.selectByIssueCodeList(issueQueryParameter.getIssueCodeList());
        Map<String, List<TagVO>> tagMap = CollectionUtil.isNotEmpty(tagVOList) ? tagVOList.stream().collect(Collectors.groupingBy(TagVO::getBusinessCode)) : null;

        //ccUsername
        List<RelevantUserVO> relevantUserList = iRelevantUserRepository.queryCCByIssueCodeList(issueQueryParameter.getIssueCodeList());
        Map<String, List<RelevantUserVO>> relevantUserMap = CollectionUtil.isNotEmpty(relevantUserList) ? relevantUserList.stream().collect(Collectors.groupingBy(RelevantUserVO::getBusinessCode)) : null;

        //rejectReason
        List<TransitionNodeEntityDO> transitionNodeList = transitionNodeRepository.queryRejectReasonByIssueCodeList(issueQueryParameter.getIssueCodeList());
        Map<String, List<TransitionNodeEntityDO>> transitionNodeMap = CollectionUtil.isNotEmpty(transitionNodeList) ? transitionNodeList.stream().collect(Collectors.groupingBy(TransitionNodeEntityDO::getBusinessCode)) : null;

        result.forEach(issue -> fillIssueData(issue, tagMap, relevantUserMap, transitionNodeMap));
        result.sort(IssueFieldEnum.getComparatorByOrderField(issueQueryParameter.getOrderField(), issueQueryParameter.getOrderType()));
        return result;
    }

    private List<IssueVO> findByIssueCodeList(IssueQueryParameter issueQueryParameter) {
        Example example = new Example(IssueEntity.class);
        example.orderBy("code");
        example.createCriteria().andEqualTo("enable", Boolean.TRUE)
                .andIn("code", issueQueryParameter.getIssueCodeList());
        List<IssueEntity> entityList = issueMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? Collections.emptyList() : issueDOConvertor.convertVOList(entityList);
    }

    private void fillIssueData(IssueVO issueVO, Map<String, List<TagVO>> tagMap, Map<String, List<RelevantUserVO>> relevantUserMap, Map<String, List<TransitionNodeEntityDO>> transitionNodeMap) {
        if (MapUtils.isNotEmpty(tagMap) && tagMap.containsKey(issueVO.getCode())) {
            issueVO.setTagName(Strings.join(tagMap.get(issueVO.getCode()).stream().map(TagVO::getTagName).collect(Collectors.toSet()), ','));
        }
        if (MapUtils.isNotEmpty(relevantUserMap) && relevantUserMap.containsKey(issueVO.getCode())) {
            issueVO.setCcUserName(Strings.join(relevantUserMap.get(issueVO.getCode()).stream().map(RelevantUserVO::getUserName).collect(Collectors.toSet()), ','));
        }
        if (MapUtils.isNotEmpty(transitionNodeMap) && transitionNodeMap.containsKey(issueVO.getCode())) {
            List<TransitionNodeEntityDO> transitionNodeEntityDOList = transitionNodeMap.get(issueVO.getCode());
            if (transitionNodeEntityDOList.size() > 1) {
                //拒绝多次，取最后一次拒绝原因
                transitionNodeEntityDOList = transitionNodeEntityDOList.stream().sorted(Comparator.comparing(TransitionNodeEntityDO::getGmtCreate, Comparator.nullsFirst(Comparator.naturalOrder())).reversed()).collect(Collectors.toList());
            }
            issueVO.setRefuseReason(RefuseReason.valueOf(transitionNodeEntityDOList.get(0).getReason().name()));
        }
    }

    @Override
    public List<String> listStatusByDefinedQuery(IssueQueryParameter parameter) {
        return issueMapper.listStatusByDefinedQuery(parameter);
    }

    @Override
    public void updateIssue(IssueRefusedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
        entity.setGmtModified(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setRejectTime(event.getRejectTime());
        if (event.getHandler() != null) {
            entity.setHandleUserId(event.getHandler().getUserId());
            entity.setHandleUserName(event.getHandler().getUserName());
        }
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void removeIssue(IssueRemovedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
        entity.setEnable(Boolean.FALSE);
        entity.setUpdateTime(event.getOccurred());
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 获取无效缺陷数
     *
     * @param versionCode 版本code
     * @return 无效缺陷数
     */
    private Integer getInvalidIssueCount(String versionCode) {
        if (StringUtil.isBlank(versionCode)) {
            return 0;
        }
        Integer result = transitionNodeMapper.countInvalidCount(versionCode);
        return null == result ? 0 : result;
    }

    /**
     * 获取无效用户体验缺陷数
     *
     * @param versionCode 版本code
     * @return 无效用户体验缺陷数
     */
    private Integer getInvalidUiTestIssueCount(String versionCode) {
        if (StringUtil.isBlank(versionCode)) {
            return 0;
        }
        Integer result = transitionNodeMapper.countInvalidUiTestCount(versionCode);
        return null == result ? 0 : result;
    }

    @Override
    public List<IssueEntityDO> selectByProductCodeAndCode(String productCode, String sprintCode) {
        Example example = new Example(IssueEntity.class);
        example.createCriteria().andEqualTo("productCode", productCode)
                .andEqualTo("sprintCode", sprintCode);
        List<IssueEntity> issueEntities = issueMapper.selectByExample(example);
        return issueDOConvertor.convertDOList(issueEntities);
    }

    @Override
    public void updateBySprintInIssueEditedEvent(SprintInIssueEditedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setSprintCode(event.getSprint().getCode());
        if (event.getSprint().getCode().equals("NotAssociated")) {
            entity.setSprintName("");
        }
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateBySprintIssueLinkedEvent(SprintIssueLinkedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setSprintCode(event.getSprintCode());
        if (event.getSprintCode().equals("NotAssociated")) {
            entity.setSprintName("");
        }
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateIssue(IssueRelatedRequirementEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getIssueCode());
        entity.setRequirementCode(event.getRequirementCode());
        entity.setRequirementLevel(event.getRequirementLevel());
        if (null != event.getFixVersion()) {
            entity.setFixVersionCode(event.getFixVersion().getCode());
            entity.setFixVersionName(event.getFixVersion().getName());
        }
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void editIssue(IssueEditedEvent event) {
        IssueEntity entity = ISSUE_ENTITY_CONVERTER.convert(event);
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void deliveryIssue(IssueDeliveryValidatedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
        entity.setFixVersionCode(event.getFixVersion().getCode());
        entity.setFixVersionName(event.getFixVersion().getName());
        entity.setGmtModified(event.getOccurred());
        entity.setUpdateTime(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setDeliverTime(event.getDeliverTime());
        entity.setHandleUserId(event.getHandler().getUserId());
        entity.setHandleUserName(event.getHandler().getUserName());
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateIssueFixVersionFiled(List<String> issueCodes, String versionCode, String versionName, String confirm) {
        if (CollectionUtil.isEmpty(issueCodes)) {
            return;
        }
        Example example = new Example(IssueEntity.class);
        example.createCriteria().andIn("code", issueCodes).andEqualTo("enable", true);
        IssueEntity issueEntity = new IssueEntity();
        issueEntity.setVersionConfirm(confirm);
        issueEntity.setFixVersionCode(versionCode);
        issueEntity.setFixVersionName(versionName);
        issueMapper.updateByExampleSelective(issueEntity, example);
    }

    @Override
    public List<Map<Long, Long>> queryIssueGroupByHandle(String status) {
        List<Map<Long, Long>> mapList = issueMapper.queryIssueGroupByHandle(status);
        return mapList;
    }

    @Override
    public IssueEntityDO selectByPrimaryKey(String code) {
        IssueEntity entity = issueMapper.selectByPrimaryKey(code);
        return convertor.convert2DO(entity);
    }

    @Override
    public List<IssueEntityDO> selectByProductCodeAndFindStageAndGmtCreate(List<String> productCode, IssueFindStage findStage, Date startDate, Date endDate) {
        Example example = new Example(IssueEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", true);
        criteria.andBetween("gmtCreate", startDate, endDate);
        criteria.andIn("productCode", productCode);
        criteria.andEqualTo("findStage", findStage);
        List<IssueEntity> entityList = issueMapper.selectByExample(example);
        return convertor.convert2DOList(entityList);
    }

    @Override
    public List<IssueEntityDO> selectByVersionCode(String versionCode) {
        Example example = new Example(IssueEntity.class);
        example.createCriteria()
                .andEqualTo("findVersionCode", versionCode)
                .andEqualTo("enable", true);
        List<IssueEntity> entityList = issueMapper.selectByExample(example);
        return convertor.convert2DOList(entityList);
    }

    @Override
    public List<TransitionNodeEntityDO> selectIssueReopenList(List<String> businessCodes) {
        return convertor.convert2TransitionNodeEntityDOList(transitionNodeMapper.selectIssueReopenList(businessCodes));
    }

    @Override
    public List<TransitionNodeEntityDO> listIssueTransitionNode(List<String> issueCodes) {
        Example example = new Example(TransitionNodeEntity.class);
        example.orderBy("gmtCreate").desc();
        example.createCriteria()
                .andIn("businessCode", issueCodes)
                .andEqualTo("domain", DomainEnum.ISSUE.name())
                .andEqualTo("enable", true);
        return convertor.convert2TransitionNodeEntityDOList(transitionNodeMapper.selectByExample(example));
    }

    @Override
    public StatisticsVersionIssueEntityDO selectStatisticsVersionIssueEntity(String versionCode) {
        StatisticsVersionIssueEntity entity = statisticsVersionIssueMapper.selectByPrimaryKey(versionCode);
        return convertor.convert(entity);
    }

    @Override
    public void updateStatisticsVersionIssueEntity(StatisticsVersionIssueEntityDO entityDO) {
        statisticsVersionIssueMapper.updateByPrimaryKeySelective(convertor.convert(entityDO));
    }

    @Override
    public void insertStatisticsVersionIssueEntity(StatisticsVersionIssueEntityDO entityDO) {
        statisticsVersionIssueMapper.insertSelective(convertor.convert(entityDO));
    }

    @Override
    public List<DevThingVO> listIssueByHandleUserId(Long handleUserId) {
        List<DevThingVO> list = new ArrayList<>();
        List<IssueVO> entityList = issueMapper.listIssueByHandleUserId(handleUserId);
        entityList.forEach(t->{
            DevThingVO vo = new DevThingVO();
            vo.setDomain(DomainEnum.ISSUE.name());
            vo.setBusinessCode(t.getCode());
            vo.setBusinessName(t.getTitle());
            vo.setHandlerId(t.getHandleUserId());
            vo.setHandlerName(t.getHandleUserName());
            vo.setProductCode(t.getProductCode());
            vo.setProductName(t.getProductName());
            list.add(vo);
        });
        return list;
    }

    @Override
    public void updateIssueHandleUserId(List<DevThingUpdateVO> vos) {
        if (CollectionUtil.isEmpty(vos)){
            return;
        }
        vos.forEach(t->{
            try {
                log.info("变更缺陷处理人{}-{}-{}",t.getBusinessCode(),t.getOldId(),t.getNewId());
                IssueEntity entity = issueMapper.selectByPrimaryKey(t.getBusinessCode());
                if (entity == null){
                    return;
                }
                if (entity.getHandleUserId().equals(t.getOldId())){
                    entity.setHandleUserId(t.getNewId());
                    entity.setHandleUserName(t.getNewName());
                }
                if (entity.getDevelopUserId().equals(t.getOldId())){
                    entity.setDevelopUserId(t.getNewId());
                    entity.setDevelopUserName(t.getNewName());
                }
                if (entity.getTestUserId().equals(t.getOldId())){
                    entity.setTestUserId(t.getNewId());
                    entity.setTestUserName(t.getNewName());
                }
                issueMapper.updateByPrimaryKeySelective(entity);

                Example example = new Example(RelevantUserEntity.class);
                example.createCriteria().andEqualTo("businessCode",t.getBusinessCode())
                        .andEqualTo("enable",Boolean.TRUE)
                        .andEqualTo("userId",t.getOldId())
                        .andEqualTo("type",RelevantUserTypeEnum.CURRENT_HANDLE_USER);
                RelevantUserEntity relevantUserEntity = relevantUserMapper.selectOneByExample(example);
                if (relevantUserEntity == null){
                    return;
                }
                relevantUserEntity.setUserId(t.getNewId()+"");
                relevantUserEntity.setUserName(t.getNewName());
                relevantUserMapper.updateByPrimaryKeySelective(relevantUserEntity);
            }catch (Exception e){
                log.warn("变更缺陷处理人异常{}-{}-{}-{}",t.getBusinessCode(),t.getOldId(),t.getNewId(),e.getMessage());
            }
        });
    }

    @Override
    public List<CountUserIssueNumVO> queryFixIssueGroupByDevelopUserId(List<IssueStatus> statusList) {
        return issueMapper.queryFixIssueGroupByDevelopUserId(statusList);
    }

    @Override
    public List<CountUserIssueNumVO> queryTestIssueGroupByTestUserId(List<IssueStatus> statusList) {
        return issueMapper.queryTestIssueGroupByTestUserId(statusList);
    }

    @Override
    public Map<String, Long> getIssueCountByFixedVersionCode(List<String> fixedVersionCodes) {
        if (CollectionUtil.isEmpty(fixedVersionCodes)){
            return Collections.emptyMap();
        }

        Map<String,Long> result = new HashMap<>();

        // 分组查询修复版本为目标版本的数据
        List<List<String>> partition = Lists.partition(fixedVersionCodes, 200);
        for (List<String> strings : partition) {
            Example example = new Example(IssueEntity.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("enable", true);
            criteria.andIn("fixVersionCode", strings);
            List<IssueEntity> issueEntities = issueMapper.selectByExample(example);
            result.putAll(issueEntities.stream()
                .collect(Collectors.groupingBy(
                    IssueEntity::getFixVersionCode,
                    Collectors.counting()
                )));
        }
        return result;
    }
}
