package com.zto.devops.qc.infrastructure.converter;


import com.zto.devops.qc.client.model.dto.TmEmailEntityDO;
import com.zto.devops.qc.client.model.testmanager.email.entity.DetailEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.EmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.VersionEmailVO;
import com.zto.devops.qc.infrastructure.dao.entity.TmEmailEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmVersionEmailEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Collection;
import java.util.List;


@Mapper(componentModel = "spring")
public interface EmailVOConverter {

    @Mapping(target = "sendUserId", source = "entity.senderId")
    @Mapping(target = "sendUserName", source = "entity.sender")
    @Mapping(target = "sendTime", source = "entity.sendDate")
    DetailEmailVO convert(TmEmailEntity entity);

    @Mapping(target = "emailTypeEnum", source = "entity.emailType")
    @Mapping(target = "versionDesc", source = "entity.versionName")
    @Mapping(target = "sendUserId", source = "entity.senderId")
    @Mapping(target = "sendUserName", source = "entity.sender")
    @Mapping(target = "sendTime", source = "entity.sendDate")
    EmailVO convertVO(TmEmailEntity entity);


    List<EmailVO> convert(List<TmEmailEntity> entityList);

    List<VersionEmailVO> convert(Collection<TmVersionEmailEntity> entityList);

    List<TmEmailEntityDO> convert2DOList(List<TmEmailEntity> entityList);

    TmEmailEntity convert2Entity(TmEmailEntityDO entityDO);
}

