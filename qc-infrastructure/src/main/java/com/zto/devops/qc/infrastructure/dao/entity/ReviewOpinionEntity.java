package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "qc_review_opinion")
public class ReviewOpinionEntity extends BaseEntity {

    /**
     * 报告code
     */
    @Column(name = "report_code")
    private String reportCode;

    /**
     * 用例code
     */
    @Id
    private String code;

    /**
     * 确认点以及修改项
     */
    private String description;
    /**
     * 编号
     */
    @Column(name = "serial_id")
    private String serialId;
    /**
     * 责任人ID
     */
    @Column(name = "owner_user_id")
    private Long ownerUserId;

    /**
     * 责任人名字
     */
    @Column(name = "owner_user_name")
    private String ownerUserName;

    /**
     * 截至时间
     */
    @Column(name = "dead_line_date")
    private Date deadLineDate;

    private static final long serialVersionUID = 1L;

}