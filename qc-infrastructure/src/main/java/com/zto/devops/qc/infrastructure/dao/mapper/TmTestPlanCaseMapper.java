package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.framework.infrastructure.dao.BaseMapper;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.PlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.SimpleTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindSortedPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.DeletePlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseCodeQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseModuleQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.PlanCaseResultCountQuery;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanCaseEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TmTestPlanCaseMapper extends BaseMapper<TmTestPlanCaseEntity> {

    List<TestPlanCaseVO> testPlanCaseList(ListPlanCaseQuery query);

    /**
     * 查询测试计划用例详情
     *
     * @param query {@link FindPlanCaseQuery}
     * @return {@link PlanCaseVO}
     */
    List<PlanCaseVO> selectPlanCase(FindPlanCaseQuery query);

    /**
     * 根据测试计划code查询关联用例列表（准入，准出，冒烟报告去重）
     *
     * @param planCode
     * @return
     */
    List<TmTestPlanCaseEntity> selectDistinctListByPlanCode(@Param("planCode") String planCode);

    /**
     * 根据自动化任务code更新用例状态
     *
     * @param automaticTaskCode 自动化任务code
     */
    void updateByAutomaticTaskCode(@Param("automaticTaskCode") String automaticTaskCode);

    /**
     * 查询计划用例操作日志code列表
     *
     * @param automaticTaskCode 自动化任务code
     * @return 操作日志code
     */
    List<String> selectOperateCaseCodeByTaskCode(@Param("automaticTaskCode") String automaticTaskCode);

    /**
     * 物理删除计划关联关系
     * <p>
     * List<Long> list
     */
    void deleteByIdList(@Param("list") List<Long> list);

    /**
     * 查询计划用例操作日志code列表
     *
     * @param caseCode 用例code
     * @return 操作日志code
     */
    List<String> selectOperateCaseCodeByCaseCode(@Param("caseCode") String caseCode);

    /**
     * 查询计划用例操作日志code列表
     *
     * @param list        用例code
     * @param versionCode 版本code
     * @return 操作日志code
     */
    List<String> selectOperateCaseCodeByVersionCode(
            @Param("list") List<String> list,
            @Param("versionCode") String versionCode);

    /**
     * 批量加入测试计划
     *
     * @param list
     */
    void batchSave(@Param("list") List<TmTestPlanCaseEntity> list);

    /**
     * 查询分组下所有用例code
     *
     * @param query
     * @return
     */
    List<SimpleTestcaseVO> selectTestcaseCodeList(ListPlanCaseCodeQuery query);

    List<TestPlanCaseVO> selectAllPlanCasePath(ListPlanCaseModuleQuery query);

    List<TestPlanCaseVO> selectPlanCaseModuleList(ListPlanCaseModuleQuery query);

    List<TestPlanCaseVO> selectPlanCaseAutomaticModuleList(ListPlanCaseModuleQuery query);

    List<TestPlanCaseStatusEnum> findStatusList(PlanCaseResultCountQuery query);

    List<DeletePlanCaseVO> selectPlanCaseByCodeList(@Param("testCaseCodeList") List<String> testCaseCodeList);

    List<PlanCaseVO> selectSimplePlanCase(FindSortedPlanCaseQuery query);

    /**
     * 根据计划code，查关联用例的版本code
     *
     * @param planCode 计划code
     * @return 版本code集合
     */
    List<String> selectVersionCodeByPlanCode(@Param("planCode") String planCode);
}
