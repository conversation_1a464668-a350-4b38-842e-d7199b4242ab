package com.zto.devops.qc.infrastructure.gateway.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.JmxParseSceneVO;
import com.zto.devops.qc.domain.gateway.util.JmxParseUtil;
import com.zto.devops.qc.domain.gateway.zbase.ZbaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class JmxParseUtilImpl implements JmxParseUtil {

    @Autowired
    private ZbaseService zbaseService;

    @Override
    public void parseJmx(Document document, JmxParseSceneVO sceneVO) {
        if(null == document) {
            throw new ServiceException("解析失败: document为空!");
        }
        List<Element> elements = document.getRootElement().elements();
        if(elements.size() != 1) {
            throw new ServiceException("解析失败: jmx脚本内容为空!");
        }
        List<Element> rootEleList = elements.get(0).elements();

        // jmx解析
        elementsParse(rootEleList, sceneVO);

    }

    private void elementsParse(List<Element> elementList, JmxParseSceneVO sceneVO) {
        if(CollectionUtil.isEmpty(elementList)) {
            return;
        }
        for(int i = 0; i < elementList.size(); i++) {
            Element element = elementList.get(i);
            if("TestPlan".equals(element.getName())) {
                testPlanParse(element, sceneVO);
            }
            if("ThreadGroup".equals(element.getName())) {
                // 线程组非空
                if(null != elementList.get(i+1).getName()
                        && "hashTree".equals(elementList.get(i+1).getName())) {
                    String threadName = element.attributeValue("testname");
                    log.info("解析线程组名称: {}", threadName);
                    threadGroupParse(elementList.get(i+1), sceneVO);
                }
            }
            if("hashTree".equals(element.getName()) && !element.elements().isEmpty()) {
                elementsParse(element.elements(), sceneVO);
            }
        }
    }

    private void threadGroupParse(Element threadGroupEle, JmxParseSceneVO sceneVO) {
        List<Element> elementList = threadGroupEle.elements();
        for(int i = 0; i < elementList.size(); i++) {
            Element element = elementList.get(i);
            if("JDBCDataSource".equals(element.getName())) {
                JDBCConfigParse(element, sceneVO);
            }
            if("Arguments".equals(element.getName())) {
                argumentsParse(element, sceneVO);
            }
            if("HeaderManager".equals(element.getName())) {
                headerManagerParse(element, sceneVO);
            }
            if("HTTPSamplerProxy".equals(element.getName())) {
                httpSampleParse(element, sceneVO, elementList.get(i+1));
            }
            if("io.github.ningyu.jmeter.plugin.dubbo.sample.DubboSample".equals(element.getName())) {
                dubboSampleParse(element, sceneVO, elementList.get(i+1));
            }
            if("JDBCSampler".equals(element.getName())) {
                JDBCSampleParse(element, sceneVO);
            }
            if(StringUtils.isNotEmpty(element.getName()) && element.getName().contains("Controller")) {
                Element nextElement = elementList.get(i+1);
                if("hashTree".equals(nextElement.getName()) && !nextElement.elements().isEmpty()) {
                    threadGroupParse(nextElement, sceneVO);
                }
            }
        }
    }

    private void JDBCConfigParse(Element JDBCConfigEle, JmxParseSceneVO sceneVO) {
        log.info("解析JDBCConfig名称: {}", JDBCConfigEle.attributeValue("testname"));
        List<Element> JDBCSourceEleList = JDBCConfigEle.elements();
        if(CollectionUtil.isEmpty(JDBCSourceEleList)) {
            return;
        }
        String dbUrl = "";
        String dataSource = "";
        for(Element JDBCSource : JDBCSourceEleList) {
            if("dbUrl".equals(JDBCSource.attributeValue("name"))) {
                dbUrl = JDBCSource.getStringValue();
            }
            if("dataSource".equals(JDBCSource.attributeValue("name"))) {
                dataSource = JDBCSource.getStringValue();
            }
        }
        if(StringUtils.isNotEmpty(dbUrl) && StringUtils.isNotEmpty(dataSource)) {
            Integer dbId = zbaseService.getPhysicsDbId(dbUrl, sceneVO.getProductCode(), sceneVO.getUser(),
                    sceneVO.getType(), sceneVO.getSceneCode());
            if(null != dbId) {
                sceneVO.getSchemaIdObj().put(dataSource, dbId);
            }
        }
    }

    private void JDBCSampleParse(Element JDBCEle, JmxParseSceneVO sceneVO) {
        log.info("解析JDBCSample: {}", JDBCEle.attributeValue("testname"));
        JSONArray preComponents = sceneVO.getPreComponents();
        JSONObject schemaIdObj = sceneVO.getSchemaIdObj();
        List<Element> stringProps = JDBCEle.elements();
        if(CollectionUtil.isEmpty(stringProps)) {
            return;
        }
        JSONObject jdbcComponent = new JSONObject();
        jdbcComponent.put("id", preComponents.size());
        jdbcComponent.put("type", "JDBC");
        for(Element stringProp : stringProps) {
            if("dataSource".equals(stringProp.attributeValue("name"))) {
                jdbcComponent.put("dbId", schemaIdObj.get(stringProp.getStringValue()));
            }
            if("query".equals(stringProp.attributeValue("name"))) {
                jdbcComponent.put("sql", stringProp.getStringValue());
            }
        }
        sceneVO.getPreComponents().add(jdbcComponent);
    }

    private void dubboSampleParse(Element dubboEle, JmxParseSceneVO sceneVO, Element hashTreeEle) {
        log.info("解析dubboSample: {}", dubboEle.attributeValue("testname"));
        List<Element> dubboElementList = dubboEle.elements();
        if(CollectionUtil.isEmpty(dubboElementList)) {
            return;
        }
        JSONObject node = newNodeObj();
        JSONObject sampler = newSamplerObj();
        JSONArray args = new JSONArray();
        JSONArray attachmentArgs = new JSONArray();

        JSONArray postComponents = new JSONArray();
//        postComponents.addAll(sceneVO.getPostComponents());
        JSONArray preComponents = new JSONArray();
        // JDBCSample放在相邻下一个节点
        if(CollectionUtil.isNotEmpty(sceneVO.getPreComponents())) {
            preComponents.addAll(sceneVO.getPreComponents());
            sceneVO.setPreComponents(new JSONArray());
        }

        for(int i = 0; i < dubboElementList.size(); i++) {
            Element dubboElement = dubboElementList.get(i);
            if("FIELD_DUBBO_TIMEOUT".equals(dubboElement.attributeValue("name"))) {
                sampler.put("timeout", dubboElement.getStringValue());
                continue;
            }
            if("FIELD_DUBBO_VERSION".equals(dubboElement.attributeValue("name"))) {
                sampler.put("version", dubboElement.getStringValue());
                continue;
            }
            if("FIELD_DUBBO_RETRIES".equals(dubboElement.attributeValue("name"))) {
                sampler.put("retries", dubboElement.getStringValue());
                continue;
            }
            if("FIELD_DUBBO_GROUP".equals(dubboElement.attributeValue("name"))) {
                sampler.put("group", dubboElement.getStringValue());
                continue;
            }
            if("FIELD_DUBBO_INTERFACE".equals(dubboElement.attributeValue("name"))) {
                sampler.put("interfaceName", dubboElement.getStringValue());
                continue;
            }
            if("FIELD_DUBBO_METHOD".equals(dubboElement.attributeValue("name"))) {
                sampler.put("methodName", dubboElement.getStringValue());
                continue;
            }
            if(dubboElement.attributeValue("name").contains("FIELD_DUBBO_METHOD_ARGS_PARAM_TYPE")) {
                JSONObject argObj = newVariableObj();
                argObj.put("paramType", dubboElement.getStringValue());
                Element nextElement = dubboElementList.get(i+1);
                if(nextElement.attributeValue("name").contains("FIELD_DUBBO_METHOD_ARGS_PARAM_VALUE")) {
                    argObj.put("paramValue", nextElement.getStringValue());
                }
                args.add(argObj);
                continue;
            }
            if(dubboElement.attributeValue("name").contains("FIELD_DUBBO_ATTACHMENT_ARGS_KEY")) {
                JSONObject attachmentObj = newVariableObj();
                attachmentObj.put("key", dubboElement.getStringValue());
                Element nextElement = dubboElementList.get(i+1);
                if(nextElement.attributeValue("name").contains("FIELD_DUBBO_ATTACHMENT_ARGS_VALUE")) {
                    attachmentObj.put("value", nextElement.getStringValue());
                }
                attachmentArgs.add(attachmentObj);
            }
        }
        sampler.put("method", "dubbo");
        sampler.put("serverNameOrIp", "host");
        sampler.put("type", "DUBBO_REQUEST_COMPONENT");
        sampler.put("args", args);
        sampler.put("attachmentArgs", attachmentArgs);
        if(null != hashTreeEle && "hashTree".equals(hashTreeEle.getName()) && CollectionUtil.isNotEmpty(hashTreeEle.elements())) {
            List<Element> componentEleList = hashTreeEle.elements();
            for(Element componentEle : componentEleList) {
                if("RegexExtractor".equals(componentEle.getName())) {
                    List<Element> stringProps = componentEle.elements("stringProp");
                    if(CollectionUtil.isEmpty(stringProps)) {
                        continue;
                    }
                    JSONObject regexComponent = regexComponentParse(stringProps, postComponents.size());
                    postComponents.add(regexComponent);
                    continue;
                }
                if("ConstantTimer".equals(componentEle.getName())) {
                    JSONObject sleepComponent = new JSONObject();
                    sleepComponent.put("id", postComponents.size());
                    sleepComponent.put("type", "SLEEP");
                    String delay = componentEle.element("stringProp").getStringValue();
                    sleepComponent.put("milliSecond", delay);
                    postComponents.add(sleepComponent);
                    continue;
                }
                if("BeanShellPreProcessor".equals(componentEle.getName())) {
                    List<Element> stringProps = componentEle.elements();
                    if(CollectionUtil.isEmpty(stringProps)) {
                        continue;
                    }
                    JSONObject scriptComponent = scriptsComponentParse(stringProps, preComponents.size());
                    preComponents.add(scriptComponent);
                    continue;
                }
                if("JSONPostProcessor".equals(componentEle.getName())) {
                    List<Element> stringProps = componentEle.elements();
                    if(CollectionUtil.isEmpty(stringProps)) {
                        continue;
                    }
                    JSONObject jsonExtractorComponent = jsonExtractorComponentParse(stringProps, postComponents.size());
                    postComponents.add(jsonExtractorComponent);
                    continue;
                }
                if("JSONPathAssertion".equals(componentEle.getName())) {
                    List<Element> stringProps = componentEle.elements();
                    if(CollectionUtil.isEmpty(stringProps)) {
                        continue;
                    }
                    JSONObject jsonAssertComponent = jsonAssertComponentParse(stringProps, postComponents.size());
                    postComponents.add(jsonAssertComponent);
                    continue;
                }
                if("ResponseAssertion".equals(componentEle.getName())) {
                    List<Element> stringProps = componentEle.elements();
                    if(CollectionUtil.isEmpty(stringProps)) {
                        continue;
                    }
                    JSONObject responseComponent = responseAssertionParse(stringProps, postComponents.size());
                    postComponents.add(responseComponent);
                }
            }
        }
        node.put("name", dubboEle.attributeValue("testname"));
        node.put("interfaceType", "dubbo");
        node.put("sampler", sampler);
        node.put("pathUrl", sampler.getString("interfaceName"));
        node.put("requestUrl", sampler.getString("interfaceName"));
        node.put("postComponents", postComponents);
        node.put("preComponents", preComponents);
        node.put("type", "NODE");
        sceneVO.getScene().getNodes().add(node);
    }


    private void httpSampleParse(Element httpEle, JmxParseSceneVO sceneVO, Element hashTreeEle) {
        log.info("解析httpSample: {}", httpEle.attributeValue("testname"));
        List<Element> httpElementList = httpEle.elements();
        if(CollectionUtil.isEmpty(httpElementList)) {
            return;
        }
        JSONObject node = newNodeObj();
        JSONObject sampler = newSamplerObj();
        JSONArray httpRequestHeader = new JSONArray();

        JSONArray postComponents = new JSONArray();
//        postComponents.addAll(sceneVO.getPostComponents());
        // JDBCSample放在相邻下一个节点
        JSONArray preComponents = new JSONArray();
        if(CollectionUtil.isNotEmpty(sceneVO.getPreComponents())) {
            preComponents.addAll(sceneVO.getPreComponents());
            sceneVO.setPreComponents(new JSONArray());
        }

        String domain = "";
        String port = "";
        String protocol= "";
        String path = "";
        for(Element httpElement : httpElementList) {
            if("HTTPsampler.Arguments".equals(httpElement.attributeValue("name"))) {
                if("Arguments".equals(httpElement.attributeValue("testclass"))) {
                    // form
                    List<Element> elementPropEle = httpElement.element("collectionProp").elements();
                    if(CollectionUtil.isEmpty(elementPropEle)) {
                        continue;
                    }
                    JSONArray parameters = httpFormParameterParse(elementPropEle);
                    sampler.put("parameters", parameters);
                    sampler.put("type", "HTTP_REQUEST_COMPONENT_FORM_TYPE");
                    continue;
                }
                // body
                Element elementPropEle = httpElement.element("collectionProp").element("elementProp");
                if(null == elementPropEle) {
                    continue;
                }
                List<Element> stringEleList = elementPropEle.elements();
                for(Element stringEle : stringEleList) {
                    if("Argument.value".equals(stringEle.attributeValue("name"))) {
                        sampler.put("bodyData", stringEle.getStringValue());
                        sampler.put("type", "HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE");
                    }
                }
                continue;
            }
            if("HTTPSampler.domain".equals(httpElement.attributeValue("name"))) {
                if(null != httpElement.getStringValue()) {
                    domain = httpElement.getStringValue();
                }
                continue;
            }
            if("HTTPSampler.port".equals(httpElement.attributeValue("name"))) {
                if(null != httpElement.getStringValue()) {
                    port = httpElement.getStringValue();
                }
                continue;
            }
            if("HTTPSampler.protocol".equals(httpElement.attributeValue("name"))) {
                if(null != httpElement.getStringValue()) {
                    protocol = httpElement.getStringValue();
                }
                continue;
            }
            if("HTTPSampler.path".equals(httpElement.attributeValue("name"))) {
                if(null != httpElement.getStringValue()) {
                    path = httpElement.getStringValue();
                }
                continue;
            }
            if("HTTPSampler.method".equals(httpElement.attributeValue("name"))) {
                sampler.put("method", httpElement.getStringValue());
            }
        }
        sampler.put("pathUrl", buildHttpSamplePathUrl(protocol, domain, port, path));
        if(null != hashTreeEle && "hashTree".equals(hashTreeEle.getName()) && CollectionUtil.isNotEmpty(hashTreeEle.elements())) {
            List<Element> componentEleList = hashTreeEle.elements();
            for(Element componentEle : componentEleList) {
                if("HeaderManager".equals(componentEle.getName())) {
                    List<Element> elementEleList = componentEle.element("collectionProp").elements("elementProp");
                    if(CollectionUtil.isEmpty(elementEleList)) {
                        continue;
                    }
                    httpRequestHeader = requestHeaderManagerParse(elementEleList);
                    sampler.put("httpRequestHeader", httpRequestHeader);
                    continue;
                }
                if("RegexExtractor".equals(componentEle.getName())) {
                    List<Element> stringProps = componentEle.elements("stringProp");
                    if(CollectionUtil.isEmpty(stringProps)) {
                        continue;
                    }
                    JSONObject regexComponent = regexComponentParse(stringProps, postComponents.size());
                    postComponents.add(regexComponent);
                    continue;
                }
                if("ConstantTimer".equals(componentEle.getName())) {
                    JSONObject sleepComponent = new JSONObject();
                    sleepComponent.put("id", postComponents.size());
                    sleepComponent.put("type", "SLEEP");
                    String delay = componentEle.element("stringProp").getStringValue();
                    sleepComponent.put("milliSecond", delay);
                    postComponents.add(sleepComponent);
                    continue;
                }
                if("BeanShellPreProcessor".equals(componentEle.getName())) {
                    List<Element> stringProps = componentEle.elements();
                    if(CollectionUtil.isEmpty(stringProps)) {
                        continue;
                    }
                    JSONObject scriptComponent = scriptsComponentParse(stringProps, preComponents.size());
                    preComponents.add(scriptComponent);
                    continue;
                }
                if("JSONPostProcessor".equals(componentEle.getName())) {
                    List<Element> stringProps = componentEle.elements();
                    if(CollectionUtil.isEmpty(stringProps)) {
                        continue;
                    }
                    JSONObject jsonExtractorComponent = jsonExtractorComponentParse(stringProps, postComponents.size());
                    postComponents.add(jsonExtractorComponent);
                }
                if("JSONPathAssertion".equals(componentEle.getName())) {
                    List<Element> stringProps = componentEle.elements();
                    if(CollectionUtil.isEmpty(stringProps)) {
                        continue;
                    }
                    JSONObject jsonAssertComponent = jsonAssertComponentParse(stringProps, postComponents.size());
                    postComponents.add(jsonAssertComponent);
                    continue;
                }
                if("ResponseAssertion".equals(componentEle.getName())) {
                    List<Element> stringProps = componentEle.elements();
                    if(CollectionUtil.isEmpty(stringProps)) {
                        continue;
                    }
                    JSONObject responseComponent = responseAssertionParse(stringProps, postComponents.size());
                    postComponents.add(responseComponent);
                }
            }
        }
        node.put("name", httpEle.attributeValue("testname"));
        node.put("interfaceType", "http");
        node.put("sampler", sampler);
        node.put("pathUrl", sampler.getString("pathUrl"));
        node.put("requestUrl", sampler.getString("pathUrl"));
        node.put("postComponents", postComponents);
        node.put("preComponents", preComponents);
        node.put("type", "NODE");
        sceneVO.getScene().getNodes().add(node);
    }


    private JSONObject newNodeObj() {
        JSONObject node = new JSONObject();
        node.put("code", "Activity_" + RandomUtil.randomString(7));
        node.put("apiCode", "");
        node.put("apiName", "");
        node.put("apiUpdated", false);
        node.put("classType", "场景用例");
        node.put("data", Collections.emptyList());
        node.put("docId", "");
        node.put("docName", "");
        node.put("docProductCode", "");
        node.put("envName", "");
        node.put("followRedirects", true);
        node.put("gatewaySource", "");
        node.put("isFirst", false);
        node.put("user", "");
        return node;
    }

    private JSONObject newResponseTestMode() {
        JSONObject object = new JSONObject();
        object.put("id", RandomUtil.randomString(32));
        object.put("operate", "");
        object.put("keyword", "");
        object.put("isInput", true);
        return object;
    }

    private JSONObject newVariableObj() {
        JSONObject object = newResponseTestMode();
        object.put("loading", false);
        return object;
    }

    private JSONObject newJsonExtractorVariableObj() {
        return newHeaderObj();
    }

    private JSONObject newRegexVariableObj() {
        return newHeaderObj();
    }

    private JSONObject newHeaderObj() {
        JSONObject headerObj = newVariableObj();
        headerObj.put("explain", "");
        return headerObj;
    }

    private JSONObject newParameterObj() {
        JSONObject parameter = newHeaderObj();
        parameter.put("contentType", "text/plain");
        return parameter;
    }

    private JSONObject newSamplerObj() {
        JSONObject sampler = new JSONObject();
        sampler.put("followRedirects", true);
        sampler.put("inputMethod", "custom");
        sampler.put("interfaceType", "custom");
        sampler.put("interfaceName", "");
        sampler.put("bodyType", "JSON");
        sampler.put("browserCompatibleHeaders", false);
        sampler.put("intries", 0);
        sampler.put("isMock", false);
        sampler.put("mockSwitch", false);
        sampler.put("redirectAutomatically", false);
        sampler.put("userKeepAlive", true);
        sampler.put("userMultipartFormData", false);
        return sampler;
    }

    private JSONObject responseAssertionParse(List<Element> list, int index) {
        if(CollectionUtil.isEmpty(list)) {
            return null;
        }
        JSONObject responseComponent = new JSONObject();
        // todo 目前只有默认
        responseComponent.put("id", index);
        responseComponent.put("assumeSuccess", false);
        responseComponent.put("ifNot", false);
        responseComponent.put("ifOr", false);
        responseComponent.put("testField", "Assertion.response_data");
        responseComponent.put("testType", "SUBSTRING");
        responseComponent.put("type", "ASSERT_RESPONSE");
        JSONArray testModeArray = new JSONArray();
        List<String> testStrings = new ArrayList<>();
        for(Element element : list) {
            if("Asserion.test_strings".equals(element.attributeValue("name"))) {
                List<Element> stringProps = element.elements();
                if(CollectionUtil.isEmpty(stringProps)) {
                    continue;
                }
                for(Element stringProp : stringProps) {
                    JSONObject testMode = newResponseTestMode();
                    testMode.put("testMode", stringProp.getStringValue());
                    testModeArray.add(testMode);
                    testStrings.add(stringProp.getStringValue());
                }
            }
        }
        responseComponent.put("testMode", testModeArray);
        responseComponent.put("testStrings", testStrings);
        return responseComponent;
    }

    private JSONArray httpFormParameterParse(List<Element> list) {
        if(CollectionUtil.isEmpty(list)) {
            return null;
        }
        JSONArray parameters = new JSONArray();
        for(Element elementEle : list) {
            List<Element> stringEleList = elementEle.elements();
            JSONObject parameter = newParameterObj();
            for(Element stringEle : stringEleList) {
                if("HTTPArgument.always_encode".equals(stringEle.attributeValue("name"))) {
                    parameter.put("urlEncode", "true".equals(stringEle.getStringValue()));
                }
                if("Argument.value".equals(stringEle.attributeValue("name"))) {
                    parameter.put("value", stringEle.getStringValue());
                }
                if("HTTPArgument.use_equals".equals(stringEle.attributeValue("name"))) {
                    parameter.put("includeEquals", "true".equals(stringEle.getStringValue()));
                }
                if("Argument.name".equals(stringEle.attributeValue("name"))) {
                    parameter.put("name", stringEle.getStringValue());
                }
            }
            parameters.add(parameter);
        }
        return parameters;
    }

    private JSONObject jsonAssertComponentParse(List<Element> list, int index) {
        if(CollectionUtil.isEmpty(list)) {
            return null;
        }
        JSONObject jsonAssertComponent = new JSONObject();
        jsonAssertComponent.put("id", index);
        jsonAssertComponent.put("type", "ASSERT_JSONPATH");
        for(Element stringProp: list) {
            if("JSON_PATH".equals(stringProp.attributeValue("name"))) {
                jsonAssertComponent.put("jsonPath", stringProp.getStringValue());
            }
            if("EXPECTED_VALUE".equals(stringProp.attributeValue("name"))) {
                jsonAssertComponent.put("expectedValue", stringProp.getStringValue());
            }
            if("JSONVALIDATION".equals(stringProp.attributeValue("name"))) {
                jsonAssertComponent.put("jsonValidation", "true".equals(stringProp.getStringValue()));
            }
            if("EXPECT_NULL".equals(stringProp.attributeValue("name"))) {
                jsonAssertComponent.put("expectNull", "true".equals(stringProp.getStringValue()));
            }
            if("INVERT".equals(stringProp.attributeValue("name"))) {
                jsonAssertComponent.put("invert", "true".equals(stringProp.getStringValue()));
            }
            if("ISREGEX".equals(stringProp.attributeValue("name"))) {
                jsonAssertComponent.put("ifRegex", "true".equals(stringProp.getStringValue()));
            }
        }
        return jsonAssertComponent;
    }

    private JSONObject jsonExtractorComponentParse(List<Element> list, int index) {
        if(CollectionUtil.isEmpty(list)) {
            return null;
        }
        JSONObject jsonExtractorComponent = new JSONObject();
        jsonExtractorComponent.put("id", index);
        jsonExtractorComponent.put("type", "EXTRACTOR_JSON");
        JSONArray variableExtraction = new JSONArray();
        JSONObject variable = newJsonExtractorVariableObj();
        variable.put("source", "responseData");
        for(Element stringProp: list) {
            // todo 目前只有responseData
            if ("JSONPostProcessor.referenceNames".equals(stringProp.attributeValue("name"))) {
                variable.put("name", stringProp.getStringValue());
            }
            if ("JSONPostProcessor.jsonPathExprs".equals(stringProp.attributeValue("name"))) {
                variable.put("value", stringProp.getStringValue());
            }
        }
        variableExtraction.add(variable);
        jsonExtractorComponent.put("variableExtraction", variableExtraction);
        return  jsonExtractorComponent;
    }

    private JSONObject scriptsComponentParse(List<Element> list, int index) {
        if(CollectionUtil.isEmpty(list)) {
            return null;
        }
        JSONObject scriptComponent = new JSONObject();
        scriptComponent.put("id", index);
        scriptComponent.put("type", "BEANSHELL_SCRIPTS");
        for(Element stringProp: list) {
            if("script".equals(stringProp.attributeValue("name"))) {
                scriptComponent.put("scripts", stringProp.getStringValue());
            }
        }
        return scriptComponent;
    }

    private JSONObject regexComponentParse(List<Element> list, int index) {
        JSONObject regexComponent = new JSONObject();
        regexComponent.put("id", index);
        regexComponent.put("type", "RegularExpressionExtractor");
        JSONArray variableExtraction = new JSONArray();
        JSONObject variable = newRegexVariableObj();
        // todo 目前只有responseData
        variable.put("value", "responseData");
        for(Element stringProp : list) {
            if("RegexExtractor.refname".equals(stringProp.attributeValue("name"))) {
                variable.put("name", stringProp.getStringValue());
            }
            if("RegexExtractor.regex".equals(stringProp.attributeValue("name"))) {
                variable.put("regex", stringProp.getStringValue());
            }
        }
        variableExtraction.add(variable);
        regexComponent.put("variableExtraction", variableExtraction);
        return regexComponent;
    }

    private JSONArray requestHeaderManagerParse(List<Element> list) {
        JSONArray httpRequestHeader = new JSONArray();
        for(Element headerEle : list) {
            JSONObject headerObj = newHeaderObj();
            List<Element> stringProps = headerEle.elements("stringProp");
            for (Element stringEle : stringProps) {
                if("Header.name".equals(stringEle.attributeValue("name"))) {
                    headerObj.put("name", stringEle.getStringValue());
                }
                if("Header.value".equals(stringEle.attributeValue("name"))) {
                    headerObj.put("value", stringEle.getStringValue());
                }
            }
            httpRequestHeader.add(headerObj);
        }
        return httpRequestHeader;
    }

    private void headerManagerParse(Element headerEle, JmxParseSceneVO sceneVO) {
        log.info("解析header组件: {}", headerEle.attributeValue("testname"));
        List<Element> headerList = headerEle.element("collectionProp").elements("elementProp");
        if(CollectionUtil.isEmpty(headerList)) {
            return;
        }
        JSONObject headerObj = this.getGlobalHeader(headerList);
        sceneVO.getGlobalHeader().putAll(headerObj);
    }

    private void argumentsParse(Element element, JmxParseSceneVO sceneVO) {
        log.info("解析arguments组件: {}", element.attributeValue("testname"));
        List<Element> argumentList = element.element("collectionProp").elements("elementProp");
        if(CollectionUtil.isEmpty(argumentList)) {
            return;
        }
        JSONObject variableObj = this.getGlobalVariable(argumentList);
        sceneVO.getGlobalVariable().putAll(variableObj);
    }


    private void testPlanParse(Element element, JmxParseSceneVO sceneVO) {
        if(null == element) {
            return;
        }
        // 计划名称
        String testPlanName = element.attributeValue("testname");
        log.info("解析测试计划名称: {}", testPlanName);
        List<Element> planVariableList = element.element("elementProp").element("collectionProp").elements("elementProp");
        if(CollectionUtil.isEmpty(planVariableList)) {
            return;
        }
        JSONObject variableObj = this.getGlobalVariable(planVariableList);
        sceneVO.getGlobalVariable().putAll(variableObj);
    }

    private JSONObject getGlobalVariable(List<Element> list) {
        JSONObject variableObj = new JSONObject();
        if(CollectionUtil.isEmpty(list)) {
            return variableObj;
        }
        for(Element argumentEle : list) {
            String key = "";
            String value = "";
            List<Element> stringProps = argumentEle.elements("stringProp");
            for (Element stringEle : stringProps) {
                if("Argument.name".equals(stringEle.attributeValue("name"))) {
                    key = stringEle.getStringValue();
                }
                if("Argument.value".equals(stringEle.attributeValue("name"))) {
                    value = stringEle.getStringValue();
                }
            }
            if(StringUtils.isNotEmpty(key)) {
                variableObj.put(key, value);
            }
        }
        return variableObj;
    }

    private JSONObject getGlobalHeader(List<Element> list) {
        JSONObject variableObj = new JSONObject();
        if(CollectionUtil.isEmpty(list)) {
            return variableObj;
        }
        for(Element argumentEle : list) {
            String key = "";
            String value = "";
            List<Element> stringProps = argumentEle.elements("stringProp");
            for (Element stringEle : stringProps) {
                if("Header.name".equals(stringEle.attributeValue("name"))) {
                    key = stringEle.getStringValue();
                }
                if("Header.value".equals(stringEle.attributeValue("name"))) {
                    value = stringEle.getStringValue();
                }
            }
            if(StringUtils.isNotEmpty(key)) {
                variableObj.put(key, value);
            }
        }
        return variableObj;
    }

    private String buildHttpSamplePathUrl(String protocol, String domain, String port, String path) {
        // 都不为空
        if(StringUtils.isNoneEmpty(protocol.trim(), domain.trim(), port.trim(), path.trim())) {
            return protocol.trim() + "://" + domain.trim() + ":" + port.trim() + path.trim();
        }
        // 只有port为空
        if(StringUtils.isNoneEmpty(protocol.trim(), domain.trim(), path.trim())
                && StringUtils.isEmpty(port.trim())) {
            return protocol.trim() + "://" + domain.trim() + path.trim();
        }
        // 以上不满足，只返回path
        if(StringUtils.isNotEmpty(path.trim())) {
            return path;
        }
        // 都不满足，返回""
        return StringUtils.EMPTY;
    }
}
