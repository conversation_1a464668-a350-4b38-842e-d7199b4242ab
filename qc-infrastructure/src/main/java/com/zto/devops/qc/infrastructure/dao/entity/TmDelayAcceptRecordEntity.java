package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Table(name = "tm_delay_accept_record")
@Data
public class TmDelayAcceptRecordEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 8825301842291372867L;

    private Long id;

    private String flowCode;

    private String productCode;

    private String versionCode;

    private String versionName;

    private String acceptRemark;

    private Boolean isSend;

    private Date sendTime;

    /**
     * 审批人中天账号,英文逗号分隔
     */
    private String approver;

    /**
     * 审批工单唯一标识
     */
    private String auditFlowCode;
}