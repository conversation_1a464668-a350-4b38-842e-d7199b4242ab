package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.model.dto.TmDelayAcceptRecordEntityDO;
import com.zto.devops.qc.domain.gateway.repository.ITmDelayAcceptRecordRepository;
import com.zto.devops.qc.infrastructure.converter.TmDelayAcceptRecordEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.TmDelayAcceptRecordEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TmDelayAcceptRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class TmDelayAcceptRecordRepositoryImpl implements ITmDelayAcceptRecordRepository {

    @Autowired
    private TmDelayAcceptRecordMapper tmDelayAcceptRecordMapper;
    @Autowired
    private TmDelayAcceptRecordEntityConverter tmDelayAcceptRecordEntityConverter;

    @Override
    public TmDelayAcceptRecordEntityDO selectByVersionCode(String versionCode) {
        if (StringUtils.isBlank(versionCode)) {
            return null;
        }
        Example example = new Example(TmDelayAcceptRecordEntity.class);
        example.createCriteria().andEqualTo("versionCode", versionCode)
                .andEqualTo("enable", Boolean.TRUE);
        example.orderBy("gmtModified").desc();
        List<TmDelayAcceptRecordEntity> entityList = tmDelayAcceptRecordMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? null : tmDelayAcceptRecordEntityConverter.convert2DO(entityList.get(0));
    }

    @Override
    public void insert(TmDelayAcceptRecordEntityDO entityDO) {
        TmDelayAcceptRecordEntity entity = tmDelayAcceptRecordEntityConverter.convert2Entity(entityDO);
        tmDelayAcceptRecordMapper.insert(entity);
    }

    @Override
    public void updateEnableByFlowCode(String code) {
        Example example = new Example(TmDelayAcceptRecordEntity.class);
        example.createCriteria().andEqualTo("flowCode", code)
                .andEqualTo("enable", Boolean.TRUE);
        TmDelayAcceptRecordEntity entity = new TmDelayAcceptRecordEntity();
        entity.setEnable(Boolean.FALSE);
        tmDelayAcceptRecordMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public TmDelayAcceptRecordEntityDO selectByFlowCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        Example example = new Example(TmDelayAcceptRecordEntity.class);
        example.createCriteria().andEqualTo("flowCode", code)
                .andEqualTo("enable", Boolean.TRUE);
        example.orderBy("gmtModified").desc();
        List<TmDelayAcceptRecordEntity> entityList = tmDelayAcceptRecordMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? null : tmDelayAcceptRecordEntityConverter.convert2DO(entityList.get(0));
    }

    @Override
    public List<TmDelayAcceptRecordEntityDO> selectListBySendFlag(Boolean sendFlag) {
        if (Objects.isNull(sendFlag)) {
            return new ArrayList<>();
        }
        Example example = new Example(TmDelayAcceptRecordEntity.class);
        example.createCriteria()
                .andEqualTo("isSend", sendFlag)
                .andEqualTo("enable", Boolean.TRUE);
        List<TmDelayAcceptRecordEntity> entityList = tmDelayAcceptRecordMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : tmDelayAcceptRecordEntityConverter.convert2DO(entityList);
    }

    @Override
    public void updateIsSendByFlowCodeList(List<String> codeList, Boolean isSend) {
        if (CollectionUtil.isEmpty(codeList)) {
            return;
        }
        Example example = new Example(TmDelayAcceptRecordEntity.class);
        example.createCriteria()
                .andIn("flowCode", codeList)
                .andEqualTo("enable", Boolean.TRUE);
        TmDelayAcceptRecordEntity entity = new TmDelayAcceptRecordEntity();
        entity.setIsSend(isSend);
        entity.setSendTime(new Date());
        tmDelayAcceptRecordMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void updateSendSuccess(TmDelayAcceptRecordEntityDO entityDO) {
        if (Objects.isNull(entityDO)) {
            return;
        }
        Example example = new Example(TmDelayAcceptRecordEntity.class);
        example.createCriteria()
                .andEqualTo("flowCode", entityDO.getFlowCode())
                .andEqualTo("enable", Boolean.TRUE);
        tmDelayAcceptRecordMapper.updateByExampleSelective(tmDelayAcceptRecordEntityConverter.convert2Entity(entityDO), example);
    }

    @Override
    public List<TmDelayAcceptRecordEntityDO> selectByVersionCodeList(List<String> versionCodeList) {
        if (CollectionUtil.isEmpty(versionCodeList)) {
            return new ArrayList<>();
        }
        Example example = new Example(TmDelayAcceptRecordEntity.class);
        example.createCriteria().andIn("versionCode", versionCodeList)
                .andEqualTo("enable", Boolean.TRUE);
        example.orderBy("gmtModified").desc();
        List<TmDelayAcceptRecordEntity> entityList = tmDelayAcceptRecordMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : tmDelayAcceptRecordEntityConverter.convert2DO(entityList);
    }
}
