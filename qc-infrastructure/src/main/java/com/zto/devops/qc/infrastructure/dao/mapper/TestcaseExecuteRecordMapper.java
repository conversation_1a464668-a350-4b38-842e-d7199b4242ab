package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteCaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListExecuteCaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseExecuteNumVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseExecuteRecordVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListExecuteCaseQuery;
import com.zto.devops.qc.client.service.testmanager.apitest.model.ListExecuteDetailReq;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseExecuteRecordEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanCaseEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TestcaseExecuteRecordMapper extends Mapper<TestcaseExecuteRecordEntity> {

    /**
     * 查询执行记录
     *
     * @param code 用例code
     * @return {@link TestcaseExecuteRecordVO}
     */
    List<TestcaseExecuteRecordVO> selectByTestcaseCode(@Param("code") String code,
                                                       @Param("type") AutomaticTaskTrigModeEnum type);

    /**
     * 查询执行记录
     *
     * @param query 自动化任务code
     * @return {@link ListExecuteCaseVO}
     */
    List<ListExecuteCaseVO> selectByAutomaticTaskCode(ListExecuteCaseQuery query);

    /**
     * 查询执行用例详情
     *
     * @param entity {@link TestcaseExecuteRecordEntity}
     * @return {@link ExecuteCaseVO}
     */
    ExecuteCaseVO selectExecuteCase(TestcaseExecuteRecordEntity entity);

    /**
     * 查询当前计划用例执行结果
     *
     * @param query {@link FindPlanCaseQuery}
     * @return {@link ExecuteCaseVO}
     */
    ExecuteCaseVO selectCurrentResult(FindPlanCaseQuery query);

    /**
     * 查询上一次执行结果
     *
     * @param testcaseCode 用例code
     * @param testPlanCode 计划code
     * @param testStage    测试阶段
     * @return {@link TmTestPlanCaseEntity}
     */
    TmTestPlanCaseEntity selectLastRecord(
            @Param("testcaseCode") String testcaseCode,
            @Param("testPlanCode") String testPlanCode,
            @Param("testStage") TestPlanStageEnum testStage);

    List<TestcaseExecuteNumVO> selectExecuteNumByTestcaseCodeList(@Param("testcaseCodeList") List<String> testcaseCodeList);

    /**
     * 批量新增
     *
     * @param entity {@link TestcaseExecuteRecordEntity}
     * @param list   用例code
     */
    void insertBatch(@Param("entity") TestcaseExecuteRecordEntity entity, @Param("list") List<String> list);

    /**
     * 查询用例code列表
     *
     * @param automaticTaskCode 自动化任务code
     * @return 用例code
     */
    List<String> selectTestcaseCodeList(@Param("automaticTaskCode") String automaticTaskCode);

    void updateResultNotFinished(@Param("automaticTaskCode") String automaticTaskCode,
                                 @Param("result") TestPlanCaseStatusEnum result);

    List<TestcaseExecuteRecordEntity> selectLastThreeTimes(TestcaseExecuteRecordEntity testcaseExecuteRecordEntity);

    List<ListExecuteCaseVO> selectWithListExecuteCaseVO(@Param("list") List<String> list);

    List<Long> getRecordNotSuccess(String taskCode);


    List<ListExecuteCaseVO>  selectByVersionCode(@Param("versionCode")String versionCode,@Param("productCode")String productCode);

    List<ListExecuteCaseVO> selectAutoByPlanCode(@Param("planCode")String planCode ,@Param("testStage")String testStage);

    List<ListExecuteCaseVO> selectManualByPlanCode(@Param("planCode")String planCode ,@Param("testStage")String testStage);


    List<TestcaseExecuteRecordEntity> selectApiExecuteRecord(ListExecuteDetailReq req);

    List<TestPlanCaseStatusEnum> selectStatusByTaskId(@Param("taskId") String taskId);
}
