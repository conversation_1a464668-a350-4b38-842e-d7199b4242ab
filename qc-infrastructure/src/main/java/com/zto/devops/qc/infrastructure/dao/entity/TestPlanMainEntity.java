package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
@Data
@Table(name = "qc_test_plan_main")
public class TestPlanMainEntity extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 编号
     */
    @Id
    private String code;

    /**
     * 测试计划主表code
     */
    private String testPlanCode;

    /**
     * 关联版本id
     */
    private String versionCode;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 开发人数
     */
    private Integer developerNo;

    /**
     * 测试人数
     */
    private Integer testerNo;

    /**
     * 产品负责人id
     */
    private Long productDirectorId;

    /**
     * 产品负责人名
     */
    private String productDirectorName;


    /**
     * 移动专项测试
     */
    private Boolean mobileSpecialTest;

    /**
     * 集成测试
     */
    private Boolean integrateTest;

    /**
     * 安全扫描
     */
    private Boolean securityScan;

    /**
     * 静态扫描
     */
    private Boolean staticAnalysis;

    /**
     * 性能测试
     */
    private Boolean performanceTest;

    /**
     * 性能测试时间
     */
    private Date performanceTestTime;

    /**
     * 性能测试负责人id
     */
    private Long performanceTestDirectorId;

    /**
     * 性能测试负责人名
     */
    private String performanceTestDirectorName;

    /**
     * 探索性测试
     */
    private Boolean exploratoryTest;

    /**
     * 探索性测试负责人
     */
    private Long exploratoryTestDirectorId;

    /**
     * 探索性测试负责人名
     */
    private String exploratoryTestDirectorName;

    private Date exploratoryTestTime;

    private Long staticAnalysisDirectorId;

    private String staticAnalysisDirectorName;

    private Date staticAnalysisTime;

    private String remarks;

    @GatewayModelProperty(description = "版本开始时间", required = false)
    private Date startDate;


    @GatewayModelProperty(description = "版本上线时间", required = false)
    private Date publishDate;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date presentationDate;
    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date approvalExitDate;
    @GatewayModelProperty(description = "精准返回结果", required = false)
    private String accurateResult;
    @GatewayModelProperty(description = "安全返回结果", required = false)
    private String safetyResult;

    @GatewayModelProperty(description = "安全测试人员id", required = false)
    private String safetyUserId;
    @GatewayModelProperty(description = "安全测试人员名", required = false)
    private String safetyUserName;

    private static final long serialVersionUID = 1L;

}