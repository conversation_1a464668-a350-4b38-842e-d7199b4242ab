package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.report.CodeCoverResult;
import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.report.*;
import com.zto.devops.qc.infrastructure.dao.typehandler.*;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Table(name = "tm_test_report")
@Data
public class TmTestReportEntity extends BaseEntity implements Serializable {

    /**
     * 报告编号
     */
    @Id
    @Column(name = "report_code")
    private String reportCode;

    /**
     * 报告名称
     */
    @Column(name = "report_name")
    private String reportName;

    /**
     * 计划编号
     */
    @Column(name = "plan_code")
    private String planCode;

    /**
     * 报告类型(准入、准出、简易...)
     */
    @Column(name = "report_type")
    @ColumnType(typeHandler = TmReportTypeHandler.class)
    private ReportType reportType;

    /**
     * 版本code--(获取计划信息；产品信息、版本信息)
     */
    @Column(name = "version_code")
    private String versionCode;

    /**
     * 版本名称
     */
    @Column(name = "version_name")
    private String versionName;

    /**
     * 总体测试结果
     */
    @Column(name = "test_result")
    @ColumnType(typeHandler = TmTestResultHandler.class)
    private TmTestResultEnum testResult;

    /**
     * 实际提测时间
     */
    @Column(name = "actual_presentation_date")
    private Date actualPresentationDate;

    /**
     * 实际准出时间
     */
    @Column(name = "actual_approval_exit_date")
    private Date actualApprovalExitDate;

    /**
     * 实际上线时间
     */
    @Column(name = "actual_online_date")
    private Date actualOnlineDate;

    /**
     * 实际测试开始时间
     */
    @Column(name = "actual_test_start")
    private Date actualTestStart;

    /**
     * 实际测试结束时间
     */
    @Column(name = "actual_test_end")
    private Date actualTestEnd;

    /**
     * 验收开始时间
     */
    @Column(name = "check_start_date")
    private Date checkStartDate;

    /**
     * 验收结束时间
     */
    @Column(name = "check_end_date")
    private Date checkEndDate;

    /**
     * 更新测试结果时间
     */
    @Column(name = "update_test_result_date")
    private Date updateTestResultDate;

    /**
     * 自动化测试结果--未执行
     */
    @Column(name = "auto_test_result")
    @ColumnType(typeHandler = TmAutoExecuteResultHandler.class)
    private AutoExecuteResult autoTestResult;

    /**
     * 安全测试人ID
     */
    @Column(name = "security_user_id")
    private Long securityUserId;

    /**
     * 安全测试人名称
     */
    @Column(name = "security_user_name")
    private String securityUserName;

    /**
     * 安全测试结果
     */
    @Column(name = "security_test_result")
    @ColumnType(typeHandler = TmSecurityTestResultHandler.class)
    private SecurityTestResult securityTestResult;

    /**
     * 验收类型 外采，内部
     */
    @Column(name = "check_type")
    @ColumnType(typeHandler = TmCheckTypeHandler.class)
    private CheckType checkType;

    /**
     * 开发人数
     */
    @Column(name = "developer_count")
    private Integer developerCount;

    /**
     * 测试人数
     */
    @Column(name = "tester_count")
    private Integer testerCount;

    /**
     * 测试用例总数
     */
    @Column(name = "test_case_num")
    private Integer testCaseNum;

    /**
     * 计划用例数
     */
    @Column(name = "plan_case_num")
    private Integer planCaseNum;

    /**
     * 通过用例数
     */
    @Column(name = "permit_num")
    private Integer permitNum;

    /**
     * 按计划范围上线  1 是, 0 否
     */
    @Column(name = "as_planed_online")
    private Integer asPlanedOnline;

    /**
     * 是否延期 1 是, 0 否
     */
    private Integer delay;

    /**
     * 总结、分析、描述
     */
    private String summary;

    /**
     * 状态： 草稿， 已发送 未发送
     */
    @Column(name = "status")
    @ColumnType(typeHandler = TestPlanStatusHandler.class)
    private TestPlanStatusEnum status;

    /**
     *  预览
     */
//    @Column(name = "preview")
//    private String preview;

    /**
     * 代码覆盖率结果
     */
    @Column(name = "code_cover_result")
    @ColumnType(typeHandler = CodeCoverResultHandler.class)
    private CodeCoverResult codeCoverResult;

    /**
     * 代码覆盖率不达标原因
     */
    @Column(name = "code_cover_reason")
    private String codeCoverReason;

    /**
     * 计划提测时间
     */
    @Column(name = "plan_presentation_date")
    private Date planPresentationDate;

    /**
     * 计划准出时间
     */
    @Column(name = "plan_approval_exit_date")
    private Date planApprovalExitDate;

    /**
     * 计划上线时间
     */
    @Column(name = "plan_online_date")
    private Date planOnlineDate;

    /**
     * ZUI测试结果
     */
    @Column(name = "zui_test_result")
    @ColumnType(typeHandler = ZUITestResultHandler.class)
    private ZUITestResultEnum zuiTestResult;

    /**
     * 用户体验测试测试结果
     */
    @Column(name = "ui_test_result")
    @ColumnType(typeHandler = UiTestResultHandler.class)
    private UiTestResultEnum uiTestResult;

    private static final long serialVersionUID = 1L;
}