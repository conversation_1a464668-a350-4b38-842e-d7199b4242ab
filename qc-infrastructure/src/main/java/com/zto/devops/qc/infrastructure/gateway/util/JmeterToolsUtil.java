package com.zto.devops.qc.infrastructure.gateway.util;

import com.zto.devops.qc.client.model.dto.JmeterEleNodeFullInfo;
import org.apache.commons.codec.digest.DigestUtils;
import org.dom4j.Document;
import org.dom4j.Element;

import java.io.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class JmeterToolsUtil {

//    /**
//     * 生成n位随机数字符串
//     *
//     * @param length 生成的数量
//     * @return str
//     */
//    public static String randomStr(int length) {
//        if (length < 1) {
//            throw new IllegalArgumentException("length is error");
//        }
//        String baseString = "0123456789";
//        final StringBuilder sb = new StringBuilder(length);
//        int baseLength = baseString.length();
//        sb.append(baseString.charAt(ThreadLocalRandom.current().nextInt("123456789".length())));
//        for (int i = 1; i < length; i++) {
//            int number = ThreadLocalRandom.current().nextInt(baseLength);
//            sb.append(baseString.charAt(number));
//        }
//        return sb.toString();
//    }

    public static String getAscii(String str){
        int ascii = 0;
        for (int i = 0; i < str.length(); i++) {

            char c = str.charAt(i);

            ascii = ascii + (int) c;
        }

        return Integer.toString(ascii);

    }

//    public static JmeterEleNodeFullInfo getSetCookieNode(Document document){
//        JmeterEleNodeFullInfo cookieNode = null;
//        Element root = document.getRootElement();
//        Element rootHashTree = (Element)root.selectSingleNode("hashTree");
//
//        Element tpEle = null;
//        Element tpTreeHash = null;
//        for (Iterator i = rootHashTree.elementIterator(); (i != null) && (i.hasNext()); ) {
//            Element elementTP = (Element) i.next();
//            if (elementTP != null && elementTP.getName().equals("TestPlan")) {
//                tpEle = elementTP;
//            }
//            if (elementTP != null && elementTP.getName().equals("hashTree") && tpEle!=null) {
//                tpTreeHash = elementTP;
//                break;
//            }
//        }
//
//        if(tpTreeHash==null){
//            return cookieNode;
//        }
//
//        for (Iterator j = tpTreeHash.elementIterator(); (j != null) && (j.hasNext()); ) {
//            Element elementSetupThreadGroup = (Element) j.next();
//            if (elementSetupThreadGroup != null && elementSetupThreadGroup.getName().equals("SetupThreadGroup")) {
//                cookieNode = new JmeterEleNodeFullInfo();
//                cookieNode.setMainNode(elementSetupThreadGroup);
//            }
//            if (elementSetupThreadGroup != null && elementSetupThreadGroup.getName().equals("hashTree") && cookieNode!=null) {
//                cookieNode.setTreeNode(elementSetupThreadGroup);
//                break;
//            }
//        }
//
//
//        return cookieNode;
//
//    }


//    public static Map<String, String> splitUrl(String url) {
//        Map<String, String> result = new HashMap<>();
//        String[] info = url.split(":", 2);
//        if (info.length <= 1) {
//            result.put("requestType", "http");
//            result.put("url", url);
//            result.put("port", "");
//        } else {
//            if (info[0].trim().toLowerCase().equals("https")) {
//                result.put("requestType", "https");
//                result.put("port", "443");
//            } else {
//                result.put("requestType", "http");
//                result.put("port", "");
//            }
//            String urlPath[] = info[1].replaceFirst("//", "").split("/", 2);
//            if (info.length <= 1) {
//                result.put("url", urlPath[0]);
//            } else {
//                result.put("url", urlPath[0]);
//                result.put("path", urlPath[1]);
//            }
//
//        }
//        return result;
//    }
//
//    public static List<String> getSourceJsonFile(String sourceFile) {
//        List<String> result = new ArrayList<>();
//        InputStreamReader read = null;
//        BufferedReader bufferedReader = null;
//        try {
//            // 需要读取的文件路径
//            File file = new File(sourceFile);
//
//            // 判断文件是否存在
//            if (file.isFile() && file.exists()) {
//                read = new InputStreamReader(new FileInputStream(file));
//                bufferedReader = new BufferedReader(read);
//                String lineText = null;
//
//                // 按行读取文件并打印,如果需要对内容进行操作可以在这里进行
//                while ((lineText = bufferedReader.readLine()) != null) {
//                    result.add(lineText.trim());
//                    System.out.println(lineText);
//                }
//            } else {
//                System.out.println("file doesn't exist");
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            try {
//                if (bufferedReader != null) {
//                    bufferedReader.close();
//                }
//                if (read != null) {
//                    read.close();
//                }
//            } catch (Throwable e) {
//                e.printStackTrace();
//            }
//        }
//        return result;
//    }

    /*
     * 1.一个运用基本类的实例
     * MessageDigest 对象开始被初始化。该对象通过使用 update 方法处理数据。
     * 任何时候都可以调用 reset 方法重置摘要。
     * 一旦所有需要更新的数据都已经被更新了，应该调用 digest 方法之一完成哈希计算。
     * 对于给定数量的更新数据，digest 方法只能被调用一次。
     * 在调用 digest 之后，MessageDigest 对象被重新设置成其初始状态。
     */
//    public static void encrypByMd5(String context) {
//        try {
//            MessageDigest md = MessageDigest.getInstance("MD5");
//            md.update(context.getBytes());//update处理
//            byte[] encryContext = md.digest();//调用该方法完成计算
//
//            int i;
//            StringBuffer buf = new StringBuffer("");
//            for (int offset = 0; offset < encryContext.length; offset++) {//做相应的转化（十六进制）
//                i = encryContext[offset];
//                if (i < 0) i += 256;
//                if (i < 16) buf.append("0");
//                buf.append(Integer.toHexString(i));
//            }
//            System.out.println("32result: " + buf.toString());// 32位的加密
//            System.out.println("16result: " + buf.toString().substring(8, 24));// 16位的加密
//        } catch (NoSuchAlgorithmException e) {
//            // TODO Auto-generated catch block
//            e.printStackTrace();
//        }
//    }

    /*
     * 2.使用开发的jar直接应用
     *  使用外部的jar包中的类：import org.apache.commons.codec.digest.DigestUtils;
     *  对上面内容的一个封装使用方便
     */
//    public static void encrypByMd5Jar(String context) {
//        String md5Str = DigestUtils.md5Hex(context);
//        System.out.println("32result: " + md5Str);
//
//    }

//    public static void main(String[] args) {
//
//        encrypByMd5("yang");
//        encrypByMd5Jar("yang");
//    }

}
