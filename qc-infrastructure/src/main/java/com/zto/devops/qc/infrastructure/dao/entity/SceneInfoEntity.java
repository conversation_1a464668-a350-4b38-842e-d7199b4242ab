package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoStatusEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.SceneInfoEnableTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.SceneInfoStatusTypeHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "tm_scene_info")
public class SceneInfoEntity {

    @Id
    @GeneratedValue(generator = "JDBC", strategy = GenerationType.IDENTITY)
    private Long id;

    private String sceneCode;

    private String sceneName;

    private String productCode;

    private String automaticSourceCode;

    private Integer sceneVersion;

    private String sceneInfoDesc;

    private String sceneOssPath;

    private String sceneOssFile;

    private String sceneBackDataMd5;

    @ColumnType(typeHandler = SceneInfoStatusTypeHandler.class)
    private SceneInfoStatusEnum status;

    private String stepRecord;

    @ColumnType(typeHandler = SceneInfoEnableTypeHandler.class)
    private SceneInfoEnableEnum enable;

    @Column(name = "scene_type")
    private Integer sceneType;

    @Column(name = "creator_id")
    private Long creatorId;
    @Column(name = "creator")
    private String creator;
    @Column(name = "gmt_create")
    private Date gmtCreate;
    @Column(name = "modifier_id")
    private Long modifierId;
    @Column(name = "modifier")
    private String modifier;
    @Column(name = "gmt_modified")
    private Date gmtModified;

    private String sceneFrontData;

    private String sceneBackData;

    private Boolean shareStatus;

    private Boolean isCollect;

    private String sceneTagData;

//    public void preCreate(HasTransactor hasTransactor) {
//        User user = hasTransactor.getTransactor();
//        if (null != user) {
//            this.setCreatorId(user.getUserId());
//            this.setCreator(user.getUserName());
//            this.setGmtCreate(new Date());
//            this.setModifierId(user.getUserId());
//            this.setModifier(user.getUserName());
//            this.setGmtModified(new Date());
//        }
//
//    }
//
//    public void preUpdate(HasTransactor hasTransactor) {
//        User user = hasTransactor.getTransactor();
//        if (null != user) {
//            this.setModifierId(user.getUserId());
//            this.setModifier(user.getUserName());
//            this.setGmtModified(new Date());
//        }
//
//    }
//
//    public void preCreate(User user) {
//        if (null != user) {
//            this.setCreatorId(user.getUserId());
//            this.setCreator(user.getUserName());
//            this.setGmtCreate(new Date());
//            this.setModifierId(user.getUserId());
//            this.setModifier(user.getUserName());
//            this.setGmtModified(new Date());
//        }
//
//    }
//
//    public void preUpdate(User user) {
//        if (null != user) {
//            this.setModifierId(user.getUserId());
//            this.setModifier(user.getUserName());
//            this.setGmtModified(new Date());
//        }
//
//    }
}
