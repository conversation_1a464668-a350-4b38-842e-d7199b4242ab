package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.issue.TagTypeEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.TagTypeHandler;
import lombok.Data;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@ToString
@Data
@Table(name = "qc_tag")
public class TagEntity extends BaseEntity {

    /**
     * 编码
     */
    @Id
    private String code;

    /**
     * 所属领域
     */
    @Column(name = "domain")
    @ColumnType(typeHandler = DomainHandler.class)
    private DomainEnum domain;

    /**
     * 业务编码
     */
    @Column(name = "business_code")
    private String businessCode;

    /**
     * 标签类型
     */
    @Column(name = "type")
    @ColumnType(typeHandler = TagTypeHandler.class)
    private TagTypeEnum type;

    /**
     * 标签别名
     */
    @Column(name = "tag_alias")
    private String tagAlias;

    /**
     * 标签名称
     */
    @Column(name = "tag_name")
    private String tagName;

    /**
     * 标签编码（lbd_user.user_profile.code)
     */
    @Column(name = "tag_code")
    private String tagCode;

}