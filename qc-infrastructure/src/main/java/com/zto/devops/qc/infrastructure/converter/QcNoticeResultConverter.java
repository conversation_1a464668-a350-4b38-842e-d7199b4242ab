package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.QcNoticeResultEntityDO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.infrastructure.dao.entity.QcNoticeResultEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface QcNoticeResultConverter {

    List<SendUserInfoVO> convert(List<QcNoticeResultEntity> entitys);

    @Mapping(target = "userStation",source = "station")
    @Mapping(target = "userAvatar",source = "avatar")
    QcNoticeResultEntity convert(SendUserInfoVO sendUserInfoVO);
    List<QcNoticeResultEntity> convertSendUserInfoVOS(List<SendUserInfoVO> sendUserInfoVO);

    List<QcNoticeResultEntityDO> convert2DOList(List<QcNoticeResultEntity> entityList);
}
