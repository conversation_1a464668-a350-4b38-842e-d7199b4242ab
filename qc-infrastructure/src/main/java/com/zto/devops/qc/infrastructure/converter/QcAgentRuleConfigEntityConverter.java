package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.QcAgentRuleConfigEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.QcAgentRuleConfigEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface QcAgentRuleConfigEntityConverter {
    List<QcAgentRuleConfigEntityDO> convert2DOList(List<QcAgentRuleConfigEntity> entityList);

    QcAgentRuleConfigEntityDO convert2DO(QcAgentRuleConfigEntity entity);
}
