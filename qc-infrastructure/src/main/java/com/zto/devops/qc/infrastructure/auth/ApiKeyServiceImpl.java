package com.zto.devops.qc.infrastructure.auth;

import com.zto.devops.qc.client.model.auth.ApiKeyInfo;
import com.zto.devops.qc.domain.gateway.auth.ApiKeyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * API密钥管理服务实现
 * 
 * 注意：这里使用内存存储作为示例，生产环境建议使用数据库存储
 * 
 * <AUTHOR>
 * @since 2025-08-14
 */
@Slf4j
@Service
public class ApiKeyServiceImpl implements ApiKeyService {
    
    // 内存存储，生产环境应该使用数据库
    private final Map<String, ApiKeyInfo> apiKeyStore = new ConcurrentHashMap<>();
    
    // 初始化一些测试用的密钥
    public ApiKeyServiceImpl() {
        initTestApiKeys();
    }
    
    @Override
    public ApiKeyInfo getApiKeyInfo(String accessKey) {
        ApiKeyInfo apiKeyInfo = apiKeyStore.get(accessKey);
        if (apiKeyInfo != null) {
            log.debug("找到API密钥: accessKey={}, appName={}", accessKey, apiKeyInfo.getAppName());
        } else {
            log.warn("未找到API密钥: accessKey={}", accessKey);
        }
        return apiKeyInfo;
    }
    
    @Override
    public ApiKeyInfo createApiKey(String appName, String appDescription, String creator) {
        String accessKey = generateAccessKey();
        String secretKey = generateSecretKey();
        
        ApiKeyInfo apiKeyInfo = new ApiKeyInfo();
        apiKeyInfo.setAccessKey(accessKey);
        apiKeyInfo.setSecretKey(secretKey);
        apiKeyInfo.setAppName(appName);
        apiKeyInfo.setAppDescription(appDescription);
        apiKeyInfo.setEnabled(true);
        apiKeyInfo.setCreateTime(new Date());
        apiKeyInfo.setUpdateTime(new Date());
        apiKeyInfo.setCreator(creator);
        apiKeyInfo.setUsageCount(0L);
        
        apiKeyStore.put(accessKey, apiKeyInfo);
        
        log.info("创建API密钥成功: accessKey={}, appName={}", accessKey, appName);
        return apiKeyInfo;
    }
    
    @Override
    public void updateApiKeyStatus(String accessKey, boolean enabled) {
        ApiKeyInfo apiKeyInfo = apiKeyStore.get(accessKey);
        if (apiKeyInfo != null) {
            apiKeyInfo.setEnabled(enabled);
            apiKeyInfo.setUpdateTime(new Date());
            log.info("更新API密钥状态: accessKey={}, enabled={}", accessKey, enabled);
        }
    }
    
    @Override
    public void updateUsageStats(String accessKey) {
        ApiKeyInfo apiKeyInfo = apiKeyStore.get(accessKey);
        if (apiKeyInfo != null) {
            apiKeyInfo.setLastUsedTime(new Date());
            apiKeyInfo.setUsageCount(apiKeyInfo.getUsageCount() + 1);
        }
    }
    
    @Override
    public ApiKeyInfo rotateSecretKey(String accessKey) {
        ApiKeyInfo apiKeyInfo = apiKeyStore.get(accessKey);
        if (apiKeyInfo != null) {
            String newSecretKey = generateSecretKey();
            apiKeyInfo.setSecretKey(newSecretKey);
            apiKeyInfo.setUpdateTime(new Date());
            log.info("轮换API密钥Secret: accessKey={}", accessKey);
            return apiKeyInfo;
        }
        return null;
    }
    
    /**
     * 生成AccessKey
     * 格式：AK + 16位随机字符
     */
    private String generateAccessKey() {
        return "AK" + DigestUtils.md5Hex(UUID.randomUUID().toString()).substring(0, 16).toUpperCase();
    }
    
    /**
     * 生成SecretKey
     * 32位随机字符串
     */
    private String generateSecretKey() {
        return DigestUtils.md5Hex(UUID.randomUUID().toString() + System.nanoTime());
    }
    
    /**
     * 初始化测试用的API密钥
     */
    private void initTestApiKeys() {
        // 为脚本调用创建测试密钥
        ApiKeyInfo scriptApiKey = new ApiKeyInfo();
        scriptApiKey.setAccessKey("AK_SCRIPT_TEST_001");
        scriptApiKey.setSecretKey("sk_test_script_scene_data_center_secret_key_001");
        scriptApiKey.setAppName("SceneDataCenter Script");
        scriptApiKey.setAppDescription("用于场景数据中心脚本调用的API密钥");
        scriptApiKey.setEnabled(true);
        scriptApiKey.setCreateTime(new Date());
        scriptApiKey.setUpdateTime(new Date());
        scriptApiKey.setCreator("system");
        scriptApiKey.setUsageCount(0L);
        
        apiKeyStore.put(scriptApiKey.getAccessKey(), scriptApiKey);
        
        log.info("初始化测试API密钥: accessKey={}", scriptApiKey.getAccessKey());
    }
}
