package com.zto.devops.qc.infrastructure.gateway.repository;

import com.github.pagehelper.PageHelper;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.coverage.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewTypeEnum;
import com.zto.devops.qc.client.model.dto.CoverageRecordBasicEntityDO;
import com.zto.devops.qc.client.model.dto.CoverageRecordEntityDO;
import com.zto.devops.qc.client.model.parameter.CoverageRecordEditParameter;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.*;
import com.zto.devops.qc.client.model.testmanager.coverage.event.CoverageReasonEditEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoveragePublishQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageRecordPageQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageResultQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageTaskQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmTestReportVO;
import com.zto.devops.qc.client.service.coverage.model.req.CoverageRecordReq;
import com.zto.devops.qc.client.service.coverage.model.resp.CoverageRecordResp;
import com.zto.devops.qc.domain.gateway.repository.CoverageRepository;
import com.zto.devops.qc.infrastructure.converter.CoverageConverter;
import com.zto.devops.qc.infrastructure.dao.entity.*;
import com.zto.devops.qc.infrastructure.dao.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;

@Repository
@Slf4j
public class CoverageRepositoryImpl implements CoverageRepository {

    @Autowired
    private CoveragePublishMapper coveragePublishMapper;
    @Autowired
    private CoverageExecMapper coverageExecMapper;
    @Autowired
    private CoverageRecordMapper coverageRecordMapper;
    @Autowired
    private CoverageRecordBasicMapper coverageRecordBasicMapper;
    @Autowired
    private CoverageSwitchMapper coverageSwitchMapper;
    @Autowired
    private CoverageConverter converter;
    @Autowired
    private TmTestPlanMapper tmTestPlanMapper;
    @Autowired
    private TmTestReportMapper tmTestReportMapper;
    @Autowired
    private CoverageBranchBasicMapper coverageBranchBasicMapper;


    @Override
    public List<CoveragePublishVO> getLatestPublishRecord(CoveragePublishQuery query) {
        List<CoveragePublishEntity> entityList = coveragePublishMapper.getLatestPublishRecord(query);
        return converter.converterPublishVO(entityList);
    }

    @Override
    public void updateCoverageRecordById(Long id, String status, String recordErrorMsg, String recordUrl, String commitId,
                                         String basicCommitId, String basicBranchName, BigDecimal recordRate,
                                         String remark, Integer codeCoverNum, Integer codeSum, String envName, String gitCompareUrl,
                                         String bucketName, String fileName) {
        coverageRecordBasicMapper.updateCoverageRecordById(id, status, recordErrorMsg, recordUrl, commitId,
                basicCommitId, basicBranchName, recordRate, remark, codeCoverNum, codeSum, envName, gitCompareUrl, bucketName, fileName);
    }

    @Override
    public List<CoverageRecordBasicVO> getInitialCoverageRecords(CoverageRecordGenerateParameter parameter) {
        List<CoverageRecordBasicEntity> entityList = coverageRecordBasicMapper.getInitialCoverageRecords(parameter);
        return converter.converterBasicVO(entityList);
    }

    @Override
    public void updateByPrimaryKeySelective(CoverageRecordBasicVO vo) {
        coverageRecordBasicMapper.updateByPrimaryKeySelective(converter.converter(vo));
    }

    @Override
    public List<CoverageRecordVO> getCoverageRecordList(CoverageRecordPageQuery coverageRecordPageQuery) {
        List<CoverageRecordEntity> entityList = coverageRecordMapper.getCoverageRecordList(coverageRecordPageQuery);
        return converter.converter(entityList);
    }

    @Override
    public CoverageRecordResp getLatestCoverageReport(CoverageRecordReq req) {
        PageHelper.startPage(1, 1);
        Example example = new Example(CoverageRecordBasicEntity.class);
        example.createCriteria()
                .andEqualTo("versionCode", req.getVersionCode())
                .andEqualTo("appId", req.getAppId())
                .andEqualTo("enable", Boolean.TRUE);
        example.orderBy("gmtCreate").desc();
        List<CoverageRecordBasicEntity> entityList = coverageRecordBasicMapper.selectByExample(example);
        return CollectionUtils.isNotEmpty(entityList) ? converter.converterEntity2Resp(entityList.get(0)) : null;
    }

    @Override
    public CoveragePublishVO getLatestPublishRecordByEntity(CoveragePublishVO publishVO) {
        CoveragePublishEntity entity = coveragePublishMapper.getLatestPublishRecordByEntity(converter.converter(publishVO));
        return converter.converter(entity);
    }

    @Override
    public CoveragePublishVO getFirstBranchPublish(CoveragePublishVO publishVO) {
        CoveragePublishEntity entity = coveragePublishMapper.getFirstBranchPublish(converter.converter(publishVO));
        return converter.converter(entity);
    }

    @Override
    public void delCoverageRecord(CoverageRecordBasicVO basicVO) {
        coverageRecordBasicMapper.delCoverageRecord(converter.converter(basicVO));
    }

    @Override
    public CoverageRecordBasicVO insertCoverageRecordBasic(CoverageRecordBasicVO basicVO) {
        CoverageRecordBasicEntity entity = converter.converter(basicVO);
        coverageRecordBasicMapper.insertSelective(entity);
        return converter.converterBasicVO(entity);
    }

    @Override
    public void insertSelective(CoveragePublishVO publishVO) {
        coveragePublishMapper.insertSelective(converter.converter(publishVO));
    }

    @Override
    public CoverageRecordGenerateVO selectCoverageRecords(CoverageRecordBasicVO basicVO) {
        CoverageRecordGenerateEntity entity = coverageRecordBasicMapper.selectCoverageRecords(converter.converter(basicVO));
        return converter.converterVO(entity);
    }

    @Override
    public List<CoverageExecVO> getExecList(String versionCode, String appId, String commitId, String flowLaneType, DiffTypeEnum diffType) {
        List<CoverageExecEntity> entityList = coverageExecMapper.getExecList(versionCode, appId, commitId, flowLaneType, diffType);
        return converter.converterExecVO(entityList);
    }

    @Override
    public List<CoveragePublishVO> getMiddlePublishEntity(CoverageRecordGenerateVO generateVO) {
        List<CoveragePublishEntity> entityList = coveragePublishMapper.getMiddlePublishEntity(converter.converterGenerateEntity(generateVO));
        return converter.converterPublishVO(entityList);
    }

    @Override
    public List<CoverageExecVO> getMiddleExecList(Set<String> middleCommitIdList, String appId, String versionCode, String branchName, String flowLaneType, DiffTypeEnum diffType) {
        List<CoverageExecEntity> entityList = coverageExecMapper.getMiddleExecList(middleCommitIdList, appId, versionCode, branchName, flowLaneType, diffType);
        return converter.converterExecVO(entityList);
    }

    @Override
    public List<CoveragePublishVO> getLatestPublishRecordByProductCode(CoveragePublishQuery query) {
        List<CoveragePublishEntity> entityList = coveragePublishMapper.getLatestPublishRecordByProductCode(query);
        return converter.converterPublishVO(entityList);
    }

    @Override
    public List<CoveragePublishVO> getPublishRecordByCommitId(CoveragePublishVO publishVO) {
        List<CoveragePublishEntity> entityList = coveragePublishMapper.getPublishRecordByCommitId(converter.converter(publishVO));
        return converter.converterPublishVO(entityList);
    }

    @Override
    public void insertSelective(CoverageExecVO execVO) {
        coverageExecMapper.insertSelective(converter.converterExecEntity(execVO));
    }

    @Override
    public CoverageVersionRateVO getVersionRecordRate(String versionCode) {
        return coverageRecordMapper.getVersionRecordRate(versionCode);
    }

    @Override
    public void editCoverage(CoverageRecordEditParameter parameter) {
        Example example = new Example(CoverageRecordBasicEntity.class);
        example.createCriteria()
                .andEqualTo("versionCode", parameter.getVersionCode())
                .andEqualTo("appId", parameter.getAppId())
                .andEqualTo("diffType", parameter.getDiffType())
                .andEqualTo("enable",
                        Boolean.TRUE);
        CoverageRecordBasicEntity entity = new CoverageRecordBasicEntity();
        entity.setComment(parameter.getComment());
        coverageRecordBasicMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<TmTestPlanVO> getTestPlan(CoverageRecordPageQuery query) {
        Example example = new Example(TmTestPlanEntity.class);
        example.createCriteria().andEqualTo("enable", 1)
                .andEqualTo("versionCode", query.getVersionCode())
                .andEqualTo("type", TestPlanNewTypeEnum.TEST_PLAN);
        List<TmTestPlanEntity> entityList = tmTestPlanMapper.selectByExample(example);
        return converter.converterPlanVO(entityList);
    }

    @Override
    public List<CoverageTaskVO> selectTaskIdFromCoverage(CoverageTaskQuery query) {
        return coverageRecordMapper.selectTaskIdFromCoverage(query);
    }

    @Override
    public List<CoverageTaskVO> selectCoverageByTaskId(CoverageTaskQuery query) {
        return coverageRecordMapper.selectCoverageByTaskId(query);
    }

    @Override
    public List<CoverageAppInfoVO> selectListByVersionCode(String versionCode, List<String> list) {
        return coverageRecordBasicMapper.selectListByVersionCode(versionCode, list);
    }

    @Override
    public List<CoverageAppInfoVO> selectListByVersionCodeList(List<String> versionCodeList, List<String> list) {
        Example example = new Example(CoverageRecordBasicEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("diffType", DiffTypeEnum.INCREMENT.name())
                .andIn("versionCode", versionCodeList);
        if (CollectionUtils.isNotEmpty(list)) {
            criteria.andIn("appId", list);
        }
        example.orderBy("appId");
        return converter.converterEntity2VO(coverageRecordBasicMapper.selectByExample(example));
    }

    @Override
    public TmTestReportVO getTmTestReport(CoverageResultQuery query) {
        Example example = new Example(TmTestReportEntity.class);
        example.createCriteria()
                .andEqualTo("versionCode", query.getVersionCode())
                .andEqualTo("reportType", query.getReportType())
                .andEqualTo("enable", Boolean.TRUE);
        TmTestReportEntity reportEntity = tmTestReportMapper.selectOneByExample(example);
        return converter.converterReportVO(reportEntity);
    }

    @Override
    public List<CoverageRecordBasicVO> getExistCoverageRecords(String versionCode, List<String> branchNames, String appType) {
        List<CoverageRecordBasicEntity> entityList = coverageRecordBasicMapper.getExistCoverageRecords(versionCode, branchNames, AppTypeEnum.JAVA.name());
        return converter.converterBasicVO(entityList);
    }

    @Override
    public void batchInsert(List<CoverageRecordBasicVO> list) {
        coverageRecordBasicMapper.batchInsert(converter.converterBasicEntity(list));
    }

    @Override
    public int selectBranchBasicCount(CoverageBranchBasicVO basicVO) {
        return coverageBranchBasicMapper.selectCount(converter.converterBasicEntity(basicVO));
    }

    @Override
    public void batchInsertBranchBasic(List<CoverageBranchBasicVO> entityList) {
        coverageBranchBasicMapper.batchInsert(converter.converterBasicEntityList(entityList));
    }

    @Override
    public void updateBatch(CoverageReasonEditEvent event) {
        if (null == event || CollectionUtils.isEmpty(event.getCoverageReasonVOS())) {
            return;
        }
        List<CoverageReasonVO> voList = event.getCoverageReasonVOS();
        voList.forEach(vo -> {
            Example example = new Example(CoverageRecordBasicEntity.class);
            example.createCriteria().andEqualTo("appId", vo.getAppId())
                    .andEqualTo("versionCode", vo.getVersionCode())
                    .andEqualTo("enable", Boolean.TRUE);
            CoverageRecordBasicEntity entity = new CoverageRecordBasicEntity();
            entity.setRemark(vo.getFinalReason());
            entity.setModifier(event.getTransactor().getUserName());
            entity.setModifierId(event.getTransactor().getUserId());
            entity.setGmtModified(event.getOccurred());
            coverageRecordBasicMapper.updateByExampleSelective(entity, example);
        });
    }

    @Override
    public void updateCoverageRemark(String versionCode, String appId, CoverageRecordBasicEntityDO entityDO) {
        if (StringUtils.isBlank(versionCode) || StringUtils.isBlank(appId)) {
            return;
        }
        Example example = new Example(CoverageRecordBasicEntity.class);
        example.createCriteria().andEqualTo("appId", appId)
                .andEqualTo("versionCode", versionCode)
                .andEqualTo("enable", Boolean.TRUE);
        CoverageRecordBasicEntity entity = converter.converter(entityDO);
        coverageRecordBasicMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public String selectCoverageRemark(String versionCode, String appId) {
        if (StringUtils.isBlank(versionCode) || StringUtils.isBlank(appId)) {
            return StringUtils.EMPTY;
        }
        Example example = new Example(CoverageRecordBasicEntity.class);
        example.createCriteria().andEqualTo("appId", appId)
                .andEqualTo("versionCode", versionCode)
                .andEqualTo("recordType", RecordTypeEnum.BRANCH.name())
                .andEqualTo("enable", Boolean.TRUE);
        List<CoverageRecordBasicEntity> list = coverageRecordBasicMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.get(0).getRemark();
    }

    @Override
    public void resetCoverageReason(String versionCode, String appId, User user) {
        Example example = new Example(CoverageRecordBasicEntity.class);
        example.createCriteria().andEqualTo("appId", appId)
                .andEqualTo("versionCode", versionCode)
                .andEqualTo("enable", Boolean.TRUE);
        CoverageRecordBasicEntity entity = new CoverageRecordBasicEntity();
        entity.setRemark("");
        entity.setModifier(Objects.nonNull(user) ? user.getUserName() : "system");
        entity.setModifierId(Objects.nonNull(user) ? user.getUserId() : 0);
        entity.setGmtModified(new Date());
        coverageRecordBasicMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<CoverageRecordBasicVO> getCoverageRunningList(String timeoutDate) {
        return converter.converterBasicVO(coverageRecordBasicMapper.getCoverageRunningList(timeoutDate));
    }

    @Override
    public List<CoverageRecordBasicEntityDO> getLastRecordEntity(CoverageRecordGenerateVO vo) {
        Example example = new Example(CoverageRecordBasicEntity.class);
        example.createCriteria()
                .andEqualTo("versionCode", vo.getVersionCode())
                .andEqualTo("appId", vo.getAppId())
                .andEqualTo("status", RecordStatusEnum.SUCCEED)
                .andEqualTo("branchName", vo.getBranchName())
                .andEqualTo("diffType", DiffTypeEnum.INCREMENT)
                .andNotEqualTo("commitId", vo.getCommitId());
        example.orderBy("id").desc();
        List<CoverageRecordBasicEntity> recordEntityList = coverageRecordBasicMapper.selectByExample(example);
        return converter.converterBasicDO(recordEntityList);
    }

    @Override
    public List<CoveragePublishVO> getPublishRecordLastDay(Date startTime, Date endTime) {
        List<CoveragePublishEntity> entityList = coveragePublishMapper.getPublishRecordLastDay(startTime, endTime);
        return converter.converterPublishVO(entityList);
    }

    @Override
    public String getGitCompareUrl(String versionCode, String appId, String branchName) {
        return coverageRecordBasicMapper.getGitCompareUrl(versionCode, appId, branchName);
    }

    @Override
    public List<CoverageRecordBasicVO> getCoverageRecordListByLimit() {
        List<CoverageRecordBasicEntity> entityList = coverageRecordBasicMapper.getCoverageRecordListByLimit();
        return converter.converterBasicVO(entityList);
    }

    @Override
    public List<CoverageRecordBasicVO> getExistCoverageRecordsByAppIdList(String versionCode,
                                                                          List<String> branchNames,
                                                                          String appType,
                                                                          List<String> appIdList) {
        if (CollectionUtils.isEmpty(appIdList)) {
            return new ArrayList<>();
        }
        List<CoverageRecordBasicEntity> entityList = coverageRecordBasicMapper.getExistCoverageRecordsByAppIdList(versionCode,
                branchNames, appType, appIdList);
        return converter.converterBasicVO(entityList);
    }

    @Override
    public CoverageRecordBasicVO getInitialCoverageRecordByAppId(CoverageRecordGenerateParameter parameter, String appId) {
        CoverageRecordGenerateParameter toQueryParam = new CoverageRecordGenerateParameter();
        BeanUtils.copyProperties(parameter, toQueryParam);
        toQueryParam.setAppIdList(Collections.singletonList(appId));
        List<CoverageRecordBasicEntity> entityList = coverageRecordBasicMapper.getInitialCoverageRecords(toQueryParam);
        return CollectionUtils.isNotEmpty(entityList) ? converter.converterBasicVO(entityList.get(0)) : null;
    }

    @Override
    public List<CoverageRecordBasicEntityDO> selectAutoGenerateResultByTaskId(String productCode, String taskId, List<RecordStatusEnum> statusList) {
        log.info("selectAutoGenerateResultByTaskId_productCode:{}_taskId:{}", productCode, taskId);
        if (StringUtils.isBlank(productCode) || StringUtils.isBlank(taskId)) {
            return new ArrayList<>();
        }
        Example example = new Example(CoverageRecordBasicEntity.class);
        example.selectProperties("versionCode", "versionName", "productCode", "appId", "appName", "status", "recordErrorMsg");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("productCode", productCode)
                .andEqualTo("taskId", taskId)
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("generateType", GenerateTypeEnum.AUTO.name())
                .andEqualTo("recordType", RecordTypeEnum.BRANCH.name())
                .andEqualTo("diffType", DiffTypeEnum.INCREMENT.name());
        if (CollectionUtils.isNotEmpty(statusList)) {
            criteria.andIn("status", statusList);
        }
        example.orderBy("gmtModified").desc();
        List<CoverageRecordBasicEntity> entityList = coverageRecordBasicMapper.selectByExample(example);
        return CollectionUtils.isNotEmpty(entityList) ? converter.converterBasicDO(entityList) : new ArrayList<>();
    }

    @Override
    public List<CoverageRecordBasicEntityDO> selectAutoGenerateResultByAppIdList(String versionCode, List<String> appIdList) {
        List<CoverageRecordBasicEntity> entityList = coverageRecordBasicMapper.selectAutoGenerateResultByAppIdList(versionCode, appIdList);
        return converter.converterBasicDO(entityList);
    }

    @Override
    public CoverageRecordEntityDO queryCoverageRecordById(Long id) {
        Example example = new Example(CoverageRecordBasicEntity.class);
        example.createCriteria()
                .andEqualTo("id", id);
        CoverageRecordBasicEntity entity = coverageRecordBasicMapper.selectOneByExample(example);
        return converter.converterEntityDO(entity);
    }

    @Override
    public List<CoverageVersionRateVO> getVersionRecordRateList(List<String> versionCode) {
        if (CollectionUtils.isEmpty(versionCode)) {
            return Collections.emptyList();
        }
        return coverageRecordMapper.getVersionRecordRateList(versionCode);
    }

    @Override
    public List<CoverageBranchBasicVO> getCoverageBasicByVersionCodesAndAppId(Set<String> versionCodes, String appId) {
        if (CollectionUtils.isEmpty(versionCodes) || StringUtils.isBlank(appId)) {
            return new ArrayList<>();
        }
        Example example = new Example(CoverageRecordBasicEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("appId", appId)
                .andIn("versionCode", versionCodes);
        List<CoverageBranchBasicEntity> entityList = coverageBranchBasicMapper.selectByExample(example);
        return CollectionUtils.isEmpty(entityList) ? new ArrayList<>() : converter.converterBasicEntity2VO(entityList);
    }

    @Override
    public void batchUpdateBranchBasic(List<CoverageBranchBasicVO> toUpdateList) {
        if (CollectionUtils.isEmpty(toUpdateList)) {
            return;
        }
        List<CoverageBranchBasicEntity> entityList = converter.converterBasicEntityList(toUpdateList);
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        entityList.forEach(item -> coverageBranchBasicMapper.updateByPrimaryKeySelective(item));
    }

    @Override
    public List<CoveragePublishVO> getByCommitId(String versionCode, String commitId, String appId) {
        if (StringUtils.isBlank(versionCode) || StringUtils.isBlank(commitId) || StringUtils.isBlank(appId)) {
            return new ArrayList<>();
        }
        Example example = new Example(CoveragePublishEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("appId", appId)
                .andEqualTo("versionCode",versionCode)
                .andEqualTo("commitId",commitId);
        List<CoveragePublishEntity> entityList = coveragePublishMapper.selectByExample(example);
        return CollectionUtils.isEmpty(entityList) ? new ArrayList<>() : converter.converterPublishVO(entityList);
    }
}
