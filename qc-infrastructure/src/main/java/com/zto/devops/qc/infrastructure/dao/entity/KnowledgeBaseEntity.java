package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "qc_knowledge_base")
public class KnowledgeBaseEntity extends BaseEntity {


    /**
     * 编码
     */
    @Id
    private Long id;

    /**
     * 知识库ID
     */
    @Column(name = "space_id")
    private String spaceId;

    /**
     * 产品code
     */
    @Column(name = "product_code")
    private String productCode;

    /**
     * 附件文档类型
     */
    @Column(name = "url")
    private String url;

}
