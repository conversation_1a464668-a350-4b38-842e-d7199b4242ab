package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiDocVersionVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiLiteInfoVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiSampleCaseVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiVO;
import com.zto.devops.qc.client.service.testmanager.apitest.model.PageApiInfoReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.PageApiReq;
import com.zto.devops.qc.infrastructure.dao.entity.ApiTestEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ApiTestMapper extends Mapper<ApiTestEntity> {

    List<ApiDocVersionVO> selectDocVersionList(@Param("productCode") String productCode, @Param("docVersion") String docVersion);

    List<ApiLiteInfoVO> selectApiLiteGroup(@Param("productCode") String productCode,
                                           @Param("nameOrAddress") String nameOrAddress,
                                           @Param("statusList") List<Integer> statusList,
                                           @Param("apiCode") String apiCode,
                                           @Param("searchName") Boolean searchName);

    List<ApiTestEntity> selectApiList(PageApiReq req);

    List<ApiVO> selectUniqueApiList(PageApiReq req);

    List<ApiSampleCaseVO> selectUniqueApiWithSampleCase(PageApiInfoReq req);

    List<String> selectApiCode(PageApiInfoReq req);
}
