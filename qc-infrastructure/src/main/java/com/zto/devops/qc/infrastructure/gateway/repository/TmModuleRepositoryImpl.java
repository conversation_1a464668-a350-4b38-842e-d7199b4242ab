package com.zto.devops.qc.infrastructure.gateway.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.devops.qc.client.model.testmanager.report.event.TmPermitReportEvent;
import com.zto.devops.qc.domain.gateway.repository.TmModuleRepository;
import com.zto.devops.qc.infrastructure.converter.TestReportEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.ModuleTestEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.ModuleTestMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Component
public class TmModuleRepositoryImpl implements TmModuleRepository {

    @Autowired
    private ModuleTestMapper moduleTestMapper;

    @Autowired
    private TestReportEntityConverter convertor;

    @Override
    public void addList(TmPermitReportEvent event) {
        List<TmModuleTestVO> moduleTestVOS = event.getModuleTestVOS();
        Example example = new Example(ModuleTestEntity.class);
        if (StringUtil.isNotBlank(event.getReportCode())) {
            example.createCriteria().andEqualTo("reportCode", event.getReportCode());
            moduleTestMapper.deleteByExample(example);
        }
        if (CollectionUtil.isNotEmpty(moduleTestVOS)) {
            List<ModuleTestEntity> entityList = convertor.convertModuleTests(moduleTestVOS);
            entityList.stream().forEach(entity -> {
                entity.preCreate(event);
                entity.setReportCode(event.getReportCode());
                moduleTestMapper.insertSelective(entity);
            });
        }
    }

}
