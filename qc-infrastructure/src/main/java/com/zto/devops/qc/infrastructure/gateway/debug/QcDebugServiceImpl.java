package com.zto.devops.qc.infrastructure.gateway.debug;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.StatusCode;
import com.zto.devops.qc.client.model.rpc.pipeline.FeatureVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskInfo;
import com.zto.devops.qc.domain.gateway.debug.IQcDebugService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.util.ApiDebugLogService;
import com.zto.devops.qc.domain.util.GenerateApiCaseUtil;
import com.zto.devops.qc.infrastructure.config.QcDebugConfig;
import com.zto.lbd.kernel.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class QcDebugServiceImpl implements IQcDebugService {

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private IPipelineRpcService pipelineRpcService;

    @Autowired
    private QcDebugConfig qcDebugConfig;

    @Autowired
    private ApiDebugLogService apiDebugLogService;

    @Autowired
    private RedisService redisService;

    private final static String DEBUG_TASK_KEY_PREFIX = "debug::task::";

    private final static long DEBUG_KEY_EXPIRED = 30L;

    private final static String NEXT_DEBUG_SERVER_KEY = "next::debug::server";

    private final static long NEXT_DEBUG_SERVER_KEY_EXPIRED = 8L;

    @Override
    public void execute(DebugTaskInfo taskInfo, boolean isDocument) {
        Map<String, Object> req = new HashMap<>();
        req.put("taskId", taskInfo.getTaskId());
        req.put("ztoenv", taskInfo.getZtoenv());
        req.put("dubboTag", getDubboTag());
        req.put("sceneCode", taskInfo.getSceneCode());
        req.put("sceneName", taskInfo.getSceneName());
        req.put("productCode", taskInfo.getProductCode());
        if (!isAutoEnv(taskInfo.getProductCode())) {
            req.put("ztoenv", "QcNoEnv" + taskInfo.getZtoenv());
        }
        req.put("requestType", taskInfo.getRequestType());
        File file = zipDebugFile(taskInfo.getTaskId());
        boolean success = false;
        List<QcDebugConfig.QcDebugServer> servers = getServerQueue();
        for (int i = 0; i < servers.size(); i++) {
            String key = DEBUG_TASK_KEY_PREFIX + servers.get(i).getSecret();
            if (redisService.listSize(key) < servers.get(i).getMaxTaskNum()) {
                redisService.addListOnRight(key, taskInfo.getTaskId());
                redisService.setAliveTime(key, DEBUG_KEY_EXPIRED, TimeUnit.MINUTES);
                String url = GenerateApiCaseUtil.completeUrl(servers.get(i).getHost(), qcDebugConfig.getExecuteUrl());
                log.info("尝试请求debug调试服务: url={}, req={}, file={}", url, req, file.getPath());
                if (null == url) {
                    continue;
                }
                apiDebugLogService.setApiDebugLog(taskInfo.getTaskId(),
                        String.format("尝试请求debug调试服务[%s]....", servers.get(i).getSecret()), isDocument);
                try (HttpResponse httpResponse = HttpUtil.createPost(url)
                        .timeout(2000)
                        .form(req)
                        .form("file", file)
                        .execute()) {
                    log.info("debug调试服务返回：{}", httpResponse.body());
                    Result response = JSON.parseObject(httpResponse.body(), Result.class);
                    if (StatusCode.OK.getCode() == response.getStatus()) {
                        success = true;
                        String nextServer;
                        if (i < servers.size() - 1) {
                            nextServer = servers.get(i + 1).getSecret();
                        } else {
                            nextServer = servers.get(0).getSecret();
                        }
                        redisService.setKey(NEXT_DEBUG_SERVER_KEY, nextServer, NEXT_DEBUG_SERVER_KEY_EXPIRED, TimeUnit.HOURS);
                        break;
                    }
                } catch (Exception e) {
                    log.error("请求debug调试服务异常！", e);
                }
                this.abort(taskInfo.getTaskId());
            }
        }
        if (!success) {
            throw new ServiceException("debug调试服务器繁忙，请稍后再试！");
        }
    }

    private String getDubboTag() {
        if (StringUtils.isNotBlank(System.getProperty("titans.dubbo.tag"))) {
            return System.getProperty("titans.dubbo.tag");
        }
        return "pro".equals(System.getProperty("env")) ? "prod" : "base";
    }

    private List<QcDebugConfig.QcDebugServer> getServerQueue() {
        List<QcDebugConfig.QcDebugServer> servers = Optional.ofNullable(qcDebugConfig.getServer())
                .map(list -> list.stream().filter(item -> item.getStatus() == 1).collect(Collectors.toList()))
                .orElse(null);
        if (CollectionUtils.isEmpty(servers)) {
            throw new ServiceException("debug服务未找到在线节点，请联系管理员！");
        }
        int index = 0;
        String secret = redisService.getKey(NEXT_DEBUG_SERVER_KEY);
        if (StringUtils.isNotEmpty(secret)) {
            for (int i = 0; i < servers.size(); i++) {
                if (secret.equals(servers.get(i).getSecret())) {
                    index = i;
                    break;
                }
            }
        }
        List<QcDebugConfig.QcDebugServer> queue = new ArrayList<>();
        for (int i = index; i < servers.size(); i++) {
            queue.add(servers.get(i));
        }
        for (int i = 0; i < index; i++) {
            queue.add(servers.get(i));
        }
        return queue;
    }

    private boolean isAutoEnv(String productCode) {
        try {
            List<FeatureVO> list = pipelineRpcService.listFeatureConfig(productCode);
            return Optional.ofNullable(list)
                    .flatMap(vos -> vos.stream().filter(vo -> "AUTO_ENV".equals(vo.getKey())).findAny())
                    .map(vo -> (Boolean) vo.getValue())
                    .orElse(false);
        } catch (Exception e) {
            log.error("PipelineRpcService#listFeatureConfig", e);
            return false;
        }
    }

    private File zipDebugFile(String taskId) {
        File src = new File("/data/jmeter/" + taskId);
        File zip = ZipUtil.zip(src.getPath(), src.getPath() + ".zip", true);
        Path path = FileUtil.move(zip.toPath(), src.toPath(), true);
        return path.toFile();
    }

    @Override
    public void abort(String taskId) {
        Executors.newSingleThreadExecutor().execute(() -> {
            log.info("终止debug任务 >>> {}", taskId);
            List<QcDebugConfig.QcDebugServer> servers = qcDebugConfig.getServer();
            if (CollectionUtils.isEmpty(servers)) {
                return;
            }
            JSONObject req = new JSONObject();
            req.put("taskId", taskId);
            req.put("dubboTag", getDubboTag());
            for (QcDebugConfig.QcDebugServer server : servers) {
                String key = DEBUG_TASK_KEY_PREFIX + server.getSecret();
                if (redisService.listRemove(key, taskId) > 0 && server.getStatus() > 0) {
                    String url = GenerateApiCaseUtil.completeUrl(server.getHost(), qcDebugConfig.getAbortUrl());
                    log.info("尝试请求debug终止服务: url={}, req={}", url, req);
                    try (HttpResponse httpResponse = HttpUtil.createPost(url)
                            .body(req.toJSONString())
                            .execute()) {
                        log.info("debug终止服务返回：{}", httpResponse.body());
                    } catch (Exception e) {
                        log.error("请求debug终止服务异常！", e);
                    }
                    break;
                }
            }
        });
    }

    @Override
    public void removeTaskId(String taskId) {
        List<QcDebugConfig.QcDebugServer> servers = qcDebugConfig.getServer();
        if (CollectionUtils.isEmpty(servers)) {
            return;
        }
        for (QcDebugConfig.QcDebugServer server : servers) {
            String key = DEBUG_TASK_KEY_PREFIX + server.getSecret();
            Long count = redisService.listRemove(key, taskId);
            if (count > 0) {
                log.info("删除redis缓存debug任务id: key={}, taskId={}", key, taskId);
                break;
            }
        }
    }

    @Override
    public boolean executeApi(DebugTaskInfo taskInfo) {
        Map<String, Object> req = new HashMap<>();
        req.put("taskId", taskInfo.getTaskId());
        req.put("ztoenv", taskInfo.getZtoenv());
        req.put("dubboTag", getDubboTag());
        req.put("sceneCode", taskInfo.getSceneCode());
        req.put("sceneName", taskInfo.getSceneName());
        req.put("productCode", taskInfo.getProductCode());
        if (!isAutoEnv(taskInfo.getProductCode())) {
            req.put("ztoenv", "QcNoEnv" + taskInfo.getZtoenv());
        }
        req.put("requestType", taskInfo.getRequestType());
        File file = zipDebugFile(taskInfo.getTaskId());
        boolean success = false;
        List<QcDebugConfig.QcDebugServer> servers = getServerQueue();
        for (int i = 0; i < servers.size(); i++) {
            String key = DEBUG_TASK_KEY_PREFIX + servers.get(i).getSecret();
            if (redisService.listSize(key) < servers.get(i).getMaxTaskNum()) {
                redisService.addListOnRight(key, taskInfo.getTaskId());
                redisService.setAliveTime(key, DEBUG_KEY_EXPIRED, TimeUnit.MINUTES);
                String url = GenerateApiCaseUtil.completeUrl(servers.get(i).getHost(), qcDebugConfig.getExecuteUrl());
                log.info("尝试请求debug调试服务: url={}, req={}, file={}", url, req, file.getPath());
                if (null == url) {
                    continue;
                }
                try (HttpResponse httpResponse = HttpUtil.createPost(url)
                        .timeout(2000)
                        .form(req)
                        .form("file", file)
                        .execute()) {
                    log.info("debug调试服务返回：{}", httpResponse.body());
                    Result response = JSON.parseObject(httpResponse.body(), Result.class);
                    if (StatusCode.OK.getCode() == response.getStatus()) {
                        success = true;
                        String nextServer;
                        if (i < servers.size() - 1) {
                            nextServer = servers.get(i + 1).getSecret();
                        } else {
                            nextServer = servers.get(0).getSecret();
                        }
                        redisService.setKey(NEXT_DEBUG_SERVER_KEY, nextServer, NEXT_DEBUG_SERVER_KEY_EXPIRED, TimeUnit.HOURS);
                        break;
                    }
                } catch (Exception e) {
                    log.error("请求debug调试服务异常！", e);
                }
                this.abort(taskInfo.getTaskId());
            }
        }
        return success;
    }
}
