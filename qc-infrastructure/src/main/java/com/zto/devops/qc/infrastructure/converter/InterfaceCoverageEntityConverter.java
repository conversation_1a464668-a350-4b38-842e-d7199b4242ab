package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.InterfaceCoverageEntityDO;
import com.zto.devops.qc.client.service.coverage.model.resp.InterfaceCoverageInfoResp;
import com.zto.devops.qc.infrastructure.dao.entity.InterfaceCoverageEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface InterfaceCoverageEntityConverter {

    @Mapping(target = "interfaceName", source = "interfaceAlias")
    @Mapping(target = "status", source = "isCovered")
    @Mapping(target = "interfaceType", source = "interfaceMethodType")
    InterfaceCoverageInfoResp converter(InterfaceCoverageEntity entity);

    List<InterfaceCoverageInfoResp> converter(List<InterfaceCoverageEntity> entityList);

    List<InterfaceCoverageEntity> convertToEntity(List<InterfaceCoverageEntityDO> entityDOList);

    List<InterfaceCoverageEntityDO> convertToEntityDO(List<InterfaceCoverageEntity> entityList);

}
