package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageAppInfoVO;
import com.zto.devops.qc.infrastructure.dao.entity.CoverageRecordBasicEntity;
import com.zto.devops.qc.infrastructure.dao.entity.CoverageRecordGenerateEntity;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/9/16 17:46
 */
public interface CoverageRecordBasicMapper extends Mapper<CoverageRecordBasicEntity> {

    List<CoverageRecordBasicEntity> getInitialCoverageRecords(CoverageRecordGenerateParameter parameter);

    List<CoverageRecordBasicEntity> getExistCoverageRecords(@Param("versionCode") String versionCode,
                                                            @Param("branchNames") List<String> branchNames,
                                                            @Param("appType") String appType);

    CoverageRecordGenerateEntity selectCoverageRecords(CoverageRecordBasicEntity entity);

    void batchInsert(List<CoverageRecordBasicEntity> list);

    void batchUpdate(@Param("versionCode") String versionCode,
                     @Param("recordType") String recordType,
                     @Param("status") String status, @Param("list") List<String> appIdList,
                     @Param("recordErrorMsg") String recordErrorMsg);

    void updateCoverageRecordById(@Param("id") Long id, @Param("status") String status,
                                  @Param("recordErrorMsg") String recordErrorMsg,
                                  @Param("recordUrl") String recordUrl,
                                  @Param("commitId") String commitId,
                                  @Param("basicCommitId") String basicCommitId,
                                  @Param("basicBranchName") String basicBranchName,
                                  @Param("recordRate") BigDecimal recordRate,
                                  @Param("remark") String remark,
                                  @Param("codeCoverNum") Integer codeCoverNum,
                                  @Param("codeSum") Integer codeSum,
                                  @Param("envName") String envName,
                                  @Param("gitCompareUrl") String gitCompareUrl,
                                  @Param("bucketName") String bucketName,
                                  @Param("fileName") String fileName

    );

    int delCoverageRecord(CoverageRecordBasicEntity entity);

    String getRemark(CoverageRecordBasicEntity entity);

    /**
     * 根据versionCode，查询待筛选不达标记录集合
     *
     * @param versionCode 版本code
     * @return {@link CoverageRecordBasicEntity}
     */
    List<CoverageAppInfoVO> selectListByVersionCode(@Param("versionCode") String versionCode, @Param("list") List<String> list);

    List<CoverageRecordBasicEntity> getCoverageRunningList(String nowTime);

    String getGitCompareUrl(@Param("versionCode") String versionCode, @Param("appId") String appId, @Param("branchName") String branchName);

    List<CoverageAppInfoVO> selectListByVersionCodeList(@Param("versionCodeList") List<String> versionCodeList, @Param("list") List<String> list);

    List<CoverageRecordBasicEntity> getCoverageRecordListByLimit();


    /**
     * 根据应用id，查询已存在覆盖率记录
     *
     * @param versionCode 版本code
     * @param branchNames 分支名称集合
     * @param appType     应用类型
     * @param appIdList   应用id集合
     * @return
     */
    List<CoverageRecordBasicEntity> getExistCoverageRecordsByAppIdList(@Param("versionCode") String versionCode,
                                                                       @Param("branchNames") List<String> branchNames,
                                                                       @Param("appType") String appType,
                                                                       @Param("appIdList") List<String> appIdList);

    /**
     * 根据taskId，查询自动生成的覆盖率结果，【Fail、INITIAL】
     *
     * @param taskId
     * @return
     */
    List<CoverageRecordBasicEntity> selectAutoGenerateResultByTaskId(@Param("taskId") String taskId,
                                                                     @Param("statusList") List<RecordStatusEnum> statusList);

    List<CoverageRecordBasicEntity> selectAutoGenerateResultByAppIdList(@Param("versionCode") String versionCode,
                                                                        @Param("appIdList") List<String> appIdList);
}
