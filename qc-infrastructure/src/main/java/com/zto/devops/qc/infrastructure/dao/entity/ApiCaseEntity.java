package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.client.simple.HasTransactor;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseSourceTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.ApiCaseEnableTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.ApiCaseSourceTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.TestcasePriorityTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseStatusHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Table(name = "tm_api_case")
public class ApiCaseEntity {

    @Id
    private String caseCode;

    private String caseName;

    private String apiCode;

    private String nodeCode;

    @ColumnType(typeHandler = ApiCaseSourceTypeHandler.class)
    private ApiCaseSourceTypeEnum sourceType;

    private String parentCode;

    @ColumnType(typeHandler = TestcasePriorityTypeHandler.class)
    private TestcasePriorityEnum priority;

    private String reqData;

    private String asserts;

    private String userVariableCode;

    private String productCode;

    @ColumnType(typeHandler = ApiCaseEnableTypeHandler.class)
    private ApiCaseEnableEnum enable;

    private Long creatorId;

    private String creator;

    private Date gmtCreate;

    private Long modifierId;

    private String modifier;

    private Date gmtModified;

    @ColumnType(typeHandler = TmTestPlanCaseStatusHandler.class)
    private TestPlanCaseStatusEnum latestExecuteResult;

    private String latestTaskId;

    private String sceneCode;

    public void preCreate(HasTransactor hasTransactor) {
        User user = hasTransactor.getTransactor();
        if (null != user) {
            this.setCreatorId(user.getUserId());
            this.setCreator(user.getUserName());
            this.setGmtCreate(new Date());
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }

    public void preUpdate(HasTransactor hasTransactor) {
        User user = hasTransactor.getTransactor();
        if (null != user) {
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }
}
