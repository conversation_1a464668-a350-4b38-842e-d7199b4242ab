package com.zto.devops.qc.infrastructure.gateway.report;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.qc.client.model.report.entity.SecurityTestResultVO;
import com.zto.devops.qc.domain.gateway.report.SecurityTestReportService;
import com.zto.devops.qc.infrastructure.config.SecurityProfile;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SecurityTestReportServiceImpl implements SecurityTestReportService {

    @Autowired
    private SecurityProfile securityProfile;

    @Override
    public SecurityTestResultVO querySecurityTestResult(String securityPlanCode) {
        //请求地址
        String url = securityProfile.getSecurityTestResultQueryUrl();
        //请求参数
        String result = "";
        try (HttpResponse httpResponse = HttpUtil.createGet(url)
                .header("token", securityProfile.getToken())
                .form("code", securityPlanCode)
                .execute()) {
            result = httpResponse.body();
            return JsonUtil.parse(result, SecurityTestResultVO.class);
        } catch (Exception e) {
            log.error("安全-查看安全测试结果-接口报错:", e);
            throw new ServiceException("安全-查看安全测试结果-接口报错!");
        } finally {
            log.info("安全-查看安全测试结果。请求地址:{};请求参数：{} 返回结果 :{}", url, securityPlanCode, result);
        }
    }
}
