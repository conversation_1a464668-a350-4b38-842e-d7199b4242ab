package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
@Table(name = "tm_automatic_source_log_testcase")
@Data
public class AutomaticSourceLogTestcaseEntity extends BaseEntity {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * code
     */
    @Id
    private String code;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 上级code
     */
    private String parentCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 属性
     */
    private String attribute;

    /**
     * 用例类型
     */
    private String type;

    /**
     * 等级
     */
    private String priority;

    /**
     * 状态
     */
    private String status;

    /**
     * 前置条件
     */
    private String precondition;

    /**
     * 责任人编码
     */
    private Long dutyUserId;

    /**
     * 责任人
     */
    private String dutyUser;

    /**
     * 备注
     */
    private String comment;

    /**
     * 解析log的code
     */
    private String automaticSourceLogCode;

    /**
     * 节点类型
     */
    private String nodeType;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 层级
     */
    private Integer layer;

    /**
     * 路径
     */
    private String path;

    /**
     * 是否删除
     */
    private Boolean enable;

    /**
     * 创建人编码
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 所有父节点类型
     */
    private String nodeTypePath;

    private String flag;

    private String testcaseCode;

    /**
     * dubbo服务接口
     */
    private String interfaceName;
}