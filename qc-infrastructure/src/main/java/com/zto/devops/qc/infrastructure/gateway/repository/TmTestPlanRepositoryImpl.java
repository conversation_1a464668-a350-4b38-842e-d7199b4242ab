package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.enums.testPlan.TestPlanTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanRangeTypeEnum;
import com.zto.devops.qc.client.model.dto.TestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestPlanRangeEntityDO;
import com.zto.devops.qc.client.model.rpc.project.VersionDeleteEvent;
import com.zto.devops.qc.client.model.rpc.project.VersionEditedEvent;
import com.zto.devops.qc.client.model.testPlan.query.TestPlanListQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestFunctionPointVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.event.ChangeCaseExecuteResultEvent;
import com.zto.devops.qc.client.model.testmanager.plan.event.PlanCaseResultCommentChangedEvent;
import com.zto.devops.qc.client.model.testmanager.plan.query.*;
import com.zto.devops.qc.domain.gateway.repository.ITmTestPlanRepository;
import com.zto.devops.qc.infrastructure.converter.TmTestPlanConvertor;
import com.zto.devops.qc.infrastructure.converter.TmTestPlanNullIgnoreConvertor;
import com.zto.devops.qc.infrastructure.dao.entity.*;
import com.zto.devops.qc.infrastructure.dao.mapper.*;
import com.zto.devops.qc.infrastructure.util.EmptyCheckerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TmTestPlanRepositoryImpl implements ITmTestPlanRepository {

    @Autowired
    private TmTestPlanMapper tmTestPlanMapper;

    @Autowired
    private TmTestPlanRangeMapper tmTestPlanRangeMapper;

    @Autowired
    private TestFunctionPointMapper testFunctionPointMapper;

    @Autowired
    private TestPlanMapper testPlanMapper;

    @Autowired
    private TmTestPlanConvertor convertor;

    @Autowired
    private TestcaseMapper testcaseMapper;

    @Autowired
    private TmTestPlanCaseMapper tmTestPlanCaseMapper;

    private TmTestPlanNullIgnoreConvertor tmTestPlanNullIgnoreConvertor = TmTestPlanNullIgnoreConvertor.INSTANCE;

    @Override
    public boolean checkSendEmailPlan(String planCode) {
        //测试计划已终止状态不支持更新
        TmTestPlanEntity planEntity = tmTestPlanMapper.selectByCode(planCode);
        if (!Objects.isNull(planEntity) && planEntity.getStatus().equals(TestPlanNewStatusEnum.TERMINATED)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public void addTestPlan(TmTestPlanVO vo, BaseEvent event) {
        TmTestPlanEntity entity = convertor.convert(vo);
        entity.preCreate(event);
        insertTestFunctionPoint(vo.getCode(), vo.getPointList(), event);
        insertTmTestPlanRangeEntity(vo, event);
        tmTestPlanMapper.insertSelective(entity);
    }

    @Override
    public void editTestPlan(VersionEditedEvent event) {
        TmTestPlanEntity entity = new TmTestPlanEntity();
        Example example = new Example(TmTestPlanEntity.class);
        entity.setAccessDate(event.getPresentationDate());
        entity.setPermitDate(event.getApprovalExitDate());
        example.createCriteria().andEqualTo("versionCode", event.getCode());
        entity.preUpdate(event);
        tmTestPlanMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void deleteTestPlanByVersionCode(VersionDeleteEvent event) {
        if(StringUtils.isNotBlank(event.getCode())) {
            TmTestPlanEntity entity = new TmTestPlanEntity();
            entity.setEnable(false);
            entity.preUpdate(event);
            Example example = new Example(TmTestPlanEntity.class);
            example.createCriteria().andEqualTo("versionCode", event.getCode());
            tmTestPlanMapper.updateByExampleSelective(entity, example);
        }
    }

    @Override
    public List<TmTestPlanEntityDO> selectTmTestPlanEntityDOByPlanCodeList(Set<String> codeList) {
        Example planExample = new Example(TmTestPlanEntity.class);
        planExample.createCriteria().andIn("code", codeList)
                .andEqualTo("enable", Boolean.TRUE);
        return convertor.covertList(tmTestPlanMapper.selectByExample(planExample));
    }

    @Override
    public TmTestPlanEntityDO getTestPlanByCode(String code) {
        TmTestPlanEntity testPlanEntity = tmTestPlanMapper.selectByPrimaryKey(code);
        return convertor.covert(testPlanEntity);
    }

    @Override
    public TmTestPlanEntityDO selectTmTestPlanByCode(String code) {
        return convertor.covert(tmTestPlanMapper.selectByCode(code));
    }

    @Override
    public List<TestPlanEntityDO> selectTestPlanByVersionCodeAndTypeList(String versionCode, List<TestPlanTypeEnum> testPlanTypeEnumList) {
        Example example1 = new Example(TestPlanEntity.class);
        example1.createCriteria().andEqualTo("versionCode", versionCode)
                .andEqualTo("enable", true)
                .andEqualTo("status", TestPlanStatusEnum.NORMAL)
                .andIn("type", testPlanTypeEnumList);
        return convertor.oldConvertList(testPlanMapper.selectByExample(example1));
    }

    @Override
    public List<TestPlanEntityDO> selectTestPlanByVersionCodeAndType(String versionCode) {
        Example example0 = new Example(TestPlanEntity.class);
        example0.createCriteria()
                .andEqualTo("versionCode", versionCode)
                .andEqualTo("enable", true)
                .andEqualTo("status", TestPlanStatusEnum.NORMAL)
                .andEqualTo("type", TestPlanTypeEnum.TEST_PLAN);
        return convertor.oldConvertList(testPlanMapper.selectByExample(example0));
    }

    private void insertTestFunctionPoint(String planCode, List<TestFunctionPointVO> pointList, BaseEvent event) {
        Example example = new Example(TestFunctionPointEntity.class);
        example.createCriteria().andEqualTo("businessCode", planCode);
        testFunctionPointMapper.deleteByExample(example);
        if (CollectionUtil.isEmpty(pointList)) {
            return;
        }
        pointList.forEach(t -> {
            TestFunctionPointEntity entity = convertor.convert(t);
            if (StringUtil.isEmpty(entity.getCode())) {
                entity.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
            }
            entity.preCreate(event);
            entity.setBusinessCode(planCode);
            testFunctionPointMapper.insertSelective(entity);
        });
    }

    private void insertTmTestPlanRangeEntity(TmTestPlanVO vo, BaseEvent event) {
        Example exampleRange = new Example(TmTestPlanRangeEntity.class);
        exampleRange.createCriteria().andEqualTo("planCode", vo.getCode())
                .andEqualTo("testRange", TestPlanRangeTypeEnum.SAFETY_SCANNING);
        TmTestPlanRangeEntity rangeEntity = tmTestPlanRangeMapper.selectOneByExample(exampleRange);

        Example example = new Example(TmTestPlanRangeEntity.class);
        example.createCriteria().andEqualTo("planCode", vo.getCode());
        tmTestPlanRangeMapper.deleteByExample(example);
        if (vo.getMobileSpecialTest() != null) {
            TmTestPlanRangeEntity entity = new TmTestPlanRangeEntity();
            entity.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
            entity.setPlanCode(vo.getCode());
            entity.setTestRange(TestPlanRangeTypeEnum.MOBILE_TEST);
            entity.setTestRangeStatus(vo.getMobileSpecialTest());
            entity.preCreate(event);
            tmTestPlanRangeMapper.insertSelective(entity);
        }
        if (vo.getSecurityScan() != null) {
            TmTestPlanRangeEntity entity = new TmTestPlanRangeEntity();
            entity.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
            entity.setPlanCode(vo.getCode());
            entity.setTestRange(TestPlanRangeTypeEnum.SAFETY_SCANNING);
            entity.setTestRangeStatus(vo.getSecurityScan());
            entity.setStatus(rangeEntity == null ? SecurityTestResult.NOOPERATE.name() : rangeEntity.getStatus());
            entity.setExecutorId(rangeEntity == null ? null : rangeEntity.getExecutorId());
            entity.setExecutor(rangeEntity == null ? "" : rangeEntity.getExecutor());
            entity.preCreate(event);
            tmTestPlanRangeMapper.insertSelective(entity);
        }
        if (vo.getStaticAnalysis() != null) {
            TmTestPlanRangeEntity entity = new TmTestPlanRangeEntity();
            entity.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
            entity.setPlanCode(vo.getCode());
            entity.setTestRange(TestPlanRangeTypeEnum.STATIC_ANALYSIS);
            entity.setTestRangeStatus(vo.getStaticAnalysis());
            if (vo.getStaticAnalysis()) {
                entity.setExecutorId(vo.getStaticAnalysisDirectorId());
                entity.setExecutor(vo.getStaticAnalysisDirectorName());
                entity.setTestTime(vo.getStaticAnalysisTime());
            }
            entity.preCreate(event);
            tmTestPlanRangeMapper.insertSelective(entity);
        }
        if (vo.getPerformanceTest() != null) {
            TmTestPlanRangeEntity entity = new TmTestPlanRangeEntity();
            entity.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
            entity.setPlanCode(vo.getCode());
            entity.setTestRange(TestPlanRangeTypeEnum.PERFORMANCE_TEST);
            entity.setTestRangeStatus(vo.getPerformanceTest());
            if (vo.getPerformanceTest()) {
                entity.setExecutorId(vo.getPerformanceTestDirectorId());
                entity.setExecutor(vo.getPerformanceTestDirectorName());
                entity.setTestTime(vo.getPerformanceTestTime());
            }
            entity.preCreate(event);
            tmTestPlanRangeMapper.insertSelective(entity);
        }
        if (vo.getExploratoryTest() != null) {
            TmTestPlanRangeEntity entity = new TmTestPlanRangeEntity();
            entity.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
            entity.setPlanCode(vo.getCode());
            entity.setTestRange(TestPlanRangeTypeEnum.EXPLORATORY_TEST);
            entity.setTestRangeStatus(vo.getExploratoryTest());
            if (vo.getExploratoryTest()) {
                entity.setExecutorId(vo.getExploratoryTestDirectorId());
                entity.setExecutor(vo.getExploratoryTestDirectorName());
                entity.setTestTime(vo.getExploratoryTestTime());
            }
            entity.preCreate(event);
            tmTestPlanRangeMapper.insertSelective(entity);
        }
        if (vo.getPermissionsTest() != null) {
            TmTestPlanRangeEntity entity = new TmTestPlanRangeEntity();
            entity.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
            entity.setPlanCode(vo.getCode());
            entity.setTestRange(TestPlanRangeTypeEnum.PERMISSIONS_TEST);
            entity.setTestTime(vo.getLastTestDate());
            entity.setTestRangeStatus(vo.getPermissionsTest());
            entity.setPriority(vo.getPriority());
            entity.setPermissionsTestInformation(vo.getPermissionsTestInformation());
            entity.setTestInformation(vo.getTestInformation());
            entity.preCreate(event);
            tmTestPlanRangeMapper.insertSelective(entity);
        }
    }

    @Override
    public TmTestPlanEntityDO getSafePlanCode(String relationPlanCode) {
        Example example = new Example(TmTestPlanEntity.class);
        example.createCriteria().andEqualTo("relationPlanCode", relationPlanCode)
                .andEqualTo("type", TestPlanNewTypeEnum.SAFETY_TEST)
                .andEqualTo("enable", true);
        return convertor.covert(tmTestPlanMapper.selectOneByExample(example));
    }

    @Override
    public List<TmTestPlanVO> pageTestPlanList(PlanListQuery query) {
        Example example = new Example(TmTestPlanEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", true);

        if (StringUtil.isNotBlank(query.getProductCode())) {
            criteria.andEqualTo("productCode", query.getProductCode());
        }
        if (StringUtil.isNotBlank(query.getVersionCode())) {
            criteria.andEqualTo("versionCode", query.getVersionCode());
        }
        if (StringUtil.isNotBlank(query.getPlanName())) {
            criteria.andLike("planName", "%" + query.getPlanName() + "%");
        }
        if (CollectionUtil.isNotEmpty(query.getTestStrategyList())) {
            criteria.andIn("testStrategy", query.getTestStrategyList());
        }
        if (CollectionUtil.isNotEmpty(query.getStatus())) {
            criteria.andIn("status", query.getStatus());
        }
        if (CollectionUtil.isNotEmpty(query.getType())) {
            criteria.andIn("type", query.getType());
        }
        if (CollectionUtil.isNotEmpty(query.getProductCodes())) {
            criteria.andIn("productCode", query.getProductCodes());
        }
        if (CollectionUtil.isNotEmpty(query.getPlanDirectorId())) {
            criteria.andIn("planDirectorId", query.getPlanDirectorId());
        }
        if (CollectionUtil.isNotEmpty(query.getModifierId())) {
            criteria.andIn("modifierId", query.getModifierId());
        }
        if (Objects.nonNull(query.getAccessTimeStart()) && Objects.nonNull(query.getAccessTimeEnd())) {
            criteria.andBetween("accessDate", query.getAccessTimeStart(), query.getAccessTimeEnd());
        }
        if (Objects.nonNull(query.getPermitTimeStart()) && Objects.nonNull(query.getPermitTimeEnd())) {
            criteria.andBetween("permitDate", query.getPermitTimeStart(), query.getPermitTimeEnd());
        }
        if (Objects.nonNull(query.getCreateTimeStart()) && Objects.nonNull(query.getCreateTimeEnd())) {
            criteria.andBetween("gmtCreate", query.getCreateTimeStart(), query.getCreateTimeEnd());
        }
        if (Objects.nonNull(query.getUpdateTimeStart()) && Objects.nonNull(query.getUpdateTimeEnd())) {
            criteria.andBetween("gmtModified", query.getUpdateTimeStart(), query.getUpdateTimeEnd());
        }
        example.orderBy("id").desc();
        List<TmTestPlanEntity> entityList = tmTestPlanMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : convertor.converter2VO(entityList);
    }

    @Override
    public List<TmTestPlanEntityDO> selectByProductCodeAndStatusList(ListPlanPhaseQuery query, List<TestPlanNewStatusEnum> statusList) {
        Example planExample = new Example(TmTestPlanEntity.class);
        Example.Criteria criteria = planExample.createCriteria()
                .andEqualTo("productCode", query.getProductCode())
                .andNotIn("status", statusList)
                .andEqualTo("enable", Boolean.TRUE);
        if (StringUtil.isNotBlank(query.getVersionCode())) {
            criteria.andEqualTo("versionCode", query.getVersionCode());
        }
        if (StringUtil.isNotBlank(query.getPlanName())) {
            criteria.andLike("planName", "%" + query.getPlanName() + "%");
        }
        planExample.setOrderByClause("gmt_create DESC");
        List<TmTestPlanEntity> planList = tmTestPlanMapper.selectByExample(planExample);
        return CollectionUtil.isEmpty(planList) ? new ArrayList<>() : convertor.covertList(planList);
    }

    @Override
    public TmTestPlanEntityDO selectByCD(String productCode, String versionCode, String testPlanType) {
        TmTestPlanEntity entity = tmTestPlanMapper.selectByCD(productCode, versionCode, testPlanType);
        return convertor.covert(entity);
    }

    @Override
    public List<TmTestPlanEntityDO> selectPlanOrSafePlan(TmTestPlanEntityDO entityDO) {
        Example example = new Example(TmTestPlanEntity.class);
        Example.Criteria criteria = example.createCriteria();
        Example.Criteria safeCriteria = example.createCriteria();
        if (StringUtil.isNotEmpty(entityDO.getProductCode())) {
            criteria.andEqualTo("productCode", entityDO.getProductCode());
            safeCriteria.andEqualTo("productCode", entityDO.getProductCode());
        }
        if (StringUtil.isNotEmpty(entityDO.getPlanName())) {
            criteria.andLike("planName", "%" + entityDO.getPlanName() + "%");
            safeCriteria.andLike("planName", "%" + entityDO.getPlanName() + "%");
        }
        criteria.andEqualTo("enable", Boolean.TRUE).andEqualTo("type",
                TestPlanNewTypeEnum.TEST_PLAN);
        safeCriteria.andEqualTo("enable", Boolean.TRUE).andEqualTo("type",
                TestPlanNewTypeEnum.SAFETY_TEST);
        example.or(safeCriteria);
        List<TmTestPlanEntity> entityList = tmTestPlanMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : convertor.covertList(entityList);
    }

    @Override
    public List<TmTestPlanEntityDO> selectVersionPlanList(VersionPlanQuery query) {
        List<TmTestPlanEntity> entityList = tmTestPlanMapper.selectVersionPlanList(query);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : convertor.covertList(entityList);
    }

    @Override
    public TmTestPlanEntityDO getSafetyPlanByVersion(String versionCode) {
        Example example = new Example(TmTestPlanEntity.class);
        example.createCriteria()
                .andEqualTo("type", TestPlanNewTypeEnum.SAFETY_TEST)
                .andEqualTo("versionCode", versionCode)
                .andEqualTo("enable", Boolean.TRUE);
        example.orderBy("gmtModified").desc();
        List<TmTestPlanEntity> entityList = tmTestPlanMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? null : convertor.covert(entityList.get(0));
    }

    @Override
    public TmTestPlanEntityDO getTestPlanByVersion(String versionCode) {
        Example example = new Example(TmTestPlanEntity.class);
        example.createCriteria()
                .andEqualTo("type", TestPlanNewTypeEnum.TEST_PLAN)
                .andEqualTo("versionCode", versionCode)
                .andEqualTo("enable", Boolean.TRUE);
        example.orderBy("gmtModified").desc();
        List<TmTestPlanEntity> entityList = tmTestPlanMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? null : convertor.covert(entityList.get(0));
    }

    @Override
    public TmTestPlanRangeEntityDO getSafePlanRangeByPlanCode(String planCode) {
        Example example = new Example(TmTestPlanRangeEntity.class);
        example.createCriteria().andEqualTo("planCode", planCode)
                .andEqualTo("testRange", TestPlanRangeTypeEnum.SAFETY_SCANNING);
        TmTestPlanRangeEntity entity = tmTestPlanRangeMapper.selectOneByExample(example);
        return convertor.covert(entity);
    }

    @Override
    public void updateStageStatusByPrimaryKey(TmTestPlanEntityDO updateEntityDO, String planCode) {
        Example example = new Example(TmTestPlanEntity.class);
        example.createCriteria().andEqualTo("code", planCode);
        TmTestPlanEntity toUpdateEntity = convertor.convert2Entity(updateEntityDO);
        tmTestPlanMapper.updateByExampleSelective(toUpdateEntity, example);
    }

    @Override
    public void updateHistoryStatusByPrimaryKey(TmTestPlanEntityDO entityDO, String planCode) {
        TmTestPlanEntity planEntity = convertor.convert2Entity(entityDO);
        Example example = new Example(TmTestPlanEntity.class);
        example.createCriteria().andEqualTo("code", planCode);
        tmTestPlanMapper.updateByExampleSelective(planEntity, example);
    }

    @Override
    public TmTestPlanEntityDO selectEnableByPrimaryKey(String planCode) {
        Example example = new Example(TmTestPlanEntity.class);
        example.selectProperties("code", "stageStatus", "status");
        example.createCriteria().andEqualTo("code", planCode).andEqualTo("enable", 1);
        TmTestPlanEntity entity = tmTestPlanMapper.selectOneByExample(example);
        return convertor.covert(entity);
    }

    @Override
    public Boolean checkTestPlanName(FindPlanByProductQuery query) {
        Example planExample = new Example(TmTestPlanEntity.class);
        planExample.createCriteria()
                .andEqualTo("productCode", query.getProductCode())
                .andEqualTo("planName", query.getPlanName())
                .andNotEqualTo("code", query.getPlanCode())
                .andEqualTo("enable", Boolean.TRUE);
        List<TmTestPlanEntity> tmTestPlanEntities = tmTestPlanMapper.selectByExample(planExample);
        return CollectionUtil.isNotEmpty(tmTestPlanEntities);
    }

    @Override
    public List<TmTestPlanEntityDO> selectTestPlanEntityListByQuery(FindPlanByProductQuery query) {
        Example planExample = new Example(TmTestPlanEntity.class);
        planExample.createCriteria()
                .andEqualTo("productCode", query.getProductCode())
                .andEqualTo("planName", query.getPlanName())
                .andNotEqualTo("code", query.getPlanCode())
                .andEqualTo("enable", Boolean.TRUE);
        List<TmTestPlanEntity> tmTestPlanEntities = tmTestPlanMapper.selectByExample(planExample);
        return convertor.covertList(tmTestPlanEntities);
    }

    @Override
    public List<TmTestPlanEntityDO> selectTestPlanEntityListByQuery(AssociatedTestPlanListQuery query, List<String> codeList) {
        Example planExample = new Example(TmTestPlanEntity.class);
        Example.Criteria criteria = planExample.createCriteria();
        criteria.andIn("code", codeList);
        criteria.andEqualTo("type", TestPlanNewTypeEnum.TEST_PLAN.name());
        criteria.andEqualTo("enable", Boolean.TRUE);
        if (StringUtil.isNotEmpty(query.getPlanName())) {
            criteria.andLike("planName", "%" + query.getPlanName() + "%");
        }
        if (StringUtil.isNotEmpty(query.getProductCode())) {
            criteria.andEqualTo("productCode", query.getProductCode());
        }
        List<TmTestPlanEntity> tmTestPlanEntities = tmTestPlanMapper.selectByExample(planExample);
        return convertor.covertList(tmTestPlanEntities);
    }

    @Override
    public void changeCaseExecuteResult(ChangeCaseExecuteResultEvent event) {
        TmTestPlanEntity tmTestPlanEntity = tmTestPlanMapper.selectByPrimaryKey(event.getPlanCode());
        if (EmptyCheckerUtil.isEmpty(tmTestPlanEntity) && tmTestPlanEntity.getEnable().equals(false)) {
            return;
        }
        if (tmTestPlanEntity.getStatus().equals(TestPlanNewStatusEnum.TERMINATED) && tmTestPlanEntity.getStatus().equals(TestPlanNewStatusEnum.COMPLETED)) {
            return;
        }
        Example caseExample = new Example(TestcaseEntity.class);
        caseExample.createCriteria()
                .andIn("code", duplicateRemoval(event.getCaseIds()))
                .andEqualTo("status", TestcaseStatusEnum.NORMAL)
                .andEqualTo("enable", Boolean.TRUE);
        List<String> collect = testcaseMapper.selectByExample(caseExample).stream().map(TestcaseEntity::getCode).collect(Collectors.toList());
        if (EmptyCheckerUtil.isEmpty(collect)) {
            return;
        }
        TmTestPlanCaseEntity entity = new TmTestPlanCaseEntity();
        entity.setStatus(event.getExecuteStatus());
        if (event.getTransactor() != null) {
            entity.setExecutorId(event.getTransactor().getUserId());
            entity.setExecutor(event.getTransactor().getUserName());
        }
        entity.preUpdate(event);
        Example example = new Example(TmTestPlanCaseEntity.class);
        example.createCriteria().andEqualTo("testStage", event.getTestStage())
                .andEqualTo("planCode", event.getPlanCode())
                .andEqualTo("caseType", "MANUAL")
                .andIn("caseCode", collect)
                .andEqualTo("enable", Boolean.TRUE);
        tmTestPlanCaseMapper.updateByExampleSelective(entity, example);
        example.selectProperties("operateCaseCode");
        List<TmTestPlanCaseEntity> entities = tmTestPlanCaseMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(entities)) {
            event.setOperateCaseCodeList(entities.stream().map(TmTestPlanCaseEntity::getOperateCaseCode).collect(Collectors.toList()));
        }
    }

    @Override
    public void changePlanCaseResultComment(PlanCaseResultCommentChangedEvent event) {
        Example example = new Example(TmTestPlanCaseEntity.class);
        example.createCriteria()
                .andEqualTo("planCode", event.getPlanCode())
                .andEqualTo("testStage", event.getTestStage())
                .andEqualTo("caseCode", event.getCaseCode());
        TmTestPlanCaseEntity entity = new TmTestPlanCaseEntity();
        entity.setResultComment(event.getResultComment());
        tmTestPlanCaseMapper.updateByExampleSelective(entity, example);
    }

    private List<String> duplicateRemoval(List<String> caseCodeList) {
        return caseCodeList.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    public void editTestPlan(TmTestPlanVO vo, BaseEvent event) {
        TmTestPlanEntity entity = tmTestPlanMapper.selectByPrimaryKey(vo.getCode());
        if (entity == null) {
            throw new ServiceException("测试计划不存在");
        }
        tmTestPlanNullIgnoreConvertor.updateTmTestPlanEntity(vo, entity);
        entity.preUpdate(event);
        tmTestPlanMapper.updateByPrimaryKeySelective(entity);
        insertTestFunctionPoint(vo.getCode(), vo.getPointList(), event);
        insertTmTestPlanRangeEntity(vo, event);
    }

    @Override
    public List<TestPlanEntityDO> selectTestPlanList(TestPlanListQuery query) {
        List<TestPlanEntity> entityList = testPlanMapper.selectTestPlanList(query);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : convertor.oldConvertList(entityList);
    }

    @Override
    public List<TmTestPlanVO> selectTmTestPlanList(PlanListQuery query) {
        return tmTestPlanMapper.selectTestPlanList(query);
    }

    @Override
    public void updateCheckFlag(String planCode, Set<String> versionCodeSet, boolean checkFlag, User transactor) {
        Example example = new Example(TmTestPlanEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("type", TestPlanNewTypeEnum.TEST_PLAN);
        if (StringUtil.isNotBlank(planCode)) {
            criteria.andEqualTo("code", planCode);
        } else if (CollectionUtil.isNotEmpty(versionCodeSet)) {
            criteria.andIn("versionCode", versionCodeSet);
        }

        TmTestPlanEntity entity = new TmTestPlanEntity();
        entity.setCheckFlag(checkFlag);
        entity.preUpdate(transactor);
        tmTestPlanMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public Integer countNotCheckListByVersionCodes(List<String> versionCodeList) {
        Example example = new Example(TmTestPlanEntity.class);
        example.createCriteria()
                .andEqualTo("type", TestPlanNewTypeEnum.TEST_PLAN)
                .andIn("versionCode", versionCodeList)
                .andEqualTo("checkFlag", Boolean.FALSE);
        return tmTestPlanMapper.selectCountByExample(example);
    }

    @Override
    public List<String> filterCompletedPlanCode(List<String> codeList) {
        Example example = new Example(TmTestPlanEntity.class);
        example.createCriteria().andIn("code", codeList)
                .andEqualTo("status", TestPlanNewStatusEnum.COMPLETED);
        example.selectProperties("code");
        List<TmTestPlanEntity> list = tmTestPlanMapper.selectByExample(example);
        return Optional.ofNullable(list).orElse(Collections.emptyList())
                .stream().map(TmTestPlanEntity::getCode).collect(Collectors.toList());
    }

    @Override
    public void updateVersionInfo(TmTestPlanEntityDO planEntity, String versionName, User transactor) {
        if (StringUtil.isBlank(versionName) || null == transactor || null == planEntity) {
            return;
        }
        Example example = new Example(TmTestPlanEntity.class);
        example.createCriteria().andEqualTo("code", planEntity.getCode());

        TmTestPlanEntity toUpdate = new TmTestPlanEntity();
        toUpdate.setPlanName(String.format("%s%s", versionName, planEntity.getType().getValue()));
        toUpdate.setVersionName(versionName);
        toUpdate.setModifier(transactor.getUserName());
        toUpdate.setModifierId(transactor.getUserId());
        toUpdate.setGmtModified(new Date());
        tmTestPlanMapper.updateByExampleSelective(toUpdate, example);
    }

    @Override
    public List<TmTestPlanEntityDO> listByVersionCodesAndTypes(String versionCode, List<TestPlanTypeEnum> testPlanTypeEnumList) {
        if (StringUtil.isBlank(versionCode) || CollectionUtils.isEmpty(testPlanTypeEnumList)) {
            return Collections.emptyList();
        }
        Example example = new Example(TmTestPlanEntity.class);
        example.createCriteria().andEqualTo("versionCode", versionCode)
                .andEqualTo("enable", true)
                .andIn("type", testPlanTypeEnumList);
        return convertor.covertList(tmTestPlanMapper.selectByExample(example));
    }
}
