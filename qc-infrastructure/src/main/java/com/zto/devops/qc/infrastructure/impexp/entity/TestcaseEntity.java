package com.zto.devops.qc.infrastructure.impexp.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.zto.devops.framework.infrastructure.dao.entity.ExpEntity;
import lombok.Data;

import java.io.Serializable;

@Data
public class TestcaseEntity extends ExpEntity implements Serializable {

    @ColumnWidth(50)
    @ExcelProperty("名称")
    private String name;

    @ColumnWidth(25)
    @ExcelProperty("编号")
    private String code;

    @ColumnWidth(50)
    @ExcelProperty("分组")
    private String fullName;

//    @GatewayModelProperty(description = "属性")
//    private TestcaseAttributeEnum attribute;

    @ColumnWidth(10)
    @ExcelProperty("类型描述")
    private String typeDesc;

    @ColumnWidth(10)
    @ExcelProperty( "等级描述")
    private String priorityDesc;

    @ColumnWidth(10)
    @ExcelProperty("状态描述")
    private String statusDesc;

    @ColumnWidth(50)
    @ContentStyle(wrapped = true)
    @ExcelProperty("前置条件")
    private String precondition;

//    @ColumnWidth(50)
//    @ExcelProperty("测试步骤")
//    private String testSteps;

    @ColumnWidth(50)
    @ContentStyle(wrapped = true)
    @ExcelProperty("步骤描述")
    private String stepDesc;

    @ColumnWidth(50)
    @ExcelProperty("预期结果")
    @ContentStyle(wrapped = true)
    private String expectResult;

    @ColumnWidth(50)
    @ExcelProperty("备注")
    private String comment;

    @ColumnWidth(10)
    @ExcelProperty("责任人")
    private String dutyUser;

    @ColumnWidth(10)
    @ExcelProperty("执行次数")
    private Integer executeNum;

    @ColumnWidth(10)
    @ExcelProperty( "标签")
    private String tags;

    @ColumnWidth(10)
    @ExcelProperty("创建人")
    private String creator;

    @ColumnWidth(20)
    @ExcelProperty("创建时间")
    private String gmtCreate;

    @ColumnWidth(10)
    @ExcelProperty("更新人")
    private String modifier;

    @ColumnWidth(20)
    @ExcelProperty("更新时间")
    private String gmtModified;

}
