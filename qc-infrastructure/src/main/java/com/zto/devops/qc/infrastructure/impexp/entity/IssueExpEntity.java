package com.zto.devops.qc.infrastructure.impexp.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.zto.devops.framework.infrastructure.dao.entity.ExpEntity;
import lombok.Data;

import java.io.Serializable;

@Data
@ContentRowHeight(30)// 表体行高
@HeadRowHeight(20)// 表头行高
@ColumnWidth(35)// 列宽
public class IssueExpEntity extends ExpEntity implements Serializable {

    @ColumnWidth(50)
    @ExcelProperty("缺陷名称")
    private String title;

    @ColumnWidth(25)
    @ExcelProperty("缺陷编号")
    private String code;

    @ColumnWidth(25)
    @ExcelProperty("缺陷状态")
    private String status;

    @ColumnWidth(25)
    @ExcelProperty("优先级")
    private String priority;

    @ColumnWidth(25)
    @ExcelProperty("产品")
    private String productName;

    @ColumnWidth(25)
    @ExcelProperty("发现版本")
    private String findVersionName;

    @ColumnWidth(25)
    @ExcelProperty("修复版本")
    private String fixVersionName;

    @ColumnWidth(25)
    @ExcelProperty("BUG根源")
    private String rootCause;

    @ColumnWidth(25)
    @ExcelProperty("BUG类别")
    private String typeDesc;

    @ColumnWidth(25)
    @ExcelProperty("测试方法")
    private String testMethod;

    @ColumnWidth(25)
    @ExcelProperty("重现概率")
    private String repetitionRate;

    @ColumnWidth(25)
    @ExcelProperty("发现阶段")
    private String findStage;

    @ColumnWidth(25)
    @ExcelProperty("发现环境")
    private String findEnv;

    @ColumnWidth(25)
    @ExcelProperty("有效缺陷")
    private String validFlag;

    @ColumnWidth(25)
    @ExcelProperty("关联需求")
    private String requirementName;

    @ColumnWidth(25)
    @ExcelProperty("关联迭代")
    private String sprintName;

    @ColumnWidth(25)
    @ExcelProperty("标签")
    private String tagName;

    @ColumnWidth(25)
    @ExcelProperty("应用类型")
    private String applicationTypeDesc;

    @ColumnWidth(25)
    @ExcelProperty("拒绝原因")
    private String refuseReasonDesc;

    @ColumnWidth(25)
    @ExcelProperty("当前处理人")
    private String handleUserName;

    @ColumnWidth(25)
    @ExcelProperty("开发人员")
    private String developUserName;

    @ColumnWidth(25)
    @ExcelProperty("测试人员")
    private String testUserName;

    @ColumnWidth(25)
    @ExcelProperty("报告人")
    private String findUserName;

    @ColumnWidth(25)
    @ExcelProperty("更新人")
    private String updateUserName;

    @ColumnWidth(25)
    @ExcelProperty("抄送人")
    private String ccUserName;

    @ColumnWidth(25)
    @ExcelProperty("创建时间")
    private String gmtCreate;

    @ColumnWidth(25)
    @ExcelProperty("开始修复时间")
    private String startFixTime;

    @ColumnWidth(25)
    @ExcelProperty("交付验证时间")
    private String deliverTime;

    @ColumnWidth(25)
    @ExcelProperty("关闭时间")
    private String closeTime;

    @ColumnWidth(25)
    @ExcelProperty("拒绝时间")
    private String rejectTime;

    @ColumnWidth(25)
    @ExcelProperty("延期修复时间")
    private String delayFixTime;

    @ColumnWidth(25)
    @ExcelProperty("重新打开时间")
    private String reopenTime;

    @ColumnWidth(25)
    @ExcelProperty("更新时间")
    private String updateTime;

}
