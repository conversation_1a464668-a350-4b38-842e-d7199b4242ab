package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.framework.infrastructure.dao.BaseMapper;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticSourceLogTestcaseVO;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceLogTestcaseEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AutomaticSourceLogTestcaseMapper extends BaseMapper<AutomaticSourceLogTestcaseEntity> {

    int insertList(@Param("list") List<AutomaticSourceLogTestcaseEntity> list);

    List<AutomaticSourceLogTestcaseVO> selectListAndPlanNameLog(@Param("logCode")String logCode, @Param("flag")String flag );
}