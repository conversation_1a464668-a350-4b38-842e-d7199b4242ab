package com.zto.devops.qc.infrastructure.impexp;

import com.zto.devops.framework.client.enums.impexp.ExpProcessorEnum;
import com.zto.devops.framework.infrastructure.service.impexp.ExpProcessor;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.query.ExpIssueQuery;
import com.zto.devops.qc.domain.service.IssueQueryDomainService;
import com.zto.devops.qc.infrastructure.impexp.entity.IssueExpEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
@Slf4j
public class IssueExpProcessor extends ExpProcessor<ExpIssueQuery, IssueExpEntity> {

    @Autowired
    private IssueQueryDomainService issueQueryDomainService;

    protected IssueExpProcessor() {
        super(IssueExpEntity.class, ExpIssueQuery.class , ExpProcessorEnum.EXP_PRODUCT_ISSUE.name());
    }

    @Override
    protected String getFileName() {
        return "缺陷导出列表";
    }

    @Override
    public List<IssueExpEntity> pageData(ExpIssueQuery query) {
        List<IssueExpEntity> exportList = new ArrayList<>();
        try {
            List<IssueVO> issueList = issueQueryDomainService.exportIssueCount(query);
            issueList.stream().forEach(issueVO -> {
                IssueExpEntity entity = new IssueExpEntity();
                entity.setTitle(issueVO.getTitle());
                entity.setCode(issueVO.getCode());
                entity.setStatus(null == issueVO.getStatus() ? "" : issueVO.getStatus().getValue());
                entity.setPriority(null == issueVO.getPriority() ? "" : issueVO.getPriority().getValue());
                entity.setProductName(issueVO.getProductName());
                entity.setFindVersionName(issueVO.getFindVersionName());
                entity.setFixVersionName(issueVO.getFixVersionName());
                entity.setRootCause(null == issueVO.getRootCause() ? "" : issueVO.getRootCause().getValue());
                entity.setTypeDesc(null == issueVO.getType() ? "" : issueVO.getType().getValue());
                entity.setTestMethod(null == issueVO.getTestMethod() ? "" : issueVO.getTestMethod().getValue());
                entity.setRepetitionRate(null == issueVO.getRepetitionRate() ? "" : issueVO.getRepetitionRate().getValue());
                entity.setFindStage(null == issueVO.getFindStage() ? "" : issueVO.getFindStage().getValue());
                entity.setFindEnv(null == issueVO.getFindEnv() ? "" : issueVO.getFindEnv().getValue());
                entity.setValidFlag(issueVO.getValidFlag() ? "是" : "否");
                entity.setRequirementName(issueVO.getRequirementName());
                entity.setSprintName(issueVO.getSprintName());
                entity.setTagName(issueVO.getTagName());
                entity.setApplicationTypeDesc(null == issueVO.getApplicationType() ? "" : issueVO.getApplicationType().getValue());
                entity.setRefuseReasonDesc(issueVO.getReasonDesc());
                entity.setHandleUserName(issueVO.getHandleUserName());
                entity.setDevelopUserName(issueVO.getDevelopUserName());
                entity.setTestUserName(issueVO.getTestUserName());
                entity.setFindUserName(issueVO.getFindUserName());
                entity.setUpdateUserName(issueVO.getUpdateUserName());
                entity.setCcUserName(issueVO.getCcUserName());
                SimpleDateFormat sdfTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                entity.setGmtCreate(null == issueVO.getGmtCreate() ? "" : sdfTime.format(issueVO.getGmtCreate()));
                entity.setStartFixTime(null == issueVO.getStartFixTime() ? "" : sdfTime.format(issueVO.getStartFixTime()));
                entity.setDeliverTime(null == issueVO.getDeliverTime() ? "" : sdfTime.format(issueVO.getDeliverTime()));
                entity.setCloseTime(null == issueVO.getCloseTime() ? "" : sdfTime.format(issueVO.getCloseTime()));
                entity.setRejectTime(null == issueVO.getRejectTime() ? "" : sdfTime.format(issueVO.getRejectTime()));
                entity.setDelayFixTime(null == issueVO.getDelayFixTime() ? "" : sdfTime.format(issueVO.getDelayFixTime()));
                entity.setReopenTime(null  == issueVO.getReopenTime() ? "" : sdfTime.format(issueVO.getReopenTime()));
                entity.setUpdateTime(null == issueVO.getUpdateTime() ? "" : sdfTime.format(issueVO.getUpdateTime()));
                exportList.add(entity);
            });
            return exportList;
        } catch (Exception e) {
            log.error("导出异常:{}", e.getMessage());
            return Collections.emptyList();
        }
    }


}
