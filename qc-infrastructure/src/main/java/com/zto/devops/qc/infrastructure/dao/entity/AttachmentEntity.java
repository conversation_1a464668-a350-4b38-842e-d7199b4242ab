package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.issue.AttachmentDocumentTypeEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentFileTypeEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentTypeEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.AttachmentDocumentTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.AttachmentFileTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.AttachmentTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler;
import lombok.Data;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@ToString
@Data
@Table(name = "qc_attachment")
public class AttachmentEntity extends BaseEntity {

    /**
     * 编码
     */
    @Id
    private String code;

    /**
     * 所属领域
     */
    @Column(name = "domain")
    @ColumnType(typeHandler = DomainHandler.class)
    private DomainEnum domain;

    /**
     * 业务编码: 业务实例 如 缺陷编码等
     */
    @Column(name = "business_code")
    private String businessCode;

    /**
     * 附件文档类型
     */
    @Column(name = "document_type")
    @ColumnType(typeHandler = AttachmentDocumentTypeHandler.class)
    private AttachmentDocumentTypeEnum documentType;

    /**
     * 附件格式 AttachmentTypeEnum
     */
    @Column(name = "type")
    @ColumnType(typeHandler = AttachmentTypeHandler.class)
    private AttachmentTypeEnum type;

    /**
     * 附件名称
     */
    private String name;

    /**
     * 私有组文件名
     */
    @Column(name = "remote_file_id")
    private String remoteFileId;

    /**
     * 文件路径
     */
    private String url;

    @Column(name = "file_type")
    @ColumnType(typeHandler = AttachmentFileTypeHandler.class)
    private AttachmentFileTypeEnum fileType;

    @Column(name = "size")
    private String size;


}