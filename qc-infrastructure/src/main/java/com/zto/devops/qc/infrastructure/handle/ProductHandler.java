package com.zto.devops.qc.infrastructure.handle;

import com.zto.devops.framework.common.message.EventHandler;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.product.client.model.product.command.AddMemberInfo;
import com.zto.devops.product.client.model.product.event.MemberAddedBatchEvent;
import com.zto.devops.product.client.model.product.event.MemberEditedEvent;
import com.zto.devops.product.client.model.product.event.MemberRemovedEvent;
import com.zto.devops.product.client.model.product.event.ProductModifiedEvent;
import com.zto.devops.qc.client.enums.knowledgebase.roleTypeEnum;
import com.zto.devops.qc.client.enums.rpc.MemberTypeEnum;
import com.zto.devops.qc.client.service.knowledgebase.KnowledgeBaseService;
import com.zto.devops.qc.client.service.knowledgebase.model.AddKnowledgeBaseByProductCodeReq;
import com.zto.devops.qc.client.service.knowledgebase.model.RemoveKnowledgeBaseByProductCodeReq;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class ProductHandler {

    @Autowired
    private KnowledgeBaseService knowledgeBaseService;


    @Autowired
    private IProductRpcService productRpcService;


    @Autowired
    private QcConfigBasicService qcConfigBasicService;


    public final static List<roleTypeEnum> allRoleTypeEnum = Arrays.asList(roleTypeEnum.MANAGER, roleTypeEnum.EDITOR, roleTypeEnum.VIEWER, roleTypeEnum.ONLY_VIEWER);

    @EventHandler
    public void handleProductModifiedEvent(ProductModifiedEvent event) {

        /**
         * 产品负责人
         */
        if (!Objects.equals(event.getProducter().getUserId(), event.getOldProductUserId()) && event.getProducter() != null) {

            forceRemoveMember(event.getCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getProducter().getUserId()), allRoleTypeEnum);
            //添加新的产品负责人的管理权限
            addMember(event.getCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getProducter().getUserId()), roleTypeEnum.MANAGER);
            //删除老成员管理
            forceRemoveMember(event.getCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getOldProductUserId()), allRoleTypeEnum);
            if (productRpcService.getAllProductRole(event.getCode(), event.getOldProductUserId()) == 0) {
                return;
            }
            addMember(event.getCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getOldProductUserId()), roleTypeEnum.EDITOR);
        }

        /**
         * 开发负责人
         */
        if (!Objects.equals(event.getDeveloper().getUserId(), event.getOldDeveloperUserId()) && event.getDeveloper() != null) {
            forceRemoveMember(event.getCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getDeveloper().getUserId()), allRoleTypeEnum);
            addMember(event.getCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getDeveloper().getUserId()), roleTypeEnum.EDITOR);
            if (productRpcService.getAllProductRole(event.getCode(), event.getOldDeveloperUserId()) == 0) {
                forceRemoveMember(event.getCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getOldDeveloperUserId()), allRoleTypeEnum);
            }
        }

        /**
         * 项目经理负责人
         */
        if (!Objects.equals(event.getProjecter().getUserId(), event.getOldProjectUserId()) && event.getProjecter() != null) {
            forceRemoveMember(event.getCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getProjecter().getUserId()), allRoleTypeEnum);
            addMember(event.getCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getProjecter().getUserId()), roleTypeEnum.EDITOR);
            if (productRpcService.getAllProductRole(event.getCode(), event.getOldProjectUserId()) == 0) {
                forceRemoveMember(event.getCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getOldProjectUserId()), allRoleTypeEnum);
            }
        }
        /**
         * 测试负责人
         */
        if (!Objects.equals(event.getTester().getUserId(), event.getOldTesterUserId()) && event.getTester() != null) {
            forceRemoveMember(event.getCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getTester().getUserId()), allRoleTypeEnum);
            addMember(event.getCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getTester().getUserId()), roleTypeEnum.EDITOR);
            if (productRpcService.getAllProductRole(event.getCode(), event.getOldTesterUserId()) == 0) {
                forceRemoveMember(event.getCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getOldTesterUserId()), allRoleTypeEnum);
            }
        }
    }

    private void addMember(String productCode, String operateUserId, String addUserId, roleTypeEnum roleType) {
        AddKnowledgeBaseByProductCodeReq addKnowledgeBaseByProductCodeReq = new AddKnowledgeBaseByProductCodeReq();
        addKnowledgeBaseByProductCodeReq.setProductCode(productCode);
        addKnowledgeBaseByProductCodeReq.setOperatedUserId(operateUserId);
        addKnowledgeBaseByProductCodeReq.setUserId(addUserId);
        addKnowledgeBaseByProductCodeReq.setRoleType(roleType);
        knowledgeBaseService.addMemberPermission(addKnowledgeBaseByProductCodeReq);
    }


    private void forceRemoveMember(String productCode, String operateUserId, String removeUserId, List<roleTypeEnum> roleTypeList) {

        if (CollectionUtil.isEmpty(roleTypeList)) {
            return;
        }
        for (roleTypeEnum roleTypeEnum : roleTypeList) {
            RemoveKnowledgeBaseByProductCodeReq removeKnowledgeBaseByProductCodeReq = new RemoveKnowledgeBaseByProductCodeReq();
            removeKnowledgeBaseByProductCodeReq.setProductCode(productCode);
            removeKnowledgeBaseByProductCodeReq.setOperatedUserId(operateUserId);
            removeKnowledgeBaseByProductCodeReq.setUserId(removeUserId);
            removeKnowledgeBaseByProductCodeReq.setRoleType(roleTypeEnum);
            knowledgeBaseService.RemoveMemberPermission(removeKnowledgeBaseByProductCodeReq);

        }


    }


    @EventHandler
    public void handleProductEditedEvent(MemberEditedEvent event) {
        if (event.getMemberType().name().equals(MemberTypeEnum.PRODUCTER_OWNER.name())) {
            forceRemoveMember(event.getProductCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getUser().getUserId()), allRoleTypeEnum);
            addMember(event.getProductCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getUser().getUserId()), roleTypeEnum.MANAGER);
            forceRemoveMember(event.getProductCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getOldUser().getUserId()), allRoleTypeEnum);
            if (productRpcService.getAllProductRole(event.getProductCode(), event.getOldUser().getUserId()) == 0) {
                return;
            }
            addMember(event.getProductCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getOldUser().getUserId()), roleTypeEnum.EDITOR);
        } else {
            forceRemoveMember(event.getProductCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getUser().getUserId()), allRoleTypeEnum);
            addMember(event.getProductCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getUser().getUserId()), roleTypeEnum.EDITOR);
            if (productRpcService.getAllProductRole(event.getProductCode(), event.getOldUser().getUserId()) == 0) {
                forceRemoveMember(event.getProductCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getOldUser().getUserId()), allRoleTypeEnum);
            }
        }
    }


    @EventHandler
    public void handleProductAddEvent(MemberAddedBatchEvent event) {

        if (CollectionUtil.isEmpty(event.getMembers())) {
            return;
        }
        for (AddMemberInfo member : event.getMembers()) {
            AddKnowledgeBaseByProductCodeReq addKnowledgeBaseByProductCodeReq = new AddKnowledgeBaseByProductCodeReq();
            //添加新的管理权限
            addKnowledgeBaseByProductCodeReq.setProductCode(event.getProductCode());
            addKnowledgeBaseByProductCodeReq.setOperatedUserId(qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId());  //操作人
            addKnowledgeBaseByProductCodeReq.setUserId(String.valueOf(member.getUser().getUserId()));  //新的成员
            addKnowledgeBaseByProductCodeReq.setRoleType(roleTypeEnum.EDITOR);
            knowledgeBaseService.addMemberPermission(addKnowledgeBaseByProductCodeReq);
        }
    }

    @EventHandler
    public void handleProductRemoveEvent(MemberRemovedEvent event) {
        //判断当前修改的成员是否还有权限
        if (productRpcService.getAllProductRole(event.getProductCode(), event.getRemoveUserId()) > 0) {
            return;
        }
        forceRemoveMember(event.getProductCode(), qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId(), String.valueOf(event.getRemoveUserId()), allRoleTypeEnum);
    }

}

