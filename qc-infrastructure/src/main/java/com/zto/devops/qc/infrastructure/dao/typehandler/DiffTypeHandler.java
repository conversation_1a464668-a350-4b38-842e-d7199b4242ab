package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

/**
 * <AUTHOR>
 * @create 2022/12/30 16:40
 */
@MappedJdbcTypes(value = JdbcType.VARCHAR, includeNullJdbcType = true)
public class DiffTypeHandler extends BaseEnumTypeHandler<DiffTypeEnum> {
    public DiffTypeHandler() {
        super(DiffTypeEnum.class);
    }
}
