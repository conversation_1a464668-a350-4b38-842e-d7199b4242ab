package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.QcVersionQualityMetricsEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.QcVersionQualityMetricsEntity;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface QcVersionQualityMetricsConverter {

    QcVersionQualityMetricsEntity convert(QcVersionQualityMetricsEntityDO entityDO);

}
