package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.dto.ApiGlobalConfigurationEntityDO;
import com.zto.devops.qc.domain.gateway.repository.ApiGlobalConfigurationRepository;
import com.zto.devops.qc.infrastructure.converter.ApiGlobalConfigurationEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.ApiGlobalConfigurationEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TmApiConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ApiGlobalConfigurationRepositoryImpl implements ApiGlobalConfigurationRepository {

    @Autowired
    private TmApiConfigMapper tmApiConfigMapper;

    @Autowired
    private ApiGlobalConfigurationEntityConverter apiGlobalConfigurationEntityConverter;

    @Override
    public void batchInsertApiGlobalConfiguration(List<ApiGlobalConfigurationEntityDO> list) {
        tmApiConfigMapper.batchInsert(apiGlobalConfigurationEntityConverter.convertApiGlobalConfigurationEntity(list));
    }


    @Override
    public int deleteApiGlobalConfigurationByProductCode(String productCode, User user) {
//        ApiGlobalConfigurationEntity apiGlobalConfigurationEntity = new ApiGlobalConfigurationEntity();
//        apiGlobalConfigurationEntity.setEnable(0);
//        apiGlobalConfigurationEntity.setGmtModified(new Date());
//        apiGlobalConfigurationEntity.setModifierId(user.getUserId());
//        apiGlobalConfigurationEntity.setModifier(user.getUserName());
        Example example = new Example(ApiGlobalConfigurationEntity.class);
        example.createCriteria().andEqualTo("productCode", productCode);
        return tmApiConfigMapper.deleteByExample(example);
    }

    @Override
    public List<ApiGlobalConfigurationEntityDO> selectApiGlobalConfigurationByProductCode(String productCode) {
        Example example = new Example(ApiGlobalConfigurationEntity.class);
        example.createCriteria().andEqualTo("productCode", productCode)
                .andNotEqualTo("enable", 0);
        return apiGlobalConfigurationEntityConverter.convertApiGlobalConfigurationEntityDO(tmApiConfigMapper.selectByExample(example));
    }

    @Override
    public List<ApiGlobalConfigurationEntityDO> queryEnabledConfigByProductCode(String productCode) {
        Example example = new Example(ApiGlobalConfigurationEntity.class);
        example.createCriteria().andEqualTo("productCode", productCode)
                .andEqualTo("enable", 2);
        return apiGlobalConfigurationEntityConverter.convertApiGlobalConfigurationEntityDO(tmApiConfigMapper.selectByExample(example));
    }

    @Override
    public List<String> queryApiConfigAllApiCode(String productCode) {
        Example example = new Example(ApiGlobalConfigurationEntity.class);
        example.createCriteria()
                .andEqualTo("apiConfigScope", 1)
                .andNotEqualTo("enable", 0);
        if (StringUtils.isNotEmpty(productCode)) {
            example.createCriteria().andEqualTo("productCode", productCode);
        }
        example.selectProperties("apiConfigValue").setDistinct(true);
        return Optional.ofNullable(tmApiConfigMapper.selectByExample(example))
                .orElse(Collections.emptyList())
                .stream().map(ApiGlobalConfigurationEntity::getApiConfigValue)
                .collect(Collectors.toList());
    }

    @Override
    public void updateApiConfigMainApiCode(String apiCode, String mainApiCode) {
        Example example = new Example(ApiGlobalConfigurationEntity.class);
        example.createCriteria()
                .andEqualTo("apiConfigValue", apiCode)
                .andEqualTo("apiConfigScope", 1)
                .andNotEqualTo("enable", 0);
        ApiGlobalConfigurationEntity entity = new ApiGlobalConfigurationEntity();
        entity.setApiConfigValue(mainApiCode);
        tmApiConfigMapper.updateByExampleSelective(entity, example);
    }
}
