package com.zto.devops.qc.infrastructure.gateway.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.LinkMapTypeEnum;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.util.ShellParseUtilService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

@Slf4j
@Service
public class ShellParseUtil implements ShellParseUtilService {

    @Autowired
    private ZtoOssService ztoOssService;

    private static final String path = "apitest/linkmap/shell/";

    @Override
    public String getAssertsAsString(JSONObject jsonObject) {
        if (StringUtils.isBlank(jsonObject.getString("type"))) {
            return null;
        }
        if (LinkMapTypeEnum.ASSERT_BEANSHELL.name().equals(jsonObject.getString("type"))) {
            if (StringUtils.isBlank(jsonObject.getString("expected"))) {
                return null;
            }
            return getAssertsScripts(jsonObject.getString("expected"));
        } else if (LinkMapTypeEnum.IF_ASSERT_BEANSHELL.name().equals(jsonObject.get("type"))) {
            if (CollectionUtil.isEmpty(jsonObject.getJSONArray("conditionExpected"))) {
                return null;
            }
            return getIfAssertsScripts(jsonObject.getJSONArray("conditionExpected"));
        } else {
            return null;
        }
    }

    @Override
    public String getSqlAsString(String sqlStr) {
        StringBuilder repSql = new StringBuilder();
        repSql.append("import org.apache.log4j.Logger;").append("\n")
                .append("String sqlStr = \"").append(sqlStr.replaceAll("\n", " ").trim()).append("\";").append("\n")
                .append("vars.put(\"devops_qc_jdbc_sql\",${__eval(sqlStr)});").append("\n")
                .append("log.warn(\"JDBC处理 >>>>> SQL语句： \"+vars.get(\"devops_qc_jdbc_sql\"));").append("\n");
        return repSql.toString();
    }

    @Override
    public String getESRequestBodyAsString(String esBody) {
        StringBuilder esBodyStr = new StringBuilder();
        esBodyStr.append("import org.apache.log4j.Logger;").append("\n")
//                .append("import com.alibaba.fastjson.JSONObject;").append("\n")
//                .append("import com.alibaba.fastjson.JSON;").append("\n")
//                .append("import com.alibaba.fastjson.JSONArray;").append("\n")
//                .append("import com.alibaba.fastjson.serializer.SerializerFeature;").append("\n")
                .append("String esBody = \"\"\"").append(esBody).append("\"\"\";").append("\n")
                .append("vars.put(\"esRequestData\",${__eval(esBody)});").append("\n")
                .append("log.warn(\"ES预处理 >>>>> 请求语句esRequestData： \"+vars.get(\"esRequestData\"));").append("\n");
        return esBodyStr.toString();
    }

    @Override
    public String getIfControllerLogScripts(String ifScript, String ifControllerComments) {
        StringBuilder ifController = new StringBuilder();
        ifController.append("import org.apache.log4j.Logger;").append("\n")
                .append("log.warn(\"条件 ").append(ifControllerComments).append("上的语句:").append(ifScript.replaceAll("\"", "")).append(" 执行结果为 >>>>>> {} \",").append("${__groovy(").append(ifScript).append(")});");
        return ifController.toString();
    }

    public static String getJSONPathScripts(List<String> varsName) {
        StringBuilder varsJson = new StringBuilder();
        varsJson.append("import org.apache.log4j.Logger;").append("\n");
        for(String varName:varsName) {
            varsJson.append("log.warn(\"变量提取： ").append(varName).append(" >>>>> {}\",").append("vars.get(\""+varName+"\"));").append("\n");
        }
        return varsJson.toString();
    }

    private String getIfAssertsScripts(JSONArray assertArray) {
        StringBuilder assertCommand = new StringBuilder();
        for (int i = 0; i < assertArray.size(); i++) {
            JSONObject assertObject = assertArray.getJSONObject(i);
            if (null != assertObject) {
                if (StringUtils.isNotBlank(assertObject.getString("condition")) &&
                        StringUtils.isNotBlank(assertObject.getString("expected"))) {
                    assertCommand.append("if(").append(assertObject.getString("condition"))
                            .append("){\n").append(assertObject.getString("expected"))
                            .append(";\n").append("}\n");
                }
            }
        }
        return getAssertsScripts(assertCommand.toString());
    }

    private String getAssertsScripts(String assertCommand) {
        String content = "";
        try {
            content = ztoOssService.getObjectText("autojmx", path + "assertJSR223.groovy");
        } catch (IOException e) {
            throw new ServiceException("获取节点用例数据失败！" + e.getMessage());
        }
        if(StringUtils.isNotBlank(content)) {
            StringBuilder repAssert = new StringBuilder();
            repAssert.append(content).append("\n")
                    .append("log.warn('''断言执行 >>>>> ")
                    .append(assertCommand.replaceAll("\"", "'")
                            .replaceAll("\r\n", " ")
                            .replaceAll("\n", " "))
                    .append("''');").append("\n")
                    .append(assertCommand);
            return repAssert.toString();
        }
        return content;
    }

    @Override
    public String getPollingWhileCondition() {
        return "${__groovy(\"${devops_qc_polling_status}\" == \"START\",)}";
    }

    @Override
    public String getPollingIntervalScript(long interval) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("log.info(\"当前轮询次数: ${devops_qc_polling_counter}\")\n")
                .append("if (${devops_qc_polling_counter} > 1) {\n")
                .append(String.format("\tThread.sleep(%s)\n", interval))
                .append("}");
        return stringBuilder.toString();
    }

    @Override
    public String getPollingConditionScript(String condition) {
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(condition)) {
            stringBuilder.append(String.format("if (%s) {\n", condition))
                    .append("\tvars.put(\"devops_qc_polling_status\", \"CONDITION\")\n")
                    .append("\tvars.put(\"devops_qc_config_thread_exit_flag\", vars.get(\"devops_qc_config_thread_exit_flag_save\"))\n")
                    .append("\tlog.info(\"轮询条件终止: ${__time(yyyy-MM-dd HH:mm:ss:SSS,)}\")\n")
                    .append("} else ");
        }
        stringBuilder.append("if (${__time(,)} - ${devops_qc_polling_start} >= ${devops_qc_polling_timeout}) {\n")
                .append("\tvars.put(\"devops_qc_polling_status\", \"TIMEOUT\")\n")
                .append("\tvars.put(\"devops_qc_config_thread_exit_flag\", vars.get(\"devops_qc_config_thread_exit_flag_save\"))\n")
                .append("\tlog.info(\"轮询超时终止: ${__time(yyyy-MM-dd HH:mm:ss:SSS,)}\")\n")
                .append("}");
        return stringBuilder.toString();
    }

    @Override
    public String getPollingIgnoreScript() {
        return "if (\"${devops_qc_polling_status}\" == \"START\") prev.setIgnore()";
    }

    @Override
    public String getPollingAssertScript(String condition) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("if (\"${devops_qc_polling_status}\" == \"TIMEOUT\" && \"${devops_qc_config_thread_exit_flag}\" == \"1\") ")
                .append(String.format("assert %s", condition));
        return stringBuilder.toString();
    }
}
