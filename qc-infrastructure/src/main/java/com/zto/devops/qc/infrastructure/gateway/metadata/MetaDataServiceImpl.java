package com.zto.devops.qc.infrastructure.gateway.metadata;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.metadata.*;
import com.zto.devops.qc.domain.gateway.metadata.MetaDataService;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.infrastructure.config.MetaDataConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class MetaDataServiceImpl implements MetaDataService {

    @Autowired
    private MetaDataConfig metaDataConfig;

    @Autowired
    private ZtoOssService ztoOssService;

    private final String ENV = "fat";

    @Cacheable(cacheNames = "docProductCodeCache", key = "#productCode")
    @Override
    public String getMetaDataProductCode(String productCode) {
        try (HttpResponse httpResponse = HttpUtil.createGet(metaDataConfig.getOpenApiUrl())
                .auth("token " + metaDataConfig.getOpenApiToken())
                .form("code", productCode)
                .execute()) {
            return Optional.ofNullable(httpResponse.body())
                    .map(JSON::parseObject)
                    .map(resp -> resp.getJSONObject("results"))
                    .map(result -> result.getString("openid"))
                    .orElse(null);
        } catch (Exception e) {
            log.error("getMetaDataProductCode {}", productCode, e);
            return null;
        }
    }

    @Override
    public List<MetaDataDocHttpVO> getMetaDataDocHttp(String productCode) {
        try (HttpResponse httpResponse = HttpUtil.createGet(metaDataConfig.getDocHttpUrl())
                .header("x-token", metaDataConfig.getDocToken())
                .form("env", ENV)
                .form("productCode", productCode)
                .execute()) {
            return Optional.ofNullable(httpResponse.body())
                    .map(JSON::parseObject)
                    .filter(resp -> Boolean.TRUE.equals(resp.getBoolean("status")))
                    .map(resp -> JSON.parseArray(resp.getString("result"), MetaDataDocHttpVO.class))
                    .orElse(null);
        } catch (Exception e) {
            log.error("getMetaDataDocHttp {}", productCode, e);
            return null;
        }
    }

    @Override
    public MetaDataDocHttpVO getMetaDataDocDetail(Long id) {
        try (HttpResponse httpResponse = HttpUtil.createGet(metaDataConfig.getDocDetailUrl())
                .header("x-token", metaDataConfig.getDocToken())
                .form("env", ENV)
                .form("id", id)
                .execute()) {
            return Optional.ofNullable(httpResponse.body())
                    .map(JSON::parseObject)
                    .filter(resp -> Boolean.TRUE.equals(resp.getBoolean("status")))
                    .map(resp -> JSON.parseObject(resp.getString("result"), MetaDataDocHttpVO.class))
                    .orElse(null);
        } catch (Exception e) {
            log.error("getMetaDataDocDetail {}", id, e);
            return null;
        }
    }

    @Override
    public List<GatewayNamespaceVO> getGatewayNamespace() {
        try (HttpResponse httpResponse = HttpUtil.createGet(metaDataConfig.getGatewayDetailUrl())
                .header("x-token", metaDataConfig.getDocToken())
                .form("env", ENV)
                .form("pageNum", 1)
                .form("pageSize", 99999)
                .execute()) {
            return Optional.ofNullable(httpResponse.body())
                    .map(JSON::parseObject)
                    .filter(resp -> Boolean.TRUE.equals(resp.getBoolean("status")))
                    .map(resp -> resp.getJSONObject("result"))
                    .map(result -> JSON.parseArray(result.getString("records"), GatewayNamespaceVO.class))
                    .orElse(null);
        } catch (Exception e) {
            log.error("getGatewayNamespace", e);
            return null;
        }
    }

    @Override
    public List<GatewayApiVO> getGatewayApi(String namespace) {
        try (HttpResponse httpResponse = HttpUtil.createGet(metaDataConfig.getGatewayApiUrl())
                .header("x-token", metaDataConfig.getDocToken())
                .form("env", ENV)
                .form("namespace", namespace)
                .form("pageNum", 1)
                .form("pageSize", 99999)
                .execute()) {
            return Optional.ofNullable(httpResponse.body())
                    .map(JSON::parseObject)
                    .filter(resp -> Boolean.TRUE.equals(resp.getBoolean("status")))
                    .map(resp -> resp.getJSONObject("result"))
                    .map(result -> JSON.parseArray(result.getString("records"), GatewayApiVO.class))
                    .orElse(null);
        } catch (Exception e) {
            log.error("getGatewayApi {}", namespace, e);
            return null;
        }
    }

    @Override
    public List<GlobalEnvVO> getDocGlobalEnv(String productCode) {
        try (HttpResponse httpResponse = HttpUtil.createGet(metaDataConfig.getDocGlobalEnvUrl())
                .header("x-token", metaDataConfig.getDocToken())
                .form("productCode", productCode)
                .execute()) {
            return Optional.ofNullable(httpResponse.body())
                    .map(JSON::parseObject)
                    .filter(resp -> Boolean.TRUE.equals(resp.getBoolean("status")))
                    .map(resp -> JSON.parseArray(resp.getString("result"), GlobalEnvVO.class))
                    .orElse(null);
        } catch (Exception e) {
            log.error("getDocGlobalEnv {}", productCode, e);
            return null;
        }
    }

    @Cacheable(cacheNames = "lubanProductCodeCache", key = "#productCode")
    @Override
    public String getLubanProductCode(String productCode) {
        try (HttpResponse httpResponse = HttpUtil.createGet(metaDataConfig.getOpenApiUrl())
                .auth("token " + metaDataConfig.getOpenApiToken())
                .form("openid", productCode)
                .execute()) {
            return Optional.ofNullable(httpResponse.body())
                    .map(JSON::parseObject)
                    .map(resp -> resp.getJSONObject("results"))
                    .map(result -> result.getString("code"))
                    .orElse(null);
        } catch (Exception e) {
            log.error("getLubanProductCode {}", productCode, e);
            return null;
        }
    }

    @Override
    public List<MetaDataDocHttpVO> getMetaDataDocDubbo(String productCode) {
        try (HttpResponse httpResponse = HttpUtil.createGet(metaDataConfig.getDocDubboUrl())
                .header("x-token", metaDataConfig.getDocToken())
                .form("env", ENV)
                .form("productCode", productCode)
                .execute()) {
            return Optional.ofNullable(httpResponse.body())
                    .map(JSON::parseObject)
                    .filter(resp -> Boolean.TRUE.equals(resp.getBoolean("status")))
                    .map(resp -> JSON.parseArray(resp.getString("result"), MetaDataDocHttpVO.class))
                    .orElse(null);
        } catch (Exception e) {
            log.error("getMetaDataDocDubbo {}", productCode, e);
            return null;
        }
    }

    @Override
    public GatewayApiSettingVO getGatewayApiSetting(String productCode, String serviceInfo) {
        Map<String, Object> form = new HashMap<>();
        form.put("env", ENV);
        form.put("productCode", productCode);
        JSONObject body = new JSONObject();
        body.put("apiServiceInfo", serviceInfo);
        String url = HttpUtil.urlWithForm(metaDataConfig.getApiSettingUrl(), form, StandardCharsets.UTF_8, true);
        try (HttpResponse httpResponse = HttpUtil.createPost(url)
                .header("x-token", metaDataConfig.getDocToken())
                .body(body.toJSONString())
                .execute()) {
            return Optional.ofNullable(httpResponse.body())
                    .map(resp -> JSON.parseObject(resp, GatewayApiSettingVO.class))
                    .map(vo -> {
                        if (StringUtils.isNotBlank(vo.getSignValue())) {
                            JSONObject gatewayConfigSign = JSON.parseObject(vo.getSignValue());
                            vo.setSignValue(gatewayConfigSign.getString("value"));
                        }
                        return vo;
                    })
                    .orElse(null);
        } catch (Exception e) {
            log.error("getGatewayApiSetting {}", serviceInfo, e);
            return null;
        }
    }
}
