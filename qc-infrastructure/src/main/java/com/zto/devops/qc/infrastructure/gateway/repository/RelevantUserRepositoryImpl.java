package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.client.enums.constants.RelevantUserHandleRoleEnum;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import com.zto.devops.qc.client.model.common.handler.AbstractCurrentHandlerChangedEvent;
import com.zto.devops.qc.client.model.dto.RelevantUserEntityDO;
import com.zto.devops.qc.client.model.issue.entity.CurrentHandlerVO;
import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import com.zto.devops.qc.client.model.issue.event.RelevantUserAddedEvent;
import com.zto.devops.qc.client.model.issue.event.RelevantUserRemovedSimpleEvent;
import com.zto.devops.qc.client.model.parameter.TaskBaseParameter;
import com.zto.devops.qc.client.model.parameter.TaskResultParameter;
import com.zto.devops.qc.domain.gateway.repository.IRelevantUserRepository;
import com.zto.devops.qc.infrastructure.converter.RelevantUserDOConvertor;
import com.zto.devops.qc.infrastructure.converter.RelevantUserEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.RelevantUserEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.RelevantUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/4/9
 * @Version 1.0
 */
@Component
public class RelevantUserRepositoryImpl implements IRelevantUserRepository {

    @Autowired
    private RelevantUserMapper relevantUserMapper;

    @Autowired
    private RelevantUserDOConvertor convertor;

    @Autowired
    private final static RelevantUserEntityConverter RELEVANT_USER_ENTITY_CONVERTER = RelevantUserEntityConverter.INSTANCE;

    @Override
    public List<TaskResultParameter> queryMyObj(TaskBaseParameter taskBaseParameter) {
        return relevantUserMapper.queryMyObj(taskBaseParameter);
    }

    @Override
    public List<RelevantUserEntityDO> queryMyTaskTotal(Long userId, List<String> types, List<String> issueStatuses,String productCode) {
        return convertor.covert(relevantUserMapper.queryMyTaskTotal(userId, types, issueStatuses,productCode));
    }

    @Override
    public Long countUserTypeIssueCount(String userId, List<String> status) {
        return relevantUserMapper.countUserTypeIssueCount(userId, status);
    }

    @Override
    public void ccIssue(RelevantUserAddedEvent event) {
        Set<RelevantUserVO> relevantUserVOS = event.getRelevantUserVOS();
        List<RelevantUserEntity> entityList = RELEVANT_USER_ENTITY_CONVERTER.convert(relevantUserVOS);
        for (RelevantUserEntity entity : entityList) {
            if (relevantUserExist(entity.getType(), entity.getBusinessCode(), entity.getUserId())) {
                continue;
            }
            entity.preCreate(event);
            relevantUserMapper.insertSelective(entity);
        }

    }

    @Override
    public void unCCIssueCommand(RelevantUserRemovedSimpleEvent event) {
        RelevantUserEntity entity = new RelevantUserEntity();
        entity.setCode(event.getCode());
        entity.setEnable(Boolean.FALSE);
        relevantUserMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public List<RelevantUserVO> findByBusinessCode(String code) {
        List<RelevantUserEntity> relevantUserEntities = relevantUserMapper.findByBusinessCode(code);
        if (CollectionUtil.isNotEmpty(relevantUserEntities)) {
            return RELEVANT_USER_ENTITY_CONVERTER.convert(relevantUserEntities);
        }
        return CollectionUtil.newEmptyList();
    }

    @Override
    public List<TaskResultParameter> queryMyIssue(TaskBaseParameter taskBaseParameter) {
        return relevantUserMapper.queryMyIssue(taskBaseParameter);
    }

    @Override
    public void saveByEvent(String businessCode, User operator, DomainEnum domainEnum, String actionCode, RelevantUserTypeEnum relevantUserTypeEnum) {
        RelevantUserEntity entity = new RelevantUserEntity();
        entity.setBusinessCode(businessCode);
        entity.setEnable(true);
        entity.setGmtCreate(new Date());
        entity.setCreator(operator.getUserName());
        entity.setCreatorId(operator.getUserId());
        entity.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
        entity.setDomain(domainEnum);
        entity.setType(relevantUserTypeEnum);
        entity.setUserId(String.valueOf(operator.getUserId()));
        entity.setHandleType(RelevantUserHandleRoleEnum.USER.name());
        entity.setUserName(operator.getUserName());
        entity.setAction(actionCode);
        relevantUserMapper.insertSelective(entity);
    }

    @Override
    public void save(RelevantUserEntityDO entityDO) {
        relevantUserMapper.insertSelective(convertor.covert(entityDO));
    }

    @Override
    public void updateDelete(String businessCode, User operator, DomainEnum domainEnum) {
        Example example = new Example(RelevantUserEntity.class);
        example.createCriteria()
                .andEqualTo("businessCode", businessCode)
                .andEqualTo("domain", domainEnum.name());
        RelevantUserEntity entity = new RelevantUserEntity();
        entity.setEnable(false);
        entity.setModifier(operator.getUserName());
        entity.setModifierId(operator.getUserId());
        entity.setGmtModified(new Date());
        relevantUserMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void updateByEvent(AbstractCurrentHandlerChangedEvent event) {
        if (CollectionUtil.isNotEmpty(event.getDeleteHandler())) {
            Example example = new Example(RelevantUserEntity.class);
            example.createCriteria().andIn("code", event.getDeleteHandler().stream().map(CurrentHandlerVO::getCode).collect(Collectors.toList()));
            RelevantUserEntity entity = new RelevantUserEntity();
            entity.setEnable(false);
            entity.preUpdate(event);
            relevantUserMapper.updateByExampleSelective(entity, example);
        }
        if (CollectionUtil.isNotEmpty(event.getLastHandler())) {
            for (CurrentHandlerVO user : event.getLastHandler()) {
                RelevantUserEntity entity = convertor.convert(user);
                entity.preCreate(event);
                relevantUserMapper.insertSelective(entity);
            }
        }
    }

    @Override
    public List<RelevantUserVO> queryCCByIssueCodeList(List<String> issueCodeList) {
        if (CollectionUtil.isEmpty(issueCodeList)) {
            return new ArrayList<>();
        }
        Example example = new Example(RelevantUserEntity.class);
        example.orderBy("businessCode");
        example.selectProperties("userName", "businessCode");
        example.createCriteria()
                .andEqualTo("type", RelevantUserTypeEnum.CC.name())
                .andEqualTo("enable", 1)
                .andIn("businessCode", issueCodeList);
        List<RelevantUserEntity> entityList = relevantUserMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : convertor.convert2VO(entityList);
    }


    private boolean relevantUserExist(RelevantUserTypeEnum type, String businessCode, String userId) {
        if (!RelevantUserTypeEnum.CC.equals(type)) {
            return false;
        }
        Example example = new Example(RelevantUserEntity.class);
        example.createCriteria().andEqualTo("type", type)
                .andEqualTo("businessCode", businessCode)
                .andEqualTo("userId", userId)
                .andEqualTo("enable", 1);
        int i = relevantUserMapper.selectCountByExample(example);
        return i > 0;

    }

    @Override
    public RelevantUserEntityDO findRelevantUser(RelevantUserTypeEnum type, String businessCode, String userId) {
        Example example = new Example(RelevantUserEntity.class);
        example.createCriteria().andEqualTo("type", type)
                .andEqualTo("businessCode", businessCode)
                .andEqualTo("userId", userId)
                .andEqualTo("enable", 1);
        List<RelevantUserEntityDO> list = convertor.covert(relevantUserMapper.selectByExample(example));
        return CollectionUtil.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public void updateAccessTagByCode(String code, String accessTag) {
        Example example = new Example(RelevantUserEntity.class);
        example.createCriteria().andEqualTo("code", code);
        RelevantUserEntity entity = new RelevantUserEntity();
        entity.setAccessTag(accessTag);
        relevantUserMapper.updateByExampleSelective(entity, example);
    }
}
