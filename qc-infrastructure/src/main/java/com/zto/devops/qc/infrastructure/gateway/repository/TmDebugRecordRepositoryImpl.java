package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.model.dto.TmDebugRecordEntityDO;
import com.zto.devops.qc.domain.gateway.repository.TmDebugRecordRepository;
import com.zto.devops.qc.infrastructure.converter.TmDebugRecordEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.TmDebugRecordEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TmDebugRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Component
@Slf4j
public class TmDebugRecordRepositoryImpl implements TmDebugRecordRepository {

    @Autowired
    private TmDebugRecordMapper tmDebugRecordMapper;

    @Autowired
    private TmDebugRecordEntityConverter converter;

    @Override
    public TmDebugRecordEntityDO queryDebugRecordByTaskId(String taskId) {
        Example example = new Example(TmDebugRecordEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", taskId);
        List<TmDebugRecordEntity> entityList = tmDebugRecordMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new TmDebugRecordEntityDO() : converter.convertEntityDO(entityList.get(0));
    }

    @Override
    public void insertDebugRecord(TmDebugRecordEntityDO entityDO) {
        tmDebugRecordMapper.insertSelective(converter.convertEntity(entityDO));
    }

    @Override
    public void updateDebugRecord(TmDebugRecordEntityDO entityDO) {
        TmDebugRecordEntity entity = converter.convertEntity(entityDO);
        Example example = new Example(TmDebugRecordEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskId", entityDO.getTaskId());
        tmDebugRecordMapper.updateByExampleSelective(entity, example);
    }
}
