package com.zto.devops.qc.infrastructure.gateway.jenkins;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.client.model.jenkins.JenkinsBuild;
import com.zto.devops.qc.client.model.jenkins.JenkinsBuildDetail;
import com.zto.devops.qc.client.model.rpc.pipeline.FeatureVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskInfo;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AnalysisAutomaticRecordVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticNode;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteCaseResultContentVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskExecutedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskTerminatedEvent;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.jenkins.IJenkinsService;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.util.DateUtil;
import com.zto.devops.qc.infrastructure.config.AmazonS3Config;
import com.zto.devops.qc.infrastructure.config.JenkinsCallbackConfig;
import com.zto.devops.qc.infrastructure.config.JenkinsConfig;
import com.zto.devops.qc.infrastructure.util.AmazonS3Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.*;

@Service
@Slf4j
public class JenkinsServiceImpl implements IJenkinsService {

    @Autowired
    private AmazonS3Utils amazonS3Utils;

    @Autowired
    private JenkinsCallbackConfig jenkinsCallbackConfig;

    @Autowired
    private AmazonS3Config amazonS3Config;

    @Autowired
    private JenkinsConfig jenkinsConfig;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    @Autowired
    private ZtoOssService ossService;

    @Autowired
    private IPipelineRpcService pipelineRpcService;

    @Override
    public String getResultFile(String ossPath, String filepath) {
        if (StringUtils.isBlank(ossPath)) {
            return null;
        }
        if (ossPath.startsWith("/")) {
            ossPath = ossPath.substring(1);
        }
        if (ossPath.endsWith("/")) {
            ossPath = ossPath.substring(0, ossPath.length() - 1);
        }
        String ossFile = ossPath + filepath;
        return amazonS3Utils.isExist(jenkinsCallbackConfig.getBucketName(), ossFile) ? ossFile : null;
    }

    @Override
    public AutomaticTaskEntityDO execute(AutomaticTaskExecutedEvent event, List<AutomaticNode> execInfo) {
        String url = null, token = null;
        try {
            if (AutomaticRecordTypeEnum.JMETER.equals(event.getType())
                    || AutomaticRecordTypeEnum.JMETER_GIT.equals(event.getType())) {
                execInfo = execInfo.get(0).getAutomaticNodeList();
                url = jenkinsConfig.getJmeterBuildUrl();
                token = jenkinsConfig.getJmeterToken();
            }
            if (AutomaticRecordTypeEnum.TESTNG.equals(event.getType())) {
                url = jenkinsConfig.getTestngBuildUrl();
                token = jenkinsConfig.getTestngBuildToken();
            }
            if (AutomaticRecordTypeEnum.PYTEST.equals(event.getType())) {
                url = jenkinsConfig.getPytestBuildUrl();
                token = jenkinsConfig.getPytestBuildToken();
            }
        } catch (Exception e) {
            log.error("执行异常！", e);
            AutomaticTaskEntityDO entityDO = new AutomaticTaskEntityDO();
            entityDO.setCode(event.getCode());
            entityDO.setContent(url);
            entityDO.setStatus(AutomaticStatusEnum.ERROR);
            entityDO.setComment("提交Jenkins执行任务请求失败！");
            entityDO.setFinishTime(new Date());
            return entityDO;
        }
        return submitAutomaticTaskExecute(event, execInfo, url, token);
    }

    private AutomaticTaskEntityDO submitAutomaticTaskExecute(
            AutomaticTaskExecutedEvent event, List<AutomaticNode> execInfo, String url, String token) {
        String execInfoFile = event.getCode() + "/execInfo.json";
        try {
            boolean result = amazonS3Utils.createObject(
                    jenkinsCallbackConfig.getBucketName(),
                    execInfoFile,
                    JSON.toJSONString(execInfo));
            log.info("上传执行自动化用例文件：{} {}", execInfoFile, result);
        } catch (Exception e) {
            log.error("上传执行自动化用例文件异常！{}", execInfoFile, e);
        }
        Map<String, Object> param = new HashMap<>();
        param.put("token", token);
        param.put("codeid", event.getCode());

        param.put("ztoenv", (null != event.getSceneCaseFlag() && event.getSceneCaseFlag())
                ? (StringUtils.isBlank(event.getTag()) ? "fat1" : event.getTag())
                : event.getEnv());

        param.put("ztoenv", event.getEnv());
        // 3.506.0 用例工厂非动态多环境不传ztoenv
        if(null != event.getSceneCaseFlag() && event.getSceneCaseFlag()) {
            List<FeatureVO> list = pipelineRpcService.listFeatureConfig(event.getProductCode());
            if(CollectionUtils.isNotEmpty(list) && !this.isAutoEnv(list)) {
                param.put("ztoenv", "QcNoEnv" + (StringUtils.isBlank(event.getTag()) ? "fat1" : event.getTag()));
            }
        }
        param.put("execInfo", execInfoFile);
        param.put("type", AutomaticTaskTrigModeEnum.HEART_BEAT.equals(event.getTrigMode())
                ? "heart"
                : ((null != event.getSceneCaseFlag() && event.getSceneCaseFlag()) ? "scene" : "normal"));
        String titansDubboTag = System.getProperty("titans.dubbo.tag");
        String heartDubboTag = qcConfigBasicService.getHeartDubboTag();
        param.put("dubbotag", StringUtils.defaultIfBlank(titansDubboTag, heartDubboTag));
        param.put("productCode", event.getProductCode());
        param.put("productName", event.getProductName());
        if (AutomaticRecordTypeEnum.JMETER.equals(event.getType())) {
            param.put("jmxFileName", new File(event.getSourceAddress(), event.getFilename()).getPath());
        }
        if (AutomaticRecordTypeEnum.TESTNG.equals(event.getType())
                || AutomaticRecordTypeEnum.PYTEST.equals(event.getType())) {
            param.put("gitUrl", event.getSourceAddress());
            param.put("branchName", event.getBranchName());
            param.put("commitid", event.getCommitId());
            param.put("workdir", event.getWorkDir());
        }
        if (AutomaticRecordTypeEnum.JMETER_GIT.equals(event.getType())) {
            param.put("jmxFileName",
                    event.getCommitId() + "/" + event.getAutomaticSourceCode() + "/" + event.getFilename());
        }
        param.put("ossPath", getOssPath(event.getOccurred(), event.getCode()));
        log.info("执行自动化用例任务参数：{}", param);
        AutomaticTaskEntityDO entityDO = new AutomaticTaskEntityDO();
        entityDO.setCode(event.getCode());
        entityDO.setContent(url);
        submitJenkins(entityDO, param);
        return entityDO;
    }

    private void submitJenkins(AutomaticTaskEntityDO entityDO, Map<String, Object> param) {
        try (HttpResponse httpResponse = HttpUtil.createPost(entityDO.getContent())
                .basicAuth(jenkinsConfig.getUserName(), jenkinsConfig.getToken())
                .form(param)
                .execute()) {
            if (StringUtils.isNotBlank(httpResponse.header("Location"))) {
                entityDO.setStatus(AutomaticStatusEnum.SUBMITTED);
            } else {
                log.error("提交Jenkins执行任务请求失败！{}", entityDO.getCode());
                entityDO.setStatus(AutomaticStatusEnum.ERROR);
                entityDO.setComment("提交Jenkins执行任务请求失败！");
                entityDO.setFinishTime(new Date());
            }
        } catch (Exception e) {
            log.error("提交Jenkins执行任务请求失败！{}", entityDO.getCode(), e);
            entityDO.setStatus(AutomaticStatusEnum.ERROR);
            entityDO.setFinishTime(new Date());
            String comment = Optional.ofNullable(e.getMessage()).orElse("提交Jenkins执行任务请求失败！");
            if (comment.length() > 500) {
                entityDO.setComment(comment.substring(0, 500));
            } else {
                entityDO.setComment(comment);
            }
        }
    }

    @Override
    public JSONObject getCaseFileContent(String caseFile) throws IOException {
        String text = amazonS3Utils.getObjectText(jenkinsCallbackConfig.getBucketName(), caseFile);
        return JSON.parseObject(text);
    }

    @Override
    public void terminate(AutomaticTaskTerminatedEvent event) {
        String url = "";
        if (AutomaticRecordTypeEnum.JMETER.equals(event.getType())
                || AutomaticRecordTypeEnum.JMETER_GIT.equals(event.getType())) {
            url = jenkinsConfig.getJmeterStopUrl();
        }
        if (AutomaticRecordTypeEnum.TESTNG.equals(event.getType())) {
            url = jenkinsConfig.getTestngStopUrl();
        }
        if (AutomaticRecordTypeEnum.PYTEST.equals(event.getType())) {
            url = jenkinsConfig.getPytestStopUrl();
        }
        if (AutomaticRecordTypeEnum.JMETER_DEBUG.equals(event.getType())) {
            url = jenkinsConfig.getJmeterDebugStopUrl();
        }
        url = MessageFormat.format(url, event.getBuildId());
        log.info("终止任务url >>>> {}",url);
        try (HttpResponse httpResponse = HttpUtil.createPost(url)
                .basicAuth(jenkinsConfig.getUserName(), jenkinsConfig.getToken())
                .execute()) {
            if (StringUtils.isBlank(httpResponse.header("Location"))) {
                log.error("终止自动化任务失败！{} {}", event.getCode(), httpResponse);
            }
        } catch (Exception e) {
            log.error("终止自动化任务失败！{}", event.getCode(), e);
        }
    }

    @Override
    public boolean analyse(AnalysisAutomaticRecordVO vo, AutomaticRecordTypeEnum type) {
        String token = null, url = null;
        if (AutomaticRecordTypeEnum.TESTNG.equals(type)) {
            token = jenkinsConfig.getTestngAnalyseToken();
            url = jenkinsConfig.getTestngAnalyseUrl();
        }
        if (AutomaticRecordTypeEnum.PYTEST.equals(type)) {
            token = jenkinsConfig.getPytestAnalyseToken();
            url = jenkinsConfig.getPytestAnalyseUrl();
        }
        if (AutomaticRecordTypeEnum.JMETER_GIT.equals(type)) {
            token = jenkinsConfig.getGitJmeterAnalyseToken();
            url = jenkinsConfig.getGitJmeterAnalyseUrl();
        }
        if (null == url) {
            return false;
        }
        Map<String, Object> param = new HashMap<>(7);
        param.put("token", token);
        param.put("codeid", vo.getCode());
        param.put("gitUrl", vo.getGitUrl());
        param.put("branchName", vo.getBranchName());
        param.put("workdir", vo.getWorkDir());
        String titansDubboTag = System.getProperty("titans.dubbo.tag");
        String heartDubboTag = qcConfigBasicService.getHeartDubboTag();
        param.put("dubbotag", StringUtils.defaultIfBlank(titansDubboTag, heartDubboTag));
        param.put("ext", JSON.toJSONString(vo));
        param.put("dataPath", vo.getDataPath());
        param.put("libPath", vo.getLibPath());
        boolean failed;
        try (HttpResponse httpResponse = HttpUtil.createPost(url)
                .basicAuth(jenkinsConfig.getUserName(), jenkinsConfig.getToken())
                .form(param)
                .execute()) {
            failed = StringUtils.isBlank(httpResponse.header("Location"));
        } catch (Exception e) {
            log.error("提交Jenkins解析请求失败！", e);
            failed = true;
        }
        return failed;
    }

    @Override
    public String getErrorLogFile(String ossPath) {
        return MessageFormat.format("{0}/{1}/{2}/jenkins.log",
                amazonS3Config.getEndPoint(),
                jenkinsCallbackConfig.getBucketName(),
                ossPath);
    }

    @Override
    public List<AutomaticNode> getCallbackFileContent(String filename) throws IOException {
        String text = amazonS3Utils.getObjectText(jenkinsCallbackConfig.getBucketName(), filename);
        return JSON.parseArray(text, AutomaticNode.class);
    }

    @Override
    public List<ExecuteCaseResultContentVO> getExecuteCaseResultContent(String resultFile) {
        try {
            String text = amazonS3Utils.getObjectText(jenkinsCallbackConfig.getBucketName(), resultFile);
            return JSON.parseArray(text, ExecuteCaseResultContentVO.class);
        } catch (Exception e) {
            log.error("获取用例执行结果文件内容异常！{}", resultFile, e);
        }
        return null;
    }

    /**
     * 自动化api Excel数据上传到OSS
     */

    @Override
    public void uploadExcelToOss(String remote, String fileName, String local) {
        try {
            ossService.createObject(jenkinsConfig.getAutoJmxBucketName(), remote, fileName, local);
        } catch (Exception e) {
            log.error("上传Excel数据文件异常", e);
            throw new ServiceException("上传Excel数据文件异常！" + e.getMessage());
        }
    }

    @Override
    public AutomaticTaskEntityDO executeApiTest(AutomaticTaskExecutedEvent event) {
        String url = jenkinsConfig.getJmeterBuildUrlApi();
        String token = jenkinsConfig.getJmeterTokenApi();

        Map<String, Object> param = new HashMap<>();
        param.put("token", token);
        param.put("codeid", event.getCode());
        param.put("ztoenv", event.getEnv());
        param.put("type", "normal");
        param.put("dubbotag", System.getProperty("titans.dubbo.tag"));
        param.put("jmxFileName", event.getSourceAddress() + "/" + event.getFilename());
        param.put("productCode", event.getProductCode());
        param.put("productName", event.getProductName());
        param.put("taskCode", event.getCode());
        log.info("执行自动化api任务参数：{}", param);

        AutomaticTaskEntityDO entityDO = new AutomaticTaskEntityDO();
        entityDO.setCode(event.getCode());
        entityDO.setContent(url);
        submitJenkins(entityDO, param);
        return entityDO;
    }

    @Override
    public boolean executeDebug(DebugTaskInfo taskInfo, String dubboTag, String productName) {
        String url = jenkinsConfig.getApiTestDebugUrl();
        String token = jenkinsConfig.getApiTestDebugToken();

        Map<String, Object> param = new HashMap<>();
        param.put("token", token);
        param.put("taskid", taskInfo.getTaskId());
        param.put("dubbotag", dubboTag);
        param.put("type", "scene");
        param.put("productCode", taskInfo.getProductCode());
        param.put("productName", productName);
        param.put("sceneCode", taskInfo.getSceneCode());
        param.put("sceneVersion", taskInfo.getSceneVersion());
        param.put("sceneName", taskInfo.getSceneName());
        param.put("ztoenv", taskInfo.getZtoenv());
        // 3.506.0 用例工厂非动态多环境不传ztoenv
        List<FeatureVO> list = pipelineRpcService.listFeatureConfig(taskInfo.getProductCode());
        if(CollectionUtils.isNotEmpty(list) && !this.isAutoEnv(list)) {
            param.put("ztoenv", "QcNoEnv" + taskInfo.getZtoenv());
        }
        log.info("执行自动化api任务参数：{}", param);

        boolean success;
        try (HttpResponse httpResponse = HttpUtil.createPost(url)
                .basicAuth(jenkinsConfig.getUserName(), jenkinsConfig.getToken())
                .form(param)
                .execute()) {
            success = StringUtils.isNotBlank(httpResponse.header("Location"));
        } catch (Exception e) {
            log.error("提交Jenkins调试失败！", e);
            success = false;
        }
        return success;
    }

    @Override
    public List<JenkinsBuild> getBuilds(AutomaticRecordTypeEnum type) {
        String baseUrl = getBaseUrl(type);
        if (StringUtils.isEmpty(baseUrl)) {
            return null;
        }
        String url = baseUrl + "/api/json?tree=builds[number,url]{0,20}";
        try (HttpResponse httpResponse = HttpUtil.createGet(url)
                .basicAuth(jenkinsConfig.getUserName(), jenkinsConfig.getToken())
                .execute()) {
            return Optional.ofNullable(httpResponse.body())
                    .map(JSONObject::parseObject)
                    .map(o -> o.getJSONArray("builds").toJavaList(JenkinsBuild.class))
                    .orElse(null);
        } catch (Exception e) {
            log.error("获取Jenkins任务构建列表失败！", e);
            return null;
        }
    }

    private String getBaseUrl(AutomaticRecordTypeEnum type) {
        if (AutomaticRecordTypeEnum.JMETER.equals(type) || AutomaticRecordTypeEnum.JMETER_GIT.equals(type)) {
            return jenkinsConfig.getJmeterBaseUrl();
        }
        if (AutomaticRecordTypeEnum.TESTNG.equals(type)) {
            return jenkinsConfig.getTestngBaseUrl();
        }
        if (AutomaticRecordTypeEnum.PYTEST.equals(type)) {
            return jenkinsConfig.getPytestBaseUrl();
        }
        return null;
    }

    @Override
    public JenkinsBuildDetail getBuildDetail(AutomaticRecordTypeEnum type, String buildId) {
        String baseUrl = getBaseUrl(type);
        if (StringUtils.isEmpty(baseUrl)) {
            return null;
        }
        String url = MessageFormat.format("{0}/{1}/api/json?tree=building,result,number,url,actions[parameters[*]]", baseUrl, buildId);
        try (HttpResponse httpResponse = HttpUtil.createGet(url)
                .basicAuth(jenkinsConfig.getUserName(), jenkinsConfig.getToken())
                .execute()) {
            return Optional.ofNullable(httpResponse.body())
                    .map(JSONObject::parseObject)
                    .map(jsonObject -> {
                        JenkinsBuildDetail buildDetail = jsonObject.toJavaObject(JenkinsBuildDetail.class);
                        JSONArray actions = jsonObject.getJSONArray("actions");
                        if (CollectionUtils.isEmpty(actions)) {
                            return buildDetail;
                        }
                        for (int i = 0; i < actions.size(); i++) {
                            JSONObject action = actions.getJSONObject(i);
                            JSONArray parameters = action.getJSONArray("parameters");
                            if (CollectionUtils.isNotEmpty(parameters)) {
                                Map<String, String> map = new HashMap<>();
                                for (int j = 0; j < parameters.size(); j++) {
                                    JSONObject param = parameters.getJSONObject(j);
                                    map.put(param.getString("name"), param.getString("value"));
                                }
                                buildDetail.setParameters(map);
                            }
                        }
                        return buildDetail;
                    })
                    .orElse(null);
        } catch (Exception e) {
            log.error("获取Jenkins任务构建详情失败！", e);
            return null;
        }
    }

    @Override
    public String getOssPath(Date date, String code) {
        return DateUtil.formatDate(date, "yyyy/MM/dd/HH/") + code;
    }

    private Boolean isAutoEnv(List<FeatureVO> list) {
        if(CollectionUtils.isEmpty(list)) {
            return false;
        }
        try {
            for (FeatureVO vo : list) {
                if("AUTO_ENV".equals(vo.getKey())) {
                    return (Boolean) vo.getValue();
                }
            }
        }catch (Exception e) {
            log.error("调用pipeline返回内容错误，{}", JSON.toJSONString(list));
        }
        return false;
    }
}
