package com.zto.devops.qc.infrastructure.gateway.file;

import com.zto.devops.qc.domain.gateway.file.FileUtilService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springside.modules.utils.io.FileUtil;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 * @create 2022/9/22 10:55
 */
@Slf4j
@Component
public class FileUtilServiceImpl implements FileUtilService {


    @Override
    public void makesureDirExists(String dir) throws IOException {
        FileUtil.makesureDirExists(dir);
    }

    @Override
    public void write(String content, File file) throws IOException {
        FileUtil.write(content, file);
    }
}
