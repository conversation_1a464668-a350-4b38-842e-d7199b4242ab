package com.zto.devops.qc.infrastructure.converter;

import com.zto.agent.base.api.customer.response.GetCompanyInfoResponse;
import com.zto.base.bean.response.UserResp;
import com.zto.devops.qc.client.model.rpc.huiyan.StoreBrandVO;
import com.zto.devops.qc.client.model.rpc.huiyan.StoreNameVO;
import com.zto.devops.qc.client.model.rpc.outlet.ItemTypeVO;
import com.zto.devops.qc.client.model.rpc.pipeline.UserRespVO;
import com.zto.devops.qc.client.model.rpc.waybill.LabelNameVO;
import com.zto.huiyan.terminal.facade.manage.AgentManageInfoFO;
import com.zto.outlet.bean.response.TDictionaryResult;
import com.zto.waybill.tag.dto.LabelAndValueCO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface BaseDataConvertor {

    UserRespVO convert(UserResp resp);

    LabelNameVO convert(LabelAndValueCO<String> valueCO);

    ItemTypeVO convert(TDictionaryResult result);

    @Mapping(target = "companyName", source = "comName")
    StoreBrandVO convert(GetCompanyInfoResponse response);

    StoreNameVO nameConvert(AgentManageInfoFO infoFO);

}
