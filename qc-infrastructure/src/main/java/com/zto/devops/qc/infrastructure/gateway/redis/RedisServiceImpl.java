package com.zto.devops.qc.infrastructure.gateway.redis;

import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * redis service impl
 *
 * <AUTHOR>
 * @date 2023-03-27 19:58
 */
@Slf4j
@Component
public class RedisServiceImpl implements RedisService {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public boolean hasKey(String key) {
        if (StringUtil.isBlank(key)) {
            return false;
        }
        return redisTemplate.hasKey(key);
    }

    public boolean tryLock(String lockKey, String lockId, long expireSecondsTime) {
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        Boolean isLockSuccess = operations.setIfAbsent(lockKey, lockId, expireSecondsTime, TimeUnit.SECONDS);
        return Boolean.TRUE.equals(isLockSuccess);
    }

    public boolean releaseLock(String lockKey, String lockId) {
        String currentValue = redisTemplate.opsForValue().get(lockKey);
        if (lockId.equals(currentValue)) {
            redisTemplate.delete(lockKey);
            return true;
        }
        return false;
    }

    @Override
    public void setKey(String key, String value, long ttl, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, ttl, timeUnit);
    }

    @Override
    public boolean delete(String key) {
        return redisTemplate.delete(key);
    }

    @Override
    public String getKey(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    @Override
    public void setKey(String key, String value) {
        redisTemplate.opsForValue().set(key, value);
    }

    @Override
    public void hashSet(String key, Object hashKey, Object value) {
        redisTemplate.opsForHash().put(key, hashKey, value);
    }

    @Override
    public Object hashGet(String key, Object hashKey) {
        return redisTemplate.opsForHash().get(key, hashKey);
    }

    @Override
    public Long hashDelete(String key, Object hashKey) {
        return redisTemplate.opsForHash().delete(key, hashKey);
    }

    @Override
    public Map<Object, Object> hashEntries(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    @Override
    public void addListOnRight(String key, String value) {
        if (null == value) {
            return;
        }
        redisTemplate.opsForList().rightPush(key, value);
    }

    public void addToListOnRightWithExpiration(String key, String value, long timeoutInSeconds) {
        if (null == value) {
            return;
        }
        redisTemplate.opsForList().rightPush(key, value);
        redisTemplate.expire(key, timeoutInSeconds, TimeUnit.SECONDS);
    }

    @Override
    public String getListOnLeft(String key) {
        String value = redisTemplate.opsForList().index(key, 0);
        redisTemplate.opsForList().leftPop(key);
        return value;
    }

    @Override
    public void setAliveTime(String key, long time, TimeUnit timeUnit) {
        redisTemplate.expire(key, time, timeUnit);
    }

    @Override
    public List<String> getListRange(String key, int beginIndex, int endIndex) {
        return redisTemplate.opsForList().range(key, beginIndex, endIndex);
    }

    @Override
    public Long listSize(String key) {
        return redisTemplate.opsForList().size(key);
    }

    @Override
    public Long listRemove(String key, Object value) {
        return redisTemplate.opsForList().remove(key, 0, value);
    }

    @Override
    public void opsForZSetAddLast(String key, String value) {
        redisTemplate.opsForZSet().add(key, value, (System.currentTimeMillis() + Math.random()));
        redisTemplate.expire(key, 7, TimeUnit.DAYS);
    }

    @Override
    public void opsForZSetAddFirst(String key, String value) {
        redisTemplate.opsForZSet().add(key, value, -(System.currentTimeMillis() + Math.random()));
        redisTemplate.expire(key, 7, TimeUnit.DAYS);
    }

    @Override
    public Set<String> opsForZSetRang(String key, long l1, long l2) {
        return redisTemplate.opsForZSet().range(key, l1, l2);
    }

    @Override
    public void opsForZSetRemove(String key, Object... values) {
        redisTemplate.opsForZSet().remove(key, values);
    }

}
