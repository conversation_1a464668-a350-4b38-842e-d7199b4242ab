package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.enums.testPlan.TestPlanTypeEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.TestPlanStatusHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.TestPlanTypeHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;

/**
 * <AUTHOR>
@Data
@Table(name = "qc_test_plan")
public class TestPlanEntity extends BaseEntity implements Serializable {
    /**
     * 编号
     */
    @Id
    private String code;

    /**
     * 计划名
     */
    @Column(name = "plan_name")
    private String planName;

    /**
     * 所属产品id
     */
    @Column(name = "product_code")
    private String productCode;

    /**
     * 产品名称
     */
    @Column(name = "product_name")
    private String productName;
    /**
     * 产品名称
     */
    @Column(name = "product_source")
    private String productSource;;

    /**
     * 类型
     */
    @ColumnType(typeHandler = TestPlanTypeHandler.class)
    @Column(name = "type")
    private TestPlanTypeEnum type;

    /**
     * 类型
     */
 //   @ColumnType(typeHandler = TestPlanTypeHandler.class)
    @Transient
    private String typeDesc;

    /**
     * 计划编号
     */
    @Column(name = "plan_code")
    private String planCode;

    /**
     * 测试负责人id
     */
    @Column(name = "test_director_id")
    private Long testDirectorId;

    /**
     * 测试负责人名
     */
    @Column(name = "test_director_name")
    private String testDirectorName;

    /**
     * 产品负责人id
     */
    @Column(name = "product_director_id")
    private Long productDirectorId;

    /**
     * 产品负责人名
     */
    @Column(name = "product_director_name")
    private String productDirectorName;

    // 这个时间会根据版本变更而变更
    /**
     * 修改次数
     */
    @Column(name = "edit_no")
    private Integer editNo;

    /**
     * 状态 是否是草稿
     */
    @ColumnType(typeHandler = TestPlanStatusHandler.class)
    @Column(name = "status")
    private TestPlanStatusEnum status;


    /**
     * 预览html
     */
    private String preview;

    @Column(name = "version_name")
    private String versionName;

    @Column(name = "version_code")
    private String versionCode;

    /**
     * 测试负责人id
     */
    @Column(name = "dept_id")
    private Long deptId;

    /**
     * 测试负责人名
     */
    @Column(name = "dept_name")
    private String deptName;

    /**
     * json
     */
    private String json;

    private static final long serialVersionUID = 1L;

   
}