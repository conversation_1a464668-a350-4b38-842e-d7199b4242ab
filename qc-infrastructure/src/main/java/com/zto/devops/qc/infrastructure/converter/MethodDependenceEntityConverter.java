package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.MethodDependenceEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.MethodDependenceEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MethodDependenceEntityConverter {


    List<MethodDependenceEntityDO> covertList(List<MethodDependenceEntity> entityList);

    MethodDependenceEntityDO covert(MethodDependenceEntity entity);

    List<MethodDependenceEntity> covert2EntityList(List<MethodDependenceEntityDO> entityDOList);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "entryMethodCode", source = "entryMethodCode", defaultValue = "")
    @Mapping(target = "entryMethodType", source = "entryMethodType", defaultValue = "")
    @Mapping(target = "parentMethodCode", source = "parentMethodCode", defaultValue = "")
    @Mapping(target = "fullClassName", source = "fullClassName", defaultValue = "")
    @Mapping(target = "methodName", source = "methodName", defaultValue = "")
    @Mapping(target = "methodDesc", source = "methodDesc", defaultValue = "")
    @Mapping(target = "methodType", source = "methodType", defaultValue = "")
    @Mapping(target = "methodAnnotation", source = "methodAnnotation", defaultValue = "")
    @Mapping(target = "methodLevel", source = "methodLevel", defaultValue = "1")
    @Mapping(target = "methodSort", source = "methodSort", defaultValue = "1")
    @Mapping(target = "methodParameterStr", source = "methodParameterStr", defaultValue = "")
    @Mapping(target = "interfaceFullClassName", source = "interfaceFullClassName", defaultValue = "")
    @Mapping(target = "zcatMetricKey", source = "zcatMetricKey", defaultValue = "")
    @Mapping(target = "enable", source = "enable", defaultValue = "true")
    @Mapping(target = "creatorId", source = "creatorId", defaultValue = "0L")
    @Mapping(target = "creator", source = "creator", defaultValue = "")
    MethodDependenceEntity covert2Entity(MethodDependenceEntityDO entityDO);
}
