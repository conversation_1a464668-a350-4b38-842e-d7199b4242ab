package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.testPlan.query.TestPlanDto;
import com.zto.devops.qc.infrastructure.dao.entity.TestPlanMainEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TestPlanMainMapper extends Mapper<TestPlanMainEntity> {

    TestPlanDto selectTestPlanMainByCode(String code);

    List<TestPlanDto> selectTestPlanMainByProductCode(String productCode);

    List<TestPlanDto> selectTestPlanMainByVersionCode(String versionCode);

    List<TestPlanDto> selectTestPlanByType(@Param("type") String type, @Param("testPlanCode") String testPlanCode);

    //查询所有未关联移动专项测试且主计划需要移动测试的计划
    List<TestPlanDto> selectMobileTestPlanByType(@Param("type") String type, @Param("planName") String planName);

    //查询所有未关联集成专项测试且主计划需要集成测试的计划
    List<TestPlanDto> selectIntegrateTestPlanByType(@Param("type") String type, @Param("planName") String planName);

    List<TestPlanDto> selectSafetyTestPlanByType(@Param("type") String type, @Param("list") List<String> list, @Param("planName") String planName);

    List<TestPlanDto> testAccessPlanQuery(@Param("list") List<String> list, @Param("planName") String planName);

    List<TestPlanDto> testPermitWithoutAccessPlanQuery(@Param("list") List<String> list, @Param("planName") String planName);

    List<TestPlanDto> testPermitWithoutOnlineSmokePlanQuery(@Param("list") List<String> list, @Param("planName") String planName);

    List<TestPlanDto> testMobilePlanList(@Param("testDirectorId") Long testDirectorId, @Param("planName") String planName);

    List<TestPlanDto> testIntegrationPlanList(@Param("testDirectorId") Long testDirectorId, @Param("planName") String planName);

    List<TestPlanDto> testCaseReviewQuery(@Param("list") List<String> list, @Param("planName") String planName);

    TestPlanMainEntity selectByCode(@Param("planCode") String planCode);

    //todo select * ,s.dept_name from qc_test_plan p
    //		left join qc_test_report t on p.code = t.plan_code and t.status='NORMAL'
    //		left  join qc_test_plan  s on p.plan_code=s.code and s.type = 'TEST_PLAN'
    //		where t.code is null and p.test_director_id ='441110' and p.type = 'INTEGRATION_TEST'
    //
}