package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.infrastructure.dao.typehandler.PriorityHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.StatusHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "qc_issue_legacy")
public class IssueLegacyEntity extends BaseEntity {

    /**
     * 模块code
     */
    @Id
    private String code;

    /**
     * 报告code
     */
    @Column(name = "report_code")
    private String reportCode;

    /**
     * 老缺陷code
     */
    @Column(name = "issue_code")
    private String issueCode;

    /**
     * 缺陷名称
     */
    private String title;

    /**
     * 缺陷状态
     */
    @Column(name = "status")
    @ColumnType(typeHandler = StatusHandler.class)
    private IssueStatus status;

    /**
     * 当前处理人人ID
     */
    @Column(name = "handle_user_id")
    private Long handleUserId;

    /**
     * 当前处理人
     */
    @Column(name = "handle_user_name")
    private String handleUserName;

    @Column(name = "develop_user_id")
    private Long developUserId;

    /**
     * 当前处理人
     */
    @Column(name = "develop_user_name")
    private String developUserName;

    /**
     * 优先级
     */
    @Column(name = "priority")
    @ColumnType(typeHandler = PriorityHandler.class)
    private IssuePriority priority;

    /**
     * 报告人ID
     */
    @Column(name = "find_user_id")
    private Long findUserId;

    /**
     * 报告人
     */
    @Column(name = "find_user_name")
    private String findUserName;

    @Column(name = "find_version_code")
    private String findVersionCode;
    @Column(name = "find_version_name")
    private String findVersionName;

    private static final long serialVersionUID = 1L;

}