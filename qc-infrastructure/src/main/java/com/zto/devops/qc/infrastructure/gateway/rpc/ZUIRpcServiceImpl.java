package com.zto.devops.qc.infrastructure.gateway.rpc;

import com.alibaba.dubbo.config.annotation.Reference;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.domain.service.EngineRoomFaultQueryDomainService;
import com.zto.devops.qc.domain.gateway.rpc.IZUIRpcService;
import com.zto.manager.api.AppSearchServiceI;
import com.zto.manager.dto.AllMicroAppQry;
import com.zto.manager.dto.domainmodel.AllMicroAppCO;
import com.zto.manager.dto.domainmodel.SearchResult;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ZUIRpcServiceImpl implements IZUIRpcService {

    @Reference
    private AppSearchServiceI appSearchServiceI;

    @Autowired
    private EngineRoomFaultQueryDomainService engineRoomFaultQueryDomainService;

    @Override
    public Set<String> getZUIAppListByProductCode(String productCode) {
        if (StringUtils.isBlank(productCode)) {
            return null;
        }
        //机房故障不往下走了
        boolean engineRoomFaultSwitch = engineRoomFaultSwitch();
        if (engineRoomFaultSwitch) {
            log.error("主机房故障,不查询微应用");
            return null;
        }
        SearchResult<AllMicroAppCO> resp = appSearchServiceI.queryAllMicroApp(new AllMicroAppQry());
        log.info("queryAllMicroApp_resp-> {}", JsonUtil.toJSON(resp));
        if (null == resp || !resp.isStatus() || CollectionUtil.isEmpty(resp.getResult())) {
            log.info("queryAllMicroApp_resp_is_null-> {}", JsonUtil.toJSON(resp));
            return null;
        }
        return resp.getResult().stream()
                .filter(item -> (item.getProductCode().equals(productCode)))
                .map(AllMicroAppCO::getMicroAppId)
                .collect(Collectors.toSet());
    }

    /**
     * 机房是否宕机，宕机返回true, 非宕机返回false
     * @return
     */
    private boolean engineRoomFaultSwitch() {
        return engineRoomFaultQueryDomainService.engineRoomFault();
    }


}
