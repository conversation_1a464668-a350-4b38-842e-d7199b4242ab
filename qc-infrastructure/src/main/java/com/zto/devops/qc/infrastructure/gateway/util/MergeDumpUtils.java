package com.zto.devops.qc.infrastructure.gateway.util;

import com.zto.devops.framework.client.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.jacoco.core.tools.ExecFileLoader;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class MergeDumpUtils {

    private final String path;

    private final File destFile;

    public MergeDumpUtils(String path) {
        this.path = path;
        this.destFile = new File(path + "jacoco.exec");
    }

    private List<File> fileSets(String dir) {
        List<File> fileSetList = new ArrayList<>();
        File path = new File(dir);
        if (!path.exists()) {
            log.error("No path name is :" + dir);
            return null;
        }
        File[] files = path.listFiles();
        try {
            if (files == null || files.length == 0) {
                return null;
            }
        } catch (NullPointerException npe) {
            npe.printStackTrace();
        }
        for (File file : files) {
            if (file.getName().contains(".exec")) {
                fileSetList.add(file);
            }
        }
        return fileSetList;
    }

    public void executeMerge() {
        final ExecFileLoader loader = new ExecFileLoader();
        load(loader);
        save(loader);
        for (final File fileSet : fileSets(this.path)) {
            if (!fileSet.getName().equals("jacoco.exec")) {
                fileSet.delete();
            }
        }
    }

    /**
     * 加载dump文件
     *
     * @param loader
     */
    private void load(final ExecFileLoader loader) {
        for (final File fileSet : fileSets(this.path)) {
            final File inputFile = new File(this.path, fileSet.getName());
            if (inputFile.isDirectory()) {
                continue;
            }
            try {
                if (!inputFile.getName().equals("jacoco.exec")) {
                    loader.load(inputFile);
                }
            } catch (final IOException e) {
                log.error("", e);
                throw new ServiceException("Unable to read " + inputFile.getAbsolutePath(), e);
            }
        }
    }

    /**
     * 执行合并文件
     *
     * @param loader
     */
    private void save(final ExecFileLoader loader) {
        // 判断是否无实现类代码变更
        if (loader.getExecutionDataStore().getContents().isEmpty()) {
            log.info("Skipping JaCoCo merge execution due to missing execution data files");
        }
        log.info("Writing merged execution data to " + this.destFile.getAbsolutePath());
        try {
            loader.save(this.destFile, false);
        } catch (final IOException e) {
            log.error("", e);
            throw new ServiceException("Unable to write merged file " + this.destFile.getAbsolutePath(), e);
        }
    }
}
