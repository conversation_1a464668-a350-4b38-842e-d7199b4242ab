package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.framework.infrastructure.dao.BaseMapper;
import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindTestcaseModulePathQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseCodeQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageTestcaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.TestcaseQuery;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TestcaseMapper extends BaseMapper<TestcaseEntity> {

    Integer saveBatch(List<TestcaseEntity> list);

    List<ListTestcaseVO> pageTestcase(PageTestcaseQuery query);

    List<SimpleTestcaseVO> selectAllTestCasePath(PageTestcaseQuery query);

    Long countNoGroupTestcase(PageTestcaseQuery query);

    List<ListTestcaseVO> selectTestCaseModuleList(PageTestcaseQuery query);

    /**
     * 查询用例code列表
     *
     * @param query {@link ListTestcaseCodeQuery}
     * @return {@link SimpleTestcaseVO}
     */
    List<SimpleTestcaseVO> selectTestcaseCodeList(ListTestcaseCodeQuery query);

    /**
     * 查询用例创建人或责任人数量
     *
     * @param list   用例code列表
     * @param userId 用户id
     * @return 数量
     */
    Integer selectCountCreatorOrDutyUser(@Param("list") List<String> list, @Param("userId") Long userId);

    void updateList(@Param("list") List<TestcaseEntity> list);

    /**
     * 查询用例状态数量
     *
     * @param list 用例code列表
     * @return 数量
     */
    CheckTestcaseStatusVO selectCountTestcaseStatus(@Param("list") List<String> list);

    void batchInsertCase(@Param("list") List<TestcaseEntity> list);

    /**
     * 查询 有心跳用例设置记录的 codeList
     *
     * @param codeList
     * @return
     */
    List<String> selectHeartCaseList(@Param("list") List<String> codeList);

    /**
     * 查询 没有心跳用例设置记录的 codeList
     *
     * @param codeList
     * @return
     */
    List<String> selectNotHeartCaseList(@Param("list") List<String> codeList);

    CaseStatusNumVO selectCaseStatusNum(@Param("list") List<String> caseCodeList);


    List<TestcaseVO> selectListAndPlanName(@Param("automaticSourceCode") String automaticSourceCode);

    int selectDirectTestcaseCount(PageTestcaseQuery query);

    String selectTestcaseModulePath(FindTestcaseModulePathQuery query);

    void replacePath(@Param("oldPath") String oldPath, @Param("newPath") String newPath);

    void replaceTestcaseModulePath(@Param("oldPath") String oldPath, @Param("newPath") String newPath);

    String selectMaxLengthPathByPath(@Param("path") String path);

    List<String> selectModuleCodeList(PageTestcaseQuery query);

    /**
     * 根据用例codeList,查询手工用例名（去重）
     *
     * @param caseCodeList
     * @return
     */
    List<String> selectDistinctCaseNameByCodeList(@Param("list") List<String> caseCodeList, @Param("setCore") Boolean setCore);

    /**
     * 根据parentCode分组
     *
     * @param caseCodeList 用例code集合
     * @param productCode  产品code
     * @param versionCode  版本code
     * @param type         用例类型
     * @return {@link TestcaseGroupVO}
     */
    List<TestcaseParentInfoVO> selectGroupByCaseCodeList(@Param("list") List<String> caseCodeList, @Param("productCode") String productCode,
                                                         @Param("versionCode") String versionCode, @Param("type") String type,
                                                         @Param("setCore") Boolean setCore);

    /**
     * 查询同名分组
     *
     * @param parentCode  分组code
     * @param moduleName  分组名
     * @param versionCode 版本code
     * @param productCode 产品code
     * @return
     */
    List<SameNameModuleVO> selectSameNameModule(@Param("parentCode") String parentCode,
                                                @Param("moduleName") String moduleName,
                                                @Param("versionCode") String versionCode,
                                                @Param("productCode") String productCode);

    List<ListTestcaseVO> selectTestCaseModule(TestcaseQuery query);

    /**
     * 根据名字和路径长度查询分组（手工用例）
     *
     * @param moduleName  分组名
     * @param pathLength  路径长度
     * @param versionCode 版本code
     * @param productCode 产品code
     * @return
     */
    SameNameModuleVO selectModuleByNameAndPath(@Param("moduleName") String moduleName,
                                               @Param("parentModuleName") String parentModuleName,
                                               @Param("pathLength") Integer pathLength,
                                               @Param("versionCode") String versionCode,
                                               @Param("productCode") String productCode);

    /**
     * 根据名字和路径长度查询分组（核心用例）
     *
     * @param moduleName  分组名
     * @param pathLength  路径长度
     * @param productCode 产品code
     * @return
     */
    SameNameModuleVO selectCoreModuleByNameAndPath(@Param("versionModuleCode") String versionModuleCode,
                                                   @Param("moduleName") String moduleName,
                                                   @Param("parentModuleName") String parentModuleName,
                                                   @Param("pathLength") Integer pathLength,
                                                   @Param("productCode") String productCode);

    /**
     * 根据用例code，查询关联计划（用例code去重）
     *
     * @param codeList 用例code集合
     * @return {@link RelatedCasePlanVO}
     */
    List<RelatedCasePlanVO> selectRelatedPlanList(@Param("list") List<String> codeList);

    TestcaseEntityDO selectModuleInfoByName(TestcaseEntityDO entityDO);

    /**
     * 根据用例code，查询parentCode集合
     *
     * @param caseCodes 用例code集合
     * @return parentCodeList
     */
    List<TestCasePathVO> getParentCodeListByCaseCodeList(@Param("list") List<String> caseCodes);

    /**
     * 统计分组下未删除用例数
     *
     * @param productCode
     * @param versionCode
     * @param parentCodeList
     * @return
     */
    List<CountCaseVO> countByParentCodeList(@Param("productCode") String productCode,
                                            @Param("versionCode") String versionCode,
                                            @Param("list") List<String> parentCodeList);

    /**
     * 根据模块name和parnetCode，查询手工用例模块
     *
     * @param moduleName 模块名
     * @param parentCode 父模块code
     * @return {@link TestcaseEntity}
     */
    TestcaseEntity selectOneByModuleNameAndParentCode(@Param("moduleName") String moduleName,
                                                      @Param("parentCode") String parentCode);

    /**
     * 根据版本code，分组统计未分组用例个数 （xmind-ALL）
     *
     * @param query {@link PageTestcaseQuery}
     * @return
     */
    List<CountNoGroupCaseVO> countNoGroupTestcaseByVersionCodeList(PageTestcaseQuery query);

    /**
     * 根据分组名称，查询顶级分组code
     *
     * @param moduleName
     * @return
     */
    String selectTopModuleCodeByName(@Param("moduleName") String moduleName, @Param("productCode") String productCode);

    /**
     * 根据产品code，查询用例工厂默认分组
     *
     * @param productCode      产品code
     * @param topModuleName    顶级分组名
     * @param secondModuleName 二级分组名
     * @return 二级分组
     */
    TestcaseEntity selectSceneDefaultModule(@Param("productCode") String productCode,
                                              @Param("topModuleName") String topModuleName,
                                              @Param("secondModuleName") String secondModuleName);

    List<TestcaseEntityDO> selectAllTestCaseByParentCode(TestcaseQuery query);
}
