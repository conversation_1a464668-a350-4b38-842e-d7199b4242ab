package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.testmanager.scheduler.entity.SchedulerCaseVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.ListSchedulerCaseQuery;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.SchedulerCaseCodeListQuery;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.SchedulerModuleListQuery;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSchedulerCaseEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface AutomaticSchedulerCaseMapper extends Mapper<AutomaticSchedulerCaseEntity> {

    List<SchedulerCaseVO> selectAllSchedulerCasePath(SchedulerModuleListQuery query);

    List<SchedulerCaseVO> selectSchedulerCaseModuleList(SchedulerModuleListQuery query);

    List<SchedulerCaseVO> selectSchedulerCaseList(ListSchedulerCaseQuery query);

    List<SchedulerCaseVO> selectSchedulerCaseByParentCodeList(ListSchedulerCaseQuery query);

    List<SchedulerCaseVO> selectSchedulerCaseAutomaticModuleList(SchedulerModuleListQuery query);


    /**
     * 批量增加关联用例
     *
     * @param caseEntityList
     */
    void insertBatch(List<AutomaticSchedulerCaseEntity> caseEntityList);

    /**
     * @param productCode   产品code
     * @param schedulerCode 定时任务code
     * @param caseCodeList  用例code 集合
     * @return 用例code 集合
     */
    List<String> selectNotIn(@Param("productCode") String productCode,
                             @Param("schedulerCode") String schedulerCode,
                             @Param("list") List<String> caseCodeList);

    List<String> selectTestcaseCodeList(SchedulerCaseCodeListQuery vo);


}
