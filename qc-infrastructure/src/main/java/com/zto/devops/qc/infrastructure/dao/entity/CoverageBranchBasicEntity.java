package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @create 2022/10/18 17:16
 */
@ToString
@Data
@Table(name = "qc_coverage_branch_basic")
public class CoverageBranchBasicEntity extends BaseEntity {

    public CoverageBranchBasicEntity() {

    }

    public CoverageBranchBasicEntity(String versionCode, String appId, String branchName) {
        this.versionCode = versionCode;
        this.appId = appId;
        this.branchName = branchName;
    }

    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 版本编码
     */
    @Column(name = "version_code")
    private String versionCode;

    /**
     * 版本名称
     */
    @Column(name = "version_name")
    private String versionName;

    /**
     * appId
     */
    @Column(name = "app_id")
    private String appId;

    /**
     * 分支名称
     */
    @Column(name = "branch_name")
    private String branchName;

    /**
     * 基准分支名称
     */
    @Column(name = "basic_branch_name")
    private String basicBranchName;

    /**
     * 基准commitId
     */
    @Column(name = "basic_commit_id")
    private String basicCommitId;

    /**
     * 项目gitId
     */
    @Column(name = "git_project_id")
    private Long gitProjectId;

}