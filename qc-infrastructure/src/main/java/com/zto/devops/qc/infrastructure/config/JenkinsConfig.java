package com.zto.devops.qc.infrastructure.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import org.springframework.stereotype.Component;

@Component
public class JenkinsConfig {

    private Config config = ConfigService.getAppConfig();

    public String getJmeterBuildUrl() {
        return config.getProperty("jenkins.jmeter.build.url", "");
    }

    public String getJmeterBuildUrlApi() {
        return config.getProperty("jenkins.jmeter.apitest.build.url", "");
    }

    public String getJmeterToken() {
        return config.getProperty("jenkins.jmeter.token", "");
    }

    public String getJmeterTokenApi() {
        return config.getProperty("jenkins.jmeter.apitest.token", "");
    }

    public String getTestngBuildUrl() {
        return config.getProperty("jenkins.testng.build.url", "");
    }

    public String getTestngBuildToken() {
        return config.getProperty("jenkins.testng.build.token", "");
    }

    public String getPytestBuildUrl() {
        return config.getProperty("jenkins.pytest.build.url", "");
    }

    public String getPytestBuildToken() {
        return config.getProperty("jenkins.pytest.build.token", "");
    }

    public String getUserName() {
        return config.getProperty("jenkins.username", "");
    }

    public String getToken() {
        return config.getProperty("jenkins.token", "");
    }

    public String getJmeterStopUrl() {
        return config.getProperty("jenkins.jmeter.stop.url", "");
    }

    public String getTestngStopUrl() {
        return config.getProperty("jenkins.testng.stop.url", "");
    }

    public String getPytestStopUrl() {
        return config.getProperty("jenkins.pytest.stop.url", "");
    }

    public String getJmeterDebugStopUrl() {
        return config.getProperty("jenkins.jmeterdebug.stop.url", "");
    }

    public String getTestngAnalyseToken() {
        return config.getProperty("jenkins.testng.analyse.token", "");
    }

    public String getTestngAnalyseUrl() {
        return config.getProperty("jenkins.testng.analyse.url", "");
    }

    public String getPytestAnalyseToken() {
        return config.getProperty("jenkins.pytest.analyse.token", "");
    }

    public String getPytestAnalyseUrl() {
        return config.getProperty("jenkins.pytest.analyse.url", "");
    }

    public String getAutoJmxBucketName() {
        return config.getProperty("jenkins.autojmx.bucket.name", "");
    }

    public String getGitJmeterAnalyseUrl() {
        return config.getProperty("jenkins.gitJmeter.analyse.url", "");
    }

    public String getGitJmeterAnalyseToken() {
        return config.getProperty("jenkins.gitJmeter.analyse.token", "");
    }

    public String getApiTestDebugUrl() {
        return config.getProperty("jenkins.jmeter.apitest.debug.url", "");
    }

    public String getApiTestDebugToken() {
        return config.getProperty("jenkins.jmeter.apitest.debug.token", "");
    }

    public String getJmeterBaseUrl() {
        return config.getProperty("jenkins.jmeter.base.url", "");
    }

    public String getTestngBaseUrl() {
        return config.getProperty("jenkins.testng.base.url", "");
    }

    public String getPytestBaseUrl() {
        return config.getProperty("jenkins.pytest.base.url", "");
    }
}
