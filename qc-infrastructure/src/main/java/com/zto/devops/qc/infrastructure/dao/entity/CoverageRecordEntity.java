package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.infrastructure.dao.typehandler.CoverageRecordStatusHandler;
import lombok.Data;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022/9/16 17:16
 */
@ToString
@Data
@Table(name = "qc_coverage_record")
public class CoverageRecordEntity extends BaseEntity {

    @Column(name = "id")
    private Long id;

    /**
     * 版本编码
     */
    @Column(name = "version_code")
    private String versionCode;

    /**
     * 版本名称
     */
    @Column(name = "version_name")
    private String versionName;

    /**
     * appId
     */
    @Column(name = "app_id")
    private String appId;

    /**
     * 标准值
     */
    @Column(name = "standard_rate")
    private BigDecimal standardRate;

    /**
     * 分支覆盖率
     */
    @Column(name = "branch_record_rate")
    private BigDecimal branchRecordRate;

    /**
     * 分支生成状态
     */
    @Column(name = "branch_status")
    @ColumnType(typeHandler = CoverageRecordStatusHandler.class)
    private String branchStatus;

    /**
     * 分支报告或错误日志地址
     */
    @Column(name = "branch_record_url")
    private String branchRecordUrl;

    /**
     * 分支存储桶名
     */
    @Column(name = "branch_bucket_name")
    private String branchBucketName;

    /**
     * 分支文件名
     */
    @Column(name = "branch_file_name")
    private String branchFileName;

    /**
     * 分支不达标原因
     */
    @Column(name = "branch_remark")
    private String branchRemark;

    /**
     * 分支覆盖率报告异常原因
     */
    @Column(name = "branch_record_error_msg")
    private String branchRecordErrorMsg;

    /**
     * 分支报告创建人
     */
    @Column(name = "branch_creator")
    private String branchCreator;

    /**
     * 分支报告创建时间
     */
    @Column(name = "branch_gmt_create")
    private Date branchGmtCreate;

    /**
     * 分支commitId
     */
    @Column(name = "branch_commit_id")
    private String branchCommitId;

    /**
     * 分支git比对地址
     */
    @Column(name = "branch_git_compare_url")
    private String branchGitCompareUrl;

    /**
     * 主干覆盖率
     */
    @Column(name = "master_record_rate")
    private BigDecimal masterRecordRate;

    /**
     * 主干生成状态
     */
    @Column(name = "master_status")
    @ColumnType(typeHandler = CoverageRecordStatusHandler.class)
    private String masterStatus;

    /**
     * 主干报告或错误日志地址
     */
    @Column(name = "master_record_url")
    private String masterRecordUrl;

    /**
     * 主干不达标原因
     */
    @Column(name = "master_remark")
    private String masterRemark;

    /**
     * 主干异常原因
     */
    @Column(name = "master_record_error_msg")
    private String masterRecordErrorMsg;

    /**
     * 主干报告创建人
     */
    @Column(name = "master_creator")
    private String masterCreator;

    /**
     * 主干报告创建时间
     */
    @Column(name = "master_gmt_create")
    private Date masterGmtCreate;

    /**
     * 测试策略
     */
    @Column(name = "test_strategy")
    private String testStrategy;

    /**
     * 是否白名单，0-否，1-是
     */
    @Column(name = "is_white_list")
    private Integer isWhiteList;

    /**
     * 分支差异类型
     */
    @Column(name = "branch_diff_type")
    private String branchDiffType;

    /**
     * 主干差异类型
     */
    @Column(name = "master_diff_type")
    private String masterDiffType;

    /**
     * 覆盖率报告总行数
     */
    @Column(name = "code_sum")
    private Integer codeSum;

    /**
     * 覆盖率报告已覆盖行数
     */
    @Column(name = "code_cover_num")
    private Integer codeCoverNum;

    /**
     * 代码覆盖率备注
     */
    @Column(name = "comment")
    private String comment;

    /**
     * 主干commitId
     */
    @Column(name = "master_commit_id")
    private String masterCommitId;

    /**
     * 主干git比对地址
     */
    @Column(name = "master_git_compare_url")
    private String masterGitCompareUrl;

    /**
     * 主干存储桶名
     */
    @Column(name = "master_bucket_name")
    private String masterBucketName;

    /**
     * 主干文件名
     */
    @Column(name = "master_file_name")
    private String masterFileName;
}
