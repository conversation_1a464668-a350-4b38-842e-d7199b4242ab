package com.zto.devops.qc.infrastructure.gateway.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/9/22 10:55
 */
@Slf4j
public class HttpUtils {

    private static final CloseableHttpClient httpClient;
    /**
     * 设置超时时间
     */
    public static final int REQUEST_TIMEOUT = 30000;

    public static final int REQUEST_SOCKET_TIME = 30000;
    public static final String DEFAULT_CHARSET = "UTF-8";

    private static RequestConfig requestConfig = RequestConfig.custom()
            .setConnectionRequestTimeout(REQUEST_TIMEOUT)
            .setConnectTimeout(REQUEST_TIMEOUT)
            .setSocketTimeout(REQUEST_TIMEOUT)
            .build();

    static {
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        cm.setMaxTotal(5000);
        cm.setDefaultMaxPerRoute(500);
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(REQUEST_TIMEOUT)
                .setSocketTimeout(REQUEST_SOCKET_TIME)
                .build();
        httpClient = HttpClients.custom()
                .setConnectionManager(cm)
                .setDefaultRequestConfig(requestConfig)
                .setRetryHandler(new DefaultHttpRequestRetryHandler(0, false))
                .disableRedirectHandling()
                .build();
    }

    public static JSONObject doGet(String url, Map<String, String> headers, Map<String, String> params) {
        CloseableHttpResponse httpResponse = null;
        try {
            URIBuilder uriBuilder = new URIBuilder(url);
            if (MapUtils.isNotEmpty(params)) {
                params.forEach(uriBuilder::addParameter);
            }
            HttpGet httpGet = new HttpGet(uriBuilder.build());
            if (MapUtils.isNotEmpty(headers)) {
                headers.forEach(httpGet::setHeader);
            }
            httpResponse = httpClient.execute(httpGet);
            byte[] bytes = EntityUtils.toByteArray(httpResponse.getEntity());
            String responseString = new String(bytes, StandardCharsets.UTF_8);
            return JSONObject.parseObject(responseString);
        } catch (Exception e) {
            log.error("", e);
        } finally {
            if (null != httpResponse) {
                try {
                    EntityUtils.consume(httpResponse.getEntity());
                    httpResponse.close();
                } catch (IOException e) {
                    log.error("", e);
                }
            }
        }
        return null;
    }

    public static String downloadFromUrl(String url, String dir) {
        try {
            URL httpUrl = new URL(url);
            log.info("url = {}, dir = {}", url, dir);
            url = url.split("\\?")[0];
            String fileName = url.substring(url.lastIndexOf("/") + 1);
            File file = new File(dir + fileName);
            FileUtils.copyURLToFile(httpUrl, file);
            return fileName;
        } catch (Exception e) {
            log.error("下载文件异常。", e);
            throw new ServiceException("下载文件异常");
        }
    }

    public static String doPost(String url, String jsonStr) {
        String str = null;
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            httpPost.setEntity(new StringEntity(jsonStr, DEFAULT_CHARSET));
            httpPost.setHeader("content-type", "application/json;charset=UTF-8");
            CloseableHttpResponse response = httpClient.execute(httpPost);

            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                str = EntityUtils.toString(response.getEntity(), DEFAULT_CHARSET);
            }else if (response.getStatusLine().getStatusCode() == 308) {
                // 获取新的Location头信息
                String newLocation = response.getFirstHeader("Location").getValue();

                // 创建新的HttpPost对象，使用新的URL
                HttpPost redirectPost = new HttpPost(newLocation);
                redirectPost.setHeader("content-type", "application/json;charset=UTF-8");
                // 复制原始请求的Entity（如果需要）
                 if (httpPost.getEntity() != null) {
                     redirectPost.setEntity(httpPost.getEntity());
                 }

                // 执行重定向的POST请求
                CloseableHttpResponse redirectResponse = httpClient.execute(redirectPost);

                try {
                    // 处理重定向响应
                    str = EntityUtils.toString(redirectResponse.getEntity());

                } finally {
                    // 释放资源
                    redirectResponse.close();
                }
            }
            response.close();
            httpClient.close();
        } catch (IOException e) {
            log.error("", e);
            throw new ServiceException("doPost error");
        }
        return str;
    }

    public static HttpResponse doPostWithForm(String url, Map<String, String> paramsMap) {
        try {
            HttpPost post = new HttpPost(url);
            List<BasicNameValuePair> pairList = new ArrayList<>();
            for (String key : paramsMap.keySet()) {
                pairList.add(new BasicNameValuePair(key, paramsMap.get(key)));
            }
            UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(pairList, DEFAULT_CHARSET);
            post.setEntity(formEntity);
            CloseableHttpClient httpClient = HttpClientBuilder.create().build();
            HttpResponse response = httpClient.execute(post);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                return response;
            } else {
                throw new RuntimeException("接口连接失败！");
            }
        } catch (Exception e) {
            throw new RuntimeException("接口连接失败！");
        }
    }

    public static String doPostWithToken(String url, String content, String token) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json;charset=UTF-8");
        headerMap.put("x-token", token);
        return sendRequest(url, Method.POST, headerMap, content);
    }

    public static String doPostWithHeader(String url, String content, Map<String, String> headerMap) {
        return sendRequest(url, Method.POST, headerMap, content);
    }

    private static String sendRequest(String url, Method method, Map<String, String> headerMap, String data) {
        log.info("sendRequest_url:{} method:{} header:{},  data:{}", url, method.name(), headerMap, data);
        try {
            HttpRequest request = HttpUtil.createRequest(method, url);
            if (headerMap != null) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    request.header(entry.getKey(), entry.getValue());
                }
            }
            if (StringUtil.isNotBlank(data)) {
                request.body(data);
            }
            cn.hutool.http.HttpResponse response = request.timeout(1000 * 5).execute();
            String result = response.body();
            log.info("sendRequest_response:{}", result);
            return result;
        } catch (Exception e) {
            log.error("sendRequest_error:{}", e.getMessage());
        }
        return Strings.EMPTY;
    }

}
