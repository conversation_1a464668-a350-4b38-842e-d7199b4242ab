package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.issue.event.IssueAddedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueEditedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueVersionEditedEvent;
import com.zto.devops.qc.infrastructure.dao.entity.IssueEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface IssueEntityConverter {
    IssueEntityConverter INSTANCE = Mappers.getMapper(IssueEntityConverter.class);
    @Mapping(target = "findUserId", source = "finder.userId")
    @Mapping(target = "findUserName", source = "finder.userName")
    @Mapping(target = "handleUserId", source = "handler.userId")
    @Mapping(target = "handleUserName", source = "handler.userName")
    @Mapping(target = "developUserId", source = "developer.userId")
    @Mapping(target = "developUserName", source = "developer.userName")
    @Mapping(target = "testUserId", source = "tester.userId")
    @Mapping(target = "testUserName", source = "tester.userName")
    /*@Mapping(target = "creatorId", source = "transactor.userId")
    @Mapping(target = "creatorName", source = "transactor.userName")*/
    @Mapping(target = "updateUserId", source = "transactor.userId")
    @Mapping(target = "updateUserName", source = "transactor.userName")
    @Mapping(target = "productCode", source = "product.code")
    @Mapping(target = "productName", source = "product.name")
    @Mapping(target = "requirementCode", source = "requirement.code")
    @Mapping(target = "requirementName", source = "requirement.name")
    @Mapping(target = "findVersionCode", source = "findVersion.code")
    @Mapping(target = "findVersionName", source = "findVersion.name")
    @Mapping(target = "fixVersionCode", source = "fixVersion.code")
    @Mapping(target = "fixVersionName", source = "fixVersion.name")
    @Mapping(target = "sprintCode", source = "sprint.code")
    @Mapping(target = "sprintName", source = "sprint.name")
    IssueEntity convert(IssueAddedEvent event);

    @Mapping(target = "handleUserId", source = "handler.userId")
    @Mapping(target = "handleUserName", source = "handler.userName")
    @Mapping(target = "developUserId", source = "developer.userId")
    @Mapping(target = "developUserName", source = "developer.userName")
    @Mapping(target = "testUserId", source = "tester.userId")
    @Mapping(target = "testUserName", source = "tester.userName")
    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "updateUserId", source = "transactor.userId")
    @Mapping(target = "updateUserName", source = "transactor.userName")
    @Mapping(target = "requirementCode", source = "requirement.code")
    @Mapping(target = "requirementName", source = "requirement.name")
    @Mapping(target = "findVersionCode", source = "findVersion.code")
    @Mapping(target = "findVersionName", source = "findVersion.name")
    @Mapping(target = "fixVersionCode", source = "fixVersion.code")
    @Mapping(target = "fixVersionName", source = "fixVersion.name")
    @Mapping(target = "sprintCode", source = "sprint.code")
    @Mapping(target = "sprintName", source = "sprint.name")
    IssueEntity convert(IssueEditedEvent event);

    @Mapping(target = "handleUserId", source = "handler.userId")
    @Mapping(target = "handleUserName", source = "handler.userName")
    @Mapping(target = "developUserId", source = "developer.userId")
    @Mapping(target = "developUserName", source = "developer.userName")
    @Mapping(target = "testUserId", source = "tester.userId")
    @Mapping(target = "testUserName", source = "tester.userName")
    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "updateUserId", source = "transactor.userId")
    @Mapping(target = "updateUserName", source = "transactor.userName")
    @Mapping(target = "requirementCode", source = "requirement.code")
    @Mapping(target = "requirementName", source = "requirement.name")
    @Mapping(target = "findVersionCode", source = "findVersion.code")
    @Mapping(target = "findVersionName", source = "findVersion.name")
    @Mapping(target = "fixVersionCode", source = "fixVersion.code")
    @Mapping(target = "fixVersionName", source = "fixVersion.name")
    @Mapping(target = "sprintCode", source = "sprint.code")
    IssueEntity convertVersion(IssueVersionEditedEvent event);
}
