package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.qc.infrastructure.dao.typehandler.*;
import lombok.Data;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@ToString
@Data
@Table(name = "qc_issue")
public class IssueEntity extends BaseEntity {

    /**
     * 缺陷编号
     */
    @Id
    private String code;

    /**
     * 标题
     */
    private String title;

    /**
     * 状态
     */
    @Column(name = "status")
    @ColumnType(typeHandler = StatusHandler.class)
    private IssueStatus status;

    /**
     * 优先级 1 紧急, 2 严重, 3 重要, 4 次要, 5 微小
     */
    @Column(name = "priority")
    @ColumnType(typeHandler = PriorityHandler.class)
    private IssuePriority priority;

    /**
     * 缺陷根源
     */
    @Column(name = "root_cause")
    @ColumnType(typeHandler = RootCauseHandler.class)
    private IssueRootCause rootCause;

    /**
     * 缺陷类型
     */
    @Column(name = "type")
    @ColumnType(typeHandler = IssueTypeHandler.class)
    private IssueType type;

    /**
     * 测试方法
     */
    @Column(name = "test_method")
    @ColumnType(typeHandler = TestMethodHandler.class)
    private IssueTestMethod testMethod;

    /**
     * 发现阶段
     */
    @Column(name = "find_stage")
    @ColumnType(typeHandler = FindStageHandler.class)
    private IssueFindStage findStage;

    /**
     * 发现环境 开发环境, 测试环境, 集成环境, 预发布环境, 生产环境
     */
    @Column(name = "find_env")
    @ColumnType(typeHandler = FindEnvHandler.class)
    private IssueFindEnv findEnv;

    /**
     * 复现概率
     */
    @Column(name = "repetition_rate")
    @ColumnType(typeHandler = RepetitionRateHandler.class)
    private IssueRepetitionRate repetitionRate;

    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    private Date gmtCreate;

    /**
     * 重新打开时间
     */
    @Column(name = "reopen_time")
    private Date reopenTime;

    /**
     * 重新打开次数
     */
    @Column(name = "reopen")
    private Integer reopen;

    /**
     * 实际工时
     */
    @Column(name = "actual_working_hours")
    private Double actualWorkingHours;

    /**
     * 开始修复时间
     */
    @Column(name = "start_fix_time")
    private Date startFixTime;

    /**
     * 延期修复时间
     */
    @Column(name = "delay_fix_time")
    private Date delayFixTime;

    /**
     * 交付验证时间
     */
    @Column(name = "deliver_time")
    private Date deliverTime;

    /**
     * 拒绝时间
     */
    @Column(name = "reject_time")
    private Date rejectTime;

    /**
     * 关闭时间
     */
    @Column(name = "close_time")
    private Date closeTime;

    /**
     * 发现时间
     */
    @Column(name = "find_time")
    private Date findTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 产品ID
     */
    @Column(name = "product_code")
    private String productCode;

    /**
     * 产品名称
     */
    @Column(name = "product_name")
    private String productName;

    /**
     * 需求ID
     */
    @Column(name = "requirement_code")
    private String requirementCode;

    /**
     * 需求名称
     */
    @Column(name = "requirement_name")
    private String requirementName;
    @Column(name = "requirement_level")
    @ColumnType(typeHandler = RequirementLevelHandler.class)
    private RequirementLevel requirementLevel;
    /**
     * 发现版本编码
     */
    @Column(name = "find_version_code")
    private String findVersionCode;

    /**
     * 发现版本名称
     */
    @Column(name = "find_version_name")
    private String findVersionName;

    /**
     * 修复版本编码
     */
    @Column(name = "fix_version_code")
    private String fixVersionCode;

    /**
     * 修复版本名称
     */
    @Column(name = "fix_version_name")
    private String fixVersionName;

    /**
     * 迭代code
     */
    @Column(name = "sprint_code")
    private String sprintCode;
    /**
     * 迭代名称
     */
    @Column(name = "sprint_name")
    private String sprintName;
    /**
     * 报告人ID
     */
    @Column(name = "find_user_id")
    private Long findUserId;

    /**
     * 报告人名称
     */
    @Column(name = "find_user_name")
    private String findUserName;

    /**
     * 更新人ID
     */
    @Column(name = "update_user_id")
    private Long updateUserId;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 经办人名称
     */
    @Column(name = "handle_user_name")
    private String handleUserName;

    /**
     * 经办人ID
     */
    @Column(name = "handle_user_id")
    private Long handleUserId;

    /**
     * 开发人员ID
     */
    @Column(name = "develop_user_id")
    private Long developUserId;

    /**
     * 开发人员名称
     */
    @Column(name = "develop_user_name")
    private String developUserName;

    /**
     * 测试人员ID
     */
    @Column(name = "test_user_id")
    private Long testUserId;

    /**
     * 测试人员名称
     */
    @Column(name = "test_user_name")
    private String testUserName;

    /**
     * 创建人ID
     */
    @Column(name = "creator_id")
    private Long creatorId;

    /**
     * 创建人名称
     */
    @Column(name = "creator")
    private String creator;

    /**
     * 缺陷描述
     */
    private String description;

    @Column(name = "version_confirm")
    private String versionConfirm;

    @Column(name = "old_code")
    private String oldCode;

    @Column(name = "modifier_id")
    private Long modifierId;
    @Column(name = "modifier")
    private String modifier;
    @Column(name = "gmt_modified")
    private Date gmtModified;


    /**
     * 审查状态  1已审查 0未审查
     */
    @Column(name = "examination")
    private Boolean examination;
    /**
     * 测试遗漏  1为是 0为否
     */
    @Column(name = "test_omission")
    private Boolean testOmission;
    /**
     * 代码缺陷  1为是 0为否
     */
    @Column(name = "code_defect")
    private Boolean codeDefect;

    @Column(name = "test_omission_version")
    private String testOmissionVersion;
    @Column(name = "code_defect_version")
    private String codeDefectVersion;

    @Column(name = "application_type")
    private IssueApplicationType applicationType;

    /**
     * 计划开始时间
     */
    @Column(name = "plan_start_date")
    private Date planStartDate;

    /**
     * 结束开始时间
     */
    @Column(name = "plan_end_date")
    private Date planEndDate;

    @Column(name = "msg_code")
    private String msgCode;

    @Column(name = "is_valid")
    private Boolean isValid;
}