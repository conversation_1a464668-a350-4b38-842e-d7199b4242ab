package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import com.zto.devops.qc.infrastructure.dao.entity.CommentEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring")
public interface CommentEntityConverter {

    CommentEntityConverter INSTANCE = Mappers.getMapper(CommentEntityConverter.class);

    CommentEntity convert(CommentVO vo);

    List<CommentEntity> convert(Collection<CommentVO> vo);

    CommentVO convert(CommentEntity entity);

    List<CommentVO> converter(Collection<CommentEntity> entityList);

}
