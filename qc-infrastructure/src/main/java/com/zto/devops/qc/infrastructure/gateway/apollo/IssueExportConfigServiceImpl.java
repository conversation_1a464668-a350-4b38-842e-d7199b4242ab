package com.zto.devops.qc.infrastructure.gateway.apollo;

import com.zto.devops.qc.domain.gateway.apollo.IssueExportConfigService;
import com.zto.devops.qc.infrastructure.config.IssueExportConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class IssueExportConfigServiceImpl implements IssueExportConfigService {

    @Autowired
    private IssueExportConfig issueExportConfig;

    @Override
    public Integer getExportLimit() {
        return issueExportConfig.getExportLimit();
    }

}
