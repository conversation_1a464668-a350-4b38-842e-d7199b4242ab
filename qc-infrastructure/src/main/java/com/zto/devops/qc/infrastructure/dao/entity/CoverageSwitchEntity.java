package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @create 2022/10/17 13:16
 */
@ToString
@Data
@Table(name = "qc_coverage_switch")
public class CoverageSwitchEntity extends BaseEntity {

    @Column(name = "id")
    private Long id;

    /**
     * 版本编码
     */
    @Column(name = "version_code")
    private String versionCode;

    /**
     * 版本名称
     */
    @Column(name = "version_name")
    private String versionName;

    /**
     * appId
     */
    @Column(name = "app_id")
    private String appId;

}