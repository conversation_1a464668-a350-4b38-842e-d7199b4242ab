package com.zto.devops.qc.infrastructure.gateway.rpc;

import com.alibaba.dubbo.config.annotation.Reference;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.project.client.model.requirement.query.RequirementByCodeListQuery;
import com.zto.devops.project.client.model.requirement.query.WithCodeQuery;
import com.zto.devops.project.client.model.version.query.FindVersionBaseInfoQuery;
import com.zto.devops.project.client.model.version.query.FindVersionQuery;
import com.zto.devops.project.client.service.common.ICommonService;
import com.zto.devops.project.client.service.pmo.IProjectService;
import com.zto.devops.project.client.service.requirement.IRequirementService;
import com.zto.devops.project.client.service.sprint.ISprintService;
import com.zto.devops.project.client.service.version.IVersionService;
import com.zto.devops.qc.client.model.issue.entity.HolidayVO;
import com.zto.devops.qc.client.model.issue.query.FindHolidayQuery;
import com.zto.devops.qc.client.model.rpc.project.*;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.infrastructure.converter.RpcVOConvertor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/4/9
 * @Version 1.0
 */
@Component
public class ProjectRpcServiceImpl implements IProjectRpcService {

    @Reference
    private IRequirementService requirementService;

    @Reference
    private IVersionService versionService;

    @Reference
    private ISprintService sprintService;

    @Reference
    private IProjectService iProjectService;

    @Reference
    private ICommonService commonService;

    @Autowired
    private RpcVOConvertor rpcVOConvertor;

    @Override
    public SimpleListRequirementVO simpleListRequirementQuery(SimpleListRequirementQuery query) {
        com.zto.devops.project.client.model.requirement.entity.SimpleListRequirementVO vo = requirementService.simpleListRequirementQuery(rpcVOConvertor.covertSimpleListRequirementQuery(query));
        SimpleListRequirementVO result = new SimpleListRequirementVO();
        result.setSimpleRequirementVOList(rpcVOConvertor.covertSimpleRequirementVO(vo.getSimpleRequirementVOList()));
        return result;
    }

    @Override
    public SimpleListSprintVO simpleListSprintQuery(SimpleListSprintQuery query) {
        com.zto.devops.project.client.model.sprint.entity.SimpleListSprintVO vo = sprintService.simpleListSprintQuery(rpcVOConvertor.covertSimpleListSprintQuery(query));
        SimpleListSprintVO result = new SimpleListSprintVO();
        result.setSimpleSprintVOList(rpcVOConvertor.covertSimpleSprintVO(vo.getSimpleSprintVOList()));
        return result;
    }

    @Override
    public SimpleListVersionVO simpleVersionListQuery(SimpleVersionListQuery query) {
        com.zto.devops.project.client.model.version.entity.SimpleListVersionVO vo = versionService.simpleVersionListQuery(rpcVOConvertor.covertSimpleVersionListQuery(query));
        SimpleListVersionVO result = new SimpleListVersionVO();
        result.setSimpleVersionVOList(rpcVOConvertor.covertSimpleVersionVO(vo.getSimpleVersionVOList()));
        return result;
    }

    @Override
    public RequirementVO requirementWithCodeQuery(String requirementCode) {
        WithCodeQuery query = new WithCodeQuery();
        query.setCode(requirementCode);
        com.zto.devops.project.client.model.requirement.entity.RequirementVO requirementVO = requirementService.withCodeQuery(query);
        return rpcVOConvertor.convertRequirementVO(requirementVO);
    }

    @Override
    public VersionInfoVO findVersionBaseInfoQuery(String findVersionCode) {
        FindVersionBaseInfoQuery query = new FindVersionBaseInfoQuery(findVersionCode);
        com.zto.devops.project.client.model.version.entity.VersionInfoVO versionInfoVO = requirementService.findVersionBaseInfoQuery(query);
        return rpcVOConvertor.convertVersionInfoVO(versionInfoVO);
    }

    @Override
    public List<HolidayVO> findHoliday(FindHolidayQuery findHolidayQuery) {
        List<com.zto.devops.project.client.model.requirement.entity.HolidayVO> rpcHolidayVOS =
                requirementService.findHoliday(rpcVOConvertor.covertFindHolidayQuery(findHolidayQuery));
        return rpcVOConvertor.covertHolidayVO(rpcHolidayVOS);
    }

    @Override
    public VersionVO findVersionQuery(String code) {
        FindVersionQuery findVersionQuery = new FindVersionQuery(code);
        return rpcVOConvertor.convertVersionVO(versionService.findVersionQuery(findVersionQuery));
    }

    @Override
    public SimpleVersionVO simpleVersionQuery(SimpleVersionQuery simpleVersionQuery) {
        SimpleVersionListQuery query = new SimpleVersionListQuery();
        query.setCode(Collections.singletonList(simpleVersionQuery.getCode()));
        SimpleListVersionVO listVersionVO = this.simpleVersionListQuery(query);
        if (null != listVersionVO && CollectionUtils.isNotEmpty(listVersionVO.getSimpleVersionVOList())) {
            return listVersionVO.getSimpleVersionVOList().get(0);
        }
        return null;
    }

    @Override
    public List<com.zto.devops.qc.client.model.rpc.project.RequirementDetailVo> requirementByCodeListQuery(com.zto.devops.qc.client.model.rpc.project.RequirementByCodeListQuery query) {
        RequirementByCodeListQuery requirementByCodeListQuery = new RequirementByCodeListQuery();
        requirementByCodeListQuery.setCodeList(query.getCodeList());
        return rpcVOConvertor.convertRequirementDetailVo(requirementService.requirementByCodeListQuery(requirementByCodeListQuery));

    }

    @Override
    public Boolean query(Date currentTime) {
        return iProjectService.query(currentTime);
    }

    @Override
    public VersionVO findVersionInfoQuery(String code) {
        com.zto.devops.project.client.model.version.query.FindVersionInfoQuery query = new com.zto.devops.project.client.model.version.query.FindVersionInfoQuery(code);
        return rpcVOConvertor.convertVersionVO(versionService.findVersionInfoQuery(query));
    }

    @Override
    public String getFileUrl(String userName, String remoteFileId) {
        return commonService.getFileUrl(userName, remoteFileId);
    }

    @Override
    public String getInnerFileUrl(String userName, String remoteFileId) {
        return commonService.getInnerFileUrl(userName, remoteFileId);
    }

    @Override
    public List<VersionVO> findListByActualPublishDate(FindVersionByActualPublishDateQuery query) {
        List<com.zto.devops.project.client.model.version.entity.VersionVO> voList = versionService.findListByActualPublishDate(rpcVOConvertor.covertActualPublishDateQuery(query));
        return rpcVOConvertor.convertVersionVOS(voList);
    }

    @Override
    public List<VersionVO> findAllDoingVersionList(List<String> productCodes) {
        List<com.zto.devops.project.client.model.version.entity.VersionVO> voList = versionService.findAllDoingVersionList(productCodes);
        return rpcVOConvertor.convertVersionVOS(voList);
    }

    @Override
    public VersionVO getVersionContainsSubVersions(String versionCode) {
        if (StringUtil.isBlank(versionCode)) {
            return null;
        }
        com.zto.devops.project.client.model.version.query.FindVersionInfoQuery query = new com.zto.devops.project.client.model.version.query.FindVersionInfoQuery(versionCode);
        com.zto.devops.project.client.model.version.entity.VersionVO version = versionService.getVersionContainsSubVersions(query);
        return rpcVOConvertor.convertVersionVO(version);
    }
}

