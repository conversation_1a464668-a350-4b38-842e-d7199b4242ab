package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.RelevantUserTypeHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "qc_relevant_user")
public class RelevantUserEntity extends BaseEntity {

    @Id
    private String code;

    /**
     * 干系人id
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 干系人名称
     */
    @Column(name = "user_name")
    private String userName;

    @Column(name = "handle_type")
    private String handleType;

    /**
     * 干系人类型：1关注人，2抄送人，3当前处理人,4当前处理角色
     */
    @Column(name = "type")
    @ColumnType(typeHandler = RelevantUserTypeHandler.class)
    private RelevantUserTypeEnum type;

    /**
     * 关联对象code
     */
    @Column(name = "business_code")
    private String businessCode;


    /**
     * 所属领域：需求，项目
     */
    @Column(name = "domain")
    @ColumnType(typeHandler = DomainHandler.class)
    private DomainEnum domain;


    @Column(name = "old_object_id")
    private Long oldObjectId;

    @Column(name = "action")
    private String action;

    @Column(name = "access_tag")
    private String accessTag;

}
