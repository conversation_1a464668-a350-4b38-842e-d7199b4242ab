package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "qc_case_execute_result")
public class TmCaseExecuteResultEntity extends BaseEntity {

    /**
     * 报告code
     */
    @Column(name = "report_code")
    private String reportCode;

    /**
     * 用例code
     */
    @Id
    private String code;

    @Column(name = "frog_plan_code")
    private String frogPlanCode;

    @Column(name = "frog_plan_name")
    private String frogPlanName;

    /**
     * 用例总数
     */
    @Column(name = "total_num")
    private Integer totalNum;

    /**
     * 计划数
     */
    @Column(name = "plan_num")
    private Integer planNum;

    /**
     * 通过用例总数
     */
    @Column(name = "pass_num")
    private Integer passNum;

    /**
     * 结果
     */
    @Column(name = "case_result")
    private String caseResult;

    private static final long serialVersionUID = 1L;

}