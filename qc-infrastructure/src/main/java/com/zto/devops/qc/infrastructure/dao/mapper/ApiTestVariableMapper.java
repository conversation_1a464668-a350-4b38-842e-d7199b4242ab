package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import com.zto.devops.qc.infrastructure.dao.entity.ApiTestVariableEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ApiTestVariableMapper extends Mapper<ApiTestVariableEntity> {

    ApiTestVariableEntity getUniqueApiTestVariable(@Param("variableCode") String variableCode, @Param("productCode") String productCode, @Param("linkCode") String linkCode,
                                                   @Param("variableKey") String variableKey);

    List<ApiTestVariableEntity> getApiVariableByProductCode(@Param("productCode") String productCode, @Param("type") String type);

    void batchInsert(@Param("list") List<ApiTestVariableEntity> list);

    ApiTestVariableEntity getUniqueDbVariable(@Param("productCode") String productCode,
                                              @Param("variableKey") String variableKey,
                                              @Param("sceneType") Integer sceneType,
                                              @Param("variableType") VariableTypeEnum variableType,
                                              @Param("businessCode") String businessCode);
}
