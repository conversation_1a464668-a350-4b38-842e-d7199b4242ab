package com.zto.devops.qc.infrastructure.gateway.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.infrastructure.util.HttpClientUtils;
import com.zto.devops.qc.client.service.testmanager.cases.model.ImportDataResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.TestCaseExcelColumn;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.excel.ExcelService;
import com.zto.titans.common.exception.ErrorCodeException;
import com.zto.titans.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ConnectTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 项目名称：qc-parent
 * 类 名 称：ExcelProcess
 * 类 描 述：TODO
 * 创建时间：2021/11/24 1:30 下午
 * 创 建 人：bulecat
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ExcelServiceImpl implements ExcelService {

    @Autowired
    private QcConfigBasicService qcConfig;

    public byte[] getFile(String url) {
        byte[] importExcelBytes = null;
        try {
            if ("2".equals(qcConfig.getQcConfig().getEnv())) {
                url.replace("https://fscdn.zto.com", "http://newfs.ztosys.com");
            }
            log.info("env is {} url is {}", System.getProperty("env"), url);
            // 下载文件
            importExcelBytes = HttpClientUtils.sendFileDownload(url, null, 500, 1000);
        } catch (ConnectTimeoutException e4) {
            log.warn("第一次导入信息失败!解析的数据文件路径[{}]", url, e4);
            // 替换预发的域名重新尝试
            String url2 = url.replace("https://fscdn.zto.com", "http://newfs.ztosys.com");
            log.info("postComplainImportCreate url2==> {}", url2);
            try {
                importExcelBytes = HttpClientUtils.sendFileDownload(url2, null, 1000, 2000);
            } catch (IOException e3) {
                log.error("第二次导入信息失败!解析的数据文件路径url2[{}]", url2, e3);
                throw new ServiceException("导入文件解析失败");
            }
        } catch (IOException e) {
            log.warn("第一次导入信息失败!解析的数据文件路径[{}]", url, e);
        } catch (ErrorCodeException e2) {
            log.warn("第一次导入信息失败!解析的数据文件路径[{}]", url, e2);
            // 替换预发的域名重新尝试
            String url2 = url.replaceAll(".*/dfsproxy/", "http://newfs.ztosys.com/");
            log.info("postComplainImportCreate url2==> {}", url2);
            try {
                importExcelBytes = HttpClientUtils.sendFileDownload(url2, null, 1000, 2000);
            } catch (IOException e3) {
                log.error("第二次导入信息失败!解析的数据文件路径url2[{}]", url2, e3);
                throw new ServiceException("导入文件解析失败");
            }
        }
        return importExcelBytes;
    }

    @Override
    public ImportDataResp excelWriterTestcase(String url) throws ServiceException {
        List<TestCaseExcelColumn> list = new LinkedList<>();
        long start = System.currentTimeMillis();
        byte[] file = getFile(url);
        InputStream stream = new ByteArrayInputStream(file);
        try {
            EasyExcel.read(stream)
                    .head(TestCaseExcelColumn.class)
                    .sheet()
                    .registerReadListener(new AnalysisEventListener<TestCaseExcelColumn>() {
                        @Override
                        public void invoke(TestCaseExcelColumn data, AnalysisContext context) {
                            if (data != null && !data.isBeanNone()) {
                                data.buildExcelData();
                                list.add(data);
                            }
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                            log.info("数据读取完毕");
                        }

                        @Override
                        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                            if (headMap == null) {
                                throw new ServiceException("模板标题为空，请检查导入模板！");
                            }
                            String[] excelArrays = {"用例分组", "用例名称", "用例等级", "用例标签",
                                    "前置条件", "测试步骤", "预期结果", "备注"};
                            List<String> errors = new ArrayList<>();
                            if (headMap.size() < excelArrays.length) {
                                List<String> missingColumns = new ArrayList<>();
                                for (int i = headMap.size(); i < excelArrays.length; i++) {
                                    missingColumns.add(String.format("[%s]（第%d列）", excelArrays[i], i + 1));
                                }
                                errors.add(String.format("缺少%d列：%s",
                                        missingColumns.size(),
                                        String.join("、", missingColumns)));
                            }
                            int maxColumnToCheck = Math.min(headMap.size(), excelArrays.length);
                            List<String> mismatchColumns = new ArrayList<>();
                            for (int i = 0; i < maxColumnToCheck; i++) {
                                String actual = headMap.get(i);
                                if (!Objects.equals(actual, excelArrays[i])) {
                                    mismatchColumns.add(String.format("第%d列应为[%s]（当前值：[%s]）",
                                            i + 1,
                                            excelArrays[i],
                                            actual != null ? actual : "空列"));
                                }
                            }
                            if (!mismatchColumns.isEmpty()) {
                                errors.add("标题不匹配：\n" + String.join("\n", mismatchColumns));
                            }
                            if (!errors.isEmpty()) {
                                String errorMsg = "模板校验失败：\n" + String.join("\n", errors);
                                throw new ServiceException(errorMsg);
                            }
                        }
                    }).doRead();
        } catch (Exception e) {
            if (StringUtil.isNotBlank(e.getMessage())) {
                String errorMsg = e.getMessage().replace("com.zto.devops.framework.client.exception.ServiceException: =", "");
                return ImportDataResp.builder().importFlag(false).errorMsg(errorMsg).build();
            }
        }
        log.info("import file time long" + (System.currentTimeMillis() - start));
        return ImportDataResp.builder().importFlag(true).data(list).build();
    }


}
