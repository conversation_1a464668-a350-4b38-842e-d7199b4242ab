package com.zto.devops.qc.infrastructure.aspect;

import com.zto.devops.qc.client.annotation.ApiSignAuth;
import com.zto.devops.qc.client.exception.ApiSignAuthException;
import com.zto.devops.qc.client.model.auth.ApiKeyInfo;
import com.zto.devops.qc.domain.gateway.auth.ApiKeyService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.infrastructure.auth.SignAuthContext;
import com.zto.devops.qc.infrastructure.auth.SignatureUtils;
import com.zto.devops.qc.infrastructure.config.ApiSignAuthConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * API签名鉴权AOP切面
 * 拦截带有@ApiSignAuth注解的方法进行签名验证
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Slf4j
@Aspect
@Component
public class ApiSignAuthAspect {

    @Autowired
    private ApiKeyService apiKeyService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ApiSignAuthConfig authConfig;

    /**
     * 拦截带有@ApiSignAuth注解的方法
     */
    @Around("@annotation(com.zto.devops.qc.client.annotation.ApiSignAuth)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        ApiSignAuth authAnnotation = method.getAnnotation(ApiSignAuth.class);

        // 检查是否启用鉴权
        if (authAnnotation != null && (!authConfig.isEnabled() || !authAnnotation.enabled())) {
            log.debug("API签名鉴权已禁用，跳过验证");
            return joinPoint.proceed();
        }

        try {
            // 执行签名验证
            validateSignature(authAnnotation, joinPoint);

            if (authConfig.isLogEnabled()) {
                log.info("API签名鉴权通过: method={}", method.getName());
            }

            // 执行目标方法
            return joinPoint.proceed();
        } catch (ApiSignAuthException e) {
            log.error("API签名鉴权失败: method={}, error={}", method.getName(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("API签名鉴权异常: method={}", method.getName(), e);
            throw new ApiSignAuthException("签名鉴权异常", e);
        }
    }

    /**
     * 验证签名
     */
    private void validateSignature(ApiSignAuth authAnnotation, ProceedingJoinPoint joinPoint) {
        // 1. 获取签名上下文
        SignAuthContext context = SignAuthContext.getContext();
        if (context == null) {
            throw new ApiSignAuthException("缺少签名参数");
        }

        // 2. 验证必要参数
        validateRequiredParams(context);

        // 3. 验证时间戳
        validateTimestamp(context, authAnnotation);

        // 4. 验证nonce（防重放）
        validateNonce(context, authAnnotation);

        // 5. 获取密钥信息
        ApiKeyInfo apiKeyInfo = apiKeyService.getApiKeyInfo(context.getAccessKey());
        if (apiKeyInfo == null) {
            throw new ApiSignAuthException("无效的AccessKey");
        }

        if (!apiKeyInfo.getEnabled()) {
            throw new ApiSignAuthException("AccessKey已禁用");
        }

        // 6. 验证签名
        validateSignatureValue(context, apiKeyInfo, authAnnotation, joinPoint);

        // 7. 更新使用统计
        apiKeyService.updateUsageStats(context.getAccessKey());
    }

    /**
     * 验证必要参数
     */
    private void validateRequiredParams(SignAuthContext context) {
        if (StringUtils.isBlank(context.getAccessKey())) {
            throw new ApiSignAuthException("缺少AccessKey");
        }
        if (StringUtils.isBlank(context.getSignature())) {
            throw new ApiSignAuthException("缺少Signature");
        }
        if (StringUtils.isBlank(context.getNonce())) {
            throw new ApiSignAuthException("缺少Nonce");
        }
        if (context.getTimestamp() <= 0) {
            throw new ApiSignAuthException("无效的Timestamp");
        }
    }

    /**
     * 验证时间戳
     */
    private void validateTimestamp(SignAuthContext context, ApiSignAuth authAnnotation) {
        long expireSeconds = authAnnotation.expireSeconds() > 0 ?
                authAnnotation.expireSeconds() : authConfig.getDefaultExpireSeconds();
        long timeSkewSeconds = authAnnotation.timeSkewSeconds() > 0 ?
                authAnnotation.timeSkewSeconds() : authConfig.getDefaultTimeSkewSeconds();

        if (!SignatureUtils.isTimestampValid(context.getTimestamp(), expireSeconds, timeSkewSeconds)) {
            throw new ApiSignAuthException("签名已过期或时间戳无效");
        }
    }

    /**
     * 验证nonce（防重放攻击）
     */
    private void validateNonce(SignAuthContext context, ApiSignAuth authAnnotation) {
        String nonceKey = authConfig.getRedisCachePrefix() + ":nonce:" + context.getNonce();

        // 检查nonce是否已使用
        if (redisService.hasKey(nonceKey)) {
            throw new ApiSignAuthException("重复的请求（Nonce已使用）");
        }

        // 缓存nonce，防止重复使用
        long expireSeconds = Math.max(authAnnotation.expireSeconds(), authConfig.getNonceCacheExpireSeconds());
        redisService.setKey(nonceKey, nonceKey, expireSeconds, TimeUnit.SECONDS);
    }

    /**
     * 验证签名值
     */
    private void validateSignatureValue(SignAuthContext context, ApiKeyInfo apiKeyInfo,
                                        ApiSignAuth authAnnotation, ProceedingJoinPoint joinPoint) {
        Object requestBody = null;

        // 如果需要验证请求体，获取第一个参数作为请求体
        if (authAnnotation.validateBody() && joinPoint.getArgs().length > 0) {
            requestBody = joinPoint.getArgs()[0];
        }

        boolean isValid = SignatureUtils.verifySignature(
                context.getAccessKey(),
                apiKeyInfo.getSecretKey(),
                context.getSignature(),
                context.getHttpMethod(),
                context.getRequestUri(),
                context.getTimestamp(),
                context.getNonce(),
                requestBody
        );

        if (!isValid) {
            throw new ApiSignAuthException("签名验证失败");
        }
    }
}
