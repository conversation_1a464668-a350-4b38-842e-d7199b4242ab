package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import com.zto.devops.qc.client.enums.testmanager.coverage.GenerateTypeEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

/**
 * <AUTHOR>
 * @create 2022/9/16 17:32
 */
@MappedJdbcTypes(value = JdbcType.VARCHAR, includeNullJdbcType = true)
public class GenerateTypeHandler extends BaseEnumTypeHandler<GenerateTypeEnum> {
    public GenerateTypeHandler() {
        super(GenerateTypeEnum.class);
    }
}
