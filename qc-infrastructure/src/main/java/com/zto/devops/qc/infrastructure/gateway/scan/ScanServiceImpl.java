package com.zto.devops.qc.infrastructure.gateway.scan;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.client.enums.testmanager.coverage.MethodTypeEnum;
import com.zto.devops.qc.client.model.dto.MethodDependenceEntityDO;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoveragePublishVO;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.repository.MethodDependenceRepository;
import com.zto.devops.qc.domain.gateway.scan.ScanService;
import com.zto.devops.qc.infrastructure.util.BatchSizeCalculateUtils;
import com.zto.devops.qc.jarscan.struct.analyzer.Method;
import com.zto.devops.qc.jarscan.struct.analyzer.MethodRelate;
import com.zto.devops.qc.jarscan.struct.scan.ScanObject;
import com.zto.devops.qc.jarscan.struct.scan.ScanResult;
import com.zto.devops.qc.jarscan.utils.ScanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springside.modules.utils.collection.MapUtil;

import java.util.*;

@Slf4j
@Service
public class ScanServiceImpl implements ScanService {

    @Autowired
    private MethodDependenceRepository methodDependenceRepository;
    @Autowired
    private QcConfigBasicService configBasicService;

    @Override
    public Boolean staticScanAndSave(com.zto.devops.qc.domain.model.scan.ScanObject object,
                                     CoveragePublishVO coveragePublishVO,
                                     CoverageRecordGenerateParameter parameter) {
        try {
            // 物理删除历史数据
            deleteHistoryData(coveragePublishVO.getVersionCode(), coveragePublishVO.getAppId());
            ScanObject scanObject = new ScanObject();
            BeanUtils.copyProperties(object, scanObject);
            ScanUtils scanUtils = new ScanUtils();
            ScanResult scanResult = scanUtils.staticScan(scanObject);
            saveMethodDependencies(scanResult, coveragePublishVO, parameter);
            return true;
        } catch (Exception e) {
            log.error("staticScanAndSave error.", e);
            return false;
        }
    }

    @Override
    public void batchDeleteHistoryDataByVersionCode(String versionCode) {
        if (StringUtil.isEmpty(versionCode)) {
            return;
        }
        this.deleteHistoryData(versionCode, null);
    }


    public void deleteHistoryData(String versionCode, String appId) {
        // 物理删除历史数据
        try {
            int count;
            do {
                count = methodDependenceRepository.deleteHistoryData(versionCode, appId, configBasicService.getDeleteCount());
                log.info("success delete count {}, versionCode : {}, appId : {}", count, versionCode, appId);
            } while (count > 0);
        } catch (Exception e) {
            log.error("deleteHistoryData error.versionCode : {}, appId : {}", versionCode, appId, e);
        }
    }

    private void saveMethodDependencies(ScanResult scanResult, CoveragePublishVO coveragePublishVO, CoverageRecordGenerateParameter parameter) {
        List<MethodDependenceEntityDO> list = new ArrayList<>();
        for (MethodRelate entryRelate : scanResult.getEntryMethods()) {
            Method method = entryRelate.getMethod();
            String methodName = method.getClassName() + "#" + method.getMethodName() + method.getMethodDesc();
            String currentMethodCode = AggregateIdUtil.generateId(AggregateType.SNOWFLAKE);
            int currentLevel = 1;
            int currentSort = 1;
            MethodDependenceEntityDO entryEntityDO = getMethodDependenceEntityDO(entryRelate, coveragePublishVO, parameter,
                    currentMethodCode, "", currentMethodCode, entryRelate.getMethodType(), currentLevel, currentSort);
            list.add(entryEntityDO);
            getDependence(entryRelate, methodName, list, coveragePublishVO, parameter, currentMethodCode, currentMethodCode, entryRelate.getMethodType(), currentLevel);
        }
        if (CollectionUtil.isEmpty(list)) {
            log.info("saveMethodDependencies list is empty.");
            return;
        }
        batchSaveMethodDependencies(list, String.format("%s_%s", coveragePublishVO.getVersionCode(), coveragePublishVO.getAppId()));
    }

    /**
     * 批量保存方法依赖数据
     */
    private void batchSaveMethodDependencies(List<MethodDependenceEntityDO> list, String tag) {
        if (CollectionUtil.isEmpty(list)) {
            log.info("batchSaveMethodDependencies_批量保存数据为空，跳过处理:{}", tag);
            return;
        }
        long startTime = System.currentTimeMillis();
        int totalCount = list.size();
        log.info("batchSaveMethodDependencies_开始优化批量保存方法依赖数据，tag:{},总数量:{},", tag, totalCount);
        try {
            int averageObjectSize = calculateProportionalSampling(list);
            int optimalBatchSize = BatchSizeCalculateUtils.calculateOptimalBatchSize(totalCount, averageObjectSize);
            List<List<MethodDependenceEntityDO>> batches = Lists.partition(list, optimalBatchSize);
            log.info("batchSaveMethodDependencies_批处理统计 - tag:{}, 总数量:{}, {}条/批次", tag, totalCount, optimalBatchSize);
            processBatchesSequentially(batches, tag);
        } catch (Exception e) {
            log.error("批量保存方法依赖数据失败，tag:{},总数量:{},耗时:{}ms", tag, totalCount, System.currentTimeMillis() - startTime, e);
        }
    }

    /**
     * 按1%比例采样，但不少于10个，不多于100个
     */
    private int calculateProportionalSampling(List<MethodDependenceEntityDO> list) {
        int sampleCount = Math.min(100, Math.max(10, list.size() / 100));

        long total = 0;
        int step = Math.max(1, list.size() / sampleCount);

        int sampled = 0;
        for (int i = 0; i < list.size() && sampled < sampleCount; i += step) {
            total += list.get(i).getEstimatedSize();
            sampled++;
        }
        return sampled > 0 ? (int) (total / sampled) : list.get(0).getEstimatedSize();
    }

    /**
     * 串行处理各个批次
     * 每个批次对应一个独立事务和一次SQL调用
     *
     * @param batches 批次列表
     */
    private void processBatchesSequentially(List<List<MethodDependenceEntityDO>> batches, String tag) {
        if (CollectionUtil.isEmpty(batches)) {
            return;
        }
        int totalBatches = batches.size();
        for (int i = 0; i < totalBatches; i++) {
            List<MethodDependenceEntityDO> batch = batches.get(i);
            int batchIndex = i + 1;
            try {
                methodDependenceRepository.batchSaveInTransaction(batch, batchIndex, totalBatches, tag);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
    }

    private void getDependence(MethodRelate caller, String invokeMethodName, List<MethodDependenceEntityDO> list,
                               CoveragePublishVO coveragePublishVO, CoverageRecordGenerateParameter parameter,
                               String parentMethodCode, String entryMethodCode, String entryMethodType, int methodLevel) {
        if (caller.getCallees().isEmpty()) {
            return;
        }
        int currentLevel = ++methodLevel;
        int currentSort = 1;
        for (MethodRelate callee : caller.getCallees()) {
            // 处理递归方法调用
            String calleeMethodName = callee.getMethod().getClassName() + "#" + callee.getMethod().getMethodName() + callee.getMethod().getMethodDesc();
            if (callee.equals(caller)) {
                continue;
            }
            if (invokeMethodName.contains(calleeMethodName)) {
                continue;
            }
            String currentMethodCode = AggregateIdUtil.generateId(AggregateType.SNOWFLAKE);
            MethodDependenceEntityDO entryEntityDO = getMethodDependenceEntityDO(callee, coveragePublishVO, parameter,
                    currentMethodCode, parentMethodCode, entryMethodCode, entryMethodType, currentLevel, currentSort);
            list.add(entryEntityDO);
            getDependence(callee, invokeMethodName + "->" + calleeMethodName, list, coveragePublishVO,
                    parameter, currentMethodCode, entryMethodCode, entryMethodType, currentLevel);
            currentSort++;
        }
    }

    private static MethodDependenceEntityDO getMethodDependenceEntityDO(MethodRelate entryRelate, CoveragePublishVO coveragePublishVO,
                                                                        CoverageRecordGenerateParameter parameter, String methodCode,
                                                                        String parentMethodCode, String entryMethodCode, String entryMethodType,
                                                                        int methodLevel, int methodSort) {
        MethodDependenceEntityDO entityDO = new MethodDependenceEntityDO();
        entityDO.setMethodCode(methodCode);
        entityDO.setEntryMethodCode(entryMethodCode);
        entityDO.setEntryMethodType(entryMethodType);
        entityDO.setProductCode(parameter.getProductCode());
        entityDO.setProductName(parameter.getProductName());
        entityDO.setAppId(coveragePublishVO.getAppId());
        entityDO.setVersionCode(coveragePublishVO.getVersionCode());
        entityDO.setVersionName(coveragePublishVO.getVersionName());
        entityDO.setCommitId(coveragePublishVO.getCommitId());
        entityDO.setFullClassName(entryRelate.getMethod().getClassName());
        entityDO.setInterfaceFullClassName(entryRelate.getMethod().getInterfaceName());
        entityDO.setMethodName(entryRelate.getMethod().getMethodName());
        entityDO.setMethodDesc(entryRelate.getMethod().getMethodDesc());
        entityDO.setMethodParameterStr();
        entityDO.setMethodType(entryRelate.getMethodType());
        entityDO.setMethodAnnotation(MapUtils.isNotEmpty(entryRelate.getMethodAnnotation()) ? JSON.toJSONString(entryRelate.getMethodAnnotation()) : null);
        entityDO.setParentMethodCode(parentMethodCode);
        entityDO.setMethodLevel(methodLevel);
        entityDO.setMethodSort(methodSort);
        entityDO.setCreator(parameter.getCreator());
        entityDO.setCreatorId(parameter.getCreatorId());
        entityDO.setGmtCreate(Objects.isNull(parameter.getGmtCreate()) ? new Date() : parameter.getGmtCreate());
        if (StringUtil.isNotEmpty(entryRelate.getMethodType())) {
            entityDO.setZcatMetricKey(setZcatMetricKey(entryRelate, entityDO));
        }
        return entityDO;
    }

    private static String setZcatMetricKey(MethodRelate entryRelate, MethodDependenceEntityDO entityDO) {
        String metricKey = "";
        if (MethodTypeEnum.DUBBO.name().equals(entryRelate.getMethodType())) {
            String className = "";
            if (StringUtil.isNotEmpty(entryRelate.getMethod().getInterfaceName())) {
                className = entityDO.getClassName(entryRelate.getMethod().getInterfaceName());
            }
            metricKey = className + "." + entryRelate.getMethod().getMethodName();
        } else if (MethodTypeEnum.HTTP.name().equals(entryRelate.getMethodType())) {
            metricKey = getMethodAnnotation(entryRelate.getMethodAnnotation());
        } else if (MethodTypeEnum.MQ.name().equals(entryRelate.getMethodType())) {
            metricKey = getMethodAnnotation(entryRelate.getMethodAnnotation());
        } else if (MethodTypeEnum.JOB.name().equals(entryRelate.getMethodType())) {
            metricKey = getMethodAnnotation(entryRelate.getMethodAnnotation());
        }
        return metricKey;
    }

    private static String getMethodAnnotation(Map<String, Object> methodAnnotation) {
        if (MapUtil.isEmpty(methodAnnotation)) {
            return "";
        }
        Object value = methodAnnotation.get("value");
        if (Objects.isNull(value)) {
            return "";
        }
        if (value instanceof List) {
            return ((List<String>) value).get(0);
        } else if (value instanceof String) {
            return String.valueOf(methodAnnotation.get("value"));
        } else {
            return "";
        }
    }

}