package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

/**
 * <AUTHOR>
 * @create 2023/5/25 08:40
 */
@MappedJdbcTypes(value = JdbcType.VARCHAR, includeNullJdbcType = true)
public class ApiTestVariableTypeHandler extends BaseEnumTypeHandler<VariableTypeEnum> {
    public ApiTestVariableTypeHandler() {
        super(VariableTypeEnum.class);
    }
}
