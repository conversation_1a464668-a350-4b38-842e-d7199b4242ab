package com.zto.devops.qc.infrastructure.gateway.rpc;

import com.alibaba.dubbo.config.annotation.Reference;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.product.client.enums.MemberTypeEnum;
import com.zto.devops.product.client.model.product.entity.MemberTypesVO;
import com.zto.devops.product.client.model.product.entity.ProductMemberVO;
import com.zto.devops.product.client.model.product.entity.SimpleQueryVO;
import com.zto.devops.product.client.model.product.query.ListProductMemberByPIdQuery;
import com.zto.devops.product.client.model.product.query.MemberTypesQuery;
import com.zto.devops.product.client.model.product.query.SimpleProductQueryList;
import com.zto.devops.product.client.model.product.query.SimpleQuery;
import com.zto.devops.product.client.service.product.IProductService;
import com.zto.devops.qc.client.enums.rpc.ProductRoleEnum;
import com.zto.devops.qc.client.model.rpc.product.PipelineProductVO;
import com.zto.devops.qc.client.model.rpc.product.ProductAllVO;
import com.zto.devops.qc.client.model.rpc.product.ProductVO;
import com.zto.devops.qc.client.model.rpc.product.SimpleProductQueryListVO;
import com.zto.devops.qc.client.model.rpc.product.query.AllProductsQuery;
import com.zto.devops.qc.client.model.rpc.product.query.FindProductByIdQuery;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.infrastructure.converter.RpcVOConvertor;
import com.zto.devops.qc.infrastructure.util.EmptyCheckerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ProductRpcServiceImpl implements IProductRpcService {

    @Reference
    private IProductService productService;

    @Autowired
    private RpcVOConvertor convertor;

    @Override
    public List<ProductRoleEnum> getProductRole(String productCode, Long userId) {
        List<ProductRoleEnum> productRoles = new ArrayList<>();
        if (StringUtils.isEmpty(productCode) || Objects.isNull(userId)) {
            return productRoles;
        }
        MemberTypesQuery query = new MemberTypesQuery(productCode, userId);
        List<MemberTypesVO> vo = productService.memberTypesQuery(query);
        if (CollectionUtils.isEmpty(vo)) {
            return productRoles;
        }
        buildMemberType(productRoles, vo);
        return productRoles;
    }

    @Override
    public int getAllProductRole(String productCode, Long userId) {
        if (StringUtils.isEmpty(productCode) || Objects.isNull(userId)) {
            return 0;
        }
        MemberTypesQuery query = new MemberTypesQuery(productCode, userId);
        List<MemberTypesVO> vo = productService.memberTypesQuery(query);
        return vo.size();
    }

    /**
     * 此方法专给Issue获取产品角色用
     */
    @Override
    public List<ProductRoleEnum> getProductRoleForIssue(String productCode, Long userId) {
        if (StringUtil.isEmpty(productCode) || userId == null) {
            return Collections.emptyList();
        }
        List<ProductRoleEnum> productRoles = new ArrayList<>();
        MemberTypesQuery query = new MemberTypesQuery(productCode, userId);
        List<MemberTypesVO> memberTypesVOList = productService.memberTypesQuery(query);
        if (CollectionUtil.isNotEmpty(memberTypesVOList)) {
            for (MemberTypesVO memberType : memberTypesVOList) {
                if (MemberTypeEnum.DEVELOPER == memberType.getMemberType()) {
                    productRoles.add(ProductRoleEnum.DEVELOPER);
                }
                if (MemberTypeEnum.DEVELOPER_OWNER == memberType.getMemberType()) {
                    productRoles.add(ProductRoleEnum.DEVELOPER_OWNER);
                }
                if (MemberTypeEnum.ARCHITECT == memberType.getMemberType()) {
                    productRoles.add(ProductRoleEnum.ARCHITECT);
                }
                if (MemberTypeEnum.DEVELOPER_M == memberType.getMemberType()) {
                    productRoles.add(ProductRoleEnum.MAJOR_DEVELOPER_OWNER);
                }
                if (MemberTypeEnum.TESTER == memberType.getMemberType()) {
                    productRoles.add(ProductRoleEnum.TESTER);
                }
                if (MemberTypeEnum.TESTER_OWNER == memberType.getMemberType()) {
                    productRoles.add(ProductRoleEnum.TESTER_OWNER);
                }
                if (MemberTypeEnum.TESTER_M == memberType.getMemberType()) {
                    productRoles.add(ProductRoleEnum.MAJOR_TESTER_OWNER);
                }
            }
        }
        return productRoles;
    }

    @Override
    public List<com.zto.devops.qc.client.model.rpc.product.ProductMemberVO> findProductMemberByIdQuery(String productCode, List<String> memberTypes) {
        List<ProductMemberVO> list = new LinkedList<>();
        if (StringUtil.isEmpty(productCode)) {
            return convertor.converterProductMemberVOList(list);
        }
        ListProductMemberByPIdQuery query = new ListProductMemberByPIdQuery();
        query.setProductCode(productCode);
        if (CollectionUtil.isNotEmpty(memberTypes)) {
            query.setMemberTypes(memberTypes);
        }
        try {
            list = productService.listProductMemberByPIdQuery(query);
        } catch (Exception e) {
            log.error("执行产品域ListProductMemberByPIdQuery异常:" + e.getMessage());
        }
        return convertor.converterProductMemberVOList(list);
    }

    @Override
    public List<com.zto.devops.qc.client.model.rpc.product.ProductMemberVO> getProductMember(String productCode, List<String> memberTypes) {
        List<ProductMemberVO> vo = new ArrayList<>();
        try {
            ListProductMemberByPIdQuery query = new ListProductMemberByPIdQuery();
            query.setProductCode(productCode);
            query.setMemberTypes(memberTypes);
            List<ProductMemberVO> memberVOS = productService.listProductMemberByPIdQuery(query);
            vo = memberVOS;
        } catch (Exception e) {
            log.error("query getProductMember is error ,productCode {}  error {}", productCode, e.toString(), e);
        }
        return convertor.converterProductMemberVOList(vo);
    }

    @Override
    public boolean checkProductPermission(Long userId, com.zto.devops.qc.client.model.rpc.product.query.ListProductMemberByPIdQuery productQuery) {
        if (EmptyCheckerUtil.isEmpty(userId)) {
            return false;
        }
        if (EmptyCheckerUtil.isEmpty(productQuery) && EmptyCheckerUtil.isEmpty(productQuery.getProductCode())) {
            return false;
        }
        List<ProductMemberVO> productMembers = null;
        try {
            productMembers = productService.listProductMemberByPIdQuery(convertor.convertProductMemberQuery(productQuery));
        } catch (Exception e) {
            log.error("ListProductMemberByPIdQuery ERROR", e);
        }
        if (EmptyCheckerUtil.isEmpty(productMembers)) {
            return false;
        }
        return productMembers.stream().anyMatch(member -> member.getUserId().equals(userId));

    }

    @Override
    public com.zto.devops.qc.client.model.rpc.product.SimpleQueryVO getProductVO(String productCode) {
        if (StringUtil.isEmpty(productCode)) {
            return null;
        }
        SimpleQuery query = new SimpleQuery();
        query.setProductCode(productCode);
        SimpleQueryVO productVO = productService.simpleQuery(query);
        return convertor.convertSimpleQueryVO(productVO);
    }

    /**
     * 校验产品测试用户权限
     *
     * @param currentUser 当前用户
     * @param productCode 产品code
     * @return true/false
     */
    @Override
    public boolean checkTestUserPermission(User currentUser, String productCode) {
        if (null == currentUser || StringUtils.isBlank(productCode)) {
            return false;
        }
        // 产品的测试负责人、产品的测试人员、超级管理员
        List<String> memberTypes = Arrays.asList(
                MemberTypeEnum.TESTER_OWNER.name(),
                MemberTypeEnum.TESTER_M.name(),
                MemberTypeEnum.TESTER.name(),
                MemberTypeEnum.SUPPER.name());
        List<ProductMemberVO> productMembers = null;
        try {
            ListProductMemberByPIdQuery productQuery = new ListProductMemberByPIdQuery();
            productQuery.setProductCode(productCode);
            productQuery.setMemberTypes(memberTypes);
            productMembers = productService.listProductMemberByPIdQuery(productQuery);
        } catch (Exception e) {
            log.error("ListProductMemberByPIdQuery ERROR", e);
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(productMembers)) {
            return false;
        }
        return productMembers.stream().anyMatch(member -> member.getUserId().equals(currentUser.getUserId()));
    }

    @Override
    public ProductVO findProductByIdQuery(FindProductByIdQuery query) {
        return convertor.convertProductVO(productService.findProductByIdQuery(convertor.coverterFindProductByIdQuery(query)));
    }

    @Override
    public ProductAllVO allProductsQuery(AllProductsQuery query) {
        return convertor.convertProductAllVO(productService.allProductsQuery(convertor.convertAllProductsQuery(query)));
    }

    @Override
    public List<com.zto.devops.qc.client.model.rpc.product.ProductMemberVO> listProductMemberByPIdQuery(com.zto.devops.qc.client.model.rpc.product.query.ListProductMemberByPIdQuery productQuery) {
        List<ProductMemberVO> productMembers = null;
        try {
            productMembers = productService.listProductMemberByPIdQuery(convertor.convertProductMemberQuery(productQuery));
        } catch (Exception e) {
            log.error("ListProductMemberByPIdQuery ERROR", e);
        }
        return convertor.convertProductMemberVO(productMembers);
    }

    private void buildMemberType(List<ProductRoleEnum> productRoles, List<MemberTypesVO> prsoductsVOS) {
        for (MemberTypesVO vo : prsoductsVOS) {
            if (vo.getMemberType().name().equals(MemberTypeEnum.DEVELOPER_M.name()) || vo.getMemberType().name().equals((MemberTypeEnum.DEVELOPER.name()))) {
                //开发权限
                productRoles.add(ProductRoleEnum.DEVELOPER);
            } else if (vo.getMemberType().name().equals(MemberTypeEnum.DEVELOPER_OWNER.name())) {
                //开发负责人
                productRoles.add(ProductRoleEnum.DEVELOPER_OWNER);
            } else if (vo.getMemberType().name().equals(MemberTypeEnum.ARCHITECT.name())) {
                productRoles.add(ProductRoleEnum.ARCHITECT);
            } else if (vo.getMemberType().name().equals(MemberTypeEnum.PROJECTER_M.name()) || vo.getMemberType().name().equals((MemberTypeEnum.PROJECTER.name()))) {
                productRoles.add(ProductRoleEnum.PROJECTER);
            } else if (vo.getMemberType().name().equals(MemberTypeEnum.PROJECTER_OWNER.name())) {
                productRoles.add(ProductRoleEnum.PROJECTER_OWNER);
            } else if (vo.getMemberType().name().equals(MemberTypeEnum.TESTER.name()) || vo.getMemberType().name().equals(MemberTypeEnum.TESTER_M.name())) {
                productRoles.add(ProductRoleEnum.TESTER);
            } else if (vo.getMemberType().name().equals(MemberTypeEnum.TESTER_OWNER.name())) {
                productRoles.add(ProductRoleEnum.TESTER_OWNER);
            } else if (vo.getMemberType().name().equals(MemberTypeEnum.PRODUCTER_M.name()) || vo.getMemberType().name().equals(MemberTypeEnum.PRODUCTER.name())) {
                productRoles.add(ProductRoleEnum.PRODUCT);
            } else if (vo.getMemberType().name().equals(MemberTypeEnum.PRODUCTER_OWNER.name())) {
                productRoles.add(ProductRoleEnum.PRODUCT_OWNER);
            }
        }
    }

    @Override
    public List<Long> getProductUser(String productCode) {
        List<Long> userIds = new ArrayList<>();
        ListProductMemberByPIdQuery query = new ListProductMemberByPIdQuery();
        query.setProductCode(productCode);
        List<ProductMemberVO> productMemberVOS = productService.listProductMemberByPIdQuery(query);
        if (CollectionUtil.isNotEmpty(productMemberVOS)) {
            userIds = productMemberVOS.stream().map(ProductMemberVO::getUserId).collect(Collectors.toList());
        }
        return userIds;
    }

    @Override
    public List<PipelineProductVO> getAllOnlineProduct() {
        com.zto.devops.product.client.model.product.entity.PipelineProductQuery query
                = new com.zto.devops.product.client.model.product.entity.PipelineProductQuery();
        query.setIsAll(Boolean.TRUE);
        return convertor.convertPipelineProductVO(productService.pipelineProductQuery(query));
    }

    @Override
    public SimpleProductQueryListVO simpleProductQueryList(List<String> productCodeList) {
        SimpleProductQueryList query = new SimpleProductQueryList();
        query.setProductCodes(productCodeList);
        return convertor.convertSimpleQueryVO(productService.simpleProductQueryList(query));
    }
}
