package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.framework.infrastructure.dao.BaseMapper;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.PlanCaseVO;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseSortEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TestcaseSortMapper extends BaseMapper<TestcaseSortEntity> {

    /**
     * 查询排序code
     *
     * @param productCode 产品code
     * @param parentCode  上级code
     * @param type        类型
     * @return 排序code
     */
    List<String> selectSortedCodeList(@Param("productCode") String productCode, @Param("parentCode") String parentCode,
                                      @Param("type") TestcaseTypeEnum type);

    /**
     * 删除原有排序
     *
     * @param list 排序节点
     */
    void deleteOldSorted(@Param("list") List<String> list);

    /**
     * 新增排序
     *
     * @param list 排序节点
     */
    void insertNewSorted(@Param("list") List<String> list);

    /**
     * 查询排序列表
     *
     * @param list 分组code
     * @return {@link PlanCaseVO}
     */
    List<PlanCaseVO> selectSortedPlanCaseByCode(@Param("list") List<String> list);
}
