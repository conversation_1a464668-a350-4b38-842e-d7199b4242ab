package com.zto.devops.qc.infrastructure.auth;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Map;
import java.util.TreeMap;

/**
 * API签名工具类
 * 实现基于HMAC-SHA256的请求签名算法
 * 
 * 签名算法：
 * 1. 构造待签名字符串：HTTPMethod + "\n" + RequestURI + "\n" + Timestamp + "\n" + Nonce + "\n" + RequestBodyMD5
 * 2. 使用HMAC-SHA256算法对待签名字符串进行签名
 * 3. 对签名结果进行Base64编码
 * 
 * <AUTHOR>
 * @since 2025-08-14
 */
@Slf4j
public class SignatureUtils {
    
    private static final String HMAC_SHA256 = "HmacSHA256";
    private static final String SEPARATOR = "\n";
    
    /**
     * 生成API签名
     * 
     * @param accessKey 访问密钥ID
     * @param secretKey 访问密钥Secret
     * @param httpMethod HTTP方法（GET、POST等）
     * @param requestUri 请求URI
     * @param timestamp 时间戳（毫秒）
     * @param nonce 随机数
     * @param requestBody 请求体（可为null）
     * @return 签名字符串
     */
    public static String generateSignature(String accessKey, String secretKey, String httpMethod, 
                                         String requestUri, long timestamp, String nonce, Object requestBody) {
        try {
            // 1. 构造待签名字符串
            String stringToSign = buildStringToSign(httpMethod, requestUri, timestamp, nonce, requestBody);
            log.debug("StringToSign: {}", stringToSign);
            
            // 2. 使用HMAC-SHA256进行签名
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKeySpec);
            byte[] signatureBytes = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            
            // 3. Base64编码
            return Base64.getEncoder().encodeToString(signatureBytes);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("生成签名失败", e);
            throw new RuntimeException("生成签名失败", e);
        }
    }
    
    /**
     * 验证API签名
     * 
     * @param accessKey 访问密钥ID
     * @param secretKey 访问密钥Secret
     * @param signature 客户端提供的签名
     * @param httpMethod HTTP方法
     * @param requestUri 请求URI
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param requestBody 请求体
     * @return 签名是否有效
     */
    public static boolean verifySignature(String accessKey, String secretKey, String signature,
                                        String httpMethod, String requestUri, long timestamp, 
                                        String nonce, Object requestBody) {
        try {
            String expectedSignature = generateSignature(accessKey, secretKey, httpMethod, 
                                                       requestUri, timestamp, nonce, requestBody);
            return signature.equals(expectedSignature);
        } catch (Exception e) {
            log.error("验证签名失败", e);
            return false;
        }
    }
    
    /**
     * 构造待签名字符串
     * 格式：HTTPMethod + "\n" + RequestURI + "\n" + Timestamp + "\n" + Nonce + "\n" + RequestBodyMD5
     */
    private static String buildStringToSign(String httpMethod, String requestUri, long timestamp, 
                                          String nonce, Object requestBody) {
        StringBuilder sb = new StringBuilder();
        
        // HTTP方法
        sb.append(StringUtils.upperCase(httpMethod)).append(SEPARATOR);
        
        // 请求URI
        sb.append(requestUri).append(SEPARATOR);
        
        // 时间戳
        sb.append(timestamp).append(SEPARATOR);
        
        // 随机数
        sb.append(nonce).append(SEPARATOR);
        
        // 请求体MD5（如果有请求体）
        if (requestBody != null) {
            String bodyJson = JSON.toJSONString(requestBody);
            String bodyMd5 = DigestUtils.md5Hex(bodyJson);
            sb.append(bodyMd5);
        }
        
        return sb.toString();
    }
    
    /**
     * 生成随机nonce
     * 
     * @return 32位随机字符串
     */
    public static String generateNonce() {
        return DigestUtils.md5Hex(System.nanoTime() + Math.random() + "");
    }
    
    /**
     * 检查时间戳是否在有效范围内
     * 
     * @param timestamp 客户端时间戳
     * @param expireSeconds 过期时间（秒）
     * @param timeSkewSeconds 允许的时间偏差（秒）
     * @return 时间戳是否有效
     */
    public static boolean isTimestampValid(long timestamp, long expireSeconds, long timeSkewSeconds) {
        long currentTime = System.currentTimeMillis();
        long timeDiff = Math.abs(currentTime - timestamp);
        
        // 检查时间偏差
        if (timeDiff > timeSkewSeconds * 1000) {
            log.warn("时间戳偏差过大: current={}, request={}, diff={}ms", currentTime, timestamp, timeDiff);
            return false;
        }
        
        // 检查是否过期
        if (currentTime - timestamp > expireSeconds * 1000) {
            log.warn("签名已过期: current={}, request={}, expire={}s", currentTime, timestamp, expireSeconds);
            return false;
        }
        
        return true;
    }
    
    /**
     * 构造授权头信息
     * 格式：ZTO-HMAC-SHA256 AccessKey=xxx,Signature=xxx,Timestamp=xxx,Nonce=xxx
     */
    public static String buildAuthorizationHeader(String accessKey, String signature, long timestamp, String nonce) {
        return String.format("ZTO-HMAC-SHA256 AccessKey=%s,Signature=%s,Timestamp=%d,Nonce=%s", 
                           accessKey, signature, timestamp, nonce);
    }
    
    /**
     * 解析授权头信息
     * 
     * @param authorization 授权头
     * @return 解析后的参数Map
     */
    public static Map<String, String> parseAuthorizationHeader(String authorization) {
        Map<String, String> params = new TreeMap<>();
        
        if (StringUtils.isBlank(authorization) || !authorization.startsWith("ZTO-HMAC-SHA256 ")) {
            return params;
        }
        
        String paramStr = authorization.substring("ZTO-HMAC-SHA256 ".length());
        String[] pairs = paramStr.split(",");
        
        for (String pair : pairs) {
            String[] kv = pair.split("=", 2);
            if (kv.length == 2) {
                params.put(kv[0].trim(), kv[1].trim());
            }
        }
        
        return params;
    }
}
