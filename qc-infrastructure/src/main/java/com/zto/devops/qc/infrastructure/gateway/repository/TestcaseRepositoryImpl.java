package com.zto.devops.qc.infrastructure.gateway.repository;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.common.util.time.DateUtil;
import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageStatusEnum;
import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseExecuteRecordEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.rpc.project.VersionVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.XmindCaseEditCommand;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.event.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.*;
import com.zto.devops.qc.client.model.testmanager.plan.entity.DeletePlanCaseVO;
import com.zto.devops.qc.domain.gateway.repository.TestcaseRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.infrastructure.converter.AutomaticSourceRecordEntityConverter;
import com.zto.devops.qc.infrastructure.converter.TagVOConverter;
import com.zto.devops.qc.infrastructure.converter.TestcaseEntityConverter;
import com.zto.devops.qc.infrastructure.converter.TestcaseExecuteRecordConverter;
import com.zto.devops.qc.infrastructure.dao.entity.*;
import com.zto.devops.qc.infrastructure.dao.mapper.*;
import com.zto.devops.qc.infrastructure.util.EmptyCheckerUtil;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TestcaseRepositoryImpl implements TestcaseRepository {

    @Autowired
    private TestcaseSortMapper testcaseSortMapper;

    @Autowired
    private TestcaseRelationMapper testcaseRelationMapper;

    @Autowired
    private TestcaseExecuteRecordMapper testcaseExecuteRecordMapper;

    @Autowired
    private IProjectRpcService projectRpcService;

    @Autowired
    private TestcaseEntityConverter testcaseConverter;

    @Autowired
    private TestcaseExecuteRecordConverter testcaseExecuteRecordConverter;

    @Autowired
    private TestcaseMapper testcaseMapper;

    @Autowired
    private TagMapper tagMapper;

    @Autowired
    private TmTestPlanMapper tmTestPlanMapper;

    @Autowired
    private TmTestPlanCaseMapper tmTestPlanCaseMapper;

    @Autowired
    private TestcaseStepMapper testcaseStepMapper;

    @Autowired
    private TagVOConverter tagConverter;

    @Autowired
    private TestHeartCaseMapper testHeartCaseMapper;

    @Autowired
    private AutomaticSourceRecordMapper automaticSourceRecordMapper;

    @Autowired
    private AutomaticSourceRecordEntityConverter automaticSourceRecordEntityConverter;

    @Autowired
    private AutomaticSourceLogMapper automaticSourceLogMapper;

    @Autowired
    private SceneInfoMapper sceneInfoMapper;

    private final static String MOVE_FLAG = "MOVE_FLAG";

    private ExecutorService executorService;

    @Override
    public ExecuteCaseVO getExecuteCase(String caseCode, String automaticTaskCode) {
        TestcaseExecuteRecordEntity entity = new TestcaseExecuteRecordEntity();
        entity.setTestcaseCode(caseCode);
        entity.setAutomaticTaskCode(automaticTaskCode);
        return testcaseExecuteRecordMapper.selectExecuteCase(entity);
    }

    @Override
    public List<TestcaseVO> getTestcaseNameListByPath(List<String> paths) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andIn("code", paths);
        example.selectProperties("code", "name");
        List<TestcaseEntity> pList = testcaseMapper.selectByExample(example);
        return testcaseConverter.converterTestcaseList(pList);
    }

    @Override
    public TestcaseVO findCaseOrModuleByCodeQuery(String code) {
        TestcaseEntity entity = testcaseMapper.selectByPrimaryKey(code);
        if (null == entity || !entity.getEnable()) {
            return null;
        }
        return testcaseConverter.converter(entity);
    }

    @Override
    public List<TestcaseVO> ListCaseByAutomaticCode(String code) {
        return testcaseMapper.selectListAndPlanName(code);
    }

    @Override
    public List<TestcaseVO> findTestcaseByNameQuery(FindTestcaseByNameQuery query) {
        Example example = new Example(TestcaseEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("enable", true)
                .andEqualTo("name", query.getName())
                .andEqualTo("parentCode", query.getParentCode())
                .andEqualTo("productCode", query.getProductCode())
                .andEqualTo("type", query.getType())
                .andEqualTo("attribute", query.getAttribute())
                .andEqualTo("setCore", Boolean.TRUE.equals(query.getSetCore()));
        if (StringUtils.isNotBlank(query.getVersionCode())) {
            criteria.andEqualTo("versionCode", query.getVersionCode());
        }
        List<TestcaseEntity> list = testcaseMapper.selectByExample(example);
        return testcaseConverter.converterTestcaseList(list);
    }

    @Override
    public List<TestcaseEntityDO> getTestcaseNew(ListTestcaseNewQuery query) {
        Example example = new Example(TestcaseEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("setCore", query.getSetCore())
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("productCode", query.getProductCode())
                .andEqualTo("type", query.getType());
        if (StringUtil.isNotBlank(query.getVersionCode())) {
            criteria.andEqualTo("versionCode", query.getVersionCode());
        }
        if (query.getParentCode() != null) {
            criteria.andEqualTo("parentCode", query.getParentCode());
        }

        if (StringUtil.isNotBlank(query.getPath())) {
            criteria.andLike("path", "%" + query.getPath() + "%");
        }

        if (query.getTestcaseAttribute() != null) {
            criteria.andEqualTo("attribute", query.getTestcaseAttribute());
        }
        if (CollectionUtils.isNotEmpty(query.getPriorityList())) {
            criteria.andIn("priority", query.getPriorityList());
        }
        if (CollectionUtils.isNotEmpty(query.getStatusList())) {
            criteria.andIn("status", query.getStatusList());
        }
        if (CollectionUtils.isNotEmpty(query.getTagList())) {
            Example example1 = new Example(TagEntity.class);
            example1.createCriteria()
                    .andEqualTo("enable", Boolean.TRUE)
                    .andEqualTo("domain", DomainEnum.TESTCASE)
                    .andIn("tagName", query.getTagList());
            List<TagEntity> tagList = tagMapper.selectByExample(example1);
            if (CollectionUtils.isEmpty(tagList)) {
                return Collections.emptyList();
            }
            List<String> codeList =
                    tagList.stream().map(TagEntity::getBusinessCode).collect(Collectors.toList());
            criteria.andIn("code", codeList);
        }
        if (CollectionUtils.isNotEmpty(query.getDutyUserList())) {
            criteria.andIn("dutyUserId", query.getDutyUserList());
        }
        if (CollectionUtils.isNotEmpty(query.getCreatorList())) {
            criteria.andIn("creatorId", query.getCreatorList());
        }
        if (null != query.getCreateTimeBegin()) {
            criteria.andGreaterThanOrEqualTo("gmtCreate", DateUtil.getStartTimeOfDay(query.getCreateTimeBegin()));
        }
        if (null != query.getCreateTimeEnd()) {
            criteria.andLessThanOrEqualTo("gmtCreate", DateUtil.getEndTimeOfDay(query.getCreateTimeEnd()));
        }
        if (null != query.getModifyTimeBegin()) {
            criteria.andGreaterThanOrEqualTo("gmtModified", DateUtil.getStartTimeOfDay(query.getModifyTimeBegin()));
        }
        if (null != query.getModifyTimeEnd()) {
            criteria.andLessThanOrEqualTo("gmtModified", DateUtil.getEndTimeOfDay(query.getModifyTimeEnd()));
        }
        if (Boolean.TRUE.equals(query.getPlanPattern())) {
            Example example1 = new Example(TmTestPlanCaseEntity.class);
            example1.createCriteria()
                    .andEqualTo("planCode", query.getPlanCode())
                    .andEqualTo("testStage", query.getTestStage())
                    .andEqualTo("caseType", query.getType());
            List<TmTestPlanCaseEntity> planCaseList = tmTestPlanCaseMapper.selectByExample(example1);
            if (CollectionUtils.isNotEmpty(planCaseList)) {
                List<String> codeList = planCaseList.stream()
                        .map(TmTestPlanCaseEntity::getCaseCode)
                        .collect(Collectors.toList());
                criteria.andNotIn("code", codeList);
            }
        }
        if (StringUtils.isNotBlank(query.getAutomaticSourceCode())) {
            criteria.andEqualTo("automaticSourceCode", query.getAutomaticSourceCode());
        }
        if (CollectionUtils.isNotEmpty(query.getNodeTypeList())) {
            criteria.andIn("nodeType", query.getNodeTypeList());
        }
        if (StringUtils.isNotBlank(query.getCodeOrTitle())) {
            String search = query.getCodeOrTitle().replaceAll("%", "/%").replaceAll("_", "/_");
            query.setCodeOrTitle(search);
            Example.Criteria criteria1 = example.createCriteria()
                    .orLike("code", "%" + query.getCodeOrTitle() + "%")
                    .orLike("name", "%" + query.getCodeOrTitle() + "%");
            example.and(criteria1);
        }
        example.orderBy("sort").orderBy("gmtCreate");
        return testcaseConverter.covertList(testcaseMapper.selectByExample(example));
    }

    @Override
    public List<TestcaseEntityDO> getCheckTestCaseList(ListTestcaseExpQuery query) {
        if (CollectionUtils.isEmpty(query.getCheckCodeList())) {
            throw new ServiceException("未勾选数据，请重新勾选后再导出");
        }
        Example example = new Example(TestcaseEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("productCode", query.getProductCode())
                .andEqualTo("setCore", query.getSetCore())
                .andEqualTo("type", query.getType())
                .andIn("code", query.getCheckCodeList());
        if (query.getTestcaseAttribute() != null) {
            criteria.andEqualTo("attribute", query.getTestcaseAttribute());
        }
        if (StringUtil.isNotBlank(query.getVersionCode())) {
            criteria.andEqualTo("versionCode", query.getVersionCode());
        }
        example.orderBy("sort").orderBy("gmtCreate");
        return testcaseConverter.covertList(testcaseMapper.selectByExample(example));
    }

    @Override
    public List<TestcaseEntityDO> simpleTestcaseModuleQuery(SimpleTestcaseModuleQuery query) {
        log.info("SimpleTestcaseModuleQuery >>> {}", JSON.toJSONString(query));
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("productCode", query.getProductCode())
                .andEqualTo("versionCode", query.getVersionCode())
                .andEqualTo("type", query.getType())
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE)
                .andEqualTo("versionCode", query.getVersionCode())
                .andEqualTo("setCore", query.getSetCore());
        example.orderBy("sort").asc().orderBy("gmtCreate").asc();
        return testcaseConverter.covertList(testcaseMapper.selectByExample(example));
    }

    @Override
    public List<TestcaseExecuteNumVO> selectExecuteNumByTestcaseCodeList(List<String> codeList) {
        return testcaseExecuteRecordMapper.selectExecuteNumByTestcaseCodeList(codeList);
    }

    @Override
    public List<TagVO> getTagsByCodeList(List<String> codeList) {
        Example example = new Example(TagEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE).andIn("businessCode", codeList);
        List<TagEntity> tagList = tagMapper.selectByExample(example);
        return tagConverter.convert(tagList);
    }

    @Override
    public void relatedCaseExecutionStatus(List<ListTestcaseVO> testcaseList, PageTestcaseQuery query) {
        Example example = new Example(TmTestPlanCaseEntity.class);
        example.createCriteria()
                .andEqualTo("planCode", query.getPlanCode())
                .andEqualTo("testStage", query.getTestStage())
                .andEqualTo("caseType", query.getType())
                .andEqualTo("enable", true);
        List<TmTestPlanCaseEntity> planCaseList = tmTestPlanCaseMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(planCaseList)) {
            Map<String, TestPlanCaseStatusEnum> caseMap = planCaseList.stream()
                    .collect(Collectors.toMap(TmTestPlanCaseEntity::getCaseCode, TmTestPlanCaseEntity::getStatus));
            testcaseList.stream().forEach(testcase -> {
                testcase.setExecutionStatus(caseMap.get(testcase.getCode()));
                testcase.setExecutionStatusDesc(caseMap.get(testcase.getCode()) != null ?
                        caseMap.get(testcase.getCode()).getValue() : "");
            });
        }
    }

    @Override
    public List<String> getParentCodeList(List<String> codeList) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andIn("parentCode", codeList);
        List<TestcaseEntity> entityList = testcaseMapper.selectByExample(example);
        // 过滤拿到有下级节点的parentCode
        return entityList.stream().map(TestcaseEntity::getParentCode)
                .distinct().collect(Collectors.toList());
    }

    @Override
    public TestcaseEntityDO getTestcaseVO(PageXmindDetailQuery query) {
        Example caseExample = new Example(TestcaseEntity.class);
        caseExample.createCriteria().andEqualTo("enable", Boolean.TRUE).andEqualTo("code", query.getCaseCode());
        TestcaseEntity testcaseEntity = testcaseMapper.selectOneByExample(caseExample);
        return testcaseConverter.covert(testcaseEntity);
    }

    @Override
    public TestcaseVO getTestcaseVOByCodeQuery(FindTestcaseEntityByCodeQuery query) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("code", query.getCode()).andEqualTo("enable", true);
        TestcaseEntity entity = testcaseMapper.selectOneByExample(example);
        return testcaseConverter.converter(entity);
    }

    @Override
    public void updateByCodeList(TestcaseEntityDO entity, List<String> caseCodeList) {
        Example updateExample = new Example(TestcaseEntity.class);
        updateExample.createCriteria().andIn("code", caseCodeList);
        testcaseMapper.updateByExampleSelective(testcaseConverter.covert2Entity(entity), updateExample);
    }

    @Override
    public String selectTestcaseModulePath(FindTestcaseModulePathQuery query) {
        return testcaseMapper.selectTestcaseModulePath(query);
    }

    @Override
    public int selectModuleCountByName(XmindCaseEditCommand command, String name) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("productCode", command.getProductCode())
                .andEqualTo("type", TestcaseTypeEnum.MANUAL)
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE)
                .andEqualTo("name", name)
                .andEqualTo("versionCode", command.getVersionCode())
                .andEqualTo("parentCode",
                        TestcaseGroupTypeEnum.ALL.name().equals(command.getParentCode()) ? "" : command.getParentCode());
        return testcaseMapper.selectCountByExample(example);
    }

    @Override
    public List<ListTestcaseVO> selectTestCaseModule(TestcaseQuery query) {
        return testcaseMapper.selectTestCaseModule(query);
    }

    @Override
    public TestcaseEntityDO selectTestcaseByCode(String caseCode) {
        Example caseExample = new Example(TestcaseEntity.class);
        caseExample.createCriteria().andEqualTo("code", caseCode)
                .andEqualTo("enable", Boolean.TRUE);
        return testcaseConverter.covert(testcaseMapper.selectOneByExample(caseExample));
    }

    @Override
    public List<TestcaseEntityDO> selectTestcaseByCodeList(List<String> caseCodes) {
        // 获取case的类型
        Example caseExample = new Example(TestcaseEntity.class);
        caseExample.createCriteria()
                .andIn("code", caseCodes)
                .andEqualTo("status", TestcaseStatusEnum.NORMAL);
        return testcaseConverter.covertList(testcaseMapper.selectByExample(caseExample));
    }

    @Override
    public void updateTestcaseByCodeList(List<String> codes, TestcaseEntityDO entity) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andIn("code", codes);
        testcaseMapper.updateByExampleSelective(testcaseConverter.covert2Entity(entity), example);
    }

    @Override
    public TestcaseEntityDO selectProductCodeAndVersionCodeByCaseCode(String caseCodes) {
        Example simpleCaseExample = new Example(TestcaseEntity.class);
        simpleCaseExample.selectProperties("productCode", "versionCode");
        simpleCaseExample.createCriteria().andEqualTo("code", caseCodes);
        return testcaseConverter.covert(testcaseMapper.selectOneByExample(simpleCaseExample));
    }

    @Override
    public List<TestcaseParentInfoVO> selectGroupByCaseCodeList(List<String> caseCodeList, String productCode, String versionCode, String type, Boolean setCore) {
        return testcaseMapper.selectGroupByCaseCodeList(caseCodeList, productCode,
                versionCode, type, Boolean.FALSE);
    }

    @Override
    public TestcaseGroupVO buildSingleModule(String parentCode, String parentName, String targetVersionCode,
                                             String productCode, User operator) {
        TestcaseGroupVO result = new TestcaseGroupVO();
        //未分组用例
        if (StringUtil.isBlank(parentCode)) {
            result.setParentName("");
            result.setParentPath("");
            result.setParentCode("");
            return result;
        }

        //复用目标版本，同名分组
        Example example = new Example(TestcaseEntity.class);
        example.selectProperties("code", "name");
        example.createCriteria().andEqualTo("name", parentName)
                .andEqualTo("path", "")
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("type", TestcaseTypeEnum.MANUAL)
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE)
                .andEqualTo("versionCode", targetVersionCode)
                .andEqualTo("productCode", productCode);
        TestcaseEntity entity = testcaseMapper.selectOneByExample(example);
        if (null != entity) {
            result.setParentName(entity.getName());
            result.setParentCode(entity.getCode());
            result.setParentPath(entity.getCode());
            return result;
        }

        //新增分组
        TestcaseEntity toSaveEntity = buildNewModule(targetVersionCode, "", "", parentCode, operator);
        testcaseMapper.insertSelective(toSaveEntity);
        result.setParentName(toSaveEntity.getName());
        result.setParentCode(toSaveEntity.getCode());
        result.setParentPath(toSaveEntity.getCode());
        return result;
    }


    /**
     * 组装多级分组信息
     *
     * @param parentName        末级分组名
     * @param sourceVersionCode 原版本code
     * @param targetVersionCode 目标版本code
     * @param productCode       产品code
     * @param operator          操作人
     * @return {@link TestcaseGroupVO}
     */
    @Override
    public TestcaseGroupVO buildComplexModule(String parentCode,
                                              String parentName,
                                              String sourceVersionCode,
                                              String targetVersionCode,
                                              String productCode,
                                              User operator) {

        TestcaseGroupVO result = new TestcaseGroupVO();
        //查询分组明细
        List<SameNameModuleVO> sourceList = testcaseMapper.selectSameNameModule(parentCode, parentName, sourceVersionCode, productCode);
        if (CollectionUtil.isEmpty(sourceList)) {
            throw new ServiceException("源用例分组不存在，分组名： " + parentName + ", 源版本号：" + sourceVersionCode + "");
        }

        //源版本分组 匹配 目标版本分组
        String finalPath = "";
        String finalParentCode = "";
        String finalParentName = "";
        Integer diffIndex = -1;
        for (int i = 0; i < sourceList.size(); i++) {
            SameNameModuleVO source = sourceList.get(i);
            String previousName = (i == 0) ? "" : sourceList.get(i - 1).getParentModuleName();
            SameNameModuleVO target = testcaseMapper.selectModuleByNameAndPath(source.getParentModuleName(), previousName,
                    source.getPathLength(), targetVersionCode, productCode);
            //目标分组为空
            if (target == null) {
                diffIndex = i;
                break;
            } else {
                //目标分组匹配
                finalParentCode = target.getModuleCode();
                finalParentName = target.getModuleName();
                finalPath += StringUtil.isBlank(finalPath) ? target.getModuleCode() : ("." + target.getModuleCode());
            }
        }

        //新增不匹配分组
        List<TestcaseEntity> toSaveModuleList = buildNewModuleList(diffIndex, sourceList, finalParentCode, finalPath, targetVersionCode, operator);
        if (CollectionUtil.isNotEmpty(toSaveModuleList)) {
            testcaseMapper.batchInsertCase(toSaveModuleList);
            TestcaseEntity lastModule = toSaveModuleList.get(toSaveModuleList.size() - 1);
            finalParentCode = lastModule.getCode();
            finalParentName = lastModule.getName();
            finalPath = StringUtil.isBlank(lastModule.getPath()) ? lastModule.getCode() : lastModule.getPath() + "." + lastModule.getCode();
        }

        result.setParentPath(finalPath);
        result.setParentCode(finalParentCode);
        result.setParentName(finalParentName);
        return result;
    }


    @Override
    public TestcaseGroupVO buildSingleModuleForSetCore(String sourceModuleCode,
                                                       String parentCode,
                                                       String parentPath,
                                                       User operator) {
        TestcaseEntity originalEntity = testcaseMapper.selectByPrimaryKey(sourceModuleCode);
        if (null == originalEntity) {
            log.error(sourceModuleCode + "_buildSingleModuleForSetCore_originalEntity_is_null!");
            return null;
        }

        //核心用例是否有同名分组
        TestcaseEntity sameModule = testcaseMapper.selectOneByModuleNameAndParentCode(originalEntity.getName(), parentCode);
        if (null != sameModule) {
            TestcaseGroupVO result = new TestcaseGroupVO();
            result.setParentName(sameModule.getName());
            result.setParentCode(sameModule.getCode());
            result.setParentPath((StringUtils.isNotBlank(sameModule.getPath()) ? sameModule.getPath() + "." : "") + sameModule.getCode());
            return result;
        }

        //新增分组
        TestcaseEntity toSaveEntity = buildNewModuleForSetCore(sourceModuleCode, parentCode, parentPath, operator, originalEntity);
        testcaseMapper.insertSelective(toSaveEntity);
        TestcaseGroupVO result = new TestcaseGroupVO();
        result.setParentName(toSaveEntity.getName());
        result.setParentCode(toSaveEntity.getCode());
        result.setParentPath((StringUtils.isNotBlank(toSaveEntity.getPath()) ? toSaveEntity.getPath() + "." : "") + toSaveEntity.getCode());
        return result;
    }

    @Override
    public TestcaseGroupVO buildComplexModuleForSetCore(String versionModuleCode,
                                                        String versionModuleName,
                                                        String parentCode,
                                                        String parentName,
                                                        String sourceVersionCode,
                                                        String productCode,
                                                        User operator) {
        //查询分组明细
        List<SameNameModuleVO> sourceList = testcaseMapper.selectSameNameModule(parentCode, parentName, sourceVersionCode, productCode);
        if (CollectionUtil.isEmpty(sourceList)) {
            throw new ServiceException("源用例分组不存在，分组名： " + parentName + ", 源版本号：" + sourceVersionCode + "");
        }

        //源版本分组 匹配 目标版本分组
        String finalPath = versionModuleCode;
        String finalParentCode = versionModuleCode;
        String finalParentName = "";
        Integer diffIndex = -1;
        for (int i = 0; i < sourceList.size(); i++) {
            SameNameModuleVO source = sourceList.get(i);
            String previousName = (i == 0) ? versionModuleName : sourceList.get(i - 1).getParentModuleName();
            Integer pathLength = source.getPathLength() == 0 ? 14 : source.getPathLength() + 15;
            SameNameModuleVO target = testcaseMapper.selectCoreModuleByNameAndPath(versionModuleCode,
                    source.getParentModuleName(),
                    previousName,
                    pathLength,
                    productCode);
            //目标分组为空
            if (target == null) {
                diffIndex = i;
                break;
            } else {
                //目标分组匹配
                finalParentCode = target.getModuleCode();
                finalParentName = target.getModuleName();
                finalPath += StringUtil.isBlank(finalPath) ? target.getModuleCode() : ("." + target.getModuleCode());
            }
        }

        //新增不匹配分组
        List<TestcaseEntity> toSaveModuleList = buildNewModuleListForSetCore(diffIndex, sourceList, finalParentCode, finalPath, operator);
        if (CollectionUtil.isNotEmpty(toSaveModuleList)) {
            testcaseMapper.batchInsertCase(toSaveModuleList);
            TestcaseEntity lastModule = toSaveModuleList.get(toSaveModuleList.size() - 1);
            finalParentCode = lastModule.getCode();
            finalParentName = lastModule.getName();
            finalPath = StringUtil.isBlank(lastModule.getPath()) ? lastModule.getCode() : lastModule.getPath() + "." + lastModule.getCode();
        }

        TestcaseGroupVO result = new TestcaseGroupVO();
        result.setParentPath(finalPath);
        result.setParentCode(finalParentCode);
        result.setParentName(finalParentName);
        return result;
    }

    @Override
    public Set<String> getExistCaseNameList(String productCode, String targetParentCode, String versionCode, Boolean setCore) {
        Example moduleCase = new Example(TestcaseEntity.class);
        moduleCase.selectProperties("name");
        moduleCase.createCriteria()
                .andEqualTo("productCode", productCode)
                .andEqualTo("versionCode", versionCode)
                .andEqualTo("parentCode", targetParentCode)
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE)
                .andEqualTo("setCore", setCore)
                .andEqualTo("enable", Boolean.TRUE);
        List<TestcaseEntity> cases = testcaseMapper.selectByExample(moduleCase);
        Set<String> existNames = cases.stream().map(TestcaseEntity::getName).collect(Collectors.toSet());
        return existNames;
    }

    @Override
    public List<String> filterCodeByName(List<String> caseCodes, Set<String> existNames, String action, Boolean setCore, String productCode, String sourceVersionCode) {
        // 获取要移动用例的名称
        Example example = new Example(TestcaseEntity.class);
        example.selectProperties("code", "name");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("productCode", productCode)
                .andEqualTo("setCore", setCore)
                .andIn("code", caseCodes)
                .andEqualTo("enable", 1)
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE);
        if (null == setCore || !setCore) {
            //核心用例不区分版本
            criteria.andEqualTo("versionCode", sourceVersionCode);
        }
        List<TestcaseEntity> cases = testcaseMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(cases)) {
            return Collections.emptyList();
        }
        // 有重复名称的用例剔除掉
        return cases.stream()
                .filter(tc -> {
                    String name = MOVE_FLAG.equals(action) ? tc.getName() : "【副本】" + tc.getName();
                    return existNames == null || !existNames.contains(name);
                }).map(TestcaseEntity::getCode).collect(Collectors.toList());
    }

    @Override
    public void updateTestcaseByFilteredCodes(TestcaseEntityDO entityDO, List<String> filteredCodes) {
        Example updateExample = new Example(TestcaseEntity.class);
        updateExample.createCriteria().andIn("code", filteredCodes);
        testcaseMapper.updateByExampleSelective(testcaseConverter.covert2Entity(entityDO), updateExample);
    }

    @Override
    public String getTargetPath(String targetParentCode, Boolean setCore, TestcaseTypeEnum typeEnum) {
        if (StringUtil.isBlank(targetParentCode)) {
            return "";
        }
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andEqualTo("code", targetParentCode)
                .andEqualTo("type", typeEnum)
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE)
                .andEqualTo("setCore", setCore)
                .andEqualTo("enable", 1);
        TestcaseEntity entity = testcaseMapper.selectOneByExample(example);
        if (entity == null) {
            throw new ServiceException("目标组不存在");
        }
        return StringUtil.isEmpty(entity.getPath()) ? targetParentCode : entity.getPath() + "." + targetParentCode;
    }

    @Override
    public TestcaseEntityDO selectModuleByParentCodeAndVersionCode(String parentCode, String versionCode, TestcaseAttributeEnum testcaseAttributeEnum, Boolean setCore) {
        Example example = new Example(TestcaseEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("code", parentCode)
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE)
                .andEqualTo("setCore", setCore)
                .andEqualTo("enable", 1);
        if (null == setCore || !setCore) {
            //核心用例不区分版本
            criteria.andEqualTo("versionCode", versionCode);
        }
        return testcaseConverter.covert(testcaseMapper.selectOneByExample(example));
    }

    @Override
    public List<TestcaseEntityDO> selectNewModuleCaseName(String parentCode, TestcaseAttributeEnum testcaseAttributeEnum, Boolean setCore, String productCode, String versionCode) {
        Example moduleCase = new Example(TestcaseEntity.class);
        moduleCase.selectProperties("name");
        Example.Criteria criteria = moduleCase.createCriteria();
        criteria.andEqualTo("parentCode", parentCode)
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE)
                .andEqualTo("setCore", setCore)
                .andEqualTo("enable", 1)
                .andEqualTo("productCode", productCode);
        if (null == setCore || !setCore) {
            //核心用例不区分版本
            criteria.andEqualTo("versionCode", versionCode);
        }
        return testcaseConverter.covertList(testcaseMapper.selectByExample(moduleCase));
    }

    @Override
    public List<TestcaseEntityDO> selectTestcaseByCodesList(List<String> filteredCodes) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andIn("code", filteredCodes)
                .andEqualTo("enable", true)
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE);
        return testcaseConverter.covertList(testcaseMapper.selectByExample(example));
    }

    @Override
    public void batchUpdateDutyUser(List<String> codes, User dutyUser, User operator) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andIn("code", codes);
        TestcaseEntity entity = new TestcaseEntity();
        entity.setDutyUser(dutyUser.getUserName());
        entity.setDutyUserId(dutyUser.getUserId());
        fillUserAndDate(entity, operator);
        testcaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void doBatchUpdateCaseGrade(List<String> codes, TestcasePriorityEnum grade, User operator) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andIn("code", codes);
        TestcaseEntity entity = new TestcaseEntity();
        entity.setPriority(grade);
        entity.setModifier(operator.getUserName());
        entity.setModifierId(operator.getUserId());
        fillUserAndDate(entity, operator);
        testcaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<String> selectDistinctCaseNameByCodeList(List<String> caseCodeList, Boolean setCore) {
        return testcaseMapper.selectDistinctCaseNameByCodeList(caseCodeList, Boolean.FALSE);
    }

    /**
     * 更新成员数据
     *
     * @param entity
     * @param operator
     */
    private void fillUserAndDate(BaseEntity entity, User operator) {
        if (entity == null) {
            return;
        }
        if (operator != null) {
            entity.setModifier(operator.getUserName());
            entity.setModifierId(operator.getUserId());
        }
        entity.setGmtModified(new Date());
    }

    /**
     * 组装新分组集合
     *
     * @param diffIndex         不匹配下标
     * @param sourceList        原用例分组
     * @param finalParentCode   最终parentCode
     * @param finalPath         最终路径
     * @param targetVersionCode 目标版本
     * @param operator          操作人
     * @return {@link TestcaseEntity}
     */
    private List<TestcaseEntity> buildNewModuleList(Integer diffIndex, List<SameNameModuleVO> sourceList, String
            finalParentCode, String finalPath, String targetVersionCode, User operator) {
        if (diffIndex < 0 || CollectionUtil.isEmpty(sourceList)) {
            return null;
        }
        List<TestcaseEntity> toSaveModuleList = new ArrayList<>();
        for (int i = diffIndex; i < sourceList.size(); i++) {
            SameNameModuleVO source = sourceList.get(i);
            TestcaseEntity entity = buildNewModule(targetVersionCode, finalParentCode, finalPath, source.getParentModuleCode(), operator);
            toSaveModuleList.add(entity);
            finalPath += StringUtil.isBlank(finalPath) ? entity.getCode() : ("." + entity.getCode());
            finalParentCode = entity.getCode();
        }
        return toSaveModuleList;
    }

    private List<TestcaseEntity> buildNewModuleListForSetCore(Integer diffIndex,
                                                              List<SameNameModuleVO> sourceList,
                                                              String finalParentCode,
                                                              String finalPath,
                                                              User operator) {
        if (diffIndex < 0 || CollectionUtil.isEmpty(sourceList)) {
            return null;
        }
        List<TestcaseEntity> toSaveModuleList = new ArrayList<>();
        for (int i = diffIndex; i < sourceList.size(); i++) {
            SameNameModuleVO source = sourceList.get(i);
            TestcaseEntity entity = buildNewModuleForSetCore(source.getParentModuleCode(), finalParentCode, finalPath, operator, null);
            toSaveModuleList.add(entity);
            finalPath += StringUtil.isBlank(finalPath) ? entity.getCode() : ("." + entity.getCode());
            finalParentCode = entity.getCode();
        }
        return toSaveModuleList;
    }

    /**
     * 组装新测试用例分组
     *
     * @param targetVersionCode
     * @param targetParent
     * @param targetPath
     * @param sourceModuleCode
     */
    private TestcaseEntity buildNewModule(String targetVersionCode, String targetParent, String targetPath, String
            sourceModuleCode, User operator) {
        TestcaseEntity originalEntity = testcaseMapper.selectByPrimaryKey(sourceModuleCode);
        TestcaseEntity toSaveEntity = new TestcaseEntity();
        BeanUtils.copyProperties(originalEntity, toSaveEntity);
        toSaveEntity.setId(null);
        toSaveEntity.setCode(AggregateIdUtil.generateId(AggregateType.TEST_CASE));
        toSaveEntity.setParentCode(targetParent);
        toSaveEntity.setPath(targetPath);
        toSaveEntity.setVersionCode(targetVersionCode);
        toSaveEntity.setDutyUser(operator.getUserName());
        toSaveEntity.setDutyUserId(operator.getUserId());
        toSaveEntity.setGmtCreate(new Date());
        toSaveEntity.setCreator(operator.getUserName());
        toSaveEntity.setCreatorId(operator.getUserId());
        toSaveEntity.setGmtModified(new Date());
        toSaveEntity.setModifier(operator.getUserName());
        toSaveEntity.setModifierId(operator.getUserId());
        return toSaveEntity;
    }

    private TestcaseEntity buildNewModuleForSetCore(String sourceModuleCode,
                                                    String targetParentCode,
                                                    String targetPath,
                                                    User operator,
                                                    TestcaseEntity originalEntity) {
        if (null == originalEntity) {
            originalEntity = testcaseMapper.selectByPrimaryKey(sourceModuleCode);
        }
        TestcaseEntity toSaveEntity = new TestcaseEntity();
        if (null != originalEntity) {
            BeanUtils.copyProperties(originalEntity, toSaveEntity);
        }
        toSaveEntity.setId(null);
        toSaveEntity.setCode(AggregateIdUtil.generateId(AggregateType.TEST_CASE));
        toSaveEntity.setParentCode(targetParentCode);
        toSaveEntity.setPath(targetPath);
        toSaveEntity.setSetCore(Boolean.TRUE);
        toSaveEntity.setGmtCreate(new Date());
        toSaveEntity.setGmtModified(new Date());
        toSaveEntity.setDutyUser(operator.getUserName());
        toSaveEntity.setDutyUserId(operator.getUserId());
        toSaveEntity.setCreator(operator.getUserName());
        toSaveEntity.setCreatorId(operator.getUserId());
        toSaveEntity.setModifier(operator.getUserName());
        toSaveEntity.setModifierId(operator.getUserId());
        return toSaveEntity;
    }


    @Override
    public TestcaseEntityDO loadFormDb(String caseCode) {
        TestcaseEntity entity = testcaseMapper.selectByPrimaryKey(caseCode);
        return testcaseConverter.covert(entity);
    }

    @Override
    public List<SimpleTestcaseVO> selectList(ListTestcaseCodeQuery query) {
        return testcaseMapper.selectTestcaseCodeList(query);
    }

    @Override
    public TestcaseEntityDO selectModule(String parentCode, String versionCode, Boolean setCore) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE)
                .andEqualTo("automaticSourceCode", StringUtils.EMPTY)
                .andEqualTo("code", parentCode)
                .andEqualTo("versionCode", versionCode)
                .andEqualTo("setCore", setCore);
        example.selectProperties("path");
        TestcaseEntity entity = testcaseMapper.selectOneByExample(example);
        return testcaseConverter.covert(entity);
    }

    @Override
    public int countCaseByCaseName(String productCode, String versionCode, String name,
                                   String parentCode, TestcaseTypeEnum testcaseType, Boolean setCore) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("productCode", productCode)
                .andEqualTo("versionCode", versionCode)
                .andEqualTo("setCore", setCore)
                .andEqualTo("type", TestcaseTypeEnum.MANUAL)
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE)
                .andEqualTo("name", name)
                .andEqualTo("parentCode", parentCode);
        return testcaseMapper.selectCountByExample(example);
    }

    @Override
    public void updatePath(TestcaseEntityDO entityDO) {
        TestcaseEntity entity = testcaseConverter.covert2Entity(entityDO);
        testcaseMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public TestcaseEntityDO findTargetModule(String targetParentCode) {
        Example findTargetModuleExample = new Example(TestcaseEntity.class);
        findTargetModuleExample.createCriteria()
                .andEqualTo("code", targetParentCode)
                .andEqualTo("setCore", Boolean.TRUE)
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE);
        return testcaseConverter.covert(testcaseMapper.selectOneByExample(findTargetModuleExample));
    }

    @Override
    public List<TestcaseEntityDO> findTargetModuleCase(String targetParentCode, String productCode) {
        // 获取目标分组下核心用例的名称
        Example findTargetModuleCaseExample = new Example(TestcaseEntity.class);
        findTargetModuleCaseExample.selectProperties("name", "code");
        findTargetModuleCaseExample.createCriteria()
                .andEqualTo("parentCode", targetParentCode)
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE)
                .andEqualTo("setCore", Boolean.TRUE)
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("productCode", productCode);
        return testcaseConverter.covertList(testcaseMapper.selectByExample(findTargetModuleCaseExample));
    }

    @Override
    public void changePlanCaseExecutor(PlanCaseExecutorChangedEvent event) {
        Example example = new Example(TmTestPlanCaseEntity.class);
        example.createCriteria().andEqualTo("planCode", event.getPlanCode())
                .andEqualTo("testStage", event.getTestStage())
                .andEqualTo("caseCode", event.getCaseCode());
        TmTestPlanCaseEntity entity = new TmTestPlanCaseEntity();
        entity.setExecutor(event.getExecutor());
        entity.setExecutorId(event.getExecutorId());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setGmtModified(event.getOccurred());
        tmTestPlanCaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void changeTestcaseDutyUser(TestcaseDutyUserChangedEvent event) {
        TestcaseEntity entity = testcaseConverter.converter(event);
        testcaseMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public TestcaseEntityDO findTargetModule(TestcaseChangeVersionEvent event) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE)
                .andEqualTo("automaticSourceCode", StringUtils.EMPTY)
                .andEqualTo("code", event.getTargetParentCode())
                .andEqualTo("versionCode", event.getTargetVersionCode())
                .andEqualTo("setCore", event.getSetCore());
        example.selectProperties("path");
        return testcaseConverter.covert(testcaseMapper.selectOneByExample(example));

    }

    @Override
    public void changeVersion(TestcaseChangeVersionEvent event, TestcaseGroupVO vo) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andEqualTo("productCode", event.getProductCode())
                .andEqualTo("versionCode", event.getTargetVersionCode())
                .andEqualTo("parentCode", vo.getParentCode())
                .andEqualTo("name", event.getName())
                .andEqualTo("setCore", Boolean.FALSE)
                .andEqualTo("type", TestcaseTypeEnum.MANUAL)
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE)
                .andEqualTo("enable", Boolean.TRUE);
        int count = testcaseMapper.selectCountByExample(example);
        if (count > 0) {
            throw new ServiceException("该目录下用例名称重复！");
        }
        TestcaseEntity entity = new TestcaseEntity();
        entity.setCode(event.getCode());
        entity.setParentCode(vo.getParentCode());
        entity.setPath(vo.getParentPath());
        entity.setVersionCode(event.getTargetVersionCode());
        entity.preUpdate(event);
        testcaseMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public Integer selectCountCreatorOrDutyUser(List<String> list, Long userId) {
        return testcaseMapper.selectCountCreatorOrDutyUser(list, userId);
    }

    @Override
    public CheckTestcaseStatusVO selectCountTestcaseStatus(List<String> list) {
        return testcaseMapper.selectCountTestcaseStatus(list);
    }


    @Override
    public List<String> selectModuleCodeList(PageTestcaseQuery pageTestcaseQuery) {
        return testcaseMapper.selectModuleCodeList(pageTestcaseQuery);
    }

    @Override
    public List<ListTestcaseModuleVO> selectModuleList(ListTestcaseModuleQuery query) {
        Map<String, Integer> testcaseSortMap = selectTestcaseSort();
        List<String> editModuleCodeList = getEditModuleCodeList(query.getSceneTopModuleCode());
        List<String> editAutomaticSourceCodeList = selectAutomaticSourceCodeListFromScene(query);
        List<TestcaseEntity> moduleList = selectTestcaseModuleList(query, editModuleCodeList);
        if (TestcaseTypeEnum.AUTO.equals(query.getType()) && !Boolean.TRUE.equals(query.getFactoryPattern())) {
            moduleList.removeIf(i -> editAutomaticSourceCodeList.contains(i.getAutomaticSourceCode()));
        }
        List<ListTestcaseModuleVO> voList = convertModule(moduleList, query.getSceneTopModuleCode(), testcaseSortMap);
        if ("AUTO".equals(query.getType().name()) && (query.getMenuPattern() == null || !query.getMenuPattern())) {
            List<AutomaticSourceRecordEntity> autoModuleList = selectAutoModuleList(query, editAutomaticSourceCodeList);
            Map<String, String> analysisSuccessAbandonedIdMap = selectAnalysisSuccessAbandonedIdMap();
            voList.addAll(convertAuto(autoModuleList, analysisSuccessAbandonedIdMap, testcaseSortMap));
        }
        List<ListTestcaseModuleVO> sortList = voList.stream()
                .filter(i -> StringUtils.isBlank(i.getAutomaticSourceCode()) || TestcaseTypeEnum.SOURCERECORD.equals(i.getType()))
                .collect(Collectors.toList());
        List<ListTestcaseModuleVO> result = sortData(sortList, query.getSceneTopModuleCode(), query.getType().name());
        List<ListTestcaseModuleVO> unSortList = voList.stream()
                .filter(i -> StringUtils.isNotBlank(i.getAutomaticSourceCode()) && TestcaseTypeEnum.AUTO.equals(i.getType()))
                .collect(Collectors.toList());
        result.addAll(unSortList);
        return result;
    }

    private Map<String, Integer> selectTestcaseSort() {
        Example example = new Example(TestcaseSortEntity.class);
        example.selectProperties("code", "sort");
        List<TestcaseSortEntity> entityList = testcaseSortMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(entityList)) {
            return Collections.emptyMap();
        }
        return entityList.parallelStream()
                .collect(Collectors.toMap(TestcaseSortEntity::getCode, TestcaseSortEntity::getSort));
    }

    private List<String> getEditModuleCodeList(String sceneTopModuleCode) {
        if (StringUtils.isBlank(sceneTopModuleCode)) {
            return Collections.emptyList();
        }
        Example example = new Example(TestcaseEntity.class);
        example.selectProperties("code");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("name", "草稿");
        criteria.andEqualTo("enable", Boolean.TRUE);
        criteria.andEqualTo("parentCode", sceneTopModuleCode);
        List<TestcaseEntity> entityList = testcaseMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        return entityList.parallelStream().distinct().map(TestcaseEntity::getCode).collect(Collectors.toList());
    }

    private List<TestcaseEntity> selectTestcaseModuleList(ListTestcaseModuleQuery query, List<String> codes) {
        Example example = new Example(TestcaseEntity.class);
        example.selectProperties("code", "productCode", "parentCode", "name", "attribute", "type", "status",
                "automaticSourceCode", "path", "layer", "id", "gmtCreate", "nodeType");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", Boolean.TRUE);
        criteria.andEqualTo("productCode", query.getProductCode());
        criteria.andEqualTo("type", query.getType());
        criteria.andEqualTo("attribute", TestcaseAttributeEnum.MODULE);
        if (TestcaseTypeEnum.AUTO.equals(query.getType()) && Boolean.TRUE.equals(query.getMenuPattern())) {
            criteria.andEqualTo("automaticSourceCode", "");
        }
        if ("AUTO".equals(query.getType().name()) && (null == query.getFactoryPattern() || query.getFactoryPattern().equals(false))) {
            if (CollectionUtil.isNotEmpty(codes)) {
                criteria.andNotIn("code", codes);
            }
        }
        if (null != query.getSetCore()) {
            criteria.andEqualTo("setCore", query.getSetCore());
        }
        if (StringUtils.isNotEmpty(query.getVersionCode())) {
            criteria.andEqualTo("versionCode", query.getVersionCode());
        }
        if (null != query.getPlanPattern() && query.getPlanPattern()) {
            criteria.andEqualTo("status", "NORMAL");
        }
        return testcaseMapper.selectByExample(example);
    }

    public List<ListTestcaseModuleVO> convertModule(List<TestcaseEntity> list, String sceneTopModuleCode, Map<String, Integer> testcaseSortMap) {
        List<ListTestcaseModuleVO> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(list)) {
            return result;
        }
        for (TestcaseEntity testcaseEntity : list) {
            ListTestcaseModuleVO vo = new ListTestcaseModuleVO();
            vo.setCode(testcaseEntity.getCode());
            vo.setProductCode(testcaseEntity.getProductCode());
            vo.setParentCode(testcaseEntity.getParentCode());
            if (StringUtils.isBlank(testcaseEntity.getParentCode()) && StringUtils.isNotEmpty(testcaseEntity.getAutomaticSourceCode())) {
                vo.setParentCode(testcaseEntity.getAutomaticSourceCode());
            }
            vo.setName(testcaseEntity.getName());
            vo.setAttribute(testcaseEntity.getAttribute());
            vo.setType(testcaseEntity.getType());
            vo.setAutomaticSourceCode(testcaseEntity.getAutomaticSourceCode());
            vo.setPath(testcaseEntity.getPath());
            vo.setLayer(testcaseEntity.getLayer());
            vo.setId(testcaseEntity.getId());
            vo.setGmtCreate(testcaseEntity.getGmtCreate());
            vo.setSceneTopModuleCode(sceneTopModuleCode);
            if (MapUtil.isNotEmpty(testcaseSortMap)) {
                vo.setSort(testcaseSortMap.get(testcaseEntity.getCode()));
            }
            vo.setNodeType(testcaseEntity.getNodeType());
            result.add(vo);
        }
        return result;
    }

    private List<String> selectAutomaticSourceCodeListFromScene(ListTestcaseModuleQuery query) {
        if (TestcaseTypeEnum.MANUAL.equals(query.getType()) || Boolean.TRUE.equals(query.getMenuPattern())) {
            return Collections.emptyList();
        }
        Example example = new Example(SceneInfoEntity.class);
        example.selectProperties("automaticSourceCode");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("productCode", query.getProductCode());
        criteria.andEqualTo("status", "edit");
        criteria.andNotEqualTo("automaticSourceCode", "");
        example.setOrderByClause("scene_code");
        List<SceneInfoEntity> entityList = sceneInfoMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        return entityList.parallelStream().distinct().map(SceneInfoEntity::getAutomaticSourceCode).collect(Collectors.toList());
    }

    private List<AutomaticSourceRecordEntity> selectAutoModuleList(ListTestcaseModuleQuery query, List<String> codes) {
        Example example = new Example(AutomaticSourceRecordEntity.class);
        example.selectProperties("code", "productCode", "testcaseCode", "name", "comment", "type", "id", "gmtCreate");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", Boolean.TRUE);
        criteria.andEqualTo("productCode", query.getProductCode());
        if ("AUTO".equals(query.getType().name()) && (null == query.getFactoryPattern() || query.getFactoryPattern().equals(false))) {
            if (CollectionUtil.isNotEmpty(codes)) {
                criteria.andNotIn("code", codes);
            }
        }
        return automaticSourceRecordMapper.selectByExample(example);
    }

    private Map<String, String> selectAnalysisSuccessAbandonedIdMap() {
        Example example = new Example(AutomaticSourceLogEntity.class);
        example.selectProperties("automaticSourceCode", "status");
        Example.Criteria criteria = example.createCriteria();
        criteria.andNotEqualTo("status", "ANALYSISSUCCESSABANDONED");
        criteria.andEqualTo("enable", Boolean.TRUE);
        example.setOrderByClause("id DESC");
        List<AutomaticSourceLogEntity> entityList = automaticSourceLogMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(entityList)) {
            return Collections.emptyMap();
        }
        return entityList.parallelStream()
                .distinct()
                .collect(Collectors.toMap(
                        AutomaticSourceLogEntity::getAutomaticSourceCode,
                        AutomaticSourceLogEntity::getStatus,
                        (existingValue, newValue) -> existingValue // 保留第一个值，忽略新值
                ));
    }

    private List<ListTestcaseModuleVO> convertAuto(List<AutomaticSourceRecordEntity> list, Map<String, String> analysisSuccessAbandonedMap, Map<String, Integer> testcaseSortMap) {
        List<ListTestcaseModuleVO> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(list)) {
            return result;
        }
        for (AutomaticSourceRecordEntity automaticSourceRecordEntity : list) {
            ListTestcaseModuleVO vo = new ListTestcaseModuleVO();
            vo.setCode(automaticSourceRecordEntity.getCode());
            vo.setProductCode(automaticSourceRecordEntity.getProductCode());
            if (StringUtils.isEmpty(automaticSourceRecordEntity.getTestcaseCode())) {
                vo.setParentCode("");
            } else {
                vo.setParentCode(automaticSourceRecordEntity.getTestcaseCode());
            }
            vo.setName(automaticSourceRecordEntity.getName());
            vo.setAttribute(TestcaseAttributeEnum.MODULE);
            vo.setType(TestcaseTypeEnum.SOURCERECORD);
            vo.setAutomaticSourceCode(automaticSourceRecordEntity.getCode());
            vo.setPath(null);
            vo.setLayer(null);
            vo.setComment(automaticSourceRecordEntity.getComment());
            if (MapUtil.isEmpty(analysisSuccessAbandonedMap) || null == analysisSuccessAbandonedMap.get(automaticSourceRecordEntity.getCode())) {
                vo.setAutomaticSourceStatus(automaticSourceRecordEntity.getStatus());
            } else {
                vo.setAutomaticSourceStatus(AutomaticStatusEnum.valueOf(analysisSuccessAbandonedMap.get(automaticSourceRecordEntity.getCode())));
            }
            vo.setAutomaticSourceType(automaticSourceRecordEntity.getType());
            vo.setId(automaticSourceRecordEntity.getId());
            vo.setGmtCreate(automaticSourceRecordEntity.getGmtCreate());
            vo.setSceneTopModuleCode("");
            if (MapUtil.isNotEmpty(testcaseSortMap)) {
                vo.setSort(testcaseSortMap.get(automaticSourceRecordEntity.getCode()));
            }
            result.add(vo);
        }
        return result;
    }

    public List<ListTestcaseModuleVO> sortData(List<ListTestcaseModuleVO> list, String sceneTopModuleCode, String type) {
        boolean sortBySceneSort = type.equals("AUTO");

        if (sortBySceneSort) {
            return list.stream()
                    .sorted((ListTestcaseModuleVO o1, ListTestcaseModuleVO o2) -> {
                        String code1 = o1.getCode() == null ? "" : o1.getCode();
                        String path1 = o1.getPath() == null ? "" : o1.getPath();
                        String code2 = o2.getCode() == null ? "" : o2.getCode();
                        String path2 = o2.getPath() == null ? "" : o2.getPath();

                        int sceneSort1 = code1.equals(sceneTopModuleCode) || path1.contains(sceneTopModuleCode) ? 0 : 1;
                        int sceneSort2 = code2.equals(sceneTopModuleCode) || path2.contains(sceneTopModuleCode) ? 0 : 1;
                        int result = Integer.compare(sceneSort1, sceneSort2);
                        if (result == 0) {
                            result = Integer.compare(o1.getSort() == null ? 99999 : o1.getSort(),
                                    o2.getSort() == null ? 99999 : o2.getSort());
                        }
                        if (result == 0) {
                            result = -o1.getGmtCreate().compareTo(o2.getGmtCreate());
                        }
                        if (result == 0) {
                            result = -Long.compare(o1.getId(), o2.getId());
                        }
                        return result;
                    })
                    .collect(Collectors.toList());
        } else {
            return list.stream()
                    .sorted(Comparator
                            .comparingInt((ListTestcaseModuleVO o) -> o.getSort() == null ? 99999 : o.getSort())
                            .thenComparing(ListTestcaseModuleVO::getGmtCreate)
                            .thenComparingLong(ListTestcaseModuleVO::getId))
                    .collect(Collectors.toList());
        }
    }

    @Override
    public List<SimpleTestcaseVO> selectAllTestCasePath(PageTestcaseQuery pageTestcaseQuery) {
        return testcaseMapper.selectAllTestCasePath(pageTestcaseQuery);
    }

    @Override
    public List<ListTestcaseVO> selectTestCaseModuleList(PageTestcaseQuery pageTestcaseQuery) {
        return testcaseMapper.selectTestCaseModuleList(pageTestcaseQuery);
    }

    @Override
    public List<ListTestcaseVO> pageTestcase(PageTestcaseQuery pageTestcaseQuery) {
        return testcaseMapper.pageTestcase(pageTestcaseQuery);
    }

    @Override
    public Integer selectTestCaseCount(TestcaseQuery query) {
        Example example = new Example(TestcaseEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("setCore", query.getSetCore())
                .andEqualTo("versionCode", query.getVersionCode())
                .andEqualTo("productCode", query.getProductCode())
                .andEqualTo("type", query.getType())
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE)
                .andLike("path", "%" + query.getParentCode() + "%");
        if (CollectionUtil.isNotEmpty(query.getStatusList())) {
            criteria.andIn("status", query.getStatusList());
        }
        if (CollectionUtil.isNotEmpty(query.getPriorityList())) {
            criteria.andIn("priority", query.getPriorityList());
        }
        return testcaseMapper.selectCountByExample(example);
    }

    @Override
    public List<TestcaseEntityDO> selectTestCase(TestcaseQuery query) {
        if (TestcaseGroupTypeEnum.ALL.name().equals(query.getParentCode())) {
            query.setParentCode("");
        }
        if (TestcaseGroupTypeEnum.NO_GROUP.name().equals(query.getParentCode())) {
            query.setParentCode("");
            query.setTestcaseAttributeString(TestcaseAttributeEnum.TESTCASE.name());
        }
        if (StringUtils.isNotBlank(query.getParentCode()) && StringUtils.isNotBlank(query.getPlanCode())) {
            if (query.getParentCode().startsWith(TestcaseGroupTypeEnum.NO_GROUP.name() + "___")) {
                query.setParentCode("");
                query.setTestcaseAttributeString(TestcaseAttributeEnum.TESTCASE.name());
            }
            if ("NONE_VERSION".equals(query.getParentCode())) {
                query.setParentCode("");
            }
            if (query.getParentCode().startsWith("VER")) {
                query.setVersionCode(query.getParentCode());
                query.setParentCode("");
            }
        }
        return testcaseMapper.selectAllTestCaseByParentCode(query);
    }

    @Override
    public Long countNoGroupTestcase(PageTestcaseQuery query) {
        return testcaseMapper.countNoGroupTestcase(query);
    }

    @Override
    public Integer countByNodeTypeAndCodeList(AutomaticNodeTypeEnum nodeTypeEnum, List<String> codeList) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("enable", true)
                .andEqualTo("nodeType", nodeTypeEnum)
                .andIn("code", codeList);
        return testcaseMapper.selectCountByExample(example);
    }

    @Override
    public List<TestHeartCaseVO> selectAllHeartCase(FindHeartCaseByUserQuery query) {
        return testHeartCaseMapper.selectAllHeartCase(query);
    }

    @Override
    public void associateTestcaseDomain(TestcaseDomainAssociatedEvent event) {
        Example example = new Example(TestcaseRelationEntity.class);
        example.createCriteria().andEqualTo("testcaseCode", event.getCode())
                .andEqualTo("domain", event.getDomain());
        List<TestcaseRelationEntity> entities = testcaseRelationMapper.selectByExample(example);
        List<String> relatedCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(entities)) {
            relatedCodeList = entities.stream().map(TestcaseRelationEntity::getBusinessCode).collect(Collectors.toList());
        }
        for (String businessCode : event.getCodeList()) {
            if (relatedCodeList.contains(businessCode)) {
                continue;
            }
            TestcaseRelationEntity entity = new TestcaseRelationEntity();
            entity.setTestcaseCode(event.getCode());
            entity.setBusinessCode(businessCode);
            entity.setDomain(event.getDomain());
            entity.setCreator(event.getTransactor().getUserName());
            entity.setCreatorId(event.getTransactor().getUserId());
            entity.setGmtCreate(event.getOccurred());
            entity.setModifier(event.getTransactor().getUserName());
            entity.setModifierId(event.getTransactor().getUserId());
            entity.setGmtModified(event.getOccurred());
            testcaseRelationMapper.insertSelective(entity);
        }
    }

    @Override
    public void batchInsertCase(List<TestcaseEntityDO> list) {
        List<TestcaseEntity> entityList = testcaseConverter.covert2EntityList(list);
        List<List<TestcaseEntity>> partition = Lists.partition(entityList, 24);
        for (List<TestcaseEntity> subList : partition) {
            testcaseMapper.batchInsertCase(subList);
        }
    }

    @Override
    public List<TestcaseVO> listCaseByAutomaticCode(String automaticSourceCode) {
        return testcaseMapper.selectListAndPlanName(automaticSourceCode);
    }

    @Override
    public List<TestcaseExecuteRecordVO> selectRecordByCode(String code, AutomaticTaskTrigModeEnum type) {
        return testcaseExecuteRecordMapper.selectByTestcaseCode(code, type);
    }

    @Override
    public TestcaseEntityDO findModulePathByParentCode(String parentCode) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE)
                .andEqualTo("automaticSourceCode", StringUtils.EMPTY)
                .andEqualTo("code", parentCode);
        example.selectProperties("path");
        TestcaseEntity entity = testcaseMapper.selectOneByExample(example);
        return testcaseConverter.covert(entity);
    }

    @Override
    public void updateTestcaseModulePath(String automaticSourceCode, String newPath, User user) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("automaticSourceCode", automaticSourceCode);
        TestcaseEntity testcaseEntity = new TestcaseEntity();
        testcaseEntity.setTestcaseModulePath(newPath);
        testcaseEntity.preUpdate(user);
        testcaseMapper.updateByExampleSelective(testcaseEntity, example);
    }

    @Override
    public List<String> selectSortedCodeList(String productCode, String parentCode, TestcaseTypeEnum auto) {
        return testcaseSortMapper.selectSortedCodeList(productCode, parentCode, auto);
    }

    @Override
    public void deleteOldSorted(List<String> sortedList) {
        testcaseSortMapper.deleteOldSorted(sortedList);
    }

    @Override
    public void insertNewSorted(List<String> sortedList) {
        testcaseSortMapper.insertNewSorted(sortedList);
    }

    @Override
    public String selectMaxLengthPathByPath(String oldPrefixPath) {
        return testcaseMapper.selectMaxLengthPathByPath(oldPrefixPath);
    }

    @Override
    public int countModuleByTypeAndNameAndParentCode(String productCode, TestcaseTypeEnum type, String name, String parentCode) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("productCode", productCode)
                .andEqualTo("type", type)
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE)
                .andEqualTo("name", name)
                .andEqualTo("parentCode", parentCode);
        return testcaseMapper.selectCountByExample(example);
    }

    @Override
    public void updateXmindDetail(XmindCaseEditEvent event, TestCaseTagNameEnum type) {
        Example example = new Example(TestcaseEntity.class);
        TestcaseEntity entity = new TestcaseEntity();
        if (TestCaseTagNameEnum.TOPIC.equals(type)) {
            entity.setName(event.getTopic());
            example.createCriteria().andEqualTo("code", event.getId());
        }
        if (TestCaseTagNameEnum.PRECONDITION.equals(type)) {
            entity.setPrecondition(event.getTopic());
            example.createCriteria().andEqualTo("code", event.getParentCode());
        }
        if (TestCaseTagNameEnum.REMARK.equals(type)) {
            entity.setComment(event.getTopic());
            example.createCriteria().andEqualTo("code", event.getParentCode());
        }
        testcaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void updateXmindStepExcept(XmindCaseEditEvent event, TestCaseTagNameEnum type) {
        Example example = new Example(TestcaseStepEntity.class);
        TestcaseStepEntity entity = new TestcaseStepEntity();
        if (TestCaseTagNameEnum.STEP.equals(type)) {
            String sort = event.getId().replace(event.getParentCode(), "")
                    .replace(TestCaseTagNameEnum.STEP.name(), "");
            example.createCriteria().andEqualTo("testcaseCode", event.getParentCode())
                    .andEqualTo("sort", Integer.valueOf(sort));
            entity.setStepDesc(event.getTopic());
        }
        if (TestCaseTagNameEnum.EXPECT.equals(type)) {
            String sort = event.getId().replace(event.getParentCode().split(TestCaseTagNameEnum.STEP.name())[0], "")
                    .replace(TestCaseTagNameEnum.EXPECT.name(), "");
            example.createCriteria().andEqualTo("testcaseCode",
                            event.getParentCode().split(TestCaseTagNameEnum.STEP.name())[0])
                    .andEqualTo("sort", Integer.valueOf(sort));
            entity.setExpectResult(event.getTopic());
        }
        testcaseStepMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void deleteXmindSteps(XmindCaseEditEvent event) {
        Example exampleStep = new Example(TestcaseStepEntity.class);
        exampleStep.orderBy("sort").asc();
        exampleStep.createCriteria().andEqualTo("testcaseCode", event.getParentCode());
        // 先查出来所有步骤
        List<TestcaseStepEntity> entityList = testcaseStepMapper.selectByExample(exampleStep);
        String sort = event.getId().replace(event.getParentCode(), "")
                .replace(TestCaseTagNameEnum.STEP.name(), "");
        entityList = entityList.stream().filter(x -> !Integer.valueOf(sort).equals(x.getSort())).collect(Collectors.toList());
        // 删除所有步骤
        testcaseStepMapper.deleteByExample(exampleStep);
        while (entityList.size() < 3) {
            TestcaseStepEntity entity = new TestcaseStepEntity();
            entity.setTestcaseCode(event.getParentCode());
            entity.setStepDesc("");
            entity.setExpectResult("");
            entity.preCreate(event);
            entity.preUpdate(event);
            entityList.add(entity);
        }
        buildTestcaseStepEntityList(entityList, event);
        testcaseStepMapper.saveBatch(entityList);
    }

    @Override
    public void updateByPrimaryKeySelective(TestcaseEntityDO entityDO) {
        TestcaseEntity entity = testcaseConverter.covert2Entity(entityDO);
        testcaseMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public int findTestcaseModulePathQuery(FindTestcaseModulePathQuery query) {
        String path = testcaseMapper.selectTestcaseModulePath(query);
        if (StringUtil.isEmpty(path) || StringUtil.isEmpty(path.replace(query.getOldPath(), ""))) {
            return 0;
        }
        String fullPath = query.getNewPath() + "." + query.getCode() + path.replace(query.getOldPath(),
                "");
        return fullPath.split("\\.").length;
    }

    @Override
    public void updateByAutomaticSourceCode(MoveModuleEvent event, String newPath) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("automaticSourceCode", event.getCode());
        TestcaseEntity testcaseEntity = new TestcaseEntity();
        testcaseEntity.setTestcaseModulePath(newPath);
        testcaseEntity.setModifier(event.getTransactor().getUserName());
        testcaseEntity.setModifierId(event.getTransactor().getUserId());
        testcaseMapper.updateByExampleSelective(testcaseEntity, example);
    }

    @Override
    public void replacePath(String oldPath, String newPath) {
        testcaseMapper.replacePath(oldPath, newPath);
    }

    @Override
    public void replaceTestcaseModulePath(String oldPath, String newPath) {
        testcaseMapper.replaceTestcaseModulePath(oldPath, newPath);
    }

    @Override
    public TestcaseEntityDO selectTestCaseEntity(TestcaseMovedEvent event, String parentCode) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE)
                .andEqualTo("automaticSourceCode", StringUtils.EMPTY)
                .andEqualTo("code", parentCode)
                .andEqualTo("versionCode", event.getVersionCode())
                .andEqualTo("setCore", event.getSetCore());
        example.selectProperties("path");
        TestcaseEntity entity = testcaseMapper.selectOneByExample(example);
        return testcaseConverter.covert(entity);
    }

    @Override
    public int selectTestCaseCount(TestcaseMovedEvent event, String parentCode) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("productCode", event.getProductCode())
                .andEqualTo("versionCode", event.getVersionCode())
                .andEqualTo("setCore", event.getSetCore())
                .andEqualTo("type", TestcaseTypeEnum.MANUAL)
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE)
                .andEqualTo("name", event.getName())
                .andEqualTo("parentCode", parentCode);
        return testcaseMapper.selectCountByExample(example);
    }

    /**
     * 排序
     */
    public void buildTestcaseStepEntityList(List<TestcaseStepEntity> list, XmindCaseEditEvent event) {
        Integer sort = 1;
        for (TestcaseStepEntity entity : list) {
            entity.setSort(sort);
            entity.preUpdate(event);
            sort++;
        }
    }

    @Override
    public void addTestcase(AddTestcaseEvent event) {
        TestcaseEntity entity = testcaseConverter.convert(event);
        entity.preCreate(event);
        if (null != event.getTransactor() && (null == event.getDutyUserId() || StringUtil.isEmpty(event.getDutyUser()))) {
            entity.setDutyUser(event.getTransactor().getUserName());
            entity.setDutyUserId(event.getTransactor().getUserId());
        }
        if (StringUtil.isEmpty(event.getPath())) {
            entity.setPath(getAllParentCode(event.getParentCode()));
        }
        event.setPath(entity.getPath());
        testcaseMapper.insertSelective(entity);
    }

    @Override
    public void editTestcase(EditTestcaseEvent event) {
        if (StringUtil.isEmpty(event.getCode())) {
            return;
        }
        TestcaseEntity entity = testcaseConverter.convert(event);
        entity.preUpdate(event);
        //不修改类型
        entity.setType(null);
        entity.setAttribute(null);
        entity.setPath(this.getAllParentCode(event.getParentCode()));
        testcaseMapper.updateByPrimaryKeySelective(entity);
    }

    private String getAllParentCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return "";
        }
        TestcaseEntity entity = testcaseMapper.selectByPrimaryKey(code);
        if (null != entity && StringUtil.isNotEmpty(entity.getPath())) {
            return entity.getPath() + "." + code;
        } else {
            return code;
        }
    }

    @Override
    public String selectAllCodeByParentCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return "";
        }
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("code", code)
                .andEqualTo("enable", true);
        example.selectProperties("code", "path");
        TestcaseEntity entity = testcaseMapper.selectOneByExample(example);
        if (null == entity) {
            return "";
        }
        if (StringUtils.isEmpty(entity.getPath())) {
            return entity.getCode();
        } else {
            return entity.getPath() + "." + entity.getCode();
        }
    }

    @Override
    public List<TestcaseEntityDO> selectAutomaticCaseByCodeList(List<String> testcaseCodeList) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("status", TestcaseStatusEnum.NORMAL)
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE)
                .andEqualTo("type", TestcaseTypeEnum.AUTO)
                .andIn("code", testcaseCodeList);
        example.selectProperties("code", "path", "automaticSourceCode", "setHeart");
        List<TestcaseEntity> entityList = testcaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : testcaseConverter.covertList(entityList);
    }

    @Override
    public List<TestcaseEntityDO> selectByCodeList(List<String> codeList) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andIn("code", codeList);
        example.selectProperties("code", "enable", "status");
        List<TestcaseEntity> entityList = testcaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : testcaseConverter.covertList(entityList);
    }

    @Override
    public List<String> selectOperateCaseCodeByTaskCode(String code) {
        return tmTestPlanCaseMapper.selectOperateCaseCodeByTaskCode(code);
    }

    @Override
    public List<TestcaseEntityDO> selectByAutomaticSourceCode(String automaticSourceCode, Boolean enable) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andEqualTo("enable", enable)
                .andEqualTo("automaticSourceCode", automaticSourceCode);
        List<TestcaseEntity> entityList = testcaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : testcaseConverter.covertList(entityList);
    }

    @Override
    public void insertBatchExecuteRecord(TestcaseExecuteRecordEntityDO executeRecordEntityDO, List<String> list) {
        TestcaseExecuteRecordEntity entity = testcaseExecuteRecordConverter.convert2Entity(executeRecordEntityDO);
        testcaseExecuteRecordMapper.insertBatch(entity, list);
    }

    @Override
    public void updateByAutomaticTaskCode(String code) {
        tmTestPlanCaseMapper.updateByAutomaticTaskCode(code);
    }

    @Override
    public void deleteTestcase(DeleteTestcaseEvent event) {
        log.info("---------------进入DeleteTestcaseEvent-----" + JsonUtil.toJSON(event));
        //用例删除
        if (event.getAttribute().name().equals(TestcaseAttributeEnum.TESTCASE.name())) {
            log.info("---------------进入用例删除-----");
            TestcaseEntity entity = testcaseConverter.convertorDeleteToEntity(event);
            if (StringUtil.isNotEmpty(entity.getCode())) {
                entity.setEnable(false);
                testcaseMapper.updateByPrimaryKeySelective(entity);
                deleteTestPlan(entity.getCode());
            }
        } else {
            log.info("---------------进入模块删除-----");
            //模块删除
            TestcaseEntity module = testcaseMapper.selectByPrimaryKey(event.getCode());
            if (null == module) {
                return;
            }
            List<TestcaseEntityDO> children = queryChildCaseAndModuleByPath(module.getCode(), module.getPath());
            if (TestcaseTypeEnum.AUTO.equals(event.getType())) {
                children.addAll(queryChildCaseAndModuleByTestcaseModulePath(module.getCode(), module.getPath()));
            }
            List<String> caseCodeList = children.stream().map(TestcaseEntityDO::getCode).collect(Collectors.toList());
            caseCodeList.add(module.getCode());

            TestcaseEntity testcaseEntity = new TestcaseEntity();
            testcaseEntity.setEnable(false);
            testcaseEntity.setModifier(event.getTransactor().getUserName());
            testcaseEntity.setModifierId(event.getTransactor().getUserId());
            testcaseEntity.setGmtModified(event.getOccurred());

            List<List<String>> partition = Lists.partition(caseCodeList, 500);
            partition.forEach(t -> {
                Example updateExample = new Example(TestcaseEntity.class);
                updateExample.createCriteria().andIn("code", caseCodeList);
                testcaseMapper.updateByExampleSelective(testcaseEntity, updateExample);
            });

            if (CollectionUtil.isNotEmpty(caseCodeList)) {
                getExecutorService().execute(() -> batchDeleteTestPlan(caseCodeList, event));
            }
            if (TestcaseTypeEnum.AUTO.equals(event.getType())) {
                batchDeleteAutomaticSourceRecord(caseCodeList, event);
            }

        }
    }

    private void batchDeleteAutomaticSourceRecord(List<String> testcaseEntities, DeleteTestcaseEvent event) {

        AutomaticSourceRecordEntity automaticSourceRecordEntity = new AutomaticSourceRecordEntity();
        automaticSourceRecordEntity.setEnable(false);
        automaticSourceRecordEntity.setModifier(event.getTransactor().getUserName());
        automaticSourceRecordEntity.setModifierId(event.getTransactor().getUserId());
        automaticSourceRecordEntity.setGmtModified(event.getOccurred());

        List<String> list = automaticSourceRecordMapper.selectByTestcaseCodeList(testcaseEntities);

        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Example updateExample = new Example(AutomaticSourceRecordEntity.class);
        updateExample.createCriteria().andIn("code", list);
        automaticSourceRecordMapper.updateByExampleSelective(automaticSourceRecordEntity, updateExample);
    }

    public void batchDeleteTestPlan(List<String> testcaseEntities, DeleteTestcaseEvent event) {

        List<DeletePlanCaseVO> deletePlanCaseVOS = tmTestPlanCaseMapper.selectPlanCaseByCodeList(testcaseEntities);
        if (CollectionUtil.isEmpty(deletePlanCaseVOS)) {
            return;
        }
        LinkedList<Long> logicDeleteList = new LinkedList<>();
        LinkedList<Long> physicsDeleteList = new LinkedList<>();

        TmTestPlanCaseEntity tmTestPlanCaseEntity = new TmTestPlanCaseEntity();
        tmTestPlanCaseEntity.setEnable(false);
        tmTestPlanCaseEntity.setModifier(event.getTransactor().getUserName());
        tmTestPlanCaseEntity.setModifierId(event.getTransactor().getUserId());
        tmTestPlanCaseEntity.setGmtModified(event.getOccurred());

        for (DeletePlanCaseVO vo : deletePlanCaseVOS) {
            if (vo.getPlanStatus().equals(TestPlanNewStatusEnum.COMPLETED)) {
                logicDeleteList.add(vo.getId());
            } else {
                if (EmptyCheckerUtil.isNotEmpty(vo.getPlanStageStatus()) && vo.getPlanStageStatus().get(vo.getTestStage().name()).equals(TestPlanStageStatusEnum.COMPLETED.name())) {
                    logicDeleteList.add(vo.getId());
                } else {
                    physicsDeleteList.add(vo.getId());
                }
            }
        }

        if (CollectionUtil.isNotEmpty(logicDeleteList)) {
            List<List<Long>> partition = Lists.partition(logicDeleteList, 1000);
            partition.forEach(t ->
            {
                Example updateExample = new Example(TmTestPlanCaseEntity.class);
                updateExample.createCriteria().andIn("id", t);
                tmTestPlanCaseMapper.updateByExampleSelective(tmTestPlanCaseEntity, updateExample);
            });
        }
        if (CollectionUtil.isNotEmpty(physicsDeleteList)) {
            List<List<Long>> partition = Lists.partition(physicsDeleteList, 1000);
            partition.forEach(t -> tmTestPlanCaseMapper.deleteByIdList(t));
        }
    }

    private ExecutorService getExecutorService() {
        if (executorService == null) {
            executorService = Executors.newCachedThreadPool();
        }
        return executorService;
    }

    private void deleteTestPlan(String caseCode) {
        //根据测试用例code查询关联计划
        Example example = new Example(TmTestPlanCaseEntity.class);
        example.createCriteria().andEqualTo("caseCode", caseCode)
                .andEqualTo("enable", true);
        List<TmTestPlanCaseEntity> testPlanCaseEntityList = tmTestPlanCaseMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(testPlanCaseEntityList)) {
            return;
        }
        for (TmTestPlanCaseEntity en : testPlanCaseEntityList) {
            //查询测试计划  进行中 or未开始的计划 删除关联关系
            TmTestPlanEntity tmTestPlanEntity = tmTestPlanMapper.selectByPrimaryKey(en.getPlanCode());
            if (tmTestPlanEntity.getStatus().name().equals(TestPlanNewStatusEnum.COMPLETED.name())) {
                en.setEnable(false);
                tmTestPlanCaseMapper.updateByPrimaryKeySelective(en);
            } else {
                if (Objects.nonNull(tmTestPlanEntity.getStageStatus()) && !tmTestPlanEntity.getStageStatus().isEmpty() && !en.getTestStage().name().equals("NULL_TEST")) {
                    if ((TestPlanStageStatusEnum.INITIAL.name().equals(tmTestPlanEntity.getStageStatus().get("SMOKE_TEST"))
                            || TestPlanStageStatusEnum.IN_PROGRESS.name().equals(tmTestPlanEntity.getStageStatus().get("SMOKE_TEST"))) && en.getTestStage().name().equals("SMOKE_TEST")) {
                        tmTestPlanCaseMapper.delete(en);
                    }
                    if (TestPlanStageStatusEnum.COMPLETED.name().equals(tmTestPlanEntity.getStageStatus().get("SMOKE_TEST")) && en.getTestStage().name().equals("SMOKE_TEST")) {
                        en.setEnable(false);
                        tmTestPlanCaseMapper.updateByPrimaryKeySelective(en);
                    }
                    if ((TestPlanStageStatusEnum.INITIAL.name().equals(tmTestPlanEntity.getStageStatus().get("FUNCTIONAL_TEST"))
                            || TestPlanStageStatusEnum.IN_PROGRESS.name().equals(tmTestPlanEntity.getStageStatus().get("FUNCTIONAL_TEST"))) && en.getTestStage().name().equals("FUNCTIONAL_TEST")) {
                        tmTestPlanCaseMapper.delete(en);
                    }
                    if (TestPlanStageStatusEnum.COMPLETED.name().equals(tmTestPlanEntity.getStageStatus().get("FUNCTIONAL_TEST")) && en.getTestStage().name().equals("FUNCTIONAL_TEST")) {
                        en.setEnable(false);
                        tmTestPlanCaseMapper.updateByPrimaryKeySelective(en);
                    }
                    if ((TestPlanStageStatusEnum.INITIAL.name().equals(tmTestPlanEntity.getStageStatus().get("ONLINE_SMOKE_TEST"))
                            || TestPlanStageStatusEnum.IN_PROGRESS.name().equals(tmTestPlanEntity.getStageStatus().get("ONLINE_SMOKE_TEST"))) && en.getTestStage().name().equals("ONLINE_SMOKE_TEST")) {
                        tmTestPlanCaseMapper.delete(en);
                    }
                    if (TestPlanStageStatusEnum.COMPLETED.name().equals(tmTestPlanEntity.getStageStatus().get("ONLINE_SMOKE_TEST")) && en.getTestStage().name().equals("ONLINE_SMOKE_TEST")) {
                        en.setEnable(false);
                        tmTestPlanCaseMapper.updateByPrimaryKeySelective(en);
                    }
                } else {
                    tmTestPlanCaseMapper.delete(en);
                }
            }
        }
    }

    @Override
    public void changeTestcaseStatus(TestcaseStatusChangedEvent event) {
        TestcaseEntity entity = testcaseConverter.converter(event);
        testcaseMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public List<SimpleTestCase> selectSimpleTestCase(PageSimpleTestCaseQuery query) {
        Example example = new Example(TestcaseEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("enable", true)
                .andEqualTo("productCode", query.getProductCode());
        if (null != query.getType()) {
            criteria.andEqualTo("type", query.getType());
        }
        if (null != query.getAttribute()) {
            criteria.andEqualTo("attribute", query.getAttribute());
        }
        if (CollectionUtil.isNotEmpty(query.getStatus())) {
            criteria.andIn("status", query.getStatus());
        }
        if (StringUtils.isNotEmpty(query.getSearchKey())) {
            example.and(example.createCriteria()
                    .andEqualTo("code", query.getSearchKey())
                    .orLike("name", "%" + query.getSearchKey() + "%"));
        } else {
            example.orderBy("id").desc();
        }
        example.selectProperties("code", "name");
        return testcaseConverter.convertSimpleTestCase(testcaseMapper.selectByExample(example));
    }

    @Override
    public List<ListExecuteCaseVO> selectByAutomaticTaskCode(ListExecuteCaseQuery query) {
        return testcaseExecuteRecordMapper.selectByAutomaticTaskCode(query);
    }

    @Override
    public List<ListExecuteCaseVO> selectWithListExecuteCaseVO(List<String> list) {
        return testcaseExecuteRecordMapper.selectWithListExecuteCaseVO(list);
    }

    @Override
    public void saveBatch(List<TestcaseEntityDO> list) {
        testcaseMapper.saveBatch(testcaseConverter.covert2EntityList(list));
    }

    @Override
    public List<TestcaseVO> selectSimpleTestcaseQuery(SimpleTestcaseQuery query) {
        Example example = new Example(TestcaseEntity.class);
        Example.Criteria criteria = example.createCriteria().andEqualTo("enable", true);
        if (StringUtil.isNotBlank(query.getName())) {
            criteria.andEqualTo("name", query.getName());
        }
        if (query.getAttribute() != null) {
            criteria.andEqualTo("attribute", query.getAttribute().name());
        }
        if (query.getType() != null) {
            criteria.andEqualTo("type", query.getType());
        }
        if (CollectionUtil.isNotEmpty(query.getParentCodeList())) {
            criteria.andIn("parentCode", query.getParentCodeList());
        }
        if (StringUtil.isNotBlank(query.getProductCode())) {
            criteria.andEqualTo("productCode", query.getProductCode());
        }
        if (StringUtil.isNotBlank(query.getVersionCode())) {
            criteria.andEqualTo("versionCode", query.getVersionCode());
        }
        List<TestcaseEntity> list = testcaseMapper.selectByExample(example);
        return testcaseConverter.converterTestcaseList(list);
    }

    @Override
    public TestcaseVO handle(FindTestcaseEntityByCodeQuery query) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("code", query.getCode()).andEqualTo("enable", true);
        TestcaseEntity entity = testcaseMapper.selectOneByExample(example);
        return testcaseConverter.converter(entity);
    }

    @Override
    public void editTestcaseTitle(EditTestcaseTitleEvent event) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("productCode", event.getProductCode())
                .andEqualTo("name", event.getName())
                .andEqualTo("attribute", event.getAttribute())
                .andEqualTo("type", event.getType())
                .andEqualTo("parentCode", event.getParentCode())
                .andEqualTo("versionCode", event.getVersionCode())
                .andNotEqualTo("code", event.getCode());
        if (testcaseMapper.selectCountByExample(example) > 0) {
            throw new ServiceException("名称不能重复");
        }
        TestcaseEntity entity = testcaseConverter.convertorEditTitleEntity(event);
        if (StringUtil.isNotEmpty(entity.getCode())) {
            testcaseMapper.updateByPrimaryKeySelective(entity);
        }
    }

    @Override
    public List<RelatedCasePlanVO> selectRelatedPlanList(List<String> codeList) {
        return testcaseMapper.selectRelatedPlanList(codeList);
    }

    @Override
    public void releaseTestcaseRelation(TestcaseRelationReleasedEvent event) {
        Example example = new Example(TestcaseRelationEntity.class);
        example.createCriteria().andEqualTo("testcaseCode", event.getCode())
                .andEqualTo("businessCode", event.getBusinessCode())
                .andEqualTo("domain", event.getDomain());
        testcaseRelationMapper.deleteByExample(example);
    }

    @Override
    public List<TestcaseEntityDO> queryChildCaseAndModuleByPath(String code, String path) {
        String fullPath = StringUtils.isBlank(path) ? code : String.format("%s.%s", path, code);
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andLike("path", fullPath + "%")
                .andEqualTo("enable", true);
        example.selectProperties("code", "attribute");
        return testcaseConverter.covertList(testcaseMapper.selectByExample(example));
    }

    @Override
    public List<TestcaseEntityDO> queryChildCaseAndModuleByTestcaseModulePath(String code, String path) {
        String fullPath = StringUtils.isBlank(path) ? code : String.format("%s.%s", path, code);
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andLike("testcaseModulePath", fullPath + "%")
                .andEqualTo("enable", true);
        example.selectProperties("code", "attribute");
        return testcaseConverter.covertList(testcaseMapper.selectByExample(example));
    }

    @Override
    public Integer findTestcaseModulePath(FindTestcaseModulePathQuery query) {
        String path = testcaseMapper.selectTestcaseModulePath(query);
        if (StringUtil.isEmpty(path) || StringUtil.isEmpty(path.replace(query.getOldPath(), ""))) {
            return 0;
        }
        String fullPath = query.getNewPath() + "." + query.getCode() + path.replace(query.getOldPath(),
                "");
        return fullPath.split("\\.").length;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void moveModule(MoveModuleEvent event) {
        String parentCode = StringUtils.defaultString(event.getParentCode());
        if (TestcaseGroupTypeEnum.ALL.name().equals(parentCode)) {
            parentCode = StringUtils.EMPTY;
        }
        if (event.getType().equals(TestcaseTypeEnum.SOURCERECORD)) {
            String newPath;
            if (StringUtil.isNotEmpty(parentCode)) {
                newPath = (StringUtils.isBlank(event.getNewPath()) ? StringUtils.EMPTY : event.getNewPath() + ".") + parentCode + "." + event.getCode();
            } else {
                newPath = event.getCode();
            }
            AutomaticSourceRecordEntity recordEntity = new AutomaticSourceRecordEntity();
            recordEntity.setCode(event.getCode());
            recordEntity.setTestcaseCode(parentCode);
            recordEntity.setModifier(event.getTransactor().getUserName());
            recordEntity.setModifierId(event.getTransactor().getUserId());
            automaticSourceRecordMapper.updateByPrimaryKeySelective(recordEntity);
            Example example = new Example(TestcaseEntity.class);
            example.createCriteria().andEqualTo("automaticSourceCode", event.getCode());
            TestcaseEntity testcaseEntity = new TestcaseEntity();
            testcaseEntity.setTestcaseModulePath(newPath);
            testcaseEntity.setModifier(event.getTransactor().getUserName());
            testcaseEntity.setModifierId(event.getTransactor().getUserId());
            testcaseMapper.updateByExampleSelective(testcaseEntity, example);
        } else {
            String newPath = (StringUtils.isBlank(event.getNewPath()) ? StringUtils.EMPTY : event.getNewPath() + ".") + parentCode;
            TestcaseEntity entity = new TestcaseEntity();
            entity.setCode(event.getCode());
            entity.setParentCode(parentCode);
            entity.setPath(newPath);
            entity.preUpdate(event);
            testcaseMapper.updateByPrimaryKeySelective(entity);
            String oldPrefixPath = (StringUtils.isBlank(event.getOldPath()) ? StringUtils.EMPTY : event.getOldPath() + ".") + event.getCode();
            String newPrefixPath = (StringUtils.isBlank(newPath) ? StringUtils.EMPTY : newPath + ".") + event.getCode();
            testcaseMapper.replacePath(oldPrefixPath, newPrefixPath);
            if (TestcaseTypeEnum.AUTO.equals(event.getType())) {
                testcaseMapper.replaceTestcaseModulePath(oldPrefixPath, newPrefixPath);
            }
        }
    }

    @Override
    public List<TestcaseEntityDO> selectByStatusAndCodeList(TestcaseStatusEnum status, List<String> caseCodes) {
        Example caseExample = new Example(TestcaseEntity.class);
        caseExample.selectProperties("code", "type");
        caseExample.createCriteria().andIn("code", caseCodes).andEqualTo("status", status);
        List<TestcaseEntity> entityList = testcaseMapper.selectByExample(caseExample);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : testcaseConverter.covertList(entityList);
    }

    public List<TestcaseEntityDO> findListByCodeIn(List<String> codeList) {
        if (CollectionUtil.isEmpty(codeList)) {
            return null;
        }
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE)
                .andIn("code", codeList);
        List<TestcaseEntity> entityList = testcaseMapper.selectByExample(example);
        return testcaseConverter.covertList(entityList);
    }

    public List<TestcaseEntityDO> findDisableListByCodeIn(List<String> codeList) {
        if (CollectionUtil.isEmpty(codeList)) {
            return null;
        }
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("status", TestcaseStatusEnum.DISABLE)
                .andIn("code", codeList);
        List<TestcaseEntity> entityList = testcaseMapper.selectByExample(example);
        return testcaseConverter.covertList(entityList);
    }

    @Override
    public void insertTestcaseExecuteRecord(TestcaseExecuteRecordEntityDO entityDO) {
        testcaseExecuteRecordMapper.insertSelective(testcaseExecuteRecordConverter.convert2Entity(entityDO));
    }

    @Override
    public void addTestCaseSteps(XmindCaseAddEvent event) {
        Example example = new Example(TestcaseStepEntity.class);
        example.orderBy("sort").desc();
        example.createCriteria().andEqualTo("testcaseCode", event.getParentCode())
                .andEqualTo("enable", Boolean.TRUE);
        List<TestcaseStepEntity> list = testcaseStepMapper.selectByExample(example);
        int sort = 1;
        if (CollectionUtil.isNotEmpty(list)) {
            sort = list.get(0).getSort() + 1;
        }
        TestcaseStepEntity entity = new TestcaseStepEntity();
        entity.setTestcaseCode(event.getParentCode());
        entity.setStepDesc(null == event.getTopic() ? "" : event.getTopic());
        entity.setExpectResult("");
        entity.preCreate(event);
        entity.preUpdate(event);
        entity.setSort(sort);
        testcaseStepMapper.insertSelective(entity);
    }

    @Override
    public List<ListExecuteCaseVO> selectByVersionCode(String versionCode) {
        VersionVO versionVO = projectRpcService.findVersionInfoQuery(versionCode);
        String productCode = null;
        if (null != versionVO) {
            productCode = versionVO.getProductCode();
        }
        return testcaseExecuteRecordMapper.selectByVersionCode(versionCode, productCode);
    }

    @Override
    public List<ListExecuteCaseVO> selectAutoByPlanCode(String planCode, String testStage) {
        return testcaseExecuteRecordMapper.selectAutoByPlanCode(planCode, testStage);
    }

    @Override
    public List<ListExecuteCaseVO> selectManualByPlanCode(String planCode, String testStage) {
        return testcaseExecuteRecordMapper.selectManualByPlanCode(planCode, testStage);
    }

    @Override
    public List<TestCasePathVO> getParentCodeListByCaseCodeList(List<String> caseCodes) {
        if (CollectionUtils.isEmpty(caseCodes)) {
            return new ArrayList<>();
        }
        return testcaseMapper.getParentCodeListByCaseCodeList(caseCodes);
    }

    @Override
    public List<CountCaseVO> countByParentCodeList(String productCode, String versionCode, List<String> parentCodeList) {
        return testcaseMapper.countByParentCodeList(productCode, versionCode, parentCodeList);
    }

    @Override
    public List<TestcaseEntityDO> selectModuleByVersionName(String versionName, String productCode) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andEqualTo("productCode", productCode)
                .andLike("name", versionName + "%")
                .andEqualTo("path", "")
                .andEqualTo("parentCode", "")
                .andEqualTo("setCore", Boolean.TRUE)
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("type", TestcaseTypeEnum.MANUAL)
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE);
        List<TestcaseEntity> entityList = testcaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : testcaseConverter.covertList(entityList);
    }

    @Override
    public List<String> selectNoGroupCaseList(List<String> caseCodeList) {
        Example example = new Example(TestcaseEntity.class);
        example.selectProperties("code");
        example.createCriteria()
                .andIn("code", caseCodeList)
                .andEqualTo("parentCode", "")
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("type", TestcaseTypeEnum.MANUAL)
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE);
        List<TestcaseEntity> entityList = testcaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList)
                ? new ArrayList<>()
                : entityList.stream().map(TestcaseEntity::getCode).collect(Collectors.toList());
    }

    @Override
    public List<CountNoGroupCaseVO> countNoGroupTestcaseByVersionCodeList(PageTestcaseQuery query) {
        return testcaseMapper.countNoGroupTestcaseByVersionCodeList(query);
    }

    @Override
    public void updateStatusFromLinkMap(String automaticSourceCode, String linkMapCode, TestcaseStatusEnum status) {
        Example example1 = new Example(TestcaseEntity.class);
        example1.createCriteria()
                .andEqualTo("automaticSourceCode", automaticSourceCode)
                .andEqualTo("name", linkMapCode)
                .andEqualTo("enable", Boolean.TRUE);
        List<TestcaseEntity> list = testcaseMapper.selectByExample(example1);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        TestcaseEntity entity1 = new TestcaseEntity();
        entity1.setCode(list.get(0).getCode());
        entity1.setStatus(status);
        testcaseMapper.updateByPrimaryKeySelective(entity1);
        Example example2 = new Example(TestcaseEntity.class);
        example2.createCriteria()
                .andEqualTo("automaticSourceCode", automaticSourceCode)
                .andEqualTo("enable", Boolean.TRUE)
                .andLike("path", "%" + list.get(0).getCode() + "%");
        TestcaseEntity entity2 = new TestcaseEntity();
        entity2.setStatus(status);
        testcaseMapper.updateByExampleSelective(entity2, example2);
    }

    @Override
    public TestcaseEntityDO selectModuleInfoByName(TestcaseEntityDO entityDO) {
        return testcaseMapper.selectModuleInfoByName(entityDO);
    }

    @Override
    public void updateStatusFromSceneInfo(String automaticSourceCode, TestcaseStatusEnum status) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andEqualTo("automaticSourceCode", automaticSourceCode)
                .andEqualTo("enable", Boolean.TRUE);
        TestcaseEntity entity = new TestcaseEntity();
        entity.setStatus(status);
        testcaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<TestcaseEntityDO> selectTestCaseByAutoSourceCodes(List<String> autoSourceCodes) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE.name())
                .andEqualTo("enable", Boolean.TRUE)
                .andIn("automaticSourceCode", autoSourceCodes);
        return testcaseConverter.covertList(testcaseMapper.selectByExample(example));
    }

    @Override
    public String selectTopModuleCodeByName(String moduleName, String productCode) {
        if (StringUtil.isBlank(moduleName) || StringUtil.isBlank(productCode)) {
            return Strings.EMPTY;
        }
        return testcaseMapper.selectTopModuleCodeByName(moduleName, productCode);
    }

    @Override
    public void updateNameBySceneIndex(String testcaseCode, String moduleName, User operator) {
        if (StringUtil.isBlank(testcaseCode) || StringUtil.isBlank(moduleName) || null == operator) {
            log.info(testcaseCode + "_updateNameBySceneIndex_param_error_{}!", moduleName);
            return;
        }
        TestcaseEntity entity = testcaseMapper.selectByPrimaryKey(testcaseCode);
        if (null == entity) {
            log.info(testcaseCode + "_updateNameBySceneIndex_testcase_is_null!");
            return;
        }
        //是否存在同名分组
        List<TestcaseEntityDO> sameNameModuleList = selectSameNameModuleByParentCode(entity.getParentCode(), moduleName);
        if (CollectionUtil.isNotEmpty(sameNameModuleList)) {
            log.info(testcaseCode + "_updateNameBySceneIndex_repeat_name_{}!", moduleName);
            return;
        }
        TestcaseEntity toUpdate = new TestcaseEntity();
        toUpdate.setCode(testcaseCode);
        toUpdate.setName(moduleName);
        toUpdate.preUpdate(operator);
        testcaseMapper.updateByPrimaryKeySelective(toUpdate);
    }

    @Override
    public void deleteModule(String testcaseCode, User operator) {
        if (StringUtil.isBlank(testcaseCode)) {
            log.info("deleteModule_testcaseCode_is_null!");
            return;
        }
        TestcaseEntity toUpdateEntity = new TestcaseEntity();
        toUpdateEntity.setEnable(Boolean.FALSE);
        toUpdateEntity.preUpdate(operator);

        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("type", TestcaseTypeEnum.AUTO.name())
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE.name());
        Example.Criteria orCriteria = example.createCriteria();
        orCriteria.orEqualTo("code", testcaseCode)
                .orLike("path", "%" + testcaseCode + "%");
        example.and(orCriteria);
        testcaseMapper.updateByExampleSelective(toUpdateEntity, example);
    }

    @Override
    public List<TestcaseEntityDO> selectSameNameModuleByParentCode(String parentCode, String moduleName) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andEqualTo("parentCode", parentCode)
                .andEqualTo("name", moduleName)
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("type", TestcaseTypeEnum.AUTO.name())
                .andEqualTo("attribute", TestcaseAttributeEnum.MODULE.name());
        List<TestcaseEntity> entityList = testcaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : testcaseConverter.covertList(entityList);
    }

    @Override
    public TestcaseEntityDO selectSceneDefaultModule(String productCode, String topModuleName, String secondModuleName) {
        TestcaseEntity entity = testcaseMapper.selectSceneDefaultModule(productCode, topModuleName, secondModuleName);
        return testcaseConverter.covert(entity);
    }

    @Override
    public void updateTestcaseEnableByAutomaticSourceCode(List<String> automaticSourceCodes, Boolean enable, User transactor, TestcaseStatusEnum status) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andIn("automaticSourceCode", automaticSourceCodes);
        TestcaseEntity entity = new TestcaseEntity();
        entity.setEnable(enable);
        entity.setStatus(status);
        entity.setModifier(transactor.getUserName());
        entity.setModifierId(transactor.getUserId());
        entity.setGmtModified(new Date());
        testcaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<TestcaseEntityDO> queryCaseListByAutomaticCodes(List<String> automaticSourceCodes) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("type", TestcaseTypeEnum.AUTO.name())
                .andEqualTo("attribute", TestcaseAttributeEnum.TESTCASE.name())
                .andIn("automaticSourceCode", automaticSourceCodes);
        List<TestcaseEntity> entityList = testcaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : testcaseConverter.covertList(entityList);
    }

    @Override
    public List<String> queryDisableCaseCodeByDurationDate(Date durationDate) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.FALSE)
                .andLessThan("gmtModified", durationDate);
        example.selectProperties("code");
        List<TestcaseEntity> list = testcaseMapper.selectByExample(example);
        return Optional.ofNullable(list).orElse(Collections.emptyList())
                .stream().map(TestcaseEntity::getCode).collect(Collectors.toList());
    }

    @Override
    public void batchDeleteCaseByCode(List<String> codeList) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andIn("code", codeList);
        testcaseMapper.deleteByExample(example);
    }
}
