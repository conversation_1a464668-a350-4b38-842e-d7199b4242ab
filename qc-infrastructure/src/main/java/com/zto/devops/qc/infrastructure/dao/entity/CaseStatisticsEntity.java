package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "qc_case_statistics")
public class CaseStatisticsEntity extends BaseEntity {

    /**
     * 报告code
     */
    @Column(name = "report_code")
    private String reportCode;

    /**
     * 用例code
     */
    @Id
    private String code;

    @Column(name = "frog_plan_code")
    private String frogPlanCode;

    @Column(name = "frog_plan_name")
    private String frogPlanName;

    /**
     * 用例总数
     */
    @Column(name = "case_count")
    private Integer caseCount;

    /**
     * 计划冒烟数
     */
    @Column(name = "plan_smoke_case_count")
    private Integer planSmokeCaseCount;

    /**
     * 冒烟通过用例总数
     */
    @Column(name = "smoke_access_count")
    private Integer smokeAccessCount;

    private static final long serialVersionUID = 1L;

}