package com.zto.devops.qc.infrastructure.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.PlatformTransactionManager;


@Configuration
public class TransactionManagerConfig {

    @Autowired
    @Qualifier("lbd_qcTransactionManager")
    private PlatformTransactionManager commonTransactionManager;

    @Primary
    @Bean
    public PlatformTransactionManager defaultTransactionManager() {
        return commonTransactionManager;
    }

}

