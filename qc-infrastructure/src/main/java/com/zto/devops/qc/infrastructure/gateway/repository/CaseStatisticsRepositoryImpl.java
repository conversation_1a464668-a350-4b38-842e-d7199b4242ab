package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.qc.client.model.report.entity.CaseStatisticsVO;
import com.zto.devops.qc.domain.gateway.repository.CaseStatisticsRepository;
import com.zto.devops.qc.infrastructure.converter.CaseStatisticsConverter;
import com.zto.devops.qc.infrastructure.dao.entity.CaseStatisticsEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.CaseStatisticsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Component
public class CaseStatisticsRepositoryImpl implements CaseStatisticsRepository {

    @Autowired
    private CaseStatisticsMapper caseStatisticsMapper;

    @Autowired
    private CaseStatisticsConverter caseStatisticsConverter;

    @Override
    public List<CaseStatisticsVO> selectByReportCode(String code) {
        Example example = new Example(CaseStatisticsEntity.class);
        example.createCriteria()
                .andEqualTo("reportCode", code)
                .andEqualTo("enable", true);
        List<CaseStatisticsEntity> caseStatisticsEntities = caseStatisticsMapper.selectByExample(example);
        List<CaseStatisticsVO> voList = caseStatisticsConverter.convertCaseStatic(caseStatisticsEntities);
        return voList;
    }
}
