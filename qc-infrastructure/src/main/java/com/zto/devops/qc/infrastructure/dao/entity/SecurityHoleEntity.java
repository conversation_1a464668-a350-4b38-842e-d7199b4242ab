package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.report.SecurityLevel;
import com.zto.devops.qc.client.enums.report.SecurityStatus;
import com.zto.devops.qc.infrastructure.dao.typehandler.SecurityLevelHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.SecurityStatusHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "qc_security_hole")
public class SecurityHoleEntity extends BaseEntity {

    /**
     * 模块code
     */
    @Id
    private String code;

    /**
     * 报告code
     */
    @Column(name = "report_code")
    private String reportCode;

    /**
     * 漏洞名称
     */
    private String name;

    /**
     * 安全等级
     */
    @ColumnType(typeHandler = SecurityLevelHandler.class)
    private SecurityLevel level;

    /**
     * 漏洞危害
     */
    private String harm;

    /**
     * 漏洞备注
     */
    private String note;

    /**
     * 漏洞状态
     */
    @ColumnType(typeHandler = SecurityStatusHandler.class)
    private SecurityStatus status;

    /**
     * 漏洞详情id
     */
    @Column(name = "sec_id")
    private String secId;

    private static final long serialVersionUID = 1L;

}