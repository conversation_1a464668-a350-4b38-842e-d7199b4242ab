package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "tm_api_config")
public class ApiGlobalConfigurationEntity {

    @Id
    private Long id;

    @Column(name = "api_config_code")
    private String apiConfigCode;

    @Column(name = "product_code")
    private String productCode;

    @Column(name = "api_config_type")
    private Integer apiConfigType;

    @Column(name = "api_config_scope")
    private Integer apiConfigScope;

    @Column(name = "api_config_value")
    private String apiConfigValue;

    @Column(name = "api_config_assert")
    private String apiConfigAssert;

    @Column(name = "enable")
    private Integer enable;

    @Column(name = "creator_id")
    private Long creatorId;

    @Column(name = "modifier_id")
    private Long modifierId;

    @Column(name = "gmt_create")
    private Date gmtCreate;

    @Column(name = "gmt_modified")
    private Date gmtModified;

    @Column(name = "creator")
    private String creator;

    @Column(name = "modifier")
    private String modifier;
}
