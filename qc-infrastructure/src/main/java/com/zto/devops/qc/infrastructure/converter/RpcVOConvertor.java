package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.pipeline.client.model.application.entity.JacocoApplicationVO;
import com.zto.devops.pipeline.client.model.application.entity.PageApplicationVO;
import com.zto.devops.pipeline.client.model.application.query.PageApplicationQuery;
import com.zto.devops.pipeline.client.model.cluster.query.FindVersionByNamespaceQuery;
import com.zto.devops.pipeline.client.model.flow.entity.JacocoVersionVO;
import com.zto.devops.pipeline.client.model.flow.entity.VersionSimpleVO;
import com.zto.devops.pipeline.client.model.flow.query.FindOssFileUrlQuery;
import com.zto.devops.pipeline.client.model.flow.query.ListFlowVersionJacocoQuery;
import com.zto.devops.pipeline.client.model.flow.query.VersionReleaseBranchCommitQuery;
import com.zto.devops.pipeline.client.service.inner.model.NamespaceResp;
import com.zto.devops.product.client.model.product.entity.ProductMemberVO;
import com.zto.devops.product.client.model.product.query.ListProductMemberByPIdQuery;
import com.zto.devops.qc.client.model.issue.entity.HolidayVO;
import com.zto.devops.qc.client.model.issue.query.FindHolidayQuery;
import com.zto.devops.qc.client.model.rpc.pipeline.*;
import com.zto.devops.qc.client.model.rpc.pipeline.query.FindVersionFlowDateQuery;
import com.zto.devops.qc.client.model.rpc.product.*;
import com.zto.devops.qc.client.model.rpc.product.query.AllProductsQuery;
import com.zto.devops.qc.client.model.rpc.product.query.FindProductByIdQuery;
import com.zto.devops.qc.client.model.rpc.project.*;
import com.zto.devops.qc.client.model.rpc.user.ListUserQuery;
import com.zto.devops.qc.client.model.rpc.user.UserSelectVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/4/9
 * @Date 2023/3/24
 * @Version 1.0
 */
@Mapper(componentModel = "spring")
public interface RpcVOConvertor {

    com.zto.devops.project.client.model.requirement.query.SimpleListRequirementQuery covertSimpleListRequirementQuery(SimpleListRequirementQuery query);

    List<SimpleRequirementVO> covertSimpleRequirementVO(List<com.zto.devops.project.client.model.requirement.entity.SimpleRequirementVO> vo);

    com.zto.devops.project.client.model.sprint.query.SimpleListSprintQuery covertSimpleListSprintQuery(SimpleListSprintQuery query);

    List<SimpleSprintVO> covertSimpleSprintVO(List<com.zto.devops.project.client.model.sprint.entity.SimpleSprintVO> vos);

    com.zto.devops.project.client.model.version.query.SimpleVersionListQuery covertSimpleVersionListQuery(SimpleVersionListQuery query);

    List<SimpleVersionVO> covertSimpleVersionVO(List<com.zto.devops.project.client.model.version.entity.SimpleVersionVO> vos);

    RequirementVO convertRequirementVO(com.zto.devops.project.client.model.requirement.entity.RequirementVO vo);

    com.zto.devops.project.client.model.requirement.query.FindHolidayQuery covertFindHolidayQuery(FindHolidayQuery findHolidayQuery);

    List<HolidayVO> covertHolidayVO(List<com.zto.devops.project.client.model.requirement.entity.HolidayVO> rpcHolidayVOS);

    VersionInfoVO convertVersionInfoVO(com.zto.devops.project.client.model.version.entity.VersionInfoVO vo);

    VersionVO convertVersionVO(com.zto.devops.project.client.model.version.entity.VersionVO versionVO);

    com.zto.devops.user.client.model.user.query.ListUserQuery covertListUserQuery(ListUserQuery query);

    List<UserSelectVO> covertUserSelectVO(List<com.zto.devops.user.client.model.user.entity.UserSelectVO> vos);

    //    com.zto.devops.project.client.model.sprint.query.SimpleListSprintQuery covert(SimpleListSprintQuery query);
    List<com.zto.devops.qc.client.model.rpc.product.ProductMemberVO> converterProductMemberVOList(List<ProductMemberVO> voList);

    SimpleQueryVO convertSimpleQueryVO(com.zto.devops.product.client.model.product.entity.SimpleQueryVO simpleQueryVO);

    ListProductMemberByPIdQuery convertProductMemberQuery(com.zto.devops.qc.client.model.rpc.product.query.ListProductMemberByPIdQuery simpleQueryVO);

    FindOssFileUrlQuery convertFindOssFileUrlQuery(com.zto.devops.qc.client.model.rpc.pipeline.query.FindOssFileUrlQuery query);

    ListFlowVersionJacocoQuery convertListFlowVersionJacocoQuery(com.zto.devops.qc.client.model.rpc.pipeline.query.ListFlowVersionJacocoQuery query);

    com.zto.devops.qc.client.model.rpc.pipeline.JacocoVersionVO convertJacocoVersionVO(JacocoVersionVO jacocoVersionVO);

    List<com.zto.devops.qc.client.model.rpc.pipeline.JacocoApplicationVO> convertJacocoApplicationVOList(List<JacocoApplicationVO> jacocoVersionVO);

    PageApplicationQuery convertPageApplicationQuery(com.zto.devops.qc.client.model.rpc.pipeline.query.PageApplicationQuery query);

    com.zto.devops.qc.client.model.rpc.pipeline.PageApplicationVO convertPageApplicationVO(PageApplicationVO pageApplicationVO);

    List<RequirementDetailVo> convertRequirementDetailVo(List<com.zto.devops.project.client.model.requirement.entity.RequirementDetailVo> requirementByCodeListQuery);

    com.zto.devops.pipeline.client.model.flow.query.FindVersionFlowDateQuery coverterFindVersionFlowDateQuery(FindVersionFlowDateQuery query);

    com.zto.devops.qc.client.model.rpc.pipeline.VersionFlowDateVO coverterVersionFlowDateVO(com.zto.devops.pipeline.client.model.flow.entity.VersionFlowDateVO vo);

    com.zto.devops.product.client.model.product.query.FindProductByIdQuery coverterFindProductByIdQuery(FindProductByIdQuery query);

    com.zto.devops.product.client.model.product.query.AllProductsQuery convertAllProductsQuery(AllProductsQuery query);

    ProductVO convertProductVO(com.zto.devops.product.client.model.product.entity.ProductVO vo);

    ProductAllVO convertProductAllVO(com.zto.devops.product.client.model.product.entity.ProductAllVO vo);

    com.zto.devops.qc.client.model.rpc.pipeline.NamespaceResp covertNamespaceResp(NamespaceResp vo);

    List<PipelineProductVO> convertPipelineProductVO(List<com.zto.devops.product.client.model.product.entity.PipelineProductVO> list);

    List<ApplicationResp> convertApplicationResp(List<com.zto.devops.pipeline.client.service.inner.model.ApplicationResp> list);

    List<com.zto.devops.qc.client.model.rpc.product.ProductMemberVO> convertProductMemberVO(List<ProductMemberVO> productMembers);

    com.zto.devops.pipeline.client.model.flow.event.ExecuteCallbackCDEvent convertExecuteCallbackCDEvent(com.zto.devops.qc.client.model.testmanager.cases.event.ExecuteCallbackCDEvent event);

    SimpleProductQueryListVO convertSimpleQueryVO(com.zto.devops.product.client.model.product.entity.SimpleProductQueryListVO simpleProductQueryListVO);

    FlowBranchVO convertFlowBranchVO(com.zto.devops.pipeline.client.model.application.entity.FlowBranchVO flowBranchVO);

    List<FeatureVO> convertFeatureVOList(List<com.zto.devops.pipeline.client.model.plan.entity.FeatureVO> list);

    FlowBaseDetailVO convertFlowDetail(com.zto.devops.pipeline.client.model.flow.entity.FlowBaseDetailVO vo);

    com.zto.devops.project.client.model.version.query.FindVersionByActualPublishDateQuery covertActualPublishDateQuery(FindVersionByActualPublishDateQuery query);

    List<VersionVO> convertVersionVOS(List<com.zto.devops.project.client.model.version.entity.VersionVO> voList);

    com.zto.devops.qc.client.model.rpc.pipeline.VersionSimpleVO covertVersionSimpleVO(VersionSimpleVO vo);

    FindVersionByNamespaceQuery convert(com.zto.devops.qc.client.model.rpc.pipeline.query.FindVersionByNamespaceQuery query);

    FindVersionByNamespaceVO convert(com.zto.devops.pipeline.client.model.cluster.entity.FindVersionByNamespaceVO vo);

    com.zto.devops.qc.client.model.rpc.pipeline.ApplicationDetailResp convert(com.zto.devops.pipeline.client.service.application.model.ApplicationDetailResp resp);

    List<ApplicationVO> convertApplicationVOList(List<com.zto.devops.pipeline.client.model.application.entity.ApplicationVO> list);

    VersionContainSubVO convertVersionContainSubVO(com.zto.devops.pipeline.client.model.flow.entity.VersionContainSubVO versionByVersionCodeAndAppId);

    VersionReleaseBranchCommitQuery convert(com.zto.devops.qc.client.model.rpc.pipeline.query.VersionReleaseBranchCommitQuery query);

}
