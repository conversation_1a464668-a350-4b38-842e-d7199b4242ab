package com.zto.devops.qc.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR> *
 */
@ConfigurationProperties("qc.jmeter.config")
@Component
@Data
public class JmeterConfig {

    /**
     * 需要判断并修改重名的节点
     */
    private String uniqueNodes;

    /**
     * 控制器名称*
     */
    private String controllerNames;

    /**
     * 配置在devops-qc的每台机器上能执行调试任务的最大数量*
     */
    private Integer debugMaxCount;

    /**
     * 固定的变量提取器变量*
     */
    private String variable;

    private String zmsZKTestAddress;

    private String dubboZKTestAddress;

    private String debugWorkspace;

}
