package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import com.zto.devops.qc.domain.model.RelevantUser;
import com.zto.devops.qc.infrastructure.dao.entity.RelevantUserEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Mapper(componentModel = "spring")
public interface RelevantUserEntityConverter {
    RelevantUserEntityConverter INSTANCE = Mappers.getMapper(RelevantUserEntityConverter.class);

    List<RelevantUserEntity> convert(Collection<RelevantUserVO> vo);

    RelevantUserEntity convert(RelevantUserVO vo);

    RelevantUserVO convert(RelevantUserEntity entity);

    List<RelevantUserVO> convert(List<RelevantUserEntity> vo);

    Set<RelevantUser> convertList(Collection<RelevantUserEntity> relevantUserEntities);

}

