package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanRangeTypeEnum;
import com.zto.devops.qc.client.model.dto.TmTestPlanRangeEntityDO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.SimpleTmTestPlanVO;
import com.zto.devops.qc.domain.gateway.repository.TmTestPlanRangeRepository;
import com.zto.devops.qc.infrastructure.converter.TmTestPlanRangeEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanRangeEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TmTestPlanRangeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class TmTestPlanRangeRepositoryImpl implements TmTestPlanRangeRepository {

    @Autowired
    private TmTestPlanRangeMapper tmTestPlanRangeMapper;
    @Autowired
    private TmTestPlanRangeEntityConverter tmTestPlanRangeEntityConverter;

    @Override
    public List<TmTestPlanRangeEntityDO> selectByPlanCode(String planCode, Boolean enable) {
        Example example = new Example(TmTestPlanRangeEntity.class);
        example.createCriteria().andEqualTo("planCode", planCode).andEqualTo("enable", enable);
        List<TmTestPlanRangeEntity> entityList = tmTestPlanRangeMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : tmTestPlanRangeEntityConverter.convert2DOList(entityList);
    }

    @Override
    public TmTestPlanRangeEntityDO selectByPlanCodeAndTestRange(String planCode, TestPlanRangeTypeEnum rangeType) {
        Example exampleRange = new Example(TmTestPlanRangeEntity.class);
        exampleRange.createCriteria().andEqualTo("planCode", planCode).andEqualTo("testRange", rangeType);
        TmTestPlanRangeEntity rangeEntity = tmTestPlanRangeMapper.selectOneByExample(exampleRange);
        return tmTestPlanRangeEntityConverter.convert2DO(rangeEntity);
    }

    @Override
    public void updateByPrimaryKey(TmTestPlanRangeEntityDO rangeEntityDO) {
        TmTestPlanRangeEntity entity = tmTestPlanRangeEntityConverter.convert2Entity(rangeEntityDO);
        tmTestPlanRangeMapper.updateByPrimaryKey(entity);
    }

    @Override
    public List<String> selectAssociatedTestPlanCode() {
        return tmTestPlanRangeMapper.selectAssociatedTestPlanCode();
    }

    @Override
    public List<TmTestPlanRangeEntityDO> selectBySimpleTmTestPlanVO(SimpleTmTestPlanVO vo) {
        Example example = new Example(TmTestPlanRangeEntity.class);
        example.createCriteria()
                .andEqualTo("planCode", vo.getCode())
                .andEqualTo("enable", Boolean.TRUE);
        List<TmTestPlanRangeEntity> rangeEntities = tmTestPlanRangeMapper.selectByExample(example);
        return tmTestPlanRangeEntityConverter.convert2DOList(rangeEntities);
    }
}
