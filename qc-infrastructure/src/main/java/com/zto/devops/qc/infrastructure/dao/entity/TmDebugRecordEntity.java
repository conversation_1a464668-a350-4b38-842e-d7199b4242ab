package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticStatusHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Table(name = "tm_debug_record")
public class TmDebugRecordEntity extends BaseEntity {

    @Id
    private String taskId;

    private String debugCode;

    private String productCode;

    private String productName;

    @ColumnType(typeHandler = AutomaticStatusHandler.class)
    private AutomaticStatusEnum status;

    private String execLogFile;

    private Date startTime;

    private Date finishTime;
}
