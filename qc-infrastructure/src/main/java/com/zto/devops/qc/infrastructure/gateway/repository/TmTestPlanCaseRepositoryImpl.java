package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.model.dto.TmTestPlanCaseEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.PlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.SimpleTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindSortedPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageTestcaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.XmindFilterQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.DeletePlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseCodeQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseModuleQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.PlanCaseResultCountQuery;
import com.zto.devops.qc.domain.gateway.repository.ITmTestPlanCaseRepository;
import com.zto.devops.qc.infrastructure.converter.TmTestPlanCaseConvert;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanCaseEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TmTestPlanCaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class TmTestPlanCaseRepositoryImpl implements ITmTestPlanCaseRepository {

    @Autowired
    private TmTestPlanCaseMapper planCaseMapper;

    @Autowired
    private TmTestPlanCaseConvert tmTestPlanCaseConvert;

    @Override
    public List<TmTestPlanCaseEntityDO> selectTestPlanCaseByPlanCodeAndTestStage(String testPlanCode, List<TestPlanStageEnum> testStage) {
        Example example = new Example(TmTestPlanCaseEntity.class);
        example.selectProperties("caseCode", "testStage", "status");
        example.createCriteria()
                .andIn("testStage", testStage)
                .andEqualTo("planCode", testPlanCode);
        return tmTestPlanCaseConvert.covertList(planCaseMapper.selectByExample(example));
    }

    @Override
    public void batchSave(List<TmTestPlanCaseEntityDO> entityDOList) {
        planCaseMapper.batchSave(tmTestPlanCaseConvert.covertListReversal(entityDOList));
    }

    @Override
    public List<TmTestPlanCaseEntityDO> selectTmTestPlanCaseEntityDOListByCaseCodeList(List<String> codes) {
        Example planCaseExample = new Example(TmTestPlanCaseEntity.class);
        planCaseExample.createCriteria().andIn("caseCode", codes).andEqualTo("enable", 1);
        return tmTestPlanCaseConvert.covertList(planCaseMapper.selectByExample(planCaseExample));
    }

    @Override
    public List<TmTestPlanCaseEntityDO> selectListByStageAndType(PageTestcaseQuery query) {
        Example example = new Example(TmTestPlanCaseEntity.class);
        example.createCriteria()
                .andEqualTo("planCode", query.getPlanCode())
                .andEqualTo("testStage", query.getTestStage())
                .andEqualTo("caseType", query.getType())
                .andEqualTo("enable", true);
        List<TmTestPlanCaseEntity> planCaseList = planCaseMapper.selectByExample(example);
        return tmTestPlanCaseConvert.covertList(planCaseList);
    }

    @Override
    public List<String> selectOperateCaseCodeByCaseCode(String caseCode) {
        return planCaseMapper.selectOperateCaseCodeByCaseCode(caseCode);
    }

    @Override
    public List<TmTestPlanCaseEntityDO> getTestPlanCaseByCode(String code) {
        Example example = new Example(TmTestPlanCaseEntity.class);
        example.createCriteria().andEqualTo("caseCode", code)
                .andEqualTo("enable", true);
        List<TmTestPlanCaseEntity> testPlanCaseEntityList = planCaseMapper.selectByExample(example);
        return tmTestPlanCaseConvert.covertList(testPlanCaseEntityList);
    }

    @Override
    public void updateByPrimaryKeySelective(TmTestPlanCaseEntityDO tmTestPlanCaseEntityDO) {
        planCaseMapper.updateByPrimaryKeySelective(tmTestPlanCaseConvert.covert2Entity(tmTestPlanCaseEntityDO));
    }

    @Override
    public void delete(TmTestPlanCaseEntityDO tmTestPlanCaseEntityDO) {
        planCaseMapper.delete(tmTestPlanCaseConvert.covert2Entity(tmTestPlanCaseEntityDO));
    }

    @Override
    public List<DeletePlanCaseVO> selectPlanCaseByCodeList(List<String> codeList) {
        return planCaseMapper.selectPlanCaseByCodeList(codeList);
    }

    @Override
    public void updateByIdList(TmTestPlanCaseEntityDO tmTestPlanCaseEntityDO, List<Long> idList) {
        Example updateExample = new Example(TmTestPlanCaseEntity.class);
        updateExample.createCriteria().andIn("id", idList);
        planCaseMapper.updateByExampleSelective(tmTestPlanCaseConvert.covert2Entity(tmTestPlanCaseEntityDO), updateExample);
    }

    @Override
    public void deleteByIdList(List<Long> idList) {
        planCaseMapper.deleteByIdList(idList);
    }

    @Override
    public List<TmTestPlanCaseEntityDO> getTestPlanCaseList(XmindFilterQuery filterQuery, List<String> codeList) {
        Example example = new Example(TmTestPlanCaseEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("planCode", filterQuery.getPlanCode())
                .andEqualTo("testStage", filterQuery.getTestStage())
                .andEqualTo("enable", true)
                .andIn("caseCode", codeList);
        if (CollectionUtil.isNotEmpty(filterQuery.getResultList())) {
            criteria.andIn("status", filterQuery.getResultList());
        }
        if (CollectionUtil.isNotEmpty(filterQuery.getExecutorIdList())) {
            criteria.andIn("executorId", filterQuery.getExecutorIdList());
        }
        List<TmTestPlanCaseEntity> entityList = planCaseMapper.selectByExample(example);
        return tmTestPlanCaseConvert.covertList(entityList);
    }

    @Override
    public List<PlanCaseVO> selectPlanCase(FindPlanCaseQuery query) {
        return planCaseMapper.selectPlanCase(query);
    }

    @Override
    public List<TmTestPlanCaseEntityDO> getDistinctListByPlanCode(String planCode) {
        return tmTestPlanCaseConvert.covertList(planCaseMapper.selectDistinctListByPlanCode(planCode));
    }

    @Override
    public List<TestPlanCaseStatusEnum> findStatusList(PlanCaseResultCountQuery query) {
        return planCaseMapper.findStatusList(query);
    }

    @Override
    public List<TestPlanCaseVO> selectPlanCaseModuleList(ListPlanCaseModuleQuery listPlanCaseModuleQuery) {
        return planCaseMapper.selectPlanCaseModuleList(listPlanCaseModuleQuery);
    }

    @Override
    public List<TestPlanCaseVO> selectPlanCaseAutomaticModuleList(ListPlanCaseModuleQuery listPlanCaseModuleQuery) {
        return planCaseMapper.selectPlanCaseAutomaticModuleList(listPlanCaseModuleQuery);
    }

    @Override
    public List<SimpleTestcaseVO> selectTestcaseCodeList(ListPlanCaseCodeQuery query) {
        return planCaseMapper.selectTestcaseCodeList(query);
    }

    @Override
    public List<TestPlanCaseVO> getTestPlanCaseList(ListPlanCaseQuery query) {
        return planCaseMapper.testPlanCaseList(query);
    }

    @Override
    public List<PlanCaseVO> selectSimplePlanCase(FindSortedPlanCaseQuery query) {
        return planCaseMapper.selectSimplePlanCase(query);
    }

    @Override
    public List<TestPlanCaseVO> selectAllPlanCasePath(ListPlanCaseModuleQuery query) {
        return planCaseMapper.selectAllPlanCasePath(query);
    }

    @Override
    public void deleteByCaseCodeList(List<String> caseCodes, String planCode, TestPlanStageEnum stage) {
        Example example = new Example(TmTestPlanCaseEntity.class);
        example.createCriteria()
                .andEqualTo("planCode", planCode)
                .andEqualTo("testStage", stage)
                .andIn("caseCode", caseCodes);
        planCaseMapper.deleteByExample(example);
    }

    @Override
    public List<TmTestPlanCaseEntityDO> selectByPlanCodeAndTestStageAndCodeList(String planCode,
                                                                                TestPlanStageEnum stage,
                                                                                List<String> caseCodes) {
        Example example = new Example(TmTestPlanCaseEntity.class);
        example.createCriteria()
                .andEqualTo("planCode", planCode)
                .andEqualTo("testStage", stage)
                .andIn("caseCode", caseCodes)
                .andEqualTo("enable", 1);
        List<TmTestPlanCaseEntity> entityList = planCaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : tmTestPlanCaseConvert.convert2DOList(entityList);
    }

    @Override
    public void updateByPlanCodeAndTestStageAndCodeList(String planCode,
                                                        TestPlanStageEnum stage,
                                                        List<String> caseCodes,
                                                        TmTestPlanCaseEntityDO entityDO) {
        TmTestPlanCaseEntity toUpdateEntity = tmTestPlanCaseConvert.covert2Entity(entityDO);
        Example example = new Example(TmTestPlanCaseEntity.class);
        example.createCriteria()
                .andEqualTo("planCode", planCode)
                .andEqualTo("testStage", stage)
                .andIn("caseCode", caseCodes)
                .andEqualTo("enable", 1);
        planCaseMapper.updateByExampleSelective(toUpdateEntity, example);
    }

    @Override
    public List<String> selectVersionCodeByPlanCode(String planCode) {
        return planCaseMapper.selectVersionCodeByPlanCode(planCode);
    }

    @Override
    public Boolean verifyCaseNotStartedByPlanCodeAndTestStage(String code, List<TestPlanStageEnum> stageList) {
        List<TmTestPlanCaseEntityDO> caseEntityDOList = selectTestPlanCaseByPlanCodeAndTestStage(code, stageList);
        if (CollectionUtil.isEmpty(caseEntityDOList)) {
            return Boolean.TRUE;
        }
        long countNum = caseEntityDOList.stream().filter(item -> (Objects.isNull(item.getStatus())
                || TestPlanCaseStatusEnum.NOT_STARTED.equals(item.getStatus())
                || TestPlanCaseStatusEnum.INITIAL.equals(item.getStatus()))).count();
        return countNum > 0;
    }

    @Override
    public List<TestPlanCaseVO> queryDeletedPlanCase(ListPlanCaseQuery listPlanCaseQuery) {
        if (StringUtils.isNotBlank(listPlanCaseQuery.getParentCode())) {
            return Collections.emptyList();
        }
        if (StringUtils.isNotBlank(listPlanCaseQuery.getSearch())) {
            return Collections.emptyList();
        }
        if (CollectionUtil.isNotEmpty(listPlanCaseQuery.getStatusList())) {
            return Collections.emptyList();
        }
        if (CollectionUtil.isNotEmpty(listPlanCaseQuery.getExecutorIdList())) {
            return Collections.emptyList();
        }
        if (CollectionUtil.isNotEmpty(listPlanCaseQuery.getPriorityList())) {
            return Collections.emptyList();
        }
        if (CollectionUtil.isNotEmpty(listPlanCaseQuery.getNodeTypeList())) {
            return Collections.emptyList();
        }
        if (CollectionUtil.isNotEmpty(listPlanCaseQuery.getAutomaticTypeList())) {
            return Collections.emptyList();
        }
        if (CollectionUtil.isNotEmpty(listPlanCaseQuery.getTagList())) {
            return Collections.emptyList();
        }
        if (!(CollectionUtil.contains(listPlanCaseQuery.getSetCoreList(), Boolean.TRUE)
                || CollectionUtil.contains(listPlanCaseQuery.getCaseTypeList(), TestcaseTypeEnum.AUTO))) {
            return Collections.emptyList();
        }
        Example example = new Example(TmTestPlanCaseEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("enable", false)
                .andEqualTo("planCode", listPlanCaseQuery.getPlanCode());
        if (CollectionUtil.isNotEmpty(listPlanCaseQuery.getCaseTypeList())) {
            criteria.andIn("caseType", listPlanCaseQuery.getCaseTypeList());
        }
        if (CollectionUtil.isNotEmpty(listPlanCaseQuery.getTestPlanStageList())) {
            criteria.andIn("testStage", listPlanCaseQuery.getTestPlanStageList());
        }
        criteria.andCondition("case_code not in (select code from tm_testcase)");
        List<TestPlanCaseVO> list = tmTestPlanCaseConvert.convert2VOList(planCaseMapper.selectByExample(example));
        list.stream().filter(vo -> null == vo.getCaseName()).forEach(vo -> {
            vo.setCaseName("用例已删除，仅展示记录");
            vo.setCaseAttribute(TestcaseAttributeEnum.TESTCASE);
            vo.setEnableChecked(true);
        });
        return list;
    }

    @Override
    public List<TmTestPlanCaseEntityDO> queryPlanCaseByCaseCode(List<String> caseCodeList) {
        Example example = new Example(TmTestPlanCaseEntity.class);
        example.createCriteria().andIn("caseCode", caseCodeList);
        example.selectProperties("planCode", "caseCode");
        return tmTestPlanCaseConvert.convert2DOList(planCaseMapper.selectByExample(example));
    }

    @Override
    public List<TmTestPlanCaseEntityDO> selectByPlanCode(String planCode) {
        Example example = new Example(TmTestPlanCaseEntity.class);
        example.createCriteria().andEqualTo("planCode", planCode).andEqualTo("enable", true);
        return tmTestPlanCaseConvert.convert2DOList(planCaseMapper.selectByExample(example));
    }
}
