package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.Reason;
import com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.ReasonHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.StatusHandler;
import lombok.Data;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@ToString
@Table(name = "qc_transition_node")
@Data
public class TransitionNodeEntity extends BaseEntity {
    /**
     * 编码
     */
    @Id
    private String code;

    /**
     * 所属领域
     */
    @Column(name = "domain")
    @ColumnType(typeHandler = DomainHandler.class)
    private DomainEnum domain;

    /**
     * 业务编码
     */
    @Column(name = "business_code")
    private String businessCode;

    /**
     * 当前节点
     */
    @Column(name = "cur_status")
    @ColumnType(typeHandler = StatusHandler.class)
    private IssueStatus curStatus;

    /**
     * 节点内容json存储
     */
    private String content;

    @Column(name = "reason")
    @ColumnType(typeHandler = ReasonHandler.class)
    private Reason reason;
    /**
     * next节点
     */
    @Column(name = "next_status")
    @ColumnType(typeHandler = StatusHandler.class)
    private IssueStatus nextStatus;
    /**
     * 是否删除 1 未删除, 0 已删除
     */
    private Boolean enable;

    /**
     * 创建人编码
     */
    @Column(name = "creator_id")
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    @Column(name = "gmt_create")
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    @Column(name = "modifier_id")
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新时间
     */
    @Column(name = "gmt_modified")
    private Date gmtModified;

}