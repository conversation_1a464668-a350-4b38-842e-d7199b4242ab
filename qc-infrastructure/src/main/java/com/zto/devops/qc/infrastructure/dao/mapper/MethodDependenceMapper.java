package com.zto.devops.qc.infrastructure.dao.mapper;


import com.zto.devops.qc.infrastructure.dao.entity.MethodDependenceEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface MethodDependenceMapper extends Mapper<MethodDependenceEntity> {

    void batchSave(@Param("list") List<MethodDependenceEntity> list);

    Integer batchDelete(@Param("versionCode") String versionCode, @Param("appId") String appId, @Param("limit") Integer limit);
}