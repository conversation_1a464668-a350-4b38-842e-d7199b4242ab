
package com.zto.devops.qc.infrastructure.util;

import com.zto.devops.framework.client.enums.impexp.ExtEnum;
import com.zto.devops.framework.infrastructure.util.AmazonS3Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * framework s3工具类 (bucket:devops-file-resources）
 */
@Component
@Slf4j
public class AmazonS3PublishUtils {

    @Autowired
    protected AmazonS3Utils amazonS3Utils;

    public String upload(String data, ExtEnum ext, String fileName) {
        InputStream inputStream = new ByteArrayInputStream(data.getBytes());
        return amazonS3Utils.uploadFile(null, inputStream, ext, fileName);
    }

}
