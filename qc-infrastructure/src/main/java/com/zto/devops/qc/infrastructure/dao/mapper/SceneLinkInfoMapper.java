package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.infrastructure.dao.entity.SceneLinkInfoEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface SceneLinkInfoMapper extends Mapper<SceneLinkInfoEntity> {

    void batchInsert(@Param("list") List<SceneLinkInfoEntity> list);

    List<String> selectLinkCodes(@Param("sceneCode") String sceneCode, @Param("sceneVersion") Integer sceneVersion);

    List<SceneLinkInfoEntity> selectLinkInfos(@Param("linkMapCode") String linkMapCode);

//    List<ApiDocVersionVO> selectDocVersionList(@Param("productCode") String productCode, @Param("docVersion") String docVersion);
//
//    List<ApiLiteInfoVO> selectApiLiteGroupByName(@Param("productCode") String productCode, @Param("nameOrAddress") String nameOrAddress);
//
//    List<ApiLiteInfoVO> selectApiLiteGroupByAddress(@Param("productCode") String productCode, @Param("nameOrAddress") String nameOrAddress);
//
//    List<ApiTestEntity> selectApiList(PageApiReq req);

}
