package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AddAutomaticRecordEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticSuccessEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.EditAutomaticRecordEvent;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceRecordEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AutomaticSourceRecordEntityConverter {

    AutomaticRecordVO convertVO(AutomaticSourceRecordEntity entity);

    List<AutomaticRecordVO> convertVOList(List<AutomaticSourceRecordEntity> entityList);

    @Mapping(target = "personLiableId", source = "transactor.userId")
    @Mapping(target = "personLiable", source = "transactor.userName")
    AutomaticSourceRecordEntity convert(AddAutomaticRecordEvent event);

    AutomaticSourceRecordEntity convert(AutomaticSourceRecordEntityDO entityDO);

    AutomaticSourceRecordEntityDO convert(AutomaticSourceRecordEntity entity);

    List<AutomaticSourceRecordEntityDO> convert(List<AutomaticSourceRecordEntity> entityList);

    AutomaticSourceRecordEntity convert(EditAutomaticRecordEvent event);

    AutomaticSourceRecordEntity convert(AutomaticSuccessEvent event);
}
