package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.model.dto.AutomaticSchedulerEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.domain.gateway.repository.AutomaticSchedulerMsgRepository;
import com.zto.devops.qc.infrastructure.converter.AutomaticSchedulerEntityConverter;
import com.zto.devops.qc.infrastructure.converter.AutomaticTaskEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSchedulerCcEntity;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSchedulerEntity;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticTaskEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.AutomaticSchedulerCcMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.AutomaticSchedulerMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.AutomaticTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AutomaticSchedulerMsgRepositoryImpl implements AutomaticSchedulerMsgRepository {

    @Autowired
    private AutomaticTaskMapper automaticTaskMapper;

    @Autowired
    private AutomaticSchedulerMapper automaticSchedulerMapper;

    @Autowired
    private AutomaticSchedulerCcMapper automaticSchedulerCcMapper;

    @Autowired
    private AutomaticTaskEntityConverter automaticTaskEntityConverter;

    @Autowired
    private AutomaticSchedulerEntityConverter automaticSchedulerEntityConverter;

    @Override
    public AutomaticSchedulerEntityDO getSchedulerEntityDOByCode(String code) {
        AutomaticSchedulerEntity entity = automaticSchedulerMapper.selectByPrimaryKey(code);
        if (null != entity) {
            return automaticSchedulerEntityConverter.converter(entity);
        }
        return null;
    }

    @Override
    public List<AutomaticTaskEntityDO> getAllTaskByTaskId(String taskId) {
        Example example = new Example(AutomaticTaskEntity.class);
        example.createCriteria().andEqualTo("taskId", taskId)
                .andIn("trigMode", Arrays.asList(AutomaticTaskTrigModeEnum.SCHEDULER_ONCE, AutomaticTaskTrigModeEnum.SCHEDULER_RUN))
                .andEqualTo("enable", true);
        List<AutomaticTaskEntity> taskList = automaticTaskMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(taskList)) {
            return CollectionUtil.newEmptyList();
        }
        return automaticTaskEntityConverter.convert2DOList(taskList);
    }

    @Override
    public Set<String> getAllReceivedUsers(String schedulerCode, Long creatorId) {
        Example userExample = new Example(AutomaticSchedulerCcEntity.class);
        userExample.createCriteria()
                .andEqualTo("schedulerCode", schedulerCode)
                .andEqualTo("enable", true);
        List<AutomaticSchedulerCcEntity> userList = automaticSchedulerCcMapper.selectByExample(userExample);
        List<Long> userIds = userList.stream().map(AutomaticSchedulerCcEntity::getCcId).collect(Collectors.toList());
        if (!userIds.contains(creatorId)) {
            userIds.add(creatorId);
        }
        return userIds.stream().map(String::valueOf).collect(Collectors.toSet());
    }

}
