package com.zto.devops.qc.infrastructure.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import org.springframework.stereotype.Component;

@Component
public class ZCatConfig {

    private Config config = ConfigService.getAppConfig();

    public String queryAppIdTagsUrl() {
        return config.getProperty("zcat.queryAppIdTags.url", "");
    }

    public String queryZcatApiUrl() {
        return config.getProperty("zcat.api.url", "");
    }

    public String getZcatApiUrlProd() {
        return config.getProperty("zcat.api.url.prod", "");
    }

    public Integer queryDependencyServiceDays(){
        return Integer.valueOf(config.getProperty("zcat.dependencyService.queryDays","10"));
    }

}
