package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.report.*;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.*;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Table(name = "qc_test_report")
@Data
public class TestReportEntity extends BaseEntity {

    /**
     * 报告编号
     */
    @Id
    private String code;

    /**
     * 报告名称
     */
    private String name;

    /**
     * 计划编号
     */
    @Column(name = "plan_code")
    private String planCode;

    /**
     * 报告类型(准入、准出、简易...)
     */
    @Column(name = "report_type")
    @ColumnType(typeHandler = ReportTypeHandler.class)
    private ReportType reportType;

    /**
     * 版本code--(获取计划信息；产品信息、版本信息)
     */
    @Column(name = "version_code")
    private String versionCode;

    /**
     * 版本名称
     */
    @Column(name = "version_name")
    private String versionName;

    /**
     * 总体测试结果
     */
    @Column(name = "test_result")
    //private Integer testResult;
    @ColumnType(typeHandler = TestResultHandler.class)
    private TestResultEunm testResult;

    /**
     * 实际提测时间
     */
    @Column(name = "actual_presentation_date")
    private Date actualPresentationDate;



    /**
     * 实际准出时间
     */
    @Column(name = "actual_approval_exit_date")
    private Date actualApprovalExitDate;



    /**
     * 实际上线时间
     */
    @Column(name = "actual_publish_date")
    private Date actualPublishDate;

    /**
     * 实际测试开始时间
     */
    @Column(name = "actual_test_start")
    private Date actualTestStart;

    /**
     * 实际测试结束时间
     */
    @Column(name = "actual_test_end")
    private Date actualTestEnd;

    /**
     * 验收开始时间
     */
    @Column(name = "check_start_date")
    private Date checkStartDate;

    /**
     * 验收结束时间
     */
    @Column(name = "check_end_date")
    private Date checkEndDate;

    /**
     * 更新测试结果时间
     */
    @Column(name = "update_test_result_date")
    private Date updateTestResultDate;

    /**
     * 自动化测试结果--未执行
     */
    @Column(name = "auto_test_result")
    @ColumnType(typeHandler = AutoExecuteResultHandler.class)
    private AutoExecuteResult autoTestResult;

    /**
     * 安全测试人ID
     */
    @Column(name = "security_user_id")
    private Long securityUserId;

    /**
     * 安全测试人名称
     */
    @Column(name = "security_user_name")
    private String securityUserName;

    /**
     * 安全测试结果
     */
    @Column(name = "security_test_result")
    @ColumnType(typeHandler = SecurityTestResultHandler.class)
    private SecurityTestResult securityTestResult;

    /**
     * 验收类型 外采，内部
     */
    @Column(name = "check_type")
    @ColumnType(typeHandler = CheckTypeHandler.class)
    private CheckType checkType;

    /**
     * 开发人数
     */
    @Column(name = "developer_count")
    private Integer developerCount;

    /**
     * 测试人数
     */
    @Column(name = "tester_count")
    private Integer testerCount;

    /**
     * 计划冒烟用例数
     */
    @Column(name = "plan_smoke_case")
    private Integer planSmokeCase;

    /**
     * 首次通过冒烟用例数
     */
    @Column(name = "first_permit_smoke")
    private Integer firstPermitSmoke;

    /**
     * 按计划范围上线  1 是, 0 否
     */
    @Column(name = "as_planed_online")
    private Integer asPlanedOnline;

    /**
     * 是否延期 1 是, 0 否
     */
    private Integer delay;

    /**
     * 总结、分析、描述
     */
    private String summary;

    /**
     * 状态： 草稿， 已发送 未发送
     */
    @Column(name = "status")
    @ColumnType(typeHandler = TestPlanStatusHandler.class)
    private TestPlanStatusEnum status;

    /**
     * 预览
     */
    @Column(name = "preview")
    private String preview;

    /**
     * 代码覆盖率结果
     */
    @Column(name = "code_cover_result")
    @ColumnType(typeHandler = CodeCoverResultHandler.class)
    private CodeCoverResult codeCoverResult;

    /**
     * 代码覆盖率不达标原因
     */
    @Transient
    @Column(name = "code_cover_reason")
    private List<Map<String, String>> codeCoverReason;

    private static final long serialVersionUID = 1L;
}
