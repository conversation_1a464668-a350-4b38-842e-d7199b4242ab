package com.zto.devops.qc.infrastructure.gateway.xmind;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.infrastructure.util.HttpClientUtils;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExcelColumn;
import com.zto.devops.qc.client.service.testmanager.cases.model.ImportDataResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.TestCaseExcelColumn;
import com.zto.devops.qc.client.service.testmanager.cases.model.XMindNodeModel;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.util.UncompressFileUtilService;
import com.zto.devops.qc.domain.gateway.xmind.XmindService;
import com.zto.titans.common.exception.ErrorCodeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.conn.ConnectTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.xmind.core.*;
import org.xmind.core.io.ByteArrayStorage;
import org.xmind.core.marker.IMarkerRef;

import java.io.*;
import java.util.*;

/**
 * @Author: wxc
 * @Description:
 * @Date: Create in 下午3:04 2021/4/25
 */
@Slf4j
@Component
public class XmindServiceImpl implements XmindService {

    public static final String CONTENT_JSON = "content.json";
    //当前文件位置
    private static final String CURRENT_PATH = "/tmp/";
    //定义分隔符,随便定义了
    private static final String SEPARATION = "#####";

    private static final String regex = "^\\b\\u6B65\\u9AA4([1-9]|[1-9][0-9]|1[0-9][0-9]|200)\\b[:|：][\\w\\W]*";

    private static final String EXCEPT = "^\\b\\u9884\\u671f\\u7ed3\\u679c([1-9]|[1-9][0-9]|1[0-9][0-9]|200)\\b[:|：][\\w\\W]*";

    @Autowired
    private QcConfigBasicService qcConfig;
    @Autowired
    private UncompressFileUtilService uncompressFileUtilService;

    public byte[] getFile(String url) {
        byte[] importExcelBytes = null;
        try {
            if ("2".equals(qcConfig.getQcConfig().getEnv())) {
                url.replace("https://fscdn.zto.com", "http://newfs.ztosys.com");
            }
            log.info("env is {} url is {}", System.getProperty("env"), url);
            // 下载文件
            importExcelBytes = HttpClientUtils.sendFileDownload(url, null, 500, 1000);
        } catch (ConnectTimeoutException e4) {
            log.warn("第一次导入信息失败!解析的数据文件路径[{}]", url, e4);
            // 替换预发的域名重新尝试
            String url2 = url.replace("https://fscdn.zto.com", "http://newfs.ztosys.com");
            log.info("postComplainImportCreate url2==> {}", url2);
            try {
                importExcelBytes = HttpClientUtils.sendFileDownload(url2, null, 1000, 2000);
            } catch (IOException e3) {
                log.error("第二次导入信息失败!解析的数据文件路径url2[{}]", url2, e3);
                throw new ServiceException("导入文件解析失败");
            }
        } catch (IOException e) {
            log.warn("第一次导入信息失败!解析的数据文件路径[{}]", url, e);
        } catch (ErrorCodeException e2) {
            log.warn("第一次导入信息失败!解析的数据文件路径[{}]", url, e2);
            // 替换预发的域名重新尝试
            String url2 = url.replaceAll(".*/dfsproxy/", "http://newfs.ztosys.com/");
            log.info("postComplainImportCreate url2==> {}", url2);
            try {
                importExcelBytes = HttpClientUtils.sendFileDownload(url2, null, 1000, 2000);
            } catch (IOException e3) {
                log.error("第二次导入信息失败!解析的数据文件路径url2[{}]", url2, e3);
                throw new ServiceException("导入文件解析失败");
            }
        }
        return importExcelBytes;
    }

    @Override
    public ImportDataResp xMindWriterTestcase(String url) {
        byte[] bs = getFile(url);
        String sourceFile = CURRENT_PATH + "XMind" + System.currentTimeMillis() + ".xmind";
        writeBytesToFile(bs, sourceFile);
        List<ExcelColumn> lists = new ArrayList<>();
        try {
            lists = this.getList(new File(sourceFile));
        } catch (Exception e) {
            log.error("", e);
            if (StringUtil.isNotBlank(e.getMessage())) {
                String errorMsg = e.getMessage().replace("com.zto.devops.framework.client.exception.ServiceException: =", "");
                return ImportDataResp.builder().importFlag(true).errorMsg(errorMsg).build();
            }
        }

        List<TestCaseExcelColumn> testCaseVOList = new ArrayList<>();
        for (ExcelColumn excelColumn : lists) {
            TestCaseExcelColumn testCaseVO = new TestCaseExcelColumn();
            testCaseVO.setName(excelColumn.getCaseName());
            testCaseVO.setParentFullName(excelColumn.getContains());
            testCaseVO.setPriorityDesc(excelColumn.getPriority());
            testCaseVO.setTagName(excelColumn.getTag());
            testCaseVO.setPrecondition(excelColumn.getPrecondition());
            testCaseVO.setStepDesc(excelColumn.getSteps());
            testCaseVO.setExpectResult(excelColumn.getExpectResult());
            testCaseVO.setComment(excelColumn.getRemark());
            if (StringUtil.isNotBlank(excelColumn.getException())) {
                testCaseVO.setCheckFailReason("节点：" + excelColumn.getException() + " 不符合模板格式无法识别！");
            }
            testCaseVOList.add(testCaseVO);

        }
        return ImportDataResp.builder().importFlag(true).data(testCaseVOList).build();
    }

    public List<ExcelColumn> getList(File file) throws Exception {
        Object obj = this.readXMind(file);
        if (obj != null) {
            XMindNodeModel node = JSON.parseObject(obj.toString(), XMindNodeModel.class);
            Stack<XMindNodeModel> pathStack = new Stack();
            HashMap<String, ExcelColumn> caseNodeModelMap = new HashMap<>();

            List<ExcelColumn> listCol = new ArrayList();
            List<ExcelColumn> columnList = iteratorNode(node, pathStack, caseNodeModelMap, listCol);
            return columnList;
        }
        return null;
    }

    /**
     * 想读XMind，读就完了
     */
    public Object readXMind(File file) {
        String res = null;
        Object contents = null;
        try {
            res = uncompressFileUtilService.extract(file);
            if (isXMindZen(res)) {
                contents = getXMindZenContent(new File(res), res);
            } else {
                contents = getXMind8Content(new File(res));
            }
        } catch (Exception e) {
            log.error("", e);
        } finally {
            File dir = new File(res);
            deleteDir(file);
            deleteDir(dir);
        }
        return contents;
    }

    /**
     * 填充节点
     *
     * @param iTopic
     * @return 一个节点
     */
    public static XMindNodeModel fillNode(ITopic iTopic) {
        XMindNodeModel newNode = new XMindNodeModel();
        try {
            //内容
            newNode.setContent(iTopic.getTitleText());
            //链接
            newNode.setLink(iTopic.getHyperlink());
            //标签
            Set<String> labels = iTopic.getLabels();
            if (labels != null && labels.size() != 0) {
                newNode.setLabels(labels);
            }
            //图片,暂时不支持吧 IImage image = iTopic.getImage();
            //如果有图片，这个不需要填充了，并且用例名称没有     newNode.setIImage(image.getSource());

            //批注
            Set<IMarkerRef> markers = iTopic.getMarkerRefs();
            if (markers != null && !markers.isEmpty()) {
                for (IMarkerRef iMarkerRef : markers) {
                    //这里定死 优先级为1的打标 priority-1
                    if ("priority-1".equals(iMarkerRef.getMarkerId())) {
                        newNode.setMarkers(true);//如果有批注，则给个高优先级
                    }
                }
            }
            //备注
            IPlainNotesContent content = (IPlainNotesContent) iTopic.getNotes().getContent(INotes.PLAIN);
            if (content != null) {
                newNode.setComment(content.getTextContent());
            }
        } catch (Exception e) {
            log.error("*****填充node出错*****", e);
        }
        return newNode;
    }

    /**
     * 填充节点
     *
     * @param attachedObject
     * @return 一个节点
     */
    public static XMindNodeModel fillNodeNetwork(JSONObject attachedObject) {
        XMindNodeModel newNode = new XMindNodeModel();
        try {
            JSONObject notes = attachedObject.getJSONObject("notes");
            if (notes != null) {
                JSONObject plain = notes.getJSONObject("plain");
                if (plain != null) {
                    newNode.setComment(plain.getString("content"));
                }
            }
            newNode.setContent(attachedObject.getString("title"));
            newNode.setLabels(Collections.singleton(attachedObject.getString("labels")));
        } catch (Exception e) {
            log.error("*****填充nodeNetwork出错*****", e);
        }
        return newNode;
    }

    /**
     * 建立文件内容树
     *
     * @param file
     * @return json串
     */
    public static Object getXMind8Content(File file) {
        //初始化builder
        IWorkbookBuilder builder = Core.getWorkbookBuilder();
        IWorkbook workbook = null;
        try {
            //打开XMind文件
            workbook = builder.loadFromFile(file, new ByteArrayStorage(), null);
        } catch (IOException | CoreException e) {
            log.error("", e);
        }
        if (workbook == null) {
            return "文件为空或格式不匹配";
        }
        //获取主Sheet
        ISheet defSheet = workbook.getPrimarySheet();
        //获取根Topic
        ITopic rootTopic = defSheet.getRootTopic();
        //遍历树
        Queue<Object> iTopicQueue = new LinkedList<>();
        iTopicQueue.add(rootTopic);
        //新树
        XMindNodeModel xMindNodeRoot = new XMindNodeModel();
        xMindNodeRoot.setContent(rootTopic.getTitleText());
        xMindNodeRoot.setLevel(1);
        xMindNodeRoot.setUuid(UUID.randomUUID().toString());
        iTopicQueue.add(xMindNodeRoot);
        while (!iTopicQueue.isEmpty()) {
            ITopic oldTreeNode = (ITopic) iTopicQueue.poll();
            XMindNodeModel treeNode = (XMindNodeModel) iTopicQueue.poll();
            if (treeNode == null) {
                return null;
            }
            Integer level = treeNode.getLevel() + 1;
            List<XMindNodeModel> xMindNodeModels = new ArrayList<>();
            for (ITopic oneChild : oldTreeNode.getAllChildren()) {
                XMindNodeModel newNode = XmindServiceImpl.fillNode(oneChild);
                //如果没有content,去除
                if (StringUtils.isNotBlank(newNode.getContent())) {
                    newNode.setLevel(level);
                    newNode.setUuid(UUID.randomUUID().toString());
                    xMindNodeModels.add(newNode);
                    iTopicQueue.add(oneChild);
                    iTopicQueue.add(newNode);
                }
            }
            treeNode.setChildren(xMindNodeModels);
        }
        return JSON.toJSON(xMindNodeRoot);
    }

    /**
     * 获取zen内容
     *
     * @param file
     * @return json串
     */
    public static Object getXMindZenContent(File file, String extractFileDir)
            throws IOException {
        List<String> keys = new ArrayList<>();
        keys.add(CONTENT_JSON);
        Map<String, String> map = getContents(keys, file, extractFileDir);
        String content = map.get(CONTENT_JSON);
        JSONArray jsonArray = JSONArray.parseArray(content);
        JSONObject jsonObject = (JSONObject) jsonArray.get(0);
        JSONObject rootTopic = jsonObject.getJSONObject("rootTopic");

        //遍历树
        Queue<Object> iTopicQueue = new LinkedList<>();
        iTopicQueue.add(rootTopic);
        //新树
        XMindNodeModel xMindNodeRoot = new XMindNodeModel();
        xMindNodeRoot.setContent(rootTopic.getString("title"));
        xMindNodeRoot.setLevel(1);
        iTopicQueue.add(xMindNodeRoot);
        while (!iTopicQueue.isEmpty()) {
            JSONObject oldTreeNode = (JSONObject) iTopicQueue.poll();
            XMindNodeModel treeNode = (XMindNodeModel) iTopicQueue.poll();
            if (treeNode == null) {
                return null;
            }
            Integer level = treeNode.getLevel() + 1;
            List<XMindNodeModel> xMindNodeModels = new ArrayList<>();
            JSONObject children = oldTreeNode.getJSONObject("children");
            if (children == null) {
                continue;
            }
            JSONArray attachedArray = children.getJSONArray("attached");
            if (attachedArray == null) {
                continue;
            }
            for (Object attached : attachedArray) {
                JSONObject attachedObject = (JSONObject) attached;
                XMindNodeModel newNode = XmindServiceImpl.fillNodeNetwork(attachedObject);
                newNode.setLevel(level);
                newNode.setUuid(UUID.randomUUID().toString());
                xMindNodeModels.add(newNode);
                iTopicQueue.add(attachedObject);
                iTopicQueue.add(newNode);
            }
            treeNode.setChildren(xMindNodeModels);
        }
        return JSON.toJSON(xMindNodeRoot);
    }

    /**
     * 判断是否zen类型
     */
    private static boolean isXMindZen(String res) {
        // 解压
        File parent = new File(res);
        if (parent.isDirectory()) {
            String[] files = parent.list(new FileFilter());
            for (int i = 0; i < Objects.requireNonNull(files).length; i++) {
                if (files[i].equals(CONTENT_JSON)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 找CONTENT_JSON文件
     */
    public static Map<String, String> getContents(List<String> subFileNames, File file, String extractFileDir)
            throws IOException {
        String destFilePath = extractFileDir;
        Map<String, String> map = new HashMap<>(16);
        File destFile = new File(destFilePath);
        if (destFile.isDirectory()) {
            String[] res = destFile.list(new FileFilter());
            for (int i = 0; i < Objects.requireNonNull(res).length; i++) {
                if (subFileNames.contains(res[i])) {
                    String s = destFilePath + File.separator + res[i];
                    String content = getFileContent(s);
                    map.put(res[i], content);
                }
            }
        }
        return map;
    }

    /**
     * 返回CONTENT_JSON文件内容
     */
    public static String getFileContent(String fileName) throws IOException {
        File file;
        FileReader fileReader = null;
        BufferedReader bufferedReder = null;
        try {
            file = new File(fileName);
        } catch (Exception e) {
            throw new RuntimeException("找不到该文件");
        }
        try {
            fileReader = new FileReader(file);
            bufferedReder = new BufferedReader(fileReader);
            StringBuilder stringBuffer = new StringBuilder();
            while (bufferedReder.ready()) {
                stringBuffer.append(bufferedReder.readLine());
            }
            return stringBuffer.toString();
        } catch (IOException e) {
            log.error("", e);
        } finally {
            // 打开的文件需关闭，在unix下可以删除，否则在windows下不能删除（file.delete())
            if (bufferedReder != null) {
                bufferedReder.close();
            }
            if (fileReader != null) {
                fileReader.close();
            }
        }
        return "";
    }

    /**
     * 过滤器
     */
    static class FileFilter implements FilenameFilter {
        @Override
        public boolean accept(File dir, String name) {
            // String的 endsWith(String str)方法 筛选出以str结尾的字符串
            if (name.endsWith(".xml") || name.endsWith(".json")) {
                return true;
            }
            return false;
        }
    }

    /**
     * 删除生成的文件夹
     */
    public static boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            String[] children = dir.list();
            // 递归删除目录中的子目录下
            for (int i = 0; i < children.length; i++) {
                boolean success = deleteDir(new File(dir, children[i]));
                if (!success) {
                    return false;
                }
            }
        }
        // 目录此时为空，可以删除
        return dir.delete();
    }

    //保存全路径
    private static String getPath(List list) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            XMindNodeModel n = (XMindNodeModel) list.get(i);
            if (i == list.size() - 1) {
                sb.append(n.getContent());
            } else {
                sb.append(n.getContent());
                sb.append(SEPARATION);
            }
        }
        return sb.toString();
    }

    private static List<ExcelColumn> iteratorNode(XMindNodeModel n, Stack<XMindNodeModel> pathStack, HashMap<String, ExcelColumn> caseNodeModelMap, List<ExcelColumn> listCol) throws Exception {
        pathStack.push(n);//入栈
        List childList = n.getChildren();
        if (CollectionUtil.isEmpty(childList)) {//没有孩子，说明是叶子节点
            List list = new ArrayList();
            Iterator stackIt = pathStack.iterator();
            while (stackIt.hasNext()) {
                list.add(stackIt.next());
            }
            ExcelColumn excelColumn = new ExcelColumn();
            excelColumn.setUuid(UUID.randomUUID().toString());
            excelColumn.setIndex(1);

            Iterator stackItV1 = pathStack.iterator();
            while (stackItV1.hasNext()) {
                XMindNodeModel xMindNodeModel = (XMindNodeModel) stackItV1.next();
                String content = xMindNodeModel.getContent();
                if (content.contains("标题：") || content.contains("标题:")
                        || content.contains("标题-高：") || content.contains("标题-高:")
                        || content.contains("标题-中：") || content.contains("标题-中:")
                        || content.contains("标题-低：") || content.contains("标题-低:")) {
                    ExcelColumn value = caseNodeModelMap.get(xMindNodeModel.getUuid());
                    if (value != null) {
                        excelColumn = value;
                    }
                    caseNodeModelMap.put(xMindNodeModel.getUuid(), excelColumn);
                    break;
                }
            }

            //内容过滤
            filterContent(excelColumn, getPath(list));

            if (!listCol.contains(excelColumn)) {
                listCol.add(excelColumn);
            }
        } else {
            Iterator it = childList.iterator();
            while (it.hasNext()) {
                XMindNodeModel child = (XMindNodeModel) it.next();
                iteratorNode(child, pathStack, caseNodeModelMap, listCol);//深度优先，进入递归
                pathStack.pop();//回溯时候出站
            }
        }
        return listCol;
    }

    //xmind内容过滤
    private static void filterContent(ExcelColumn excelColumn, String content) throws Exception {
        //全路径里面有的内容，根据分隔符截取
        String[] strs = content.split(SEPARATION);
        //前两层定义为用例所属
        String contains = "";
        //第三层开始判断 是否 包含 前置条件：，步骤描述：，包含预期结果：
        StringBuilder sbPrecondition = new StringBuilder();//前置条件
        StringBuilder sbStepIndex = new StringBuilder();//步骤+序号描述
        StringBuilder sbSteps = new StringBuilder();//步骤：描述
        StringBuilder sbExpect = new StringBuilder();//预期结果
        StringBuilder sbCaseName = new StringBuilder();//用例描述
        StringBuilder sbException = new StringBuilder();//异常
        String priority = "中";
        String remark = "";
        if (strs.length < 2) {
            throw new Exception("模板层级小于2，请检查模板！！！");
        }
        int containsIndex = strs.length;
        for (int i = 1; i < strs.length; i++) {
            String variable = strs[i].trim();
            if (containStr(variable, "标题") || containStr(variable, "标题-高")
                    || containStr(variable, "标题-中") || containStr(variable, "标题-低")
                    || containStr(variable, "前置条件") || variable.matches(regex)
                    || containStr(variable, "预期结果") || containStr(variable, "备注")) {
                containsIndex = i;
                break;
            } else {//拼接分组
                if (StringUtil.isBlank(contains)) {
                    contains = variable;
                } else {
                    contains = contains + ">" + variable;
                }
            }
        }
        excelColumn.setContains(contains);

        for (int i = containsIndex; i < strs.length; i++) {
            String variable = strs[i].trim();
            if (containStr(variable, "标题")) {
                sbCaseName.append(variable);
            } else if (containStr(variable, "标题-高")) {
                sbCaseName.append(variable);
                priority = "高";
            } else if (containStr(variable, "标题-中")) {
                sbCaseName.append(variable);
                priority = "中";
            } else if (containStr(variable, "标题-低")) {
                sbCaseName.append(variable);
                priority = "低";
            } else if (containStr(variable, "前置条件")) {
                sbPrecondition.append(variable);
            } else if (variable.matches(regex)) {
                sbStepIndex.append(variable);
            } else if (containStr(variable, "步骤")) {
                sbSteps.append(variable);
            } else if (containStr(variable, "预期结果") || match(strs, i)) { // 末节点的上一节点是步骤或者是标题
                sbExpect.append(variable);
                // 如果上一节点是标题需要将用例标题填充到步骤中
                if (matchTitle(strs, i)) {
                    String step = "步骤" + excelColumn.getIndex();
                    sbStepIndex.append(strs[i - 1].trim()
                            .replaceFirst("标题-高", step)
                            .replaceFirst("标题-中", step)
                            .replaceFirst("标题-低", step)
                            .replaceFirst("标题", step));
                    // 步骤index+1
                    excelColumn.setIndex(excelColumn.getIndex() + 1);
                }
                if (matchSteps(strs, i) && (StringUtils.isEmpty(excelColumn.getSteps()) || excelColumn.getIndex() != 1)) {
                    if (StringUtils.isEmpty(sbSteps)) {
                        String step = excuteStepString(strs, i, excelColumn.getIndex());
                        sbStepIndex.replace(0, sbStepIndex.length(), step.trim());
                        excelColumn.setIndex(excelColumn.getIndex() + 1);
                    }
                }
            } else if (containStr(variable, "备注")) {
                if (StringUtil.isBlank(remark)) {
                    remark = variable;
                }
            } else {
                if (StringUtil.isNotBlank(sbException)) {
                    sbException.append("、" + variable);
                } else {
                    sbException.append(variable);
                }
            }
        }
        String caseName = sbCaseName.toString().trim()
                .replace("标题：", "")
                .replace("标题:", "")
                .replace("标题-高：", "")
                .replace("标题-高:", "")
                .replace("标题-中：", "")
                .replace("标题-中:", "")
                .replace("标题-低：", "")
                .replace("标题-低:", "");
        excelColumn.setCaseName(caseName);
        excelColumn.setPriority(priority);
        //处理：前置条件：替换成空，支持多个前置条件，','
        if (StringUtils.isNotBlank(sbPrecondition)) {
            String precondition = sbPrecondition.toString().trim().replace("前置条件：", "").replace("前置条件:", "");
            excelColumn.setPrecondition(precondition);
        }
        if (StringUtils.isNotBlank(sbSteps)) {
            String steps = "1：" + sbSteps.toString().trim().replaceFirst("步骤：", "").replaceFirst("步骤:", "").replaceAll("\\r\\n|\\n|\\r", "").trim();
            excelColumn.setSteps((StringUtil.isNotBlank(excelColumn.getSteps()) ?
                    (excelColumn.getSteps().equals(steps) ? steps : (excelColumn.getSteps() + "\n" + steps)) : steps));
        }
        if (StringUtils.isNotBlank(sbStepIndex)) {
            if (!sbStepIndex.toString().contains("\r\n") && sbStepIndex.toString().contains("\n")) {
                sbStepIndex.replace(0, sbStepIndex.length(), sbStepIndex.toString().replaceAll("\n", "\r\n"));
            }
            String[] stepArr = sbStepIndex.toString().split("\\r\\n");
            if (stepArr.length <= 1) {
                // 只有一个步骤
                //处理：步骤描述：替换成空，多个步骤描述，暂时不支持
                String steps = sbStepIndex.toString().trim().replaceFirst("步骤", "").replaceAll("\\r\\n|\\n|\\r", "").trim();
                excelColumn.setSteps((StringUtil.isNotBlank(excelColumn.getSteps()) ? (excelColumn.getSteps() + "\n" + steps) : steps));
            } else {
                // 多个步骤\n拼接
                StringBuilder stepsBuilder = new StringBuilder();
                for (String str : stepArr) {
                    stepsBuilder.append("\n").append(str.trim().replaceFirst("步骤", ""));
                }
                String steps = stepsBuilder.toString().trim();
                excelColumn.setSteps((StringUtil.isNotBlank(excelColumn.getSteps()) ?
                        (excelColumn.getSteps().equals(steps) ? steps : (excelColumn.getSteps() + "\n" + steps)) : steps));
            }
        } else {//没有写步骤，把用例名称设置为步骤
//            excelColumn.setSteps(caseName);
        }
        if (StringUtils.isNotBlank(sbExpect)) {
            if (!sbExpect.toString().contains("\r\n") && sbExpect.toString().contains("\n")) {
                sbExpect.replace(0, sbExpect.length(), sbExpect.toString().replaceAll("\n", "\r\n"));
            }
            String[] expectArr = sbExpect.toString().split("\\r\\n");
            if (expectArr.length <= 1) {
                // 只有一个结果
                // 处理：预期结果： 替换成空，多个预期结果暂时不支持
                String expect = "";
                if (sbExpect.toString().matches(EXCEPT)) {
                    expect = "：" + sbExpect.toString()
                            .replaceFirst("预期结果([1-9]|[1-9][0-9]|1[0-9][0-9]|200):", "")
                            .replaceFirst("预期结果([1-9]|[1-9][0-9]|1[0-9][0-9]|200)：", "")
                            .replaceAll("\\r\\n|\\n|\\r", "")
                            .trim();
                } else {
                    expect = "：" + sbExpect.toString()
                            .replaceFirst("预期结果:", "")
                            .replaceFirst("预期结果：", "")
                            .replaceAll("\\r\\n|\\n|\\r", "")
                            .trim();
                }
                if (StringUtils.isNotBlank(sbStepIndex)) {
                    String steps = sbStepIndex.toString().trim().replaceFirst("步骤", "");
                    List<String> specialCharacterList = Arrays.asList(":", "：");
                    int index = getSpecialCharacterIndex(specialCharacterList, steps);
                    String num = steps.substring(0, index);
                    expect = num + expect;
                }
                if (StringUtils.isNotBlank(sbSteps)) {
                    expect = "1" + expect;
                }
                excelColumn.setExpectResult(StringUtil.isNotBlank(excelColumn.getExpectResult()) ? (excelColumn.getExpectResult() + "\n" + expect) : expect);
            } else {
                String expect = "";
                if (sbExpect.toString().contains("预期结果：") || sbExpect.toString().contains("预期结果:")) {
                    // 多个预期结果但是写在一起
                    expect = "1:" + sbExpect.toString().trim().replaceFirst("预期结果：", "").replaceFirst("预期结果:", "").replaceAll("\\r\\n|\\n|\\r", "").trim();
                } else {
                    // 有多个预期结果分开写的
                    StringBuilder expectBuilder = new StringBuilder();
                    for (String str : expectArr) {
                        expectBuilder.append("\n").append(str.trim().replaceFirst("预期结果", ""));
                    }
                    expect = expectBuilder.toString().trim();
                }
                excelColumn.setExpectResult(StringUtil.isNotBlank(excelColumn.getExpectResult()) ?
                        (excelColumn.getExpectResult().equals(expect) ? expect : (excelColumn.getExpectResult() + "\n" + expect)) : expect);
            }
        } else {
//            excelColumn.setExpectResult("请检查预期结果");
        }
        excelColumn.setRemark(remark);
        if (StringUtils.isNotBlank(sbException)) {
            excelColumn.setException((StringUtil.isNotBlank(excelColumn.getException()) ? (excelColumn.getException() + "、" + sbException.toString()) : sbException.toString()));
        }
    }

    public static int getSpecialCharacterIndex(List<String> specialCharacterList, String str) {
        int index = -1;
        for (String specialCharacter : specialCharacterList) {
            int tempIndex = str.indexOf(specialCharacter);
            if (tempIndex > 0) {
                if (index < 0) {
                    index = tempIndex;
                } else {
                    if (tempIndex < index) {
                        index = tempIndex;
                    }
                }
            }
        }
        return index;
    }

    public static void writeBytesToFile(byte[] bs, String filePath) {
        OutputStream out = null;
        InputStream is = null;
        try {
            out = new FileOutputStream(filePath);
            is = new ByteArrayInputStream(bs);
            byte[] buff = new byte[bs.length];
            int len = 0;
            while ((len = is.read(buff)) != -1) {
                out.write(buff, 0, len);
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (null != is) {
                    is.close();
                }
                if (null != out) {
                    out.close();
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }


    // 判断是不是预期结果叶子节点
    public static Boolean match(String[] strs, int i) {
        return i == strs.length - 1 && (matchSteps(strs, i) || matchTitle(strs, i));
    }

    // 判断上一级节点是不是步骤
    public static Boolean matchSteps(String[] strs, int i) {
        String variable2 = strs[i - 1].trim();
        return variable2.matches(regex) || variable2.contains("步骤：") || variable2.contains("步骤:");
    }

    // 判断上一级节点是不是标题
    public static Boolean matchTitle(String[] strs, int i) {
        String variable1 = strs[i].replaceAll(" ", "");
        String variable2 = strs[i - 1].replaceAll(" ", "");
        if (!variable1.contains("前置条件：")
                && !variable1.contains("前置条件:")
                && !variable1.contains("备注：")
                && !variable1.contains("备注:")) {
            return variable2.contains("标题：")
                    || variable2.contains("标题:")
                    || variable2.contains("标题-高：")
                    || variable2.contains("标题-高:");
        }
        return false;
    }

    public static String excuteStepString(String[] strs, int i, Integer index) {
        String variable2 = strs[i - 1].trim();
        if (variable2.contains("步骤：") || variable2.contains("步骤:")) {
            return variable2.replaceFirst("步骤", "步骤" + index);
        } else {
            return "步骤" + index + "：" + (variable2.substring(4).trim());
        }
    }

    private static Boolean containStr(String variable, String str) {
        return variable.contains(str + ":") || variable.contains(str + "：");
    }
}
