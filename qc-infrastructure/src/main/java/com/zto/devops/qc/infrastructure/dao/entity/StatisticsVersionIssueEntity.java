package com.zto.devops.qc.infrastructure.dao.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
@Table(name = "qc_statistics_version_issue")
public class StatisticsVersionIssueEntity implements Serializable {

    /**
     * 版本编码
     */
    @Id
    @Column(name = "version_code")
    private String versionCode;
    /**
     * 版本名称
     */
    private String name;
    private String status;
    private String type;
    /**
     * 实际发布时间
     */
    @Column(name = "actual_publish_date")
    private Date actualPublishDate;

    /**
     * 所属产品code
     */
    @Column(name = "product_code")
    private String productCode;

    /**
     * 部门id
     */
    @Column(name = "dept_id")
    private Long deptId;

    /**
     * 从根部门到当前部门id拼接
     */
    @Column(name = "dept_no")
    private String deptNo;

    /**
     * 紧急优先级缺陷修复时长(秒)
     */
    @Column(name = "urgency_repair_time")
    private Long urgencyRepairTime;

    /**
     * 高优先级缺陷修复时长(秒)
     */
    @Column(name = "high_repair_time")
    private Long highRepairTime;

    /**
     * 中优先级缺陷开修复长(秒)
     */
    @Column(name = "middle_repair_time")
    private Long middleRepairTime;

    /**
     * 低优先级缺陷开修复长(秒)
     */
    @Column(name = "low_repair_time")
    private Long lowRepairTime;

    /**
     * 紧急优先级缺陷数量
     */
    @Column(name = "urgency_num")
    private Long urgencyNum;

    /**
     * 高优先级缺陷数量
     */
    @Column(name = "high_num")
    private Long highNum;

    /**
     * 中优先级缺陷数量
     */
    @Column(name = "middle_num")
    private Long middleNum;

    /**
     * 低优先级缺陷数量
     */
    @Column(name = "low_num")
    private Long lowNum;

    /**
     * 紧急优先级缺陷按期修复数量
     */
    @Column(name = "urgency_punctual_num")
    private Long urgencyPunctualNum;

    /**
     * 高优先级缺陷按期修复数量
     */
    @Column(name = "high_punctual_num")
    private Long highPunctualNum;

    /**
     * 中优先级缺陷按期修复数量
     */
    @Column(name = "middle_punctual_num")
    private Long middlePunctualNum;

    /**
     * 紧急优先级缺陷reopen数量
     */
    @Column(name = "urgency_reopen_num")
    private Long urgencyReopenNum;

    /**
     * 高优先级缺陷reopen数量
     */
    @Column(name = "high_reopen_num")
    private Long highReopenNum;

    /**
     * 中优先级缺陷reopen数量
     */
    @Column(name = "middle_reopen_num")
    private Long middleReopenNum;

    /**
     * 低优先级缺陷reopen数量
     */
    @Column(name = "low_reopen_num")
    private Long lowReopenNum;

    /**
     * 紧急优先级缺陷有效数量
     */
    @Column(name = "urgency_effective_num")
    private Long urgencyEffectiveNum;

    /**
     * 高优先级缺陷有效数量
     */
    @Column(name = "high_effective_num")
    private Long highEffectiveNum;

    /**
     * 中优先级缺陷有效数量
     */
    @Column(name = "middle_effective_num")
    private Long middleEffectiveNum;

    /**
     * 低优先级缺陷有效数量
     */
    @Column(name = "low_effective_num")
    private Long lowEffectiveNum;

    /**
     * 需求问题数量
     */
    @Column(name = "requirement_issue_num")
    private Long requirementIssueNum;

    /**
     * 版本创建时间
     */
    @Column( name = "version_gmt_create" )
    private Date versionGmtCreate;
    @Column(name = "gmt_modified")
    private Date gmtModified;
}