package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
@Data
@Table(name = "qc_test_function_point")
public class TestFunctionPointEntity extends BaseEntity implements Serializable {
	    /**
     * 自增ID
     */
    private Long id;

    /**
     * 业务主键
     */
    private String code;
    /**
     * 业务code
     */
    private String businessCode;

    /**
     * 测试类型
     */
    private String type;

    /**
     * 功能测试点
     */
    private String functionPoint;

    /**
     * 测试负责人/参与人id
     */
    private Long directorId;

    /**
     * 测试负责人/参与人名
     */
    private String directorName;

    /**
     * 创建人编码
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 是否删除 1 未删除, 0 已删除
     */
    private Boolean enable;

    /**
     * 编号
     */
    private String number;

    private static final long serialVersionUID = 1L;

    
}