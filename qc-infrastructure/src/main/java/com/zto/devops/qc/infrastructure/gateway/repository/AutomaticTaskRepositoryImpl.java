package com.zto.devops.qc.infrastructure.gateway.repository;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseExecuteRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTask;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTaskVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteTagVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListExecuteEnvQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageAutomaticTaskGroupQuery;
import com.zto.devops.qc.client.service.testmanager.apitest.model.ListExecuteDetailReq;
import com.zto.devops.qc.domain.gateway.repository.IAutomaticTaskRepository;
import com.zto.devops.qc.infrastructure.converter.AutomaticTaskEntityConverter;
import com.zto.devops.qc.infrastructure.converter.TestcaseEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticTaskEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseExecuteRecordEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanCaseEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AutomaticTaskRepositoryImpl implements IAutomaticTaskRepository {

    @Autowired
    private AutomaticTaskEntityConverter automaticTaskEntityConverter;

    @Autowired
    private AutomaticTaskMapper automaticTaskMapper;

    @Autowired
    private TmTestPlanCaseMapper tmTestPlanCaseMapper;

    @Autowired
    private TestcaseMapper testcaseMapper;

    @Autowired
    private AutomaticSchedulerMapper automaticSchedulerMapper;

    @Autowired
    private TestcaseExecuteRecordMapper testcaseExecuteRecordMapper;

    @Autowired
    private TestcaseEntityConverter testcaseEntityConverter;

    @Override
    public PageInfo<String> selectParentTaskIdList(PageAutomaticTaskGroupQuery query) {
        PageHelper.startPage(query.getPage(), query.getSize());
        List<String> taskIdList = automaticTaskMapper.selectAutomaticParentTaskIdList(query);
        PageInfo<String> pageInfo = new PageInfo<>(taskIdList);
        return pageInfo;
    }

    @Override
    public List<AutomaticTaskVO> selectTaskList(List<String> taskIdList) {
        return automaticTaskMapper.selectAutomaticTaskListByTaskId(taskIdList);
    }

    @Override
    public void insertSelective(AutomaticTaskEntityDO taskEntityDO) {
        AutomaticTaskEntity entity = automaticTaskEntityConverter.convert2Entity(taskEntityDO);
        automaticTaskMapper.insertSelective(entity);
    }

    @Override
    public List<AutomaticTaskEntityDO> selectListByTaskId(String taskId) {
        List<AutomaticTaskEntity> entityList = automaticTaskMapper.selectListByTaskId(taskId);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : automaticTaskEntityConverter.convert2DOList(entityList);
    }

    @Override
    public void updateByPrimaryKeySelective(AutomaticTaskEntityDO taskEntityDO) {
        automaticTaskMapper.updateByPrimaryKeySelective(automaticTaskEntityConverter.convert2Entity(taskEntityDO));
    }

    @Override
    public AutomaticTask loadFromDb(String code) {
        AutomaticTaskEntity entity = automaticTaskMapper.selectByPrimaryKey(code);
        if (null != entity) {
            return automaticTaskEntityConverter.convert(entity);
        }
        return null;
    }

    @Override
    public void updatePlanCaseStatus(String code, AutomaticStatusEnum status, String testPlanCode, TestPlanStageEnum testStage) {
        if (AutomaticStatusEnum.TERMINATION.equals(status)) {
            Example example1 = new Example(TestcaseExecuteRecordEntity.class);
            example1.createCriteria().andEqualTo("automaticTaskCode", code);
            example1.selectProperties("testcaseCode");
            List<TestcaseExecuteRecordEntity> list = testcaseExecuteRecordMapper.selectByExample(example1);
            for (TestcaseExecuteRecordEntity record : list) {
                TmTestPlanCaseEntity lastRecord = testcaseExecuteRecordMapper.selectLastRecord(
                        record.getTestcaseCode(), testPlanCode, testStage);
                if (null == lastRecord) {
                    lastRecord = new TmTestPlanCaseEntity();
                    lastRecord.setStatus(TestPlanCaseStatusEnum.NOT_STARTED);
                    lastRecord.setCreatorId(0L);
                    lastRecord.setCreator("");
                }
                Example example2 = new Example(TmTestPlanCaseEntity.class);
                example2.createCriteria()
                        .andEqualTo("planCode", testPlanCode)
                        .andEqualTo("testStage", testStage)
                        .andEqualTo("caseCode", record.getTestcaseCode());
                tmTestPlanCaseMapper.updateByExampleSelective(lastRecord, example2);
            }
        } else {
            tmTestPlanCaseMapper.updateByAutomaticTaskCode(code);
        }
    }

    public AutomaticStatusEnum getStatus(List<AutomaticTaskEntity> taskList) {
        //组装定时任务状态
        List<AutomaticStatusEnum> statusList =
                taskList.stream().map(AutomaticTaskEntity::getStatus).distinct().collect(Collectors.toList());

        if (CollectionUtil.isEmpty(statusList)) {
            return AutomaticStatusEnum.UNKNOWN;
        }
        //1）已终止：有一个子任务是终止，则父任务为终止
        if (statusList.contains(AutomaticStatusEnum.TERMINATION)) {
            return AutomaticStatusEnum.TERMINATION;
        }
        //2）排队中：没有终止的子任务时，有一个为排队中则状态为排队中
        if (statusList.contains(AutomaticStatusEnum.NOT_STARTED)) {
            return AutomaticStatusEnum.NOT_STARTED;
        }
        //3）待执行：没有终止与排队中任务时，有一个任务为待执行则为待执行
        if (statusList.contains(AutomaticStatusEnum.SUBMITTED)) {
            return AutomaticStatusEnum.SUBMITTED;
        }
        //4）执行中：在没有终止/排队/待执行的情况下，有一个任务为执行中，都为执行中
        if (statusList.contains(AutomaticStatusEnum.IN_PROGRESS)) {
            return AutomaticStatusEnum.IN_PROGRESS;
        }
        //5）成功：子任务全部执行完，且全部成功为成功
        if (statusList.size() == 1 && statusList.contains(AutomaticStatusEnum.SUCCESS)) {
            return AutomaticStatusEnum.SUCCESS;
        }
        //6）错误：子任务全部执行完，有一个错误，则为错误
        if (statusList.contains(AutomaticStatusEnum.ERROR)) {
            return AutomaticStatusEnum.ERROR;
        }
        //7）失败：子任务全部执行完，没有错误，有一个为失败就是失败
        if (statusList.contains(AutomaticStatusEnum.FAIL)) {
            return AutomaticStatusEnum.FAIL;
        }
        return AutomaticStatusEnum.UNKNOWN;
    }

    @Override
    public void updateByAutomaticTaskEntityDO(AutomaticTaskEntityDO entityDO) {
        AutomaticTaskEntity entity = automaticTaskEntityConverter.convert2Entity(entityDO);
        automaticTaskMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateResultByAutomaticTaskCode(String code, TestPlanCaseStatusEnum status) {
        Example example = new Example(TestcaseExecuteRecordEntity.class);
        example.createCriteria().andEqualTo("automaticTaskCode", code);
        TestcaseExecuteRecordEntity entity = new TestcaseExecuteRecordEntity();
        entity.setResult(status);
        testcaseExecuteRecordMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void updateResultNotFinished(String code, TestPlanCaseStatusEnum status) {
        testcaseExecuteRecordMapper.updateResultNotFinished(code, status);
    }

    @Override
    public void updateResultFilesByTaskCode(TestcaseExecuteRecordEntityDO entityDO, String code) {
        Example example = new Example(TestcaseExecuteRecordEntity.class);
        example.createCriteria().andEqualTo("automaticTaskCode", code);
        TestcaseExecuteRecordEntity recordEntity = automaticTaskEntityConverter.convert(entityDO);
        testcaseExecuteRecordMapper.updateByExampleSelective(recordEntity, example);
    }

    @Override
    public void updateTestcaseStatus(TestcaseExecuteRecordEntityDO entityDO, String testcaseCode, String automaticTaskCode) {
        Example example = new Example(TestcaseExecuteRecordEntity.class);
        example.createCriteria()
                .andEqualTo("testcaseCode", testcaseCode)
                .andEqualTo("automaticTaskCode", automaticTaskCode);
        TestcaseExecuteRecordEntity recordEntity = automaticTaskEntityConverter.convert(entityDO);
        testcaseExecuteRecordMapper.updateByExampleSelective(recordEntity, example);
    }

    @Override
    public List<Long> getRecordNotSuccess(String taskCode) {
        return testcaseExecuteRecordMapper.getRecordNotSuccess(taskCode);
    }

    @Override
    public void updateRecordToStatusByIds(List<Long> ids) {
        Example example = new Example(TestcaseExecuteRecordEntity.class);
        example.createCriteria().andIn("id", ids);
        TestcaseExecuteRecordEntity recordEntity = new TestcaseExecuteRecordEntity();
        recordEntity.setResult(TestPlanCaseStatusEnum.NOT_STARTED);
        testcaseExecuteRecordMapper.updateByExampleSelective(recordEntity, example);
    }

    @Override
    public List<TestcaseEntityDO> getTestcaseByAutomaticSourceCode(String automaticSourceCode) {
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("automaticSourceCode", automaticSourceCode);
        List<TestcaseEntity> list = testcaseMapper.selectByExample(example);
        return testcaseEntityConverter.covertList(list);
    }

    @Override
    public List<String> selectTestcaseCodeList(String automaticTaskCode) {
        return testcaseExecuteRecordMapper.selectTestcaseCodeList(automaticTaskCode);
    }

    @Override
    public List<AutomaticTaskEntityDO> selectAutomaticTaskByStatusList(String code, Boolean parentFlag,
                                                                       List<AutomaticStatusEnum> statusEnums) {
        Example example = new Example(AutomaticTaskEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("status", statusEnums)
                .andEqualTo("enable", Boolean.TRUE);
        if (parentFlag) {
            criteria.andEqualTo("taskId", code);
        } else {
            criteria.andEqualTo("code", code);
        }
        return automaticTaskEntityConverter.convert2DOList(automaticTaskMapper.selectByExample(example));
    }

    @Override
    public List<AutomaticTaskEntityDO> selectByTaskIdAndVersionCode(String taskId, String versionCode) {
        Example example = new Example(AutomaticTaskEntity.class);
        Example.Criteria criteria = example.createCriteria().andEqualTo("taskId", taskId);
        if (StringUtil.isNotBlank(versionCode)) {
            criteria.andEqualTo("versionCode", versionCode);
        }
        List<AutomaticTaskEntity> entities = automaticTaskMapper.selectByExample(example);
        return automaticTaskEntityConverter.convert2DOList(entities);
    }

    @Override
    public List<String> getAbortAutomaticTasks(Integer duration) {
        return automaticTaskMapper.selectAbortAutomaticTasks(duration);
    }

    @Override
    public AutomaticTaskEntityDO getAutomaticTaskByCode(String code) {
        AutomaticTaskEntity entity = automaticTaskMapper.selectByPrimaryKey(code);
        return automaticTaskEntityConverter.convert2DO(entity);
    }

    @Override
    public List<TestcaseExecuteRecordEntityDO> getTestcaseRecordByTaskCode(ListExecuteDetailReq req) {
        return automaticTaskEntityConverter.convertList(testcaseExecuteRecordMapper.selectApiExecuteRecord(req));
    }

    @Override
    public List<TestPlanCaseStatusEnum> getTestcaseResultByTaskId(String taskId) {
        return testcaseExecuteRecordMapper.selectStatusByTaskId(taskId);
    }

    @Override
    public List<ExecuteTagVO> listExecuteEnv(ListExecuteEnvQuery query) {
        return automaticTaskMapper.listExecuteEnv(query.getProductCode(), query.getKeyWord());
    }

    @Override
    public List<String> getApiTestWaitTasks(Integer duration) {
        return automaticTaskMapper.selectApiTestWaitTasks(duration);
    }

    @Override
    public List<AutomaticTaskEntityDO> selectListByCodeList(List<String> codeList) {
        Example example = new Example(AutomaticTaskEntity.class);
        example.createCriteria().andIn("code", codeList)
                .andEqualTo("enable", Boolean.TRUE);
        List<AutomaticTaskEntity> entityList = automaticTaskMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : automaticTaskEntityConverter.convert2DOList(entityList);
    }

    @Override
    public void deleteTaskCase(String taskCode, List<String> list) {
        if (StringUtil.isEmpty(taskCode) || CollectionUtil.isEmpty(list)) {
            return;
        }
        Example example = new Example(TestcaseExecuteRecordEntity.class);
        example.createCriteria()
                .andEqualTo("automaticTaskCode", taskCode)
                .andIn("testcaseCode", list);
        testcaseExecuteRecordMapper.deleteByExample(example);
    }

    @Override
    public void updateCaseFileByAutomaticTask(AutomaticTaskEntityDO entityDO) {
        Example example = new Example(TestcaseExecuteRecordEntity.class);
        example.createCriteria().andEqualTo("automaticTaskCode", entityDO.getCode());
        TestcaseExecuteRecordEntity entity = new TestcaseExecuteRecordEntity();
        entity.setExecLogFile(entityDO.getExecLogFile());
        entity.setReportFile(entityDO.getReportFile());
        testcaseExecuteRecordMapper.updateByExampleSelective(entity, example);
    }
}
