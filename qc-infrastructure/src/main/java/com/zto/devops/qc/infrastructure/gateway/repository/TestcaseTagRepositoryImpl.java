package com.zto.devops.qc.infrastructure.gateway.repository;

import com.google.common.collect.Lists;
import com.zto.devops.framework.client.entity.action.ActionLogDO;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.domain.gateway.repository.IEventLogRepository;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.client.model.dto.TagEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagBatchAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagRemovedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseTagQuery;
import com.zto.devops.qc.domain.gateway.repository.TestcaseTagRepository;
import com.zto.devops.qc.infrastructure.converter.TestcaseTagConverter;
import com.zto.devops.qc.infrastructure.dao.entity.TagEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TagMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.TestcaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class TestcaseTagRepositoryImpl implements TestcaseTagRepository {


    @Autowired
    TagMapper tagMapper;

    @Autowired
    TestcaseMapper testcaseMapper;

    @Autowired
    TestcaseTagConverter testcaseTagConverter;

    @Autowired
    IEventLogRepository eventLogManager;

    @Override
    public void addTestcaseTag(TestcaseTagAddedEvent event) {
        TagEntity entity = testcaseTagConverter.converter(event);
        entity.setEnable(Boolean.TRUE);
        tagMapper.insertSelective(entity);
    }

    @Override
    @Async
    public void batchAddTestcaseTag(TestcaseTagBatchAddedEvent event) {
        if (CollectionUtils.isEmpty(event.getBusinessCodes())) {
            return;
        }
        saveBatch(event);
    }

    @Override
    public List<TagEntityDO> selectTestcaseTagByCaseCodeList(List<String> caseCodes) {
        Example tagExample = new Example(TagEntity.class);
        tagExample.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andIn("businessCode", caseCodes);
        return testcaseTagConverter.convertList(tagMapper.selectByExample(tagExample));
    }

    @Override
    public void insertSelective(TagEntityDO entityDO) {
        tagMapper.insertSelective(testcaseTagConverter.convert2(entityDO));
    }

    @Override
    public int selectCountByExample(String businessCode, String tagName) {
        Example example1 = new Example(TagEntity.class);
        example1.createCriteria()
                .andEqualTo("businessCode", businessCode)
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("tagName", tagName);
        return tagMapper.selectCountByExample(example1);
    }

    @Override
    public int selectCountByExample(String businessCode, DomainEnum domain) {
        Example example2 = new Example(TagEntity.class);
        example2.createCriteria()
                .andEqualTo("businessCode", businessCode)
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("domain", domain);
        return tagMapper.selectCountByExample(example2);
    }

    @Override
    public List<TagVO> handle(ListTestcaseTagQuery query) {
        log.info("ListTestcaseTagQuery >>> {}", query.getBusinessCode());
        Example example3 = new Example(TagEntity.class);
        example3.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("businessCode", query.getBusinessCode());
        example3.orderBy("gmtCreate");
        List<TagEntity> tagList = tagMapper.selectByExample(example3);
        return testcaseTagConverter.convert(tagList);
    }

    private void saveBatch(TestcaseTagBatchAddedEvent event) {
        List<TagEntity> tagEntities = setTagEntityList(event);
        if (CollectionUtils.isEmpty(tagEntities)) {
            return;
        }
        List<List<TagEntity>> partitionList = Lists.partition(tagEntities, 200);
        partitionList.parallelStream().forEach(t -> {
            tagMapper.saveBatch(t);
        });
        addActionLog(tagEntities);
    }


    private List<TagEntity> setTagEntityList(TestcaseTagBatchAddedEvent event) {
        List<TagEntity> tagEntities = new ArrayList<>();
        for (String businessCode : event.getBusinessCodes()) {
            for (String tagName : event.getTagNames()) {
                TagEntity tagEntity = new TagEntity();
                Example example1 = new Example(TagEntity.class);
                example1.createCriteria().andEqualTo("businessCode", businessCode)
                        .andEqualTo("enable", Boolean.TRUE).andEqualTo("tagName", tagName);
                int count1 = tagMapper.selectCountByExample(example1);
                if (count1 > 0) {
                    continue;
                }
                tagEntity.setBusinessCode(businessCode);
                tagEntity.setTagName(tagName);
                tagEntity.setDomain(event.getDomain());
                tagEntity.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
                tagEntity.setCreator(event.getTransactor().getUserName());
                tagEntity.setCreatorId(event.getTransactor().getUserId());
                tagEntity.setModifier(event.getTransactor().getUserName());
                tagEntity.setModifierId(event.getTransactor().getUserId());
                tagEntity.setType(event.getType());
                tagEntities.add(tagEntity);
            }
        }
        return tagEntities;
    }

    @Async
    public void addActionLog(List<TagEntity> tagEntities) {
        List<ActionLogDO> logs = new ArrayList<>();
        for (TagEntity entity : tagEntities) {
            ActionLogDO log = new ActionLogDO(
                    entity.getBusinessCode(),
                    "添加了标签",
                    entity.getTagName(),
                    TestcaseTagBatchAddedEvent.class.getName(),
                    entity.getCreator(),
                    entity.getCreatorId());
            logs.add(log);
        }
        eventLogManager.createLogs(logs);
    }

    @Override
    public List<TagVO> listTestcaseTagQuery(ListTestcaseTagQuery query) {
        log.info("ListTestcaseTagQuery >>> {}", query.getBusinessCode());
        Example example = new Example(TagEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("businessCode", query.getBusinessCode());
        example.orderBy("gmtCreate");
        List<TagEntity> tagList = tagMapper.selectByExample(example);
        return testcaseTagConverter.convertVOList(tagList);
    }

    @Override
    public void removeTestcaseTag(TestcaseTagRemovedEvent event) {
        tagMapper.deleteByPrimaryKey(event.getCode());
    }

}
