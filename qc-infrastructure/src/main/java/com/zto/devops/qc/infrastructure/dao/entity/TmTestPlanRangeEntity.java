package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanPriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanRangeTypeEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanPriorityHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanRangeTypeHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
@Table(name = "tm_test_plan_range")
@Data
public class TmTestPlanRangeEntity extends BaseEntity implements Serializable {
    /**
     * 自增ID
     */
    @Id
    private Long id;

    /**
     * 测试范围编号
     */
    private String code;

    /**
     * 关联计划code
     */
    private String planCode;

    /**
     * 测试范围，MOBILE_TEST-移动专项测试|SAFETY_SCANNING-安全扫描|EXPLORATORY_TEST-探索性测试|STATIC_ANALYSIS-静态分析|PERFORMANCE_TEST-性能测试|PERMISSIONS_TEST-权限测试...
     */
    @ColumnType(typeHandler = TmTestPlanRangeTypeHandler.class)
    private TestPlanRangeTypeEnum testRange;

    private Date testTime;


    private Boolean testRangeStatus;

    /**
     * 优先级，HIGH-高|MEDIUM-中|LOW-低
     */
    @ColumnType(typeHandler = TmTestPlanPriorityHandler.class)
    private TestPlanPriorityEnum priority;

    private String permissionsTestInformation;

    private String testInformation;

    /**
     * 测试结果
     */
    private String status;

    /**
     * 执行人编码
     */
    private Long executorId;

    /**
     * 执行人
     */
    private String executor;


    private static final long serialVersionUID = 1L;
}