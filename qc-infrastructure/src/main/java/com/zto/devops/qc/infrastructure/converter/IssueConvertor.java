package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.IssueEntityDO;
import com.zto.devops.qc.client.model.dto.StatisticsVersionIssueEntityDO;
import com.zto.devops.qc.client.model.dto.TransitionNodeEntityDO;
import com.zto.devops.qc.domain.model.Attachment;
import com.zto.devops.qc.domain.model.Comment;
import com.zto.devops.qc.domain.model.Issue;
import com.zto.devops.qc.infrastructure.dao.entity.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Mapper(componentModel = "spring")
public interface IssueConvertor {

    @Mapping(target = "creator.userId", source = "creatorId")
    @Mapping(target = "creator.userName", source = "creator")
    @Mapping(target = "modifier.userId", source = "modifierId")
    @Mapping(target = "modifier.userName", source = "modifier")
    @Mapping(target = "handler.userId", source = "handleUserId")
    @Mapping(target = "handler.userName", source = "handleUserName")
    @Mapping(target = "finder.userId", source = "findUserId")
    @Mapping(target = "finder.userName", source = "findUserName")
    @Mapping(target = "developer.userId", source = "developUserId")
    @Mapping(target = "developer.userName", source = "developUserName")
    @Mapping(target = "tester.userId", source = "testUserId")
    @Mapping(target = "tester.userName", source = "testUserName")
    @Mapping(target = "requirement.code", source = "requirementCode")
    @Mapping(target = "requirement.name", source = "requirementName")
    @Mapping(target = "product.code", source = "productCode")
    @Mapping(target = "product.name", source = "productName")
    @Mapping(target = "findVersion.code", source = "findVersionCode")
    @Mapping(target = "findVersion.name", source = "findVersionName")
    @Mapping(target = "fixVersion.code", source = "fixVersionCode")
    @Mapping(target = "fixVersion.name", source = "fixVersionName")
    @Mapping(target = "sprint.code", source = "sprintCode")
    @Mapping(target = "sprint.name", source = "sprintName")
    Issue convert(IssueEntity entity);

    List<Attachment> convertList(Collection<AttachmentEntity> attachmentEntities);

    @Mapping(target = "creator.userId", source = "creatorId")
    @Mapping(target = "creator.userName", source = "creator")
    Comment convert(CommentEntity comment);

    Set<Comment> convertCommentList(Collection<CommentEntity> commentEntities);

    IssueEntityDO convert2DO(IssueEntity entity);

    List<IssueEntityDO> convert2DOList(List<IssueEntity> entityList);

    List<TransitionNodeEntityDO> convert2TransitionNodeEntityDOList(List<TransitionNodeEntity> entityList);

    StatisticsVersionIssueEntityDO convert(StatisticsVersionIssueEntity entity);

    StatisticsVersionIssueEntity convert(StatisticsVersionIssueEntityDO entityDO);


//    @Mapping(target = "aggregateId",expression = "java(com.zto.lbd.kernel.domain.AggregateIdUtil.generateId(com.zto.lbd.kernel.domain.AggregateType.SNOWFLAKE))")
//    @Mapping(target = "title", source = "title")
//    @Mapping(target = "status", expression = "java(com.zto.lbd.qc.issue.enums.IssueStatus.WAIT_FIX)")
//    @Mapping(target = "priority", expression = "java(com.zto.lbd.qc.issue.enums.IssuePriority.URGENCY)")
//    @Mapping(target = "description", source = "title")
//    @Mapping(target = "rootCause", expression = "java(com.zto.lbd.qc.issue.enums.IssueRootCause.FUNCTIONAL_DEVELOPMENT_BUG)")
//    @Mapping(target = "type", expression = "java(com.zto.lbd.qc.issue.enums.IssueType.FUNCTION_BUG)")
//    @Mapping(target = "testMethod", expression = "java(com.zto.lbd.qc.issue.enums.IssueTestMethod.FUNCTION_TEST)")
//    @Mapping(target = "repetitionRate", expression = "java(com.zto.lbd.qc.issue.enums.IssueRepetitionRate.INEVITABLE)")
//    @Mapping(target = "findStage", expression = "java(com.zto.lbd.qc.issue.enums.IssueFindStage.OPERATE_STAGE)")
//    @Mapping(target = "findEnv", expression = "java(com.zto.lbd.qc.issue.enums.IssueFindEnv.PRODUCT_EVN)")
//    AddIssueCommand convert(RelatedItemVO vo);



//    List<AddIssueCommand> convert(List<RelatedItemVO> vos);

//    List<AddVersionIssueCommand> convertVersion(List<RelatedItemVO> vos);

}
