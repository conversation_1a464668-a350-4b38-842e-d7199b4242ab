package com.zto.devops.qc.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@ConfigurationProperties("qc.scheduler.message")
@Component
public class AutomaticSchedulerMsgConfig {

    /**
     * 跳转链接-基础
     */
    private String baseUrl;
    /**
     * 跳转链接-业务部分
     */
    private String detailUrl;

    /**
     * 定时任务失败重试
     */
    private String scheduledRetryUrl;


    /**
     * 告警内容
     */
    private String content;

}
