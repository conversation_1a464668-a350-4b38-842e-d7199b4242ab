package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticRecordTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticStatusHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Table(name = "tm_automatic_source_record")
public class AutomaticSourceRecordEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 编号
     */
    @Id
    private String code;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 登记库名称
     */
    private String name;

    /**
     * 备注
     */
    private String comment;

    /**
     * jmeter:源地址-OSS上的脚本文件地址;xunit-git地址
     */
    private String address;

    /**
     * 类型：1-jmeter, 2-testng, 3-postman, 4-junit,5-pyunit
     */
    @ColumnType(typeHandler = AutomaticRecordTypeHandler.class)
    private AutomaticRecordTypeEnum type;

    /**
     * 新增用例数量
     */
    private Integer addCaseNo;

    /**
     * 更新用例数量
     */
    private Integer updateCaseNo;

    /**
     * 删除用例数量
     */
    private Integer deleteCaseNo;

    @ColumnType(typeHandler = AutomaticStatusHandler.class)
    private AutomaticStatusEnum status;

    private String dataFileAddress;

    private String extendJarAddress;

    private String thirdJarAddress;

    private String fileName;

    private String bucketName;

    private String failInformation;

    private String lastAutomaticSourceLogCode;

    /**
     * 更新人编码
     */
    private Long personLiableId;

    /**
     * 更新人
     */
    private String personLiable;

    //所属目录code
    private String testcaseCode;

    private String workSpace;

    private String branch;

    private String commitId;

    private String scanDirectory;

    private String errorLogFile;

    /**
     * 是否根据git提交操作，自动重新解析登记库（0:不是；1:是;）
     */
    private Boolean autoAnalysisFlag;
}