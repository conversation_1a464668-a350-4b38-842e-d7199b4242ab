package com.zto.devops.qc.infrastructure.gateway.repository;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.model.dto.InterfaceCoverageEntityDO;
import com.zto.devops.qc.client.service.coverage.model.req.PageInterfaceCoverageInfoReq;
import com.zto.devops.qc.client.service.coverage.model.req.ZcatMqBodyReq;
import com.zto.devops.qc.client.service.coverage.model.resp.InterfaceCoverageInfoResp;
import com.zto.devops.qc.domain.gateway.repository.InterfaceCoverageRepository;
import com.zto.devops.qc.domain.model.coverage.ZcatMetricsVO;
import com.zto.devops.qc.infrastructure.converter.InterfaceCoverageEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.InterfaceCoverageEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.InterfaceCoverageMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;

@Component
@Slf4j
public class InterfaceCoverageRepositoryImpl implements InterfaceCoverageRepository {

    @Autowired
    private InterfaceCoverageMapper interfaceCoverageMapper;
    @Autowired
    private InterfaceCoverageEntityConverter interfaceCoverageEntityConverter;

    @Override
    public PageInfo<InterfaceCoverageInfoResp> queryPageInterfaceCoverageInfo(PageInterfaceCoverageInfoReq req) {
        Example example = new Example(InterfaceCoverageEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("commitId", req.getCommitId()).andEqualTo("enable", 1);
        if (req.getStatus() != null) {
            criteria.andEqualTo("isCovered", req.getStatus());
        }
        if (req.getInterfaceType() != null) {
            criteria.andEqualTo("interfaceMethodType", req.getInterfaceType());
        }
        if (StringUtil.isNotEmpty(req.getInterfaceFullClassName()) &&
                StringUtil.isNotEmpty(req.getInterfaceMethodName()) &&
                StringUtil.isNotEmpty(req.getInterfaceMethodDesc())) {
            criteria.andEqualTo("modifyClassName", req.getInterfaceFullClassName()).
                    andEqualTo("modifyMethodName", req.getInterfaceMethodName()).
                    andEqualTo("methodParameterStr", req.getInterfaceMethodDesc());
        }
        example.setOrderByClause("interface_call_number desc");
        example.selectProperties("interfaceAlias", "interfaceDocAddress", "isCovered", "interfaceMethodType", "interfaceCallNumber", "interfaceErrorNumber");
        example.setDistinct(true);
//        int count = interfaceCoverageMapper.selectCountByExample(example);
        Page<InterfaceCoverageEntity> page = PageHelper.startPage(req.getPage(), req.getSize(), true);
        List<InterfaceCoverageEntity> list = interfaceCoverageMapper.selectByExample(example);
        PageInfo<InterfaceCoverageInfoResp> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setList(interfaceCoverageEntityConverter.converter(list));
        return pageInfo;
    }

    @Override
    public BigDecimal queryInterfaceCoverageRate(String commitId) {
        Example example = new Example(InterfaceCoverageEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("commitId", commitId).andEqualTo("enable", 1);
        example.selectProperties("interfaceAlias", "interfaceDocAddress", "isCovered", "interfaceMethodType", "interfaceCallNumber", "interfaceErrorNumber");
        example.setDistinct(true);
        int countAll = interfaceCoverageMapper.selectCountByExample(example);
        if (countAll == 0) {
            return null;
        }
        BigDecimal countAllBigDecimal = BigDecimal.valueOf(countAll);
        criteria.andEqualTo("isCovered", 1);
        int countCovered = interfaceCoverageMapper.selectCountByExample(example);
        BigDecimal countCoveredBigDecimal = BigDecimal.valueOf(countCovered);
        return countCoveredBigDecimal.divide(countAllBigDecimal, 2, RoundingMode.CEILING).multiply(BigDecimal.valueOf(100));
    }

    @Override
    public Integer queryInterfaceCoverageCountByCommitId(String commitId) {
        Example example = new Example(InterfaceCoverageEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("commitId", commitId)
                .andEqualTo("enable", 1);
        return interfaceCoverageMapper.selectCountByExample(example);
    }

    @Override
    public List<InterfaceCoverageEntityDO> queryInterfaceCoverageListByCommitId(String commitId) {
        Example example = new Example(InterfaceCoverageEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("commitId", commitId)
                .andEqualTo("isCovered", 1);
        return interfaceCoverageEntityConverter.convertToEntityDO(interfaceCoverageMapper.selectByExample(example));
    }

    @Override
    public void batchSave(List<InterfaceCoverageEntityDO> list) {
        long start = System.currentTimeMillis();
        List<InterfaceCoverageEntity> entityList = interfaceCoverageEntityConverter.convertToEntity(list);
        List<List<InterfaceCoverageEntity>> partition = Lists.partition(entityList, 200);
        for (List<InterfaceCoverageEntity> subList : partition) {
            interfaceCoverageMapper.batchSave(subList);
        }
        log.info(">>>>>>>>>use : {}", System.currentTimeMillis() - start);
    }

    @Override
    public void deleteByCommitId(String commitId) {
        Example example = new Example(InterfaceCoverageEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("commitId", commitId);
        InterfaceCoverageEntity entity = new InterfaceCoverageEntity();
        entity.setEnable(false);
        interfaceCoverageMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<InterfaceCoverageEntityDO> queryAllInterfaceCoverages(String appId) {
        Example example = new Example(InterfaceCoverageEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", 1);
        if (StringUtil.isNotEmpty(appId)) {
            criteria.andEqualTo("appId", appId);
        }
        example.selectProperties("appId", "interfaceMethodType", "zcatMetricKey");
        return interfaceCoverageEntityConverter.convertToEntityDO(interfaceCoverageMapper.selectByExample(example));
    }

    @Override
    public void updateByZcatMetricKey(ZcatMetricsVO vo, String appId) {
        Example example = new Example(InterfaceCoverageEntity.class);
        example.createCriteria()
                .andEqualTo("appId", appId)
                .andEqualTo("zcatMetricKey", vo.getMetricKey())
                .andEqualTo("enable", 1);
        InterfaceCoverageEntity entity = new InterfaceCoverageEntity();
        entity.setInterfaceCallNumber(vo.getCount());
        entity.setInterfaceErrorNumber(vo.getFail());
        interfaceCoverageMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void updateIsCoveredByZcatMetricKey(ZcatMqBodyReq req) {
        Example example = new Example(InterfaceCoverageEntity.class);
        example.createCriteria()
                .andEqualTo("appId",req.getAppId())
                .andEqualTo("versionCode",req.getVersionCode())
                .andEqualTo("zcatMetricKey", req.getInterfaceName())
                .andEqualTo("enable", 1);
        InterfaceCoverageEntity entity = new InterfaceCoverageEntity();
        entity.setIsCovered(1);
        interfaceCoverageMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<InterfaceCoverageEntityDO> queryInterfaceCoverageList(String versionCode) {
        if (StringUtil.isEmpty(versionCode)) {
            return null;
        }
        Example example = new Example(InterfaceCoverageEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("versionCode", versionCode)
                .andEqualTo("enable", 1);
        example.selectProperties("appId", "commitId", "interfaceAlias", "interfaceDocAddress", "isCovered", "interfaceMethodType");
        example.setDistinct(true);
        return interfaceCoverageEntityConverter.convertToEntityDO(interfaceCoverageMapper.selectByExample(example));
    }

    @Override
    public List<InterfaceCoverageEntityDO> queryInterfacesAnnotations(String versionCode, String interfaceMethodType) {
        if (StringUtil.isEmpty(versionCode) || StringUtil.isEmpty(interfaceMethodType)) {
            return Collections.emptyList();
        }
        Example example = new Example(InterfaceCoverageEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("versionCode", versionCode)
                .andEqualTo("enable", 1);
        example.selectProperties("interfaceMethodAnnotation");
        return interfaceCoverageEntityConverter.convertToEntityDO(interfaceCoverageMapper.selectByExample(example));
    }
}
