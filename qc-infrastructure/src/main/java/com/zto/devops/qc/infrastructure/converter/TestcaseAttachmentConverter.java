package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.AttachmentEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseAttachmentAddedEvent;
import com.zto.devops.qc.domain.model.TestcaseAttachment;
import com.zto.devops.qc.infrastructure.dao.entity.AttachmentEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TestcaseAttachmentConverter {

    @Mapping(target = "creatorId", source = "transactor.userId")
    @Mapping(target = "creator", source = "transactor.userName")
    @Mapping(target = "gmtCreate", source = "occurred")
    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "gmtModified", source = "occurred")
    AttachmentEntity converter(TestcaseAttachmentAddedEvent event);


    TestcaseAttachment domainConverter(AttachmentEntity entity);

    AttachmentEntityDO convert(AttachmentEntity entity);

    List<AttachmentEntityDO> convertList(List<AttachmentEntity> entityList);

    AttachmentEntity convert2(AttachmentEntityDO entityDO);


}
