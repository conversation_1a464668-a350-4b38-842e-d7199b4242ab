package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.DiffTypeHandler;
import lombok.Data;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022/9/26 14:22
 */
@ToString
@Data
@Table(name = "qc_coverage_exec")
public class CoverageExecEntity {

    public CoverageExecEntity() {

    }

    public CoverageExecEntity(String versionCode, String appId, String branchName,
                              String execName, String execPath, String bucketName,
                              String commitId, String creator, Long creatorId, String modifier,
                              Long modifierId) {
        this.versionCode = versionCode;
        this.appId = appId;
        this.branchName = branchName;
        this.execName = execName;
        this.execPath = execPath;
        this.bucketName = bucketName;
        this.commitId = commitId;
        this.creator = creator;
        this.creatorId = creatorId;
        this.modifier = modifier;
        this.modifierId = modifierId;
    }


    /**
     * 版本编码
     */
    @Column(name = "version_code")
    private String versionCode;

    /**
     * appId
     */
    @Column(name = "app_id")
    private String appId;

    /**
     * 分支名称
     */
    @Column(name = "branch_name")
    private String branchName;

    /**
     * commitId
     */
    @Column(name = "commit_id")
    private String commitId;

    /**
     * 存储桶名字
     */
    @Column(name = "bucket_name")
    private String bucketName;

    /**
     * exec路径
     */
    @Column(name = "exec_path")
    private String execPath;

    /**
     * exec名称
     */
    @Column(name = "exec_name")
    private String execName;

    @Column(name = "creator_id")
    private Long creatorId;

    @Column(name = "creator")
    private String creator;

    @Column(name = "gmt_create")
    private Date gmtCreate;

    @Column(name = "modifier_id")
    private Long modifierId;

    @Column(name = "modifier")
    private String modifier;

    @Column(name = "gmt_modified")
    private Date gmtModified;

    /**
     * 发布泳道
     */
    @Column(name = "flow_lane_type")
    private String flowLaneType;

    /**
     * 差异类型
     */
    @Column(name = "diff_type")
    @ColumnType(typeHandler = DiffTypeHandler.class)
    private DiffTypeEnum diffType;

}