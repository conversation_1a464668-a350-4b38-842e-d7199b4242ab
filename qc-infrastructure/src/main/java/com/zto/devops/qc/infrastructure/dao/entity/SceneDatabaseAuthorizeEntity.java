package com.zto.devops.qc.infrastructure.dao.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "tm_scene_database_authorize")
public class SceneDatabaseAuthorizeEntity {

    @Id
    @GeneratedValue(generator = "JDBC", strategy = GenerationType.IDENTITY)
    private Long id;

    private String authorizeCode;

    private String productCode;

    private String authorizeProductCode;

    private String authorizeProductName;

    private Long dbId;

    private String dbName;

    private String physicDbName;

    private Boolean enable;

    @Column(name = "creator_id")
    private Long creatorId;

    @Column(name = "creator")
    private String creator;

    @Column(name = "gmt_create")
    private Date gmtCreate;

    @Column(name = "modifier_id")
    private Long modifierId;

    @Column(name = "modifier")
    private String modifier;

    @Column(name = "gmt_modified")
    private Date gmtModified;

}
