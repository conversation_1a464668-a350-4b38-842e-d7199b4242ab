package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "qc_issue_info")
public class IssueInfoEntity extends BaseEntity {

    /**
     * 模块code
     */
    @Id
    private String code;

    /**
     * 报告code
     */
    @Column(name = "report_code")
    private String reportCode;

    /**
     * 缺陷总数
     */
    @Column(name = "issue_count")
    private Integer issueCount;

    /**
     * 有效缺陷总数
     */
    @Column(name = "valid_issue_count")
    private Integer validIssueCount;

    /**
     * 遗留缺陷数
     */
    @Column(name = "legacy_issue_count")
    private Integer legacyIssueCount;

    /**
     * p012遗留数
     */
    @Column(name = "legacy_issue_high")
    private Integer legacyIssueHigh;

    private static final long serialVersionUID = 1L;
}