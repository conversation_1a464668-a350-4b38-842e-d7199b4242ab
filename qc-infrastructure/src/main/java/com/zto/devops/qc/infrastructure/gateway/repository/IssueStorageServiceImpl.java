package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.issue.event.*;
import com.zto.devops.qc.domain.gateway.repository.IssueStorageService;
import com.zto.devops.qc.infrastructure.converter.IssueEntityConverter;
import com.zto.devops.qc.infrastructure.converter.RelevantUserEntityConverter;
import com.zto.devops.qc.infrastructure.converter.TagVOConverter;
import com.zto.devops.qc.infrastructure.dao.entity.IssueEntity;
import com.zto.devops.qc.infrastructure.dao.entity.RelevantUserEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TagEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.IssueMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.RelevantUserMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.TagMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2022/9/30
 * @Version 1.0
 */
@Component
@Slf4j
public class IssueStorageServiceImpl implements IssueStorageService {

    @Autowired
    private IssueMapper issueMapper;

    @Autowired
    private RelevantUserMapper relevantUserMapper;

    @Autowired
    private TagMapper tagMapper;

    @Autowired
    private TagVOConverter tagVOConverter;

    @Autowired
    private IssueEntityConverter issueEntityConverter;

    @Autowired
    private RelevantUserEntityConverter relevantUserEntityConverter;

    @Override
    public void addIssue(IssueAddedEvent event) {
        IssueEntity entity = issueEntityConverter.convert(event);
        entity.preCreate(event);
        issueMapper.insertSelective(entity);
    }

    // 编辑缺陷
    @Override
    public void updateIssue(IssueEditedEvent event) {
        IssueEntity entity = issueEntityConverter.convert(event);
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }
    // 开始修复缺陷
    @Override
    public void updateIssue(IssueStartFixedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
/*        entity.setHandleUserId(event.getTransactor().getUserId());
        entity.setHandleUserName(event.getTransactor().getUserName());*/
        entity.setUpdateTime(event.getOccurred());
        entity.setGmtModified(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setStartFixTime(event.getStartFixTime());

        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }
    // 延期修复缺陷
    @Override
    public void updateIssue(IssueDelayFixedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
        entity.setHandleUserId(event.getTransactor().getUserId());
        entity.setHandleUserName(event.getTransactor().getUserName());
        entity.setGmtModified(event.getOccurred());
        entity.setUpdateTime(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setDelayFixTime(event.getDelayFixTime());
        entity.setFixVersionCode(event.getFixVersion().getCode());
        entity.setFixVersionName(event.getFixVersion().getName());

        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    // 交付验证
    @Override
    public void updateIssue(IssueDeliveryValidatedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
        entity.setFixVersionCode(event.getFixVersion().getCode());
        entity.setFixVersionName(event.getFixVersion().getName());
        entity.setGmtModified(event.getOccurred());
        entity.setUpdateTime(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setDeliverTime(event.getDeliverTime());
        entity.setHandleUserId(event.getHandler().getUserId());
        entity.setHandleUserName(event.getHandler().getUserName());
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    // 验证通过并关闭
    @Override
    public void updateIssue(IssueValidatedAccessClosedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
        //entity.setFixVersionName(event.getFixVersion());
        entity.setFixVersionCode(event.getFixVersionCode());
        entity.setGmtModified(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setCloseTime(event.getCloseTime());

        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateIssue(IssueBackToRepairedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
        entity.setGmtModified(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        if (null != event.getHandler()) {
            entity.setHandleUserName(event.getHandler().getUserName());
            entity.setHandleUserId(event.getHandler().getUserId());
        }
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateIssue(IssueCirculationedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());

        if (null != event.getTestId()) {
            entity.setTestUserId(event.getTestId());
            entity.setTestUserName(event.getTestName());
        }
        if (null != event.getDevelopId()) {
            entity.setDevelopUserId(event.getDevelopId());
            entity.setDevelopUserName(event.getDevelopName());
        }
        if (null != event.getHandler()) {
            entity.setHandleUserName(event.getHandler().getUserName());
            entity.setHandleUserId(event.getHandler().getUserId());
        }

        entity.setGmtModified(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }


    @Override
    public void updateIssue(IssueRefusedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
        entity.setGmtModified(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setRejectTime(event.getRejectTime());
        if (event.getHandler() != null) {
            entity.setHandleUserId(event.getHandler().getUserId());
            entity.setHandleUserName(event.getHandler().getUserName());
        }
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    @Transactional
    public void updateIssue(IssueReopenEvent event) {
         try{
             IssueEntity issueEntity = issueMapper.selectByPrimaryKey(event.getCode());

             IssueEntity entity = new IssueEntity();
             entity.setCode(event.getCode());
             entity.setStatus(event.getStatus());
             entity.setGmtModified(event.getOccurred());
             entity.setModifier(event.getTransactor().getUserName());
             entity.setModifierId(event.getTransactor().getUserId());
             entity.setReopenTime(event.getReopenTime());
             if(issueEntity != null){
                 entity.setReopen(issueEntity.getReopen() + 1);
             }
             if (null != event.getHandler()) {
                 entity.setHandleUserName(event.getHandler().getUserName());
                 entity.setHandleUserId(event.getHandler().getUserId());
             }
             if (null != event.getIsValid()){
                 entity.setIsValid(event.getIsValid());
             }
             entity.preUpdate(event);
             issueMapper.updateByPrimaryKeySelective(entity);
         }catch (Exception e){
             log.error("IssueReopenEvent 入库异常:" + e.getMessage());
             throw new ServiceException("服务器执行异常");
         }
    }

    @Override
    public void updateIssue(IssueConfirmClosedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
        entity.setGmtModified(event.getOccurred());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setCloseTime(event.getCloseTime());

        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateIssue(IssueRelatedRequirementEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setUpdateTime(event.getOccurred());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setCode(event.getIssueCode());
        //entity.setRequirementName(event.getRequirementName());
        entity.setRequirementCode(event.getRequirementCode());
        entity.setRequirementLevel(event.getRequirementLevel());
        if(event.getFixVersion() != null){
            entity.setFixVersionCode(event.getFixVersion().getCode());
            entity.setFixVersionName(event.getFixVersion().getName());
        }
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void removeIssue(IssueRemovedEvent event) {
        IssueEntity entity = new IssueEntity();
        entity.setCode(event.getCode());
        entity.setStatus(event.getStatus());
        entity.setEnable(Boolean.FALSE);
        entity.setUpdateTime(event.getOccurred());
        entity.preUpdate(event);
        issueMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateRelevantUser(RelevantUserAddedEvent event) {
        Set<RelevantUserVO> relevantUserVOS = event.getRelevantUserVOS();
        List<RelevantUserEntity> entityList = relevantUserEntityConverter.convert(relevantUserVOS);
        for (RelevantUserEntity entity : entityList) {
            if (relevantUserExist(entity.getType(), entity.getBusinessCode(), entity.getUserId())) {
                continue;
            }
            entity.preCreate(event);
            relevantUserMapper.insertSelective(entity);
        }
    }

    @Override
    public void updateRelevantUser(RelevantUserRemovedSimpleEvent event) {
        RelevantUserEntity entity = new RelevantUserEntity();
        entity.setCode(event.getCode());
        entity.setEnable(Boolean.FALSE);
        relevantUserMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void addTag(TagAddedEvent event) {
        List<TagVO> tagVOS = event.getTags();
        List<TagEntity> entityList = tagVOConverter.convert(tagVOS);
        for (TagEntity entity : entityList) {
            entity.preCreate(event);
            tagMapper.insertSelective(entity);
        }
    }

    @Override
    public void removeTag(TagRemovedSimpleEvent event) {
        TagEntity entity = new TagEntity();
        entity.setCode(event.getCode());
        entity.setEnable(Boolean.FALSE);
        tagMapper.updateByPrimaryKeySelective(entity);
    }

    private boolean relevantUserExist(RelevantUserTypeEnum type, String businessCode, String userId) {
        if (!RelevantUserTypeEnum.CC.equals(type)) {
            return false;
        }
        Example example = new Example(RelevantUserEntity.class);
        example.createCriteria().andEqualTo("type", type)
                .andEqualTo("businessCode", businessCode)
                .andEqualTo("userId", userId)
                .andEqualTo("enable", 1);
        int i = relevantUserMapper.selectCountByExample(example);
        return i > 0;

    }

}
