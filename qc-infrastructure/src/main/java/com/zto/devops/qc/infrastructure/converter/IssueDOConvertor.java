package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.IssueEntityDO;
import com.zto.devops.qc.client.model.issue.entity.IssueBaseVO;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.entity.RelatedMatterStatusCountVO;
import com.zto.devops.qc.client.model.issue.query.PageIssueQuery;
import com.zto.devops.qc.client.model.issue.query.PageIssueThingQuery;
import com.zto.devops.qc.client.model.parameter.IssueQueryParameter;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.devops.qc.domain.model.Attachment;
import com.zto.devops.qc.domain.model.Comment;
import com.zto.devops.qc.domain.model.Issue;
import com.zto.devops.qc.domain.model.RelevantUser;
import com.zto.devops.qc.domain.model.Tag;
import com.zto.devops.qc.infrastructure.dao.entity.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Collection;
import java.util.List;
import java.util.Set;


@Mapper(componentModel = "spring")
public interface IssueDOConvertor {

    IssueQueryParameter convert(PageIssueQuery query);

    List<RelatedMatterStatusCountVO> covertRelatedMatterStatusCountVO(List<com.zto.devops.project.client.model.requirement.entity.RelatedMatterStatusCountVO> vos);


    @Mapping(target = "creator.userId", source = "creatorId")
    @Mapping(target = "creator.userName", source = "creator")
    @Mapping(target = "modifier.userId", source = "modifierId")
    @Mapping(target = "modifier.userName", source = "modifier")
    @Mapping(target = "handler.userId", source = "handleUserId")
    @Mapping(target = "handler.userName", source = "handleUserName")
    @Mapping(target = "finder.userId", source = "findUserId")
    @Mapping(target = "finder.userName", source = "findUserName")
    @Mapping(target = "developer.userId", source = "developUserId")
    @Mapping(target = "developer.userName", source = "developUserName")
    @Mapping(target = "tester.userId", source = "testUserId")
    @Mapping(target = "tester.userName", source = "testUserName")
    @Mapping(target = "requirement.code", source = "requirementCode")
    @Mapping(target = "requirement.name", source = "requirementName")
    @Mapping(target = "product.code", source = "productCode")
    @Mapping(target = "product.name", source = "productName")
    @Mapping(target = "findVersion.code", source = "findVersionCode")
    @Mapping(target = "findVersion.name", source = "findVersionName")
    @Mapping(target = "fixVersion.code", source = "fixVersionCode")
    @Mapping(target = "fixVersion.name", source = "fixVersionName")
    @Mapping(target = "sprint.code", source = "sprintCode")
    @Mapping(target = "sprint.name", source = "sprintName")
    Issue convert(IssueEntity entity);

    List<Attachment> convertList(Collection<AttachmentEntity> attachmentEntities);

    List<Tag> convertTagList(Collection<TagEntity> tagEntities);

    @Mapping(target = "creator.userId", source = "creatorId")
    @Mapping(target = "creator.userName", source = "creator")
    Comment coverter(CommentEntity comment);

    Set<Comment> convertCommentList(Collection<CommentEntity> commentEntities);

    Set<RelevantUser> convertRelevantUserList(Collection<RelevantUserEntity> relevantUserEntities);

    IssueBaseVO convertIssueBaseVO(IssueEntity entity);

    IssueQueryParameter covertPageIssueThingQuery(PageIssueThingQuery query);

    List<IssueBaseVO> convertIssueBaseVOList(List<IssueEntity> entities);

    IssueEntityDO convertDO(IssueEntity entity);

    List<IssueEntityDO> convertDOList(List<IssueEntity> entities);

    List<IssueLegacyVO> convertIssueLegacyVOList(List<IssueEntity> entities);

    List<IssueVO> convertVOList(List<IssueEntity> entityList);

    @Mapping(target = "validFlag", source = "isValid")
    IssueVO convertVO(IssueEntity entity);
}
