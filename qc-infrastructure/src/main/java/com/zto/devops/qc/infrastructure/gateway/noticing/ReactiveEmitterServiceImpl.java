package com.zto.devops.qc.infrastructure.gateway.noticing;

import com.zto.devops.framework.client.simple.ReactiveId;
import com.zto.devops.framework.infrastructure.util.noticing.ReactiveEmitter;
import com.zto.devops.qc.domain.gateway.noticing.ReactiveEmitterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * reactive emitter service impl
 *
 * <AUTHOR>
 * @date 2023-03-27 15:45
 */
@Slf4j
@Component
public class ReactiveEmitterServiceImpl implements ReactiveEmitterService {
    @Autowired
    private ReactiveEmitter reactiveEmitter;

    @Override
    public void emit(ReactiveId reactiveId, Object data) {
        reactiveEmitter.emit(reactiveId, data);
    }
}
