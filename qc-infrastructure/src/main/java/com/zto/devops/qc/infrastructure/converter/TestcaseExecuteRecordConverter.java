package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TestcaseExecuteRecordEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseExecuteRecordEntity;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface TestcaseExecuteRecordConverter {

    TestcaseExecuteRecordEntity convert2Entity(TestcaseExecuteRecordEntityDO executeRecordEntityDO);
}
