package com.zto.devops.qc.infrastructure.gateway.rpc;

import com.alibaba.dubbo.config.annotation.Reference;
import com.zto.devops.qc.client.model.rpc.user.ListUserQuery;
import com.zto.devops.qc.client.model.rpc.user.ProfileAddedSnapshotEvent;
import com.zto.devops.qc.client.model.rpc.user.UserSelectVO;
import com.zto.devops.qc.domain.gateway.rpc.IUserRpcService;
import com.zto.devops.qc.infrastructure.converter.RpcVOConvertor;
import com.zto.devops.user.client.service.user.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class UserRpcServiceImpl implements IUserRpcService {

    @Reference
    private IUserService userService;

    @Autowired
    private RpcVOConvertor rpcVOConvertor;

    @Override
    public List<UserSelectVO> listUserQuery(ListUserQuery query) {
        return rpcVOConvertor.covertUserSelectVO(userService.listUserQuery(rpcVOConvertor.covertListUserQuery(query)));
    }
}
