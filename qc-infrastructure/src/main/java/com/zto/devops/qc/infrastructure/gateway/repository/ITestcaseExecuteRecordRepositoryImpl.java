package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteCaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindPlanCaseQuery;
import com.zto.devops.qc.domain.gateway.repository.ITestcaseExecuteRecordRepository;
import com.zto.devops.qc.infrastructure.dao.mapper.TestcaseExecuteRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ITestcaseExecuteRecordRepositoryImpl implements ITestcaseExecuteRecordRepository {

    @Autowired
    private TestcaseExecuteRecordMapper testcaseExecuteRecordMapper;

    @Override
    public ExecuteCaseVO selectCurrentResult(FindPlanCaseQuery query) {
        return testcaseExecuteRecordMapper.selectCurrentResult(query);
    }
}
