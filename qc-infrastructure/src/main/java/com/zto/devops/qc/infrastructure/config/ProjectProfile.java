package com.zto.devops.qc.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @author: minjd
 * @date: Created in 2021/11/3 18:10
 */
@Data
@ConfigurationProperties("project")
@Component
public class ProjectProfile {

    private String frontUrl;

    private String productList;

    private String issueList;

    private String syncQAXDeptNos;

    private String issueMessageUrl;

}
