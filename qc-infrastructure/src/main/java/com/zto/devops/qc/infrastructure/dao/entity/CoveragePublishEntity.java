package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.rpc.FlowLaneTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.AppTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.DeploymentIdentityEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.AppTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.DeploymentIdentityHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.FlowLaneTypeHandler;
import lombok.Data;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @create 2022/9/16 17:16
 */
@ToString
@Data
@Table(name = "qc_coverage_publish")
public class CoveragePublishEntity extends BaseEntity {

    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 应用类型，JAVA-java应用，WEB-前端应用，GOLANG-go应用
     */
    @Column(name = "app_type")
    @ColumnType(typeHandler = AppTypeHandler.class)
    private AppTypeEnum appType;

    /**
     * 版本编码
     */
    @Column(name = "version_code")
    private String versionCode;

    /**
     * 版本名称
     */
    @Column(name = "version_name")
    private String versionName;

    /**
     * appId
     */
    @Column(name = "app_id")
    private String appId;

    /**
     * git提交id
     */
    @Column(name = "commit_id")
    private String commitId;

    /**
     * 分支名称
     */
    @Column(name = "branch_name")
    private String branchName;

    /**
     * git地址
     */
    @Column(name = "git_url")
    private String gitUrl;

    /**
     * 部署标识，VIRTUAL-虚拟机,DOCKER-容器
     */
    @Column(name = "deployment_identity")
    @ColumnType(typeHandler = DeploymentIdentityHandler.class)
    private DeploymentIdentityEnum deploymentIdentityEnum;

    /**
     * ip或实例名
     */
    @Column(name = "service_name")
    private String serviceName;

    /**
     * 端口号
     */
    @Column(name = "port")
    private Integer port;

    /**
     * 下载包名
     */
    @Column(name = "package_name")
    private String packageName;

    /**
     * 空间名称
     */
    @Column(name = "env_name")
    private String envName;

    /**
     * 合并后文件名
     */
    @Column(name = "output_file_name")
    private String outputFileName;

    /**
     * 发布泳道
     */
    @Column(name = "flow_lane_type")
    private String flowLaneType;

}