package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageTaskVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageVersionRateVO;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageRecordPageQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageResultQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageTaskQuery;
import com.zto.devops.qc.infrastructure.dao.entity.CoverageRecordEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/9/16 17:46
 */
public interface CoverageRecordMapper extends Mapper<CoverageRecordEntity> {

    List<CoverageRecordEntity> getCoverageRecordList(CoverageRecordPageQuery query);

    List<String> getCoverageResultList(CoverageResultQuery query);

    CoverageVersionRateVO getVersionRecordRate(@Param("versionCode") String versionCode);

    /**
     * 根据版本批量查询覆盖率
     * @param versionCode 版本编码
     * @return 覆盖率
     */
    List<CoverageVersionRateVO> getVersionRecordRateList(@Param("list") List<String> versionCode);

    List<CoverageTaskVO> selectTaskIdFromCoverage(CoverageTaskQuery query);

    List<CoverageTaskVO> selectCoverageByTaskId(CoverageTaskQuery query);
}
