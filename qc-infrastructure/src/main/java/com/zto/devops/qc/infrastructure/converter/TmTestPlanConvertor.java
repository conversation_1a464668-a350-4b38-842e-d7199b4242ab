package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestPlanRangeEntityDO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestFunctionPointVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.event.EditPlanStatusEvent;
import com.zto.devops.qc.client.model.testmanager.plan.event.SendTestPlanEvent;
import com.zto.devops.qc.infrastructure.dao.entity.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TmTestPlanConvertor {

    TmTestPlanEntity convert(TmTestPlanVO vo);

    TestFunctionPointEntity convert(TestFunctionPointVO vo);

    @Mapping(target = "code", source = "planCode")
    @Mapping(target = "type", source = "planType")
    TmTestPlanEntity convert(EditPlanStatusEvent event);

    @Mapping(target = "emailName", source = "name")
    @Mapping(target = "planPresentationDate", source = "accessDate")
    @Mapping(target = "planApprovalExitDate", source = "permitDate")
    @Mapping(target = "relatePlanCode", source = "relationPlanCode")
    @Mapping(target = "relatePlanName", source = "relationPlanName")
    TmEmailEntity convert(SendTestPlanEvent event);

    TmTestPlanEntityDO covert(TmTestPlanEntity tmTestPlanEntity);

    List<TmTestPlanEntityDO> covertList(List<TmTestPlanEntity> tmTestPlanEntityList);

    TestPlanEntityDO oldConvert(TestPlanEntity testPlanEntity);

    List<TestPlanEntityDO> oldConvertList(List<TestPlanEntity> testPlanEntityList);

    TmTestPlanRangeEntityDO covert(TmTestPlanRangeEntity entity);

    TmTestPlanEntity convert2Entity(TmTestPlanEntityDO updateEntityDO);

    List<TmTestPlanVO> converter2VO(List<TmTestPlanEntity> entityList);
}
