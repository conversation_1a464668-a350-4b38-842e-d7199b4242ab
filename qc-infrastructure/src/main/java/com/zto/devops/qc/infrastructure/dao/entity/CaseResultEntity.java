package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "qc_case_result")
public class CaseResultEntity extends BaseEntity {

    /**
     * 编号code
     */
    @Id
    private String code;

    /**
     * 报告code
     */
    @Column(name = "report_code")
    private String reportCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 用例总数
     */
    @Column(name = "total_case_count")
    private Integer totalCaseCount;

    /**
     * 执行的用例总数
     */
    @Column(name = "execute_case_count")
    private Integer executeCaseCount;

    /**
     * 轮次
     */
    @Column(name = "rounds")
    private Integer rounds;

    /**
     * 通过的用例总数
     */
    @Column(name = "access_case_count")
    private Integer accessCaseCount;

    /**
     * 失败的用例总数
     */
    @Column(name = "failed_case_count")
    private Integer failedCaseCount;

    /**
     * 阻塞的用例总数
     */
    @Column(name = "blocked_case_count")
    private Integer blockedCaseCount;

    private static final long serialVersionUID = 1L;

}