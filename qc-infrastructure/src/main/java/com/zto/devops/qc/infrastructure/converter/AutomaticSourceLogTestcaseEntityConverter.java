package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticSourceLogTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AddTestcaseEvent;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceLogTestcaseEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AutomaticSourceLogTestcaseEntityConverter {

    AutomaticSourceLogTestcaseEntity convert(AddTestcaseEvent event);

    AutomaticSourceLogTestcaseVO convertVO(AutomaticSourceLogTestcaseEntity entity);

    List<AutomaticSourceLogTestcaseVO> convertVOList(List<AutomaticSourceLogTestcaseEntity> list);
}
