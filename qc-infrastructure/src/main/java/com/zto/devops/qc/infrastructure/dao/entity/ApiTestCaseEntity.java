package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.client.simple.HasTransactor;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiConfigTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.ApiCaseEnableTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.ApiCaseGenerateRulesHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.ApiTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseStatusHandler;
import lombok.Data;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ToString
@Data
@Table(name = "tm_api_test_case")
public class ApiTestCaseEntity {

    @Column(name = "id")
    @Id
    private Long id;

    /**
     * 用例编码
     */
    @Column(name = "case_code")
    private String caseCode;

    /**
     * 用例名称
     */
    @Column(name = "case_name")
    private String caseName;

    /**
     * 产品编码
     */
    @Column(name = "product_code")
    private String productCode;

    /**
     * 接口编码
     */
    @Column(name = "api_code")
    private String apiCode;

    /**
     * 用例状态
     */
    @Column(name = "status")
    private ApiCaseStatusEnum status;

    /**
     * 用例类型：1-接口用例
     */
    @Column(name = "case_type")
    private Integer caseType;

    /**
     * 用例请求数据
     */
    @Column(name = "case_req_data")
    private String caseReqData;

    /**
     * 父用例编码
     */
    @Column(name = "parent_case_code")
    private String parentCaseCode;

    /**
     * 最新任务id
     */
    @Column(name = "latest_task_id")
    private String latestTaskId;

    /**
     * 最近一次执行时间
     */
    @Column(name = "latest_execute_time")
    private Date latestExecuteTime;

    /**
     * 最近一次运行结果
     */
    @Column(name = "latest_execute_result")
    @ColumnType(typeHandler = TmTestPlanCaseStatusHandler.class)
    private TestPlanCaseStatusEnum latestExecuteResult;

    /**
     * 关联API地址
     */
    @Column(name = "related_api_address")
    private String relatedApiAddress;

    /**
     * 关联API中文名称
     */
    @Column(name = "related_api_name")
    private String relatedApiName;

    /**
     * 请求类型
     */
    @Column(name = "api_type")
    @ColumnType(typeHandler = ApiTypeHandler.class)
    private ApiTypeEnum apiType;

    /**
     * 文档版本
     */
    @Column(name = "doc_version")
    private String docVersion;

    /**
     * 标签值(多标签英文逗号分隔）
     */
    @Column(name = "tag_value")
    private String tagValue;

    /**
     * 是否发布（0未发布,1已发布）
     */
    @Column(name = "published")
    private Boolean published;

    /**
     * 生成字段来源
     */
    @Column(name = "generate_field_source")
    private String generateFieldSource;

    /**
     * 生成规则
     */
    @Column(name = "generate_rules")
    @ColumnType(typeHandler = ApiCaseGenerateRulesHandler.class)
    private ApiConfigTypeEnum generateRules;

    /**
     * 是否删除：1启用，0删除，2停用
     */
    @Column(name = "enable")
    @ColumnType(typeHandler = ApiCaseEnableTypeHandler.class)
    private ApiCaseEnableEnum enable;

    private Long creatorId;

    private String creator;

    private Date gmtCreate;

    private Long modifierId;

    private String modifier;

    private Date gmtModified;

    /**
     * 初始化状态: 0-未初始化 1-已初始化 2-初始化失败
     */
    @Column(name = "init_status")
    private Integer initStatus;

    @Column(name = "automatic_source_code")
    private String automaticSourceCode;

    public void preCreate(HasTransactor hasTransactor) {
        User user = hasTransactor.getTransactor();
        if (null != user) {
            this.setCreatorId(user.getUserId());
            this.setCreator(user.getUserName());
            this.setGmtCreate(new Date());
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }

    public void preUpdate(HasTransactor hasTransactor) {
        User user = hasTransactor.getTransactor();
        if (null != user) {
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }

    public void preUpdate(User user) {
        if (null != user) {
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }
}
