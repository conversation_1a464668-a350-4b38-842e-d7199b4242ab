package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseStatusHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanStageHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
@Table(name = "tm_test_plan_case")
@Data
public class TmTestPlanCaseEntity extends BaseEntity implements Serializable {
    /**
     * 自增ID
     */
    @Id
    private Long id;

    /**
     * 关联计划code
     */
    private String planCode;

    /**
     * 用例类型，MANUAL-手工|AUTO-自动化
     */
    @ColumnType(typeHandler = TmTestPlanCaseTypeHandler.class)
    private TestcaseTypeEnum caseType;

    /**
     * 测试阶段，SMOKE_TEST-冒烟测试|FUNCTIONAL_TEST-功能测试|ONLINE_SMOKE_TEST-线上冒烟测试
     */
    @ColumnType(typeHandler = TmTestPlanStageHandler.class)
    private TestPlanStageEnum testStage;

    /**
     * 用例编号
     */
    private String caseCode;

    /**
     * 执行结果，INITIAL-未执行|PASSED-通过|FAILED-失败|BLOCK-阻塞|SKIP-跳过|RETEST-重测
     */
    @ColumnType(typeHandler = TmTestPlanCaseStatusHandler.class)
    private TestPlanCaseStatusEnum status;


    /**
     * 执行人编码
     */
    private Long executorId;

    /**
     * 执行人
     */
    private String executor;

    /**
     * 执行结果路径
     */
    private String resultPath;

    /**
     * 执行日志路径
     */
    private String logPath;

    private String operateCaseCode;

    /**
     * 结果备注
     */
    private String resultComment;


    private static final long serialVersionUID = 1L;

}