package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.qc.client.model.dto.AttachmentEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseAttachmentAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseAttachmentRemovedEvent;
import com.zto.devops.qc.domain.gateway.repository.TestcaseAttachmentRepository;
import com.zto.devops.qc.domain.model.TestcaseAttachment;
import com.zto.devops.qc.infrastructure.converter.TestcaseAttachmentConverter;
import com.zto.devops.qc.infrastructure.dao.entity.AttachmentEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.AttachmentMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Component
@Slf4j
public class TestcaseAttachmentRepositoryImpl implements TestcaseAttachmentRepository {


    @Autowired
    AttachmentMapper attachmentMapper;

    @Autowired
    TestcaseAttachmentConverter testcaseAttachmentConverter;

    @Override
    public void addTestcaseAttachment(TestcaseAttachmentAddedEvent event) {
        AttachmentEntity entity = testcaseAttachmentConverter.converter(event);
        entity.setEnable(Boolean.TRUE);
        attachmentMapper.insertSelective(entity);
    }

    @Override
    public int selectCountByCaseCodeList(String businessCode) {
        Example example = new Example(AttachmentEntity.class);
        example.createCriteria().andEqualTo("businessCode", businessCode)
                .andEqualTo("enable", Boolean.TRUE);
        return attachmentMapper.selectCountByExample(example);
    }

    @Override
    public TestcaseAttachment loadFormDb(String s) {
        AttachmentEntity entity = attachmentMapper.selectByPrimaryKey(s);
        return testcaseAttachmentConverter.domainConverter(entity);
    }

    @Override
    public List<AttachmentEntityDO> selectTestcaseAttachmentByCaseCodeList(List<String> caseCodes) {
        Example attachmentExample = new Example(AttachmentEntity.class);
        attachmentExample.orderBy("gmtCreate").asc();
        attachmentExample.createCriteria()
                .andIn("businessCode", caseCodes)
                .andEqualTo("enable", Boolean.TRUE);
        return testcaseAttachmentConverter.convertList(attachmentMapper.selectByExample(attachmentExample));
    }

    @Override
    public void insertSelective(AttachmentEntityDO entityDO) {
        attachmentMapper.insertSelective(testcaseAttachmentConverter.convert2(entityDO));
    }

    @Override
    public void removeTestcaseAttachment(TestcaseAttachmentRemovedEvent event) {
        attachmentMapper.deleteByPrimaryKey(event.getCode());
    }

}
