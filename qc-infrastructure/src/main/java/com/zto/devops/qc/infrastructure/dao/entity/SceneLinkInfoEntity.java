package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.client.simple.HasTransactor;
import com.zto.devops.framework.client.simple.User;
import lombok.*;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "tm_scene_link_info")
public class SceneLinkInfoEntity  {

    private String productCode;

    private String sceneCode;

    private Integer sceneVersion;

    private String linkMapCode;

    /**
     * 是否删除 1 停用, 0 正常
     */
    private Boolean enable;

    /**
     * 节点或者线的code*
     */
    private String linkComponentCode;

    /**
     * 节点或者线的名称*
     */
    private String linkComponentName;

    /**
     * 类型：NODE;LINE*
     */
    private String linkComponentType;

    /**
     * 顺序号*
     */
    private Integer sequenceNumber;

    /**
     * 创建人编码
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新时间
     */
    private Date gmtModified;

    public void preCreate(User user) {
        if (null != user) {
            this.setCreatorId(user.getUserId());
            this.setCreator(user.getUserName());
            this.setGmtCreate(new Date());
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }

    public void preUpdate(User user) {
        if (null != user) {
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }

}
