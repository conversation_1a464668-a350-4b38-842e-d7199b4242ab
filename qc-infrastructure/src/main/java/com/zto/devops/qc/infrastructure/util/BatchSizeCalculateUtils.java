package com.zto.devops.qc.infrastructure.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 批次大小动态计算器
 * 根据MySQL数据包大小限制和对象大小动态计算最优批次大小
 */
@Component
@Slf4j
public class BatchSizeCalculateUtils {

    // MySQL默认max_allowed_packet大小（16MB）
    private static final long DEFAULT_MAX_PACKET_SIZE = 16 * 1024 * 1024; // 16MB

    // 安全系数，预留20%的空间给SQL语句本身
    private static final double SAFETY_FACTOR = 0.8;

    // 最小批次大小
    private static final int MIN_BATCH_SIZE = 10;

    // 最大批次大小
    private static final int MAX_BATCH_SIZE = 500;

    /**
     * 计算MethodDependenceEntity的最优批次大小
     *
     * @param totalCount 总数据量
     * @param estimatedRecordSize 估算的SQL语句大小(byte)
     * @return 最优批次大小
     */
    public static int calculateOptimalBatchSize(int totalCount, int estimatedRecordSize) {
        return calculateOptimalBatchSize(totalCount, estimatedRecordSize, DEFAULT_MAX_PACKET_SIZE);
    }

    /**
     * 根据指定的数据包大小计算最优批次大小
     *
     * @param totalCount    总数据量
     * @param maxPacketSize MySQL max_allowed_packet大小
     * @return 最优批次大小
     */
    public static int calculateOptimalBatchSize(int totalCount, int estimatedRecordSize, long maxPacketSize) {
        // 计算可用的数据包大小（考虑安全系数）
        long availablePacketSize = (long) (maxPacketSize * SAFETY_FACTOR);

        // 计算理论最大批次大小
        int theoreticalMaxBatchSize = (int) (availablePacketSize / estimatedRecordSize);

        // 应用约束条件
        int optimalBatchSize = Math.max(MIN_BATCH_SIZE, Math.min(MAX_BATCH_SIZE, theoreticalMaxBatchSize));

        // 根据总数据量进行调整
        optimalBatchSize = adjustBatchSizeByTotalCount(optimalBatchSize, totalCount);

        log.info("批次大小计算 - 总数据量: {}, 估算SQL大小: {}字节/条, 最大数据包: {}MB, 计算批次大小: {}",
                totalCount, estimatedRecordSize, maxPacketSize / 1024 / 1024, optimalBatchSize);
        return optimalBatchSize;
    }

    /**
     * 根据总数据量调整批次大小
     *
     * @param calculatedBatchSize 计算出的批次大小
     * @param totalCount          总数据量
     * @return 调整后的批次大小
     */
    private static int adjustBatchSizeByTotalCount(int calculatedBatchSize, int totalCount) {
        // 如果总数据量很小，直接使用总数据量
        if (totalCount <= MIN_BATCH_SIZE) {
            return totalCount;
        }

        // 如果总数据量小于计算出的批次大小，使用总数据量
        if (totalCount < calculatedBatchSize) {
            return totalCount;
        }

        // 优化批次数量，避免最后一批数据量过小
        int batchCount = (totalCount + calculatedBatchSize - 1) / calculatedBatchSize;
        int optimizedBatchSize = (totalCount + batchCount - 1) / batchCount;

        // 确保优化后的批次大小在合理范围内
        return Math.max(MIN_BATCH_SIZE, Math.min(MAX_BATCH_SIZE, optimizedBatchSize));
    }
}
