package com.zto.devops.qc.infrastructure.gateway.message;

import cn.hutool.json.JSONUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.infrastructure.service.ZimManager;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.model.message.TestPlanOrReportMsg;
import com.zto.devops.qc.client.model.testmanager.plan.event.SendTestPlanEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.ReportAddedEvent;
import com.zto.devops.qc.domain.gateway.message.ITestReportOrPlanMsgAuditor;
import com.zto.devops.qc.domain.gateway.repository.IQueryUserMange;
import com.zto.devops.qc.domain.gateway.repository.QcNoticeResultRepository;
import com.zto.devops.qc.infrastructure.config.PlanProfile;
import com.zto.devops.qc.infrastructure.message.MessageConstant;
import com.zto.message.dto.clientobject.BatchMessageCO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Component
@Slf4j
public class ITestReportOrPlanOrPlanMsgAuditorImpl implements ITestReportOrPlanMsgAuditor {

    @Autowired
    private IQueryUserMange queryUserManage;

    @Autowired
    private QcNoticeResultRepository qcNoticeResultRepository;

    @Autowired
    private PlanProfile planProfile;

    @Autowired
    private ZimManager messageManager;


    @Async
    @Override
    public void sendMessage(TestPlanOrReportMsg testPlanOrReportMsg) {
        if (testPlanOrReportMsg == null) {
            return;
        }
        List<BatchMessageCO> batchList = new ArrayList<>();
        BatchMessageCO co = new BatchMessageCO();
        co.setBizId(UUID.randomUUID().toString());
        co.putPayload("subject", testPlanOrReportMsg.getSubject());
        co.putPayload("sender", testPlanOrReportMsg.getSender());
        co.putPayload("copyPerson", testPlanOrReportMsg.getCopyPerson());
        co.putPayload("receiver", testPlanOrReportMsg.getReceiver());
        co.putPayload("content", testPlanOrReportMsg.getContent());
        co.putPayload("attachment", testPlanOrReportMsg.getAttachment());
        co.setBizId(UUID.randomUUID().toString());
        batchList.add(co);
        String s = messageManager.sendMessage(batchList, MessageConstant.PLAN_OR_REPORT_EMAIL);
        log.info("send report email req {} ,resp {}", JSONUtil.toJsonStr(batchList), s);
    }

    @Override
    public TestPlanOrReportMsg doHandleReportAddedEvent(ReportAddedEvent event) {
        log.info("测试报告邮件");
        if (!Objects.nonNull(event)) {
            return null;
        }
        if (TestPlanStatusEnum.DRAFT.equals(event.getStatus())) {
            log.info("报告草稿不发送 {}", event.getReportCode());
            return null;
        }

        TestPlanOrReportMsg msg = new TestPlanOrReportMsg();
        String email = queryUserManage.getEmailForUser(event.getCcUsers());
        if (StringUtil.isBlank(email)) {
            email = planProfile.getSqaEmail();
        } else {
            email = email + "," + planProfile.getSqaEmail();
        }
        msg.setContent(event.getPreview());
        msg.setSender(planProfile.getSenderEmail());
        msg.setReceiver(queryUserManage.getEmailForUser(event.getReceiveUsers()));
        msg.setCopyPerson(email);
        msg.setSubject(event.getReportName());
        msg.setAttachment(qcNoticeResultRepository.getAttachment(event.getAttachments()));
        return msg;
    }

    @Override
    public TestPlanOrReportMsg doHandleSendTestPlanEvent(SendTestPlanEvent event) {
        if (Objects.isNull(event)) {
            return null;
        }
        TestPlanOrReportMsg msg = new TestPlanOrReportMsg();
        String email = queryUserManage.getEmailForUser(event.getCcUsers());
        if (StringUtil.isBlank(email)) {
            email = planProfile.getSqaEmail();
        } else {
            email = email + "," + planProfile.getSqaEmail();
        }
        msg.setContent(event.getPreview());
        msg.setSender(planProfile.getSenderEmail());
        msg.setReceiver(queryUserManage.getEmailForUser(event.getReceiveUsers()));
        msg.setCopyPerson(email);
        msg.setSubject(event.getName());
        return msg;
    }
}
