package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "qc_statistic_case_result")
public class TmStatisticCaseResultEntity extends BaseEntity {

    /**
     * 报告code
     */
    @Column(name = "report_code")
    private String reportCode;

    /**
     * 用例code
     */
    @Id
    private String code;

    @Column(name = "frog_plan_code")
    private String frogPlanCode;

    @Column(name = "frog_plan_name")
    private String frogPlanName;

    /**
     * 用例数
     */
    private Long num;

    /**
     * 占比
     */
    private Double ratio;

    /**
     * 结果
     */
    private String result;

    private static final long serialVersionUID = 1L;

}