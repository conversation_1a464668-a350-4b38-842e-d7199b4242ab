package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.SceneIndexVO;
import com.zto.devops.qc.client.service.testmanager.apitest.model.SceneModuleQueryReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.SharedSceneModuleQueryReq;
import com.zto.devops.qc.infrastructure.dao.entity.SceneIndexEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface SceneIndexMapper extends Mapper<SceneIndexEntity> {

    void batchInsert(List<SceneIndexEntity> list);

    List<SceneIndexVO> queryAllSceneModule(SceneModuleQueryReq req);

    List<SceneIndexEntity> queryModuleByParentCode(String parentCode);

    List<SceneIndexVO> selectSceneIndexModuleInfo(@Param("productCode") String productCode);

    List<SceneIndexVO> selectSimpleSceneIndex(@Param("productCode") String productCode);

    List<SceneIndexVO> selectSharedPreData(SharedSceneModuleQueryReq req);

    List<SceneIndexVO> selectSharedPreDataModuleByProduct(@Param("list") List<String> list);
}
