package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TagEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagAddedEvent;
import com.zto.devops.qc.infrastructure.dao.entity.TagEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TestcaseTagConverter {

    @Mapping(target = "creatorId", source = "transactor.userId")
    @Mapping(target = "creator", source = "transactor.userName")
    @Mapping(target = "gmtCreate", source = "occurred")
    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "gmtModified", source = "occurred")
    TagEntity converter(TestcaseTagAddedEvent event);

    TagEntityDO convert(TagEntity entity);

    List<TagEntityDO> convertList(List<TagEntity> entityList);

    TagEntity convert2(TagEntityDO entityDO);

    List<TagVO> convertVOList(List<TagEntity> tagList);

    TagVO convertVO(TagEntity entity);

    List<TagVO> convert(List<TagEntity> entity);

}
