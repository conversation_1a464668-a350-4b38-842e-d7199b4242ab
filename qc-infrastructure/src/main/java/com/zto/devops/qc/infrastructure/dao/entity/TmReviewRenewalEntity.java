package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "tm_review_renewal")
public class TmReviewRenewalEntity extends BaseEntity {

    /**
     * 评审报告code
     */
    @Column(name = "report_code")
    private String reportCode;
    /**
     * 编号
     */
    @Column(name = "serial_id")
    private String serialId;
    /**
     * code
     */
    @Id
    private String code;

    /**
     * 评审前信息
     */
    @Column(name = "review_before")
    private String reviewBefore;

    /**
     * 评审后信息
     */
    @Column(name = "review_after")
    private String reviewAfter;

    /**
     * 更新内容
     */
    @Column(name = "renewal_content")
    private String renewalContent;

    private static final long serialVersionUID = 1L;

}
