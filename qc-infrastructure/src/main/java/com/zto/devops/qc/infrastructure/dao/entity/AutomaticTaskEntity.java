package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticExecuteModeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.*;
import lombok.Getter;
import lombok.Setter;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * xby
 */
@Getter
@Setter
@Table(name = "tm_automatic_task")
public class AutomaticTaskEntity extends BaseEntity {

    private Long id;

    /**
     * 编号
     */
    @Id
    private String code;

    /**
     * taskid，可重复，因为一个任务可能会并行执行多个子任务
     */
    private String taskId;

    /**
     * Jenkins构建id
     */
    private String buildId;

    /**
     * 自动化用例源登记code
     */
    private String automaticSourceCode;

    /**
     * 所属产品id
     */
    private String productCode;

    /**
     * 自动化用例源地址目录
     */
    private String sourceAddress;

    /**
     * jmeter:jmx脚本文件名
     */
    private String filename;

    /**
     * 类型：1-jmeter, 2-testng, 3-postman, 4-junit,5-pyunit
     */
    @ColumnType(typeHandler = AutomaticRecordTypeHandler.class)
    private AutomaticRecordTypeEnum type;

    /**
     * 类型：1-api调用jenkins, 2-定时任务
     */
    @ColumnType(typeHandler = AutomaticExecuteModeTypeHandler.class)
    private AutomaticExecuteModeEnum executeMode;

    /**
     * 测试环境
     */
    private String env;

    /**
     * 运行空间tag，用于场景用例执行
     */
    private String executeTag;

    /**
     * api调用jenkins则为调用的httpurl，定时任务则为crontab表达式
     */
    private String content;

    /**
     * 状态：0-未开始，1-已提交, 2-运行中, 3-成功，4-失败, 5-中止
     */
    @ColumnType(typeHandler = AutomaticStatusHandler.class)
    private AutomaticStatusEnum status;

    /**
     * 执行结果文件地址，一般是csv/xml或者json格式的方便解析
     */
    private String resultFile;

    /**
     * 测试报告，一般是html格式
     */
    private String reportFile;

    /**
     * 执行日志地址
     */
    private String execLogFile;

    /**
     * 备注失败原因之类的
     */
    private String comment;

    /**
     * 测试计划code
     */
    private String testPlanCode;

    /**
     * 测试阶段
     */
    @ColumnType(typeHandler = TmTestPlanStageHandler.class)
    private TestPlanStageEnum testStage;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date finishTime;

    /**
     * 执行用例结果地址
     */
    private String caseFile;

    /**
     * 版本code
     */
    private String versionCode;

    /**
     * 触发方式
     */
    @ColumnType(typeHandler = AutomaticTrigModeTypeHandler.class)
    private AutomaticTaskTrigModeEnum trigMode;

    /**
     * testng：分支
     */
    private String branchName;

    /**
     * testng：工作目录
     */
    private String workDir;

    /**
     * testng：commit id
     */
    private String commitId;

    /**
     * 是否生成覆盖率
     */
    private Boolean coverageFlag;

    /**
     * 错误日志地址
     */
    private String errorLogFile;

    /**
     * 定时任务code
     */
    private String schedulerCode;
}
