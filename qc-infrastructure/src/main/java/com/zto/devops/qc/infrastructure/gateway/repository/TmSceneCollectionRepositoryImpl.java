package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.dto.SceneCollectionEntityDO;
import com.zto.devops.qc.domain.gateway.repository.TmSceneCollectionRepository;
import com.zto.devops.qc.infrastructure.converter.TmSceneCollectionEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.SceneCollectionEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.SceneCollectionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TmSceneCollectionRepositoryImpl implements TmSceneCollectionRepository {

    @Autowired
    private SceneCollectionMapper sceneCollectionMapper;

    @Autowired
    private TmSceneCollectionEntityConverter tmSceneCollectionEntityConverter;

    @Override
    public void saveFavoritesPreData(List<SceneCollectionEntityDO> sharePreDataSceneAndModuleByProduct) {
        sceneCollectionMapper.batchInsert(tmSceneCollectionEntityConverter.convertSceneCollectionEntityList(sharePreDataSceneAndModuleByProduct));
    }

    @Override
    public int deleteFavoritesPreData(List<SceneCollectionEntityDO> sharePreDataSceneAndModuleByProduct, User user) {
        List<String> collect = sharePreDataSceneAndModuleByProduct.stream().map(SceneCollectionEntityDO::getSceneIndexCode).collect(Collectors.toList());
        Example example = new Example(SceneCollectionEntity.class);
        example.createCriteria()
                .andIn("sceneIndexCode", collect)
                .andEqualTo("creatorId", user.getUserId());
        return sceneCollectionMapper.deleteByExample(example);
    }

    @Override
    public int deleteFavoritesPreDataByProductCode(String productCode, User user) {
        Example example = new Example(SceneCollectionEntity.class);
        example.createCriteria()
                .andEqualTo("productCode", productCode)
                .andEqualTo("creatorId", user.getUserId());
        return sceneCollectionMapper.deleteByExample(example);
    }

    @Override
    public List<SceneCollectionEntityDO> selectFavoritesPreData(Long userId) {
        Example example = new Example(SceneCollectionEntity.class);
        example.createCriteria()
                .andEqualTo("creatorId", userId);
        example.orderBy("gmtCreate").desc();
        return tmSceneCollectionEntityConverter.convertSceneCollectionEntityDOList(sceneCollectionMapper.selectByExample(example));
    }
}
