package com.zto.devops.qc.infrastructure.gateway.repository;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.client.enums.testmanager.email.EmailSourceEnum;
import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.email.TmEmailDataSourceEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.enums.testmanager.report.TestReportTypeEnum;
import com.zto.devops.qc.client.model.dto.TmEmailEntityDO;
import com.zto.devops.qc.client.model.testmanager.email.entity.*;
import com.zto.devops.qc.client.model.testmanager.email.query.DetailEmailQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.PageEmailQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.TestPlanToSendMailQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.VersionEmailQuery;
import com.zto.devops.qc.client.model.testmanager.report.entity.EmailMemberVO;
import com.zto.devops.qc.client.model.testmanager.report.event.ReportAddedEvent;
import com.zto.devops.qc.domain.gateway.repository.IEmailRepository;
import com.zto.devops.qc.domain.gateway.repository.ITmTestReportRepository;
import com.zto.devops.qc.domain.gateway.repository.QcNoticeResultRepository;
import com.zto.devops.qc.infrastructure.config.TransferEmailProfile;
import com.zto.devops.qc.infrastructure.converter.EmailVOConverter;
import com.zto.devops.qc.infrastructure.dao.entity.QcNoticeResultEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmEmailEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmVersionEmailEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.QcNoticeResultMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.TestPlanMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.TestReportMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.TmEmailMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EmailRepositoryImpl implements IEmailRepository {

    @Autowired
    private TmEmailMapper tmEmailMapper;
    @Autowired
    private TestReportMapper testReportMapper;
    @Autowired
    private EmailVOConverter emailVOConverter;
    @Autowired
    private ITmTestReportRepository iTmTestReportRepository;

    @Autowired
    private QcNoticeResultMapper qcNoticeResultMapper;

    @Autowired
    private QcNoticeResultRepository qcNoticeResultRepository;
    @Autowired
    private TransferEmailProfile transferEmailProfile;
    @Autowired
    private TestPlanMapper testPlanMapper;

    @Override
    public PageEmailVO pageEmail(PageEmailQuery query) {
        PageEmailVO emailVO = new PageEmailVO();
        Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        Example example = new Example(TmEmailEntity.class);
        example.selectProperties("emailCode", "emailName", "emailType", "businessCode", "relatePlanCode", "relatePlanName", "productCode",
                "versionCode", "versionName", "sendDate", "senderId", "sender", "planPresentationDate", "planApprovalExitDate");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", Boolean.TRUE);
        if (CollectionUtil.isNotEmpty(query.getTypeList())) {
            criteria.andIn("emailType", query.getTypeList());
        }
        if (CollectionUtil.isNotEmpty(query.getSendUserIdList())) {
            criteria.andIn("senderId", query.getSendUserIdList());
        }
        if (CollectionUtil.isNotEmpty(query.getVersionCodeList())) {
            criteria.andIn("versionCode", query.getVersionCodeList());
        }
        if (CollectionUtil.isNotEmpty(query.getPlanCodeList())) {
            criteria.andIn("relatePlanCode", query.getPlanCodeList());
        }
        if (Objects.nonNull(query.getSendTimeStart()) && Objects.nonNull(query.getSendTimeEnd())) {
            criteria.andBetween("sendDate", query.getSendTimeStart(), query.getSendTimeEnd());
        }
        if (Objects.nonNull(query.getEmailSourceEnum())) {
            criteria.andEqualTo("emailSource", query.getEmailSourceEnum().name());
        }
        if (query.getIsPersonal() && Objects.nonNull(query.getTransactor())) {
            criteria.andEqualTo("senderId", query.getTransactor().getUserId());
        }
        if (StringUtils.isNotBlank(query.getProductCode())) {
            criteria.andEqualTo("productCode", query.getProductCode());

        }
        Example.Criteria orCriteria = example.createCriteria();
        if (StringUtils.isNotBlank(query.getNameOrCode())) {
            orCriteria.orLike("emailName", "%" + query.getNameOrCode() + "%")
                    .orLike("businessCode", "%" + query.getNameOrCode() + "%")
                    .orLike("emailCode", "%" + query.getNameOrCode() + "%");
        }
        example.and(orCriteria);
        example.orderBy("sendDate").desc();
        List<TmEmailEntity> entityList = tmEmailMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(entityList)) {
            emailVO.setEmailVOS(emailVOConverter.convert(entityList));
            emailVO.setTotal(page.getTotal());
        } else {
            emailVO.setEmailVOS(new ArrayList<>());
            emailVO.setTotal(0L);
        }
        return emailVO;
    }

    @Override
    public Boolean testPlanToSendMail(TestPlanToSendMailQuery query) {
        Example example = new Example(TmEmailEntity.class);
        example.createCriteria()
                .andEqualTo("emailType", query.getPlanType())
                .andEqualTo("businessCode", query.getPlanCode())
                .andEqualTo("enable", Boolean.TRUE);
        List<TmEmailEntity> tmEmailEntities = tmEmailMapper.selectByExample(example);
        return CollectionUtils.isNotEmpty(tmEmailEntities);
    }

    @Override
    public List<VersionEmailVO> getVersionEmail(VersionEmailQuery query) {
        List<TmVersionEmailEntity> entityList = tmEmailMapper.selectVersionEmail(query);
        List<VersionEmailVO> versionEmailVOS = emailVOConverter.convert(entityList);
        return versionEmailVOS;
    }

    @Override
    public DetailEmailVO findEmailDetail(DetailEmailQuery query) {
        Example example = new Example(TmEmailEntity.class);
        example.selectProperties("emailCode", "emailName", "emailType", "businessCode", "emailSource", "relatePlanCode",
                "productCode", "versionCode", "sendDate", "senderId", "sender", "preview", "dataSource");
        if (StringUtils.isNotBlank(query.getEmailCode())) {
            //邮件code不为空，按邮件code查询
            example.createCriteria().andEqualTo("emailCode", query.getEmailCode());

        } else if (StringUtils.isNotBlank(query.getBusinessCode())) {
            //邮件code为空，按关联code查询 &时间倒序 &最后发送邮件记录
            example.createCriteria().andEqualTo("businessCode", query.getBusinessCode());
            example.orderBy("sendDate").desc();

        } else if (StringUtils.isNotBlank(query.getRelatePlanCode()) && !Objects.isNull(query.getEmailType())) {
            //按关联计划code 和 报告类型 查询 &时间倒序 &最后发送邮件记录
            example.createCriteria()
                    .andEqualTo("relatePlanCode", query.getRelatePlanCode())
                    .andEqualTo("emailType", query.getEmailType().name());
            example.orderBy("sendDate").desc();
        }
        List<TmEmailEntity> entityList = tmEmailMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(entityList)) {
            return null;
        }
        //邮件基础信息
        DetailEmailVO result = emailVOConverter.convert(entityList.get(0));
        return result;
    }

    @Override
    public List<TmEmailEntityDO> selectEnableByRelatePlanCodeAndEmailType(String relatePlanCode, EmailTypeEnum emailType) {
        Example example = new Example(TmEmailEntity.class);
        example.createCriteria().andEqualTo("relatePlanCode", relatePlanCode)
                .andEqualTo("emailType", emailType)
                .andEqualTo("enable", Boolean.TRUE);
        List<TmEmailEntity> entityList = tmEmailMapper.selectByExample(example);
        return CollectionUtils.isEmpty(entityList) ? new ArrayList<>() : emailVOConverter.convert2DOList(entityList);
    }

    @Override
    public List<EmailMemberVO> queryEmailMembers(String reportCode) {
        List<EmailMemberVO> list = new LinkedList<>();
        if (StringUtils.isBlank(reportCode)) {
            return list;
        }

        //查最后一封邮件code
        String emailCode = getLatestEmailCode(reportCode);
        if (StringUtils.isBlank(emailCode)) {
            return list;
        }

        Example example = new Example(QcNoticeResultEntity.class);
        example.createCriteria().andEqualTo("businessCode", emailCode).andEqualTo("enable", true);
        List<QcNoticeResultEntity> entities = qcNoticeResultMapper.selectByExample(example);
        entities.forEach(t -> {
            EmailMemberVO vo = new EmailMemberVO();
            vo.setEmail(t.getEmail());
            vo.setUserId(t.getUserId());
            vo.setUserName(t.getUserName());
            vo.setAvatar(t.getUserAvatar());
            vo.setUserTypeCode(t.getUserType());
            list.add(vo);
        });
        return list;
    }

    /**
     * 查当前报告发的最近一封邮件code
     * @param businessCode
     * @return
     */
    private String getLatestEmailCode(String businessCode) {
        Example example = new Example(TmEmailEntity.class);
        example.createCriteria().andEqualTo("businessCode", businessCode)
                .andEqualTo("enable", Boolean.TRUE);
        example.orderBy("gmtModified").desc();
        List<TmEmailEntity> entityList = tmEmailMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(entityList)) {
            return entityList.get(0).getEmailCode();
        }
        return "";
    }

    @Override
    public List<TmEmailEntityDO> selectByBusinessCode(String businessCode) {
        Example example = new Example(TmEmailEntity.class);
        example.createCriteria()
                .andEqualTo("businessCode", businessCode)
                .andEqualTo("enable", Boolean.TRUE);
        List<TmEmailEntity> entityList = tmEmailMapper.selectByExample(example);
        return CollectionUtils.isEmpty(entityList) ? new ArrayList<>() : emailVOConverter.convert2DOList(entityList);
    }

    @Override
    public List<TmEmailEntityDO> getRelatedPlanList(String versionCode, TestPlanNewTypeEnum planType) {
        Example example = new Example(TmEmailEntity.class);
        example.createCriteria()
                .andEqualTo("versionCode", versionCode)
                .andNotEqualTo("emailType", planType)
                .andEqualTo("emailSource", EmailSourceEnum.TEST_PLAN.name())
                .andEqualTo("enable", Boolean.TRUE);
        example.orderBy("sendDate").desc();
        List<TmEmailEntity> entityList = tmEmailMapper.selectByExample(example);
        return CollectionUtils.isEmpty(entityList) ? new ArrayList<>() : emailVOConverter.convert2DOList(entityList);
    }

    @Override
    public List<TmEmailEntityDO> getRelatedReportList(String versionCode, TestReportTypeEnum reportType) {
        Example example = new Example(TmEmailEntity.class);
        example.createCriteria()
                .andEqualTo("versionCode", versionCode)
                .andNotEqualTo("emailType", reportType)
                .andEqualTo("emailSource", EmailSourceEnum.TEST_REPORT.name())
                .andEqualTo("enable", Boolean.TRUE);
        example.orderBy("sendDate").desc();
        List<TmEmailEntity> entityList = tmEmailMapper.selectByExample(example);
        return CollectionUtils.isEmpty(entityList) ? new ArrayList<>() : emailVOConverter.convert2DOList(entityList);
    }

    @Override
    public void addEmail(ReportAddedEvent event) {
        //组装邮件数据
        TmEmailEntity entity = buildEntity(event);

        //是否第一次发邮件
        if (isFirstSend(entity.getRelatePlanCode(), entity.getEmailType()) && isNeedChange(entity.getEmailType())) {
            //更新测试计划，阶段状态
            iTmTestReportRepository.updatePlanStageStatus(entity.getRelatePlanCode(), TestPlanStageEnum.getEnumByEmailType(entity.getEmailType()));
        }

        //保存邮件数据
        tmEmailMapper.insertSelective(entity);

        //保存邮件收件人，抄送人
        qcNoticeResultRepository.addDB(event, entity.getEmailCode(), event.getCcUsers(), event.getReceiveUsers());
    }

    @Override
    public void transferEmail(Date begin, Date end) {

        log.info("transferEmail_begin>>>");
        transferPlan(begin, end);
        transferReport(begin, end);
        log.info("transferEmail_end<<<");

    }

    @Override
    public List<TmEmailEntityDO> selectByBusinessCodeAndEmailType(String businessCode, EmailTypeEnum emailType) {
        Example example = new Example(TmEmailEntity.class);
        example.createCriteria().andEqualTo("businessCode", businessCode)
                .andEqualTo("emailType", emailType)
                .andEqualTo("enable", Boolean.TRUE);
        List<TmEmailEntity> entityList = tmEmailMapper.selectByExample(example);
        return CollectionUtils.isEmpty(entityList) ? new ArrayList<>() : emailVOConverter.convert2DOList(entityList);
    }

    @Override
    public void insertSelective(TmEmailEntityDO entityDO) {
        TmEmailEntity entity = emailVOConverter.convert2Entity(entityDO);
        tmEmailMapper.insertSelective(entity);
    }

    /**
     * 同步计划邮件
     *
     * @param begin 开始时间
     * @param end   结束时间
     */
    public void transferPlan(Date begin, Date end) {
        List<TestPlanDetailVO> voList = new ArrayList<>();
        Integer batchSize = transferEmailProfile.getBatchSize();

        //普通测试计划
        List<TestPlanDetailVO> planList = testPlanMapper.selectCommonListByReq(begin, end);
        if (CollectionUtils.isNotEmpty(planList)) {
            voList.addAll(planList);
        }
        //其他测试计划
        List<TestPlanDetailVO> otherPlanList = testPlanMapper.selectOtherListByReq(begin, end);
        if (CollectionUtils.isNotEmpty(otherPlanList)) {
            voList.addAll(otherPlanList);
        }
        if (voList.size() < 1) {
            log.info("LbdQcService_voList_is_null-> {},{}", begin, end);
        }

        //保存邮件数据
        List<TmEmailEntity> entityList = buildListByPlan(voList);
        if (CollectionUtils.isEmpty(entityList)) {
            log.info("transferPlan_entityList_is_null-> {},{}", begin, end);
            return;
        }
        log.info("transferPlan_entityList_size ->{}", entityList.size());
        if (entityList.size() > batchSize) {
            List<List<TmEmailEntity>> part = Lists.partition(entityList, batchSize);
            part.forEach(item -> doBatchSaveEmail(item, begin, end));
        } else {
            doBatchSaveEmail(entityList, begin, end);
        }
    }

    /**
     * 同步报告邮件
     *
     * @param begin 开始时间
     * @param end   结束时间
     */
    public void transferReport(Date begin, Date end) {
        Integer batchSize = transferEmailProfile.getBatchSize();

        //普通测试计划
        List<TestReportDetailVO> voList = testReportMapper.selectTransferList(begin, end);
        if (CollectionUtils.isEmpty(voList)) {
            log.info("transferReport_reportList_is_null-> {},{}", begin, end);
            return;
        }

        //保存邮件数据
        List<TmEmailEntity> entityList = buildListByReport(voList);
        if (CollectionUtils.isEmpty(entityList)) {
            log.info("transferReport_entityList_is_null-> {},{}", begin, end);
            return;
        }

        log.info("transferReport_entityList_size ->{}", entityList.size());
        if (entityList.size() > batchSize) {
            List<List<TmEmailEntity>> part = Lists.partition(entityList, batchSize);
            part.forEach(item -> doBatchSaveEmail(item, begin, end));
        } else {
            doBatchSaveEmail(entityList, begin, end);
        }
    }

    /**
     * 批量组装邮件list
     *
     * @param voList {@link TestReportDetailVO}
     * @return {@link TmEmailEntity}
     */
    private List<TmEmailEntity> buildListByReport(List<TestReportDetailVO> voList) {
        List<TmEmailEntity> entityList = new ArrayList<>(voList.size());
        voList.forEach(vo -> {
            TmEmailEntity entity = buildEntityByReport(vo);
            if (null != entity) {
                entityList.add(entity);
            }
        });
        return entityList;
    }

    /**
     * 组装报告邮件
     *
     * @param vo {@link TestReportDetailVO}
     * @return {@link TmEmailEntity}
     */
    private TmEmailEntity buildEntityByReport(TestReportDetailVO vo) {
        TmEmailEntity entity = new TmEmailEntity();
        entity.setDataSource(TmEmailDataSourceEnum.TRANSFER);
        entity.setEmailCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
        entity.setEmailName(vo.getReportName());
        entity.setEmailType(EmailTypeEnum.buildSelf(vo.getReportType()));
        entity.setEmailSource(EmailSourceEnum.TEST_REPORT);
        entity.setBusinessCode(vo.getReportCode());
        entity.setBusinessName(vo.getReportName());
        entity.setRelatePlanCode(vo.getRelatePlanCode());
        entity.setRelatePlanName(vo.getRelatePlanName());
        entity.setProductCode(vo.getProductCode());
        entity.setProductName(vo.getProductName());
        entity.setVersionCode(vo.getVersionCode());
        entity.setVersionName(vo.getVersionName());
        entity.setPreview(vo.getPreview());
        entity.setSendDate(vo.getGmtModified());
        entity.setSenderId(vo.getModifierId());
        entity.setSender(vo.getModifier());
        entity.setEnable(Boolean.TRUE);
        entity.setGmtCreate(vo.getGmtCreate());
        entity.setGmtModified(vo.getGmtModified());
        entity.setCreatorId(vo.getCreatorId());
        entity.setCreator(vo.getCreator());
        entity.setModifierId(vo.getModifierId());
        entity.setModifier(vo.getModifier());
        entity.setSyncCreate(new Date());
        return entity;
    }

    /**
     * 批量保存邮件数据
     *
     * @param part {@link TmEmailEntity}
     */
    private void doBatchSaveEmail(List<TmEmailEntity> part, Date begin, Date end) {
        if (CollectionUtils.isEmpty(part)) {
            return;
        }
        List<String> businessCodeList = part.stream().map(TmEmailEntity::getBusinessCode).collect(Collectors.toList());
        doUpdateBatchEmail(businessCodeList, begin, end);
        tmEmailMapper.insertBatch(part);
    }

    /**
     * 批量更新邮件状态
     *
     * @param businessCodeList 计划/报告code
     * @param begin            开始时间
     * @param end              结束时间
     */
    private void doUpdateBatchEmail(List<String> businessCodeList, Date begin, Date end) {
        if (CollectionUtils.isEmpty(businessCodeList)) {
            return;
        }
        Example example = new Example(TmEmailEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("dataSource", TmEmailDataSourceEnum.TRANSFER)
                .andIn("businessCode", businessCodeList);
        if (null != begin && null != end) {
            criteria.andBetween("gmtCreate", begin, end);
        }
        TmEmailEntity toUpdate = new TmEmailEntity();
        toUpdate.setEnable(Boolean.FALSE);
        toUpdate.setSyncModified(new Date());
        tmEmailMapper.updateByExampleSelective(toUpdate, example);
    }

    /**
     * 组装邮件list
     *
     * @param voList {@link TestPlanDetailVO}
     * @return
     */
    private List<TmEmailEntity> buildListByPlan(List<TestPlanDetailVO> voList) {
        List<TmEmailEntity> entityList = new ArrayList<>(voList.size());
        voList.forEach(vo -> {
            TmEmailEntity entity = buildEntityByPlan(vo);
            if (null != entity) {
                entityList.add(entity);
            }
        });
        return entityList;
    }

    /**
     * 组装邮件基础信息
     *
     * @param vo 入参
     * @return {@link TmEmailEntity}
     */
    private TmEmailEntity buildEntityByPlan(TestPlanDetailVO vo) {
        TmEmailEntity entity = new TmEmailEntity();
        entity.setDataSource(TmEmailDataSourceEnum.TRANSFER);
        entity.setEmailCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
        entity.setEmailName(vo.getPlanName());
        entity.setEmailType(EmailTypeEnum.buildSelf(vo.getPlanType()));
        entity.setEmailSource(EmailSourceEnum.TEST_PLAN);
        entity.setBusinessCode(vo.getPlanCode());
        entity.setBusinessName(vo.getPlanName());
        entity.setRelatePlanCode(vo.getPlanCode());
        entity.setRelatePlanName(vo.getPlanName());
        entity.setProductCode(vo.getProductCode());
        entity.setProductName(vo.getProductName());
        entity.setVersionCode(vo.getVersionCode());
        entity.setVersionName(vo.getVersionName());
        entity.setPlanPresentationDate(vo.getPlanPresentationDate());
        entity.setPlanApprovalExitDate(vo.getPlanApprovalExitDate());
        entity.setPreview(vo.getPreview());
        entity.setSendDate(vo.getGmtModified());
        entity.setSenderId(vo.getModifierId());
        entity.setSender(vo.getModifier());
        entity.setEnable(Boolean.TRUE);
        entity.setGmtCreate(vo.getGmtCreate());
        entity.setGmtModified(vo.getGmtModified());
        entity.setCreatorId(vo.getCreatorId());
        entity.setCreator(vo.getCreator());
        entity.setModifierId(vo.getModifierId());
        entity.setModifier(vo.getModifier());
        entity.setSyncCreate(new Date());
        return entity;
    }

    /**
     * 判断是否需要修改测试计划状态
     *
     * @param emailType 邮件类型
     * @return true: 是 ；false ：不是
     */
    private boolean isNeedChange(EmailTypeEnum emailType) {
        return emailType.equals(EmailTypeEnum.TEST_ACCESS)
                || emailType.equals(EmailTypeEnum.TEST_PERMIT)
                || emailType.equals(EmailTypeEnum.ONLINE_SMOKE)
                || emailType.equals(EmailTypeEnum.SIMPLE_PROCESS)
                || emailType.equals(EmailTypeEnum.SPECIAL_MOBILE)
                || emailType.equals(EmailTypeEnum.CHECED_TEST);
    }

    /**
     * 判断当前报告是否第一次发送邮件
     *
     * @param relatePlanCode 关联计划code
     * @param emailType      邮件类型
     * @return true: 是 ；false ：不是
     */
    private boolean isFirstSend(String relatePlanCode, EmailTypeEnum emailType) {
        Example example = new Example(TmEmailEntity.class);
        example.selectProperties("emailCode", "enable");
        example.createCriteria().andEqualTo("relatePlanCode", relatePlanCode)
                .andEqualTo("emailType", emailType.name())
                .andEqualTo("enable", Boolean.TRUE);
        List<TmEmailEntity> entityList = tmEmailMapper.selectByExample(example);
        return cn.hutool.core.collection.CollectionUtil.isEmpty(entityList);
    }


    /**
     * 组装邮件基础信息
     *
     * @param event 入参
     * @return {@link TmEmailEntity}
     */
    private TmEmailEntity buildEntity(ReportAddedEvent event) {
        TmEmailEntity entity = new TmEmailEntity();
        entity.setEmailCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
        entity.setEmailName(event.getReportName());
        entity.setEmailType(EmailTypeEnum.buildSelf(event.getReportType()));
        entity.setEmailSource(EmailSourceEnum.TEST_REPORT);
        entity.setBusinessCode(event.getReportCode());
        entity.setBusinessName(event.getReportName());
        entity.setRelatePlanCode(event.getPlanCode());
        entity.setRelatePlanName(event.getPlanName());
        entity.setProductCode(event.getProductCode());
        entity.setProductName(event.getProductName());
        entity.setVersionCode(event.getVersionCode());
        entity.setVersionName(event.getVersionName());
        entity.setPlanPresentationDate(event.getPlanPresentationDate());
        entity.setPlanApprovalExitDate(event.getPlanApprovalExitDate());
        entity.setPreview(event.getPreview());
        entity.setSendDate(event.getOccurred());
        if (event.getTransactor() != null) {
            entity.setSenderId(event.getTransactor().getUserId());
            entity.setSender(event.getTransactor().getUserName());
        }
        entity.preCreate(event);
        return entity;
    }
}
