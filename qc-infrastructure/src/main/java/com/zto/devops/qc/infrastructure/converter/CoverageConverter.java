package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.pipeline.client.model.plan.event.ReleasedQcEvent;
import com.zto.devops.qc.client.model.dto.CoverageRecordBasicEntityDO;
import com.zto.devops.qc.client.model.dto.CoverageRecordEntityDO;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.testmanager.coverage.command.BatchCoverageCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.command.CoverageSwitchBaseCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.command.GenerateCoverageCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.*;
import com.zto.devops.qc.client.model.testmanager.coverage.event.BatchCoverageEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.event.CoverageSwitchBaseEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.event.GenerateCoverageEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoveragePublishQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmTestReportVO;
import com.zto.devops.qc.client.service.coverage.model.resp.CoverageRecordResp;
import com.zto.devops.qc.infrastructure.dao.entity.*;
import org.jacoco.core.internal.diff.ClassInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/10/19 10:50
 */
@Mapper(componentModel = "spring")
public interface CoverageConverter {

    CoverageConverter INSTANCE = Mappers.getMapper(CoverageConverter.class);

    @Mapping(target = "creatorId", source = "transactor.userId")
    @Mapping(target = "creator", source = "transactor.userName")
    CoveragePublishQuery converter(ReleasedQcEvent event);

    CoverageSwitchBaseEvent converter(CoverageSwitchBaseCommand command);

    GenerateCoverageEvent conveter(GenerateCoverageCommand command);

    BatchCoverageEvent conveter(BatchCoverageCommand command);

    CoverageRecordGenerateParameter converter(GenerateCoverageEvent event);

    @Mapping(expression = "java(java.util.Arrays.asList(command.getAppId()))", target = "appIdList")
    CoverageRecordGenerateParameter converter(GenerateCoverageCommand command);

    CoverageRecordGenerateParameter converter(BatchCoverageCommand command);

    @Mapping(target = "branchGmtCreate", source = "branchGmtCreate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "masterGmtCreate", source = "masterGmtCreate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    CoverageStatusVO converter(CoverageRecordEntity coverageRecordEntity);

    List<CoveragePublishVO> converterPublishVO(List<CoveragePublishEntity> entity);

    List<CoverageRecordBasicVO> converterBasicVO(List<CoverageRecordBasicEntity> entity);

    CoverageRecordBasicEntity converter(CoverageRecordBasicVO vo);

    CoverageRecordBasicVO converterBasicVO(CoverageRecordBasicEntity entity);

    List<CoverageRecordVO> converter(List<CoverageRecordEntity> entityList);

    CoveragePublishEntity converter(CoveragePublishVO vo);

    CoveragePublishVO converter(CoveragePublishEntity entity);

    CoverageRecordGenerateEntity converterGenerateEntity(CoverageRecordGenerateVO vo);

    CoverageRecordGenerateVO converterVO(CoverageRecordGenerateEntity entity);

    List<CoverageExecVO> converterExecVO(List<CoverageExecEntity> entity);

    CoverageExecEntity converterExecEntity(CoverageExecVO execVO);

    List<TmTestPlanVO> converterPlanVO(List<TmTestPlanEntity> entity);

    TmTestReportVO converterReportVO(TmTestReportEntity entity);

    List<CoverageRecordBasicEntity> converterBasicEntity(List<CoverageRecordBasicVO> voList);

    CoverageBranchBasicEntity converterBasicEntity(CoverageBranchBasicVO basicVO);

    List<CoverageBranchBasicEntity> converterBasicEntityList(List<CoverageBranchBasicVO> voList);

    List<CoverageRecordBasicEntityDO> converterBasicDO(List<CoverageRecordBasicEntity> entityList);

    CoverageRecordBasicEntity converter(CoverageRecordBasicEntityDO entityDO);

    List<CoverageAppInfoVO> converterEntity2VO(List<CoverageRecordBasicEntity> entityList);

    @Mapping(target = "reason", source = "remark")
    CoverageAppInfoVO converterEntity2VO(CoverageRecordBasicEntity entity);

    CoverageRecordEntityDO converterEntityDO(CoverageRecordBasicEntity coverageRecordEntity);

    CoverageRecordResp converterEntity2Resp(CoverageRecordBasicEntity coverageRecordEntity);

    com.zto.devops.qc.domain.model.coverage.ClassInfo converterClassInfo(ClassInfo classInfo);

    List<CoverageBranchBasicVO> converterBasicEntity2VO(List<CoverageBranchBasicEntity> entityList);
}
