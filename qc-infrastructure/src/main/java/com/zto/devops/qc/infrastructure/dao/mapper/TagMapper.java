package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.infrastructure.dao.entity.TagEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TagMapper extends Mapper<TagEntity> {

    Integer saveBatch(List<TagEntity> list);

    Long tagTestcaseNo(@Param("tagName") String tagName, @Param("domain") String domain, @Param("productCode") String productCode);

    List<String> tagTestcaseCodeList(@Param("tagName") String tagName, @Param("domain") String domain, @Param("productCode") String productCode);

    void deleteByCodes(@Param("codes") List<String> codes);

    List<TagEntity> queryBusinessTagWithFixVersionCode(String versionCode);

    void insertSceneTag(TagEntity entity);
}