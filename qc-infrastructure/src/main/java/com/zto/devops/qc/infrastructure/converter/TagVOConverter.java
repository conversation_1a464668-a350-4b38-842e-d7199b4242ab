package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TagEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.domain.model.Tag;
import com.zto.devops.qc.infrastructure.dao.entity.TagEntity;
import org.mapstruct.Mapper;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring")
public interface TagVOConverter {

    TagVO convert(TagEntity entity);

    List<TagVO> convert(List<TagEntity> entities);

    List<TagEntity> convert(Collection<TagVO> vos);
    List<Tag> convertList(Collection<TagEntity> entities);

    List<TagEntity> convertTagEntity(List<TagEntityDO> list);

    TagEntity convert(TagEntityDO entityDO);
}
