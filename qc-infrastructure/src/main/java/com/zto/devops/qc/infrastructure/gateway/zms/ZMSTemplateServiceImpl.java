package com.zto.devops.qc.infrastructure.gateway.zms;

import com.zto.devops.qc.client.model.websocket.WebSocketMessage;
import com.zto.devops.qc.domain.gateway.zms.ZMSTemplateService;
import com.zto.titans.mq.configuration.ZMSTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ZMSTemplateServiceImpl implements ZMSTemplateService {

    @Autowired
    private ZMSTemplate zmsTemplate;

    @Override
    public void send(String topic, WebSocketMessage message) {
        zmsTemplate.send(topic, message);
    }
}
