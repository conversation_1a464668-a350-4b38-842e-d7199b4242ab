package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

@MappedJdbcTypes(value = JdbcType.VARCHAR, includeNullJdbcType = true)
public class TestcasePriorityTypeHandler extends BaseEnumTypeHandler<TestcasePriorityEnum> {

    public TestcasePriorityTypeHandler(Class<TestcasePriorityEnum> type) {
        super(type);
    }
}
