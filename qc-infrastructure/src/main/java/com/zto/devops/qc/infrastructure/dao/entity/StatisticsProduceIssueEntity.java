package com.zto.devops.qc.infrastructure.dao.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import lombok.ToString;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @Date: 2022/1/13 15:01
 */
@ToString
@Data
@Table(name = "qc_statistics_produce_issue")
public class StatisticsProduceIssueEntity implements Serializable {

    /**
     * 版本编码
     */
    @Id
    @Column(name = "id")
    private Long id;
    /**
     * 所属产品code
     */
    @Column(name = "product_code")
    private String productCode;

    /**
     * 查询时间
     */
    @Column(name = "select_date")
    private Date selectDate;

    /**
     * 生产缺陷数量
     */
    @Column(name = "produce_issue_num")
    private Long produceIssueNum;


    @Column(name = "gmt_modified")
    private Date gmtModified;

}
