package com.zto.devops.qc.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@ConfigurationProperties("metadata")
@Component
public class MetaDataConfig {

    private String docToken;

    private String docHttpUrl;

    private String openApiUrl;

    private String openApiToken;

    private String docDetailUrl;

    private String gatewayDetailUrl;

    private String gatewayApiUrl;

    private String docGlobalEnvUrl;

    private String docDubboUrl;

    private String apiSettingUrl;
}
