package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TmTestPlanCaseEntityDO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanCaseVO;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanCaseEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TmTestPlanCaseConvert {

    TmTestPlanCaseEntityDO covert(TmTestPlanCaseEntity entity);

    List<TmTestPlanCaseEntityDO> covertList( List<TmTestPlanCaseEntity> entityList);


    TmTestPlanCaseEntityDO covertReversal(TmTestPlanCaseEntityDO entityDO);

    List<TmTestPlanCaseEntity> covertListReversal(List<TmTestPlanCaseEntityDO> entityDOList);

    TmTestPlanCaseEntity covert2Entity(TmTestPlanCaseEntityDO entityDO);

    List<TmTestPlanCaseEntityDO> convert2DOList(List<TmTestPlanCaseEntity> entityList);

    @Mapping(target = "type", source = "caseType")
    @Mapping(target = "enable", expression = "java(entity.getEnable() ? 1 : 0)")
    TestPlanCaseVO convert2VO(TmTestPlanCaseEntity entity);

    List<TestPlanCaseVO> convert2VOList(List<TmTestPlanCaseEntity> entityList);
}
