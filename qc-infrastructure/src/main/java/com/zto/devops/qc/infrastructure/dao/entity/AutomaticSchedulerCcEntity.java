package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Table(name = "tm_automatic_scheduler_cc")
public class AutomaticSchedulerCcEntity extends BaseEntity {

    @Id
    private Long id;

    private String schedulerCode;

    private Long ccId;

    private String ccName;
}
