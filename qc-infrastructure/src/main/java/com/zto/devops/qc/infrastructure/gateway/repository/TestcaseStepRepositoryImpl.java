package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseStepEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseStepQuery;
import com.zto.devops.qc.domain.gateway.repository.TestcaseStepRepository;
import com.zto.devops.qc.infrastructure.converter.TestcaseStepEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseStepEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TestcaseStepMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Component
@Slf4j
public class TestcaseStepRepositoryImpl implements TestcaseStepRepository {

    @Autowired
    private TestcaseStepMapper testcaseStepMapper;

    @Autowired
    private TestcaseStepEntityConverter testcaseStepEntityConverter;

    @Override
    public List<TestcaseStepVO> listTestcaseStepQuery(ListTestcaseStepQuery query) {
        Example example = new Example(TestcaseStepEntity.class);
        example.createCriteria().andEqualTo("testcaseCode", query.getTestcaseCode());
        example.orderBy("sort");
        List<TestcaseStepEntity> stepList = testcaseStepMapper.selectByExample(example);
        return testcaseStepEntityConverter.converter(stepList);
    }

    @Override
    public List<TestcaseStepVO> selectStepByCaseCodes(List<String> caseCodes) {
        Example example = new Example(TestcaseStepEntity.class);
        example.createCriteria().andIn("testcaseCode", caseCodes);
        List<TestcaseStepEntity> stepList = testcaseStepMapper.selectByExample(example);
        return testcaseStepEntityConverter.converter(stepList);
    }

    @Override
    public List<TestcaseStepVO> getTestcaseStepVOList(TestcaseEntityDO vo) {
        Example stepExample = new Example(TestcaseStepEntity.class);
        stepExample.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("testcaseCode", vo.getCode());
        List<TestcaseStepEntity> stepList = testcaseStepMapper.selectByExample(stepExample);
        return testcaseStepEntityConverter.converter(stepList);
    }

    @Override
    public List<TestcaseStepEntityDO> selectTestcaseStepByCaseCodeList(List<String> caseCodes) {
        Example testcaseStepExample = new Example(TestcaseStepEntity.class);
        testcaseStepExample.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andIn("testcaseCode", caseCodes);
        testcaseStepExample.orderBy("sort");
        return testcaseStepEntityConverter.convertList(testcaseStepMapper.selectByExample(testcaseStepExample));
    }

    @Override
    public void saveBatch(List<TestcaseStepEntityDO> entityDOList) {
        testcaseStepMapper.saveBatch(testcaseStepEntityConverter.convert2List(entityDOList));
    }

    @Override
    public void addTestcaseStepList(List<TestcaseStepVO> testSteps, String testcaseCode, BaseEvent event) {
        Example exampleTestcaseStep = new Example(TestcaseStepEntity.class);
        exampleTestcaseStep.createCriteria().andEqualTo("testcaseCode", testcaseCode);
        testcaseStepMapper.deleteByExample(exampleTestcaseStep);
        if (CollectionUtil.isEmpty(testSteps)) {
            return;
        }
        List<TestcaseStepEntity> entityList = testcaseStepEntityConverter.convertVOList(testSteps);
        for (TestcaseStepEntity en : entityList) {
            en.preCreate(event);
            en.setTestcaseCode(testcaseCode);
            testcaseStepMapper.insertSelective(en);
        }
    }

    @Override
    public List<TestcaseStepVO> handle(ListTestcaseStepQuery query) {
        log.info("ListTestcaseStepQuery >>> {}", query.getTestcaseCode());
        Example example = new Example(TestcaseStepEntity.class);
        example.createCriteria().andEqualTo("testcaseCode", query.getTestcaseCode());
        example.orderBy("sort");
        List<TestcaseStepEntity> stepList = testcaseStepMapper.selectByExample(example);
        return testcaseStepEntityConverter.converter(stepList);
    }
}
