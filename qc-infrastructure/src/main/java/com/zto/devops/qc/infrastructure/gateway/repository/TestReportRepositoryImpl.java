package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.qc.client.enums.report.ReportType;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.model.dto.TestReportEntityDO;
import com.zto.devops.qc.client.model.report.entity.ReportVO;
import com.zto.devops.qc.client.model.report.query.PageReportQuery;
import com.zto.devops.qc.client.model.testPlan.query.TestPlanDto;
import com.zto.devops.qc.domain.gateway.repository.TestReportRepository;
import com.zto.devops.qc.infrastructure.converter.TestReportEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.TestReportEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TestPlanMainMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.TestReportMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Component
@Slf4j
public class TestReportRepositoryImpl implements TestReportRepository {

    @Autowired
    private TestReportMapper testReportMapper;

    @Autowired
    private TestPlanMainMapper testPlanMainMapper;

    @Autowired
    private TestReportEntityConverter testReportEntityConverter;


    @Override
    public List<TestReportEntityDO> selectTestReportByVersionAnReportTypeList(String versionCode, List<ReportType> reportTypeList) {
        Example example = new Example(TestReportEntity.class);
        example.createCriteria()
                .andEqualTo("versionCode", versionCode)
                .andEqualTo("enable", true)
                .andEqualTo("status", TestPlanStatusEnum.NORMAL)
                .andIn("reportType", reportTypeList);
        return  testReportEntityConverter.convertList(testReportMapper.selectByExample(example));
    }

    @Override
    public List<ReportVO> pageQuery(PageReportQuery pageReportQuery) {
        return testReportMapper.pageQuery(pageReportQuery);
    }

    @Override
    public TestPlanDto selectTestPlanMainByCode(String code) {
        return testPlanMainMapper.selectTestPlanMainByCode(code);
    }

}
