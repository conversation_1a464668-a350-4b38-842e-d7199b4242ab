package com.zto.devops.qc.infrastructure.impexp;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.qc.client.model.report.entity.PlanMobileResp;
import com.zto.devops.qc.client.model.report.entity.ReportAccessResp;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目名称：qc-parent
 * 类 名 称：ReportAccessHeadDataListener
 * 类 描 述：TODO
 * 创建时间：2022/5/24 9:09 下午
 * 创 建 人：bulecat
 */
@Slf4j
public class PlanMobileHeadDataListener extends AnalysisEventListener<PlanMobileResp> {

    List<ReportAccessResp> list = new ArrayList<ReportAccessResp>();


    @Override
    public void invoke(PlanMobileResp data, AnalysisContext analysisContext) {
        //log.info(JSONUtil.toJsonStr(reportAccessResp));

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        if (headMap == null) {
            throw new ServiceException("用例导入格式不正确");
        }
        String s0 = headMap.get(0);
        String s1 = headMap.get(1);
        String s2 = headMap.get(2);
        if (!"测试类型".equals(s0) || !"功能测试点".equals(s1)|| !"测试负责人/参与人".equals(s2)) {
            throw new ServiceException("导入格式不正确");
        }

    }
}
