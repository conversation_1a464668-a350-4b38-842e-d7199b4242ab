package com.zto.devops.qc.infrastructure.message;

import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.domain.message.TemplateAuditor;
import com.zto.devops.qc.client.model.message.AutomaticSchedulerMsg;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTaskGroupVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTaskVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.CountCaseStatusVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticSchedulerMsgEvent;
import com.zto.devops.qc.domain.gateway.repository.IAutomaticTaskRepository;
import com.zto.devops.qc.infrastructure.config.AutomaticSchedulerMsgConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class AutomaticSchedulerAuditor extends TemplateAuditor<AutomaticSchedulerMsgEvent, AutomaticSchedulerMsg> {

    @Autowired
    private AutomaticSchedulerMsgConfig automaticSchedulerMsgConfig;

    @Autowired
    private IAutomaticTaskRepository automaticTaskRepository;

    public AutomaticSchedulerAuditor() {
        super(MessageConstant.AUTO_SCHEDULER_MSG, AutomaticSchedulerMsgEvent.class);
    }

    @Override
    protected AutomaticSchedulerMsg doHandle(AutomaticSchedulerMsgEvent event) {
        log.info("进入发送消息事件:AutomaticSchedulerMsgEvent" + "------>消息模板id=" + MessageConstant.AUTO_SCHEDULER_MSG);
        if (Objects.isNull(event)) {
            return null;
        }
        AutomaticSchedulerMsg msg = new AutomaticSchedulerMsg();
        msg.setTitle(automaticSchedulerMsgConfig.getContent());
        msg.setValue1(event.getSchedulerName());
        msg.setValue2(event.getExecuteResult().getDesc());
        List<AutomaticTaskVO> taskList = automaticTaskRepository.selectTaskList(Arrays.asList(event.getTaskId()));
        if (CollectionUtil.isNotEmpty(taskList)){
            List<AutomaticTaskGroupVO> groupList = AutomaticTaskGroupVO.buildListByGroup(taskList);
            msg.setValue3(buildValue3(groupList));
        }
        msg.setProductNameValue(StringUtils.isBlank(event.getProductName()) ?
                "" : event.getProductName());
        msg.setCreatorValue(event.getCreator());
        msg.setStartTimeValue(event.getStartTime());
        msg.setEndTimeValue(event.getFinishTime());
        msg.setActionUrl(String.format(automaticSchedulerMsgConfig.getBaseUrl() +
                automaticSchedulerMsgConfig.getDetailUrl(), event.getProductCode(), event.getTaskId()));
        msg.setReceivedUsers(event.getReceivedUsers());
        log.info("发送消息事件结束,请求参数：" + JSON.toJSONString(msg));
        return msg;
    }

    private String buildValue3(List<AutomaticTaskGroupVO> groupList){
        if (CollectionUtil.isEmpty(groupList)){
            return "";
        }
        AutomaticTaskGroupVO vo = groupList.get(0);
        if (CollectionUtil.isEmpty(vo.getCaseStatusVOS())){
            return "";
        }
        List<CountCaseStatusVO> statusVOS = vo.getCaseStatusVOS();
        StringBuilder sb = new StringBuilder();
        sb.append("用例总数:" + vo.getTotalCount() + "|");
        statusVOS.forEach(t->{
            sb.append(t.getStatusDesc() + ":" + t.getNum());
            sb.append("|");
        });
        return sb.substring(0,sb.length()-1);
    }

}
