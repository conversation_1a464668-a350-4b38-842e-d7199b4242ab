package com.zto.devops.qc.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * KDCS SSO配置类
 * 从Apollo读取快超SSO相关配置信息
 *
 * <AUTHOR>
 * @create 2024/12/20
 */
@Data
@Component
@ConfigurationProperties(prefix = "tm.scene.login.kdcs")
public class KdcsSsoConfig {

    /**
     * IAM服务地址
     */
    private String iamUrl = "https://iam.test.ztosys.com";

    /**
     * 应用ID
     */
    private String appId = "";

    /**
     * 应用密钥
     */
    private String appSecret = "";

    /**
     * 快超网关地址
     */
    private String gatewayUrl = "https://ztwj.gw-test.ztosys.com";

    /**
     * 设备ID（用于登录请求）
     */
    private String deviceId = "66B04D66-9ED1-4C6A-A311-76DA32B95FDF";

    /**
     * 设备名称（用于登录请求）
     */
    private String deviceName = "iphone 11-1";

    /**
     * 平台名称（用于登录请求）
     */
    private String platformName = "app";

    /**
     * User-Agent（用于HTTP请求头）
     */
    private String userAgent = "wanjiaExpress/4.41.2 (iPhone; iOS 17.5.1; Scale/2.00)";

    /**
     * 一账通登录接口名称
     */
    private String accountLoginApiName = "tuxi.spm.account.accountLoginByPwd";

    /**
     * SN登录接口名称
     */
    private String snLoginApiName = "snLogin";

    /**
     * 网关接口路径
     */
    private String gatewayPath = "/gateway.do";

    /**
     * API版本
     */
    private String apiVersion = "1";
} 