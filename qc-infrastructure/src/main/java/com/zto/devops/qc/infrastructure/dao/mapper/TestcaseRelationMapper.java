package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.model.issue.entity.CaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseByBusinessCodeVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseIssueVO;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseRelationEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TestcaseRelationMapper extends Mapper<TestcaseRelationEntity> {

    /**
     * 根据测试用例code查询关联缺陷
     *
     * @param code   测试用例code
     * @param domain 领域
     * @return {@link TestcaseIssueVO}
     */
    List<TestcaseIssueVO> selectIssueByTestcaseCode(@Param("code") String code, @Param("domain") DomainEnum domain);


    List<TestcaseByBusinessCodeVO> selectTestCaseByCodeList(@Param("codeList") List<String> codeList, @Param("domain") DomainEnum domain);


    List<CaseVO> getCaseNameList(@Param("code") String code);
}
