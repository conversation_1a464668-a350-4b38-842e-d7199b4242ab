package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

@MappedJdbcTypes(value = JdbcType.VARCHAR, includeNullJdbcType = true)
public class TestcaseTypeTypeHandler extends BaseEnumTypeHandler<TestcaseTypeEnum> {

    public TestcaseTypeTypeHandler(Class<TestcaseTypeEnum> type) {
        super(type);
    }
}
