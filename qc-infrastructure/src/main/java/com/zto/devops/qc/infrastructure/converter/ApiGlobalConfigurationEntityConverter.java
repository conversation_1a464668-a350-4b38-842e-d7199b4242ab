package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.ApiGlobalConfigurationEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.ApiGlobalConfigurationEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ApiGlobalConfigurationEntityConverter {

    List<ApiGlobalConfigurationEntity> convertApiGlobalConfigurationEntity(List<ApiGlobalConfigurationEntityDO> list);

    ApiGlobalConfigurationEntity convertApiGlobalConfigurationEntity(ApiGlobalConfigurationEntityDO entityDO);


    List<ApiGlobalConfigurationEntityDO> convertApiGlobalConfigurationEntityDO(List<ApiGlobalConfigurationEntity> list);


    ApiGlobalConfigurationEntityDO convertApiGlobalConfigurationEntityDO(ApiGlobalConfigurationEntity entityDO);
}
