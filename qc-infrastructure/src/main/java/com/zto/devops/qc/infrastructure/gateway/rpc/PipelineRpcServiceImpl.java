package com.zto.devops.qc.infrastructure.gateway.rpc;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.rpc.RpcException;
import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.pipeline.client.model.application.entity.PageApplicationVO;
import com.zto.devops.pipeline.client.model.flow.entity.JacocoVersionVO;
import com.zto.devops.pipeline.client.model.flow.entity.OssFileVO;
import com.zto.devops.pipeline.client.model.flow.entity.VersionSimpleVO;
import com.zto.devops.pipeline.client.model.flow.query.FindVersionAppIdQuery;
import com.zto.devops.pipeline.client.model.flow.query.FindVersionSimpleQuery;
import com.zto.devops.pipeline.client.model.flow.query.ListDeployApplicationQuery;
import com.zto.devops.pipeline.client.service.IFlowService;
import com.zto.devops.pipeline.client.service.application.IApplicationService;
import com.zto.devops.pipeline.client.service.inner.IPipelineQcService;
import com.zto.devops.pipeline.client.service.inner.model.FindVersionContainSubReq;
import com.zto.devops.pipeline.client.service.namespace.INamespaceService;
import com.zto.devops.qc.client.model.rpc.pipeline.*;
import com.zto.devops.qc.client.model.rpc.pipeline.query.*;
import com.zto.devops.qc.client.model.testmanager.cases.event.ExecuteCallbackCDEvent;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.infrastructure.converter.RpcVOConvertor;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class PipelineRpcServiceImpl implements IPipelineRpcService {

    @Reference(timeout = 10000)
    private IPipelineQcService pipelineQcService;

    @Reference
    private IFlowService iFlowService;

    @Reference
    private INamespaceService iNamespaceService;

    @Reference
    private IApplicationService iApplicationService;

    @Autowired
    private RpcVOConvertor rpcVOConvertor;

    @Autowired
    private RedisService redisService;

    @Override
    public String downloadUrl(FindOssFileUrlQuery query) {
        Result<OssFileVO> result = pipelineQcService.findOssFileUrlQuery(rpcVOConvertor.convertFindOssFileUrlQuery(query));
        if (Objects.isNull(result) || Objects.isNull(result.getData()) || StringUtil.isEmpty(result.getData().getDownloadUrl())) {
            return null;
        }
        return result.getData().getDownloadUrl();
    }

    @Override
    public com.zto.devops.qc.client.model.rpc.pipeline.JacocoVersionVO getJacocoVersion(ListFlowVersionJacocoQuery query) {
        Result<JacocoVersionVO> result = pipelineQcService.listFlowVersionJacocoQuery(rpcVOConvertor.convertListFlowVersionJacocoQuery(query));
        log.info("listFlowVersionJacocoQuery result : {}", result);
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            return null;
        }
        return rpcVOConvertor.convertJacocoVersionVO(result.getData());
    }

    @Override
    public com.zto.devops.qc.client.model.rpc.pipeline.PageApplicationVO getPageApplicationVO(PageApplicationQuery query) {
        Result<PageApplicationVO> result = pipelineQcService.pageApplicationQuery(rpcVOConvertor.convertPageApplicationQuery(query));
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            return null;
        }
        return rpcVOConvertor.convertPageApplicationVO(result.getData());
    }

    @Override
    public List<JacocoApplicationVO> listDeployApplicationQuery(String flowCode, String planCode) {
        ListDeployApplicationQuery query = new ListDeployApplicationQuery();
        query.setFlowCode(flowCode);
        query.setPlanCode(planCode);
        Result<List<com.zto.devops.pipeline.client.model.application.entity.JacocoApplicationVO>> result =
                pipelineQcService.listDeployApplicationQuery(query);
        log.info("listDeployApplicationQuery result : {}", result);
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            return null;
        }
        return rpcVOConvertor.convertJacocoApplicationVOList(result.getData());
    }

    @Override
    public VersionFlowDateVO findVersionFlowDateQuery(FindVersionFlowDateQuery query) {
        return rpcVOConvertor.coverterVersionFlowDateVO(iFlowService.findVersionFlowDateQuery(rpcVOConvertor.coverterFindVersionFlowDateQuery(query)));
    }

    @Override
    public NamespaceResp findBaseNamespace(String productCode) {
        Result<com.zto.devops.pipeline.client.service.inner.model.NamespaceResp> result = pipelineQcService.findBaseNamespace(productCode);
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            return null;
        }
        return rpcVOConvertor.covertNamespaceResp(result.getData());
    }

    @Override
    public List<ApplicationResp> getApplicationList(String productCode) {
        Result<List<com.zto.devops.pipeline.client.service.inner.model.ApplicationResp>> result =
                pipelineQcService.listApplication(productCode);
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            return null;
        }
        return rpcVOConvertor.convertApplicationResp(result.getData());
    }

    @Override
    public List<String> findVersionAppIdQuery(String versionCode) {
        return pipelineQcService.findVersionAppIdQuery(new FindVersionAppIdQuery(versionCode));
    }

    @Override
    public void handleExecuteCallbackCDEvent(ExecuteCallbackCDEvent event) {
        log.info("handleExecuteCallbackCDEvent >>> {}", JSON.toJSONString(event));
        pipelineQcService.handleExecuteCallbackCDEvent(rpcVOConvertor.convertExecuteCallbackCDEvent(event));
    }

    /**
     * value：抛出指定异常才会重试
     * include：和value一样，默认为空，当exclude也为空时，默认所有异常
     * exclude：指定不处理的异常
     * maxAttempts：最大重试次数，默认3次
     * backoff：重试等待策略，
     * 默认使用@Backoff，@Backoff的value默认为1000L，我们设置为2000； 以毫秒为单位的延迟（默认 1000）
     * multiplier（指定延迟倍数）默认为0，表示固定暂停1秒后进行重试，如果把multiplier设置为1.5，则第一次重试为2秒，第二次为3秒，第三次为4.5秒。
     */
    @Retryable(value = RpcException.class, maxAttempts = 3, backoff = @Backoff(delay = 1000, multiplier = 1.5))
    @Override
    public List<String> getPodIpListByInstanceNameList(List<String> instanceNameList) {
        List<String> resultList = pipelineQcService.listPodIpByInstanceNameList(instanceNameList);
        return CollectionUtil.isNotEmpty(resultList) ? resultList : new ArrayList<>();
    }

    @Override
    public FlowBranchVO queryBranchByVersionCode(String versionCode) {
        return rpcVOConvertor.convertFlowBranchVO(pipelineQcService.queryBranchByVersionCode(versionCode));
    }

    @Override
    public List<com.zto.devops.qc.client.model.rpc.pipeline.FeatureVO> listFeatureConfig(String productCode) {
        log.info("请求pipeline判断动态多环境，productCode：{}", productCode);
        try {
            List<com.zto.devops.pipeline.client.model.plan.entity.FeatureVO> list = pipelineQcService.listFeatureConfig(productCode);
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }
            log.info("返回结果：{}", JSON.toJSONString(list));
            return rpcVOConvertor.convertFeatureVOList(list);
        } catch (Exception e) {
            log.info("请求pipeline判断动态多环境，请求失败！");
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public FlowBaseDetailVO flowDetail(String flowCode) {
        log.info("flowDetail_flowCode:{}", flowCode);
        com.zto.devops.pipeline.client.model.flow.entity.FlowBaseDetailVO resp = pipelineQcService.getFlowDetailByFlowCode(flowCode);
        log.info("flowDetail_resp:{}", JsonUtil.toJSON(resp));
        return rpcVOConvertor.convertFlowDetail(resp);
    }

    @Override
    public String getVersionSimpleVO(String appId, String tag) {
        if (StringUtil.isEmpty(appId)) {
            throw new ServiceException("appId不能为空");
        }
        String versionKey = appId + "." + tag;
        if (redisService.hasKey(versionKey)) {
            return redisService.getKey(versionKey);
        }
        String versionCode;
        if (tag.equals("base") || tag.equals("test") || StringUtil.isEmpty(tag)) {
            versionCode = StringUtil.isEmpty(tag) ? "base" : tag ;
        } else {
            FindVersionSimpleQuery query = new FindVersionSimpleQuery();
            query.setAppId(appId);
            query.setTag(tag);
            Result<VersionSimpleVO> resp = pipelineQcService.getVersionSimpleVO(query);
            if (null == resp || !resp.getStatusCode().equals("200")) {
                throw new ServiceException("请求pipeline异常, appId = "+ appId + " tag = " + tag);
            }
            if (null == resp.getData()) {
                throw new ServiceException("查询版本数据为空, appId = "+ appId + " tag = " + tag);
            }
            log.info("FindVersionSimpleQuery resp : {}", JsonUtil.toJSON(resp));
            versionCode = resp.getData().getVersionCode();
        }
        if (StringUtil.isNotEmpty(versionCode)) {
            redisService.setKey(versionKey, versionCode, 1, TimeUnit.DAYS);
            return versionCode;
        }
        return null;
    }

    @Override
    public FindVersionByNamespaceVO queryVersionByNamespace(FindVersionByNamespaceQuery query) {
        com.zto.devops.pipeline.client.model.cluster.entity.FindVersionByNamespaceVO findVersionByNamespaceVO =
                iNamespaceService.queryVersionByNamespace(rpcVOConvertor.convert(query));
        return rpcVOConvertor.convert(findVersionByNamespaceVO);
    }

    @Override
    public List<ApplicationVO> listApplicationByAppIds(List<String> appIds) {
        return rpcVOConvertor.convertApplicationVOList(iApplicationService.listApplicationByAppIds(appIds));
    }

    @Override
    public VersionContainSubVO getVersionByVersionCodeAndAppId(String versionCode, String appId) {
        FindVersionContainSubReq req = new FindVersionContainSubReq(versionCode, appId);
        return rpcVOConvertor.convertVersionContainSubVO(pipelineQcService.getVersionByVersionCodeAndAppId(req));
    }

    @Override
    public String queryVersionReleaseBranchCommit(VersionReleaseBranchCommitQuery query) {
        return iFlowService.queryVersionReleaseBranchCommit(rpcVOConvertor.convert(query));
    }

}
