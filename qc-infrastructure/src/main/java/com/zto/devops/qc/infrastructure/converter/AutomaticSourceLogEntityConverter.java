package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.AutomaticSourceLogEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.AutomaticRecordLiteInfoVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordLogVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AddAutomaticRecordLogEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.EditAutomaticRecordLogEvent;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceLogEntity;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceRecordEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AutomaticSourceLogEntityConverter {

    AutomaticRecordLogVO convertVO(AutomaticSourceLogEntity entity);

    List<AutomaticRecordLogVO> convertVOList(List<AutomaticSourceLogEntity> entityList);

    AutomaticSourceLogEntity convert(AddAutomaticRecordLogEvent event);

    AutomaticSourceLogEntity convert(EditAutomaticRecordLogEvent event);

    AutomaticSourceLogEntity convert(AutomaticSourceLogEntityDO entityDO);

    AutomaticSourceRecordEntityDO convert(AutomaticSourceRecordEntity selectByPrimaryKey);

    List<AutomaticRecordVO> converterAutomaticSourceRecordList(List<AutomaticSourceRecordEntity> entityList);

    List<AutomaticRecordLiteInfoVO> converterLiteInfoPage(List<AutomaticSourceRecordEntity> list);

    AutomaticSourceLogEntityDO convert(AutomaticSourceLogEntity entity);
}
