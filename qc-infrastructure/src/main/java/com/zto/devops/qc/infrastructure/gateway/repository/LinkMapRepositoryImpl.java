package com.zto.devops.qc.infrastructure.gateway.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.LinkMapTypeEnum;
import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.client.model.dto.SceneLinkInfoEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.ChangeLinkStatusCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.PageLinkMapVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.ComponentBaseInfo;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.SingleLinkBaseInfo;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.lines.Line;
import com.zto.devops.qc.client.service.testmanager.apitest.model.PageLinkMapReq;
import com.zto.devops.qc.domain.gateway.repository.ApiTestRepository;
import com.zto.devops.qc.domain.gateway.repository.LinkMapRepository;
import com.zto.devops.qc.infrastructure.converter.SceneLinkInfoEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.SceneLinkInfoEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.SceneLinkInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class LinkMapRepositoryImpl implements LinkMapRepository {

    @Autowired
    private SceneLinkInfoMapper sceneLinkInfoMapper;
    @Autowired
    private ApiTestRepository apiTestRepository;
    @Autowired
    private SceneLinkInfoEntityConverter sceneLinkInfoEntityConverter;

    @Override
    public List<String> queryLinkMapCodesBySceneCode(String sceneCode, Integer version) {
        return sceneLinkInfoMapper.selectLinkCodes(sceneCode,version);
    }

    @Override
    public List<SceneLinkInfoEntityDO> getLinkInfo(String linkMapCode) {

        return sceneLinkInfoEntityConverter.convertSceneLinkInfoEntityDO(sceneLinkInfoMapper.selectLinkInfos(linkMapCode));
    }

    @Override
    public PageLinkMapVO  querySceneLink(PageLinkMapReq linkMapReq) {
        SceneInfoEntityDO sceneInfoEntityDO = apiTestRepository.queryLatestSceneInfo(linkMapReq.getSceneCode(),linkMapReq.getStatus());
        if(sceneInfoEntityDO==null && linkMapReq.getStatus()==SceneInfoStatusEnum.publish){
            linkMapReq.setStatus(SceneInfoStatusEnum.edit);
            sceneInfoEntityDO = apiTestRepository.queryLatestSceneInfo(linkMapReq.getSceneCode(),linkMapReq.getStatus());
        }
        if(sceneInfoEntityDO==null){
            return PageLinkMapVO.buildSelf(null,0L);
        }
        linkMapReq.setSceneVersion(sceneInfoEntityDO.getSceneVersion());
        this.queryLinkMapStatus(linkMapReq.getSceneCode(),linkMapReq.getStatus());
        List<String> linkMapCodes = sceneLinkInfoMapper.selectLinkCodes(linkMapReq.getSceneCode(),linkMapReq.getSceneVersion());
        if(linkMapCodes==null || linkMapCodes.size()==0){
            return PageLinkMapVO.buildSelf(null,0L);
        }

        Integer startNumber = (linkMapReq.getPage()-1) * linkMapReq.getSize();
        Integer endNumber = startNumber + linkMapReq.getSize() - 1;

        if(linkMapCodes.size()<startNumber){
            return PageLinkMapVO.buildSelf(null,(long) linkMapCodes.size());
        }
        if(linkMapCodes.size()<endNumber){
            endNumber = linkMapCodes.size()-1;
        }

        List<SingleLinkBaseInfo> links = new ArrayList<>();
        for(int i=startNumber;i<=endNumber;i++){
            List<SceneLinkInfoEntity> singleLinkComponentInfos = sceneLinkInfoMapper.selectLinkInfos(linkMapCodes.get(i));
            if(singleLinkComponentInfos==null){
                continue;
            }
            log.info("singleLinkComponentInfos count: {} >>>> {}",singleLinkComponentInfos.size(),JSON.toJSONString(singleLinkComponentInfos));
            Map<Integer, SceneLinkInfoEntity> linkComponentMap = singleLinkComponentInfos.stream().collect(Collectors.toMap(SceneLinkInfoEntity::getSequenceNumber, (p) -> p));
            log.info("sceneLinkInfoMapper:{}",JSON.toJSONString(linkComponentMap));
            SingleLinkBaseInfo singleLinkBaseInfo = new SingleLinkBaseInfo();
            singleLinkBaseInfo.setType(LinkMapTypeEnum.LINK);
            singleLinkBaseInfo.setCode(linkMapCodes.get(i));
            singleLinkBaseInfo.setName(linkMapCodes.get(i));
            singleLinkBaseInfo.setSceneCode(linkMapReq.getSceneCode());
            singleLinkBaseInfo.setSceneVersion(linkMapReq.getSceneVersion());
            singleLinkBaseInfo.setEnable(false);
            List<ComponentBaseInfo> nodes = new ArrayList<>();
            List<Line> lines = new ArrayList<>();

            for(int sn=0; sn<linkComponentMap.keySet().size();sn++) {
                SceneLinkInfoEntity componentInfo = linkComponentMap.get(sn);
                log.info("componentInfo >>>>> {}",JSON.toJSONString(componentInfo));
                if(LinkMapTypeEnum.valueOf(componentInfo.getLinkComponentType())==LinkMapTypeEnum.NODE){
                    log.info("querySceneLink:开始设置节点信息");
                    ComponentBaseInfo nodeInfo = getNodeInfo(componentInfo);
                    if(nodeInfo!=null){
                        log.info("querySceneLink:开始设置节点信息-{}",JSON.toJSONString(nodeInfo));
                        nodes.add(nodeInfo);
                        if(nodeInfo.getEnable()){
                            singleLinkBaseInfo.setEnable(true);
                        }
//                        if(lastComponentIsLine && lines.size()>0){
//                            if(StringUtil.isNotBlank(lines.get(lines.size()-1).getOutNodeCode())){
//                                lines.get(lines.size()-1).setOutNodeCode(nodeInfo.getCode());
//                            }
//                            lastComponentIsLine = false;
//                        }
                    }
                }
                if(LinkMapTypeEnum.valueOf(componentInfo.getLinkComponentType())==LinkMapTypeEnum.LINE) {
                    log.info("querySceneLink:开始设置线条信息");
                    Line lineInfo = getLineInfo(componentInfo);
                    if(lineInfo!=null){
                        log.info("querySceneLink:开始设置线条信息-{}",JSON.toJSONString(lineInfo));
                        SceneLinkInfoEntity inComponentInfo = linkComponentMap.get(sn-1);
                        SceneLinkInfoEntity outComponentInfo = linkComponentMap.get(sn+1);
                        lineInfo.setInNodeCode(inComponentInfo.getLinkComponentCode());
                        lineInfo.setOutNodeCode(outComponentInfo.getLinkComponentCode());
                        lines.add(lineInfo);
                    }
                }
            }
            singleLinkBaseInfo.setNodes(nodes);
            singleLinkBaseInfo.setLines(lines);
            links.add(singleLinkBaseInfo);
        }

        return PageLinkMapVO.buildSelf(links,(long) linkMapCodes.size());
    }

    @Override
    public void batchInsertLink(List<SceneLinkInfoEntityDO> list) {
        sceneLinkInfoMapper.batchInsert(sceneLinkInfoEntityConverter.convertSceneLinkInfoEntity(list));
    }

    private ComponentBaseInfo getNodeInfo(SceneLinkInfoEntity sceneLinkInfo){
        if(sceneLinkInfo==null){
            return null;
        }

        if(LinkMapTypeEnum.valueOf(sceneLinkInfo.getLinkComponentType())!=LinkMapTypeEnum.NODE){
            return null;
        }

        ComponentBaseInfo nodeInfo = new ComponentBaseInfo();
        nodeInfo.setCode(sceneLinkInfo.getLinkComponentCode());
        nodeInfo.setName(sceneLinkInfo.getLinkComponentName());
        nodeInfo.setType(LinkMapTypeEnum.NODE);
        nodeInfo.setEnable(sceneLinkInfo.getEnable());

        return nodeInfo;

    }

    private Line getLineInfo(SceneLinkInfoEntity sceneLinkInfo){
        if(sceneLinkInfo==null){
            return null;
        }

        if(LinkMapTypeEnum.valueOf(sceneLinkInfo.getLinkComponentType())!=LinkMapTypeEnum.LINE) {
            return null;
        }

        Line lineInfo = new Line();
        lineInfo.setCode(sceneLinkInfo.getLinkComponentCode());
        lineInfo.setName(sceneLinkInfo.getLinkComponentName());
        lineInfo.setType(LinkMapTypeEnum.LINE);
        lineInfo.setEnable(sceneLinkInfo.getEnable());

        return lineInfo;

    }

    @Override
    public List<SceneLinkInfoEntityDO> getLinkInfoBySceneCode(String sceneCode, Integer version) {
        Example example = new Example(SceneLinkInfoEntity.class);
        example.createCriteria().andEqualTo("sceneCode", sceneCode)
                .andEqualTo("sceneVersion", version)
                .andEqualTo("enable", Boolean.TRUE);
        return sceneLinkInfoEntityConverter.convertSceneLinkInfoEntityDO(sceneLinkInfoMapper.selectByExample(example));
    }

    @Override
    public void updateSceneLinkInfoEnable(ChangeLinkStatusCommand command) {
        Example example = new Example(SceneLinkInfoEntity.class);
        example.createCriteria()
                .andEqualTo("sceneCode", command.getAggregateId())
                .andEqualTo("sceneVersion", command.getSceneVersion())
                .andEqualTo("linkMapCode", command.getLinkMapCode())
                .andEqualTo("linkComponentType", "NODE");
        SceneLinkInfoEntity entity = new SceneLinkInfoEntity();
        entity.setEnable(command.getEnable());
        entity.preUpdate(command.getTransactor());
        sceneLinkInfoMapper.updateByExampleSelective(entity, example);
    }

    private void queryLinkMapStatus(String sceneCode, SceneInfoStatusEnum status){
//        SceneInfoReq sceneInfoReq = new SceneInfoReq();
//        sceneInfoReq.setSceneCode(sceneCode);
//        sceneInfoReq.setStatus(status);
//        sceneInfoReq.setSceneVersion(sceneVersion.toString());
        SceneInfoEntityDO sceneDO = apiTestRepository.queryLatestSceneInfo(sceneCode,status);
        if(sceneDO==null){
            throw new ServiceException( "请保存场景图后再试!");
        }
        if(StringUtil.isEmpty(sceneDO.getStepRecord())){
            throw new ServiceException( "请编辑场景图中任何节点信息并保存后再试!");
        }
        JSONObject stepJson = JSON.parseObject(sceneDO.getStepRecord());
        if(stepJson!=null){
            if(stepJson.containsKey("LinkMap")){
                if("FAIL".equals(stepJson.getString("LinkMap"))){
                    if(stepJson.containsKey("message")) {
                        throw new ServiceException(stepJson.getString("message"));
                    }else {
                        throw new ServiceException("链路生成失败，请修改场景结构或者节点名称后再试一次！");
                    }
                }
            }
        }

    }

    @Override
    public void deleteBySceneInfo(String sceneCode, Integer version) {
        Example example = new Example(SceneLinkInfoEntity.class);
        example.createCriteria()
                .andEqualTo("sceneCode", sceneCode)
                .andEqualTo("sceneVersion", version);
        sceneLinkInfoMapper.deleteByExample(example);
    }

}
