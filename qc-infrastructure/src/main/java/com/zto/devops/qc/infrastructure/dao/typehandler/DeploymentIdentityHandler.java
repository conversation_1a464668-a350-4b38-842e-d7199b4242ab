package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import com.zto.devops.qc.client.enums.testmanager.coverage.DeploymentIdentityEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

/**
 * <AUTHOR>
 * @create 2022/9/10 16:40
 */
@MappedJdbcTypes(value = JdbcType.VARCHAR, includeNullJdbcType = true)
public class DeploymentIdentityHandler extends BaseEnumTypeHandler<DeploymentIdentityEnum> {
    public DeploymentIdentityHandler() {
        super(DeploymentIdentityEnum.class);
    }
}
