package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TransitionNodeEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TransitionNodeVO;
import com.zto.devops.qc.infrastructure.dao.entity.TransitionNodeEntity;
import org.mapstruct.Mapper;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring")
public interface TransitionNodeVOConverter {

    List<TransitionNodeVO> converterList(List<TransitionNodeEntity> entityList);

    List<TransitionNodeVO> convertTransitionNodeVO(Collection<TransitionNodeEntity> transitionNodeEntities);

    List<TransitionNodeEntityDO> converter2DOList(List<TransitionNodeEntity> entityList);
}
