package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TmTestReportEntityDO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmTestReportVO;
import com.zto.devops.qc.client.model.testmanager.report.event.TmAccessReportAddEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmAccessReportEditEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmOnlineSmokeReportAddEvent;
import com.zto.devops.qc.client.model.testmanager.report.event.TmOnlineSmokeReportEditEvent;
import com.zto.devops.qc.infrastructure.dao.entity.ModuleTestEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TestFunctionPointEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestReportEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TmTestReportEntityConverter {

    List<TmTestReportEntityDO> convert2DOList(List<TmTestReportEntity> entityList);

    List<TmTestReportVO> convertVOList(List<TmTestReportEntity> entityList);

    List<TmModuleTestVO> convertModuleTest(List<ModuleTestEntity> moduleTestEntities);

    List<TmModuleTestVO> convertByFunctionPoint(List<TestFunctionPointEntity> functionPointEntityList);

    TmTestReportEntityDO convert2DO(TmTestReportEntity entity);

    TmTestReportEntity converter(TmAccessReportAddEvent event);

    TmTestReportEntity converter(TmAccessReportEditEvent event);

    TmTestReportEntity converter(TmOnlineSmokeReportAddEvent event);

    TmTestReportEntity converter(TmOnlineSmokeReportEditEvent event);
}
