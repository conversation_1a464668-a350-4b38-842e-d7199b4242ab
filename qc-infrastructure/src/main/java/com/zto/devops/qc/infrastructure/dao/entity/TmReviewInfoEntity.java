package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.report.ReviewType;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmReviewTypeHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "tm_review_info")
public class TmReviewInfoEntity extends BaseEntity {

    /**
     * 模块code
     */
    @Id
    private String code;

    /**
     * 报告code
     */
    @Column(name = "report_code")
    private String reportCode;

    /**
     * 评审日期
     */
    @Column(name = "review_date")
    private Date reviewDate;

    /**
     * 评审类型方式
     */
    @Column(name = "review_type")
    @ColumnType(typeHandler = TmReviewTypeHandler.class)
    private ReviewType reviewType;

    /**
     * 记录人id
     */
    @Column(name = "record_user_id")
    private Long recordUserId;

    /**
     * 记录人
     */
    @Column(name = "record_user_name")
    private String recordUserName;

    /**
     * 产品参评人
     */
    @Column(name = "product_review_users")
    private String productReviewUsers;

    /**
     * 开发参评人
     */
    @Column(name = "develop_review_users")
    private String developReviewUsers;

    /**
     * 测试参评人
     */
    @Column(name = "test_review_users")
    private String testReviewUsers;

    private static final long serialVersionUID = 1L;
}
