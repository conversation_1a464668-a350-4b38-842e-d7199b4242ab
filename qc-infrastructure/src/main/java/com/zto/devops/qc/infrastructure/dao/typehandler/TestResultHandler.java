package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import com.zto.devops.qc.client.enums.report.TestResultEunm;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

/**
 * 项目名称：qc-parent
 * 类 名 称：TestResultHandler
 * 类 描 述：TODO
 * 创建时间：2021/11/27 9:55 上午
 * 创 建 人：bulecat
 */
@MappedJdbcTypes(value = JdbcType.VARCHAR, includeNullJdbcType = true)
public class TestResultHandler extends BaseEnumTypeHandler<TestResultEunm> {
    public TestResultHandler() {
        super(TestResultEunm.class);
    }

}
