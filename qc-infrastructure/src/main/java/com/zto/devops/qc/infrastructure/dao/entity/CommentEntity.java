package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler;
import lombok.Data;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@ToString
@Data
@Table(name = "qc_comment")
public class CommentEntity extends BaseEntity {

    /**
     * 业务主键
     */
    @Id
    private String code;

    /**
     * 领域
     */
    @Column(name = "domain")
    @ColumnType(typeHandler = DomainHandler.class)
    private DomainEnum domain;

    /**
     * 业务编码: 业务实例 如 缺陷编码等
     */
    @Column(name = "business_code")
    private String businessCode;

    /**
     * 被回复评论的最上层code
     */
    @Column(name = "top_replied_code")
    private String topRepliedCode;

    /**
     * 被回复评论code
     */
    @Column(name = "replied_code")
    private String repliedCode;

    /**
     * 被回复人名
     */
    @Column(name = "replied_user_name")
    private String repliedUserName;
    /**
     * 被回复人编码
     */
    @Column(name = "replied_user_id")
    private Long repliedUserId;

    /**
     * 评论的层级
     */
    private Integer level;

    /**
     * 评论内容
     */
    private String content;


}