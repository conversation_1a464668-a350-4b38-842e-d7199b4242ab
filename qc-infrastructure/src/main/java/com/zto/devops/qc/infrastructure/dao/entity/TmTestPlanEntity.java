package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.framework.infrastructure.dao.handler.MapJsonTypeHandler;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanDatePartitionEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStrategyEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanDatePartitionHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanStatusHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanStrategyHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanTypeHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
@Table(name = "tm_test_plan")
@Data
public class TmTestPlanEntity extends BaseEntity implements Serializable {


    /**
     * 自增ID
     */
    private Long id;

    /**
     * 计划编号
     */
    @Id
    private String code;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划状态 ，INITIAL-未开始|IN_ROGESS进行中|已完成|已终止
     */
    @ColumnType(typeHandler = TmTestPlanStatusHandler.class)
    private TestPlanNewStatusEnum status;

    /**
     * 计划类型，TEST_PLAN-测试计划|MOBILE_SPECIAL-移动专项|SAFETY_TEST-安全测试计划
     */
    @ColumnType(typeHandler = TmTestPlanTypeHandler.class)
    private TestPlanNewTypeEnum type;

    /**
     * 所属产品id
     */
    private String productCode;

    /**
     * 所属产品名称
     */
    private String productName;

    /**
     * 关联版本id
     */
    private String versionCode;

    /**
     * 关联版本名称
     */
    private String versionName;

    /**
     * 测试策略
     */
    @ColumnType(typeHandler = TmTestPlanStrategyHandler.class)
    private TestPlanStrategyEnum testStrategy;

    /**
     * 开发人数
     */
    private Integer developerNum;

    /**
     * 测试人数
     */
    private Integer testerNum;

    /**
     * 计划准入/提测时间
     */
    private Date accessDate;

    /**
     * 计划准入/提测时间上下午时间分区 上午 下午
     */
    @ColumnType(typeHandler = TmTestPlanDatePartitionHandler.class)
    private TestPlanDatePartitionEnum accessDatePartition;

    /**
     * 计划准出时间
     */
    private Date permitDate;
    /**
     * 计划准出时间上下午时间分区 上午 下午
     */
    @ColumnType(typeHandler = TmTestPlanDatePartitionHandler.class)
    private TestPlanDatePartitionEnum permitDatePartition;

    /**
     * 版本开始时间
     */
    private Date startDate;

    /**
     * 计划上线时间
     */
    private Date publishDate;

    /**
     * 产品负责人id
     */
    private Long productDirectorId;

    /**
     * 产品负责人名
     */
    private String productDirectorName;

    /**
     * 测试负责人id
     */
    private Long testDirectorId;

    /**
     * 测试负责人名
     */
    private String testDirectorName;

    /**
     * 计划负责人id
     */
    private Long planDirectorId;

    /**
     * 计划负责人姓名
     */
    private String planDirectorName;

    /**
     * 关联计划编号
     */
    private String relationPlanCode;

    /**
     * 修改次数
     */
    private Integer editNo;

    /**
     * 阶段状态，json格式
     */
    @ColumnType(typeHandler = MapJsonTypeHandler.class)
    private Map<String, Object> stageStatus;

    /**
     * 计划状态 ，INITIAL-未开始|IN_ROGESS进行中|已完成|已终止
     */
    @ColumnType(typeHandler = TmTestPlanStatusHandler.class)
    private TestPlanNewStatusEnum historyStatus;

    /**
     * 产品类型
     */
    private String productSource;

    private String comment;

    /**
     * 是否已确认覆盖率结果（0:否; 1:是）
     */
    private Boolean checkFlag;

    private static final long serialVersionUID = 1L;


}