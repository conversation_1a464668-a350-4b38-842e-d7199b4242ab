package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

@MappedJdbcTypes(value = JdbcType.VARCHAR, includeNullJdbcType = true)
public class DomainHandler extends BaseEnumTypeHandler<DomainEnum> {
    public DomainHandler() {
        super(DomainEnum.class);
    }
}
