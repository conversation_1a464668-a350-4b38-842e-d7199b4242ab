
package com.zto.devops.qc.infrastructure.gateway.zcat;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.agent.DependencyTypeEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.QueryAppTagsVO;
import com.zto.devops.qc.client.service.agent.model.DependencyQueryReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.QueryAppIdTagsReq;
import com.zto.devops.qc.domain.gateway.http.HttpService;
import com.zto.devops.qc.domain.gateway.zcat.ZCatService;
import com.zto.devops.qc.domain.model.coverage.ZcatMetricsVO;
import com.zto.devops.qc.infrastructure.config.ZCatConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ZCatServiceImpl implements ZCatService {

    @Autowired
    private ZCatConfig zCatConfig;
    @Autowired
    private HttpService httpService;

    @Override
    public Map<String, Map<String, String>> queryAppIdTags(QueryAppIdTagsReq req) {
        String resp = httpService.doPost(zCatConfig.queryAppIdTagsUrl(), JSONUtil.toJsonStr(req));
        log.info("queryAppIdTags_resp:{}", resp);
        if (StringUtil.isBlank(resp)) {
            return null;
        }

        QueryAppTagsVO response = null;
        try {
            response = JSONUtil.toBean(resp, QueryAppTagsVO.class);
        } catch (Exception e) {
            log.error("queryAppIdTags_error:{}", e.getMessage());
        }
        if (response == null || !response.isSuccess()) {
            return null;
        }
        return response.getData();
    }

    public JSONObject queryDependencyService(DependencyQueryReq req) {
        if (req.getApiType() == DependencyTypeEnum.DubboService && StringUtil.isNotEmpty(req.getServiceName())) {
            //提取serviceName.Method
            String targetStr = req.getServiceName();
            if (targetStr.contains("(")) {
                String[] strings = targetStr.split("\\(");
                targetStr = strings[0];
                String[] serviceNames = targetStr.split("\\.");
                if (serviceNames.length >= 2) {
                    targetStr = serviceNames[serviceNames.length - 1].replace("#", ".");
                }
            }
            req.setServiceName(targetStr);
        }
        LocalDate endLocalDate = LocalDate.now();
        LocalDate startLocalDate = endLocalDate.minusDays(zCatConfig.queryDependencyServiceDays().longValue());
        endLocalDate = endLocalDate.plusDays(1L);
        Long startAt = startLocalDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
        Long endAt = endLocalDate.atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli();
        Map<String, Object> pageMap = new HashMap<>();
        pageMap.put("page", req.getPage());
        pageMap.put("pageSize", req.getPageSize());
        Map<String, Object> reqMap = new HashMap<>();
        if (req.getApiType() == DependencyTypeEnum.DubboService) {
            reqMap.put("serviceType", "rpc.dubbo.service");
        }
        if (req.getApiType() == DependencyTypeEnum.HttpService) {
            reqMap.put("serviceType", "rpc.http.service");
        }
        if (StringUtil.isNotEmpty(req.getServiceName())) {
            reqMap.put("serviceName", req.getServiceName());
        }
        if (req.getDependencyType() == DependencyTypeEnum.HttpClient) {
            reqMap.put("clientType", "rpc.http.client");
        }
        if (req.getDependencyType() == DependencyTypeEnum.DubboClient) {
            reqMap.put("clientType", "rpc.dubbo.client");
        }
        if (StringUtil.isNotEmpty(req.getDependencyName())) {
            if (req.getDependencyType() == DependencyTypeEnum.DubboClient) {
                reqMap.put("clientName", req.getDependencyName().replace("#", "."));
            } else {
                reqMap.put("clientName", req.getDependencyName());
            }
        }
        reqMap.put("startAt", startAt);
        reqMap.put("endAt", endAt);
        reqMap.put("appId", req.getAppid());
        reqMap.put("page", pageMap);
        String resp = httpService.doPost(zCatConfig.queryZcatApiUrl() + "/api/devops/queryInvocation", JSON.toJSONString(reqMap));
        log.info("Zcat queryInvocation resp:{}", resp);
        try {
            JSONObject respObj = JSON.parseObject(resp);
            if (respObj.containsKey("data")) {
                JSONObject datas = respObj.getJSONObject("data");
                if (datas.containsKey("data")) {
                    JSONArray dataList = datas.getJSONArray("data");
                    if (CollectionUtil.isEmpty(dataList)) {
                        return null;
                    }
                    for (int i = 0; i < dataList.size(); i++) {
                        JSONObject dataObj = dataList.getJSONObject(i);
                        if (dataObj.containsKey("clientType") && dataObj.containsKey("clientName")) {
                            if ("rpc.dubbo.client".equals(dataObj.getString("clientType"))) {
                                dataObj.put("clientType", DependencyTypeEnum.DubboClient.name());
                                dataObj.put("clientName", dataObj.getString("clientName").replace(".", "#"));
                            }
                            if ("rpc.http.client".equals(dataObj.getString("clientType"))) {
                                dataObj.put("clientType", DependencyTypeEnum.HttpClient.name());
                            }
                        }
                    }
                }
                return datas;
            } else {
                return null;
            }
        } catch (Exception ex) {
            log.error("", ex);
        }
        return null;
    }

    public List<ZcatMetricsVO> getInterfaceCallsNumber(Map<String, Object> reqMap) {
        String url = zCatConfig.getZcatApiUrlProd() + "/api/zbase/services/statistics";
        String resp = httpService.doPost(url, JSON.toJSONString(reqMap));
        if (StringUtil.isEmpty(resp)) {
            return null;
        }
        JSONObject respObj = JSON.parseObject(resp);
        if (!"SYS000".equals(respObj.getString("statusCode"))) {
            return null;
        }
        if (StringUtil.isEmpty(respObj.getString("result"))) {
            return null;
        }
        List<ZcatMetricsVO> metricsVOS = JSON.parseArray(respObj.getString("result"), ZcatMetricsVO.class);
        return metricsVOS;
    }

}
