package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.infrastructure.dao.typehandler.DomainHandler;
import lombok.Getter;
import lombok.Setter;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@Table(name = "tm_testcase_relation")
public class TestcaseRelationEntity extends BaseEntity {

    @Id
    private Long id;

    /**
     * 测试用例code
     */
    private String testcaseCode;

    /**
     * 关联业务code
     */
    private String businessCode;

    /**
     * 领域
     */
    @ColumnType(typeHandler = DomainHandler.class)
    private DomainEnum domain;
}
