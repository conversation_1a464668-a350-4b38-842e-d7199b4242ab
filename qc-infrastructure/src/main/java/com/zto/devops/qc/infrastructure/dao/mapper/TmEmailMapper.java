package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.testmanager.email.query.VersionEmailQuery;
import com.zto.devops.qc.infrastructure.dao.entity.TmEmailEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmVersionEmailEntity;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 发送邮件记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-09-17
 */
public interface TmEmailMapper extends Mapper<TmEmailEntity> {

    List<TmVersionEmailEntity> selectVersionEmail(VersionEmailQuery query);

    void insertBatch(List<TmEmailEntity> part);

}
