package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "qc_module_test")
public class ModuleTestEntity extends BaseEntity {


    /**
     * 模块code
     */
    @Id
    private String code;

    /**
     * 报告code
     */
    @Column(name = "report_code")
    private String reportCode;

    /**
     * 测试类型
     */
    @Column(name = "test_type")
    private String testType;

    /**
     * 测试j结果 -1失败；1通过
     */
    @Column(name = "test_result")
    private Integer testResult;

    /**
     * 有效缺陷数
     */
    @Column(name = "valid_issue_count")
    private Integer validIssueCount;

    /**
     * 遗留P012缺陷数
     */
    @Column(name = "legacy_issue_count")
    private Integer legacyIssueCount;

    /**
     * 报告人id
     */
    @Column(name = "report_user_id")
    private Long reportUserId;

    /**
     * 报告人名称
     */
    @Column(name = "report_user_name")
    private String reportUserName;

    private static final long serialVersionUID = 1L;

}