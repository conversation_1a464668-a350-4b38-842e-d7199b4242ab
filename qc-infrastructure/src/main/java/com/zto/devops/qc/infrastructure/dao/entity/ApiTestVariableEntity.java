package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableUsageTypeEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.ApiTestSubVariableTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.ApiTestVariableTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.ApiTestVariableUsageTypeHandler;
import lombok.Data;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@ToString
@Data
@Table(name = "tm_api_test_variable")
public class ApiTestVariableEntity extends BaseEntity {

    @Column(name = "id")
    private Long id;

    /**
     * 产品编码
     */
    @Column(name = "product_code")
    private String productCode;

    /**
     * 产品名称
     */
    @Column(name = "product_name")
    private String productName;

    /**
     * 链路code
     */
    @Column(name = "link_code")
    private String linkCode;

    /**
     * 变量编号
     */
    @Id
    @Column(name = "variable_code")
    private String variableCode;

    /**
     * 变量名
     */
    @Column(name = "variable_name")
    private String variableName;

    /**
     * 变量键
     */
    @Column(name = "variable_key")
    private String variableKey;

    /**
     * 变量值
     */
    @Column(name = "variable_value")
    private String variableValue;

    /**
     * 类型(SSO, JDBC_CONFIG, VARIABLE)
     */
    @Column(name = "type")
    @ColumnType(typeHandler = ApiTestVariableTypeHandler.class)
    private VariableTypeEnum type;

    @Column(name = "scene_type")
    private Integer sceneType;

    /**
     * 使用类型(OUTPUT(2),INPUT(1))
     */
    @Column(name = "usage_type")
    @ColumnType(typeHandler = ApiTestVariableUsageTypeHandler.class)
    private VariableUsageTypeEnum usageType;

    /**
     * 是否必填
     */
    @Column(name = "required_status")
    private Boolean requiredStatus;

    /**
     * 执行状态
     */
    @Column(name = "variable_status")
    private Boolean variableStatus;

    /**
     * 变量说明
     */
    @Column(name = "variable_desc")
    private String variableDesc;

    /**
     * 变量子类型
     */
    @Column(name = "sub_variable_type")
    @ColumnType(typeHandler = ApiTestSubVariableTypeHandler.class)
    private SubVariableTypeEnum subVariableType;

    /**
     * 登录有效时间
     */
    @Column(name = "login_valid_time")
    private Integer loginValidTime;

}
