package com.zto.devops.qc.infrastructure.gateway.zim;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.infrastructure.service.ZimManager;
import com.zto.devops.qc.client.model.issue.entity.CountUserIssueNumVO;
import com.zto.devops.qc.client.model.testmanager.config.entity.ProjectProfileVO;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.zim.ZimService;
import com.zto.message.dto.clientobject.BatchMessageCO;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


@Slf4j
@Component
public class ZimServiceImpl implements ZimService {

    @Autowired
    private ZimManager zimManager;
    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    @Override
    public String sendMessage(List<CountUserIssueNumVO> batchList, String sceneCode) {
        return zimManager.sendMessage(sendBody(batchList), sceneCode);
    }

    /**
     * 消息体封装
     *
     * @param dataList
     * @return
     */
    private List<BatchMessageCO> sendBody(List<CountUserIssueNumVO> dataList) {
        log.info("doSendIssueMessage_datalist->{}", JsonUtil.toJSON(dataList));
        List<BatchMessageCO> batchList = new ArrayList<>();
        if (CollectionUtil.isEmpty(dataList)) {
            return batchList;
        }
        ProjectProfileVO projectProfileVO = qcConfigBasicService.getProjectProfileConfig();
        String skipUrl = "[查看详情](" + projectProfileVO.getFrontUrl() + projectProfileVO.getIssueList() + ")";
        dataList.forEach(vo -> {
            BatchMessageCO co = new BatchMessageCO();
            co.setBizId(UUID.randomUUID().toString());
            co.putPayload("count", vo.getCountNum());
            co.putPayload("receivedUser", vo.getHandleUserId());
            co.putPayload("linkUrl", skipUrl);
            batchList.add(co);
        });
        return batchList;
    }
}
