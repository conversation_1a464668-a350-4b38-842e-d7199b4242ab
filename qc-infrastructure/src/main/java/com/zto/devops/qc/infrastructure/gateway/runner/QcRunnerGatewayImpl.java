package com.zto.devops.qc.infrastructure.gateway.runner;

import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskInfo;
import com.zto.devops.qc.domain.gateway.runner.QcRunnerGateway;
import com.zto.devops.qc.infrastructure.cache.ZBaseDbInfoCache;
import com.zto.devops.qc.infrastructure.config.QcRunnerConfig;
import com.zto.devops.qc.infrastructure.gateway.util.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Component
public class QcRunnerGatewayImpl implements QcRunnerGateway {

    @Autowired
    private QcRunnerConfig runnerConfig;
    @Autowired
    private ZBaseDbInfoCache dbInfoCache;

    @Override
    public boolean isWhiteList(User user) {
        if (Objects.isNull(user)) {
            log.info("QcRunner while list: api调用，用户信息为空，默认走性能优化版本");
            return false;
        }
        String username = user.getZtoAccount();
        if (StringUtils.isBlank(runnerConfig.getWhiteList()) || StringUtils.isBlank(username)) {
            log.info("QcRunner while list: 未获取到用户信息   white list: {}  user: {}", runnerConfig.getWhiteList(), JSON.toJSONString(user));
            return false;
        }
        boolean isWhiteList = ArrayUtils.contains(runnerConfig.getWhiteList().split(","), username);
        log.info("QcRunner while list: {} current user is {}  rssult is {}", runnerConfig.getWhiteList(), username, isWhiteList);
        return isWhiteList;
    }

    @Override
    public void debug(DebugTaskInfo debugTaskInfo, Map<String, Object> context, Map<String, Pair<DebugTaskInfo, Map<String, Object>>> debugTaskInfoMap) {
        JSONObject scene = getSceneRequestJson(debugTaskInfo, context);
        extractedDataGenerationData(debugTaskInfoMap, scene);

        String param = JSON.toJSONString(scene);
        log.info("造数性能优化，请求地址： {}, 参数：{}", runnerConfig.getUrl(), param);

        HttpUtils.doPost(runnerConfig.getUrl(), param);
    }

    private void extractedDataGenerationData(Map<String, Pair<DebugTaskInfo, Map<String, Object>>> debugTaskInfoMap, JSONObject scene) {
        // 在用例工厂中，会调用造数，这里额外补充造数数据到同一个JSON中
        Map<String, JSONObject> dataGenerationMap = new HashMap<>();
        debugTaskInfoMap.forEach((sceneCode, pair) -> {
            DebugTaskInfo dataGenerationDebugTaskInfo = pair.getLeft();
            Map<String, Object> dataGenerationContext = pair.getRight();
            JSONObject dataGenerationScene = getSceneRequestJson(dataGenerationDebugTaskInfo, dataGenerationContext);
            dataGenerationMap.put(sceneCode, dataGenerationScene);
        });
        scene.put("dataGenerationMap", dataGenerationMap);
    }

    private @NotNull JSONObject getSceneRequestJson(DebugTaskInfo debugTaskInfo, Map<String, Object> context) {
        // todo 临时代码，优先解决造数性能问题
        JSONObject scene = JSON.parseObject(JSON.toJSONString(debugTaskInfo.getScene()));
        scene.put("taskId", debugTaskInfo.getTaskId());
        scene.put("sceneCode", debugTaskInfo.getSceneCode());
        scene.put("context", context);
        // 用户执行时的tag
        scene.put("executeTag", debugTaskInfo.getZtoenv());
        // qc域自己的tag
        scene.put("titansRouteTag", getTag());
        // qc域自己的环境
        scene.put("env", System.getProperty("env", "FAT"));
        Set<String> dbIds = debugTaskInfo.getScene().getDbIds();
        Map<String, JSONObject> jdbcConfigs = new HashMap<>();
        for (String dbId : dbIds) {
            jdbcConfigs.put(dbId, dbInfoCache.queryDbConfig(dbId));
        }
        scene.put("jdbcConfigs", jdbcConfigs);

        // todo 在造数中，scene中的line和start、end是没有意义的，完全依赖于 linkBaseInfo  这里再做一次兼容
        scene.put("linkBaseInfo", debugTaskInfo.getLinkBaseInfo());
        return scene;
    }

    private String getTag() {
        try {
            RpcContext context = RpcContext.getContext();
            if (context != null
                    && CollectionUtil.isNotEmpty(context.getAttachments())
                    && context.getAttachments().containsKey("dubbo.tag")) {
                String tag = context.getAttachment("dubbo.tag");
                if (StringUtil.isNotEmpty(tag)) {
                    return tag;
                }
            }
            return System.getProperty("titansRouteTag");
        } catch (Exception e) {
            log.error("获取tag异常", e);
            return null;
        }
    }

    @Override
    public JSONObject queryDbConfig(String dbId) {
        return dbInfoCache.queryDbConfig(dbId);
    }
}
