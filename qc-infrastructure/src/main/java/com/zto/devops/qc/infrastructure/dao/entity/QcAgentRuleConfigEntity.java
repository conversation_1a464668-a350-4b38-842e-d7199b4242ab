package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.infrastructure.dao.handler.MapJsonTypeHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Table;
import java.beans.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Table(name = "qc_agent_rule_config")
@Data
public class QcAgentRuleConfigEntity implements Serializable {
    /**
     * 自增ID
     */
    private Long id;

    @Column(name = "product_code")
    private String productCode;

    @Column(name = "appid")
    private String appid;

    @Column(name = "version_code")
    private String versionCode;

    @Column(name = "injection_rule_name")
    private String injectionRuleName;

    @Column(name = "heuristic_class_name")
    private String heuristicClassName;

    @Column(name = "method_name")
    private String methodName;

    @Column(name = "chaos_exception_type")
    private String chaosExceptionType;

    @Column(name = "rule_type")
    private String ruleType;

    @Column(name = "rule_body")
    @ColumnType(typeHandler = MapJsonTypeHandler.class)
    private Map<String, Object> ruleBody;

    @Column(name = "status")
    private Integer status;

    @Column(name = "gmt_create")
    private Date gmtCreate;

    @Column(name = "gmt_modified")
    private Date gmtModified;

    @Column(name = "creator")
    private String creator;

    @Column(name = "modifier")
    private String modifier;

    @Column(name = "enable")
    private Integer enable;

    @Column(name = "injection")
    private String injection;

    @Transient
    public boolean hasId() {
        return this.getId() != null;
    }

    public void preUpdate(User user) {
        if (null != user) {
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }

    public void preCreate(User user) {
        if (null != user) {
            this.setCreator(user.getUserName());
            this.setGmtCreate(new Date());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }

}
