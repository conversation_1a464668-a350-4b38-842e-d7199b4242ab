package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseSourceTypeEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedJdbcTypes(value = JdbcType.INTEGER, includeNullJdbcType = true)
public class ApiCaseSourceTypeHandler extends BaseTypeHandler<ApiCaseSourceTypeEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, ApiCaseSourceTypeEnum apiCaseSourceTypeEnum, JdbcType jdbcType) throws SQLException {
        if (null == apiCaseSourceTypeEnum) {
            preparedStatement.setInt(i, 0);
        } else {
            preparedStatement.setInt(i, apiCaseSourceTypeEnum.getCode());
        }
    }

    @Override
    public ApiCaseSourceTypeEnum getNullableResult(ResultSet resultSet, String s) throws SQLException {
        int result = resultSet.getInt(s);
        return ApiCaseSourceTypeEnum.codeOf(result);
    }

    @Override
    public ApiCaseSourceTypeEnum getNullableResult(ResultSet resultSet, int i) throws SQLException {
        int result = resultSet.getInt(i);
        return ApiCaseSourceTypeEnum.codeOf(result);
    }

    @Override
    public ApiCaseSourceTypeEnum getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        int result = callableStatement.getInt(i);
        return ApiCaseSourceTypeEnum.codeOf(result);
    }
}
