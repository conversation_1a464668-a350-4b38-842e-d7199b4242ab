package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.SceneInfoEntity;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface SceneEntityConverter {

    List<SceneInfoEntityDO> converterToSceneInfoEntityList(List<SceneInfoEntity> entityList);

}
