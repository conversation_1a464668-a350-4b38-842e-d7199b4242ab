package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.rpc.FlowLaneTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.GenerateTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.devops.qc.client.model.testmanager.coverage.query.ReportDto;
import com.zto.devops.qc.infrastructure.dao.typehandler.DiffTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.FlowLaneTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.GenerateTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.RecordTypeHandler;
import lombok.Data;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2022/10/21 0:18
 */
@ToString
@Data
public class CoverageRecordGenerateEntity extends BaseEntity {

    @Column(name = "id")
    private Long id;

    /**
     * 版本编码
     */
    @Column(name = "version_code")
    private String versionCode;

    /**
     * 版本名称
     */
    @Column(name = "version_name")
    private String versionName;

    /**
     * appId
     */
    @Column(name = "app_id")
    private String appId;

    /**
     * 报告类型(BRANCH-分支,MASTER-主干)
     */
    @Column(name = "record_type")
    @ColumnType(typeHandler = RecordTypeHandler.class)
    private RecordTypeEnum recordType;

    /**
     * 生成类型(AUTO-自动生成,MANUAL-手动触发)
     */
    @Column(name = "generate_type")
    @ColumnType(typeHandler = GenerateTypeHandler.class)
    private GenerateTypeEnum generateType;

    /**
     * 分支名称
     */
    @Column(name = "branch_name")
    private String branchName;

    /**
     * git提交id
     */
    @Column(name = "commit_id")
    private String commitId;

    /**
     * git地址
     */
    @Column(name = "git_url")
    private String gitUrl;

    /**
     * 基准分支名称
     */
    @Column(name = "basic_branch_name")
    private String basicBranchName;

    /**
     * 基准commitId
     */
    @Column(name = "basic_commit_id")
    private String basicCommitId;

    /**
     * 下载包名
     */
    @Column(name = "package_name")
    private String packageName;

    /**
     * 下载包地址
     */
    private String downloadUrl;

    /**
     * 分支第一次部署记录(获取exec文件)
     */
    private String firstBranchCommitId;

    /**
     * 是否需要合并
     */
    private Boolean mergeDump;

    /**
     * 通过报告查看具体到行的源码的目录
     */
    private List<String> parent;

    /**
     * 本地保存的路径
     */
    private String localClassesPath;

    /**
     * 两个版本中间版本
     */
    private Set<String> middleCommitIdList;

    /**
     * 上一次发布的commitId
     */
    private String lastCommitId;

    /**
     * 历史报告
     */
    private ReportDto reportDto;

    /**
     * exec名字
     */
    private String execName;

    /**
     * 本地报告保存的路径
     */
    private String htmlPath;

    /**
     * 项目名
     */
    private String pName;

    /**
     * 差异类型
     */
    @Column(name = "diff_type")
    @ColumnType(typeHandler = DiffTypeHandler.class)
    private DiffTypeEnum diffType;

    /**
     * 空间名称
     */
    private String envName;

    /**
     * 项目gitId
     */
    private Long gitProjectId;

    private String productCode;

    private String outputFileName;

    /**
     * 发布泳道
     */
    @Column(name = "flow_lane_type")
    @ColumnType(typeHandler = FlowLaneTypeHandler.class)
    private FlowLaneTypeEnum flowLaneType;
}