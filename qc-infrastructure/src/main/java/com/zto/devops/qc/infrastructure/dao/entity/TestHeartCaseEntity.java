package com.zto.devops.qc.infrastructure.dao.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;

@Data
@Table(name = "tm_heart_case")
public class TestHeartCaseEntity extends TestcaseEntity implements Serializable {

    /**
     * 主键
     */
    @Id
    private Long id;

    /**
     * 用例code
     */
    private String caseCode;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 应用id
     */
    private String appName;

    /**
     * 应用code
     */
    private String appCode;

    /**
     * 最新告警时间
     */
    private Date warnTime;
}
