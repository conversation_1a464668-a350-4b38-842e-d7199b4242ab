package com.zto.devops.qc.infrastructure.gateway.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.project.client.enums.common.EnableEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanDatePartitionEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.enums.testmanager.report.TestReportTypeEnum;
import com.zto.devops.qc.client.model.dto.ReviewInfoEntityDO;
import com.zto.devops.qc.client.model.dto.TestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TestReportEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestReportEntityDO;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.ReportVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.*;
import com.zto.devops.qc.client.model.testmanager.report.event.*;
import com.zto.devops.qc.client.model.testmanager.report.query.PageReportMqQuery;
import com.zto.devops.qc.domain.gateway.repository.ITmTestReportRepository;
import com.zto.devops.qc.domain.gateway.repository.TestReportRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.infrastructure.converter.TestReportEntityConverter;
import com.zto.devops.qc.infrastructure.converter.TmTestReportEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.*;
import com.zto.devops.qc.infrastructure.dao.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

@Component
@Slf4j
public class TmTestReportRepositoryImpl implements ITmTestReportRepository {

    @Autowired
    private TmTestReportMapper tmTestReportMapper;
    @Autowired
    private TmTestReportEntityConverter tmTestReportEntityConverter;

    @Autowired
    private TmReviewInfoMapper tmReviewInfoMapper;

    @Autowired
    private TmReviewRenewalMapper tmReviewRenewalMapper;

    @Autowired
    private TmReviewOpinionMapper tmReviewOpinionMapper;

    @Autowired
    private ModuleTestMapper moduleTestMapper;

    @Autowired
    private TestReportEntityConverter convertor;

    @Autowired
    private TmTestPlanMapper tmTestPlanMapper;

    @Autowired
    private TestFunctionPointMapper testFunctionPointMapper;

    @Autowired
    private IProductRpcService iProductRpcService;

    @Autowired
    private AttachmentMapper attachmentMapper;

    /**
     * 超级管理员标识
     */
    private static final String SUPPER_USER_PERMISSION_KEY = "globalpermCanWriteProd";

    @Override
    public List<TmTestReportEntityDO> selectByPlanCode(String code) {
        Example example = new Example(TmTestReportEntity.class);
        example.createCriteria().andEqualTo("planCode", code).andEqualTo("enable",
                Boolean.TRUE);
        List<TmTestReportEntity> entityList = tmTestReportMapper.selectByExample(example);
        return CollectionUtils.isEmpty(entityList) ? new ArrayList<>() : tmTestReportEntityConverter.convert2DOList(entityList);
    }

    @Override
    public TmTestReportEntityDO getReportByReportCode(String reportCode) {
        Example example = new Example(TmTestReportEntity.class);
        example.createCriteria().andEqualTo("reportCode", reportCode)
                .andEqualTo("enable", EnableEnum.enable.getValue());
        List<TmTestReportEntity> entityList = tmTestReportMapper.selectByExample(example);
        return CollectionUtil.isNotEmpty(entityList) ? tmTestReportEntityConverter.convert2DO(entityList.get(0)) : null;
    }

    @Override
    public TmTestReportEntityDO getReportByPlanCodeAndReportType(String planCode, ReportType type) {
        List<TmTestReportEntity> entityList = null;
        Example example = new Example(TmTestReportEntity.class);
        example.createCriteria().andEqualTo("planCode", planCode)
                .andEqualTo("reportType", type).andEqualTo("enable", Boolean.TRUE);
        entityList = tmTestReportMapper.selectByExample(example);
        return CollectionUtil.isNotEmpty(entityList) ? tmTestReportEntityConverter.convert2DO(entityList.get(0)) : null;
    }

    @Override
    public List<TmTestReportVO> getReportListByPlanCodeAndReportType(String planCode, ReportType type) {
        List<TmTestReportEntity> entityList = null;
        Example example = new Example(TmTestReportEntity.class);
        example.createCriteria().andEqualTo("planCode", planCode)
                .andEqualTo("reportType", type).andEqualTo("enable", Boolean.TRUE);
        entityList = tmTestReportMapper.selectByExample(example);
        return tmTestReportEntityConverter.convertVOList(entityList);
    }

    @Override
    public List<TmModuleTestVO> findReportModuleTestVOS(String reportCode) {
        Example example = new Example(ModuleTestEntity.class);
        example.createCriteria().andEqualTo("reportCode", reportCode).andEqualTo("enable", true);
        List<ModuleTestEntity> moduleTestEntities = moduleTestMapper.selectByExample(example);
        List<TmModuleTestVO> voList = tmTestReportEntityConverter.convertModuleTest(moduleTestEntities);
        return CollectionUtil.isEmpty(voList) ? new ArrayList<>() : voList;
    }

    @Override
    public List<TmModuleTestVO> findPlanModuleTestVOS(String planCode) {
        Example example = new Example(TestFunctionPointEntity.class);
        example.createCriteria().andEqualTo("businessCode", planCode).andEqualTo("enable", true);
        List<TestFunctionPointEntity> functionPointEntityList = testFunctionPointMapper.selectByExample(example);
        List<TmModuleTestVO> voList = tmTestReportEntityConverter.convertByFunctionPoint(functionPointEntityList);
        return CollectionUtil.isEmpty(voList) ? new ArrayList<>() : voList;
    }

    @Override
    public TmTestReportEntityDO loadFromDb(String reportCode) {
        TmTestReportEntity entity = tmTestReportMapper.selectByPrimaryKey(reportCode);
        return tmTestReportEntityConverter.convert2DO(entity);
    }

    @Override
    public TmTestReportEntityDO getTmReportByPrimaryKey(String reportCode) {
        return tmTestReportEntityConverter.convert2DO(tmTestReportMapper.selectByPrimaryKey(reportCode));

    }

    @Override
    public void saveTmAccessReport(TmAccessReportAddEvent event) {
        TmTestReportEntity entity = tmTestReportEntityConverter.converter(event);
        entity.preCreate(event);
        tmTestReportMapper.insertSelective(entity);
    }

    @Override
    public void updateTmAccessReport(TmAccessReportEditEvent event) {
        TmTestReportEntity entity = tmTestReportEntityConverter.converter(event);
        entity.preUpdate(event);
        tmTestReportMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void saveTmOnlineSmokeReport(TmOnlineSmokeReportAddEvent event) {
        TmTestReportEntity entity = tmTestReportEntityConverter.converter(event);
        entity.preCreate(event);
        tmTestReportMapper.insertSelective(entity);
    }

    @Override
    public void updateTmOnlineSmokeReport(TmOnlineSmokeReportEditEvent event) {
        TmTestReportEntity entity = tmTestReportEntityConverter.converter(event);
        entity.preUpdate(event);
        tmTestReportMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updatePlanStageStatus(String planCode, TestPlanStageEnum stageEnum) {
        Map<String, Object> map = new HashMap<>();
        map.put(TestPlanStageEnum.SMOKE_TEST.name(), TestPlanStageStatusEnum.COMPLETED);
        TmTestPlanEntity entity = new TmTestPlanEntity();
        if (stageEnum.equals(TestPlanStageEnum.SMOKE_TEST)) {
            map.put(TestPlanStageEnum.FUNCTIONAL_TEST.name(), TestPlanStageStatusEnum.IN_PROGRESS);
            map.put(TestPlanStageEnum.ONLINE_SMOKE_TEST.name(), TestPlanStageStatusEnum.INITIAL);

        } else if (stageEnum.equals(TestPlanStageEnum.FUNCTIONAL_TEST)) {
            map.put(stageEnum.name(), TestPlanStageStatusEnum.COMPLETED);
            map.put(TestPlanStageEnum.ONLINE_SMOKE_TEST.name(), TestPlanStageStatusEnum.IN_PROGRESS);

        } else {
            map.put(TestPlanStageEnum.FUNCTIONAL_TEST.name(), TestPlanStageStatusEnum.COMPLETED);
            map.put(stageEnum.name(), TestPlanStageStatusEnum.COMPLETED);
            entity.setStatus(TestPlanNewStatusEnum.COMPLETED);
        }
        entity.setStageStatus(map);
        entity.setGmtModified(new Date());
        Example example = new Example(TmTestPlanEntity.class);
        example.createCriteria().andEqualTo("code", planCode);
        tmTestPlanMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void editSimpleTestReport(SimpleTestReportEditEvent event) {
        TmTestReportEntity entity = convertor.convert(event);
        entity.preUpdate(event);
        tmTestReportMapper.updateByPrimaryKeySelective(entity);

        // 修改计划中开发、测试人数&&计划提测、计划准出上下午
        updatePlanNum(event);
        mergeAttachments(event.getAttachments(), convertor.convertReportBaseEvent(event));
    }

    private void mergeAttachments(List<AttachmentVO> attachments, ReportBaseEvent event) {
        Example example = new Example(AttachmentEntity.class);
        if (StringUtil.isNotBlank(event.getReportCode())) {
            example.createCriteria().andEqualTo("businessCode", event.getReportCode());
            attachmentMapper.deleteByExample(example);
        }

        List<AttachmentEntity> entityList = convertor.convertAttachments(attachments);
        if (CollectionUtil.isNotEmpty(entityList)) {
            entityList.forEach(entity -> {
                entity.preCreate(event);
                entity.setBusinessCode(event.getReportCode());
                attachmentMapper.insertSelective(entity);
            });
        }

    }

    private void mergeReviewInfo(ReviewInfoDTO reviewInfo, ReportBaseEvent event) {
        Example example = new Example(ReviewInfoEntity.class);
        if (StringUtil.isNotBlank(event.getReportCode())) {
            example.createCriteria().andEqualTo("reportCode", event.getReportCode());
            tmReviewInfoMapper.deleteByExample(example);
        }

        TmReviewInfoEntity entity = convertor.convertReviewInfo(reviewInfo);
        entity.preCreate(event);
        entity.setReportCode(event.getReportCode());
        tmReviewInfoMapper.insertSelective(entity);
    }

    private void mergeReviewRenewals(List<ReviewRenewalVO> reviewRenewals, ReportBaseEvent event) {
        Example example = new Example(ReviewRenewalEntity.class);
        if (StringUtil.isNotBlank(event.getReportCode())) {
            example.createCriteria().andEqualTo("reportCode", event.getReportCode());
            tmReviewRenewalMapper.deleteByExample(example);
        }
        if (CollectionUtil.isEmpty(reviewRenewals)) {
            return;
        }
        List<TmReviewRenewalEntity> entityList = convertor.convertReviewRenwals(reviewRenewals);
        entityList.forEach(entity -> {
            entity.preCreate(event);
            entity.setReportCode(event.getReportCode());
            tmReviewRenewalMapper.insertSelective(entity);
        });
    }

    private void mergeReviewOpinions(List<ReviewOpinionVO> reviewOpinions, ReportBaseEvent event) {
        Example example = new Example(ReviewOpinionEntity.class);
        if (StringUtil.isNotBlank(event.getReportCode())) {
            example.createCriteria().andEqualTo("reportCode", event.getReportCode());
            tmReviewOpinionMapper.deleteByExample(example);
        }
        if (CollectionUtil.isEmpty(reviewOpinions)) {
            return;
        }
        List<TmReviewOpinionEntity> entityList = convertor.convertReviewOpinions(reviewOpinions);
        entityList.forEach(entity -> {
            entity.preCreate(event);
            entity.setReportCode(event.getReportCode());
            tmReviewOpinionMapper.insertSelective(entity);
        });
    }

    @Override
    public void saveTmPermitReport(TmPermitReportAddEvent event) {
        TmTestReportEntity entity = convertor.convert(event);
        entity.preCreate(event);
        tmTestReportMapper.insertSelective(entity);
    }

    @Override
    public void updateTmPermitReport(TmPermitReportEditedEvent event) {
        TmTestReportEntity entity = convertor.convert(event);
        entity.preUpdate(event);
        tmTestReportMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void addSimpleTestReport(SimpleReportAddedEvent event) {
        TmTestReportEntity entity = convertor.convert(event);
        entity.preCreate(event);
        tmTestReportMapper.insertSelective(entity);
        // 修改计划中开发、测试人数&&计划提测、计划准出上下午
        updatePlanNum(event);
        mergeAttachments(event.getAttachments(), convertor.convertReportBaseEvent(event));
    }

    private void updatePlanNum(SimpleReportEvent event) {
        TmTestPlanEntity tmTestPlanEntity = new TmTestPlanEntity();
        tmTestPlanEntity.setDeveloperNum(event.getDeveloperCount());
        tmTestPlanEntity.setTesterNum(event.getTesterCount());
        tmTestPlanEntity.setPermitDatePartition(enumConvert(event.getPlanPresentationDay()));
        tmTestPlanEntity.setAccessDatePartition(enumConvert(event.getPlanApprovalExitDay()));
        Example planExample = new Example(TmTestPlanEntity.class);
        planExample.createCriteria().andEqualTo("code", event.getPlanCode());
        tmTestPlanMapper.updateByExampleSelective(tmTestPlanEntity, planExample);
    }

    private TestPlanDatePartitionEnum enumConvert(String partition) {
        if (StringUtils.isEmpty(partition)) {
            return TestPlanDatePartitionEnum.AM;
        }
        return "18:00:00".equals(partition) ? TestPlanDatePartitionEnum.PM
                : TestPlanDatePartitionEnum.AM;
    }

    public void deleteModuleByReportCode(String reportCode) {
        Example example = new Example(ModuleTestEntity.class);
        example.createCriteria().andEqualTo("reportCode", reportCode);
        moduleTestMapper.deleteByExample(example);
    }

    private void mergeModuleTests(List<TmModuleTestVO> moduleTests, ReportBaseEvent event) {
        if (StringUtil.isNotBlank(event.getReportCode())) {
            deleteModuleByReportCode(event.getReportCode());
        }

        if (CollectionUtil.isNotEmpty(moduleTests)) {
            List<ModuleTestEntity> entityList = convertor.convertModuleTests(moduleTests);
            entityList.forEach(entity -> {
                entity.preCreate(event);
                entity.setReportCode(event.getReportCode());
                moduleTestMapper.insertSelective(entity);
            });
        }
    }

    @Override
    public ReviewInfoEntityDO getReviewInfoUserByCode(String reportCode) {
        Example example = new Example(TmReviewInfoEntity.class);
        example.createCriteria().andEqualTo("reportCode", reportCode).andEqualTo("enable", true);
        return convertor.converter(tmReviewInfoMapper.selectOneByExample(example));
    }

    @Override
    public TmTestReportEntityDO getReportByProductCodeAndUser(String productCode, Long reportUserId) {
        return convertor.converter(tmTestReportMapper.getByProductCodeAndUser(productCode, reportUserId));
    }

    @Override
    public List<ReviewOpinionVO> getReviewOpinionVOS(String code) {
        Example example = new Example(TmReviewOpinionEntity.class);
        example.createCriteria().andEqualTo("reportCode", code).andEqualTo("enable", true);
        List<TmReviewOpinionEntity> tmReviewOpinionEntities = tmReviewOpinionMapper.selectByExample(example);
        return convertor.convertReviewOpinion(tmReviewOpinionEntities);
    }

    @Override
    public List<ReviewRenewalVO> getReviewRenewalVOS(String code) {
        Example example = new Example(ReviewRenewalEntity.class);
        example.createCriteria().andEqualTo("reportCode", code).andEqualTo("enable", true);
        List<TmReviewRenewalEntity> tmReviewRenewalEntities = tmReviewRenewalMapper.selectByExample(example);
        return convertor.convertReviewRenewal(tmReviewRenewalEntities);
    }

    @Override
    public void saveTestReviewReport(ReviewReportAddedEvent event) {
        TmTestReportEntity entity = convertor.convert(event);
        entity.preCreate(event);
        tmTestReportMapper.insertSelective(entity);

        mergeReviewInfo(event.getReviewInfo(), convertor.convertReportBaseEvent(event));
        mergeReviewOpinions(event.getReviewOpinions(), convertor.convertReportBaseEvent(event));
        mergeReviewRenewals(event.getReviewRenewals(), convertor.convertReportBaseEvent(event));
        mergeAttachments(event.getAttachments(), convertor.convertReportBaseEvent(event));
    }

    @Override
    public void updateTestReviewReport(ReviewReportEditedEvent event) {
        TmTestReportEntity entity = convertor.convert(event);
        entity.preUpdate(event);
        tmTestReportMapper.updateByPrimaryKeySelective(entity);

        mergeReviewInfo(event.getReviewInfo(), convertor.convertReportBaseEvent(event));
        mergeReviewOpinions(event.getReviewOpinions(), convertor.convertReportBaseEvent(event));
        mergeReviewRenewals(event.getReviewRenewals(), convertor.convertReportBaseEvent(event));
        mergeAttachments(event.getAttachments(), convertor.convertReportBaseEvent(event));
    }

    @Override
    public void saveExternalTestReport(ExternalReportAddEvent event) {
        TmTestReportEntity entity = convertor.convert(event);
        entity.preCreate(event);
        tmTestReportMapper.insertSelective(entity);

        mergeAttachments(event.getAttachments(), convertor.convertReportBaseEvent(event));
    }

    @Override
    public void updateExternalTestReport(ExternalReportEditEvent event) {
        TmTestReportEntity entity = convertor.convert(event);
        entity.preUpdate(event);
        tmTestReportMapper.updateByPrimaryKeySelective(entity);

        mergeAttachments(event.getAttachments(), convertor.convertReportBaseEvent(event));
    }

    @Override
    public void addMobileTestReport(MobileTestReportAddedEvent event) {
        TmTestReportEntity entity = convertor.convert(event);
        entity.preCreate(event);
        tmTestReportMapper.insertSelective(entity);
        mergeModuleTests(event.getModuleTestVOS(), convertor.convertReportBaseEvent(event));
    }

    @Override
    public void updateMobileTestReport(MobileTestReportEditEvent event) {
        TmTestReportEntity entity = convertor.convert(event);
        entity.preUpdate(event);
        tmTestReportMapper.updateByPrimaryKeySelective(entity);
        mergeModuleTests(event.getModuleTestVOS(), convertor.convertReportBaseEvent(event));
    }

    @Override
    public List<ReportVO> pageQuery(PageReportMqQuery query) {
        return tmTestReportMapper.pageQuery(query);
    }

    @Override
    public List<TmTestReportEntityDO> findSentEmailList(List<String> versionCodeList, List<ReportType> typeList) {
        return tmTestReportEntityConverter.convert2DOList(tmTestReportMapper.findSentEmailList(versionCodeList, typeList));
    }

    @Override
    public List<TmTestReportEntityDO> listByVersionCodesAndTypes(List<String> versionCodes, List<TestReportTypeEnum> reportTypes) {
        if (CollectionUtil.isEmpty(versionCodes) || CollectionUtils.isEmpty(reportTypes)) {
            return new ArrayList<>();
        }
        Example example = new Example(TmTestReportEntity.class);
        example.selectProperties("reportCode", "reportName", "reportType", "versionCode", "versionName", "planCode", "actualPresentationDate", "actualApprovalExitDate", "actualOnlineDate");
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andIn("versionCode", versionCodes)
                .andIn("reportType", reportTypes);
        List<TmTestReportEntity> entities = tmTestReportMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entities) ? new ArrayList<>() : convertor.convert2DOS(entities);
    }

    @Override
    public void updateVersionInfo(TmTestReportEntityDO reportEntity, String versionName, User transactor) {
        if (StringUtil.isBlank(versionName) || null == transactor || null == reportEntity) {
            return;
        }
        Example example = new Example(TmTestReportEntity.class);
        example.createCriteria().andEqualTo("reportCode", reportEntity.getReportCode());

        TmTestReportEntity toUpdate = new TmTestReportEntity();
        toUpdate.setReportName(String.format("%s%s", versionName, reportEntity.getReportType().getValue()));
        toUpdate.setVersionName(versionName);
        toUpdate.setModifier(transactor.getUserName());
        toUpdate.setModifierId(transactor.getUserId());
        toUpdate.setGmtModified(new Date());
        tmTestReportMapper.updateByExampleSelective(toUpdate, example);
    }
}
