package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TestcaseStepEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseStepEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TestcaseStepEntityConverter {

    List<TestcaseStepVO> converter(List<TestcaseStepEntity> list);

    TestcaseStepEntityDO convert(TestcaseStepEntity entity);

    List<TestcaseStepEntityDO> convertList(List<TestcaseStepEntity> entityList);

    TestcaseStepEntity convert2(TestcaseStepEntityDO entityDO);

    List<TestcaseStepEntity> convert2List(List<TestcaseStepEntityDO> entityDOList);

    TestcaseStepEntity convertVO(TestcaseStepVO vo);

    List<TestcaseStepEntity> convertVOList(List<TestcaseStepVO> list);
}
