package com.zto.devops.qc.infrastructure.gateway.apollo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.zto.devops.qc.client.enums.testmanager.config.QcConfigEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.JmeterConfigVO;
import com.zto.devops.qc.client.model.testmanager.config.entity.ProjectProfileVO;
import com.zto.devops.qc.client.model.testmanager.config.entity.QcConfigVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.AmazonS3ConfigVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageConfigVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.GitLabConfigVO;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.infrastructure.config.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class QcConfigBasicServiceImpl implements QcConfigBasicService {

    @Autowired
    private QcConfig qcConfig;

    @Autowired
    private CoverageConfig coverageConfig;

    @Autowired
    private JmeterConfig jmeterConfig;

    @Autowired
    private QcGitLabConfig gitLabConfig;

    @Autowired
    private AmazonS3Config amazonS3Config;

    @Autowired
    private JenkinsCallbackConfig jenkinsCallbackConfig;

    @Autowired
    private ProjectProfile messageProfile;

    @ApolloConfig()
    private Config config;

    @Override
    public QcConfigVO getConfig(QcConfigEnum qcConfigEnum) {
        QcConfigVO configVO = new QcConfigVO();
        if (QcConfigEnum.UPLOAD_FILE.equals(qcConfigEnum)) {
            configVO.setUploadFileNum(Integer.valueOf(qcConfig.getUploadFileNum()));
            configVO.setUploadFileSize(Long.valueOf(qcConfig.getUploadFileSize()));
        }
        return configVO;
    }

    @Override
    public CoverageConfigVO getCoverageConfig() {
        CoverageConfigVO configVO = new CoverageConfigVO();
        configVO.setZkeApikey(coverageConfig.getZkeApikey());
        configVO.setZkePodIpUrl(coverageConfig.getZkePodIpUrl());
        configVO.setCodeCoverageReason(coverageConfig.getCodeCoverageReason());
        configVO.setProducts(coverageConfig.getProducts());
        configVO.setModules(coverageConfig.getModules());
        configVO.setExcludeApps(coverageConfig.getExcludeApps().equals("\"\"") ? null : coverageConfig.getExcludeApps());
        configVO.setFilterUnComparedProducts(coverageConfig.getFilterUnComparedProducts().equals("\"\"") ? null : coverageConfig.getFilterUnComparedProducts());
        configVO.setNewReportUrlProducts(coverageConfig.getNewReportUrlProducts().equals("\"\"") ? null : coverageConfig.getNewReportUrlProducts());
        configVO.setReportUrl(coverageConfig.getReportUrl().equals("\"\"") ? null : coverageConfig.getReportUrl());
        configVO.setDocAddress(coverageConfig.getDocAddress().equals("\"\"") ? null : coverageConfig.getDocAddress());
        return configVO;
    }

    @Override
    public GitLabConfigVO getGitLabConfig() {
        GitLabConfigVO configVO = new GitLabConfigVO();
        configVO.setUname(gitLabConfig.getUname());
        configVO.setPass(gitLabConfig.getPass());
        return configVO;
    }

    /**
     * 内网域名
     * @return
     */
    @Override
    public AmazonS3ConfigVO getAmazonS3ConfigIntranet() {
        AmazonS3ConfigVO vo = new AmazonS3ConfigVO();
        vo.setAccessKey(amazonS3Config.getAccessKey());
        vo.setEndPoint(amazonS3Config.getIntranetDomain());
        vo.setSecretKey(amazonS3Config.getSecretKey());
        return vo;
    }

    /**
     * 公网域名
     * @return
     */
    @Override
    public AmazonS3ConfigVO getAmazonS3Config() {
        AmazonS3ConfigVO vo = new AmazonS3ConfigVO();
        vo.setAccessKey(amazonS3Config.getAccessKey());
        vo.setEndPoint(amazonS3Config.getEndPoint());
        vo.setSecretKey(amazonS3Config.getSecretKey());
        return vo;
    }

    @Override
    public ProjectProfileVO getProjectProfileConfig() {
        ProjectProfileVO vo = new ProjectProfileVO();
        vo.setFrontUrl(messageProfile.getFrontUrl());
        vo.setIssueList(messageProfile.getIssueList());
        vo.setProductList(messageProfile.getProductList());
        vo.setSyncQAXDeptNos(messageProfile.getSyncQAXDeptNos());
        vo.setIssueMessageUrl(messageProfile.getIssueMessageUrl());
        return vo;
    }

    @Override
    public String getCallbackBucketName() {
        return jenkinsCallbackConfig.getBucketName();
    }

    @Override
    public Long getCheckFileSize() {
        return config.getLongProperty("qc.check.file.size", 10240L);
    }

    @Override
    public String getHeartDubboTag() {
        return config.getProperty("heart_dubbo_tag", "");
    }

    @Override
    public Integer getMediumSize() {
        return config.getIntProperty("mediumSize", 1000);
    }

    @Override
    public Integer getBigSize() {
        return config.getIntProperty("bigSize", 2000);
    }

    @Override
    public Integer getMinSize() {
        return config.getIntProperty("minSize", 500);
    }

    @Override
    public Integer getBusinessCount() {
        return config.getIntProperty("qc.business.count", 500);
    }

    @Override
    public Integer getProductTagMax() {
        return config.getIntProperty("product.tag.max", 20);
    }

    @Override
    public Integer getTestcaseTagMax() {
        return config.getIntProperty("testcase.tag.max", 100);
    }

    @Override
    public Integer getTestcaseImportCount() {
        return config.getIntProperty("qc.testcase.import.count", 1000);
    }

    @Override
    public QcConfigVO getQcConfig() {
        QcConfigVO configVO = new QcConfigVO();
        configVO.setUploadFileNum(Integer.valueOf(qcConfig.getUploadFileNum()));
        configVO.setUploadFileSize(Long.valueOf(qcConfig.getUploadFileSize()));
        configVO.setEnv(qcConfig.getEnv());
        return configVO;
    }

    @Override
    public Integer getJobAutoTestAbortDuration() {
        return config.getIntProperty("qc.job.autoTestAbort.duration", 180);
    }

    @Override
    public Integer getJobAnalysisAutomaticRecordAbortDuration() {
        return config.getIntProperty("qc.job.analysisAutomaticRecordAbort.duration", 3600);
    }

    @Override
    public String getKnowledgeBaseUrl() {
        return config.getProperty("knowledgeBaseUrl", "");
    }

    @Override
    public String getDingDingKnowledgeBaseManagerUserId() {
        return config.getProperty("knowledgeBaseManagerUserId", "");
    }

    @Override
    public String getAutoJmxBucketName() {
        return config.getProperty("jenkins.autojmx.bucket.name", "");
    }

    @Override
    public JmeterConfigVO getJmeterConfig() {
        JmeterConfigVO jmeterConfigVO = new JmeterConfigVO();
        List<String> stringList = Arrays.asList(jmeterConfig.getUniqueNodes().split(";"));
        jmeterConfigVO.setUniqueNodeList(stringList);
        List<String> controllerStringList = Arrays.asList(jmeterConfig.getControllerNames().split(";"));
        jmeterConfigVO.setControllerList(controllerStringList);
        return jmeterConfigVO;
    }

    @Override
    public Integer getJmeterDebugMaxCount() {
        return jmeterConfig.getDebugMaxCount();
    }

    @Override
    public JSONObject getJmeterVariable() {
        String variable = jmeterConfig.getVariable();
        return JSON.parseObject(variable);
    }

    @Override
    public String getDubboZKTestAddress() {
        return jmeterConfig.getDubboZKTestAddress();
    }

    @Override
    public String getZMSZKTestAddress() {
        return jmeterConfig.getZmsZKTestAddress();
    }

    @Override
    public String getApiMockBaseUrl() {
        return config.getProperty("apitest.mock.baseUrl", "");
    }

    @Override
    public String getDomainConfig() {
        return config.getProperty("qc.domain.config", "");
    }

    @Override
    public String getCoverageInnerUrl() {
        return config.getProperty("qc.coverage.config.innerip", "");
    }

    @Override
    public String getJmeterInitMethod() {
        return config.getProperty("qc.jmeter.init.method", "");
    }

    @Override
    public String getDebugWorkspace() {
        return jmeterConfig.getDebugWorkspace();
    }

    @Override
    public Boolean getApiGatewayRule() {
        return config.getBooleanProperty("api.gateway.rule", false);
    }

    @Override
    public String getSceneJmxUploadPermission() {
        return config.getProperty("qc.scene.jmx.upload.permission", "");
    }

    @Override
    public String getSceneInputParameterNetwork() {
        return config.getProperty("qc.scene.inputParameter.network", "");
    }

    @Override
    public String getSceneInputParameterPersonnel() {
        return config.getProperty("qc.scene.inputParameter.personnel", "");
    }

    @Override
    public String getSceneInputParameterDepartment() {
        return config.getProperty("qc.scene.inputParameter.department", "");
    }

    @Override
    public String getSceneInputParameterProvince() {
        return config.getProperty("qc.scene.inputParameter.province", "");
    }

    @Override
    public Integer getClearTestcaseDuration() {
        return config.getIntProperty("qc.job.testcase.clear.duration", 100);
    }

    @Override
    public String getDelayAcceptAuditSkipUrl() {
        return config.getProperty("qc.audit.delay.accept.skipUrl", "");
    }

    @Override
    public String getDelayAcceptAuditDoneSkipUrl() {
        return config.getProperty("qc.audit.delay.accept.done.skipUrl", "");
    }

    @Override
    public String getDelayAcceptAuditTitle() {
        return config.getProperty("qc.audit.delay.accept.title", "");
    }

    @Override
    public String getDelayAcceptAuditBizType() {
        return config.getProperty("qc.audit.delay.accept.bizType", "");
    }

    @Override
    public String getDelayAcceptAuditBizName() {
        return config.getProperty("qc.audit.delay.accept.bizName", "");
    }

    @Override
    public String getDelayAcceptAuditSubmitterCode() {
        return config.getProperty("qc.audit.delay.accept.submitterCode", "");
    }

    @Override
    public String getDelayAcceptAuditSubmitterName() {
        return config.getProperty("qc.audit.delay.accept.submitterName", "");
    }

    @Override
    public String getChaosException() {
        return config.getProperty("qc.agent.chaos.exception", "");
    }

    @Override
    public List<String> getEnableCoverageNamespace() {
        try {
            String envStr = config.getProperty("qc.coverage.config.namespaceList", "DEFAULT_EMPTY,BASE,BASE_FAT,BASE_TEST");
            if (envStr == null || envStr.trim().isEmpty()) {
                return Collections.emptyList();
            }
            return Arrays.stream(envStr.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取可生成覆盖率环境异常，error：{}", e.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public Integer getDeleteCount() {
        return config.getIntProperty("qc.coverage.config.deleteCount", 20000);
    }

}
