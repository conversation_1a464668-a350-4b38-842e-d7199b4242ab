package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.AutomaticPreExecutionEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticSchedulerEntityDO;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.AutomaticSchedulerVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticSchedulerAddedEvent;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticSchedulerEditedEvent;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticPreExecutionEntity;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSchedulerEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AutomaticSchedulerEntityConverter {

    AutomaticSchedulerEntity covert2Entity(AutomaticSchedulerEntityDO entityDO);

    AutomaticSchedulerEntityDO converter(AutomaticSchedulerEntity entity);

    AutomaticSchedulerEntity converter(AutomaticSchedulerAddedEvent event);

    AutomaticSchedulerEntity converter(AutomaticSchedulerEditedEvent event);

    AutomaticSchedulerVO domainConverter(AutomaticSchedulerEntity entity);

    AutomaticPreExecutionEntityDO converter(AutomaticPreExecutionEntity entity);

    List<AutomaticPreExecutionEntityDO> converterList(List<AutomaticPreExecutionEntity> entityList);
}
