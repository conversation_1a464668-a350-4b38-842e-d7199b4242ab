package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.testmanager.coverage.query.CoveragePublishQuery;
import com.zto.devops.qc.infrastructure.dao.entity.CoveragePublishEntity;
import com.zto.devops.qc.infrastructure.dao.entity.CoverageRecordGenerateEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/9/16 17:16
 */
public interface CoveragePublishMapper extends Mapper<CoveragePublishEntity> {

    List<CoveragePublishEntity> getLatestPublishRecord(CoveragePublishQuery query);

    CoveragePublishEntity getLatestPublishRecordByEntity(CoveragePublishEntity entity);

    CoveragePublishEntity getLatestProdPublish(CoveragePublishEntity entity);

    CoveragePublishEntity getFirstBranchPublish(CoveragePublishEntity entity);

    List<CoveragePublishEntity> getMiddlePublishEntity(CoverageRecordGenerateEntity entity);

    List<CoveragePublishEntity> getLatestPublishRecordByProductCode(CoveragePublishQuery query);

    List<CoveragePublishEntity> getPublishRecordByCommitId(CoveragePublishEntity entity);

    List<CoveragePublishEntity> getPublishRecordLastDay(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
