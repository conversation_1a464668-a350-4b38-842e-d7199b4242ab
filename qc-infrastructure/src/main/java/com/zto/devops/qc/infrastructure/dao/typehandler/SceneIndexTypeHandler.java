package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.qc.client.enums.testmanager.apitest.SceneIndexTypeEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedJdbcTypes(value = JdbcType.INTEGER, includeNullJdbcType = true)
public class SceneIndexTypeHandler extends BaseTypeHandler<SceneIndexTypeEnum> {


    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, SceneIndexTypeEnum sceneIndexTypeEnum, JdbcType jdbcType) throws SQLException {
        if (null == sceneIndexTypeEnum) {
            preparedStatement.setInt(i, 1);
        } else {
            preparedStatement.setInt(i, sceneIndexTypeEnum.getCode());
        }
    }

    @Override
    public SceneIndexTypeEnum getNullableResult(ResultSet resultSet, String s) throws SQLException {
        int result = resultSet.getInt(s);
        return SceneIndexTypeEnum.codeOf(result);
    }

    @Override
    public SceneIndexTypeEnum getNullableResult(ResultSet resultSet, int i) throws SQLException {
        int result = resultSet.getInt(i);
        return SceneIndexTypeEnum.codeOf(result);
    }

    @Override
    public SceneIndexTypeEnum getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        int result = callableStatement.getInt(i);
        return SceneIndexTypeEnum.codeOf(result);
    }
}
