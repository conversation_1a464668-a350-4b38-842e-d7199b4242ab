package com.zto.devops.qc.infrastructure.gateway.jacoco;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.client.constants.InterfaceCoverageConstant;
import com.zto.devops.qc.client.enums.rpc.BranchEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.MethodTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordErrorMsgEnum;
import com.zto.devops.qc.client.model.dto.ApiTestEntityDO;
import com.zto.devops.qc.client.model.dto.InterfaceCoverageEntityDO;
import com.zto.devops.qc.client.model.dto.MethodDependenceEntityDO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoveragePublishVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordGenerateVO;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.gitlab.GitlabService;
import com.zto.devops.qc.domain.gateway.jacoco.JacocoService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.ApiTestRepository;
import com.zto.devops.qc.domain.gateway.repository.InterfaceCoverageRepository;
import com.zto.devops.qc.domain.gateway.repository.MethodDependenceRepository;
import com.zto.devops.qc.domain.model.coverage.CommitInfo;
import com.zto.devops.qc.domain.model.coverage.Metrics;
import com.zto.devops.qc.domain.service.InterfaceCoverageDomainService;
import com.zto.devops.qc.domain.util.DateUtil;
import com.zto.devops.qc.infrastructure.converter.CoverageConverter;
import com.zto.devops.qc.infrastructure.gateway.util.MergeDumpUtils;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.RepositoryApi;
import org.gitlab4j.api.models.Branch;
import org.jacoco.core.analysis.Analyzer;
import org.jacoco.core.analysis.CoverageBuilder;
import org.jacoco.core.analysis.IBundleCoverage;
import org.jacoco.core.data.ExecutionDataWriter;
import org.jacoco.core.internal.diff.ClassInfo;
import org.jacoco.core.internal.diff.GitAdapter;
import org.jacoco.core.internal.diff.MethodInfo;
import org.jacoco.core.runtime.RemoteControlReader;
import org.jacoco.core.runtime.RemoteControlWriter;
import org.jacoco.core.tools.ExecFileLoader;
import org.jacoco.report.DirectorySourceFileLocator;
import org.jacoco.report.FileMultiReportOutput;
import org.jacoco.report.IReportVisitor;
import org.jacoco.report.MultiSourceFileLocator;
import org.jacoco.report.html.HTMLFormatter;
import org.jacoco.report.xml.XMLFormatter;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.rmi.ServerException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;


@Slf4j
@Service
public class JacocoServiceImpl implements JacocoService {

    @Autowired
    private RepositoryApi repositoryApi;
    @Autowired
    private GitlabService gitlabService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private CoverageConverter converter;
    @Autowired
    private InterfaceCoverageRepository interfaceCoverageRepository;
    @Autowired
    private MethodDependenceRepository methodDependenceRepository;
    @Autowired
    private ApiTestRepository apiTestRepository;
    @Autowired
    private QcConfigBasicService configBasicService;
    @Autowired
    private InterfaceCoverageDomainService interfaceCoverageDomainService;

    private ExecFileLoader execFileLoader;
    private static final String SOURCE_PATH = "sources";
    private static final String CLASS_PATH = "classes/";

    public JacocoServiceImpl(RepositoryApi repositoryApi, GitlabService gitlabService, RedisService redisService,
                             CoverageConverter converter, InterfaceCoverageRepository interfaceCoverageRepository,
                             MethodDependenceRepository methodDependenceRepository, ApiTestRepository apiTestRepository,
                             QcConfigBasicService configBasicService, InterfaceCoverageDomainService interfaceCoverageDomainService) {
        this.repositoryApi = repositoryApi;
        this.gitlabService = gitlabService;
        this.redisService = redisService;
        this.converter = converter;
        this.interfaceCoverageRepository = interfaceCoverageRepository;
        this.methodDependenceRepository = methodDependenceRepository;
        this.apiTestRepository = apiTestRepository;
        this.configBasicService = configBasicService;
        this.interfaceCoverageDomainService = interfaceCoverageDomainService;
    }

    @Override
    public void socketConnect(FileOutputStream localFile, CoveragePublishVO entity, String ip) {
        Socket socket = new Socket();
        try {
            ExecutionDataWriter localWriter = new ExecutionDataWriter(localFile);
            log.info("开始连接socket.版本号：{}，ip:{}", entity.getVersionName(), ip);
            socket.connect(new InetSocketAddress(InetAddress.getByName(ip), entity.getPort()), 5000);
            log.info("开始writer.版本号：{}，ip:{}", entity.getVersionName(), ip);
            RemoteControlWriter writer = new RemoteControlWriter(socket.getOutputStream());
            log.info("开始reader.版本号：{}，ip:{}", entity.getVersionName(), ip);
            RemoteControlReader reader = new RemoteControlReader(socket.getInputStream());
            log.info("开始setSessionInfoVisitor.版本号：{}，ip:{}", entity.getVersionName(), ip);
            reader.setSessionInfoVisitor(localWriter);
            log.info("开始setExecutionDataVisitor.版本号：{}，ip:{}", entity.getVersionName(), ip);
            reader.setExecutionDataVisitor(localWriter);
            log.info("开始visitDumpCommand.版本号：{}，ip:{}", entity.getVersionName(), ip);
            writer.visitDumpCommand(true, false);
            log.info("开始read().版本号：{}，ip:{}", entity.getVersionName(), ip);
            if (!reader.read()) {
                throw new Exception("Socket closed unexpectedly.");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (socket != null) {
                log.info("开始关闭socket.版本号：{}，ip:{}", entity.getVersionName(), ip);
                try {
                    socket.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            if (localFile != null) {
                log.info("开始关闭localFile.版本号：{}，ip:{}", entity.getVersionName(), ip);
                try {
                    localFile.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    @Override
    public void executeMerge(String execPath) {
        MergeDumpUtils dump = new MergeDumpUtils(execPath);
        dump.executeMerge();
    }

    @Override
    public Map<String, Object> create(String sourcePath, final CoverageRecordGenerateVO entity, String localReportPath, String localPath, Logger logger, String uname, String pass, String products) throws IOException {
        final JacocoServiceImpl jacocoService = new JacocoServiceImpl(repositoryApi, gitlabService, redisService, converter,
                interfaceCoverageRepository, methodDependenceRepository, apiTestRepository, configBasicService, interfaceCoverageDomainService);
        return jacocoService.generator(entity, localReportPath, localPath, logger, uname, pass, products, sourcePath);
    }

    @Override
    public void setLocalClassPath(CoverageRecordGenerateVO entity, String classPath) {
        File classFile = new File(classPath + "BOOT-INF");
        if (classFile.exists()) {
            entity.setLocalClassesPath(classPath + "BOOT-INF/classes/com/");
            return;
        } else {
            File files = new File(classPath + "WEB-INF");
            if (files.exists()) {
                entity.setLocalClassesPath(classPath + "WEB-INF/classes/");
                return;
            } else {
                File file1 = new File(classPath + "com");
                if (file1.exists()) {
                    entity.setLocalClassesPath(classPath + "com/");
                    return;
                }
            }
        }
        log.info("setLocalClassPath 为空");
    }

    @Override
    public boolean isContainText(String recordUrl, String text) throws IOException {
        Document doc = Jsoup.connect(recordUrl).get();
        if (null != doc && doc.getElementsContainingOwnText(text).size() == 0) {
            return true;
        }
        return false;
    }

    @Override
    public String getValueByReport(String recordUrl, String key) throws IOException {
        Document doc = Jsoup.connect(recordUrl).get();
        if (null == doc) {
            return "";
        }
        return getValueByReport(doc, key);
    }

    private String getValueByReport(Document doc, String key) {
        Elements foot = doc.select("tfoot");
        Elements tr = foot.select("tr");
        Element td = tr.select(key).first();
        String text = td.text();
        if (key.equals("td.ctr2")) {
            return text.substring(0, text.length() - 1);
        }
        if (key.equals("td.bar")) {
            return text.replaceAll(" ", "").replaceAll(",", "");
        }
        return "";
    }

    private Map<String, Object> generator(final CoverageRecordGenerateVO entity, String localReportPath, String localPath,
                                          Logger logger, String uname, String pass, String products, String sourcePath) throws IOException {
        loadExecFile(localPath, entity);
        final IBundleCoverage bundleCoverage = analyzeStructure(entity, localPath, logger, uname, pass, products, sourcePath);
        Map<String, Object> map = createReport(bundleCoverage, entity, localReportPath, localPath);
        return map;
    }

    private void loadExecFile(String path, final CoverageRecordGenerateVO entity) throws IOException {
        execFileLoader = new ExecFileLoader();
        path = path + "/exec";
        execFileLoader.load(new File(path, entity.getExecName()));
    }

    private IBundleCoverage analyzeStructure(final CoverageRecordGenerateVO entity, String path, Logger logger, String uname,
                                             String pass, String products, String sourcePath) throws IOException {
        logger.info("开始分析。versionCode = " + entity.getVersionCode() + ", appId = " + entity.getAppId() + ", diffType = " + entity.getDiffType());
        GitAdapter.setCredentialsProvider(uname, pass);
        CoverageBuilder coverageBuilder;
        if (DiffTypeEnum.FULL == entity.getDiffType()) {
            logger.info("生成全量覆盖率");
            coverageBuilder = new CoverageBuilder();
        } else {
            logger.info("生成增量覆盖率");
            if (StringUtil.isEmpty(path + SOURCE_PATH)
                    || StringUtil.isEmpty(entity.getBranchName())
                    || StringUtil.isEmpty(entity.getCommitId())
                    || StringUtil.isEmpty(entity.getBasicCommitId())) {
                throw new ServiceException("本地GIT路径,分支,当前CommitId,基准CommitId不能为空。");
            } else if (entity.getCommitId().equals(entity.getBasicCommitId())) {
                throw new ServiceException(RecordErrorMsgEnum.COMMIT_ID_IS_IDENTICAL.getValue());
            }
            if (StringUtil.isNotEmpty(products) && Arrays.stream(products.split(",")).anyMatch(obj -> obj.equals(entity.getProductCode()))) {
                if (entity.getGitProjectId() != 0) {
                    getBasicCommitId(entity, repositoryApi);
                }
            }
            coverageBuilder = new CoverageBuilder(path + SOURCE_PATH, entity.getBranchName(), entity.getCommitId(), entity.getBasicCommitId(), entity.getLastCommitId(),
                    JSONObject.parseObject(JSONObject.toJSONString(entity.getReportDto())));
            if (CollectionUtil.isEmpty(CoverageBuilder.classInfos)) {
                logger.warning("无代码实现类文件变更，请确认代码变更范围。");
                throw new ServiceException(RecordErrorMsgEnum.NO_IMPLEMENTATION_CLASS.getValue());
            }
            try {
                setCommitInfoCache(entity, sourcePath);
            } catch (ServiceException e) {
                logger.info("设置覆盖率报告类增量数据异常，请联系值班人员");
                throw new ServerException("设置覆盖率报告类增量数据异常，请联系值班人员");
            }
            try {
                // 第一次部署主动生成覆盖率报告，根据方法变更情况推算接口覆盖率
                inferredAffectedInterface(entity);
            } catch (Exception e) {
                log.error("根据方法变更情况推算影响接口范围异常", e);
            }
        }

        final Analyzer analyzer = new Analyzer(execFileLoader.getExecutionDataStore(), coverageBuilder);
        if (StringUtil.isEmpty(entity.getLocalClassesPath())) {
            setLocalClassPath(entity, path + CLASS_PATH);
        }
        if (StringUtil.isEmpty(entity.getLocalClassesPath())) {
            throw new ServiceException("Class文件目录为空。请重新部署后重新生成覆盖报告。如还有问题请联系值班人员。");
        }
        File classesFile = new File(entity.getLocalClassesPath());
        if (classesFile.isDirectory()) {
            String[] files = classesFile.list();
            if (files.length == 0) {
                throw new ServiceException("Class文件目录为空。请重新部署后重新生成覆盖率报告。如还有问题请联系值班人员。");
            }
        }
        analyzer.analyzeAll(new File(entity.getLocalClassesPath(), ""), JSONObject.parseObject(JSONObject.toJSONString(entity.getReportDto())));
        return coverageBuilder.getBundle(new File(path).getName());
    }

    private void setCommitInfoCache(CoverageRecordGenerateVO entity, String sourcePath) {
        String prefixKey = entity.getAppId() + ":" + entity.getVersionCode() + ":" + entity.getCommitId();
        log.info("setCommitInfoCache start. key : {}", prefixKey);
        if (redisService.hasKey(prefixKey)) {
            log.info("setCommitInfoCache terminal. key {} is exist", prefixKey);
            return;
        }
        if (CollectionUtils.isEmpty(CoverageBuilder.classInfos) || CoverageBuilder.classInfos.size() == 0) {
            log.info("setCommitInfoCache terminal. CoverageBuilder.classInfos is empty. key : {}", prefixKey);
            return;
        }
        for (ClassInfo classInfo : CoverageBuilder.classInfos) {
            com.zto.devops.qc.domain.model.coverage.ClassInfo ci = converter.converterClassInfo(classInfo);
            List<CommitInfo> commitInfos = gitlabService.getLastCommitInfo(classInfo.getPackages() + "." + classInfo.getClassName(), sourcePath);
            if (CollectionUtil.isNotEmpty(commitInfos)) {
                ci.setCommitInfos(commitInfos);
            }
            String key = prefixKey + ":" + classInfo.getPackages() + "." + classInfo.getClassName();
            redisService.setKey(key, JSONObject.toJSONString(ci), 30, TimeUnit.DAYS);
            log.info("setCommitInfoCache_redis_key_{}_success", key);
        }
        redisService.setKey(prefixKey, "", 30, TimeUnit.DAYS);
        log.info("setCommitInfoCache end. key : {}", prefixKey);
    }

    /**
     * 根据方法变更情况推算影响接口范围
     *
     * @param vo
     */
    private void inferredAffectedInterface(CoverageRecordGenerateVO vo) {
        long start = System.currentTimeMillis();
        Integer interfaceCoverageCount = interfaceCoverageRepository.queryInterfaceCoverageCountByCommitId(vo.getCommitId());
        // 已存在当前commitId数据，不重复处理
        if (interfaceCoverageCount > 0) {
            return;
        }
        List<InterfaceCoverageEntityDO> saveEntityList = new ArrayList<>();
        Map<String, Set<Metrics>> noCallsDataMap = new HashMap<>();
        for (ClassInfo classInfo : CoverageBuilder.classInfos) {
            for (MethodInfo methodInfo : classInfo.getMethodInfos()) {
                List<String> entryCodeList = queryAffectedInterfaceEntity(vo, classInfo, methodInfo);
                if (CollectionUtil.isEmpty(entryCodeList)) {
                    log.info("未查询到影响接口。方法 ：{}.{}#{}{}", classInfo.getPackages(), classInfo.getClassName(), methodInfo.getMethodName(), methodInfo.getParameterStr());
                    continue;
                }
                setSaveEntityList(vo, entryCodeList, classInfo, methodInfo, saveEntityList, noCallsDataMap);
            }
        }
        if (CollectionUtil.isEmpty(saveEntityList)) {
            return;
        }
        String key = InterfaceCoverageConstant.INTERFACE_COVERAGE_PREFIX + "_" + vo.getVersionCode() + "_" + vo.getAppId();
        if (redisService.hasKey(key)) {
            resetInterfaceCoveredStatus(saveEntityList, redisService.getKey(key));
            interfaceCoverageRepository.deleteByCommitId(redisService.getKey(key));
        }
        interfaceCoverageRepository.batchSave(saveEntityList);
        if (MapUtils.isNotEmpty(noCallsDataMap)) {
            interfaceCoverageDomainService.queryZcatAndSave(noCallsDataMap);
        }
        redisService.setKey(key, vo.getCommitId(), 30, TimeUnit.DAYS);
        log.info("根据方法变更情况推算接口覆盖率耗时：[{}]ms", System.currentTimeMillis() - start);
    }


    /**
     * 已经被覆盖过，且所有方法没有变更过的接口，继承原接口覆盖率状态
     *
     * @param saveEntityList
     * @param lastCommitId
     */
    private void resetInterfaceCoveredStatus(List<InterfaceCoverageEntityDO> saveEntityList, String lastCommitId) {
        if (CollectionUtil.isEmpty(saveEntityList)) {
            return;
        }
        Map<String, List<InterfaceCoverageEntityDO>> coveredMap = new HashMap<>();
        if (StringUtil.isNotEmpty(lastCommitId)) {
            List<InterfaceCoverageEntityDO> coveredEntityList = interfaceCoverageRepository.queryInterfaceCoverageListByCommitId(lastCommitId);
            for (InterfaceCoverageEntityDO coveredEntity : coveredEntityList) {
                String key = coveredEntity.getInterfaceFullClassName() + "#" + coveredEntity.getInterfaceMethodName() + coveredEntity.getInterfaceMethodDesc();
                coveredMap.computeIfAbsent(key, k -> new ArrayList<>()).add(coveredEntity);
            }
        }
        Map<String, List<InterfaceCoverageEntityDO>> toSaveMap = new HashMap<>();
        for (InterfaceCoverageEntityDO saveEntity : saveEntityList) {
            String key = saveEntity.getInterfaceFullClassName() + "#" + saveEntity.getInterfaceMethodName() + saveEntity.getInterfaceMethodDesc();
            toSaveMap.computeIfAbsent(key, k -> new ArrayList<>()).add(saveEntity);
        }
        if (MapUtils.isNotEmpty(coveredMap)) {
            compareMaps(coveredMap, toSaveMap);
        }
    }

    private static void compareMaps(Map<String, List<InterfaceCoverageEntityDO>> coveredMap,
                                    Map<String, List<InterfaceCoverageEntityDO>> toSaveMap) {
        for (String key : coveredMap.keySet()) {
            if (!toSaveMap.containsKey(key)) {
                continue;
            }

            List<InterfaceCoverageEntityDO> coveredList = coveredMap.get(key);
            List<InterfaceCoverageEntityDO> toSaveList = toSaveMap.get(key);
            if (coveredList.equals(toSaveList)) {
                for (InterfaceCoverageEntityDO entityDO : toSaveList) {
                    entityDO.setIsCovered(1);
                }
            }
        }
    }

    /**
     * 查询受影响接口
     *
     * @param vo
     * @param classInfo
     * @param methodInfo
     * @return
     */
    private List<String> queryAffectedInterfaceEntity(CoverageRecordGenerateVO vo, ClassInfo classInfo, MethodInfo methodInfo) {
        MethodDependenceEntityDO queryDO = new MethodDependenceEntityDO();
        queryDO.setAppId(vo.getAppId());
        queryDO.setVersionCode(vo.getVersionCode());
        queryDO.setCommitId(vo.getCommitId());
        queryDO.setFullClassName(classInfo.getPackages() + "." + classInfo.getClassName());
        queryDO.setMethodName(methodInfo.getMethodName());
        queryDO.setMethodParameterStr(methodInfo.getParameterStr());
        return methodDependenceRepository.queryAffectedEntryCodeList(queryDO);
    }

    private void setSaveEntityList(CoverageRecordGenerateVO vo, List<String> entryCodeList, ClassInfo classInfo,
                                                        MethodInfo methodInfo, List<InterfaceCoverageEntityDO> saveEntityList,
                                                        Map<String, Set<Metrics>> noCallsDataMap) {
        List<MethodDependenceEntityDO> interfaceEntityList = methodDependenceRepository.queryAffectedInterfaceEntity(entryCodeList);
        String queryDate = DateUtil.getYesterday(new Date());
        for (MethodDependenceEntityDO interfaceEntity : interfaceEntityList) {
            InterfaceCoverageEntityDO entity = new InterfaceCoverageEntityDO();
            entity.setInterfaceCoverageCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
            entity.setAppId(vo.getAppId());
            entity.setVersionCode(vo.getVersionCode());
            entity.setVersionName(vo.getVersionName());
            entity.setCommitId(vo.getCommitId());
            entity.setInterfaceFullClassName(interfaceEntity.getFullClassName());
            entity.setInterfaceMethodName(interfaceEntity.getMethodName());
            entity.setInterfaceMethodDesc(interfaceEntity.getMethodDesc());
            entity.setInterfaceMethodAnnotation(interfaceEntity.getMethodAnnotation());
            entity.setInterfaceMethodType(interfaceEntity.getMethodType());
            entity.setInterfaceAlias(interfaceEntity.getClassName(interfaceEntity.getFullClassName()) + "#" + interfaceEntity.getMethodName());
            entity.setZcatMetricKey(interfaceEntity.getZcatMetricKey());
            entity.setInterfaceDocAddress(setInterfaceDocAddress(vo, interfaceEntity));
            String countKey = InterfaceCoverageConstant.INTERFACE_CALLS_COUNT + "_" + vo.getAppId() + "_" + interfaceEntity.getZcatMetricKey() + "_" + queryDate;
            String failKey = InterfaceCoverageConstant.INTERFACE_CALLS_FAIL + "_" + vo.getAppId() + "_" + interfaceEntity.getZcatMetricKey() + "_" + queryDate;
            if (redisService.hasKey(countKey) && redisService.hasKey(failKey)) {
                entity.setInterfaceCallNumber(Integer.valueOf(redisService.getKey(countKey)));
                entity.setInterfaceErrorNumber(Integer.valueOf(redisService.getKey(failKey)));
            } else {
                Metrics metrics = new Metrics(interfaceCoverageDomainService.getGroup(interfaceEntity.getMethodType()), interfaceEntity.getZcatMetricKey());
                noCallsDataMap.computeIfAbsent(vo.getAppId(), k -> new HashSet<>()).add(metrics);
            }
            entity.setModifyClassName(classInfo.getPackages() + "." + classInfo.getClassName());
            entity.setModifyMethodName(methodInfo.getMethodName());
            entity.setMethodParameterStr(methodInfo.getParameterStr());
            entity.setModifyMethodMd5(methodInfo.getMd5());
            entity.setCreator(vo.getCreator());
            entity.setCreatorId(vo.getCreatorId());
            saveEntityList.add(entity);
        }
    }

    private String setInterfaceDocAddress(CoverageRecordGenerateVO vo, MethodDependenceEntityDO interfaceEntity) {
        if (!MethodTypeEnum.HTTP.name().equals(interfaceEntity.getEntryMethodType()) && !MethodTypeEnum.DUBBO.name().equals(interfaceEntity.getEntryMethodType())) {
            return "";
        }
        String fullApiAddress = null;
        if (MethodTypeEnum.HTTP.name().equals(interfaceEntity.getEntryMethodType())) {
            fullApiAddress = interfaceEntity.getZcatMetricKey();
        }
        if (MethodTypeEnum.DUBBO.name().equals(interfaceEntity.getEntryMethodType())) {
            fullApiAddress = interfaceEntity.getInterfaceFullClassName() + "#" + interfaceEntity.getMethodName() + interfaceEntity.getMethodParameterStr();
        }
        if (StringUtil.isEmpty(fullApiAddress)) {
            return "";
        }
        ApiTestEntityDO entityDO = new ApiTestEntityDO();
        entityDO.setProductCode(vo.getProductCode());
        entityDO.setAppId(vo.getAppId());
        entityDO.setApiType(ApiTypeEnum.valueOf(interfaceEntity.getEntryMethodType()));
        entityDO.setApiAddress(fullApiAddress);
        List<ApiTestEntityDO> apiTestEntityDOList = apiTestRepository.queryApiDocId(entityDO, true);
        if (CollectionUtil.isEmpty(apiTestEntityDOList) && MethodTypeEnum.HTTP.name().equals(interfaceEntity.getEntryMethodType())) {
            log.warn("未找到文档信息。productCode : {}, appId : {}, apiType : {}, apiAddress : {}", entityDO.getProductCode(), entityDO.getAppId(), entityDO.getApiType(), entityDO.getApiAddress());
            return "";
        }
        // 如果精确查找不到，前缀模糊查找
        if (CollectionUtil.isEmpty(apiTestEntityDOList)) {
            String prefixApiAddress = interfaceEntity.getInterfaceFullClassName() + "#" + interfaceEntity.getMethodName();
            entityDO.setApiAddress(prefixApiAddress);
            apiTestEntityDOList = apiTestRepository.queryApiDocId(entityDO, false);
        }
        if (CollectionUtil.isEmpty(apiTestEntityDOList)) {
            log.warn("未找到文档信息。productCode : {}, appId : {}, apiType : {}, apiAddress : {}", entityDO.getProductCode(), entityDO.getAppId(), entityDO.getApiType(), entityDO.getApiAddress());
            return "";
        }
        Long docId;
        if (apiTestEntityDOList.size() == 1) {
            docId = apiTestEntityDOList.get(0).getDocId();
        } else {
            log.warn("模糊匹配到多个文档信息。productCode : {}, appId : {}, apiType : {}, apiAddress : {}", entityDO.getProductCode(), entityDO.getAppId(), entityDO.getApiType(), entityDO.getApiAddress());
            return "";
        }
        String docAddress = String.format(configBasicService.getCoverageConfig().getDocAddress(), vo.getProductCode(), docId, entityDO.getApiType().getValue());
        return docAddress;
    }

    private Map<String, Object> createReport(final IBundleCoverage bundleCoverage, final CoverageRecordGenerateVO entity, String path, String localPath) throws IOException {

        IReportVisitor xmlVisitor2 = null;
        try {
            final XMLFormatter xmlFormatter = new XMLFormatter();
            String xmlReportLocalPath = path + "/xmlreport";
            new File(xmlReportLocalPath).mkdirs();
            xmlVisitor2 = xmlFormatter.createVisitor(new FileOutputStream(new File(xmlReportLocalPath, entity.getCommitId() + ".xml")));
            xmlVisitor2.visitInfo(execFileLoader.getSessionInfoStore().getInfos(), execFileLoader.getExecutionDataStore().getContents());
        } catch (Exception e) {
            log.error("xmlFormatter error.entity : {}", JsonUtil.toJSON(entity));
        }

        IReportVisitor xmlVisitor = null;
        try {
            final XMLFormatter xmlFormatter = new XMLFormatter();
            String xmlReportLocalPath = localPath + "/xmlreport";
            new File(xmlReportLocalPath).mkdirs();
            xmlVisitor = xmlFormatter.createVisitor(new FileOutputStream(new File(xmlReportLocalPath, entity.getCommitId() + ".xml")));
            xmlVisitor.visitInfo(execFileLoader.getSessionInfoStore().getInfos(), execFileLoader.getExecutionDataStore().getContents());
        } catch (Exception e) {
            log.error("xmlFormatter error.entity : {}", JsonUtil.toJSON(entity));
        }

        final HTMLFormatter htmlFormatter = new HTMLFormatter();
        File coveragereport = new File(path, "coveragereport");
        IReportVisitor htmlVisitor = htmlFormatter.createVisitor(new FileMultiReportOutput(coveragereport), JSONObject.parseObject(JSONObject.toJSONString(entity.getReportDto())));
        htmlVisitor.visitInfo(execFileLoader.getSessionInfoStore().getInfos(), execFileLoader.getExecutionDataStore().getContents());
        //多源码路径
        List<String> list = entity.getParent();
        MultiSourceFileLocator sourceLocator = null;
        if (!list.isEmpty() && list != null) {
            sourceLocator = new MultiSourceFileLocator(list.size());
            for (int i = 0; i < list.size(); i++) {
                sourceLocator.add(new DirectorySourceFileLocator(new File(list.get(i), "src/main/java"), "utf-8", list.size()));
            }
        }
        CollectingSourceFileLocator collectingLocator = new CollectingSourceFileLocator(sourceLocator);

        xmlVisitor2.visitBundle(bundleCoverage, collectingLocator);
        xmlVisitor2.visitEnd();
        xmlVisitor.visitBundle(bundleCoverage, collectingLocator);
        xmlVisitor.visitEnd();
        Map<String, Object> map = htmlVisitor.visitBundle(bundleCoverage, collectingLocator);
        htmlVisitor.visitEnd();
        try {
            copyJavaSrc(list, collectingLocator.getSourceFiles(), new File(coveragereport, "javaSrc"));
        } catch (Exception e) {
            log.error("copyJavaSrc error.", e);
        }

        return null;
    }

    @Override
    public void copyJavaSrc(List<String> allSrcList, Map<String, List<String>> locatedSrc, File targetDir) {
        if (CollectionUtil.isEmpty(allSrcList) || CollectionUtil.isEmpty(locatedSrc)) {
            return;
        }

        for (Map.Entry<String, List<String>> stringListEntry : locatedSrc.entrySet()) {
            for (String path : allSrcList) {
                String key = stringListEntry.getKey();
                List<String> classNames = stringListEntry.getValue();
                for (String name : classNames) {
                    String fullName = "";
                    String targetFilePath;
                    if (System.getProperty("os.name").contains("Windows")) {
                        fullName = "E:\\" + path.replace("/", "\\") + "\\src\\main\\java\\" + key + "\\" + name;
                        targetFilePath = targetDir + File.separator + key.replace("/", "\\") + "\\";
                    } else {
                        fullName = path + "/src/main/java/" + key + "/" + name;
                        targetFilePath = targetDir + File.separator + key.replace("\\", "/") + "/";
                    }
                    File sourceFile = new File(fullName);
                    if (!sourceFile.exists()) {
                        continue;
                    }
                    File targetFile = new File(targetFilePath);
                    if (!targetFile.exists()) {
                        targetFile.mkdirs();
                    }
                    targetFilePath = targetFilePath + sourceFile.getName();
                    File finalTargetFile = new File(targetFilePath);
                    try {
                        Files.copy(sourceFile.toPath(), finalTargetFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                    } catch (IOException e) {
                        log.error("复制文件时出现错误。", e);
                    }
                }
            }
        }
    }

    private void getBasicCommitId(CoverageRecordGenerateVO entity, RepositoryApi repositoryApi) {
        try {
            Branch branch = repositoryApi.getBranch(entity.getGitProjectId().intValue(), BranchEnum.MASTER.getValue());
            if (Objects.nonNull(branch) && Objects.nonNull(branch.getCommit())) {
                entity.setBasicCommitId(branch.getCommit().getId());
            } else {
                log.error("获取分支commit失败：appId:{}, gitProjectId:{}, branchName:{}, " + "branch:{}", entity.getAppId(), entity.getGitProjectId(), entity.getBranchName(), branch);
            }
        } catch (GitLabApiException e) {
            log.error("获取分支异常：appId:{}, gitProjectId:{}, branchName：{}", entity.getAppId(), entity.getGitProjectId(), entity.getBasicBranchName());
            e.printStackTrace();
            throw new ServiceException("获取最新master分支commitId异常，请稍后再试");
        }
    }

}