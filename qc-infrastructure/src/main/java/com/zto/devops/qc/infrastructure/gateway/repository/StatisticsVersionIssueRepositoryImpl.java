package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.model.dto.StatisticsVersionIssueEntityDO;
import com.zto.devops.qc.domain.gateway.repository.StatisticsVersionIssueRepository;
import com.zto.devops.qc.infrastructure.converter.StatisticsVersionIssueConverter;
import com.zto.devops.qc.infrastructure.dao.entity.StatisticsVersionIssueEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.StatisticsVersionIssueMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

@Component
public class StatisticsVersionIssueRepositoryImpl implements StatisticsVersionIssueRepository {

    @Autowired
    private StatisticsVersionIssueMapper statisticsVersionIssueMapper;

    @Autowired
    private StatisticsVersionIssueConverter statisticsVersionIssueConverter;

    @Override
    public List<StatisticsVersionIssueEntityDO> selectByActualPublishDateAndProductCode(Date startDate,
                                                                                        Date endDate,
                                                                                        List<String> productCode) {
        Example example = new Example(StatisticsVersionIssueEntity.class);
        Example.Criteria criteria = example.createCriteria();
        if (startDate != null && endDate != null) {
            criteria.andBetween("actualPublishDate", startDate, endDate);
        }
        if (CollectionUtil.isNotEmpty(productCode)) {
            criteria.andIn("productCode", productCode);
        }
        List<StatisticsVersionIssueEntity> entity = statisticsVersionIssueMapper.selectByExample(example);
        return statisticsVersionIssueConverter.convert2DO(entity);
    }
}
