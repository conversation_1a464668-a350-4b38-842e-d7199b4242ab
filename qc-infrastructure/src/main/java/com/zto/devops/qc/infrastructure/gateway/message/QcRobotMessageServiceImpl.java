package com.zto.devops.qc.infrastructure.gateway.message;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.simple.LockSeal;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.domain.gateway.PublishEventService;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.gateway.util.LockService;
import com.zto.devops.product.client.enums.TriggerEventEnum;
import com.zto.devops.product.client.model.group.enevt.CoverageRateFailMessageEvent;
import com.zto.devops.product.client.model.group.enevt.IssueMsgEvent;
import com.zto.devops.product.client.model.group.enevt.QcScheduledAutoFailMessageEvent;
import com.zto.devops.product.client.model.group.robot.MarkdownAts;
import com.zto.devops.product.client.model.group.robot.temple.ApplicationInfo;
import com.zto.devops.product.client.model.group.robot.temple.CoverageRateMsgTemPle;
import com.zto.devops.product.client.model.group.robot.temple.IssueMsgTemple;
import com.zto.devops.product.client.model.group.robot.temple.QcScheduledAutoFailMsgTemple;
import com.zto.devops.qc.client.enums.constants.LockStoreEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.model.dto.CoverageRecordBasicEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestPlanEntityDO;
import com.zto.devops.qc.client.model.issue.event.IssueAddedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueDeliveryValidatedEvent;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.rpc.project.VersionVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTaskGroupVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTaskVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.CountCaseStatusVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.ExecuteCallbackEvent;
import com.zto.devops.qc.client.model.testmanager.config.entity.ProjectProfileVO;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.message.QcRobotMessageService;
import com.zto.devops.qc.domain.gateway.repository.CoverageRepository;
import com.zto.devops.qc.domain.gateway.repository.IAutomaticTaskRepository;
import com.zto.devops.qc.domain.gateway.repository.ITmTestPlanRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProductGroupRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.domain.model.Issue;
import com.zto.devops.qc.infrastructure.config.AutomaticSchedulerMsgConfig;
import com.zto.devops.qc.infrastructure.config.CoverageConfig;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSchedulerCcEntity;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSchedulerEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.AutomaticSchedulerCcMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.AutomaticSchedulerMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class QcRobotMessageServiceImpl implements QcRobotMessageService {
    @Autowired
    private CoverageRepository coverageRepository;
    @Autowired
    private IAutomaticTaskRepository automaticTaskRepository;
    @Autowired
    private ITmTestPlanRepository iTmTestPlanRepository;
    @Autowired
    private PublishEventService publishService;
    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;
    @Autowired
    private AutomaticSchedulerMapper automaticSchedulerMapper;
    @Autowired
    private AutomaticSchedulerCcMapper automaticSchedulerCcMapper;
    @Autowired
    private CoverageConfig coverageConfig;
    @Autowired
    private AutomaticSchedulerMsgConfig automaticSchedulerMsgConfig;
    @Autowired
    private LockService lockService;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    @Autowired
    private IProjectRpcService projectRpcService;

    @Autowired
    private IProductGroupRpcService productGroupRpcService;

    @Override
    public void sendCoverageRateFailMessageEvent(CoverageRecordGenerateParameter parameter) {
        List<CoverageRecordBasicEntityDO> recordList = coverageRepository.selectAutoGenerateResultByTaskId(
                        parameter.getProductCode(),
                        parameter.getTaskId(),
                        Arrays.asList(RecordStatusEnum.FAIL, RecordStatusEnum.INITIAL));
        if (CollectionUtil.isEmpty(recordList)) {
            log.info("sendCoverageRateFailMessageEvent_recordList_is_null_taskId:{}", parameter.getTaskId());
            return;
        }
        CoverageRateMsgTemPle template = buildTemplate(parameter, buildMarkAt(parameter.getCreatorId()), buildAppInfo(recordList));
        CoverageRateFailMessageEvent event = new CoverageRateFailMessageEvent(parameter.getProductCode(), template);
        event.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        log.info("sendCoverageRateFailMessageEvent_event:{}", JSON.toJSON(event));
        publishService.publish(event);
    }

    @Override
    public void sendScheduledFailMessageEvent(ExecuteCallbackEvent event) {
        log.info("sendScheduledFailMessageEvent_event: {}", JSONUtil.toJsonStr(event));
        QcScheduledAutoFailMsgTemple qcScheduledAutoFailMsgTemple = buildScheduledAutoFailMsgTemplate(event);
        if (null == qcScheduledAutoFailMsgTemple) {
            log.info("sendScheduledFailMessageEvent_template_is_blank_taskId:{}", event.getTaskId());
            return;
        }
        doSendMessage(event, qcScheduledAutoFailMsgTemple);
    }

    private void doSendMessage(ExecuteCallbackEvent event, QcScheduledAutoFailMsgTemple qcScheduledAutoFailMsgTemple) {
        log.info("sendScheduledFailMessageEvent_lock_in_taskId: {}", event.getTaskId());
        try {
            LockSeal lockSeal = lockService.acquireLock(LockStoreEnum.SCHEDULE_MESSAGE.getValue(),
                    Collections.singletonList(event.getTaskId()), 5000L);
            log.info("sendScheduledFailMessageEvent_lock: {}", (null != lockSeal ? lockSeal.getKeys() : "null"));
            if (null != lockSeal) {
                QcScheduledAutoFailMessageEvent qcScheduledAutoFailMessageEvent = new QcScheduledAutoFailMessageEvent(event.getProductCode(), qcScheduledAutoFailMsgTemple);
                qcScheduledAutoFailMessageEvent.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                log.info("sendScheduledFailMessageEvent_qcScheduledAutoFailMessageEvent:{}", JSON.toJSON(qcScheduledAutoFailMessageEvent));
                publishService.publish(qcScheduledAutoFailMessageEvent);
            }
        } catch (Exception e) {
            log.error("sendScheduledFailMessageEvent_lock_error_taskId: {}", event.getTaskId());
        }
    }

    private MarkdownAts buildMarkAt(Long creatorId) {
        MarkdownAts markdownAts = new MarkdownAts();
        markdownAts.setAtUserIds(Collections.singletonList(String.valueOf(creatorId)));
        return markdownAts;
    }

    private List<ApplicationInfo> buildAppInfo(List<CoverageRecordBasicEntityDO> recordList) {
        List<ApplicationInfo> appInfos = new ArrayList<>(recordList.size());
        recordList.forEach(record -> {
            ApplicationInfo applicationInfo = new ApplicationInfo(record.getAppName(), record.getAppId());
            applicationInfo.setFailReason(record.getRecordErrorMsg());
            applicationInfo.setInfo(RecordStatusEnum.getValueForMessage(record.getStatus()));
            appInfos.add(applicationInfo);
        });
        return appInfos;
    }

    private CoverageRateMsgTemPle buildTemplate(CoverageRecordGenerateParameter parameter, MarkdownAts markdownAts, List<ApplicationInfo> appInfos) {
        //组装跳转链接后缀
        TmTestPlanEntityDO planEntityDO = iTmTestPlanRepository.getTestPlanByVersion(parameter.getVersionCode());
        String planCode = (null != planEntityDO) ? planEntityDO.getCode() : "";
        String suffix = "?defaultActive=Coverage"
                + "&planCode=" + planCode
                + "&code=" + planCode
                + "&productCode=" + parameter.getProductCode()
                + "&versionCode=" + parameter.getVersionCode()
                + "&versionName=" + parameter.getVersionName();
        String skipURL = (coverageConfig.getMessageSkipLink() + suffix).replaceAll(" ", "");
        //模板
        CoverageRateMsgTemPle template = new CoverageRateMsgTemPle();
        template.setExt("自动生成覆盖率失败");
        template.setVersionCode(parameter.getVersionCode());
        template.setVersionName(parameter.getVersionName());
        template.setAppInfos(appInfos);
        template.setAt(markdownAts);
        template.setDetailUrl(skipURL);
        return template;
    }

    @Override
    public void sendIssueMessageEvent(IssueAddedEvent event) {
        String groupId = "";
        if(Objects.nonNull(event.getProduct())&&StringUtils.isNotBlank(event.getProduct().getCode())){
            groupId =  productGroupRpcService.getGroupIdByProductCode(event.getProduct().getCode());
        }else{
            log.info("--------------群id为空,不发送消息！");
            return;
        }
        IssueMsgTemple temple = new IssueMsgTemple();
        temple.setExt("有新的缺陷待修复");
        temple.setExtColor("orange");
        temple.setCreater(event.getTransactor().getUserName());
        temple.setDeveloper(event.getDeveloper().getUserName());
        temple.setPriority(event.getPriority().getValue());
        temple.setPriorityColor(event.getPriority().getColor());
        temple.setTester(event.getTester().getUserName());
        temple.setIssueName(event.getTitle());
        if (Objects.nonNull(event.getFindVersion())) {
            VersionVO vo = this.getVersionByCode(event.getFindVersion().getCode());
            if(Objects.nonNull(vo)){
                temple.setVersionName(" "+vo.getVerName());
                temple.setVersionNum(vo.getVersionNum());
            } else {
                temple.setVersionName("未关联版本");
                temple.setVersionNum("");
            }
        } else {
            temple.setVersionName("未关联版本");
            temple.setVersionNum("");
        }
        ProjectProfileVO projectProfileVO = qcConfigBasicService.getProjectProfileConfig();
        String detailUrl = getDetailUrl(event.getCode(),projectProfileVO.getIssueMessageUrl(),groupId);
        String startUrl =   projectProfileVO.getIssueMessageUrl()+"&_routeType=sidebar&code="+event.getCode()+"&activeName=issue&action=FIX&groupId="+groupId;
        temple.setDetailUrl(detailUrl);
        temple.setStartUrl(startUrl);
        temple.setMemberTypeValue("开发人员");
        MarkdownAts at = new MarkdownAts();
        at.setAtUserIds(Arrays.asList(event.getDeveloper().getUserId()+""));
        temple.setAt(at);
        temple.setTest(false);
        IssueMsgEvent issueMsgEvent = new IssueMsgEvent(event.getProduct().getCode(),temple);
        issueMsgEvent.setTriggerEvent(TriggerEventEnum.ISSUE_ADD_MESSAGE);
        issueMsgEvent.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        log.info("-----------添加缺陷发送宝盒消息-----------"+ JSONObject.toJSONString(issueMsgEvent));
        publishService.publish(issueMsgEvent);
    }

    private String getDetailUrl(String code,String url,String groupId){
        return  url+"&_routeType=sidebar&code="+code+"&activeName=issue&groupId="+groupId;
    }

    private VersionVO getVersionByCode(String code ){
        return projectRpcService.findVersionQuery(code);
    }

    @Override
    public void sendDeliveryIssueMessageEvent(IssueDeliveryValidatedEvent event, Issue issue) {
        String groupId = "";
        if(Objects.nonNull(issue.getProduct())&&StringUtils.isNotBlank(issue.getProduct().getCode())){
            groupId =  productGroupRpcService.getGroupIdByProductCode(issue.getProduct().getCode());
        }else{
            log.info("--------------群id为空,不发送消息！");
            return;
        }
        IssueMsgTemple temple = new IssueMsgTemple();
        temple.setExt("有缺陷待验证");
        temple.setExtColor("orange");
        temple.setCreater(issue.getCreator().getUserName());
        temple.setDeveloper(issue.getDeveloper().getUserName());
        temple.setPriority(issue.getPriority().getValue());
        temple.setPriorityColor(issue.getPriority().getColor());
        temple.setTester(issue.getTester().getUserName());
        temple.setIssueName(issue.getTitle());
        VersionVO vo = this.getVersionByCode(event.getFixVersion().getCode());
        temple.setVersionName(" "+vo.getVerName());
        temple.setVersionNum(vo.getVersionNum());
        ProjectProfileVO projectProfileVO = qcConfigBasicService.getProjectProfileConfig();
        String detailUrl =  getDetailUrl(event.getCode(),projectProfileVO.getIssueMessageUrl() ,groupId) ;
        String startUrl = projectProfileVO.getIssueMessageUrl()+"&_routeType=sidebar&code="+event.getCode()+"&activeName=issue&action=TEST_PASS_CLOSE&groupId="+groupId ;
        temple.setDetailUrl(detailUrl);
        temple.setStartUrl(startUrl);
        temple.setMemberTypeValue("测试人员");
        temple.setTest(true);
        MarkdownAts at = new MarkdownAts();
        at.setAtUserIds(Arrays.asList(issue.getTester().getUserId()+""));
        temple.setAt(at);
        IssueMsgEvent issueMsgEvent = new IssueMsgEvent(issue.getProduct().getCode(),temple);
        issueMsgEvent.setTriggerEvent(TriggerEventEnum.ISSUE_FIX_MESSAGE);
        issueMsgEvent.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        log.info("-----------待验证缺陷发送宝盒消息-----------"+ JSONObject.toJSONString(issueMsgEvent));
        publishService.publish(issueMsgEvent);
    }


    private QcScheduledAutoFailMsgTemple buildScheduledAutoFailMsgTemplate(ExecuteCallbackEvent event) {
        try {
            Thread.sleep(5000L);
        } catch (Exception e) {
            log.error("buildScheduledAutoFailMsgTemplate_sleep_error:{}", e.getMessage());
        }

        //子任务列表
        List<AutomaticTaskVO> taskList = automaticTaskRepository.selectTaskList(Collections.singletonList(event.getTaskId()));
        if (CollectionUtil.isEmpty(taskList)) {
            log.info("sendScheduledFailMessageEvent_taskList_is_null_taskId:{}", event.getTaskId());
            return null;
        }
        //任务没有完结
        List<AutomaticTaskVO> completeTasks = taskList.stream().filter(t ->
                t.getStatus().equals(AutomaticStatusEnum.NOT_STARTED) ||
                        t.getStatus().equals(AutomaticStatusEnum.SUBMITTED) ||
                        t.getStatus().equals(AutomaticStatusEnum.IN_PROGRESS)
        ).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(completeTasks)) {
            log.info("sendScheduledFailMessageEvent_taskId_{}_in_processing_size:{}", event.getTaskId(), completeTasks.size());
            return null;
        }
        //任务分组统计
        return buildTemplate(AutomaticTaskGroupVO.buildListByGroup(taskList), event.getProductCode(), event.getTaskId());
    }

    private QcScheduledAutoFailMsgTemple buildTemplate(List<AutomaticTaskGroupVO> groupList, String productCode, String taskId) {
        if (CollectionUtils.isEmpty(groupList) || null == groupList.get(0)) {
            log.info("sendScheduledFailMessageEvent_groupList_is_empty_taskId: {}", taskId);
            return null;
        }
        AutomaticTaskGroupVO groupVO = groupList.get(0);
        log.info("sendScheduledFailMessageEvent_parent_status:{}", groupVO.getStatus());
        if (!groupVO.getStatus().equals(AutomaticStatusEnum.FAIL)) {
            return null;
        }
        List<CountCaseStatusVO> caseStatusVOS = groupVO.getCaseStatusVOS();
        if (CollectionUtil.isEmpty(caseStatusVOS)) {
            log.info("sendScheduledFailMessageEvent_caseStatusVOS_is_empty_taskId: {}", taskId);
            return null;
        }

        //跳转链接
        String detailUrl = String.format(automaticSchedulerMsgConfig.getBaseUrl() + automaticSchedulerMsgConfig.getDetailUrl(), productCode, taskId);
        String retryUrl = String.format(automaticSchedulerMsgConfig.getBaseUrl() + automaticSchedulerMsgConfig.getScheduledRetryUrl(), productCode, groupVO.getSchedulerName(), groupVO.getSchedulerCode()).replaceAll(" ", "");

        //抄送人&创建人
        AutomaticSchedulerEntity entity = automaticSchedulerMapper.selectBySchedulerCode(groupVO.getSchedulerCode());
        MarkdownAts markdownAts = new MarkdownAts();
        markdownAts.setAtUserIds(getUserId(entity));

        QcScheduledAutoFailMsgTemple qcScheduledAutoFailMsgTemple = new QcScheduledAutoFailMsgTemple();
        qcScheduledAutoFailMsgTemple.setName(groupVO.getSchedulerName());
        qcScheduledAutoFailMsgTemple.setStartTime(convertTime(groupVO.getStartTime()));
        qcScheduledAutoFailMsgTemple.setEndTime(convertTime(groupVO.getFinishTime()));
        qcScheduledAutoFailMsgTemple.setRunNamespace(groupVO.getEnv());
        qcScheduledAutoFailMsgTemple.setTotalCaseNum(caseStatusVOS.stream().mapToInt(CountCaseStatusVO::getNum).sum());
        qcScheduledAutoFailMsgTemple.setTaskCreator(entity.getCreator());
        qcScheduledAutoFailMsgTemple.setRetryBtnUrl(retryUrl);
        qcScheduledAutoFailMsgTemple.setDetailUrl(detailUrl);
        qcScheduledAutoFailMsgTemple.setAt(markdownAts);
        //任务执行结果
        for (CountCaseStatusVO caseStatusVO : caseStatusVOS) {
            if (caseStatusVO.getStatus().equals(TestPlanCaseStatusEnum.FAILED)) {
                qcScheduledAutoFailMsgTemple.setFailCaseNum(caseStatusVO.getNum());
            }
            if (caseStatusVO.getStatus().equals(TestPlanCaseStatusEnum.PASSED)) {
                qcScheduledAutoFailMsgTemple.setPassCaseNum(caseStatusVO.getNum());
            }
            if (caseStatusVO.getStatus().equals(TestPlanCaseStatusEnum.BROKEN)) {
                qcScheduledAutoFailMsgTemple.setBrokenCaseNum(caseStatusVO.getNum());
            }
            if (caseStatusVO.getStatus().equals(TestPlanCaseStatusEnum.TERMINATION)) {
                qcScheduledAutoFailMsgTemple.setTerminationCaseNum(caseStatusVO.getNum());
            }
            if (caseStatusVO.getStatus().equals(TestPlanCaseStatusEnum.SKIPPED)
                    || caseStatusVO.getStatus().equals(TestPlanCaseStatusEnum.SKIP)) {
                int skipNum = qcScheduledAutoFailMsgTemple.getSkipCaseNum() + caseStatusVO.getNum();
                qcScheduledAutoFailMsgTemple.setSkipCaseNum(skipNum);
            }
        }
        return qcScheduledAutoFailMsgTemple;
    }

    private List<String> getUserId(AutomaticSchedulerEntity entity) {
        if (null == entity) {
            return new ArrayList<>();
        }
        //定时任务创建人
        Set<String> userIdList = new HashSet<>();
        userIdList.add(String.valueOf(entity.getCreatorId()));
        //定时任务抄送人
        if (StringUtils.isNotBlank(entity.getSchedulerCode())) {
            Example example = new Example(AutomaticSchedulerCcEntity.class);
            example.createCriteria()
                    .andEqualTo("schedulerCode", entity.getSchedulerCode())
                    .andEqualTo("enable", Boolean.TRUE);
            List<AutomaticSchedulerCcEntity> ccEntities = automaticSchedulerCcMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(ccEntities)) {
                ccEntities.forEach(cc -> userIdList.add(String.valueOf(cc.getCcId())));
            }
        }
        return new ArrayList<>(userIdList);
    }


    private String convertTime(Date time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(time);
    }

}
