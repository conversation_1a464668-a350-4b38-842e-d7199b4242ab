package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.KnowledgeBaseEntityDO;
import com.zto.devops.qc.client.model.knowledgebase.query.KnowledgeBaseVO;
import com.zto.devops.qc.infrastructure.dao.entity.KnowledgeBaseEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface KnowledgeBaseEntityConverter {

    KnowledgeBaseVO convert(KnowledgeBaseEntity entity);

    KnowledgeBaseEntity convert(KnowledgeBaseEntityDO entity);

    List<KnowledgeBaseEntity> convert(List<KnowledgeBaseEntityDO> entityDOList);
}
