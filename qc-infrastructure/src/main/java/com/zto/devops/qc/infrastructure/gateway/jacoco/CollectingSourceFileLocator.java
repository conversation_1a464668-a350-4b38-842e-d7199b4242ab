package com.zto.devops.qc.infrastructure.gateway.jacoco;

import org.jacoco.report.ISourceFileLocator;

import java.io.IOException;
import java.io.Reader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CollectingSourceFileLocator implements ISourceFileLocator {

    private ISourceFileLocator locator;

    private Map<String, List<String>> sourceFiles = new HashMap<>();

    public CollectingSourceFileLocator(ISourceFileLocator locator) {
        this.locator = locator;
    }

    @Override
    public Reader getSourceFile(String packageName, String fileName) throws IOException {
        sourceFiles.computeIfAbsent(packageName, k -> new ArrayList<>()).add(fileName);
        return locator.getSourceFile(packageName, fileName);
    }

    @Override
    public int getTabWidth() {
        return locator.getTabWidth();
    }

    public Map<String, List<String>> getSourceFiles() {
        return sourceFiles;
    }
}
