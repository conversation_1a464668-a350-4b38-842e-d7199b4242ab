package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.qc.client.enums.testmanager.apitest.SceneIndexTypeEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.SceneIndexTypeHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "tm_scene_index")
public class SceneIndexEntity {

    private static final long serialVersionUID = 6426717368326759022L;
    @Id
    @GeneratedValue(generator = "JDBC", strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "scene_index_code")
    private String sceneIndexCode;

    @Column(name = "scene_index_name")
    private String sceneIndexName;

    @Column(name = "product_code")
    private String productCode;

    @Column(name = "parent_code")
    private String parentCode;

    @ColumnType(typeHandler = SceneIndexTypeHandler.class)
    private SceneIndexTypeEnum sceneIndexType;

    @Column(name = "scene_type")
    private Integer sceneType;

    @Column(name = "enable")
    private Boolean enable;

    @Column(name = "creator_id")
    private Long creatorId;

    @Column(name = "creator")
    private String creator;

    @Column(name = "gmt_create")
    private Date gmtCreate;

    @Column(name = "modifier_id")
    private Long modifierId;

    @Column(name = "modifier")
    private String modifier;

    @Column(name = "gmt_modified")
    private Date gmtModified;

    @Column(name = "testcase_code")
    private String testcaseCode;

    @Column(name = "is_collect")
    private Boolean isCollect;
}
