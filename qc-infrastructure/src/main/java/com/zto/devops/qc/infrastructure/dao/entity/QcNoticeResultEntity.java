package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Table(name = "qc_notice_result")
public class QcNoticeResultEntity extends BaseEntity implements Serializable {

    /**
     * code
     */
    @Id
    private String code;
    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Integer userId;

    /**
     * 用户姓名
     */
    @Column(name = "user_name")
    private String userName;
    /**
     * 用户头像
     */
    @Column(name = "user_avatar")
    private String userAvatar;
    /**
     * 用户岗位
     */
    @Column(name = "user_station")
    private String userStation;
    /**
     * 用户id
     */
    @Column(name = "dept_code")
    private Integer deptCode;

    /**
     * 用户姓名
     */
    @Column(name = "dept_name")
    private String deptName;

    /**
     * 用户类型
     */
    @Column(name = "user_type")
    private String userType;
    /**
     * 通知类型
     */
    @Column(name = "notice_type")
    private String noticeType;

    /**
     * 发送状态
     */
    private String status;

    /**
     * 发送时间
     */
    @Column(name = "send_time")
    private Date sendTime;

    /**
     * 投递失败原因
     */
    @Column(name = "fail_send_reson")
    private String failSendReson;

    /**
     * 消息id，用于快速检索
     */
    @Column(name = "msg_id")
    private String msgId;

    @Column(name = "email")
    private String email;

    private static final long serialVersionUID = 1L;

    /**
     * 获取code
     *
     * @return code - code
     */
    public String getCode() {
        return code;
    }

    /**
     * 设置code
     *
     * @param code code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 获取用户id
     *
     * @return user_id - 用户id
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * 设置用户id
     *
     * @param userId 用户id
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取用户姓名
     *
     * @return user_name - 用户姓名
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 设置用户姓名
     *
     * @param userName 用户姓名
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 获取发送状态
     *
     * @return status - 发送状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置发送状态
     *
     * @param status 发送状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取发送时间
     *
     * @return send_time - 发送时间
     */
    public Date getSendTime() {
        return sendTime;
    }

    /**
     * 设置发送时间
     *
     * @param sendTime 发送时间
     */
    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    /**
     * 获取投递失败原因
     *
     * @return fail_send_reson - 投递失败原因
     */
    public String getFailSendReson() {
        return failSendReson;
    }

    /**
     * 设置投递失败原因
     *
     * @param failSendReson 投递失败原因
     */
    public void setFailSendReson(String failSendReson) {
        this.failSendReson = failSendReson;
    }

    /**
     * 获取消息id，用于快速检索
     *
     * @return msg_id - 消息id，用于快速检索
     */
    public String getMsgId() {
        return msgId;
    }

    /**
     * 设置消息id，用于快速检索
     *
     * @param msgId 消息id，用于快速检索
     */
    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }


    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }

    public Integer getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(Integer deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getUserAvatar() {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public String getUserStation() {
        return userStation;
    }

    public void setUserStation(String userStation) {
        this.userStation = userStation;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}