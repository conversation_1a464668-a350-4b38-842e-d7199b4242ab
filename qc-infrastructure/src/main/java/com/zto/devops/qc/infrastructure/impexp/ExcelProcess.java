package com.zto.devops.qc.infrastructure.impexp;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.infrastructure.util.HttpClientUtils;
import com.zto.devops.qc.client.enums.report.CaseExecuteResult;
import com.zto.devops.qc.client.model.report.entity.*;
import com.zto.devops.qc.domain.gateway.impexp.IExcelProcess;
import com.zto.devops.qc.infrastructure.config.QcConfig;
import com.zto.titans.common.exception.ErrorCodeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ConnectTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.LinkedList;
import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：ExcelProcess
 * 类 描 述：TODO
 * 创建时间：2021/11/24 1:30 下午
 * 创 建 人：bulecat
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ExcelProcess implements IExcelProcess {

    @Autowired
    private QcConfig qcConfig;

    public byte[] getFile(String url) {
        byte[] importExcelBytes = null;
        try {
            if ("2".equals(qcConfig.getEnv())) {
                url.replace("https://fscdn.zto.com", "http://newfs.ztosys.com");
            }
            log.info("env is {} url is {}", System.getProperty("env"), url);
            // 下载文件
            importExcelBytes = HttpClientUtils.sendFileDownload(url, null, 500, 1000);
        } catch (ConnectTimeoutException e4) {
            log.warn("第一次导入信息失败!解析的数据文件路径[{}]", url, e4);
            // 替换预发的域名重新尝试
            String url2 = url.replace("https://fscdn.zto.com", "http://newfs.ztosys.com");
            log.info("postComplainImportCreate url2==> {}", url2);
            try {
                importExcelBytes = HttpClientUtils.sendFileDownload(url2, null, 1000, 2000);
            } catch (IOException e3) {
                log.error("第二次导入信息失败!解析的数据文件路径url2[{}]", url2, e3);
                throw new ServiceException("导入文件解析失败");
            }
        } catch (IOException e) {
            log.warn("第一次导入信息失败!解析的数据文件路径[{}]", url, e);
        } catch (ErrorCodeException e2) {
            log.warn("第一次导入信息失败!解析的数据文件路径[{}]", url, e2);
            // 替换预发的域名重新尝试
            String url2 = url.replaceAll(".*/dfsproxy/", "http://newfs.ztosys.com/");
            log.info("postComplainImportCreate url2==> {}", url2);
            try {
                importExcelBytes = HttpClientUtils.sendFileDownload(url2, null, 1000, 2000);
            } catch (IOException e3) {
                log.error("第二次导入信息失败!解析的数据文件路径url2[{}]", url2, e3);
                throw new ServiceException("导入文件解析失败");
            }
        }
        return importExcelBytes;
    }

    @Override
    public List<PlanFunctionPointResp> ExcelWriterPFP(String url) {
        List<PlanFunctionPointResp> list = new LinkedList<>();
        byte[] file = getFile(url);
        InputStream stream = new ByteArrayInputStream(file);
        try {
            EasyExcel.read(new ByteArrayInputStream(file), PlanFunctionPointResp.class, new PlanPointHeadDataListener()).sheet().doRead();
        } catch (Exception e) {
            if (StringUtil.isNotBlank(e.getMessage())) {
                throw new ServiceException(e.getMessage().replace("com.zto.devops.framework.client.exception.ServiceException:", ""));
            }

        }
        EasyExcel.read(stream)
                .head(PlanFunctionPointResp.class)
                .sheet()
                .registerReadListener(new AnalysisEventListener<PlanFunctionPointResp>() {
                    @Override
                    public void invoke(PlanFunctionPointResp data, AnalysisContext context) {
                        if (data != null && !data.isBeanNone()) {
                            list.add(data);
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        log.info("数据读取完毕");
                    }
                }).doRead();

        return list;
    }

    /*public List<PlanIntegrateResp> ExcelWriterPI(String url) {
        List<PlanIntegrateResp> list = new LinkedList<>();
        byte[] file = getFile(url);
        InputStream stream = new ByteArrayInputStream(file);
        try {
            EasyExcel.read(new ByteArrayInputStream(file), PlanIntegrateResp.class, new PlanIntegrateHeadDataListener()).sheet().doRead();
        } catch (Exception e) {
            if (StringUtil.isNotBlank(e.getMessage())) {
                throw new ServiceException(e.getMessage().replace("com.zto.lbd.kernel.exception.ServiceException:", ""));
            }

        }
        EasyExcel.read(stream)
                .head(PlanIntegrateResp.class)
                .sheet()
                .registerReadListener(new AnalysisEventListener<PlanIntegrateResp>() {
                    @Override
                    public void invoke(PlanIntegrateResp data, AnalysisContext context) {
                        if (data != null && !data.isBeanNone()) {
                            list.add(data);
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        log.info("数据读取完毕");
                    }
                }).doRead();
        return list;
    }
*/
    @Override
    public List<PlanMobileResp> ExcelWriterPM(String url) {
        List<PlanMobileResp> list = new LinkedList<>();
        byte[] file = getFile(url);
        InputStream stream = new ByteArrayInputStream(file);
        try {
            EasyExcel.read(new ByteArrayInputStream(file), PlanMobileResp.class, new PlanMobileHeadDataListener()).sheet().doRead();
        } catch (Exception e) {
            if (StringUtil.isNotBlank(e.getMessage())) {
                throw new ServiceException(e.getMessage().replace("com.zto.devops.framework.client.exception.ServiceException:", ""));
            }

        }
        EasyExcel.read(stream)
                .head(PlanMobileResp.class)
                .sheet()
                .registerReadListener(new AnalysisEventListener<PlanMobileResp>() {
                    @Override
                    public void invoke(PlanMobileResp data, AnalysisContext context) {
                        if (data != null && !data.isBeanNone()) {
                            list.add(data);
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        log.info("数据读取完毕");
                    }
                }).doRead();
        return list;
    }

  /*  public List<ReportAccessResp> ExcelWriterRA(String url) {
        List<ReportAccessResp> list = new LinkedList<>();
        byte[] file = getFile(url);
        InputStream stream = new ByteArrayInputStream(file);
        try {
            EasyExcel.read(new ByteArrayInputStream(file), ReportAccessResp.class, new ReportAccessHeadDataListener()).sheet().doRead();
        } catch (Exception e) {
            if (StringUtil.isNotBlank(e.getMessage())) {
                throw new ServiceException(e.getMessage().replace("com.zto.lbd.kernel.exception.ServiceException:", ""));
            }

        }
        EasyExcel.read(stream)
                .head(ReportAccessResp.class)
                .sheet()
                .registerReadListener(new AnalysisEventListener<ReportAccessResp>() {
                    @Override
                    public void invoke(ReportAccessResp data, AnalysisContext context) {
                        if (data != null && !data.isBeanNone()) {
                            list.add(data);
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        log.info("数据读取完毕");
                    }
                }).doRead();

        for (ReportAccessResp resp : list) {
            // 有 bug ，解析出来的字段对不上
            if (StringUtil.isNotBlank(resp.getExecuteResult())) {
                String code = resp.getExecuteResult();
                String byName = CaseExecuteResult.getByName(code);
                resp.setExecuteResult(byName);
                resp.setExecuteResultDesc(code);
            } else {
                resp.setExecuteResult(CaseExecuteResult.getByName(resp.getExecuteResultDesc()));
            }
        }

        return list;
    }

    public List<ReportMobileResp> ExcelWriterRM(String url) {
        List<ReportMobileResp> list = new LinkedList<>();
        byte[] file = getFile(url);
        InputStream stream = new ByteArrayInputStream(file);
        try {
            EasyExcel.read(new ByteArrayInputStream(file), ReportMobileResp.class, new ReportMobileHeadDataListener()).sheet().doRead();
        } catch (Exception e) {
            if (StringUtil.isNotBlank(e.getMessage())) {
                throw new ServiceException(e.getMessage().replace("com.zto.lbd.kernel.exception.ServiceException:", ""));
            }

        }
        EasyExcel.read(stream)
                .head(ReportMobileResp.class)
                .sheet()
                .registerReadListener(new AnalysisEventListener<ReportMobileResp>() {
                    @Override
                    public void invoke(ReportMobileResp data, AnalysisContext context) {
                        if (data != null && !data.isBeanNone()) {
                            list.add(data);
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        log.info("数据读取完毕");
                    }
                }).doRead();
        for (ReportMobileResp resp : list) {
            // 有 bug ，解析出来的字段对不上
            if (StringUtil.isNotBlank(resp.getExecuteResult())) {
                String code = resp.getExecuteResult();
                String byName = CaseExecuteResult.getByName(code);
                resp.setExecuteResult(code);
                resp.setExecuteResultDesc(byName);
            } else {
                resp.setExecuteResult(CaseExecuteResult.getByName(resp.getExecuteResultDesc()));
            }
        }

        return list;
    }

    public List<ReportOnlineSmokeResp> ExcelWriterRO(String url) {
        List<ReportOnlineSmokeResp> list = new LinkedList<>();
        byte[] file = getFile(url);
        InputStream stream = new ByteArrayInputStream(file);
        try {
            EasyExcel.read(new ByteArrayInputStream(file), ReportOnlineSmokeResp.class, new ReportOnlineHeadDataListener()).sheet().doRead();
        } catch (Exception e) {
            if (StringUtil.isNotBlank(e.getMessage())) {
                throw new ServiceException(e.getMessage().replace("com.zto.lbd.kernel.exception.ServiceException:", ""));
            }

        }
        EasyExcel.read(stream)
                .head(ReportOnlineSmokeResp.class)
                .sheet()
                .registerReadListener(new AnalysisEventListener<ReportOnlineSmokeResp>() {
                    @Override
                    public void invoke(ReportOnlineSmokeResp data, AnalysisContext context) {
                        if (data != null && !data.isBeanNone()) {
                            list.add(data);
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        log.info("数据读取完毕");
                    }
                }).doRead();
        for (ReportOnlineSmokeResp resp : list) {
            // 有 bug ，解析出来的字段对不上
            if (StringUtil.isNotBlank(resp.getExecuteResult())) {
                String code = resp.getExecuteResult();
                String byName = CaseExecuteResult.getByName(code);
                resp.setExecuteResult(code);
                resp.setExecuteResultDesc(byName);
            } else {
                resp.setExecuteResult(CaseExecuteResult.getByName(resp.getExecuteResultDesc()));
            }

        }

        return list;
    }
*/

}