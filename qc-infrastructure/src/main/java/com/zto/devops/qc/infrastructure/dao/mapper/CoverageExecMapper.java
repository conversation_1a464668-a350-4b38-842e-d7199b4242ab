package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.infrastructure.dao.entity.CoverageExecEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2022/9/16 17:16
 */
public interface CoverageExecMapper extends Mapper<CoverageExecEntity> {
    List<CoverageExecEntity> getExecList(@Param("versionCode") String versionCode,
                                         @Param("appId") String appId,
                                         @Param("commitId") String commitId,
                                         @Param("flowLaneType") String flowLaneType,
                                         @Param("diffType") DiffTypeEnum diffType);

    List<CoverageExecEntity> getMiddleExecList(@Param("list") Set<String> middleCommitIdList,
                                               @Param("appId") String appId,
                                               @Param("versionCode") String versionCode,
                                               @Param("branchName") String branchName,
                                               @Param("flowLaneType") String flowLaneType,
                                               @Param("diffType") DiffTypeEnum diffType);
}