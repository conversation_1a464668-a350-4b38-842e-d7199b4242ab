package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.report.ExecResultBusinessDomain;
import com.zto.devops.qc.infrastructure.dao.typehandler.BusinessDomainHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "qc_exec_result")
public class ExecResultEntity extends BaseEntity {

    /**
     * 编号code
     */
    @Id
    private String code;

    /**
     * 关联的实例ID,比如版本的ID,bug的ID等
     */
    @Column(name = "business_code")
    private String businessCode;

    /**
     * 业务域,比如自动化测试等
     */
    @Column(name = "business_domain")
    @ColumnType(typeHandler = BusinessDomainHandler.class)
    private ExecResultBusinessDomain businessDomain;

    /**
     * * 自动化测试结果 0 无需执行、1.待执行、2.执行中、3.通过、4.不通过、5.执行失败
     *  * 其他
     */
    @Column(name = "execute_result")
    private String executeResult;

    private static final long serialVersionUID = 1L;

}