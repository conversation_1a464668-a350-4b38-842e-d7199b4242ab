package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.RelevantUserEntityDO;
import com.zto.devops.qc.client.model.issue.entity.CurrentHandlerVO;
import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import com.zto.devops.qc.infrastructure.dao.entity.RelevantUserEntity;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/4/9
 * @Version 1.0
 */
@Mapper(componentModel = "spring")
public interface RelevantUserDOConvertor {

    RelevantUserEntityDO covert(RelevantUserEntity entity);

    List<RelevantUserEntityDO> covert(List<RelevantUserEntity> entity);

    RelevantUserEntity convert(CurrentHandlerVO currentHandlerVO);

    List<RelevantUserVO> convert2VO(List<RelevantUserEntity> entityList);

    RelevantUserEntity covert(RelevantUserEntityDO entityDO);
}
