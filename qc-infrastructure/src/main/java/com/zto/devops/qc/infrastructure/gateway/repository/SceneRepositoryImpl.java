package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoStatusEnum;
import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.domain.gateway.repository.SceneRepository;
import com.zto.devops.qc.infrastructure.converter.SceneEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.SceneInfoEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.SceneInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Component
@Slf4j
public class SceneRepositoryImpl implements SceneRepository {

    @Autowired
    private SceneInfoMapper sceneInfoMapper;
    @Autowired
    private SceneEntityConverter sceneEntityConverter;


    @Override
    public List<SceneInfoEntityDO> queryPublishScenes(List<String> sceneCodes, String productCde) {
        Example example = new Example(SceneInfoEntity.class);
        example.selectProperties("sceneCode", "automaticSourceCode");
        Example.Criteria criteria = example.createCriteria();
        criteria
                .andEqualTo("productCode", productCde)
                .andEqualTo("enable", SceneInfoEnableEnum.PUBLISHED.getCode())
                .andEqualTo("status", SceneInfoStatusEnum.publish.name())
                .andEqualTo("sceneType", 1);
        if (CollectionUtils.isNotEmpty(sceneCodes)) {
            criteria.andIn("sceneCode", sceneCodes);
        }
        example.setDistinct(true);
        return sceneEntityConverter.converterToSceneInfoEntityList(sceneInfoMapper.selectByExample(example));
    }

}