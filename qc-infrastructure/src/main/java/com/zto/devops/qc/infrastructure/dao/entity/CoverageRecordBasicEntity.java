package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.rpc.FlowLaneTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.*;
import com.zto.devops.qc.infrastructure.dao.typehandler.*;
import lombok.Data;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2022/10/08 10:31
 */
@ToString
@Data
@Table(name = "qc_coverage_record")
public class CoverageRecordBasicEntity extends BaseEntity {

    public CoverageRecordBasicEntity() {

    }

    public CoverageRecordBasicEntity(String appId, String versionCode, String versionName,
                                     String branchName, RecordTypeEnum recordType,
                                     RecordStatusEnum status, Boolean enable,
                                     AppTypeEnum appType) {
        this.appId = appId;
        this.versionCode = versionCode;
        this.versionName = versionName;
        this.branchName = branchName;
        this.recordType = recordType;
        this.status = status;
        this.appType = appType;
        super.setEnable(enable);
    }

    public CoverageRecordBasicEntity(String appId, Long deptId, String deptName,
                                     String productCode, String productName,
                                     String versionCode, String versionName,
                                     String branchName, RecordTypeEnum recordType,
                                     RecordStatusEnum status, Boolean isWhiteList,
                                     String testStrategy, AppTypeEnum appType,
                                     BigDecimal standardRate) {
        this.appId = appId;
        this.deptId = deptId;
        this.deptName = deptName;
        this.productCode = productCode;
        this.productName = productName;
        this.versionCode = versionCode;
        this.versionName = versionName;
        this.branchName = branchName;
        this.isWhiteList = isWhiteList;
        this.testStrategy = testStrategy;
        this.status = status;
        this.recordType = recordType;
        this.appType = appType;
        this.standardRate = standardRate;
    }

    @Id
    @GeneratedValue(generator = "JDBC", strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 部门编码
     */
    @Column(name = "dept_id")
    private Long deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    private String deptName;

    /**
     * 产品编码
     */
    @Column(name = "product_code")
    private String productCode;

    /**
     * 产品名称
     */
    @Column(name = "product_name")
    private String productName;

    /**
     * 版本编码
     */
    @Column(name = "version_code")
    private String versionCode;

    /**
     * 版本名称
     */
    @Column(name = "version_name")
    private String versionName;

    /**
     * appId
     */
    @Column(name = "app_id")
    private String appId;

    /**
     * appName
     */
    @Column(name = "app_name")
    private String appName;

    /**
     * 报告类型(FULL-全量,BRANCH-分支增量,MASTER-主干增量)
     */
    @Column(name = "record_type")
    @ColumnType(typeHandler = RecordTypeHandler.class)
    private RecordTypeEnum recordType;

    /**
     * 生成类型(AUTO-自动生成,MANUAL-手动触发)
     */
    @Column(name = "generate_type")
    @ColumnType(typeHandler = GenerateTypeHandler.class)
    private GenerateTypeEnum generateType;

    /**
     * 标准值
     */
    @Column(name = "standard_rate")
    private BigDecimal standardRate;

    /**
     * 覆盖率
     */
    @Column(name = "record_rate")
    private BigDecimal recordRate;

    /**
     * 生成状态(INITIAL-待生成，RUNNING-生成中，SUCCEED-成功，FAIL-失败，NEEDLESS-无需生成)
     */
    @Column(name = "status")
    @ColumnType(typeHandler = CoverageRecordStatusHandler.class)
    private RecordStatusEnum status;

    /**
     * 报告或错误日志地址
     */
    @Column(name = "record_url")
    private String recordUrl;

    /**
     * 不达标原因
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 覆盖率报告异常原因
     */
    @Column(name = "record_error_msg")
    private String recordErrorMsg;

    /**
     * 测试策略
     */
    @Column(name = "test_strategy")
    private String testStrategy;

    /**
     * 是否白名单，0-否，1-是
     */
    @Column(name = "is_white_list")
    private Boolean isWhiteList;

    /**
     * 分支名称
     */
    @Column(name = "branch_name")
    private String branchName;

    /**
     * git提交id
     */
    @Column(name = "commit_id")
    private String commitId;

    /**
     * 基准分支名称
     */
    @Column(name = "basic_branch_name")
    private String basicBranchName;

    /**
     * 基准commitId
     */
    @Column(name = "basic_commit_id")
    private String basicCommitId;

    /**
     * 应用类型，JAVA-java应用，WEB-前端应用，GOLANG-go应用
     */
    @Column(name = "app_type")
    @ColumnType(typeHandler = AppTypeHandler.class)
    private AppTypeEnum appType;

    /**
     * 差异类型
     */
    @Column(name = "diff_type")
    @ColumnType(typeHandler = DiffTypeHandler.class)
    private DiffTypeEnum diffType;

    /**
     * 空间名称
     */
    @Column(name = "env_name")
    private String envName;

    /**
     * 任务编号
     */
    @Column(name = "task_id")
    private String taskId;

    /**
     * 备注
     */
    @Column(name = "comment")
    private String comment;

    /**
     * 发布泳道
     */
    @Column(name = "flow_lane_type")
    @ColumnType(typeHandler = FlowLaneTypeHandler.class)
    private FlowLaneTypeEnum flowLaneType;

    /**
     * 存储桶名
     */
    @Column(name = "bucket_name")
    private String bucketName;

    /**
     * 文件名（全路径）
     */
    @Column(name = "file_name")
    private String fileName;

}