package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.scheduler.SchedulerPreExecuteStatus;
import com.zto.devops.qc.infrastructure.dao.typehandler.SchedulerPreExecuteStatusHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "tm_pre_execution_scheduler")
public class AutomaticPreExecutionEntity extends BaseEntity {

    @Id
    private Long id;

    private String preCode;

    private String schedulerCode;

    private String schedulerName;

    private Date executeTime;

    @ColumnType(typeHandler = SchedulerPreExecuteStatusHandler.class)
    private SchedulerPreExecuteStatus preStatus;

}
