package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.ApiTypeHandler;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Table(name = "tm_scene_api_relation")
public class SceneApiRelationEntity extends BaseEntity {

    @Id
    private Long id;

    private String sceneCode;

    private String productCode;

    private String appId;

    @ColumnType(typeHandler = ApiTypeHandler.class)
    private ApiTypeEnum apiType;

    private String apiAddress;

    private Integer apiTestIndex;

    private String relatedApiName;

    private String mainApiCode;
}
