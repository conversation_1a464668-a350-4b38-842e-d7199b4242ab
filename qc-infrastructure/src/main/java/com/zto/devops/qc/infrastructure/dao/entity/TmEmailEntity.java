package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.email.EmailSourceEnum;
import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.email.TmEmailDataSourceEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.EmailDataSourceHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.EmailSourceHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.EmailTypeHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "tm_email")
public class TmEmailEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @Column(name = "id")
    private Long id;

    /**
     * 邮件code（本表唯一标识）
     */
    @Id
    @Column(name = "email_code")
    private String emailCode;

    /**
     * 邮件名称
     */
    @Column(name = "email_name")
    private String emailName;

    /**
     * 邮件类型(准入、准出、简易...)
     */
    @ColumnType(typeHandler = EmailTypeHandler.class)
    @Column(name = "email_type")
    private EmailTypeEnum emailType;

    /**
     * 数据来源(报告、计划)
     */
    @ColumnType(typeHandler = EmailSourceHandler.class)
    @Column(name = "email_source")
    private EmailSourceEnum emailSource;

    /**
     * 报告/计划code
     */
    @Column(name = "business_code")
    private String businessCode;

    /**
     * 报告/计划名称
     */
    @Column(name = "business_name")
    private String businessName;


    /**
     * 关联计划code
     */
    @Column(name = "relate_plan_code")
    private String relatePlanCode;

    /**
     * 关联计划名称
     */
    @Column(name = "relate_plan_name")
    private String relatePlanName;

    /**
     * 产品ID
     */
    @Column(name = "product_code")
    private String productCode;

    /**
     * 产品名称
     */
    @Column(name = "product_name")
    private String productName;


    /**
     * 版本code
     */
    @Column(name = "version_code")
    private String versionCode;

    /**
     * 版本名称
     */
    @Column(name = "version_name")
    private String versionName;


    /**
     * 计划提测时间
     */
    @Column(name = "plan_presentation_date")
    private Date planPresentationDate;

    /**
     * 计划准出时间
     */
    @Column(name = "plan_approval_exit_date")
    private Date planApprovalExitDate;

    /**
     * 预览的html
     */
    @Column(name = "preview")
    private String preview;

    /**
     * 发送时间
     */
    @Column(name = "send_date")
    private Date sendDate;

    /**
     * 发送人ID
     */
    @Column(name = "sender_id")
    private Long senderId;

    /**
     * 发送人
     */
    @Column(name = "sender")
    private String sender;

    /**
     * 数据同步-创建时间
     */
    @Column(name = "sync_create")
    private Date syncCreate;

    /**
     * 数据同步-更新时间
     */
    @Column(name = "sync_modified")
    private Date syncModified;

    /**
     * 数据来源(同步、流程创建)
     */
    @ColumnType(typeHandler = EmailDataSourceHandler.class)
    @Column(name = "data_source")
    private TmEmailDataSourceEnum dataSource;

}
