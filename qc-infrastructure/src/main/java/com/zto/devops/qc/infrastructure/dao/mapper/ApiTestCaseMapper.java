package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.dto.ApiTestCaseEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestCaseEnableVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.CountApiTestCaseChildrenVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.PageApiTestCaseVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.StatApiCaseExecuteResultVO;
import com.zto.devops.qc.client.model.testmanager.apitest.query.ApiTestCaseExecuteDetailQuery;
import com.zto.devops.qc.client.model.testmanager.apitest.query.PageApiTestCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ApiTestCaseExecuteDetailTiledVO;
import com.zto.devops.qc.infrastructure.dao.entity.ApiTestCaseEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Set;

public interface ApiTestCaseMapper extends Mapper<ApiTestCaseEntity> {

    List<PageApiTestCaseVO> queryApiTestCasePage(PageApiTestCaseQuery query);

    void batchInsert(List<ApiTestCaseEntityDO> apiTestCaseEntityDOList);

    /**
     * 查询用例enbale,合并草稿和发布态
     *
     * @param productCode
     * @param codeList
     * @return
     */
    List<ApiTestCaseEnableVO> queryApiCaseEnable(@Param("productCode") String productCode,
                                                 @Param("codeList") List<String> codeList,
                                                 @Param("status") String status);

    List<StatApiCaseExecuteResultVO> statApiCaseExecuteResult(@Param("taskCode") String taskCode);

    /**
     * 根据父用例code，统计子用例数
     *
     * @param codeSet 父用例code集合
     * @return {@link CountApiTestCaseChildrenVO}
     */
    List<CountApiTestCaseChildrenVO> selectApiTestCaseListByCode(@Param("codeSet") Set<String> codeSet,
                                                                 @Param("productCode") String productCode);

    /**
     * 查询接口用例执行明细
     *
     * @param query {@link ApiTestCaseExecuteDetailQuery}
     * @return {@link ApiTestCaseExecuteDetailTiledVO}
     */
    List<ApiTestCaseExecuteDetailTiledVO> queryApiTestCaseExecuteDetail(ApiTestCaseExecuteDetailQuery query);
}
