package com.zto.devops.qc.infrastructure.sso;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.infrastructure.gateway.util.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.CookieStore;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.cookie.Cookie;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2023/5/25 10:42
 */
@Component
@Slf4j
public class ZtoSsoService {

    @Cacheable(value = "testCaseSsoCookie", key = "'SSO_'+#key")
    public String getCookie(String key, String secret, String nameSpace) throws Exception {
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = null;
        try {
            String state1 = "DYnCrlI2Uwtrm2z9K";
            String state2 = "VeIK1w9bD0Kr55PV";
            HttpGet httpGet;
            String data;
            CookieStore cookieStore = new BasicCookieStore();
            httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
            httpGet = new HttpGet("https://sso.test.ztosys.com/oauth2/authorize?appid=ztjEceipVPWKmAUdZ8_q2ERg&redirect_uri=https%3A%2F%2F" + nameSpace + ".test.ztosys" +
                    ".com%2Fhome%2Findex%3Fcode%3Du8WHCcW0ZX70NXLnEVSKMMwxRTatn1w%26state%3" + state1 + "&response_type=code&scope=userinfo,user_id,usercontact&state=" + state2);
            httpGet.getParams().setParameter("http.protocol.allow-circular-redirects", true);
            httpGet.setHeader("X-Canvas-Fingerprint", "dcbea16d7d767b7c02e3e630eb7d0ad8");
            httpGet.setHeader("Authorization", secret);
            httpGet.setHeader("X-Unique-Id", "sid_zt19320e809f406eb59d0136ccfd0e18");
            httpGet.setHeader("Content-Type", "application/json;charset=UTF-8");
            response = httpClient.execute(httpGet);
            HttpEntity httpEntity = response.getEntity();
            data = EntityUtils.toString(httpEntity, "utf-8");
            JSONObject object = JSONObject.parseObject(data);
            String ticket = object.getString("ticket_1");
            String whyzjznUrl = "https://sso.test.ztosys.com/whyzjzn?ticket=" + ticket + "&host=.ztosys.com";
            HttpPost httpPost = new HttpPost(whyzjznUrl);
            httpClient.execute(httpPost);
            List<Cookie> cookieList = cookieStore.getCookies();
            for (Cookie co : cookieList) {
                if (co.getName().equals("wyandyy")) {
                    String cookie = "wyandyy=" + co.getValue();
                    return cookie;
                }
            }
        } catch (Exception e) {
            log.error("getCookie error.", e);
            throw new Exception(e);
        } finally {
            if (null != response) {
                response.close();
            }
            if (null != httpClient) {
                httpClient.close();
            }
        }
        return "";
    }

    @Cacheable(value = "testCaseCookie", key = "'GW_'+#key")
    public String getCookieGw(String key, String secret, String nameSpace) throws Exception {
        CloseableHttpClient httpClient = null;
        HttpGet httpGet;
        HttpPost httpPost;
        String data;
        CloseableHttpResponse response = null;
        String cookieStr = "";
        String lubanUrl = "https://sso.test.ztosys.com/oauth2/authorize?appid=ztuiM86j3_Xk6AToj2WOVA7w&redirect_uri=https%3A%2F%2F" + nameSpace + ".gw-test.ztosys" +
                ".com%2FloginCallback%3Fns%3Dluban%26redirect%3Dhttp%253A%252F%252Fluban-frontend.xlb.ft.ztosys.com%252F&response_type=code&scope=userinfo,user_id&state=13";
        RequestConfig defaultConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.STANDARD).build();
        try {
            httpClient = HttpClients.createDefault();
            CookieStore cookieStore = new BasicCookieStore();
            httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
            httpGet = new HttpGet(lubanUrl);
            httpGet.setHeader("X-Canvas-Fingerprint", "7a0008927ac936ae207afb680718b56e");
            httpGet.setHeader("Authorization", secret);
            httpGet.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.99 Safari/537.36 mobile");
            httpGet.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpGet.setConfig(defaultConfig);
            response = httpClient.execute(httpGet);
            HttpEntity httpEntity = response.getEntity();
            data = EntityUtils.toString(httpEntity, "utf-8");
            JSONObject object = JSONObject.parseObject(data);
            String ticket1 = object.getString("ticket_1");
            String url = "https://sso.test.ztosys.com/whyzjzn?ticket=" + ticket1 + "&host=.ztosys.com";
            httpPost = new HttpPost(url);
            httpPost.setConfig(defaultConfig);
            response = httpClient.execute(httpPost);

            url = "https://sso.test.ztosys.com/set-cookie?ticket=" + ticket1 + "&host=.ztosys.com";
            httpPost = new HttpPost(url);
            httpPost.setConfig(defaultConfig);
            response = httpClient.execute(httpPost);
            String gatewayRedirectUri = object.getString("redirect_uri");
            log.info("namespace : {}, gatewayRedirectUri : {}", nameSpace, gatewayRedirectUri);
            // 获取网关的cookie
            String mobileSessionId = getGatewayMobileSessionId(gatewayRedirectUri);
            // 获取cookies信息
            List<Cookie> cookieList = cookieStore.getCookies();
            for (Cookie cookie : cookieList) {
                if (cookie.getName().equals("com.zto.sessionid")) {
                    cookieStr += "com.zto.sessionid=" + cookie.getValue() + ";";
                }
                if (cookie.getName().equals("com.zto.lid")) {
                    cookieStr += "com.zto.lid=" + cookie.getValue() + ";";
                }
                if (cookie.getName().equals("wyandyy")) {
                    cookieStr += "wyandyy=" + cookie.getValue() + ";";
                }
                if (cookie.getName().equals("wyzdzjxhdnh")) {
                    cookieStr += "wyzdzjxhdnh=" + cookie.getValue() + ";";
                }
            }
            cookieStr += "mobileSessionId=" + mobileSessionId;
            return cookieStr;
        } catch (Exception e) {
            log.error("getCookieGw error.", e);
            throw new Exception(e);
        } finally {
            if (null != response) {
                response.close();
            }
            if (null != httpClient) {
                httpClient.close();
            }
        }
    }

    private String getGatewayMobileSessionId(String gatewayRedirectUri) {
        if (StringUtil.isEmpty(gatewayRedirectUri)) {
            return "";
        }
        CloseableHttpClient httpClient = HttpClients.custom().disableRedirectHandling().build();
        CloseableHttpResponse response = null;
        RequestConfig defaultConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.STANDARD).build();
        try {
            HttpGet httpGet = new HttpGet(gatewayRedirectUri);
            httpGet.setConfig(defaultConfig);
            response = (CloseableHttpResponse) httpClient.execute(httpGet);
            Header[] headers = response.getHeaders("set-cookie");
            for (Header header : headers) {
                String value = header.getValue();
                if (value != null && value.contains("mobileSessionId=")) {
                    String mobileSessionId = value.replaceAll(".*mobileSessionId=([^;]+).*", "$1");
                    if (!mobileSessionId.equals(value)) {  // 确保正则表达式匹配成功
                        return mobileSessionId;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("getGatewayMobileSessionId error", e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (Exception e) {
                log.error("", e);
            }
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return "";
    }

    @Cacheable(value = "testCaseCookie", key = "'KDGJ_'+#username")
    public String getCookieKDGJ(String username, String password) {
        try {
            String url = "https://ydd-web-new-base-kdgj.test.ztosys.com/user/login";
            Map<String, String> params = new HashMap<>();
            params.put("username", username);
            params.put("password", password);
            params.put("isPassWordLogin", "true");
            HttpResponse response = HttpUtils.doPostWithForm(url, params);
            if (Objects.isNull(response)) {
                throw new ServiceException("获取cookies异常");
            }
            String session = response.getHeaders("Set-Cookie")[0].getValue().split(";")[0];
            String responseStr = EntityUtils.toString(response.getEntity(), "UTF-8");
            if (StringUtil.isEmpty(responseStr)) {
                return null;
            }
            if (JSONObject.parseObject(responseStr).getBoolean("status") != true) {
                throw new ServiceException(JSONObject.parseObject(responseStr).getString("message"));
            }
            JSONObject respJson = JSONObject.parseObject(JSONObject.parseObject(responseStr).getString("result"));
            String token = respJson.getString("token");
            String cookie = "wyandyy=" + token + ";" + session;
            log.info("{} cookie is {}", username, cookie);
            return cookie;
        } catch (Exception e) {
            log.error("获取cookies异常", e);
            throw new ServiceException(e.getMessage());
        }
    }

    @Cacheable(value = "testCaseCookie", key = "'ZZT_'+#key")
    public Map<String, String> getOpenIdZZT(String key, String secret) {
        try {
            Map<String, String> resultMap = new HashMap<>();
            String url = "https://sso.test.ztosys.com/oauth2/authorize?appid=ztvkqYM2xJU-6kFjUIxI4e4g&response_type=token&scope=userinfo,usercontact,user_id,node_id,usercert";
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("Authorization", secret);
            headerMap.put("Content-Type", "application/json");
            headerMap.put("X-Device-Id", "1419A1A2-A2AC-4DA2-871C-F7C808B9006F");
            JSONObject respJson = HttpUtils.doGet(url, headerMap, null);
            if (Objects.isNull(respJson)) {
                return null;
            }
            if (StringUtil.isEmpty(respJson.getString("access_token")) || StringUtil.isEmpty(respJson.getString("openid"))) {
                return null;
            }
            resultMap.put("X-Token", respJson.getString("access_token"));
            resultMap.put("X-OpenId", respJson.getString("openid"));
            return resultMap;
        } catch (Exception e) {
            log.error("获取cookies异常", e);
            throw new ServiceException("获取cookies异常");
        }
    }

    @Cacheable(value = "testCaseCookie", key = "'CustomerClient_'+#username")
    public String getCustomerClientToken(String username, String password) {
        log.info("getCustomerClientToken username : {}", username);
        try {
            String url = "https://collapsar.gw-test.ztosys.com/auth_account_loginByPassword";
            Map<String, String> params = new HashMap<>();
            params.put("userName", username);
            params.put("password", password);
            params.put("isAgainBind", "false");
            HttpResponse response = HttpUtils.doPostWithForm(url, params);
            if (Objects.isNull(response)) {
                throw new ServiceException("获取cookies异常");
            }
            String responseStr = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject respJson = JSONObject.parseObject(JSONObject.parseObject(responseStr).getString("result"));
            String token = respJson.getString("token");
            return token;
        } catch (Exception e) {
            log.error("获取cookies异常", e);
            throw new ServiceException("获取cookies异常");
        }
    }

    /**
     * 光合三方cookies
     *
     * @param key
     * @param secret
     * @return
     * @throws Exception
     */
    @Cacheable(value = "testCaseCookie", key = "'GHThird_'+#key")
    public String getCookieGHThird(String key, String secret) throws Exception {
        CloseableHttpClient httpClient = null;
        HttpGet httpGet;
        HttpPost httpPost;
        String data;
        CloseableHttpResponse response = null;
        String cookieStr = "";
        String lubanUrl = "https://sso.test.ztosys.com/oauth2/authorize?appid=zt5KKDQDIuWUyBDHxk1J2MbQ&redirect_uri" +
                "=https%3A%2F%2Fhappyworkorder-third.gw-test.ztosys.com%2FloginCallback%3Fns%3Dhappyworkorder-third%26redirect" +
                "&response_type=code&scope=userinfo,user_id,usercontact&state=suibiantian";
        RequestConfig defaultConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.STANDARD).build();
        try {
            httpClient = HttpClients.createDefault();
            CookieStore cookieStore = new BasicCookieStore();
            httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
            httpGet = new HttpGet(lubanUrl);
            httpGet.setHeader("X-Canvas-Fingerprint", "e42e3b3f3d4a6a8e37bee92b56db792b");
            httpGet.setHeader("Authorization", secret);
            httpGet.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.99 Safari/537.36 mobile");
            httpGet.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpGet.setConfig(defaultConfig);
            response = httpClient.execute(httpGet);
            HttpEntity httpEntity = response.getEntity();
            data = EntityUtils.toString(httpEntity, "utf-8");
            JSONObject object = JSONObject.parseObject(data);
            String ticket1 = object.getString("ticket_1");
            String url = "https://sso.test.ztosys.com/set-cookie?ticket=" + ticket1 + "&host=.ztosys.com&dt=483effc196dc42178a4de182d6b4e482_%2Fe7eSaKK7fhBQgERVQORvQ%2F6%2FACnxzPv";
            httpPost = new HttpPost(url);
            httpPost.setConfig(defaultConfig);
            response = httpClient.execute(httpPost);

            url = "https://sso.test.ztosys.com/whyzjzn?ticket=" + ticket1 + "&host=.ztosys.com&dt=483effc196dc42178a4de182d6b4e482_%2Fe7eSaKK7fhBQgERVQORvQ%2F6%2FACnxzPv";
            httpPost = new HttpPost(url);
            httpPost.setConfig(defaultConfig);
            response = httpClient.execute(httpPost);
            String gatewayRedirectUri = object.getString("redirect_uri");
            log.info("gatewayRedirectUri : {}", gatewayRedirectUri);
            // 获取网关的cookie
            String mobileSessionId = getGatewayMobileSessionId(gatewayRedirectUri);
            // 获取cookies信息
            List<Cookie> cookieList = cookieStore.getCookies();
            for (Cookie cookie : cookieList) {
                if (cookie.getName().equals("com.zto.sessionid")) {
                    cookieStr += "com.zto.sessionid=" + cookie.getValue() + ";";
                }
                if (cookie.getName().equals("com.zto.lid")) {
                    cookieStr += "com.zto.lid=" + cookie.getValue() + ";";
                }
                if (cookie.getName().equals("wyandyy")) {
                    cookieStr += "wyandyy=" + cookie.getValue() + ";";
                }
                if (cookie.getName().equals("wyzdzjxhdnh")) {
                    cookieStr += "wyzdzjxhdnh=" + cookie.getValue() + ";";
                }
            }
            cookieStr += "mobileSessionId=" + mobileSessionId;
            return cookieStr;
        } catch (Exception e) {
            log.error("getCookieThird error.", e);
            throw new Exception(e);
        } finally {
            if (null != response) {
                response.close();
            }
            if (null != httpClient) {
                httpClient.close();
            }
        }
    }

}
