package com.zto.devops.qc.infrastructure.gateway.http;

import com.zto.devops.qc.domain.gateway.http.HttpService;
import com.zto.devops.qc.infrastructure.gateway.util.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/9/22 10:55
 */
@Slf4j
@Component
public class HttpServiceImpl implements HttpService {

    @Override
    public String downloadFromUrl(String url, String dir) {
        return HttpUtils.downloadFromUrl(url, dir);
    }

    @Override
    public String doPost(String url, String jsonStr) {
        return HttpUtils.doPost(url, jsonStr);
    }

    @Override
    public String doPostWithToken(String url, String content, String token) {
        return HttpUtils.doPostWithToken(url, content, token);
    }

    @Override
    public String doPostWithHeader(String url, String content, Map<String, String> headerMap) {
        return HttpUtils.doPostWithHeader(url, content, headerMap);
    }

}
