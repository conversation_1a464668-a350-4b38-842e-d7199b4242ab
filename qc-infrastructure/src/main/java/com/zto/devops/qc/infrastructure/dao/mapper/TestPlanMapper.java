package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.framework.infrastructure.dao.BaseMapper;
import com.zto.devops.qc.client.model.report.entity.RelatedBaseVO;
import com.zto.devops.qc.client.model.report.entity.SafeTestPlanMsgVO;
import com.zto.devops.qc.client.model.report.query.RelatedQuery;
import com.zto.devops.qc.client.model.testPlan.query.TestPlanListQuery;
import com.zto.devops.qc.client.model.testPlan.query.VersionPlanVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.TestPlanDetailVO;
import com.zto.devops.qc.infrastructure.dao.entity.TestPlanEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TestPlanMapper extends BaseMapper<TestPlanEntity> {

    List<TestPlanEntity> selectTestPlanList(TestPlanListQuery query);

    List<VersionPlanVO> versionPlanQuery(@Param("versionCode") String versionCode);

    List<RelatedBaseVO> getSimplePlanVOByVersionCode(@Param("versionCode") String versionCode);

    TestPlanEntity selectByCode(@Param("planCode") String planCode);


    List<TestPlanEntity> selectTestPlanList();

    List<RelatedBaseVO> getSimplePlanVOByQuery(RelatedQuery query);

    List<SafeTestPlanMsgVO> getTestPlanWithoutSafePlan(@Param("approvalExitDate") Date approvalExitDate);

    List<TestPlanDetailVO> selectCommonListByReq(@Param("begin") Date begin, @Param("end") Date end);

    List<TestPlanDetailVO> selectOtherListByReq(@Param("begin") Date begin, @Param("end") Date end);
}