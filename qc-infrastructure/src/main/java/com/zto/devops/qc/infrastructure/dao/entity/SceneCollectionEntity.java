package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "tm_scene_collection")
public class SceneCollectionEntity {

    @Id
    private Long id;

    @Column(name = "scene_index_code")
    private String sceneIndexCode;

    @Column(name = "product_code")
    private String productCode;

    @Column(name = "favorite_type")
    private Integer favoriteType;

    @Column(name = "creator_id")
    private Long creatorId;

    @Column(name = "creator")
    private String creator;

    @Column(name = "gmt_create")
    private Date gmtCreate;

    @Column(name = "modifier_id")
    private Long modifierId;

    @Column(name = "modifier")
    private String modifier;

    @Column(name = "gmt_modified")
    private Date gmtModified;

}
