package com.zto.devops.qc.infrastructure.gateway.sso;

import com.zto.devops.qc.domain.gateway.sso.SsoService;
import com.zto.devops.qc.infrastructure.sso.KdcsSsoService;
import com.zto.devops.qc.infrastructure.sso.ZtoSsoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: wxc
 * @Description:
 * @Date: Create in 下午3:04 2021/4/25
 */
@Slf4j
@Component
public class SsoServiceImpl implements SsoService {

    @Autowired
    private ZtoSsoService ztoSsoService;

    @Autowired
    private KdcsSsoService kdcsSsoService;

    @Override
    public String getCookie(String key, String secret, String nameSpace) throws Exception {
        return ztoSsoService.getCookie(key, secret, nameSpace);
    }

    @Override
    public String getCookieGw(String key, String secret, String nameSpace) throws Exception {
        return ztoSsoService.getCookieGw(key, secret, nameSpace);
    }

    @Override
    public String getCookieKDGJ(String username, String password) {
        return ztoSsoService.getCookieKDGJ(username, password);
    }

    @Override
    public Map<String, String> getOpenIdZZT(String key, String secret) {
        return ztoSsoService.getOpenIdZZT(key, secret);
    }

    @Override
    public String getCustomerClientToken(String username, String password) {
        return ztoSsoService.getCustomerClientToken(username, password);
    }

    @Override
    public String getCookieGHThird(String key, String secret) throws Exception {
        return ztoSsoService.getCookieGHThird(key, secret);
    }

    @Override
    public Map<String, String> getKdcsLoginInfo(String key, String data) {
        return kdcsSsoService.kuaiChaoLogin(key, data);
    }


}
