package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanCaseStatusHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.TmTestPlanStageHandler;
import lombok.Getter;
import lombok.Setter;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@Table(name = "tm_testcase_execute_record")
public class TestcaseExecuteRecordEntity extends BaseEntity {

    @Id
    private Long id;

    /**
     * 测试用例code
     */
    private String testcaseCode;

    /**
     * 测试计划code
     */
    private String testPlanCode;

    /**
     * 测试阶段
     */
    @ColumnType(typeHandler = TmTestPlanStageHandler.class)
    private TestPlanStageEnum testStage;

    /**
     * 结果
     */
    @ColumnType(typeHandler = TmTestPlanCaseStatusHandler.class)
    private TestPlanCaseStatusEnum result;

    /**
     * 自动化测试任务code
     */
    private String automaticTaskCode;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date finishTime;

    /**
     * 执行结果文件
     */
    private String resultFile;

    /**
     * 执行日志文件
     */
    private String execLogFile;

    /**
     * 测试报告，一般是html格式
     */
    private String reportFile;

}
