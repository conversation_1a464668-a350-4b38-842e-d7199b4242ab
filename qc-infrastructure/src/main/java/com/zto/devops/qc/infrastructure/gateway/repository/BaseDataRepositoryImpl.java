package com.zto.devops.qc.infrastructure.gateway.repository;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Lists;
import com.site.lookup.util.StringUtils;
import com.zto.agent.base.api.customer.CustomerService;
import com.zto.agent.base.api.customer.response.GetCompanyInfoResponse;
import com.zto.base.UserService;
import com.zto.base.bean.BaseResponse;
import com.zto.base.bean.PageBean;
import com.zto.base.bean.response.UserResp;
import com.zto.devops.qc.client.model.rpc.huiyan.StoreBrandVO;
import com.zto.devops.qc.client.model.rpc.huiyan.StoreNameVO;
import com.zto.devops.qc.client.model.rpc.outlet.ItemTypeVO;
import com.zto.devops.qc.client.model.rpc.pipeline.UserRespVO;
import com.zto.devops.qc.client.model.rpc.waybill.LabelNameVO;
import com.zto.devops.qc.domain.gateway.repository.BaseDataRepository;
import com.zto.devops.qc.infrastructure.converter.BaseDataConvertor;
import com.zto.huiyan.terminal.facade.manage.AgentManageInfoFO;
import com.zto.huiyan.terminal.facade.manage.AgentManageInfoFacade;
import com.zto.huiyan.terminal.facade.manage.AgentManageInfoListByPageFI;
import com.zto.outlet.DictionaryService;
import com.zto.outlet.bean.OutletResponse;
import com.zto.outlet.bean.request.QueryDictionaryParam;
import com.zto.outlet.bean.response.TDictionaryResult;
import com.zto.shenzhou.common.model.PageResult;
import com.zto.titans.common.entity.Result;
import com.zto.waybill.tag.api.WaybillTagServiceI;
import com.zto.waybill.tag.dto.LabelAndValueCO;
import com.zto.waybill.tag.dto.query.AllTagQry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BaseDataRepositoryImpl implements BaseDataRepository {

    @Reference(group = "userService", version = "1.0.0", timeout = 3000, check = false)
    private UserService userService;

    @Reference(timeout = 3000, check = false)
    private WaybillTagServiceI waybillTagService;

    @Reference(timeout = 3000, check = false)
    private DictionaryService dictionaryService;

    @Reference(group = "huiyan-terminal", version = "1.0.0", timeout = 3000, check = false)
    private AgentManageInfoFacade agentManageInfoFacade;

    @Reference(timeout = 3000, check = false, version = "1.0")
    private CustomerService customerService;

    @Autowired
    private BaseDataConvertor baseDataConvertor;

    @Override
    public List<UserRespVO> queryUserAutoSearch(String keyWord) {
        if(StringUtils.isEmpty(keyWord)) {
            return Lists.newArrayList();
        }
        BaseResponse<PageBean<UserResp>> response = userService.autoSearch(keyWord, null, null, 1, 124);
        PageBean<UserResp> pageData = Optional.ofNullable(response).map(BaseResponse::getData).orElse(new PageBean());
        List<UserRespVO> resultList = Optional.ofNullable(pageData.getData())
                .orElse(Lists.newArrayList()).stream()
                .map(baseDataConvertor::convert)
                .collect(Collectors.toList());
        return resultList;
    }

    @Override
    public List<LabelNameVO> listAllTags() {
        MultiResponse<LabelAndValueCO<String>> response = waybillTagService.listAllTags(new AllTagQry());
        List<LabelNameVO> resultList = Optional.ofNullable(response.getData())
                .orElse(Lists.newArrayList()).stream()
                .map(baseDataConvertor::convert)
                .collect(Collectors.toList());
        return resultList;
    }

    @Override
    public List<ItemTypeVO> queryDictionaries(String categoryCode) {
        QueryDictionaryParam param = new QueryDictionaryParam();
        param.setCategoryCode(categoryCode);
        OutletResponse<List<TDictionaryResult>> response = dictionaryService.queryDictionaries(param);
        List<ItemTypeVO> resultList = Optional.ofNullable(response.getData())
                .orElse(Lists.newArrayList()).stream()
                .map(baseDataConvertor::convert)
                .collect(Collectors.toList());
        return resultList;
    }

    @Override
    public List<StoreBrandVO> queryStoreBrand() {
        Result<List<GetCompanyInfoResponse>> response = customerService.getCompanyInfoList();
        List<StoreBrandVO> resultList = Optional.ofNullable(response.getResult())
                .orElse(Lists.newArrayList()).stream()
                .map(baseDataConvertor::convert)
                .collect(Collectors.toList());
        return resultList;
    }

    @Override
    public List<StoreNameVO> queryStoreName(String keyWord, String companyCode) {
        AgentManageInfoListByPageFI pageFI = new AgentManageInfoListByPageFI();
        pageFI.setPageSize(50);
        if(StringUtils.isNotEmpty(keyWord)) {
            pageFI.setAgentName(keyWord);
        }
        if(StringUtils.isNotEmpty(companyCode)) {
            pageFI.setCompanyCode(companyCode);
        }
        Result<PageResult<AgentManageInfoFO>> response = agentManageInfoFacade.queryListByPage(pageFI);
        PageResult<AgentManageInfoFO> pageData = Optional.ofNullable(response).map(Result::getResult).orElse(new PageResult());
        List<StoreNameVO> resultList = Optional.ofNullable(pageData.getList())
                .orElse(Lists.newArrayList()).stream()
                .map(baseDataConvertor::nameConvert)
                .collect(Collectors.toList());
        return resultList;
    }

}
