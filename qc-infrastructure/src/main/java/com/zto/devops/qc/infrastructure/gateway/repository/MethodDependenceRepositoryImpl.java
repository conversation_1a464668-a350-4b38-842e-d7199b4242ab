package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.model.dto.MethodDependenceEntityDO;
import com.zto.devops.qc.domain.gateway.repository.MethodDependenceRepository;
import com.zto.devops.qc.infrastructure.converter.MethodDependenceEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.MethodDependenceEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.MethodDependenceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class MethodDependenceRepositoryImpl implements MethodDependenceRepository {

    @Autowired
    private MethodDependenceMapper methodDependenceMapper;
    @Autowired
    private MethodDependenceEntityConverter converter;

    @Override
    public Integer deleteHistoryData(String versionCode, String appId, Integer limit) {
        return methodDependenceMapper.batchDelete(versionCode, appId, limit);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW, timeout = 300)
    @Override
    public void batchSaveInTransaction(List<MethodDependenceEntityDO> batch, int batchIndex, int totalBatches, String tag) {
        if (batch == null || batch.isEmpty()) {
            log.warn("batchSaveMethodDependencies_tag:{},分段 {}/{} 数据为空，跳过处理", tag, batchIndex, totalBatches);
            return;
        }
        try {
            methodDependenceMapper.batchSave(converter.covert2EntityList(batch));
        } catch (Exception e) {
            throw new ServiceException(String.format("batchSaveMethodDependencies_tag:[%s]，" +
                            "分段[%s/%s]保存失败，当前批次数据量[%s]，将回滚该分段的所有数据，errorMsg:[%s]",
                    tag, batchIndex, totalBatches, batch.size(), e.getMessage()), e);
        }
    }

    @Override
    public List<String> queryAffectedEntryCodeList(MethodDependenceEntityDO entityDO) {
        Example example = new Example(MethodDependenceEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("enable", 1);
        if (StringUtil.isNotEmpty(entityDO.getAppId())) {
            criteria.andEqualTo("appId", entityDO.getAppId());
        }
        if (StringUtil.isNotEmpty(entityDO.getVersionCode())) {
            criteria.andEqualTo("versionCode", entityDO.getVersionCode());
        }
        if (StringUtil.isNotEmpty(entityDO.getCommitId())) {
            criteria.andEqualTo("commitId", entityDO.getCommitId());
        }
        if (StringUtil.isNotEmpty(entityDO.getFullClassName())) {
            criteria.andEqualTo("fullClassName", entityDO.getFullClassName());
        }
        if (StringUtil.isNotEmpty(entityDO.getMethodName())) {
            criteria.andEqualTo("methodName", entityDO.getMethodName());
        }
        if (StringUtil.isNotEmpty(entityDO.getMethodDesc())) {
            criteria.andEqualTo("methodDesc", entityDO.getMethodDesc());
        }
        if (StringUtil.isNotEmpty(entityDO.getMethodParameterStr())) {
            criteria.andEqualTo("methodParameterStr", entityDO.getMethodParameterStr());
        }
        List<MethodDependenceEntity> list = methodDependenceMapper.selectByExample(example);
        return Optional.ofNullable(list)
                .orElse(Collections.emptyList())
                .stream().map(MethodDependenceEntity::getEntryMethodCode)
                .collect(Collectors.toList());
    }

    @Override
    public List<MethodDependenceEntityDO> queryAffectedInterfaceEntity(List<String> entryCodeList) {
        Example example = new Example(MethodDependenceEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("enable", 1);
        if (CollectionUtil.isNotEmpty(entryCodeList)) {
            criteria.andIn("methodCode", entryCodeList);
        }
        List<MethodDependenceEntity> list = methodDependenceMapper.selectByExample(example);
        return converter.covertList(list);
    }

    @Override
    public Integer queryCountByCommitId(String commitId) {
        Example example = new Example(MethodDependenceEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("enable", 1)
                .andEqualTo("commitId", commitId);
        return methodDependenceMapper.selectCountByExample(example);
    }
}
