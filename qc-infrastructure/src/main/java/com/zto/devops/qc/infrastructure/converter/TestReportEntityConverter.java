package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.ReviewInfoEntityDO;
import com.zto.devops.qc.client.model.dto.TestReportEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestReportEntityDO;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.*;
import com.zto.devops.qc.client.model.testmanager.report.event.*;
import com.zto.devops.qc.infrastructure.dao.entity.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TestReportEntityConverter {

    TestReportEntityDO convert(TestReportEntity entity);

    List<TestReportEntityDO> convertList(List<TestReportEntity> entityList);

    @Mapping(target = "code",expression = "java(com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.SNOWFLAKE))")
    AttachmentEntity convertAttachments(AttachmentVO attachments);

    List<AttachmentEntity> convertAttachments(List<AttachmentVO> attachments);

    TmReviewInfoEntity convertReviewInfo(ReviewInfoDTO reviewInfo);

    List<TmReviewRenewalEntity> convertReviewRenwals(List<ReviewRenewalVO> reviewRenewals);

    List<TmReviewOpinionEntity> convertReviewOpinions(List<ReviewOpinionVO> reviewOpinions);

    @Mapping(target = "codeCoverResult",source = "coverageResult")
    @Mapping(target = "codeCoverReason",expression = "java(com.zto.devops.framework.common.util.CollectionUtil.isNotEmpty(event.getCoverageReasonVOS()) ? com.alibaba.fastjson.JSON.toJSONString(event.getCoverageReasonVOS()) : \"\")")
    TmTestReportEntity convert(TmPermitReportAddEvent event);

    @Mapping(target = "codeCoverResult",source = "coverageResult")
    @Mapping(target = "codeCoverReason",expression = "java(com.zto.devops.framework.common.util.CollectionUtil.isNotEmpty(event.getCoverageReasonVOS()) ? com.alibaba.fastjson.JSON.toJSONString(event.getCoverageReasonVOS()) : \"\")")
    TmTestReportEntity convert(TmPermitReportEditedEvent event);

    List<ModuleTestEntity> convertModuleTests(List<TmModuleTestVO> moduleTests);

    @Mapping(target = "code",expression = "java(com.zto.devops.framework.infrastructure.util.AggregateIdUtil.generateId(com.zto.devops.framework.client.enums.AggregateType.SNOWFLAKE))")
    ModuleTestEntity convert(TmModuleTestVO moduleTest);

    @Mapping(target = "codeCoverResult",source = "coverageResult")
    @Mapping(target = "codeCoverReason",expression = "java(com.zto.devops.framework.common.util.CollectionUtil.isNotEmpty(event.getCoverageReasonVOS()) ? com.alibaba.fastjson.JSON.toJSONString(event.getCoverageReasonVOS()) : \"\")")
    TmTestReportEntity convert(SimpleReportEvent event);

    ReportBaseEvent convertReportBaseEvent(SimpleReportEvent event);

    ReviewInfoEntityDO converter(TmReviewInfoEntity tmReviewInfoEntity);

    TmTestReportEntityDO converter(TmTestReportEntity entity);

    List<ReviewOpinionVO> convertReviewOpinion(List<TmReviewOpinionEntity> tmReviewOpinionEntities);

    List<ReviewRenewalVO> convertReviewRenewal(List<TmReviewRenewalEntity> tmReviewRenewalEntities);

    TmTestReportEntity convert(ReviewReportAddedEvent event);

    ReportBaseEvent convertReportBaseEvent(ReviewReportAddedEvent event);

    TmTestReportEntity convert(ReviewReportEditedEvent event);

    ReportBaseEvent convertReportBaseEvent(ReviewReportEditedEvent event);

    TmTestReportEntity convert(ExternalReportAddEvent event);

    ReportBaseEvent convertReportBaseEvent(ExternalReportAddEvent event);

    TmTestReportEntity convert(ExternalReportEditEvent event);

    ReportBaseEvent convertReportBaseEvent(ExternalReportEditEvent event);

    TmTestReportEntity convert(MobileTestReportAddedEvent event);

    ReportBaseEvent convertReportBaseEvent(MobileTestReportAddedEvent event);

    TmTestReportEntity convert(MobileTestReportEditEvent event);

    ReportBaseEvent convertReportBaseEvent(MobileTestReportEditEvent event);

    List<TmTestReportEntityDO> convert2DOS(List<TmTestReportEntity> entity);
}
