package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.entity.action.ActionLogDO;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.domain.gateway.repository.IEventLogRepository;
import com.zto.devops.qc.client.model.dto.TestcaseRelationEntityDO;
import com.zto.devops.qc.client.model.issue.entity.CaseVO;
import com.zto.devops.qc.client.model.issue.entity.RelationTestcaseListVO;
import com.zto.devops.qc.client.model.issue.event.TestcaseRelationAddEvent;
import com.zto.devops.qc.client.model.issue.query.RelationTestcaseListQuery;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseByBusinessCodeVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseIssueVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseRelationVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListRelationIssueQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListRelationRequirementQuery;
import com.zto.devops.qc.domain.gateway.repository.ITestcaseRelationRepository;
import com.zto.devops.qc.infrastructure.converter.TestcaseRelationEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseRelationEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TestcaseMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.TestcaseRelationMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.TmTestPlanCaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/3/17
 * @Version 1.0
 */
@Component
@Slf4j
public class TestcaseRelationRepositoryImpl implements ITestcaseRelationRepository {

    @Autowired
    private TestcaseRelationMapper testcaseRelationMapper;
    @Autowired
    private TestcaseMapper testcaseMapper;

    @Autowired
    private TmTestPlanCaseMapper tmTestPlanCaseMapper;

    @Autowired
    private IEventLogRepository eventLogManager;

    @Autowired
    private TestcaseRelationEntityConverter testcaseRelationEntityConverter;

    @Override
    public List<TestcaseByBusinessCodeVO> selectTestCaseByCodeList(List<String> codeList, DomainEnum domain) {
        return testcaseRelationMapper.selectTestCaseByCodeList(codeList, domain);
    }

    @Override
    public List<CaseVO> getCaseNameList(String code) {
        return testcaseRelationMapper.getCaseNameList(code);
    }

    @Override
    public void addTestCase(TestcaseRelationAddEvent event) {
        Example example = new Example(TestcaseRelationEntity.class);
        example.createCriteria().andEqualTo("businessCode", event.getBusinessCode());
        testcaseRelationMapper.deleteByExample(example);

        if (null == event.getTestcaseCodes() || !(event.getTestcaseCodes().size() > 0)) {
            log.info("清空当前关联关系");
            return;
        }

        List<ActionLogDO> logs = new ArrayList<>();

        for (String testcaseCode : event.getTestcaseCodes()) {
            TestcaseRelationEntity testcaseRelationEntity = new TestcaseRelationEntity();
            testcaseRelationEntity.setTestcaseCode(testcaseCode);
            testcaseRelationEntity.setBusinessCode(event.getBusinessCode());
            testcaseRelationEntity.setDomain(DomainEnum.ISSUE);
            testcaseRelationEntity.preUpdate(event);
            testcaseRelationMapper.insertSelective(testcaseRelationEntity);

            ActionLogDO log = new ActionLogDO(
                    testcaseCode,
                    "新建了缺陷",
                    "",
                    TestcaseRelationAddEvent.class.getName(),
                    event.getTransactor().getUserName(),
                    event.getTransactor().getUserId());
            logs.add(log);
        }

        List<String> operateCaseCodeList = tmTestPlanCaseMapper.selectOperateCaseCodeByVersionCode(
                event.getTestcaseCodes(), event.getVersionCode());
        if (null != operateCaseCodeList && operateCaseCodeList.size() > 0) {
            for (String operateCaseCode : operateCaseCodeList) {
                ActionLogDO log = new ActionLogDO(
                        operateCaseCode,
                        "新建了缺陷",
                        "",
                        TestcaseRelationAddEvent.class.getName(),
                        event.getTransactor().getUserName(),
                        event.getTransactor().getUserId());
                logs.add(log);
            }
        }

        //eventLogManager.createLogs(logs);
    }

    @Override
    public List<RelationTestcaseListVO> getRelationTestcaseList(RelationTestcaseListQuery query) {
        log.info("RelationTestcaseListQuery >>> {}", query.getCode());
        Example example = new Example(TestcaseRelationEntity.class);
        example.setDistinct(true);
        example.createCriteria()
                .andEqualTo("businessCode", query.getCode())
                .andEqualTo("domain", "ISSUE")
                .andEqualTo("enable", Boolean.TRUE);
        example.setOrderByClause("gmt_create desc");
        List<TestcaseRelationEntity> testcaseRelationEntities = testcaseRelationMapper.selectByExample(example);
        List<String> list = testcaseRelationEntities.stream().distinct().map(TestcaseRelationEntity::getTestcaseCode).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        Example caseExample = new Example(TestcaseEntity.class);

        caseExample.setDistinct(true);
        caseExample.createCriteria()
                .andIn("code", list)
                .andEqualTo("enable", Boolean.TRUE);
        example.setOrderByClause("gmt_create desc");
        List<TestcaseEntity> testcaseEntities = testcaseMapper.selectByExample(caseExample);

        if (CollectionUtil.isEmpty(testcaseEntities)) {
            return new ArrayList<>();
        }
        List<RelationTestcaseListVO> voList = new LinkedList<>();
        for (TestcaseEntity testcaseEntity : testcaseEntities) {
            RelationTestcaseListVO vo = new RelationTestcaseListVO();
            vo.setCode(testcaseEntity.getCode());
            vo.setTitle(testcaseEntity.getName());
            vo.setStatus(testcaseEntity.getStatus().name());
            vo.setStatusDesc(testcaseEntity.getStatus().getDesc());
            vo.setCaseType(testcaseEntity.getType().name());
            vo.setCaseTypeDesc(testcaseEntity.getType().getValue());
            vo.setDutyUser(testcaseEntity.getDutyUser());
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public void addTestcaseRelationList(List<TestcaseRelationVO> list, String testcaseCode, BaseEvent event) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<TestcaseRelationEntity> entityList = testcaseRelationEntityConverter.convertList(list);
        for (TestcaseRelationEntity en : entityList) {
            en.preCreate(event);
            en.setBusinessCode(en.getBusinessCode());
            en.setTestcaseCode(testcaseCode);
            testcaseRelationMapper.insertSelective(en);
        }
    }

    @Override
    public List<TestcaseIssueVO> selectIssueByTestcaseCode(ListRelationIssueQuery query) {
        log.info("ListRelationIssueQuery >>> {}", query.getCode());
        return testcaseRelationMapper.selectIssueByTestcaseCode(query.getCode(), DomainEnum.ISSUE);
    }

    @Override
    public List<TestcaseRelationEntityDO> selectTestcaseRelationEntities(ListRelationRequirementQuery query) {
        Example example = new Example(TestcaseRelationEntity.class);
        example.setDistinct(true);
        example.createCriteria()
                .andIn("testcaseCode", query.getCodeList())
                .andEqualTo("domain", query.getDomain())
                .andEqualTo("enable", Boolean.TRUE);
        example.setOrderByClause("gmt_create desc");
        return testcaseRelationEntityConverter.convertDOList(testcaseRelationMapper.selectByExample(example));
    }
}
