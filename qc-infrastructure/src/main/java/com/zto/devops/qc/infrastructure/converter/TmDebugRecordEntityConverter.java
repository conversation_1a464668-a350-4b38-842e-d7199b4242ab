package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TmDebugRecordEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.TmDebugRecordEntity;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface TmDebugRecordEntityConverter {

    TmDebugRecordEntity convertEntity(TmDebugRecordEntityDO entityDO);

    TmDebugRecordEntityDO convertEntityDO(TmDebugRecordEntity entity);

}
