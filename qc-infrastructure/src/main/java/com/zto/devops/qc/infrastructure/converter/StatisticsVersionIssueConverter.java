package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.StatisticsVersionIssueEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.StatisticsVersionIssueEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface StatisticsVersionIssueConverter {

    List<StatisticsVersionIssueEntityDO> convert2DO(List<StatisticsVersionIssueEntity> entity);
}
