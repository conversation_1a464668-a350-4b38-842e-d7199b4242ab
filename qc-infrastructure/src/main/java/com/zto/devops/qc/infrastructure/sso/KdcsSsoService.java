package com.zto.devops.qc.infrastructure.sso;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.infrastructure.config.KdcsSsoConfig;
import com.zto.devops.qc.infrastructure.gateway.util.HttpUtils;
import com.zto.iam.sdk.DefaultIamClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 快超SSO服务 - 基于JMX文件的真实实现
 * 实现快超系统的完整HTTP认证登录流程
 *
 * <AUTHOR>
 * @create 2024/12/20
 */
@Component
@Slf4j
public class KdcsSsoService {

    // 设备类型常量 - 根据快超系统的设备分类
    public static final int EQUIPMENT_TYPE_SCANNER = 1;      // 其他设备
    public static final int EQUIPMENT_TYPE_CAMERA = 2;     // 高拍仪设备
    @Autowired
    private KdcsSsoConfig kdcsSsoConfig;

    /**
     * 快超登录流程
     * 基于JMX文件中的HTTP接口调用流程：
     * 1. 解析JSON格式的登录数据
     * 2. BeanShell脚本进行IAM RSA加密
     * 3. 一账通登录接口获取用户信息和Token
     * 4. 高拍仪SN登录获取设备Token
     *
     * @param key  登录标识键
     * @param data JSON格式的登录数据，包含mobile、password、snCode、equipmentType
     * @return 登录结果包含tokens和用户信息
     */
    @Cacheable(value = "testCaseCookie", key = "'KDCS_'+#key")
    public Map<String, String> kuaiChaoLogin(String key, String data) {
        try {
            log.info("开始快超登录流程，key: {}, data: {}", key, data);

            byte[] decodedBytes = Base64.getDecoder().decode(data);
            String value = new String(decodedBytes);
            // 1. 解析JSON数据并验证必填参数
            JSONObject loginData = JSONObject.parseObject(value);
            String mobile = loginData.getString("mobile");
            String password = loginData.getString("password");
            String snCode = loginData.getString("snCode");
            Integer equipmentType = loginData.getInteger("equipmentType");

            // 验证所有参数都不能为空
            if (StringUtil.isBlank(mobile)) {
                throw new ServiceException("mobile参数不能为空");
            }
            if (StringUtil.isBlank(password)) {
                throw new ServiceException("password参数不能为空");
            }
            if (equipmentType == null) {
                throw new ServiceException("equipmentType参数不能为空");
            }

            log.info("登录参数解析成功，mobile: {}, snCode: {}, equipmentType: {}", mobile, snCode, equipmentType);

            // 2. IAM SDK RSA加密 - 按照JMX的BeanShell脚本
            String authorization = encryptCredentials(mobile, password);

            // 3. 一账通登录 - 对应JMX中的"一账通登录"HTTP请求（固定使用测试环境）
            Map<String, Object> userInfo = accountLogin(authorization);
            String accessToken = userInfo.get("accessToken").toString();
            String userId = userInfo.get("userId").toString();
            String staffCode = userInfo.get("staffCode").toString();
            String unionId = userInfo.get("unionId").toString();

            // 4. 高拍仪SN登录 - 对应JMX中的"高拍仪SN登录"
            Map<String, String> deviceTokens = snLogin(mobile, snCode, equipmentType);

            // 组装返回结果
            Map<String, String> result = new HashMap<>();
            result.put("accessToken", accessToken);
            result.put("userId", userId);
            result.put("staffCode", staffCode);
            result.put("unionId", unionId);
            result.put("mobile", mobile);
            result.put("snCode", snCode);
            result.put("equipmentType", String.valueOf(equipmentType));
            result.put("loginSuccess", "true");

            if (deviceTokens != null) {
                result.put("deviceToken", deviceTokens.get("token"));
                result.put("refreshToken", deviceTokens.get("refreshToken"));
            }

            log.info("快超登录成功，userId: {}, staffCode: {}, unionId: {}, equipmentType: {}", userId, staffCode, unionId, equipmentType);
            return result;

        } catch (Exception e) {
            log.error("快超登录失败", e);
            throw new ServiceException("快超登录失败: " + e.getMessage());
        }
    }

    /**
     * 步骤1: IAM SDK RSA加密用户凭据
     * 完全按照JMX脚本的BeanShell代码逻辑：
     * client.RSAEncrypt("${mobile} ${password}")
     */
    private String encryptCredentials(String mobile, String password) {
        try {
            log.debug("开始IAM RSA加密，mobile: {}", mobile);

            // 创建IAM客户端 - 使用配置类获取参数
            DefaultIamClient iamClient = new DefaultIamClient(
                    kdcsSsoConfig.getIamUrl(),
                    kdcsSsoConfig.getAppId(),
                    kdcsSsoConfig.getAppSecret()
            );

            // 按照JMX脚本: client.RSAEncrypt("18817543989 Zto714517")
            String credentials = mobile + " " + password;
            String authorization = iamClient.RSAEncrypt(credentials);

            log.debug("IAM加密完成，加密后长度: {}", authorization.length());
            return authorization;

        } catch (Exception e) {
            log.error("IAM加密失败", e);
            throw new ServiceException("用户凭据加密失败: " + e.getMessage());
        }
    }

    /**
     * 步骤2: 一账通登录
     * 对应JMX脚本中的"一账通登录"HTTP请求
     * URL: gateway.do
     * X-Zop-Name: tuxi.spm.account.accountLoginByPwd
     */
    private Map<String, Object> accountLogin(String authorization) {
        try {
            log.debug("开始一账通登录");

            String baseUrl = kdcsSsoConfig.getGatewayUrl();  // 从配置获取网关地址

            // 按照JMX脚本构造请求体 - 对应"一账通登录"的JSON数据结构
            Map<String, Object> data = new HashMap<>();
            data.put("platformName", kdcsSsoConfig.getPlatformName());
            data.put("deviceId", kdcsSsoConfig.getDeviceId());
            data.put("authorization", authorization);
            data.put("verifyId", "");
            data.put("deviceName", kdcsSsoConfig.getDeviceName());

            Map<String, Object> request = new HashMap<>();
            request.put("data", data);

            // 调用HTTP网关 - 按照JMX脚本的配置
            String url = baseUrl + kdcsSsoConfig.getGatewayPath();
            String requestBody = JSONObject.toJSONString(request);

            // 设置HTTP Headers - 按照JMX脚本的HeaderManager配置
            Map<String, String> headers = new HashMap<>();
            headers.put("X-Zop-Name", kdcsSsoConfig.getAccountLoginApiName());
            headers.put("X-Ca-Version", kdcsSsoConfig.getApiVersion());
            headers.put("Content-Type", "application/json");
            // 添加JMeter中的User-Agent - 这很重要用于服务端识别客户端类型
            headers.put("User-Agent", kdcsSsoConfig.getUserAgent());

            log.debug("一账通登录请求URL: {}", url);
            log.debug("一账通登录请求体: {}", requestBody);

            String responseStr = HttpUtils.doPostWithHeader(url, requestBody, headers);
            log.debug("一账通登录响应: {}", responseStr);

            JSONObject responseJson = JSONObject.parseObject(responseStr);
            log.debug("解析后的响应JSON: {}", responseJson);

            // 检查响应状态 - 修正为使用正确的字段名
            Boolean status = responseJson.getBoolean("status");
            String message = responseJson.getString("message");
            String statusCode = responseJson.getString("statusCode");
            log.debug("响应状态: status={}, message={}, statusCode={}", status, message, statusCode);

            // 改进判断逻辑：status为false才是失败，或者message包含明确的错误信息
            if (status != null && !status) {
                throw new ServiceException("一账通登录失败: " + message);
            }

            // 如果status为null或true，但message包含错误关键词，也视为失败
            if (message != null && (
                    message.contains("失败") ||
                            message.contains("错误") ||
                            message.contains("异常") ||
                            message.contains("手机号或密码错误") ||
                            message.contains("账号不存在") ||
                            message.contains("密码错误")
            )) {
                throw new ServiceException("一账通登录失败: " + message);
            }

            // 提取用户信息 - 使用正确的result字段而不是data字段
            JSONObject result_response = responseJson.getJSONObject("result");
            if (result_response == null) {
                throw new ServiceException("一账通登录失败: 响应数据为空，可能登录未成功");
            }

            log.debug("响应数据: {}", result_response);
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("accessToken", result_response.getString("accessToken"));
            userInfo.put("userId", result_response.getLong("userId"));
            userInfo.put("staffCode", result_response.getString("staffCode"));
            userInfo.put("unionId", result_response.getString("unionId"));

            log.debug("一账通登录成功，userId: {}, staffCode: {}, unionId: {}",
                    userInfo.get("userId"), userInfo.get("staffCode"), userInfo.get("unionId"));
            return userInfo;

        } catch (Exception e) {
            log.error("一账通登录失败", e);
            throw new ServiceException("账户登录验证失败: " + e.getMessage());
        }
    }

    /**
     * 步骤3: 高拍仪SN登录
     * 对应JMX脚本中的"高拍仪SN登录_TG220485362388"HTTP请求
     * URL: gateway.do
     * X-Zop-Name: snLogin
     */
    private Map<String, String> snLogin(String mobile, String snCode, int equipmentType) {
        try {
            log.debug("开始高拍仪SN登录，mobile: {}, snCode: {}", mobile, snCode);

            String baseUrl = kdcsSsoConfig.getGatewayUrl();  // 从配置获取网关地址

            // 按照JMX脚本构造请求体 - 对应"高拍仪SN登录"的JSON数据结构
            Map<String, Object> data = new HashMap<>();
            data.put("accout", mobile);  // 注意：JMX中确实是accout，不是account
            data.put("equipmentType", equipmentType);
            data.put("snCode", snCode);

            Map<String, Object> request = new HashMap<>();
            request.put("data", data);

            // 调用HTTP网关 - 按照JMX脚本的配置
            String url = baseUrl + kdcsSsoConfig.getGatewayPath();
            String requestBody = JSONObject.toJSONString(request);

            // 设置HTTP Headers - 按照JMX脚本的HeaderManager配置
            Map<String, String> headers = new HashMap<>();
            headers.put("X-Zop-Name", kdcsSsoConfig.getSnLoginApiName());
            headers.put("X-Ca-Version", kdcsSsoConfig.getApiVersion());
            headers.put("Content-Type", "application/json");
            // 添加JMeter中的User-Agent
            headers.put("User-Agent", kdcsSsoConfig.getUserAgent());

            log.debug("SN登录请求URL: {}", url);
            log.debug("SN登录请求体: {}", requestBody);

            String responseStr = HttpUtils.doPostWithHeader(url, requestBody, headers);
            log.debug("SN登录响应: {}", responseStr);

            JSONObject responseJson = JSONObject.parseObject(responseStr);
            log.debug("解析后的SN登录响应JSON: {}", responseJson);

            // 检查响应状态 - 使用正确的status字段
            Boolean status = responseJson.getBoolean("status");
            String message = responseJson.getString("message");
            String statusCode = responseJson.getString("statusCode");
            log.debug("SN登录响应状态: status={}, message={}, statusCode={}", status, message, statusCode);

            // 改进判断逻辑：status为false才是失败，或者message包含明确的错误信息
            if (status != null && !status) {
                throw new ServiceException("SN设备登录失败: " + message);
            }

            // 如果status为null或true，但message包含错误关键词，也视为失败
            if (message != null && (
                    message.contains("失败") ||
                            message.contains("错误") ||
                            message.contains("异常") ||
                            message.contains("设备不存在") ||
                            message.contains("SN码错误") ||
                            message.contains("设备未授权")
            )) {
                throw new ServiceException("SN设备登录失败: " + message);
            }

            // 提取token信息 - 使用正确的result字段
            JSONObject result_response = responseJson.getJSONObject("result");
            if (result_response == null) {
                throw new ServiceException("SN设备登录失败: 响应数据为空，可能登录未成功");
            }

            log.debug("SN登录响应数据: {}", result_response);
            Map<String, String> tokens = new HashMap<>();
            tokens.put("token", result_response.getString("token"));
            tokens.put("refreshToken", result_response.getString("refreshToken"));

            log.debug("SN设备登录成功，token: {}, refreshToken: {}",
                    tokens.get("token"), tokens.get("refreshToken"));
            return tokens;

        } catch (Exception e) {
            log.error("SN设备登录失败", e);
            throw new ServiceException("设备登录验证失败: " + e.getMessage());
        }
    }


}