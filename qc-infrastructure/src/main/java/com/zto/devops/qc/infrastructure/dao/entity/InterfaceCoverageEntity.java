package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.jarscan.enums.MethodTypeEnum;
import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@ToString
@Data
@Table(name = "qc_interface_coverage")
public class InterfaceCoverageEntity extends BaseEntity {

    @Column(name = "id")
    private Long id;

    /**
     * 接口覆盖率编号
     */
    @Column(name = "interface_coverage_code")
    private String interfaceCoverageCode;

    /**
     * 版本code
     */
    @Column(name = "version_code")
    private String versionCode;

    /**
     * 版本名称
     */
    @Column(name = "version_name")
    private String versionName;

    /**
     * appId
     */
    @Id
    @Column(name = "app_id")
    private String appId;

    /**
     * 提交记录id
     */
    @Column(name = "commit_id")
    private String commitId;

    /**
     * 接口文档地址
     */
    @Column(name = "interface_doc_address")
    private String interfaceDocAddress;

    /**
     * 全路径类名称
     */
    @Column(name = "interface_full_class_name")
    private String interfaceFullClassName;

    /**
     * 接口方法名称
     */
    @Column(name = "interface_method_name")
    private String interfaceMethodName;

    /**
     * 接口方法描述
     */
    @Column(name = "interface_method_desc")
    private String interfaceMethodDesc;

    /**
     * 接口方法类型
     */
    @Column(name = "interface_method_type")
    private String interfaceMethodType;

    /**
     * 接口方法注解
     */
    @Column(name = "interface_method_annotation")
    private String interfaceMethodAnnotation;

    /**
     * 接口线上调用数量
     */
    @Column(name = "interface_call_number")
    private Integer interfaceCallNumber;

    /**
     * 接口线上错误数量
     */
    @Column(name = "interface_error_number")
    private Integer interfaceErrorNumber;

    /**
     * 是否覆盖
     */
    @Column(name = "is_covered")
    private Integer isCovered;

    /**
     * 变更方法类名称
     */
    @Column(name = "modify_class_name")
    private String modifyClassName;

    /**
     * 变更方法名称
     */
    @Column(name = "modify_method_name")
    private String modifyMethodName;

    /**
     * 变更方法描述
     */
    @Column(name = "modify_method_desc")
    private String modifyMethodDesc;


    /**
     * 变更方法MD5值
     */
    @Column(name = "modify_method_md5")
    private String modifyMethodMd5;

    /**
     * 接口别名
     */
    @Column(name = "interface_alias")
    private String interfaceAlias;

    /**
     * 方法参数
     */
    @Column(name = "method_parameter_str")
    private String methodParameterStr;

    /**
     * zcat数据源metricKey字段
     */
    @Column(name = "zcat_metric_key")
    private String zcatMetricKey;

}
