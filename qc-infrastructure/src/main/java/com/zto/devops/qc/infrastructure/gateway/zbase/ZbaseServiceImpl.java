package com.zto.devops.qc.infrastructure.gateway.zbase;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.*;
import com.zto.devops.qc.client.service.testmanager.apitest.model.QueryZBaseESListReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.listESQueryReq;
import com.zto.devops.qc.domain.gateway.http.HttpService;
import com.zto.devops.qc.domain.gateway.metadata.MetaDataService;
import com.zto.devops.qc.domain.gateway.zbase.ZbaseService;
import com.zto.devops.qc.infrastructure.config.ZbaseConfig;
import com.zto.titans.common.util.JsonUtil;
import com.zto.utils.HMacSHA1Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @create 2023/7/31 10:22
 */
@Slf4j
@Component
public class ZbaseServiceImpl implements ZbaseService {

    @Autowired
    private ZbaseConfig zbaseConfig;
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private HttpService httpService;

    @Override
    public JSONArray queryProductDb(String productCode) {
        log.info("获取产品下所有逻辑数据库。{}", productCode);
        String url = String.format(zbaseConfig.queryProductDbUrl(), productCode);
        Map<String, String> reqMap = new HashMap<>();
        reqMap.put("x-token", zbaseConfig.queryDbToken());
        JSONObject result = doGet(url, reqMap);
        if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getJSONArray("result"))) {
            return null;
        }
        log.info("old result --> {}", JsonUtil.toJSON(result.getJSONArray("result")));
        result.getJSONArray("result").stream().forEach((Object obj) -> {
            if (obj instanceof JSONObject) {
                JSONObject jsonObject = (JSONObject) obj;
                JSONArray physicsDbArray = jsonObject.getJSONArray("dblresourceRelatePhysicsDbList");
                if (CollectionUtil.isNotEmpty(physicsDbArray)) {
                    if (physicsDbArray.size() == 1) {
                        jsonObject.put("physicDbName", physicsDbArray.getJSONObject(0).getString("schema"));
                    } else if (physicsDbArray.size() > 1) {
                        JSONObject physicsDbObj = (JSONObject) physicsDbArray.stream().filter(physicsDb -> {
                            if (null != ((JSONObject) physicsDb).getBoolean("isDefault")) {
                                return ((JSONObject) physicsDb).getBoolean("isDefault");
                            }
                            return false;
                        }).findAny().orElse(null);
                        jsonObject.put("physicDbName", physicsDbObj != null ? physicsDbObj.getString("schema") : "");
                    } else {
                        jsonObject.put("physicDbName", "");
                    }
                } else {
                    jsonObject.put("physicDbName", "");
                }
            }
        });
        log.info("new result --> {}", JsonUtil.toJSON(result.getJSONArray("result")));
        return result.getJSONArray("result");
    }

    @Override
    public JSONObject queryPhysicalDbInfo(String id) {
        log.info("获取物理库信息。{}", id);
        String url = String.format(zbaseConfig.queryPhysicalDbInfoUrl(), id);
        Map<String, String> reqMap = new HashMap<>();
        reqMap.put("x-token", zbaseConfig.queryDbToken());
        JSONObject result = doGet(url, reqMap);
        if (Objects.isNull(result)) {
            return null;
        }
        return result.getJSONObject("result");
    }

    @Override
    public JSONObject queryDbAccountInfo(JSONObject reqObject) {
        log.info("获取数据库账号信息请求参数。{}", reqObject);
        if (Objects.isNull(reqObject)) {
            return null;
        }

        String productCode = reqObject.getString("productCode");
        JSONArray relatePhysicsDbList = reqObject.getJSONArray("dblresourceRelatePhysicsDbList");
        if (StringUtil.isEmpty(productCode) || CollectionUtil.isEmpty(relatePhysicsDbList)) {
            return null;
        }

        JSONObject dbObject = relatePhysicsDbList.getJSONObject(0);
        String env = dbObject.getString("env");
        String masterDb = dbObject.getString("masterDb");
        String schema = dbObject.getString("schema");
        if (StringUtil.isEmpty(env) || StringUtil.isEmpty(masterDb) || StringUtil.isEmpty(schema)) {
            return null;
        }

        String url = String.format(zbaseConfig.queryDbAccountInfoUrl(), env, productCode, masterDb, schema);
        Map<String, String> reqMap = new HashMap<>();
        reqMap.put("x-token", zbaseConfig.queryAccountInfoToken());
        JSONObject resultObject = doGet(url, reqMap);
        if (Objects.isNull(resultObject)) {
            resultObject = new JSONObject();
        }

        resultObject.put("ip", masterDb);
        resultObject.put("schema", schema);
        resultObject.put("dbType", dbObject.getString("dbType"));
        resultObject.put("port", dbObject.getString("port"));
        return resultObject;
    }

    private JSONObject doGet(String url, Map<String, String> reqMap) {
        log.info("doGet_url:{}     reqMap:{}", url, JsonUtil.toJSON(reqMap));
        try (HttpResponse httpResponse = HttpUtil.createGet(url)
                .addHeaders(reqMap)
                .execute()) {
            if (null == httpResponse) {
                return null;
            }
            String body = httpResponse.body();
            if (StringUtil.isEmpty(body)) {
                return null;
            }
            JSONObject respJson = JSON.parseObject(body);
            if (Objects.isNull(respJson)) {
                return null;
            }
            return respJson;
        } catch (Exception e) {
            log.error("请求zbase获取信息异常！{}", url, e);
            throw new ServiceException("请求zbase获取信息异常！");
        }
    }

    @Override
    public List<ListRedisQueryVO> queryRedisList(String productCode, String search) {
        String docProductCode = metaDataService.getMetaDataProductCode(productCode);
        if (StringUtil.isBlank(docProductCode)) {
            log.error("queryESList_docProductCode_is_blank:{}", productCode);
            return new ArrayList<>();
        }

        String url = String.format(zbaseConfig.queryRedisUrl(), docProductCode, search);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-token", zbaseConfig.queryEsToken());
        JSONObject result = doGet(url, headerMap);
        log.info("queryRedisList_Result:{}", JsonUtil.toJSON(result));
        if (Objects.isNull(result)) {
            return new ArrayList<>();
        }

        QueryRedisResultRespVO response = JSONObject.toJavaObject(result, QueryRedisResultRespVO.class);
        if (!response.isStatus()) {
            return new ArrayList<>();
        }
        return response.buildResult();
    }

    @Override
    public List<ListESQueryVO> queryESList(listESQueryReq req) {
        String docProductCode = metaDataService.getMetaDataProductCode(req.getProductCode());
        if (StringUtil.isBlank(docProductCode)) {
            log.error("queryESList_docProductCode_is_blank:{}", req.getProductCode());
            return new ArrayList<>();
        }

        QueryZBaseESListReq toQuery = QueryZBaseESListReq.buildSelf(docProductCode, req.getSearch());
        String sign = this.generateSign(toQuery, zbaseConfig.getZBaseSecret());
        String url = String.format(zbaseConfig.queryEsUrl(), docProductCode, sign);

        String resp = httpService.doPostWithToken(url, JSONUtil.toJsonStr(toQuery), zbaseConfig.queryEsToken());
        log.info("queryESList_resp:{}", resp);
        if (StringUtil.isBlank(resp)) {
            return new ArrayList<>();
        }

        QueryESResultRespVO response = JSONUtil.toBean(resp, QueryESResultRespVO.class);
        if (!response.isStatus()) {
            return new ArrayList<>();
        }
        return response.buildResult();
    }

    private String generateSign(Object dto, String secret) {
        Map<String, String> bodyMap = objectToMap(dto);
        return HMacSHA1Util.hmacSHA1Encrypt(bodyMap, secret);
    }

    private Map<String, String> objectToMap(Object obj) {
        return Arrays.stream(BeanUtils.getPropertyDescriptors(obj.getClass()))
                .filter(pd -> !"class".equals(pd.getName()))
                .filter(pd -> !"bytes".equals(pd.getName()))
                .collect(HashMap::new,
                        (map, pd) -> {
                            Object o = ReflectionUtils.invokeMethod(pd.getReadMethod(), obj);
                            if (null != o) {
                                map.put(pd.getName(), String.valueOf(o));
                            }
                        }, HashMap::putAll);
    }

    @Override
    public PageInfo<TopicSimpleVO> topicSimpleQuery(TopicSimpleQuery query) {
        String sign = generateSign(query, zbaseConfig.getZBaseSecret());
        try (HttpResponse httpResponse = HttpUtil.createPost(
                UrlBuilder.of(zbaseConfig.getTopicUrl())
                        .addQuery("appId", "devops")
                        .addQuery("sign", sign)
                        .build())
                .header("x-token", zbaseConfig.queryEsToken())
                .body(JSON.toJSONString(query))
                .execute()) {
            return Optional.ofNullable(httpResponse.body())
                    .map(JSON::parseObject)
                    .filter(resp -> Boolean.TRUE.equals(resp.getBoolean("status")))
                    .map(resp -> resp.getJSONObject("result"))
                    .map(result -> {
                        PageInfo<TopicSimpleVO> pageInfo = new PageInfo<>();
                        pageInfo.setPageNum(result.getInteger("current"));
                        pageInfo.setPageSize(result.getInteger("size"));
                        pageInfo.setTotal(result.getInteger("total"));
                        pageInfo.setList(JSON.parseArray(result.getString("records"), TopicSimpleVO.class));
                        return pageInfo;
                    })
                    .orElse(null);
        } catch (Exception e) {
            log.error("topicSimpleQuery {}", JSON.toJSONString(query), e);
            return null;
        }
    }

    @Override
    public Integer getPhysicsDbId(String databaseUrl, String productCode, User user,
                                  UseCaseFactoryTypeEnum type, String businessCode) {
        if (StringUtils.isEmpty(databaseUrl)) {
            return null;
        }
        String dbNameIn = this.extractDbNameFromDatabaseUrl(databaseUrl);
        if (StringUtils.isEmpty(dbNameIn)) {
            return null;
        }
        JSONArray dbs = queryProductDb(productCode);
        if (null == dbs) {
            return null;
        }
        for (int j = 0; j < dbs.size() ; j++) {
            JSONObject jsonObject = dbs.getJSONObject(j);
            JSONArray physicsDbArray = jsonObject.getJSONArray("dblresourceRelatePhysicsDbList");
            if (CollectionUtil.isEmpty(physicsDbArray)) {
                continue;
            }
            String dbName;
            if (physicsDbArray.size() == 1) {
                dbName = physicsDbArray.getJSONObject(0).getString("schema");
                if (dbNameIn.equals(dbName)) {
                    return physicsDbArray.getJSONObject(0).getInteger("dblresourceId");
                }
            } else {
                JSONObject physicsDbObj = (JSONObject) physicsDbArray.stream().filter(physicsDb -> {
                    if (dbNameIn.equals(((JSONObject) physicsDb).getString("schema"))) {
                        return true;
                    }
                    return false;
                }).findAny().orElse(null);
                return physicsDbObj != null ? physicsDbObj.getInteger("dblresourceId") : null;
            }
        }
        return null;
    }

//    @Override
//    public JSONObject queryDependencyPageListByAppId(String appid, Integer pageNum, Integer pageSize) {
//        String url = String.format(zbaseConfig.selectDependencyPageListByAppIdUrl(),appid,pageNum.toString(),pageSize.toString());
//        Map<String,String> reqMap = new HashMap<>();
//        reqMap.put("x-token", zbaseConfig.getDevopsQcToken());
//        JSONObject result = doGet(url, reqMap);
//        if (Objects.isNull(result)) {
//            return null;
//        }
//        if(result.containsKey("result")) {
//            return result.getJSONObject("result");
//        }else {
//            return null;
//        }
//    }
//
//
//    @Override
//    public JSONArray queryMethodsByDependService(DependencyServiceReq dependencyServiceReq) {
//        String url = String.format(zbaseConfig.getMethodsByServiceKey(),dependencyServiceReq.getDependAppId(),dependencyServiceReq.getDependService());
//        Map<String,String> reqMap = new HashMap<>();
////        reqMap.put("appId", dependencyServiceReq.getDependAppId());
////        reqMap.put("serviceKey", dependencyServiceReq.getDependService());
//        reqMap.put("x-token", zbaseConfig.getDevopsQcToken());
//        JSONObject result = doGet(url, reqMap);
//        if (Objects.isNull(result)) {
//            return null;
//        }
//        if(result.containsKey("result")) {
//            return result.getJSONArray("result");
//        }else {
//            return null;
//        }
//    }

    private String extractDbNameFromDatabaseUrl(String jdbcUrl) {
        String[] parts = jdbcUrl.split("/");
        if (parts.length > 0) {
            String lastPart = parts[parts.length - 1];
            return lastPart;
        }
        return null;
    }

//    public static void main(String[] args) {
//        Map<String, String> reqMap = new HashMap<>();
//        reqMap.put("appId","devops-qc");
//        reqMap.put("pageNum","1");
//        reqMap.put("pageSize","20");
//        reqMap.put("x-token", "00e01a3aeeb54f55a1e90f5da935e746");
//        String url = "https://zbase-server.dev.ztosys.com/microservice/view/dependency/selectDependencyPageListByAppId?env=fat&productCode=prdglo5ag&appId=devops-qc&pageNum=1&pageSize=20";
//        JSONObject obj = new ZbaseServiceImpl().doGet(url, reqMap);
//        System.out.println(obj);

//        Map<String, String> reqMap = new HashMap<>();
//        reqMap.put("x-token", "df5410b422eb4a4a8c985fd36690a5b1");
//        String url = "https://zbase.dev.ztosys.com/_index/database/view/fat/logicDbAndPhysicalInfo?devopsProductCode=PRO_39&env=fat";
//        JSONObject obj = new ZbaseServiceImpl().doGet(url, reqMap);
//        System.out.println(obj);
//
//        reqMap.put("x-token", "df5410b422eb4a4a8c985fd36690a5b1");
//        String url2 = "https://zbase.dev.ztosys.com/_index/database/view/dblresource/253/fat/physicaldbinfo";
//        JSONObject obj2 = new ZbaseServiceImpl().doGet(url2, reqMap);
//        System.out.println(obj2);
//
//        String url1 = String.format("https://zbase.dev.ztosys.com/_sys/conf/view/queryDBInfo?env=fat&productCode=prdq6uzb2&host=**********&schema=thrall");
//        reqMap.put("x-token", "4ab1087167f042d3b810b7a5da42405a");
//        JSONObject obj1 = new ZbaseServiceImpl().doGet(url1, reqMap);
//        System.out.println(obj1);
//    }
}
