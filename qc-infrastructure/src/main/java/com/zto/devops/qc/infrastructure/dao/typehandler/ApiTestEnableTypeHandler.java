package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTestEnableEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedJdbcTypes(value = JdbcType.INTEGER, includeNullJdbcType = true)
public class ApiTestEnableTypeHandler extends BaseTypeHandler<ApiTestEnableEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, ApiTestEnableEnum apiTestEnableEnum, JdbcType jdbcType) throws SQLException {
        if (null == apiTestEnableEnum) {
            preparedStatement.setInt(i, 1);
        } else {
            preparedStatement.setInt(i, apiTestEnableEnum.getCode());
        }
    }

    @Override
    public ApiTestEnableEnum getNullableResult(ResultSet resultSet, String s) throws SQLException {
        int result = resultSet.getInt(s);
        return ApiTestEnableEnum.codeOf(result);
    }

    @Override
    public ApiTestEnableEnum getNullableResult(ResultSet resultSet, int i) throws SQLException {
        int result = resultSet.getInt(i);
        return ApiTestEnableEnum.codeOf(result);
    }

    @Override
    public ApiTestEnableEnum getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        int result = callableStatement.getInt(i);
        return ApiTestEnableEnum.codeOf(result);
    }
}
