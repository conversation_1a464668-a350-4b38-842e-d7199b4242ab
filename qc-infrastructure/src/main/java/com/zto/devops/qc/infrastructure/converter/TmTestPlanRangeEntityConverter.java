package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TmTestPlanRangeEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanRangeEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TmTestPlanRangeEntityConverter {

    List<TmTestPlanRangeEntityDO> convert2DOList(List<TmTestPlanRangeEntity> entityList);

    TmTestPlanRangeEntityDO convert2DO(TmTestPlanRangeEntity rangeEntity);

    TmTestPlanRangeEntity convert2Entity(TmTestPlanRangeEntityDO rangeEntityDO);
}
