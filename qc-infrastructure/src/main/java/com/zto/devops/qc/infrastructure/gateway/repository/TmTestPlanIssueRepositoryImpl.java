package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.qc.client.model.dto.TmPlanIssueEntityDO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmPlanCaseIssueVO;
import com.zto.devops.qc.domain.gateway.repository.TmTestPlanIssueRepository;
import com.zto.devops.qc.infrastructure.converter.TmTestPlanIssueEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.TmPlanIssueEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TmTestPlanIssueMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class TmTestPlanIssueRepositoryImpl implements TmTestPlanIssueRepository {
    @Autowired
    private TmTestPlanIssueMapper tmTestPlanIssueMapper;
    @Autowired
    private TmTestPlanIssueEntityConverter tmTestPlanIssueEntityConverter;

    @Override
    public List<TmPlanCaseIssueVO> selectPlanIssue(TmPlanIssueEntityDO issueEntityDO) {
        TmPlanIssueEntity planIssueEntity = tmTestPlanIssueEntityConverter.covert2Entity(issueEntityDO);
        return tmTestPlanIssueMapper.selectPlanIssue(planIssueEntity);
    }
}
