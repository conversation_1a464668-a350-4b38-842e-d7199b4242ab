package com.zto.devops.qc.infrastructure.gateway.repository;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.qc.client.enums.testmanager.apitest.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.testmanager.apitest.command.*;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.*;
import com.zto.devops.qc.client.model.testmanager.apitest.event.*;
import com.zto.devops.qc.client.model.testmanager.apitest.query.ApiTestCaseExecuteDetailQuery;
import com.zto.devops.qc.client.model.testmanager.apitest.query.PageApiCaseQuery;
import com.zto.devops.qc.client.model.testmanager.apitest.query.PageApiExceptionCaseQuery;
import com.zto.devops.qc.client.model.testmanager.apitest.query.PageApiTestCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ApiTestCaseExecuteDetailTiledVO;
import com.zto.devops.qc.client.service.testmanager.apitest.model.*;
import com.zto.devops.qc.domain.gateway.repository.ApiTestRepository;
import com.zto.devops.qc.domain.util.DateUtil;
import com.zto.devops.qc.infrastructure.converter.ApiTestEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.*;
import com.zto.devops.qc.infrastructure.dao.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ApiTestRepositoryImpl implements ApiTestRepository {

    @Autowired
    private ApiTestVariableMapper apiTestVariableMapper;

    @Autowired
    private ApiTestEntityConverter apiTestEntityConverter;

    @Autowired
    private ApiTestMapper apiTestMapper;

    @Autowired
    private ApiCaseMapper apiCaseMapper;

    @Autowired
    private SceneInfoMapper sceneInfoMapper;

    @Autowired
    private SceneIndexMapper sceneIndexMapper;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private SceneApiRelationMapper sceneApiRelationMapper;

    @Autowired
    private SceneDatabaseAuthorizeMapper sceneDatabaseAuthorizeMapper;

    @Autowired
    private ApiTestCaseMapper apiTestCaseMapper;

    @Autowired
    private AutomaticTaskMapper automaticTaskMapper;

    @Override
    public ApiTestVariableVO getApiTestVariableByVariableCode(String variableCode) {
        Example example = new Example(ApiTestVariableEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", Boolean.TRUE);
        criteria.andEqualTo("variableCode", variableCode);
        ApiTestVariableEntity entity = apiTestVariableMapper.selectOneByExample(example);
        return apiTestEntityConverter.converter(entity);
    }

    @Override
    public ApiTestVariableVO getUniqueApiTestVariable(String variableCode, String productCode, String linkCode, String variableKey) {
        ApiTestVariableEntity entity = apiTestVariableMapper.getUniqueApiTestVariable(variableCode, productCode, linkCode, variableKey);
        return apiTestEntityConverter.converter(entity);
    }

    @Override
    public void addApiTestVariable(AddApiTestVariableEvent event) {
        apiTestVariableMapper.insertSelective(apiTestEntityConverter.converter(event));
    }

    @Override
    public void editApiTestVariable(EditApiTestVariableEvent event) {
        apiTestVariableMapper.updateByPrimaryKeySelective(apiTestEntityConverter.converter(event));
    }

    @Override
    public List<ApiTestVariableVO> queryApiTestVariablePage(PageApiTestVariableReq req) {
        Example example = new Example(ApiTestVariableEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", Boolean.TRUE);
        if (CollectionUtil.isNotEmpty(req.getTypeList())) {
            criteria.andIn("type", req.getTypeList());
        }
        if (CollectionUtil.isNotEmpty(req.getStatusList())) {
            criteria.andIn("variableStatus", req.getStatusList());
        }
        if (Objects.nonNull(req.getProductCode())) {
            criteria.andEqualTo("productCode", req.getProductCode());
        }
        if (StringUtils.isNotBlank(req.getVariableKey())) {
            criteria.andEqualTo("variableName", req.getVariableName());
        }
        if (StringUtils.isNotBlank(req.getVariableKey())) {
            criteria.andEqualTo("variableKey", req.getVariableKey());
        }
        example.orderBy("gmtModified").desc();
        return apiTestEntityConverter.converterToList(apiTestVariableMapper.selectByExample(example));
    }

    @Override
    public void updateApiTestVariableStatus(UpdateApiTestVariableStatusEvent event) {
        apiTestVariableMapper.updateByPrimaryKeySelective(apiTestEntityConverter.converter(event));
    }

    @Override
    public void deleteApiTestVariable(DeleteApiTestVariableEvent event) {
        ApiTestVariableEntity entity = apiTestEntityConverter.converter(event);
        entity.setEnable(Boolean.FALSE);
        apiTestVariableMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void deleteApiTestByProductAndType(String productCode, List<ApiTypeEnum> typeList) {
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria().andEqualTo("productCode", productCode)
                .andIn("apiType", typeList.stream().map(ApiTypeEnum::name).collect(Collectors.toList()));
        apiTestMapper.deleteByExample(example);
    }

    @Override
    public void insertApiTest(ApiTestEntityDO entityDO) {
        apiTestMapper.insertSelective(apiTestEntityConverter.convertApiTestEntity(entityDO));
    }

    @Override
    public void updateApiTest(ApiTestEntityDO entityDO) {
        apiTestMapper.updateByPrimaryKeySelective(apiTestEntityConverter.convertApiTestEntity(entityDO));
    }

    @Override
    public void deleteApiTest(ApiTestEntityDO entityDO) {
        ApiTestEntity entity = new ApiTestEntity();
        entity.setApiCode(entityDO.getApiCode());
        entity.setEnable(ApiTestEnableEnum.DELETED);
        apiTestMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public List<ApiTestEntityDO> queryApiDetailByCodeList(List<String> codeLst) {
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria().andIn("apiCode", codeLst);
        List<ApiTestEntity> entityList = apiTestMapper.selectByExample(example);
        return apiTestEntityConverter.convertApiTestEntityDO(entityList);
    }

    @Override
    public List<ApiTestEntityDO> queryApiDetailByMainCodeListAndEnable(List<String> codeList, List<ApiTestEnableEnum> enableList) {
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria()
                .andIn("mainApiCode", codeList)
                .andIn("enable", enableList.stream().map(ApiTestEnableEnum::getCode).collect(Collectors.toList()));
        List<ApiTestEntity> entityList = apiTestMapper.selectByExample(example);
        return apiTestEntityConverter.convertApiTestEntityDO(entityList);
    }

    @Override
    public ApiTestEntityDO queryApiDetailByCode(String apiCode) {
        return apiTestEntityConverter.convertApiTestEntityDO(apiTestMapper.selectByPrimaryKey(apiCode));
    }

    @Override
    public ApiCaseEntityDO queryApiCaseByCode(String caseCode) {
        ApiCaseEntity entity = apiCaseMapper.selectByPrimaryKey(caseCode);
        return apiTestEntityConverter.convertApiCaseEntityDO(entity);
    }

    @Override
    public ApiTestCaseEntityDO queryApiCaseByCodeAndStatus(String caseCode, ApiCaseStatusEnum status) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("caseCode", caseCode)
                .andEqualTo("status", status)
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        List<ApiTestCaseEntity> list = apiTestCaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(list) ? null : apiTestEntityConverter.convertApiTestCaseEntityDO(list.get(0));
    }

    @Override
    public List<ApiTestVariableVO> queryVariableListByProductCode(String productCode, String type) {
        List<ApiTestVariableEntity> entityList = apiTestVariableMapper.getApiVariableByProductCode(productCode, type);
        return apiTestEntityConverter.converterToList(entityList);
    }

    @Override
    public void updateApiCaseSelective(ApiCaseEntityDO entityDO) {
        apiCaseMapper.updateByPrimaryKeySelective(apiTestEntityConverter.convertApiCaseEntity(entityDO));
    }

    @Override
    public void deleteApiCaseByApiCode(String apiCode) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("apiCode", apiCode);
        ApiTestCaseEntity entity = new ApiTestCaseEntity();
        entity.setEnable(ApiCaseEnableEnum.DELETED);
        apiTestCaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<ApiTestCaseEntityDO> getApiCaseByCodes(List<String> apiCaseCodes) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andIn("caseCode", apiCaseCodes);
        return apiTestEntityConverter.convertApiTestCaseEntityDO(apiTestCaseMapper.selectByExample(example));
    }

    @Override
    public List<ApiVO> queryApiPage(PageApiReq req) {
        Example example = new Example(ApiTestEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("productCode", req.getProductCode())
                .andIn("enable", Arrays.asList(
                        ApiTestEnableEnum.ONLINE.getCode(),
                        ApiTestEnableEnum.OFFLINE.getCode(),
                        ApiTestEnableEnum.UNKNOWN.getCode()))
                .andEqualTo("modifyFlag", 1);
        if (CollectionUtil.isNotEmpty(req.getAppIdList())) {
            criteria.andIn("appId", req.getAppIdList());
        }
        if (CollectionUtil.isNotEmpty(req.getApiTypeList())) {
            criteria.andIn("apiType",
                    req.getApiTypeList().stream().map(ApiTypeEnum::name).collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(req.getReqMethodList())) {
            criteria.andIn("reqMethod",
                    req.getReqMethodList().stream().map(RequestMethodEnum::name).collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(req.getEnableNumList())) {
            criteria.andIn("enable", req.getEnableNumList());
        }
        if (CollectionUtil.isNotEmpty(req.getDocVersionList())) {
            String docVersionListStr = req.getDocVersionList().stream().map(i -> "\"" + i + "\"").collect(Collectors.joining(","));
            criteria.andCondition("main_api_code in (select distinct main_api_code from tm_api_test where doc_version in (" + docVersionListStr + "))");
        }
        if (CollectionUtil.isNotEmpty(req.getOperatorNameList())) {
            criteria.andIn("modifier", req.getOperatorNameList());
        }
        if (null != req.getStartTime() && null != req.getEndTime()) {
            criteria.andBetween("gmtModified", req.getStartTime(), req.getEndTime());
        }
        if (StringUtils.isNotEmpty(req.getApiDesc())) {
            criteria.andLike("apiDesc", "%" + req.getApiDesc() + "%");
        }
        if (StringUtils.isNotEmpty(req.getNameOrAddress())) {
            example.and(example.createCriteria()
                    .orLike("apiName", "%" + req.getNameOrAddress() + "%")
                    .orLike("apiAddress", "%" + req.getNameOrAddress() + "%"));
        }
        if (CollectionUtil.isNotEmpty(req.getSceneRelatedList())) {
            criteria.andIn("relatedSceneFlag",
                    req.getSceneRelatedList().stream().map(i -> i ? 1 : 0).collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(req.getTagList())) {
            Example.Criteria orCriteria = example.createCriteria();
            req.getTagList().forEach(i -> orCriteria.orLike("tagValue", "%" + i + "%"));
            example.and(orCriteria);
        }
        example.selectProperties("apiCode", "apiName", "appId", "apiType", "reqMethod", "apiAddress", "apiDesc",
                "docId", "enable", "modifierId", "modifier", "gmtModified", "docProductCode", "relatedSceneFlag",
                "tagValue", "mainApiCode");
        example.orderBy("gmtModified").desc();
        return apiTestEntityConverter.convertApiVO(apiTestMapper.selectByExample(example));
    }

    @Override
    public List<ApiDocVersionVO> queryApiDocVersionPage(PageApiDocVersionReq req) {
        return apiTestMapper.selectDocVersionList(req.getProductCode(), req.getDocVersion());
    }

    @Override
    public List<ApiLiteInfoVO> queryApiLiteInfoPage(PageApiLiteInfoReq req) {
        List<ApiLiteInfoVO> resultList = apiTestMapper.selectApiLiteGroup(req.getProductCode(), req.getNameOrAddress(), req.getStatusList(), req.getApiCode(), req.getSearchName());
        return new ArrayList<>(resultList);
    }

    @Override
    public List<ApiCaseEntityDO> getApiCaseByParentCodeAndSourceType(String parentCode, ApiCaseSourceTypeEnum sourceType) {
        if (StringUtils.isBlank(parentCode) || null == sourceType) {
            return new ArrayList<>();
        }
        Example example = new Example(ApiCaseEntity.class);
        example.createCriteria().andEqualTo("parentCode", parentCode)
                .andEqualTo("sourceType", sourceType.getCode())
                .andEqualTo("enable", ApiCaseEnableEnum.ENABLED.getCode());
        return apiTestEntityConverter.convertApiCaseEntityDO(apiCaseMapper.selectByExample(example));
    }

    @Override
    public List<ApiCaseEntityDO> getApiCaseByApiCodesAndSourceType(List<String> apiCodes, ApiCaseSourceTypeEnum sourceType) {
        if (CollectionUtil.isEmpty(apiCodes) || null == sourceType) {
            return new ArrayList<>();
        }
        Example example = new Example(ApiCaseEntity.class);
        example.createCriteria().andIn("apiCode", apiCodes)
                .andEqualTo("sourceType", sourceType.getCode())
                .andEqualTo("enable", ApiCaseEnableEnum.ENABLED.getCode());
        return apiTestEntityConverter.convertApiCaseEntityDO(apiCaseMapper.selectByExample(example));
    }

    @Override
    public List<ApiCaseVO> queryApiCasePage(PageApiCaseQuery query) {
        return apiCaseMapper.selectApiCaseList(query);
    }

    @Override
    public List<ApiCaseChildVO> queryApiCaseChildrenPage(PageApiCaseChildrenReq req) {
        return apiCaseMapper.selectApiCaseChildren(req);
    }

    @Override
    public Integer countCase(CountCaseReq req) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("productCode", req.getProductCode())
                .andIn("apiCode", req.getApiCodeList())
                .andIn("caseType", Arrays.asList(ApiCaseTypeEnum.API_CASE.getCode(), ApiCaseTypeEnum.SYSTEM_CASE.getCode()))
                .andEqualTo("enable", ApiCaseEnableEnum.ENABLED.getCode())
                .andEqualTo("status", ApiCaseStatusEnum.publish.name());
        return apiTestCaseMapper.selectCountByExample(example);
    }

    @Override
    public void updateApiCaseUserByParent(UpdateApiCaseUserCommand command) {
        Example example = new Example(ApiCaseEntity.class);
        example.createCriteria().andEqualTo("parentCode", command.getAggregateId())
                .andIn("enable",
                        Arrays.asList(ApiCaseEnableEnum.ENABLED.getCode(), ApiCaseEnableEnum.DISABLED.getCode()));
        ApiCaseEntity entity = new ApiCaseEntity();
        entity.setUserVariableCode(command.getUserVariableCode());
        entity.preUpdate(command);
        apiCaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<ApiTestEntityDO> getApiTestByDocId(Long docId, String productCode) {
        Example example = new Example(ApiTestEntity.class);
        Example.Criteria criteria = example.createCriteria().andEqualTo("docId", docId);
        if (StringUtils.isNotEmpty(productCode)) {
            criteria.andEqualTo("productCode", productCode);
        }
        return apiTestEntityConverter.convertApiTestEntityDO(apiTestMapper.selectByExample(example));
    }

    @Override
    public List<ApiTestEntityDO> getApiTestByDocIds(List<Long> docIds, String productCode) {
        Example example = new Example(ApiTestEntity.class);
        Example.Criteria criteria = example.createCriteria().andIn("docId", docIds);
        if (StringUtils.isNotEmpty(productCode)) {
            criteria.andEqualTo("productCode", productCode);
        }
        criteria.andEqualTo("enable", ApiCaseEnableEnum.ENABLED.getCode());
        example.selectProperties("docId", "mainApiCode");
        return apiTestEntityConverter.convertApiTestEntityDO(apiTestMapper.selectByExample(example));
    }

    @Override
    public List<ApiTestCaseEntityDO> getApiTestCaseByMainApiCodes(List<String> mainApiCodes, String productCode) {
        Example example = new Example(ApiTestCaseEntity.class);
        Example.Criteria criteria = example.createCriteria().andIn("apiCode", mainApiCodes);
        if (StringUtils.isNotEmpty(productCode)) {
            criteria.andEqualTo("productCode", productCode);
        }
        criteria.andEqualTo("enable", ApiCaseEnableEnum.ENABLED.getCode());
        example.selectProperties("caseCode", "apiCode");
        List<ApiTestCaseEntity> entityList = apiTestCaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? null : apiTestEntityConverter.convertApiTestCaseEntityDO(entityList);
    }

    @Override
    public List<ApiTestEntityDO> queryApiTestByProductAndType(String productCode, List<ApiTypeEnum> typeList) {
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria()
                .andEqualTo("productCode", productCode)
                .andIn("apiType", typeList.stream().map(ApiTypeEnum::name).collect(Collectors.toList()));
        example.selectProperties("apiCode", "docId", "apiType", "apiAddress", "gatewayApiInfo", "mainApiCode");
        return apiTestEntityConverter.convertApiTestEntityDO(apiTestMapper.selectByExample(example));
    }

    @Override
    public List<ApiTestEntityDO> getAllVersionApiTest(ApiTestEntityDO entityDO) {
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria()
                .andEqualTo("productCode", entityDO.getProductCode())
                .andEqualTo("apiType", entityDO.getApiType())
                .andEqualTo("apiAddress", entityDO.getApiAddress())
                .andEqualTo("gatewayApiInfo", entityDO.getGatewayApiInfo());
        example.selectProperties("apiCode", "gmtModified", "mainApiCode", "enable");
        return apiTestEntityConverter.convertApiTestEntityDO(apiTestMapper.selectByExample(example));
    }

    @Override
    public void updateApiCaseUserByVariableCode(ApiCaseEntityDO entityDO) {
        ApiCaseEntity entity = new ApiCaseEntity();
        entity.setUserVariableCode("");
        Example example = new Example(ApiCaseEntity.class);
        example.createCriteria().andEqualTo("userVariableCode", entityDO.getUserVariableCode());
        apiCaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public Boolean checkSceneName(String sceneName, String productCode, String sceneCode, Integer sceneType) {
        Example example = new Example(SceneInfoEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andNotEqualTo("enable", SceneInfoEnableEnum.DELETED)
                .andEqualTo("sceneName", sceneName)
                .andEqualTo("sceneType", sceneType)
                .andEqualTo("productCode", productCode);
        if (StringUtils.isNotEmpty(sceneCode)) {
            criteria.andNotEqualTo("sceneCode", sceneCode);
        }
        return sceneInfoMapper.selectCountByExample(example) > 0;
    }

    @Override
    public void insertSceneInfoSelective(SceneInfoEntityDO entityDO) {
        SceneInfoEntity entity = apiTestEntityConverter.convertSceneInfoEntity(entityDO);
        sceneInfoMapper.insertSelective(entity);
        entityDO.setId(entity.getId());
    }

    @Override
    public void updateSceneInfoByCode(SceneInfoEntityDO entityDO) {
        Example example = new Example(SceneInfoEntity.class);
        example.createCriteria().andEqualTo("sceneCode", entityDO.getSceneCode());
        sceneInfoMapper.updateByExampleSelective(apiTestEntityConverter.convertSceneInfoEntity(entityDO), example);
    }

    @Override
    public void updateSceneInfoByPrimaryKey(SceneInfoEntityDO entityDO) {
        sceneInfoMapper.updateByPrimaryKeySelective(apiTestEntityConverter.convertSceneInfoEntity(entityDO));
    }

    @Override
    public SceneInfoEntityDO querySceneInfoByPrimaryKey(SceneInfoEntityDO entityDO) {
        return apiTestEntityConverter.convertSceneInfoEntityDO(sceneInfoMapper.selectByPrimaryKey(apiTestEntityConverter.convertSceneInfoEntity(entityDO)));
    }

    @Override
    public SceneInfoEntityDO querySceneInfoByCodeAndVersion(String sceneCode, Integer sceneVersion) {
        Example example = new Example(SceneInfoEntity.class);
        example.createCriteria()
                .andEqualTo("sceneCode", sceneCode)
                .andEqualTo("sceneVersion", sceneVersion);
        return apiTestEntityConverter.convertSceneInfoEntityDO(sceneInfoMapper.selectOneByExample(example));
    }

    @Override
    public SceneInfoEntityDO queryLatestSceneInfo(String sceneCode, SceneInfoStatusEnum status) {
        Example example = new Example(SceneInfoEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sceneCode", sceneCode);
        if (null != status) {
            criteria.andEqualTo("status", status);
        }
        example.orderBy("sceneVersion").desc();
        PageHelper.startPage(1, 1);
        List<SceneInfoEntity> list = sceneInfoMapper.selectByExample(example);
        return CollectionUtil.isEmpty(list) ? null : apiTestEntityConverter.convertSceneInfoEntityDO(list.get(0));
    }

    @Override
    public List<SceneInfoEntityDO> pageQuerySceneList(PageSceneInfoReq req) {
        return sceneInfoMapper.pageQuerySceneList(req);
    }

    @Override
    public SceneInfoEntityDO queryLatestEditSceneInfo(QueryLatestEditSceneInfoReq req) {
        SceneInfoEntity entity = sceneInfoMapper.queryLatestEditSceneInfo(req);
        return apiTestEntityConverter.convertSceneInfoEntityDO(entity);
    }

    @Override
    public List<ApiCaseEntityDO> queryNodeCaseDataList(String sceneCode, List<String> nodeList) {
        Example example = new Example(ApiCaseEntity.class);
        example.createCriteria().andIn("nodeCode", nodeList)
                .andIn("sourceType", Arrays.asList(ApiCaseSourceTypeEnum.NODE_DATA.getCode(),
                        ApiCaseSourceTypeEnum.GENERAL.getCode()))
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("sceneCode", sceneCode);
        return apiTestEntityConverter.convertApiCaseEntityDO(apiCaseMapper.selectByExample(example));
    }

    @Override
    public PageInfo<ApiSampleCaseVO> queryPageApiTest(PageApiInfoReq req) {
        Example example = new Example(ApiTestEntity.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotEmpty(req.getApiCode())) {
            criteria.andEqualTo("apiCode", req.getApiCode());
            example.or(example.createCriteria()
                    .andEqualTo("mainApiCode", req.getApiCode())
                    .andEqualTo("modifyFlag", 1));
        } else {
            criteria.andEqualTo("modifyFlag", 1)
                    .andIn("enable",
                            Arrays.asList(ApiTestEnableEnum.ONLINE.getCode(), ApiTestEnableEnum.UNKNOWN.getCode()));
            if (StringUtils.isNotEmpty(req.getProductCode())) {
                criteria.andEqualTo("productCode", req.getProductCode());
            }
            if (null != req.getApiType()) {
                criteria.andEqualTo("apiType", req.getApiType());
            }
            if (StringUtils.isNotEmpty(req.getNameOrAddress())) {
                example.and(example.createCriteria()
                        .orLike("apiName", "%" + req.getNameOrAddress() + "%")
                        .orLike("apiAddress", "%" + req.getNameOrAddress() + "%"));
            }
        }
        long start = System.currentTimeMillis();
        int count = apiTestMapper.selectCountByExample(example);
        log.info("queryPageApiTest查询: {}- 耗时:{}", JSON.toJSONString(req), System.currentTimeMillis() - start);
        example.selectProperties("apiCode", "apiName", "reqMethod", "docId", "productCode", "docProductCode", "appId",
                "apiType", "apiAddress", "docVersion", "apiData", "mainApiCode");
        Page<ApiTestEntity> page = PageHelper.startPage(req.getPage(), req.getSize(), false);
        List<ApiTestEntity> list = apiTestMapper.selectByExample(example);
        PageInfo<ApiSampleCaseVO> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(count);
        pageInfo.setList(apiTestEntityConverter.convertApiSampleCaseVO(list));
        return pageInfo;
    }

    @Override
    public String queryExecuteResultByParentCodeAndTaskCode(String parentCode, String taskCode) {
        if (StringUtils.isBlank(parentCode) || StringUtils.isBlank(taskCode)) {
            return "";
        }
        return apiCaseMapper.selectExecuteResultByParentCodeAndTaskCode(parentCode, taskCode);
    }

    @Override
    public void updateApiCaseExecuteResult(String parentCode, String taskCode, TestPlanCaseStatusEnum executeResult) {
        if (StringUtils.isBlank(parentCode) || StringUtils.isBlank(taskCode)) {
            return;
        }
        ApiTestCaseEntity apiCaseEntity = new ApiTestCaseEntity();
        if (null != executeResult) {
            apiCaseEntity.setLatestExecuteResult(executeResult);
        }
        apiCaseEntity.setLatestTaskId(taskCode);
        apiCaseEntity.setLatestExecuteTime(new Date());
        apiCaseEntity.setGmtModified(new Date());
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("caseCode", parentCode);
        apiTestCaseMapper.updateByExampleSelective(apiCaseEntity, example);
    }

    @Override
    public List<StatApiCaseExecuteResultVO> statApiCaseExecuteResult(String taskCode) {
        if (StringUtils.isBlank(taskCode)) {
            return null;
        }
        return apiTestCaseMapper.statApiCaseExecuteResult(taskCode);
    }

    @Override
    public SceneInfoEntityDO queryLatestSceneInfoByAutomaticSourceCode(String automaticSourceCode) {
        Example example = new Example(SceneInfoEntity.class);
        example.createCriteria()
                .andEqualTo("automaticSourceCode", automaticSourceCode)
                .andIn("enable", Arrays.asList(SceneInfoEnableEnum.UNPUBLISHED.getCode(), SceneInfoEnableEnum.PUBLISHED.getCode()));
        example.orderBy("sceneVersion").desc();
        List<SceneInfoEntity> list = sceneInfoMapper.selectByExample(example);
        return CollectionUtil.isEmpty(list) ? null : apiTestEntityConverter.convertSceneInfoEntityDO(list.get(0));
    }

    @Override
    public List<SceneInfoEntityDO> querySceneInfoByCode(String sceneCode) {
        Example example = new Example(SceneInfoEntity.class);
        example.createCriteria().andEqualTo("sceneCode", sceneCode);
        return apiTestEntityConverter.converterToSceneInfoEntityList(sceneInfoMapper.selectByExample(example));
    }

    @Override
    public List<SceneInfoEntityDO> querySceneByAutoSourceCode(List<String> autoSourceList) {
        Example example = new Example(SceneInfoEntity.class);
        example.createCriteria().andIn("automaticSourceCode", autoSourceList)
                .andEqualTo("status", SceneInfoStatusEnum.edit.name());
        return apiTestEntityConverter.converterToSceneInfoEntityList(sceneInfoMapper.selectByExample(example));
    }

    @Override
    public List<SceneInfoEntityDO> querySceneByProductCodeAndSceneType(String productCode, Integer sceneType) {
        return apiTestEntityConverter.converterToSceneInfoEntityList(sceneInfoMapper.querySceneByProductCodeAndSceneType(productCode, sceneType));
    }

    @Override
    public void deleteAllModuleScene(String productCode) {
        Example example = new Example(SceneIndexEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sceneIndexType", SceneIndexTypeEnum.SCENE.getCode());
        if (StringUtils.isNotBlank(productCode)) {
            criteria.andEqualTo("productCode", productCode);
        }
        sceneIndexMapper.deleteByExample(example);
    }

    @Override
    public void insertAllModuleScene(List<SceneIndexVO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<List<SceneIndexVO>> partition = Lists.partition(list, 200);
        partition.forEach(e -> sceneIndexMapper.batchInsert(apiTestEntityConverter.converterVOList(e)));
    }

    @Override
    public List<SceneIndexVO> queryAllSceneModule(SceneModuleQueryReq req) {
        return sceneIndexMapper.queryAllSceneModule(req);
    }

    @Override
    public List<SceneIndexVO> queryModuleByParentCode(String parentCode) {
        return apiTestEntityConverter.converterList(sceneIndexMapper.queryModuleByParentCode(parentCode));
    }

    @Override
    public void insertSceneIndexSelective(SceneIndexVO indexVO) {
        SceneIndexEntity entity = apiTestEntityConverter.converterVO(indexVO);
        sceneIndexMapper.insertSelective(entity);
    }

    @Override
    public SceneIndexVO querySceneIndexByCode(String sceneIndexCode) {
        Example example = new Example(SceneIndexEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("sceneIndexCode", sceneIndexCode);
        List<SceneIndexEntity> list = sceneIndexMapper.selectByExample(example);
        return CollectionUtil.isEmpty(list) ? null : apiTestEntityConverter.converter(list.get(0));
    }

    @Override
    public List<SceneIndexVO> querySceneIndexModuleInfo(String productCode) {
        return sceneIndexMapper.selectSceneIndexModuleInfo(productCode);
    }

    @Override
    public void updateSceneIndexByCode(SceneIndexVO sceneIndex) {
        Example example = new Example(SceneIndexEntity.class);
        example.createCriteria().andEqualTo("sceneIndexCode", sceneIndex.getSceneIndexCode());
        sceneIndexMapper.updateByExampleSelective(apiTestEntityConverter.converterVO(sceneIndex), example);
    }

    @Override
    public SceneIndexVO queryUniqueSceneModule(String productCode, String parentCode, String sceneIndexName, SceneIndexTypeEnum type, UseCaseFactoryTypeEnum sceneType) {
        Example example = new Example(SceneIndexEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("productCode", productCode)
                .andEqualTo("parentCode", parentCode)
                .andEqualTo("sceneIndexName", sceneIndexName)
                .andEqualTo("sceneIndexType", type.getCode())
                .andEqualTo("sceneType", sceneType.getCode());
        SceneIndexEntity entity = sceneIndexMapper.selectOneByExample(example);
        return apiTestEntityConverter.converter(entity);
    }

    @Override
    public void updateSceneIndexSelective(SceneIndexVO indexVO) {
        SceneIndexEntity entity = new SceneIndexEntity();
        entity.setSceneIndexName(indexVO.getSceneIndexName());
        entity.setId(indexVO.getId());
        sceneIndexMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateByExampleSelective(SceneIndexVO indexVO) {
        Example example = new Example(SceneIndexEntity.class);
        example.createCriteria()
                .andEqualTo("sceneIndexCode", indexVO.getSceneIndexCode());
        sceneIndexMapper.updateByExampleSelective(apiTestEntityConverter.converterVO(indexVO), example);
    }

    @Override
    public SceneIndexVO querySceneModuleById(Long id) {
        Example example = new Example(SceneIndexEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("id", id);
        SceneIndexEntity entity = sceneIndexMapper.selectOneByExample(example);
        return apiTestEntityConverter.converter(entity);
    }

    @Override
    public List<SceneIndexVO> querySceneIndexByParentCode(String parentCode) {
        Example example = new Example(SceneIndexEntity.class);
        example.createCriteria()
                .andEqualTo("enable", true)
                .andEqualTo("parentCode", parentCode);
        return apiTestEntityConverter.converterList(sceneIndexMapper.selectByExample(example));
    }

    @Override
    public void batchMoveScene(BatchMoveSceneEvent event) {
        Example example = new Example(SceneIndexEntity.class);
        example.createCriteria()
                .andEqualTo("productCode", event.getProductCode())
                .andIn("sceneIndexCode", event.getSceneCodeList());
        SceneIndexEntity entity = new SceneIndexEntity();
        entity.setParentCode(event.getParentCode());
        sceneIndexMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<SceneInfoEntityDO> queryPublishSceneAutoSource(List<String> sceneCodes) {
        Example example = new Example(SceneInfoEntity.class);
        example.createCriteria()
                .andEqualTo("enable", SceneInfoEnableEnum.PUBLISHED.getCode())
                .andEqualTo("status", SceneInfoStatusEnum.publish.name())
                .andIn("sceneCode", sceneCodes);
        example.selectProperties("sceneCode", "automaticSourceCode");
        example.setDistinct(true);
        return apiTestEntityConverter.converterToSceneInfoEntityList(sceneInfoMapper.selectByExample(example));
    }

    @Override
    public List<ApiTestVariableVO> querySceneVariable(String productCode, String linkCode, VariableTypeEnum type, SubVariableTypeEnum subVariableType,
                                                      List<VariableUsageTypeEnum> variableUsageTypes) {
        Example example = new Example(ApiTestVariableEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("productCode", productCode)
                .andEqualTo("linkCode", linkCode)
                .andEqualTo("type", type)
                .andEqualTo("subVariableType", subVariableType.getValue());
        if (CollectionUtil.isNotEmpty(variableUsageTypes)) {
            criteria.andIn("usageType", variableUsageTypes.stream().map(VariableUsageTypeEnum::getValue).collect(Collectors.toList()));
        }
        List<ApiTestVariableEntity> list = apiTestVariableMapper.selectByExample(example);
        return apiTestEntityConverter.converter(list);
    }

    @Override
    public void batchDeleteVariable(String productCode, String linkCode, VariableTypeEnum type, List<VariableUsageTypeEnum> variableUsageTypes) {
        Example example = new Example(ApiTestVariableEntity.class);
        example.createCriteria()
                .andEqualTo("productCode", productCode)
                .andEqualTo("linkCode", linkCode)
                .andEqualTo("type", type)
                .andIn("usageType", variableUsageTypes.stream().map(VariableUsageTypeEnum::getValue).collect(Collectors.toList()));
        ApiTestVariableEntity entity = new ApiTestVariableEntity();
        entity.setEnable(Boolean.FALSE);
        apiTestVariableMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void batchAddVariable(BatchAddVariableCommand command) {
        List<ApiTestVariableEntity> list = new ArrayList<>();
        if (CollectionUtil.isEmpty(command.getApiTestVariableList())) {
            return;
        }
        for (BaseApiTestVariableReq req : command.getApiTestVariableList()) {
            ApiTestVariableEntity entity = new ApiTestVariableEntity();
            entity.setVariableCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
            entity.setProductCode(command.getProductCode());
            entity.setProductName(command.getProductName());
            entity.setLinkCode(command.getLinkCode());
            entity.setType(command.getType());
            entity.setVariableName(req.getVariableKey());
            entity.setVariableKey(req.getVariableKey());
            entity.setVariableValue(req.getVariableValue());
            entity.setEnable(true);
            entity.setVariableStatus(true);
            entity.setRequiredStatus(Boolean.FALSE);
            entity.setUsageType(VariableUsageTypeEnum.UNKNOWN);
            entity.setSceneType(UseCaseFactoryTypeEnum.SCENE.getCode());
            entity.preCreate(command.getTransactor());
            entity.setVariableDesc(StringUtils.isNotEmpty(req.getVariableDesc()) ? req.getVariableDesc() : "");
            entity.setSubVariableType(command.getSubVariableType());
            entity.setLoginValidTime(0);
            list.add(entity);
        }
        apiTestVariableMapper.batchInsert(list);
    }

    @Override
    public void batchInsertVariable(List<ApiTestVariableVO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        apiTestVariableMapper.batchInsert(apiTestEntityConverter.convertVOList(list));
    }

    @Override
    public void batchUpdateVariable(List<ApiTestVariableVO> list) {
        list.forEach(vo ->
                apiTestVariableMapper.updateByPrimaryKeySelective(apiTestEntityConverter.convert(vo))
        );
    }

    @Override
    public List<ApiTestVariableVO> querySceneVariableExcludeKeySet(String sceneCode, Set<String> variableKeySet, VariableTypeEnum type) {
        if (StringUtils.isBlank(sceneCode)) {
            return new ArrayList<>();
        }
        Example example = new Example(ApiTestVariableEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("linkCode", sceneCode);
        if (CollectionUtil.isNotEmpty(variableKeySet)) {
            criteria.andNotIn("variableKey", variableKeySet);
        }
        if (null != type) {
            criteria.andEqualTo("type", type.name());
        }
        List<ApiTestVariableEntity> list = apiTestVariableMapper.selectByExample(example);
        return apiTestEntityConverter.converter(list);
    }

    @Override
    public List<SceneIndexVO> querySimpleSceneIndex(SceneModuleQueryReq req) {
        return sceneIndexMapper.selectSimpleSceneIndex(req.getProductCode());
    }

    @Override
    public int countEnableBySourceCode(String automaticSourceCode) {
        Example example = new Example(SceneInfoEntity.class);
        example.createCriteria()
                .andNotEqualTo("enable", SceneInfoEnableEnum.DELETED.getCode())
                .andEqualTo("automaticSourceCode", automaticSourceCode);
        return sceneInfoMapper.selectCountByExample(example);
    }

    @Override
    public int countApiEnableBySourceCode(String automaticSourceCode) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode())
                .andEqualTo("automaticSourceCode", automaticSourceCode);
        return apiTestCaseMapper.selectCountByExample(example);
    }

    @Override
    public void batchAddPreDataVariable(BatchAddPreDataVariableCommand command) {
        List<ApiTestVariableEntity> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(command.getInputParameter())) {
            command.getInputParameter().forEach(item -> {
                list.add(buildVariableEntity(command, item, VariableUsageTypeEnum.INPUT, UseCaseFactoryTypeEnum.CREATE));
            });
        }
        if (CollectionUtil.isNotEmpty(command.getOutputParameter())) {
            command.getOutputParameter().forEach(item -> {
                list.add(buildVariableEntity(command, item, VariableUsageTypeEnum.OUTPUT, UseCaseFactoryTypeEnum.CREATE));
            });
        }
        if (CollectionUtil.isNotEmpty(list)) {
            apiTestVariableMapper.batchInsert(list);
        }

    }

    private ApiTestVariableEntity buildVariableEntity(BatchAddPreDataVariableCommand command,
                                                      BasePreDataVariableReq req,
                                                      VariableUsageTypeEnum usageType,
                                                      UseCaseFactoryTypeEnum sceneType) {
        ApiTestVariableEntity entity = new ApiTestVariableEntity();
        entity.setVariableCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        entity.preCreate(command.getTransactor());
        entity.setProductCode(command.getProductCode());
        entity.setProductName(command.getProductName());
        entity.setLinkCode(command.getLinkCode());
        entity.setType(command.getType());
        entity.setRequiredStatus(req.getRequiredStatus());
        entity.setVariableName(req.getVariableName());
        entity.setVariableKey(req.getVariableKey());
        entity.setVariableValue(req.getVariableValue());
        entity.setUsageType(usageType);
        entity.setSceneType(sceneType.getCode());
        entity.setEnable(true);
        entity.setVariableStatus(true);
        entity.setVariableDesc("");
        entity.setSubVariableType(SubVariableTypeEnum.CUSTOM);
        entity.setLoginValidTime(0);
        return entity;
    }

    @Override
    public ApiTestVariableVO getUniqueDbVariable(String productCode, String variableKey, Integer sceneType, VariableTypeEnum variableType, String businessCode) {
        ApiTestVariableEntity entity = apiTestVariableMapper.getUniqueDbVariable(productCode, variableKey, sceneType, variableType, businessCode);
        return apiTestEntityConverter.converter(entity);
    }

    @Override
    public List<SceneIndexVO> querySharedPreData(SharedSceneModuleQueryReq req) {
        return sceneIndexMapper.selectSharedPreData(req);
    }

    @Override
    public List<SceneIndexVO> querySharedPreDataModuleByProduct(List<String> productList) {
        return sceneIndexMapper.selectSharedPreDataModuleByProduct(productList);
    }

    @Override
    public List<ApiTestEntityDO> queryApiTestByDocVersion(String docVersion) {
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria()
                .andEqualTo("docVersion", docVersion)
                .andIn("enable", Arrays.asList(
                        ApiTestEnableEnum.ONLINE.getCode(),
                        ApiTestEnableEnum.OFFLINE.getCode(),
                        ApiTestEnableEnum.UNKNOWN.getCode()));
        example.selectProperties("apiCode");
        return apiTestEntityConverter.convertApiTestEntityDO(apiTestMapper.selectByExample(example));
    }

    @Override
    public PageInfo<PageApiLiteResp> queryPageSceneApi(PageApiLiteReq req) {
        PageHelper.startPage(req.getPage(), req.getSize());
        Example example = new Example(SceneApiRelationEntity.class);
        example.selectProperties("productCode", "appId", "apiType", "apiAddress", "relatedApiName").setDistinct(true);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("productCode", req.getProductCode());
        if (StringUtils.isNotBlank(req.getAppId())) {
            criteria.andEqualTo("appId", req.getAppId());
        }
        if (null != req.getApiType() && StringUtils.isNotBlank(req.getApiType().name())) {
            criteria.andEqualTo("apiType", req.getApiType().name());
        }
        Example.Criteria orCriteria = example.createCriteria();
        if (StringUtils.isNotBlank(req.getNameOrAddress())) {
            orCriteria.orLike("apiAddress", "%" + req.getNameOrAddress() + "%")
                    .orLike("relatedApiName", "%" + req.getNameOrAddress() + "%");
        }
        example.and(orCriteria);
        List<SceneApiRelationEntity> entityList = sceneApiRelationMapper.selectByExample(example);
        return new PageInfo<>(CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : this.convertApiRelationEntity2Resp(entityList));
    }

    @Override
    public void deleteSceneApiRelationBySceneCode(String sceneCode) {
        Example example = new Example(SceneApiRelationEntity.class);
        example.createCriteria().andEqualTo("sceneCode", sceneCode);
        sceneApiRelationMapper.deleteByExample(example);
    }

    @Override
    public void insertSceneApiRelation(SceneApiRelationEntityDO entityDO) {
        sceneApiRelationMapper.insertSelective(apiTestEntityConverter.convert(entityDO));
    }

    @Override
    public List<String> querySceneApiRelationAllSceneCode(String productCode) {
        Example example = new Example(SceneApiRelationEntity.class);
        if (StringUtils.isNotEmpty(productCode)) {
            example.createCriteria().andEqualTo("productCode", productCode);
        }
        example.selectProperties("sceneCode").setDistinct(true);
        List<SceneApiRelationEntity> list = sceneApiRelationMapper.selectByExample(example);
        return list.stream().map(SceneApiRelationEntity::getSceneCode).collect(Collectors.toList());
    }

    @Override
    public void addSceneDbAuthorize(List<SceneDatabaseAuthorizeEntityDO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        list.forEach(e -> {
            sceneDatabaseAuthorizeMapper.insertSelective(apiTestEntityConverter.convert(e));
        });
    }

    @Override
    public void dbRevoke(String productCode, String authorizeProductCode) {
        if (StringUtils.isBlank(productCode) || StringUtils.isBlank(authorizeProductCode)) {
            return;
        }
        Example example = new Example(SceneDatabaseAuthorizeEntity.class);
        example.createCriteria().andEqualTo("productCode", productCode)
                .andEqualTo("authorizeProductCode", authorizeProductCode);
        SceneDatabaseAuthorizeEntity entity = new SceneDatabaseAuthorizeEntity();
        entity.setEnable(false);
        sceneDatabaseAuthorizeMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<SceneDatabaseAuthorizeEntityDO> queryAuthorizeList(String productCode) {
        return apiTestEntityConverter.convertList(sceneDatabaseAuthorizeMapper.queryAuthorizeList(productCode));
    }

    @Override
    public List<SceneDatabaseAuthorizeEntityDO> queryAuthorizeInfo(String authorizeProductCode) {
        Example example = new Example(SceneDatabaseAuthorizeEntity.class);
        example.createCriteria().andEqualTo("authorizeProductCode", authorizeProductCode)
                .andEqualTo("enable", true);
        return apiTestEntityConverter.convertList(sceneDatabaseAuthorizeMapper.selectByExample(example));
    }

    @Override
    public List<String> queryPageApiCode(PageApiInfoReq req) {
        PageHelper.startPage(req.getPage(), req.getSize(), false);
        return apiTestMapper.selectApiCode(req);
    }

    @Override
    public void addApiTestCase(AddApiTestCaseEvent event) {
        event.setLatestTaskId("");
        apiTestCaseMapper.insertSelective(apiTestEntityConverter.converter(event));
    }

    @Override
    public List<TmApiTestCaseVO> queryUniqueApiTestCase(String apiCode, String caseCode, String caseName) {
        if (StringUtils.isBlank(caseCode)) {
            return new ArrayList<>();
        }
        Example example = new Example(ApiTestCaseEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", ApiCaseEnableEnum.DRAFT.getCode())
                .andEqualTo("apiCode", apiCode)
                .andEqualTo("caseName", caseName)
                .andEqualTo("caseType", ApiCaseTypeEnum.API_CASE.getCode());
        if (StringUtil.isNotEmpty(caseCode)) {
            criteria.andNotEqualTo("caseCode", caseCode);
        }
        List<ApiTestCaseEntity> list = apiTestCaseMapper.selectByExample(example);
        return apiTestEntityConverter.converterToTmApiTestCaseVOList(list);
    }

    @Override
    public void editApiTestCase(EditApiTestCaseEvent event) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andEqualTo("caseCode", event.getCaseCode())
                .andEqualTo("status", event.getStatus());
        ApiTestCaseEntity entity = apiTestEntityConverter.converterEventToVo(event);
        apiTestCaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public TmApiTestCaseVO getApiTestCase(String caseCode, ApiCaseStatusEnum status) {
        Example example = new Example(ApiTestCaseEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("caseCode", caseCode);
        criteria.andEqualTo("status", status);
        criteria.andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        ApiTestCaseEntity entity = apiTestCaseMapper.selectOneByExample(example);
        return apiTestEntityConverter.converterToTmApiTestCaseVO(entity);
    }

    @Override
    public List<ApiTestCaseEntityDO> queryExceptionCaseByParentCaseCode(String parentCaseCode, ApiCaseStatusEnum status, boolean sorted, List<Integer> caseTypeList) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("parentCaseCode", parentCaseCode)
                .andEqualTo("status", status)
                .andIn("caseType", caseTypeList)
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        if (sorted) {
            example.orderBy("gmtCreate").desc();
        }
        return apiTestEntityConverter.convertApiTestCaseEntityDO(apiTestCaseMapper.selectByExample(example));
    }

    @Override
    public void updateExceptionCaseByCode(ApiTestCaseEntityDO entityDO) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("caseCode", entityDO.getCaseCode())
                .andEqualTo("caseType", ApiCaseTypeEnum.EXCEPTION_CASE.getCode());
        apiTestCaseMapper.updateByExampleSelective(apiTestEntityConverter.convertApiTestCaseEntity(entityDO), example);
    }

    @Override
    public void batchDeleteApiCaseExceptionByCodeList(List<String> caseCodeList, User transactor) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andIn("caseCode", caseCodeList)
                .andEqualTo("caseType", ApiCaseTypeEnum.EXCEPTION_CASE.getCode());
        ApiTestCaseEntity entity = new ApiTestCaseEntity();
        entity.setEnable(ApiCaseEnableEnum.DELETED);
        entity.preUpdate(transactor);
        apiTestCaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void updateApiTestCaseSelective(ApiTestCaseEntityDO entityDO) {
        apiTestCaseMapper.updateByPrimaryKeySelective(apiTestEntityConverter.convertApiTestCaseEntity(entityDO));
    }

    @Override
    public void insertApiTestCaseSelective(ApiTestCaseEntityDO entityDO) {
        apiTestCaseMapper.insertSelective(apiTestEntityConverter.convertApiTestCaseEntity(entityDO));
    }

    @Override
    public List<PageApiTestCaseVO> queryParentApiTestCaseList(PageApiTestCaseQuery query) {
        List<PageApiTestCaseVO> resultList = queryApiTestCaseList(query, "");
        if (CollectionUtil.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        //发布态
        if (CollectionUtil.isEmpty(query.getEnableNumList()) || query.getEnableNumList().size() > 1) {
            Example example = new Example(ApiTestCaseEntity.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("caseCode", resultList.stream().map(PageApiTestCaseVO::getCaseCode).collect(Collectors.toSet()))
                    .andEqualTo("status", ApiCaseStatusEnum.publish.name())
                    .andNotEqualTo("enable", Boolean.FALSE);
            example.orderBy("gmtModified").desc();
            List<ApiTestCaseEntity> publishList = apiTestCaseMapper.selectByExample(example);
            if (CollectionUtil.isNotEmpty(publishList)) {
                publishList.forEach(publish -> resultList.forEach(result -> {
                    if (publish.getCaseCode().equals(result.getCaseCode())) {
                        result.setStatus(publish.getStatus());
                        result.setEnable(publish.getEnable());
                        result.setApiCode(publish.getApiCode());
                        result.setApiName(publish.getRelatedApiName());
                        result.setApiAddress(publish.getRelatedApiAddress());
                        result.setTestResult(publish.getLatestExecuteResult());
                        result.setTaskCode(publish.getLatestTaskId());
                        result.setExecuteTime(publish.getLatestExecuteTime());
                        result.setTagValue(publish.getTagValue());
                    }
                }));
            }
        }

        //子用例数
        Set<String> publishCodeSet = resultList.stream()
                .filter(item -> (ApiCaseStatusEnum.publish.equals(item.getStatus())))
                .map(PageApiTestCaseVO::getCaseCode).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(publishCodeSet)) {
            Example example = new Example(ApiTestCaseEntity.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("parentCaseCode", publishCodeSet)
                    .andIn("caseType", Arrays.asList(ApiCaseTypeEnum.EXCEPTION_CASE.getCode(), ApiCaseTypeEnum.NORMAL_CASE.getCode()))
                    .andEqualTo("status", ApiCaseStatusEnum.publish.name())
                    .andNotEqualTo("enable", Boolean.FALSE);
            example.orderBy("gmtModified").desc();
            List<ApiTestCaseEntity> children = apiTestCaseMapper.selectByExample(example);
            if (CollectionUtil.isNotEmpty(children)) {
                children.stream().collect(Collectors.groupingBy(ApiTestCaseEntity::getParentCaseCode))
                        .forEach((key, value) -> resultList.forEach(result -> {
                            if (key.equals(result.getCaseCode())) {
                                result.setExecuteCaseCount(value.size());
                            }
                        }));
            }
        }

        //执行耗时
        Set<String> taskCodeSet = resultList.stream()
                .filter(item -> (ApiCaseStatusEnum.publish.equals(item.getStatus()) && StringUtils.isNotBlank(item.getTaskCode())))
                .map(PageApiTestCaseVO::getTaskCode).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(taskCodeSet)) {
            Example queryExecuteDurationExample = new Example(AutomaticTaskEntity.class);
            Example.Criteria queryExecuteDurationCriteria = queryExecuteDurationExample.createCriteria();
            queryExecuteDurationCriteria.andIn("code", taskCodeSet)
                    .andEqualTo("enable", Boolean.TRUE);
            List<AutomaticTaskEntity> taskList = automaticTaskMapper.selectByExample(queryExecuteDurationExample);
            if (CollectionUtil.isNotEmpty(taskList)) {
                taskList.forEach(item -> resultList.forEach(result -> {
                    if (item.getCode().equals(result.getTaskCode())) {
                        result.setDuration(DateUtil.secondBetween(item.getStartTime(), item.getFinishTime()));
                    }
                }));
            }
        }
        return resultList;
    }

    @Override
    public List<PageApiTestCaseVO> queryApiTestCaseList(PageApiTestCaseQuery query, String parentCode) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.selectProperties("id", "caseCode", "caseName", "productCode",
                "apiCode", "status", "caseType", "parentCaseCode", "latestTaskId",
                "latestExecuteResult", "enable", "gmtModified", "relatedApiAddress",
                "relatedApiName", "apiType", "docVersion", "tagValue", "latestExecuteTime", "generateFieldSource", "generateRules");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("productCode", query.getProductCode())
                .andNotEqualTo("enable", Boolean.FALSE);
        if (query.getQueryEnable()) {
            criteria.andEqualTo("status", ApiCaseStatusEnum.publish.name());
        } else if (query.getQueryDraft()) {
            criteria.andEqualTo("status", ApiCaseStatusEnum.edit.name())
                    .andEqualTo("published", 0);
        } else {
            criteria.andEqualTo("status", ApiCaseStatusEnum.edit.name());
        }
        if (StringUtils.isNotBlank(parentCode)) {
            criteria.andEqualTo("parentCaseCode", parentCode);
        }
        if (StringUtils.isNotBlank(query.getApiCode())) {
            criteria.andEqualTo("apiCode", query.getApiCode());
        }
        if (StringUtils.isNotBlank(query.getCaseName())) {
            criteria.andLike("caseName", "%" + query.getCaseName() + "%");
        }
        if (null != query.getStartTime() && null != query.getEndTime()) {
            criteria.andBetween("latestExecuteTime", query.getStartTime(), query.getEndTime());
        }
        if (CollectionUtil.isNotEmpty(query.getApiTypeList())) {
            criteria.andIn("apiType", query.getApiTypeList());
        }
        if (CollectionUtil.isNotEmpty(query.getTypeNumList())) {
            criteria.andIn("caseType", query.getTypeNumList());
        }
        if (CollectionUtil.isNotEmpty(query.getApiNameList())) {
            criteria.andIn("relatedApiName", query.getApiNameList());
        }
        if (CollectionUtil.isNotEmpty(query.getApiAddressList())) {
            criteria.andIn("relatedApiAddress", query.getApiAddressList());
        }
        if (CollectionUtil.isNotEmpty(query.getTestResultList())) {
            criteria.andIn("latestExecuteResult", query.getTestResultList());
        }
        if (CollectionUtil.isNotEmpty(query.getDocVersionList())) {
            criteria.andIn("docVersion", query.getDocVersionList());
        }
        Example.Criteria orCriteria = example.createCriteria();
        if (CollectionUtil.isNotEmpty(query.getTagList())) {
            query.getTagList().forEach(tag -> {
                orCriteria.orLike("tagValue", "%" + tag + "%");
            });
        }
        example.and(orCriteria);
        example.orderBy("gmtModified").desc();
        List<ApiTestCaseEntity> entityList = apiTestCaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : apiTestEntityConverter.convertPageApiTestCaseVOList(entityList);
    }

    @Override
    public ApiTestCaseEntityDO selectApiTestCaseByCodeAndStatus(String apiCaseCode, ApiCaseStatusEnum status) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("caseCode", apiCaseCode)
                .andEqualTo("status", status.name());
        example.orderBy("gmtModified").desc();
        List<ApiTestCaseEntity> entityList = apiTestCaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? null : apiTestEntityConverter.convertApiTestCaseEntityDO(entityList.get(0));
    }

    @Override
    public void updateApiTestCaseEnable(ChangeApiCaseStatusCommand command) {
        ApiTestCaseEntity toUpdate = new ApiTestCaseEntity();
        toUpdate.setEnable(command.getOperation().mapping());
        toUpdate.preUpdate(command.getTransactor());

        Example example = new Example(ApiTestCaseEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.orEqualTo("caseCode", command.getApiCaseCode())
                .orEqualTo("parentCaseCode", command.getApiCaseCode());
        apiTestCaseMapper.updateByExampleSelective(toUpdate, example);
    }

    @Override
    public List<ApiTestCaseEnableVO> queryApiCaseEnable(String productCode, List<String> codeList, String status) {
        return apiTestCaseMapper.queryApiCaseEnable(productCode, codeList, status);
    }

    @Override
    public void batchChangeApiTestCaseEnable(BatchChangeApiCaseStatusCommand command) {
        if (CollectionUtil.isEmpty(command.getApiCaseCodeList())
                || Objects.isNull(command.getOperation().mapping())) {
            log.warn("batchChangeApiTestCaseEnable_param_is_null!");
            return;
        }
        ApiTestCaseEntity toUpdate = new ApiTestCaseEntity();
        toUpdate.setEnable(command.getOperation().mapping());
        toUpdate.preUpdate(command.getTransactor());

        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andIn("caseCode", command.getApiCaseCodeList())
                .andEqualTo("productCode", command.getProductCode())
                .andEqualTo("caseType", ApiCaseTypeEnum.API_CASE.getCode())
                .andNotIn("enable",
                        Arrays.asList(ApiCaseEnableEnum.DELETED.getCode(), ApiCaseEnableEnum.DRAFT.getCode(), command.getOperation().mapping().getCode()));
        apiTestCaseMapper.updateByExampleSelective(toUpdate, example);

        Example example1 = new Example(ApiTestCaseEntity.class);
        example1.createCriteria()
                .andIn("parentCaseCode", command.getApiCaseCodeList())
                .andEqualTo("productCode", command.getProductCode())
                .andIn("caseType", Arrays.asList(ApiCaseTypeEnum.NORMAL_CASE.getCode(), ApiCaseTypeEnum.EXCEPTION_CASE.getCode()))
                .andNotIn("enable",
                        Arrays.asList(ApiCaseEnableEnum.DELETED.getCode(), ApiCaseEnableEnum.DRAFT.getCode(), command.getOperation().mapping().getCode()));
        apiTestCaseMapper.updateByExampleSelective(toUpdate, example1);
    }

    @Override
    public void batchDeleteApiTestCase(BatchChangeApiCaseStatusCommand command) {
        if (CollectionUtil.isEmpty(command.getApiCaseCodeList())
                || Objects.isNull(command.getOperation().mapping())) {
            log.warn("batchDeleteApiTestCase_param_is_null!");
            return;
        }
        ApiTestCaseEntity toDelete = new ApiTestCaseEntity();
        toDelete.setEnable(ApiCaseEnableEnum.DELETED);
        toDelete.preUpdate(command.getTransactor());

        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andIn("caseCode", command.getApiCaseCodeList())
                .andEqualTo("productCode", command.getProductCode())
                .andIn("caseType", Arrays.asList(ApiCaseTypeEnum.API_CASE.getCode(), ApiCaseTypeEnum.SYSTEM_CASE.getCode()))
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        apiTestCaseMapper.updateByExampleSelective(toDelete, example);

        Example example1 = new Example(ApiTestCaseEntity.class);
        example1.createCriteria()
                .andIn("parentCaseCode", command.getApiCaseCodeList())
                .andEqualTo("productCode", command.getProductCode())
                .andIn("caseType", Arrays.asList(ApiCaseTypeEnum.NORMAL_CASE.getCode(), ApiCaseTypeEnum.EXCEPTION_CASE.getCode()))
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        apiTestCaseMapper.updateByExampleSelective(toDelete, example1);
    }

    @Override
    public List<ApiTestCaseEntityDO> queryExceptionCaseByCodeList(List<String> list) {
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andIn("caseCode", list);
        return apiTestEntityConverter.convertApiTestCaseEntityDO(apiTestCaseMapper.selectByExample(example));
    }

    @Override
    public List<ApiTestCaseEntityDO> queryApiCaseDataByApiCode(List<String> list) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andIn("apiCode", list)
                .andEqualTo("status", ApiCaseStatusEnum.publish)
                .andEqualTo("enable", ApiCaseEnableEnum.ENABLED.getCode())
                .andIn("caseType", Arrays.asList(ApiCaseTypeEnum.EXCEPTION_CASE.getCode(), ApiCaseTypeEnum.NORMAL_CASE.getCode()));
        return apiTestEntityConverter.convertApiTestCaseEntityDO(apiTestCaseMapper.selectByExample(example));
    }

    @Override
    public List<ApiTestCaseEntityDO> queryApiCaseDataByParentCode(List<String> list) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andIn("parentCaseCode", list)
                .andEqualTo("status", ApiCaseStatusEnum.publish)
                .andEqualTo("enable", ApiCaseEnableEnum.ENABLED.getCode())
                .andIn("caseType", Arrays.asList(ApiCaseTypeEnum.EXCEPTION_CASE.getCode(), ApiCaseTypeEnum.NORMAL_CASE.getCode()));
        return apiTestEntityConverter.convertApiTestCaseEntityDO(apiTestCaseMapper.selectByExample(example));
    }

    @Override
    public void editDraftApiTestCaseStatus(String caseCode, ApiCaseEnableEnum enabled, User transactor) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andEqualTo("caseCode", caseCode)
                .andEqualTo("status", ApiCaseStatusEnum.edit.name());
        ApiTestCaseEntity entity = new ApiTestCaseEntity();
        entity.setEnable(ApiCaseEnableEnum.ENABLED);
        entity.setPublished(Boolean.TRUE);
        entity.preUpdate(transactor);
        apiTestCaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<CountApiTestCaseChildrenVO> selectApiTestCaseListByCode(Set<String> codeSet, String productCode) {
        if (CollectionUtil.isEmpty(codeSet)) {
            return new ArrayList<>();
        }
        return apiTestCaseMapper.selectApiTestCaseListByCode(codeSet, productCode);
    }

    @Override
    public List<ApiTestCaseExecuteDetailTiledVO> queryApiTestCaseExecuteDetail(ApiTestCaseExecuteDetailQuery query) {
        return apiTestCaseMapper.queryApiTestCaseExecuteDetail(query);
    }

    @Override
    public void updateApiTestCaseSelectiveByCaseCodeAndStatus(ApiTestCaseEntityDO entityDO) {
        ApiTestCaseEntity toUpdate = apiTestEntityConverter.convertApiTestCaseEntity(entityDO);

        Example example = new Example(ApiTestCaseEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("caseCode", entityDO.getCaseCode())
                .andEqualTo("status", entityDO.getStatus());
        apiTestCaseMapper.updateByExampleSelective(toUpdate, example);
    }

    @Override
    public int countApiTestCaseByAutomaticSourceCode(String automaticSourceCode) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("automaticSourceCode", automaticSourceCode);
        return apiTestCaseMapper.selectCountByExample(example);
    }

    @Override
    public void deleteApiCaseByParentCaseCode(String parentCaseCode, ApiCaseStatusEnum status, User transactor) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andEqualTo("parentCaseCode", parentCaseCode)
                .andEqualTo("status", status.name());
        ApiTestCaseEntity entity = new ApiTestCaseEntity();
        entity.setEnable(ApiCaseEnableEnum.DELETED);
        entity.preUpdate(transactor);
        apiTestCaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void batchInsertApiTestCase(List<ApiTestCaseEntityDO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<List<ApiTestCaseEntityDO>> partition = Lists.partition(list, 200);
        partition.forEach(e -> apiTestCaseMapper.batchInsert(e));
    }

    @Override
    public List<String> selectAutomaticSourceCodeList(List<String> apiCaseCodes, ApiCaseStatusEnum status) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andIn("caseCode", apiCaseCodes)
                .andEqualTo("status", status.name());
        example.selectProperties("automaticSourceCode", "automaticSourceCode");
        example.setDistinct(true);
        List<String> entityList = Optional.ofNullable(apiTestCaseMapper.selectByExample(example))
                .orElse(Collections.emptyList()).stream()
                .filter(t -> StringUtil.isNotEmpty(t.getAutomaticSourceCode()))
                .map(ApiTestCaseEntity::getAutomaticSourceCode)
                .collect(Collectors.toList());
        return entityList;
    }

    @Override
    public List<ApiTestCaseEntityDO> querySubApiCasesByParentCaseCodes(List<String> parentCaseCodes, ApiCaseStatusEnum status) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andIn("parentCaseCode", parentCaseCodes)
                .andEqualTo("status", status)
                .andIn("caseType", Arrays.asList(ApiCaseTypeEnum.EXCEPTION_CASE.getCode(), ApiCaseTypeEnum.NORMAL_CASE.getCode()))
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        return apiTestEntityConverter.convertApiTestCaseEntityDO(apiTestCaseMapper.selectByExample(example));
    }

    @Override
    public List<String> queryApiCaseCodeByApiCode(String apiCode) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andEqualTo("apiCode", apiCode)
                .andEqualTo("parentCaseCode", "")
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        example.selectProperties("caseCode").setDistinct(true);
        return Optional.ofNullable(apiTestCaseMapper.selectByExample(example))
                .orElse(Collections.emptyList())
                .stream()
                .map(ApiTestCaseEntity::getCaseCode)
                .collect(Collectors.toList());
    }

    @Override
    public String selectApiCodeByAutomaticSourceCode(String automaticSourceCode) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andEqualTo("automaticSourceCode", automaticSourceCode);
        example.selectProperties("apiCode", "apiCode");
        example.setDistinct(true);
        if (null == apiTestCaseMapper.selectOneByExample(example)) {
            return "";
        }
        return apiTestCaseMapper.selectOneByExample(example).getApiCode();
    }

    @Override
    public ApiTestCaseEntityDO queryNormalCaseByParentCode(String parentCaseCode) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andEqualTo("parentCaseCode", parentCaseCode)
                .andEqualTo("status", ApiCaseStatusEnum.edit)
                .andEqualTo("caseType", ApiCaseTypeEnum.NORMAL_CASE.getCode())
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        List<ApiTestCaseEntity> list = apiTestCaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(list) ? null : apiTestEntityConverter.convertApiTestCaseEntityDO(list.get(0));
    }

    @Override
    public int countSystemCaseByApiCode(String apiCode, ApiCaseStatusEnum status) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andEqualTo("apiCode", apiCode)
                .andEqualTo("status", status)
                .andEqualTo("caseType", ApiCaseTypeEnum.SYSTEM_CASE.getCode())
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        return apiTestCaseMapper.selectCountByExample(example);
    }

    @Override
    public void addApiTestCaseTag(String caseCode, SceneTagEnum tagEnum, User transactor) {
        ApiTestCaseEntityDO draft = queryApiCaseByCodeAndStatus(caseCode, ApiCaseStatusEnum.edit);
        if (Objects.isNull(draft)) {
            return;
        }
        String toUpdateTag = StringUtils.isBlank(draft.getTagValue()) ? tagEnum.name()
                : (draft.getTagValue().contains(tagEnum.name()) ? draft.getTagValue() : (draft.getTagValue() + "," + tagEnum.name()));
        ApiTestCaseEntity entity = new ApiTestCaseEntity();
        entity.setTagValue(toUpdateTag);
        entity.preUpdate(transactor);
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("caseCode", caseCode);
        apiTestCaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public void deleteApiTestCaseTag(String caseCode, List<SceneTagEnum> tagEnumList, User transactor) {
        if (CollectionUtil.isEmpty(tagEnumList)) {
            return;
        }
        List<ApiTestCaseEntityDO> toUpdateCaseList = getApiCaseByCodes(Collections.singletonList(caseCode));
        if (CollectionUtil.isEmpty(toUpdateCaseList)) {
            return;
        }
        Set<String> localTagSet = new HashSet<>();
        toUpdateCaseList.forEach(toUpdateCase -> {
            if (Objects.nonNull(toUpdateCase) && ApiCaseEnableEnum.ENABLED.equals(toUpdateCase.getEnable())
                    && StringUtils.isNotBlank(toUpdateCase.getTagValue())) {
                localTagSet.addAll(Arrays.asList(toUpdateCase.getTagValue().split(",")));
            }
        });
        if (CollectionUtil.isEmpty(localTagSet)) {
            return;
        }
        StringBuilder toUpdateTagValue = new StringBuilder();
        Set<String> toDeleteSet = tagEnumList.stream().map(SceneTagEnum::name).collect(Collectors.toSet());
        localTagSet.forEach(localTag -> {
            if (!toDeleteSet.contains(localTag)) {
                toUpdateTagValue.append(localTag).append(",");
            }
        });
        ApiTestCaseEntity entity = new ApiTestCaseEntity();
        entity.setTagValue(toUpdateTagValue.substring(0, toUpdateTagValue.length()));
        entity.preUpdate(transactor);
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("caseCode", caseCode);
        apiTestCaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<ApiTestCaseEntityDO> selectApiTestCaseListByCaseType(List<Integer> caseTypeList, Set<String> apiCodeSet) {
        if (CollectionUtil.isEmpty(caseTypeList)) {
            return new ArrayList<>();
        }
        Example example = new Example(ApiTestCaseEntity.class);
        example.selectProperties("id", "caseCode", "caseName", "productCode",
                "apiCode", "status", "caseType", "parentCaseCode", "latestTaskId",
                "latestExecuteResult", "enable", "gmtModified", "relatedApiAddress",
                "relatedApiName", "apiType", "docVersion", "tagValue", "latestExecuteTime");
        Example.Criteria criteria = example.createCriteria();
        if (CollectionUtil.isNotEmpty(apiCodeSet)) {
            criteria.andIn("apiCode", apiCodeSet);
        }
        criteria.andIn("caseType", caseTypeList)
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        List<ApiTestCaseEntity> list = apiTestCaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(list) ? new ArrayList<>() : apiTestEntityConverter.convertApiTestCaseEntityDO(list);
    }

    @Override
    public List<ApiTestEntityDO> selectApiTestByCodeList(Set<String> apiCodeSet) {
        if (CollectionUtil.isEmpty(apiCodeSet)) {
            return new ArrayList<>();
        }
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria()
                .andIn("apiCode", apiCodeSet)
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        List<ApiTestEntity> entityList = apiTestMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : apiTestEntityConverter.convertApiTestEntityDO(entityList);
    }

    @Override
    public void updateCaseApiInfo(ApiTestEntityDO apiTest) {
        ApiTestCaseEntity toUpdateEntity = new ApiTestCaseEntity();
        toUpdateEntity.setApiType(apiTest.getApiType());
        toUpdateEntity.setRelatedApiAddress(apiTest.getApiAddress());
        toUpdateEntity.setRelatedApiName(apiTest.getApiName());
        toUpdateEntity.setDocVersion(apiTest.getDocVersion());

        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("apiCode", apiTest.getApiCode())
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        apiTestCaseMapper.updateByExampleSelective(toUpdateEntity, example);
    }

    @Override
    public void updateApiCaseExecuteResultByTaskId(AutomaticTaskEntityDO taskEntityDO) {
        ApiTestCaseEntity toUpdateEntity = new ApiTestCaseEntity();
        toUpdateEntity.setLatestExecuteResult(TestPlanCaseStatusEnum.getApiCaseExecuteResult(taskEntityDO.getStatus().name()));
        toUpdateEntity.setLatestExecuteTime(taskEntityDO.getFinishTime());

        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("latestTaskId", taskEntityDO.getCode())
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        apiTestCaseMapper.updateByExampleSelective(toUpdateEntity, example);
    }

    @Override
    public void updateApiTestCasePublishFlag(Set<String> caseCodeSet) {
        ApiTestCaseEntity toUpdateEntity = new ApiTestCaseEntity();
        toUpdateEntity.setPublished(Boolean.TRUE);

        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andIn("caseCode", caseCodeSet)
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        apiTestCaseMapper.updateByExampleSelective(toUpdateEntity, example);
    }

    @Override
    public void updateApiTestCaseTag(String caseCode, String tagValue) {
        ApiTestCaseEntity toUpdateEntity = new ApiTestCaseEntity();
        toUpdateEntity.setTagValue(tagValue);

        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("caseCode", caseCode)
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        apiTestCaseMapper.updateByExampleSelective(toUpdateEntity, example);
    }

    @Override
    public List<String> queryApiCaseTagByApiCode(String apiCode) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andEqualTo("apiCode", apiCode)
                .andEqualTo("parentCaseCode", "")
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        example.selectProperties("tagValue");
        return Optional.ofNullable(apiTestCaseMapper.selectByExample(example))
                .orElse(Collections.emptyList())
                .stream()
                .map(ApiTestCaseEntity::getTagValue)
                .collect(Collectors.toList());
    }

    @Override
    public List<ApiCaseExceptionVO> queryExceptionCaseList(PageApiExceptionCaseQuery query) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.selectProperties("caseCode", "caseReqData");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("parentCaseCode", query.getParentCaseCode())
                .andEqualTo("caseType", ApiCaseTypeEnum.EXCEPTION_CASE.getCode())
                .andEqualTo("status", query.getStatus())
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        if (StringUtils.isNotBlank(query.getKey())) {
            criteria.andLike("generateFieldSource", "%" + query.getKey() + "%");
        }
        if (StringUtils.isNotBlank(query.getCaseCode())) {
            criteria.andEqualTo("caseCode", query.getCaseCode());
        }
        if (CollectionUtil.isNotEmpty(query.getGenerateRulesList())) {
            criteria.andIn("generateRules", query.getGenerateRulesList());
        }
        if (StringUtils.isNotBlank(query.getOrderField()) && StringUtils.isNotBlank(query.getOrderType())) {
            if ("asc".equalsIgnoreCase(query.getOrderType())) {
                example.orderBy(query.getOrderField()).asc();
            } else {
                example.orderBy(query.getOrderField()).desc();
            }
        } else {
            example.orderBy("gmtModified").desc();
        }
        List<ApiTestCaseEntity> entityList = apiTestCaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : apiTestEntityConverter.convertApiTestCaseVO(entityList);
    }

    @Override
    public List<ApiTestCaseEntityDO> queryApiExceptionCase(List<String> caseCodes, Integer caseType) {
        Example example = new Example(ApiTestCaseEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("caseType", caseType)
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        if (CollectionUtils.isNotEmpty(caseCodes)) {
            criteria.andIn("caseCode", caseCodes);
        }
        List<ApiTestCaseEntity> list = apiTestCaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(list) ? new ArrayList<>() : apiTestEntityConverter.convertApiTestCaseEntityDO(list);
    }

    @Override
    public void batchUpdateApiExceptionCase(List<ApiTestCaseEntityDO> list) {
        list.forEach(item ->
                apiTestCaseMapper.updateByPrimaryKeySelective(apiTestEntityConverter.convertApiTestCaseEntity(item))
        );
    }

    public List<PageApiLiteResp> convertApiRelationEntity2Resp(List<SceneApiRelationEntity> list) {
        if (list == null) {
            return null;
        }

        List<PageApiLiteResp> list1 = new ArrayList<PageApiLiteResp>(list.size());
        for (SceneApiRelationEntity sceneApiRelationEntity : list) {
            list1.add(sceneApiRelationEntityToPageApiLiteResp(sceneApiRelationEntity));
        }

        return list1;
    }

    protected PageApiLiteResp sceneApiRelationEntityToPageApiLiteResp(SceneApiRelationEntity sceneApiRelationEntity) {
        if (sceneApiRelationEntity == null) {
            return null;
        }

        PageApiLiteResp pageApiLiteResp = new PageApiLiteResp();

        pageApiLiteResp.setApiAddress(sceneApiRelationEntity.getApiAddress());
        pageApiLiteResp.setAppId(sceneApiRelationEntity.getAppId());
        pageApiLiteResp.setProductCode(sceneApiRelationEntity.getProductCode());
        pageApiLiteResp.setApiType(sceneApiRelationEntity.getApiType());
        pageApiLiteResp.setApiName(sceneApiRelationEntity.getRelatedApiName());

        return pageApiLiteResp;
    }

    @Override
    public Set<String> selectDisableSceneCode() {
        Example example = new Example(SceneInfoEntity.class);
        example.createCriteria()
                .andEqualTo("enable", SceneInfoEnableEnum.DELETED.getCode());
        example.selectProperties("sceneCode");
        example.setDistinct(true);
        return Optional.ofNullable(sceneInfoMapper.selectByExample(example)).orElse(Collections.emptyList())
                .stream().map(SceneInfoEntity::getSceneCode).filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
    }

    @Override
    public void batchDeleteSceneApiRelation(List<String> codes) {
        Example example = new Example(SceneApiRelationEntity.class);
        example.createCriteria().andIn("sceneCode", codes);
        sceneApiRelationMapper.deleteByExample(example);
    }

    @Override
    public List<ApiTestCaseEntityDO> selectApiTestCaseContainPreData() {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria()
                .andLike("caseReqData", "%dataCenterSceneCode%")
                .andIn("caseType", Arrays.asList(ApiCaseTypeEnum.API_CASE.getCode(), ApiCaseTypeEnum.SYSTEM_CASE.getCode()))
                .andEqualTo("status", ApiCaseStatusEnum.edit.name())
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        List<ApiTestCaseEntity> list = apiTestCaseMapper.selectByExample(example);
        return CollectionUtil.isEmpty(list) ? null : apiTestEntityConverter.convertApiTestCaseEntityDO(list);
    }

    @Override
    public List<ApiTestEntityDO> queryApiTestCodeByMainApiCode(String mainApiCode) {
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria().andEqualTo("mainApiCode", mainApiCode)
                .andNotEqualTo("enable", ApiTestEnableEnum.DELETED.getCode());
        example.orderBy("gmtModified").desc();
        example.selectProperties("apiCode", "gmtModified");
        return apiTestEntityConverter.convertApiTestEntityDO(apiTestMapper.selectByExample(example));
    }

    @Override
    public void deleteApiTestByMainApiCode(String mainApiCode) {
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria().andEqualTo("mainApiCode", mainApiCode);
        ApiTestEntity entity = new ApiTestEntity();
        entity.setEnable(ApiTestEnableEnum.DELETED);
        apiTestMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<ApiTestEntityDO> queryApiTestDocVersionByMainApiCode(List<String> list) {
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria().andIn("mainApiCode", list)
                .andIn("enable", Arrays.asList(
                        ApiTestEnableEnum.ONLINE.getCode(),
                        ApiTestEnableEnum.OFFLINE.getCode(),
                        ApiTestEnableEnum.UNKNOWN.getCode()));
        example.selectProperties("mainApiCode", "docVersion").setDistinct(true);
        return apiTestEntityConverter.convertApiTestEntityDO(apiTestMapper.selectByExample(example));
    }

    @Override
    public List<String> queryApiCaseAllApiCode(String productCode) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        if (StringUtils.isNotEmpty(productCode)) {
            example.createCriteria().andEqualTo("productCode", productCode);
        }
        example.selectProperties("apiCode").setDistinct(true);
        return Optional.ofNullable(apiTestCaseMapper.selectByExample(example))
                .orElse(Collections.emptyList())
                .stream().map(ApiTestCaseEntity::getApiCode)
                .collect(Collectors.toList());
    }

    @Override
    public void updateApiCaseMainApiCode(String apiCode, String mainApiCode) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("apiCode", apiCode)
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        ApiTestCaseEntity entity = new ApiTestCaseEntity();
        entity.setApiCode(mainApiCode);
        apiTestCaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public ApiTestEntityDO queryLatestApiTestByMainCode(String mainApiCode) {
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria()
                .andEqualTo("mainApiCode", mainApiCode)
                .andEqualTo("modifyFlag", 1);
        List<ApiTestEntity> list = apiTestMapper.selectByExample(example);
        return CollectionUtil.isEmpty(list) ? null : apiTestEntityConverter.convertApiTestEntityDO(list.get(0));
    }

    @Override
    public void updateApiTestTagByMainApiCode(String mainApiCode, String tagValue) {
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria().andEqualTo("mainApiCode", mainApiCode)
                .andNotEqualTo("enable", ApiTestEnableEnum.DELETED.getCode());
        example.selectProperties("apiCode", "gmtModified");
        List<ApiTestEntity> list = apiTestMapper.selectByExample(example);
        list.forEach(item -> {
            item.setTagValue(tagValue);
            apiTestMapper.updateByPrimaryKeySelective(item);
        });
    }

    @Override
    public boolean refreshApiTestModifyFlag(String mainApiCode) {
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria().andEqualTo("mainApiCode", mainApiCode)
                .andNotEqualTo("enable", ApiTestEnableEnum.DELETED.getCode());
        example.orderBy("gmtModified").desc();
        example.selectProperties("apiCode", "gmtModified");
        List<ApiTestEntity> list = apiTestMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setModifyFlag(i == 0 ? 1 : 0);
            apiTestMapper.updateByPrimaryKeySelective(list.get(i));
        }
        return true;
    }

    @Override
    public List<String> queryApiCodeByCaseCode(List<String> list) {
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andIn("caseCode", list);
        example.selectProperties("apiCode").setDistinct(true);
        return Optional.ofNullable(apiTestCaseMapper.selectByExample(example))
                .orElse(Collections.emptyList())
                .stream().map(ApiTestCaseEntity::getApiCode)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> queryRelatedApiCodeBySceneCode(String sceneCode) {
        Example example = new Example(SceneApiRelationEntity.class);
        example.createCriteria()
                .andEqualTo("sceneCode", sceneCode)
                .andNotEqualTo("mainApiCode", "");
        example.selectProperties("mainApiCode").setDistinct(true);
        return Optional.ofNullable(sceneApiRelationMapper.selectByExample(example))
                .orElse(Collections.emptyList())
                .stream().map(SceneApiRelationEntity::getMainApiCode)
                .collect(Collectors.toList());
    }

    @Override
    public int countSceneApiRelationByMainApiCode(String mainApiCode) {
        Example example = new Example(SceneApiRelationEntity.class);
        example.createCriteria().andEqualTo("mainApiCode", mainApiCode);
        return sceneApiRelationMapper.selectCountByExample(example);
    }

    @Override
    public void updateApiTestRelatedSceneFlagByMainApiCode(String mainApiCode, Integer relatedSceneFlag) {
        Example example = new Example(ApiTestEntity.class);
        example.createCriteria().andEqualTo("mainApiCode", mainApiCode)
                .andNotEqualTo("enable", ApiTestEnableEnum.DELETED.getCode());
        example.selectProperties("apiCode", "gmtModified");
        List<ApiTestEntity> list = apiTestMapper.selectByExample(example);
        list.forEach(item -> {
            item.setRelatedSceneFlag(relatedSceneFlag);
            apiTestMapper.updateByPrimaryKeySelective(item);
        });
    }

    @Override
    public void updateApiCaseRelatedByApiCode(String apiCode) {
        ApiTestEntityDO apiTest = queryLatestApiTestByMainCode(apiCode);
        if (null == apiTest) {
            return;
        }
        Example example = new Example(ApiTestCaseEntity.class);
        example.createCriteria().andEqualTo("apiCode", apiTest.getMainApiCode())
                .andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode());
        example.and(example.createCriteria()
                .orNotEqualTo("relatedApiName", apiTest.getApiName())
                .orNotEqualTo("relatedApiAddress", apiTest.getApiAddress()));
        ApiTestCaseEntity entity = new ApiTestCaseEntity();
        entity.setRelatedApiName(apiTest.getApiName());
        entity.setRelatedApiAddress(apiTest.getApiAddress());
        apiTestCaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<SceneInfoEntityDO> querySceneInfoTagList(List<String> sceneCodes) {
        Example example = new Example(SceneInfoEntity.class);
        example.createCriteria()
                .andEqualTo("status", SceneInfoStatusEnum.edit.name())
                .andIn("sceneCode", sceneCodes);
        example.selectProperties("sceneCode", "sceneTagData");
        example.setDistinct(true);
        return apiTestEntityConverter.converterToSceneInfoEntityList(sceneInfoMapper.selectByExample(example));
    }

    @Override
    public List<ApiTestEntityDO> queryApiDocId(ApiTestEntityDO entityDO, boolean isPreciseSearch) {
        Example example = new Example(ApiTestEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("productCode", entityDO.getProductCode())
                .andEqualTo("appId", entityDO.getAppId())
                .andEqualTo("apiType", entityDO.getApiType());
        if (isPreciseSearch) {
            criteria.andEqualTo("apiAddress", entityDO.getApiAddress());
        } else {
            criteria.andLike("apiAddress", entityDO.getApiAddress() + "%");
        }
        example.selectProperties("docId", "apiAddress");
        List<ApiTestEntity> entityList = apiTestMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? null : apiTestEntityConverter.convertApiTestEntityDO(entityList);
    }

    @Override
    public boolean isUniqueCaseName(String apiCode, String caseName) {
        if (StringUtil.isBlank(apiCode) || StringUtil.isBlank(caseName)) {
            return false;
        }
        Example example = new Example(ApiTestCaseEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andNotEqualTo("enable", ApiCaseEnableEnum.DELETED.getCode())
                .andEqualTo("apiCode", apiCode)
                .andEqualTo("caseName", caseName)
                .andEqualTo("caseType", ApiCaseTypeEnum.API_CASE.getCode());
        return CollectionUtil.isEmpty(apiTestCaseMapper.selectByExample(example));
    }

}