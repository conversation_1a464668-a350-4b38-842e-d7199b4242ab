package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordLogVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.SimpleTestCase;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.*;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceLogEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TestcaseEntityConverter {

    TestcaseVO converter(TestcaseEntity entity);

    List<TestcaseVO> converterTestcaseList(List<TestcaseEntity> entityList);

    TestcaseEntityDO covert(TestcaseEntity entity);

    List<TestcaseEntityDO> covertList(List<TestcaseEntity> entityList);

    TestcaseEntity covert2Entity(TestcaseEntityDO entityDO);

    List<TestcaseEntity> covert2EntityList(List<TestcaseEntityDO> entityDOList);

    AutomaticRecordLogVO converter(AutomaticSourceLogEntity entity);

    TestcaseEntity convert(AddTestcaseEvent event);

    TestcaseEntity convert(EditTestcaseEvent event);

    TestcaseEntity convertorDeleteToEntity(DeleteTestcaseEvent event);

    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "gmtModified", source = "occurred")
    TestcaseEntity converter(TestcaseStatusChangedEvent event);

    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "gmtModified", source = "occurred")
    TestcaseEntity converter(TestcaseDutyUserChangedEvent event);

    @Mapping(target = "priorityDesc", expression = "java(null == vo.getPriority()? \"\" : vo.getPriority().getValue())")
    @Mapping(target = "statusDesc", expression = "java(null == vo.getStatus()? \"\" : vo.getStatus().getDesc())")
    @Mapping(target = "typeDesc", expression = "java(null == vo.getType()? \"\" : vo.getType().getValue())")
    @Mapping(target = "tags", ignore = true)
    @Mapping(target = "gmtCreate", ignore = true)
    @Mapping(target = "gmtModified", ignore = true)
    com.zto.devops.qc.infrastructure.impexp.entity.TestcaseEntity convertor(ListTestcaseVO vo);

    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "gmtModified", source = "occurred")
    TestcaseEntity convertorEditTitleEntity(EditTestcaseTitleEvent event);

    SimpleTestCase convertSimpleTestCase(TestcaseEntity entity);

    List<SimpleTestCase> convertSimpleTestCase(List<TestcaseEntity> list);
}
