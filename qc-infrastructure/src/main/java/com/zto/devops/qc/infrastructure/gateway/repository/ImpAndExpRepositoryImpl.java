package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.enums.JobStatus;
import com.zto.devops.framework.client.enums.impexp.ExpProcessorEnum;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.infrastructure.dao.entity.ImpExpEntity;
import com.zto.devops.framework.infrastructure.dao.mapper.ImpExpMapper;
import com.zto.devops.qc.client.service.testmanager.cases.model.ImportTestcaseReq;
import com.zto.devops.qc.domain.gateway.repository.ImpExpRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Slf4j
public class ImpAndExpRepositoryImpl implements ImpExpRepository {

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;
    @Autowired
    private ImpExpMapper impExpMapper;

    @Override
    public void insert(ImportTestcaseReq req, long fileSize, String url) {
        ImpExpEntity impExpEntity = new ImpExpEntity();
        impExpEntity.setCode(aggregateIdGenerateService.generateId("SNOWFLAKE"));
        impExpEntity.setImpFilename(req.getFileName());
        impExpEntity.setImpUrl(req.getRemoteFileId());
        impExpEntity.setJobType(ExpProcessorEnum.IMP_TEST_CASE.name());
        impExpEntity.setStatus(JobStatus.FINISHED);
        impExpEntity.setFileSize(fileSize);
        impExpEntity.setErrorMessage(url);
        impExpEntity.setCreatorId(req.getUser().getUserId());
        impExpEntity.setCreator(req.getUser().getUserName());
        impExpEntity.setModifierId(req.getUser().getUserId());
        impExpEntity.setModifier(req.getUser().getUserName());
        impExpEntity.setGmtCreate(new Date());
        impExpEntity.setGmtModified(new Date());
        impExpEntity.setRemoteFileId(url);
        impExpMapper.insertSelective(impExpEntity);
    }
}
