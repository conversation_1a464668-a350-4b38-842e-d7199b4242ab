package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.infrastructure.dao.typehandler.*;
import lombok.Getter;
import lombok.Setter;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * <AUTHOR>
@Getter
@Setter
@Table(name = "tm_testcase")
public class TestcaseEntity extends BaseEntity {

    private Long id;

    /**
     * 用例code
     */
    @Id
    private String code;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 版本code
     */
    private String versionCode;

    /**
     * 上级code
     */
    private String parentCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 属性
     */
    @ColumnType(typeHandler = TestcaseAttributeTypeHandler.class)
    private TestcaseAttributeEnum attribute;

    /**
     * 用例类型
     */
    @ColumnType(typeHandler = TestcaseTypeTypeHandler.class)
    private TestcaseTypeEnum type;

    /**
     * 等级
     */
    @ColumnType(typeHandler = TestcasePriorityTypeHandler.class)
    private TestcasePriorityEnum priority;

    /**
     * 状态
     */
    @ColumnType(typeHandler = TestcaseStatusTypeHandler.class)
    private TestcaseStatusEnum status;

    /**
     * 前置条件
     */
    private String precondition;

    /**
     * 责任人编码
     */
    private Long dutyUserId;

    /**
     * 责任人
     */
    private String dutyUser;

    /**
     * 备注
     */
    private String comment;

    /**
     * 废弃原因
     */
    @ColumnType(typeHandler = TestcaseAbandonReasonTypeHandler.class)
    private TestcaseAbandonReasonEnum abandonReason;

    /**
     * 自动化解析来源code
     */
    private String automaticSourceCode;

    /**
     * 自动化节点类型
     */
    @ColumnType(typeHandler = AutomaticNodeTypeHandler.class)
    private AutomaticNodeTypeEnum nodeType;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 层级
     */
    private Integer layer;

    /**
     * 所有父节点code
     */
    private String path;

    @Transient
    private List<TestcaseEntity> list;

    @Transient
    private Long number;

    private String nodeTypePath;

    /**
     * 是否心跳用例
     */
    private Boolean setHeart;

    private String testcaseModulePath;

    /**
     * dubbo服务接口
     */
    private String interfaceName;

    /**
     * 是否核心用例
     */
    private Boolean setCore;

}
