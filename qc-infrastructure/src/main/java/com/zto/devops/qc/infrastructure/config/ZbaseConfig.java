package com.zto.devops.qc.infrastructure.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import org.springframework.stereotype.Component;

@Component
public class ZbaseConfig {

    private Config config = ConfigService.getAppConfig();

    public String queryProductDbUrl() {
        return config.getProperty("zbase.queryProductDb.url", "");
    }

    public String queryPhysicalDbInfoUrl() {
        return config.getProperty("zbase.queryPhysicalDbInfo.url", "");
    }

    public String queryDbToken() {
        return config.getProperty("zbase.queryDb.token", "");
    }

    public String queryDbAccountInfoUrl() {
        return config.getProperty("zbase.queryDbAccountInfoUrl.url", "");
    }

    public String queryAccountInfoToken() {
        return config.getProperty("zbase.AccountInfo.token", "");
    }

    public String queryRedisUrl() {
        return config.getProperty("zbase.queryRedis.url", "");
    }

    public String queryEsUrl() {
        return config.getProperty("zbase.queryEs.url", "");
    }

    public String queryEsToken() {
        return config.getProperty("zbase.queryEs.token", "");
    }

    public String getZBaseSecret() {
        return config.getProperty("zbase.queryEs.secret", "");
    }

    public String getTopicUrl() {
        return config.getProperty("zbase.queryTopic.url", "");
    }

    public String getDevopsQcToken(){ return config.getProperty("zbase.devopsqc.token","");}

    public String selectDependencyPageListByAppIdUrl(){ return config.getProperty("zbase.selectDependencyPageListByAppId","");}

    public String getMethodsByServiceKey(){ return config.getProperty("zbase.getMethodsByServiceKey","");}
}
