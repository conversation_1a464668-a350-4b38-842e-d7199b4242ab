package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordAnalyticMethodEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticRecordAnalyticMethodHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticRecordTypeHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
@Table(name = "tm_automatic_source_log")
@Data
public class AutomaticSourceLogEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long id;

    /**
     * 编号
     */
    @Id
    private String code;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 自动化解析来源code
     */
    private String automaticSourceCode;

    /**
     * 创建人编码
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date gmtModified;
    /**
     * jmeter:源地址-OSS上的脚本文件地址;xunit-git地址
     */
    private String address;

    /**
     * 类型：1-jmeter, 2-testng, 3-postman, 4-junit,5-pyunit
     */
    @ColumnType(typeHandler = AutomaticRecordTypeHandler.class)
    private AutomaticRecordTypeEnum type;

    private String fileName;

    private String bucketName;

    private String failInformation;

    /**
     * 是否删除 1 未删除, 0 已删除
     */
    private Boolean enable;

    private String status;

    private String commitId;

    private String errorLogFile;

    /**
     * 自动化登记库解析方式
     */
    @ColumnType(typeHandler = AutomaticRecordAnalyticMethodHandler.class)
    private AutomaticRecordAnalyticMethodEnum analyticMethod;
}