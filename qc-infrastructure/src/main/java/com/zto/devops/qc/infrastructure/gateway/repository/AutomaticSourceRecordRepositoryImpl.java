package com.zto.devops.qc.infrastructure.gateway.repository;

import com.google.common.collect.Lists;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseFlagEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseGroupTypeEnum;
import com.zto.devops.qc.client.model.dto.AutomaticSourceLogEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.AutomaticRecordLiteInfoVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordLogVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticSourceLogTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.AnalysisAutomaticRecordAbortQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.AutomaticRecordByNameQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.AutomaticRecordLogQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.MoveModuleReq;
import com.zto.devops.qc.domain.gateway.repository.AutomaticSourceRecordRepository;
import com.zto.devops.qc.infrastructure.converter.AutomaticSourceLogEntityConverter;
import com.zto.devops.qc.infrastructure.converter.AutomaticSourceLogTestcaseEntityConverter;
import com.zto.devops.qc.infrastructure.converter.AutomaticSourceRecordEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceLogEntity;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceLogTestcaseEntity;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceRecordEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.AutomaticSourceLogMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.AutomaticSourceLogTestcaseMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.AutomaticSourceRecordMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.TestcaseMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class AutomaticSourceRecordRepositoryImpl implements AutomaticSourceRecordRepository {

    @Autowired
    private AutomaticSourceRecordMapper automaticSourceRecordMapper;

    @Autowired
    private AutomaticSourceRecordEntityConverter automaticSourceRecordEntityConverter;

    @Autowired
    private TestcaseMapper testcaseMapper;

    @Autowired
    private AutomaticSourceLogMapper automaticSourceLogMapper;

    @Autowired
    private AutomaticSourceLogEntityConverter automaticSourceLogEntityConverter;

    @Autowired
    private AutomaticSourceLogTestcaseEntityConverter automaticSourceLogTestcaseEntityConverter;

    @Autowired
    private AutomaticSourceLogTestcaseMapper automaticSourceLogTestcaseMapper;

    @Override
    public List<AutomaticRecordVO> listAutomaticRecordVO(AutomaticRecordByNameQuery query) {
        Example example = new Example(AutomaticSourceRecordEntity.class);
        example.createCriteria().andEqualTo("enable", true)
                .andEqualTo("productCode", query.getProductCode())
                .andEqualTo("name", query.getName());
        List<AutomaticSourceRecordEntity> list = automaticSourceRecordMapper.selectByExample(example);
        return automaticSourceRecordEntityConverter.convertVOList(list);
    }

    @Override
    public void addAutomaticRecord(AddAutomaticRecordEvent event) {
        AutomaticSourceRecordEntity entity = automaticSourceRecordEntityConverter.convert(event);
        entity.setEnable(true);
        if (AutomaticRecordTypeEnum.API_TEST.equals(event.getType())) {
            entity.setStatus(AutomaticStatusEnum.SUCCESS);
        } else {
            entity.setStatus(AutomaticStatusEnum.IN_PROGRESS);
        }
        entity.preCreate(event);
        automaticSourceRecordMapper.insertSelective(entity);
    }

    @Override
    public void update(AutomaticSourceRecordEntityDO entityDO) {
        AutomaticSourceRecordEntity entity = automaticSourceRecordEntityConverter.convert(entityDO);
        automaticSourceRecordMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public AutomaticSourceRecordEntityDO find(String code) {
        AutomaticSourceRecordEntity entity = automaticSourceRecordMapper.selectByPrimaryKey(code);
        return automaticSourceRecordEntityConverter.convert(entity);
    }

    @Override
    public void editAutomaticRecord(EditAutomaticRecordEvent event) {
        AutomaticSourceRecordEntity entityAutomatic = automaticSourceRecordEntityConverter.convert(event);
        entityAutomatic.preUpdate(event);
        automaticSourceRecordMapper.updateByPrimaryKeySelective(entityAutomatic);

        String testcaseModulePath = (StringUtils.isEmpty(event.getPath()) ? "" : event.getPath() + ".")
                + (StringUtils.isEmpty(event.getTestcaseCode()) ? "" : event.getTestcaseCode() + ".")
                + entityAutomatic.getCode();
        Example example = new Example(TestcaseEntity.class);
        example.createCriteria().andEqualTo("automaticSourceCode", entityAutomatic.getCode());
        TestcaseEntity entity = new TestcaseEntity();
        entity.setTestcaseModulePath(testcaseModulePath);
        testcaseMapper.updateByExampleSelective(entity, example);
    }

    @Override
    public List<AutomaticRecordLogVO> listAutomaticRecordLogVO(AutomaticRecordLogQuery query) {
        Example example = new Example(AutomaticSourceLogEntity.class);
        Example.Criteria criteria = example.createCriteria().andEqualTo("enable", true)
                .andEqualTo("automaticSourceCode", query.getCode());
        if (CollectionUtil.isNotEmpty(query.getList())) {
            criteria.andIn("status", query.getList());
        }
        example.orderBy("gmtCreate").desc();
        List<AutomaticSourceLogEntity> list = automaticSourceLogMapper.selectByExample(example);
        return automaticSourceLogEntityConverter.convertVOList(list);
    }

    @Override
    public void addLog(AddAutomaticRecordLogEvent event) {
        AutomaticSourceLogEntity entity = automaticSourceLogEntityConverter.convert(event);
        entity.setCode(event.getAutomaticSourceCode());
        entity.setAutomaticSourceCode(event.getCode());
        entity.preCreate(event);
        automaticSourceLogMapper.insertSelective(entity);
        if (CollectionUtil.isNotEmpty(event.getList())) {
            List<AutomaticSourceLogTestcaseEntity> list = event.getList().stream()
                    .map(e -> {
                        AutomaticSourceLogTestcaseEntity a = automaticSourceLogTestcaseEntityConverter.convert(e);
                        a.setAutomaticSourceLogCode(entity.getCode());
                        a.setTestcaseCode(a.getCode());
                        a.setCode(AggregateIdUtil.generateId(AggregateType.TEST_CASE));
                        a.preCreate(event);
                        return a;
                    }).collect(Collectors.toList());
            List<List<AutomaticSourceLogTestcaseEntity>> partition = Lists.partition(list, 20);
            for (List<AutomaticSourceLogTestcaseEntity> subList : partition) {
                automaticSourceLogTestcaseMapper.insertList(subList);
            }
        }
        AutomaticSourceRecordEntity entityAutomatic = new AutomaticSourceRecordEntity();
        entityAutomatic.setCode(entity.getAutomaticSourceCode());
        entityAutomatic.setLastAutomaticSourceLogCode(entity.getCode());
        entityAutomatic.preUpdate(event);
        automaticSourceRecordMapper.updateByPrimaryKeySelective(entityAutomatic);
    }

    @Override
    public void editLog(EditAutomaticRecordLogEvent event) {
        AutomaticSourceLogEntity entity = automaticSourceLogEntityConverter.convert(event);
        entity.setCode(event.getAutomaticSourceCode());
        entity.setAutomaticSourceCode(event.getCode());
        entity.preUpdate(event);
        automaticSourceLogMapper.updateByPrimaryKeySelective(entity);
        if (CollectionUtil.isNotEmpty(event.getList())) {
            List<AutomaticSourceLogTestcaseEntity> list = event.getList().stream()
                    .map(e -> {
                        AutomaticSourceLogTestcaseEntity a = automaticSourceLogTestcaseEntityConverter.convert(e);
                        a.setAutomaticSourceLogCode(entity.getCode());
                        a.setTestcaseCode(a.getCode());
                        a.setCode(AggregateIdUtil.generateId(AggregateType.TEST_CASE));
                        a.preCreate(event);
                        return a;
                    }).collect(Collectors.toList());
            List<List<AutomaticSourceLogTestcaseEntity>> partition = Lists.partition(list, 20);
            for (List<AutomaticSourceLogTestcaseEntity> subList : partition) {
                automaticSourceLogTestcaseMapper.insertList(subList);
            }
        }
        AutomaticSourceRecordEntity entityAutomatic = new AutomaticSourceRecordEntity();
        entityAutomatic.setCode(event.getCode());
        entityAutomatic.setFailInformation(event.getFailInformation());
        entityAutomatic.preUpdate(event);
        automaticSourceRecordMapper.updateByPrimaryKeySelective(entityAutomatic);
    }

    @Override
    public void updateByPrimaryKeySelective(AutomaticSourceRecordEntityDO recordEntityDO) {
        AutomaticSourceRecordEntity recordEntity = automaticSourceRecordEntityConverter.convert(recordEntityDO);
        automaticSourceRecordMapper.updateByPrimaryKeySelective(recordEntity);
    }

    @Override
    public List<String> selectByTestcaseCodeList(List<String> testcaseCodeList) {
        return automaticSourceRecordMapper.selectByTestcaseCodeList(testcaseCodeList);
    }

    @Override
    public void updateByCodeList(AutomaticSourceRecordEntityDO entityDO, List<String> codeList) {
        Example updateExample = new Example(AutomaticSourceRecordEntity.class);
        updateExample.createCriteria().andIn("code", codeList);
        automaticSourceRecordMapper.updateByExampleSelective(automaticSourceRecordEntityConverter.convert(entityDO), updateExample);
    }

    @Override
    public void cancelAnalysisAutomatic(CancelAnalysisAutomaticEvent event) {
        AutomaticSourceLogEntity entity = new AutomaticSourceLogEntity();
        entity.setCode(event.getAutomaticSourceLogCode());
        entity.setStatus(AutomaticStatusEnum.ANALYSISSUCCESSABANDONED.name());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        automaticSourceLogMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public AutomaticRecordLogVO findAutomaticRecordLogVO(String code) {
        AutomaticSourceLogEntity entity = automaticSourceLogMapper.selectByPrimaryKey(code);
        return automaticSourceLogEntityConverter.convertVO(entity);
    }

    @Override
    public void editAutomaticLogStatus(String automaticSourceLogCode) {
        AutomaticSourceLogEntity entity = new AutomaticSourceLogEntity();
        entity.setCode(automaticSourceLogCode);
        entity.setStatus(AutomaticStatusEnum.CONFIRMING.name());
        automaticSourceLogMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void submitAnalysisAutomatic(SubmitAnalysisAutomaticEvent event) {
        AutomaticSourceLogEntity entity = new AutomaticSourceLogEntity();
        entity.setCode(event.getAutomaticSourceLogCode());
        entity.setStatus(AutomaticStatusEnum.NALYSISSUCCESSCONFIRMED.name());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setAnalyticMethod(event.getAnalyticMethod());
        automaticSourceLogMapper.updateByPrimaryKeySelective(entity);
        Example example = new Example(AutomaticSourceLogEntity.class);
        example.createCriteria().andEqualTo("automaticSourceCode", event.getCode())
                .andEqualTo("status", AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name());
        AutomaticSourceLogEntity updateEntity = new AutomaticSourceLogEntity();
        updateEntity.setStatus(AutomaticStatusEnum.ANALYSISSUCCESSABANDONED.name());
        updateEntity.setAnalyticMethod(event.getAnalyticMethod());
        automaticSourceLogMapper.updateByExampleSelective(updateEntity, example);
        AutomaticSourceRecordEntity entityAutomatic = new AutomaticSourceRecordEntity();
        entityAutomatic.setCode(event.getCode());
        entityAutomatic.setStatus(AutomaticStatusEnum.SUCCESS);
        entityAutomatic.setLastAutomaticSourceLogCode(event.getAutomaticSourceLogCode());
        entityAutomatic.setFileName(event.getFileName());
        entityAutomatic.setCommitId(event.getCommitId());
        automaticSourceRecordMapper.updateByPrimaryKeySelective(entityAutomatic);
    }

    @Override
    public List<AutomaticSourceLogTestcaseVO> listAutomaticSourceLogTestcaseVO(String automaticSourceLogCode) {
        List<String> inList = new ArrayList<>();
        inList.add(TestcaseFlagEnum.insert.name());
        inList.add(TestcaseFlagEnum.update.name());
        Example insertExample = new Example(AutomaticSourceLogTestcaseEntity.class);
        insertExample.createCriteria().andEqualTo("automaticSourceLogCode", automaticSourceLogCode)
                .andIn("flag", inList);
        List<AutomaticSourceLogTestcaseEntity> insertEntityList =
                automaticSourceLogTestcaseMapper.selectByExample(insertExample);
        return automaticSourceLogTestcaseEntityConverter.convertVOList(insertEntityList);
    }

    @Override
    public List<AutomaticSourceRecordEntityDO> queryAutomaticSourceByTestcaseCode(List<String> testcaseCodeList) {
        Example example = new Example(AutomaticSourceRecordEntity.class);
        example.createCriteria().andEqualTo("enable", true)
                .andIn("testcaseCode", testcaseCodeList);
        example.selectProperties("code", "name", "type");
        return automaticSourceRecordEntityConverter.convert(automaticSourceRecordMapper.selectByExample(example));
    }

    @Override
    public List<AutomaticSourceRecordEntityDO> selectByCodeList(List<String> automaticSourceCodeList, Boolean enable) {
        Example example = new Example(AutomaticSourceRecordEntity.class);
        example.createCriteria().andEqualTo("enable", enable).andIn("code", automaticSourceCodeList);
        example.selectProperties("code");
        List<AutomaticSourceRecordEntity> list = automaticSourceRecordMapper.selectByExample(example);
        return CollectionUtil.isEmpty(list) ? new ArrayList<>() : automaticSourceRecordEntityConverter.convert(list);
    }

    @Override
    public void automaticSuccess(AutomaticSuccessEvent event) {
        AutomaticSourceRecordEntity entity = automaticSourceRecordEntityConverter.convert(event);
        entity.setStatus(AutomaticStatusEnum.SUCCESS);
        automaticSourceRecordMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void deleteAutomaticRecord(DeleteAutomaticRecordEvent event) {
        AutomaticSourceRecordEntity entityAutomatic = new AutomaticSourceRecordEntity();
        entityAutomatic.setEnable(false);
        entityAutomatic.setCode(event.getCode());
        entityAutomatic.setModifier(event.getTransactor().getUserName());
        entityAutomatic.setModifierId(event.getTransactor().getUserId());
        entityAutomatic.setGmtModified(event.getOccurred());
        automaticSourceRecordMapper.updateByPrimaryKeySelective(entityAutomatic);
    }

    @Override
    public void deleteTempFileName(DeleteTempFileNameEvent event) {
        AutomaticSourceLogEntity entity = new AutomaticSourceLogEntity();
        entity.setCode(event.getLogCode());
        entity.setEnable(false);
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        automaticSourceLogMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void editAutomaticPersonLiable(EditAutomaticPersonLiableEvent event) {
        AutomaticSourceRecordEntity entityAutomatic = new AutomaticSourceRecordEntity();
        entityAutomatic.setCode(event.getCode());
        entityAutomatic.setModifier(event.getTransactor().getUserName());
        entityAutomatic.setModifierId(event.getTransactor().getUserId());
        entityAutomatic.setGmtModified(event.getOccurred());
        entityAutomatic.setPersonLiableId(event.getPersonLiableId());
        entityAutomatic.setPersonLiable(event.getPersonLiable());
        automaticSourceRecordMapper.updateByPrimaryKeySelective(entityAutomatic);
    }

    @Override
    public List<AutomaticSourceLogTestcaseVO> selectListAndPlanNameLog(String logCode, String flag) {
        return automaticSourceLogTestcaseMapper.selectListAndPlanNameLog(logCode, flag);
    }

    @Override
    public void update(AutomaticSourceLogEntityDO entityDO) {
        AutomaticSourceLogEntity entity = automaticSourceLogEntityConverter.convert(entityDO);
        automaticSourceLogMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public AutomaticSourceRecordEntityDO selectByPrimaryKey(String code) {
        return automaticSourceLogEntityConverter.convert(automaticSourceRecordMapper.selectByPrimaryKey(code));
    }

    @Override
    public void checkRecordName(MoveModuleReq req) {

        if (req.getOldParentCode().equals(req.getParentCode())) {
            throw new ServiceException("目标分组相同，无需移动");
        }
        Example example = new Example(AutomaticSourceRecordEntity.class);
        example.createCriteria().andEqualTo("name", req.getName())
                .andEqualTo("testcaseCode", TestcaseGroupTypeEnum.ALL.name().equals(req.getParentCode()) ? "" : req.getParentCode())
                .andEqualTo("enable", true);
        List<AutomaticSourceRecordEntity> list = automaticSourceRecordMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(list)) {
            throw new ServiceException("同层级下，登记库不能重名");
        }

    }

    @Override
    public List<AutomaticRecordVO> analysisAutomaticRecordAbortQuery(AnalysisAutomaticRecordAbortQuery query) {
        List<AutomaticSourceRecordEntity> list = automaticSourceRecordMapper.selectAnalysisAutomaticRecordAbort(query.getRecordAbortSecond(), query.getTypeList());
        return automaticSourceLogEntityConverter.converterAutomaticSourceRecordList(list);
    }

    @Override
    public List<AutomaticRecordLiteInfoVO> pageLiteInfoByProductCode(String productCode, String nameOrCode) {
        Example example = new Example(AutomaticSourceRecordEntity.class);
        example.selectProperties("code", "name");
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("productCode", productCode);
        if (StringUtils.isNotBlank(nameOrCode)) {
            Example.Criteria orLikeCriteria = example.createCriteria();
            orLikeCriteria.orLike("code", "%" + nameOrCode + "%")
                    .orLike("name", "%" + nameOrCode + "%");
            example.and(orLikeCriteria);
        }
        example.orderBy("gmtModified").desc();
        List<AutomaticSourceRecordEntity> list = automaticSourceRecordMapper.selectByExample(example);
        return automaticSourceLogEntityConverter.converterLiteInfoPage(list);
    }

    @Override
    public List<String> queryRelatedPlanName(String automaticSourceCode) {
        return automaticSourceRecordMapper.selectRelatedPlanName(automaticSourceCode);
    }

    @Override
    public List<String> queryRelatedTaskName(String automaticSourceCode) {
        return automaticSourceRecordMapper.selectRelatedTaskName(automaticSourceCode);
    }

    @Override
    public List<AutomaticSourceRecordEntityDO> selectAutoAnalysisByAddress(String gitHttpUrl) {
        Example example = new Example(AutomaticSourceRecordEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("address", gitHttpUrl)
                .andEqualTo("autoAnalysisFlag", Boolean.TRUE)
                .andIn("type",
                        Arrays.asList(AutomaticRecordTypeEnum.JMETER_GIT,
                                AutomaticRecordTypeEnum.TESTNG,
                                AutomaticRecordTypeEnum.PYTEST));
        List<AutomaticSourceRecordEntity> entityList = automaticSourceRecordMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : automaticSourceRecordEntityConverter.convert(entityList);
    }

    @Override
    public AutomaticSourceLogEntityDO findAutomaticSourceByLogCode(String logCode) {
        return automaticSourceLogEntityConverter.convert(automaticSourceLogMapper.selectByPrimaryKey(logCode));
    }

    @Override
    public List<AutomaticRecordVO> queryAutomaticRecordByType(String type) {
        Example example = new Example(AutomaticSourceRecordEntity.class);
        example.createCriteria()
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("type", type);
        List<AutomaticSourceRecordEntity> entityList = automaticSourceRecordMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : automaticSourceRecordEntityConverter.convertVOList(entityList);
    }
}
