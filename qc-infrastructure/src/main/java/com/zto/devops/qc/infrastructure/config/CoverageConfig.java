package com.zto.devops.qc.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @create 2022/10/8 9:33
 */
@ConfigurationProperties("qc.coverage.config")
@Component
@Data
public class CoverageConfig {

    private String zkePodIpUrl;

    private String zkeApikey;

    /**
     * 代码覆盖率不达标原因
     */
    private String codeCoverageReason;

    private String products;

    private String modules;

    private String excludeApps;

    /**
     * 过滤不需要比较的产品
     */
    private String filterUnComparedProducts;

    /**
     * 测试通过，产品群通知覆盖率结果，查看详情跳转链接
     */
    private String messageSkipLink;

    /**
     * 覆盖率报告灰度产品
     */
    private String newReportUrlProducts;

    /**
     * 覆盖率报告地址
     */
    private String reportUrl;

    /**
     * 文档跳转地址
     */
    private String docAddress;
}
