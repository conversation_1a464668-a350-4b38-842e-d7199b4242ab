package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

@MappedJdbcTypes(value = JdbcType.VARCHAR, includeNullJdbcType = true)
public class TmTestPlanCaseStatusHandler extends BaseEnumTypeHandler<TestPlanCaseStatusEnum> {
    public TmTestPlanCaseStatusHandler() {
        super(TestPlanCaseStatusEnum.class);
    }
}

