package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.testmanager.scheduler.entity.AutomaticSchedulerVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.ProductSchedulerVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.PageSchedulerQuery;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSchedulerEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface AutomaticSchedulerMapper extends Mapper<AutomaticSchedulerEntity> {

    /**
     * 查询定时任务列表
     *
     * @param query {@link PageSchedulerQuery}
     * @return {@link AutomaticSchedulerVO}
     */
    List<AutomaticSchedulerVO> selectList(PageSchedulerQuery query);

    /**
     * 根据定时任务code查询
     *
     * @param schedulerCode
     * @return
     */
    AutomaticSchedulerEntity selectBySchedulerCode(@Param("schedulerCode") String schedulerCode);

    /**
     * 根据产品code查询定时任务列表
     *
     * @param productCode 产品code
     * @return {@link ProductSchedulerVO}
     */
    List<ProductSchedulerVO> selectByProductCode(@Param("productCode") String productCode,
                                                 @Param("schedulerName") String schedulerName);
}
