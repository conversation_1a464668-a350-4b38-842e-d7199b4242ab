package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseStepQuery;
import com.zto.devops.qc.domain.gateway.repository.ITestcaseStepRepository;
import com.zto.devops.qc.infrastructure.converter.TestcaseStepEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseStepEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TestcaseStepMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Component
@Slf4j
public class ITestcaseStepRepositoryImpl implements ITestcaseStepRepository {

    @Autowired
    private TestcaseStepMapper testcaseStepMapper;
    @Autowired
    private TestcaseStepEntityConverter testcaseStepEntityConverter;

    @Override
    public List<TestcaseStepVO> ListTestcaseStepQuery(ListTestcaseStepQuery query) {
        log.info("ListTestcaseStepQuery >>> {}", query.getTestcaseCode());
        Example example = new Example(TestcaseStepEntity.class);
        example.createCriteria().andEqualTo("testcaseCode", query.getTestcaseCode());
        example.orderBy("sort");
        List<TestcaseStepEntity> stepList = testcaseStepMapper.selectByExample(example);
        return testcaseStepEntityConverter.converter(stepList);
    }
}
