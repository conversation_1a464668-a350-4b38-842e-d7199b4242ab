package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.SceneCollectionEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.SceneCollectionEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TmSceneCollectionEntityConverter {

    SceneCollectionEntity convertSceneCollectionEntity(SceneCollectionEntityDO entityDO);

    SceneCollectionEntityDO convertSceneCollectionEntityDO(SceneCollectionEntity entityDO);

    List<SceneCollectionEntity> convertSceneCollectionEntityList(List<SceneCollectionEntityDO> entityList);

    List<SceneCollectionEntityDO> convertSceneCollectionEntityDOList(List<SceneCollectionEntity> entityList);
}
