package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import com.zto.devops.qc.client.model.issue.event.CommentAddedEvent;
import com.zto.devops.qc.client.model.issue.event.CommentRemovedSimpleEvent;
import com.zto.devops.qc.domain.gateway.repository.CommentRepository;
import com.zto.devops.qc.infrastructure.converter.CommentEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.CommentEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.CommentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class CommentRepositoryImpl implements CommentRepository {

    @Autowired
    private CommentMapper commentMapper;

    @Autowired
    private CommentEntityConverter commentEntityConverter;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addComment(CommentAddedEvent event) {
        Set<CommentVO> commentVOS = event.getCommentVOS();
        List<CommentEntity> entityList = commentEntityConverter.convert(commentVOS);
        for (CommentEntity entity : entityList) {
            entity.preCreate(event);
            commentMapper.insertSelective(entity);
        }
    }

    @Override
    public void removeComment(CommentRemovedSimpleEvent event) {
        CommentEntity entity = new CommentEntity();
        entity.setCode(event.getCode());
        entity.setEnable(Boolean.FALSE);
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setGmtModified(event.getOccurred());
        commentMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public List<CommentVO> queryCommentByBusinessCode(String businessCode) {
        Example example = new Example(CommentEntity.class);
        example.createCriteria().andEqualTo("businessCode", businessCode).andEqualTo("enable",Boolean.TRUE);
        example.orderBy("gmtCreate").asc();
        List<CommentEntity> entityList = commentMapper.selectByExample(example);
        List<CommentVO> commentVOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(entityList)) {
            Map<String, List<CommentEntity>> commentMap = entityList.stream()
                    .peek(o -> o.setTopRepliedCode(StringUtil.isEmpty(o.getTopRepliedCode()) ? "" : o.getTopRepliedCode()))
                    .collect(Collectors.groupingBy(CommentEntity::getTopRepliedCode));
            List<CommentEntity> commentEntityList = commentMap.get("");
            if(CollectionUtil.isNotEmpty(commentEntityList)){
                for (CommentEntity entity : commentEntityList) {
                    CommentVO commentVO = commentEntityConverter.convert(entity);
                    List<CommentEntity> commentEntities = commentMap.get(entity.getCode());
                    if(CollectionUtil.isNotEmpty(commentEntities)){
                        List<CommentVO> childCommentList = commentEntityConverter.converter(commentEntities);
                        commentVO.setList(childCommentList.stream().sorted(Comparator.comparingLong(o -> o.getGmtCreate().getTime())).collect(Collectors.toList()));
                    }
                    commentVOList.add(commentVO);
                }
            }
        }
        return commentVOList;
    }

    @Override
    public CommentVO queryCommentByCode(String code) {
        Example example = new Example(CommentEntity.class);
        example.createCriteria().andEqualTo("code", code).andEqualTo("enable",Boolean.TRUE);
        CommentEntity entity = commentMapper.selectOneByExample(example);
        if(null != entity) {
            return commentEntityConverter.convert(entity);
        }
        return null;
    }

}
