package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.quality.VersionQualityMetricTypeEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.VersionQualityMetricTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 版本维度，服务质量指标
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "qc_version_quality_metrics")
public class QcVersionQualityMetricsEntity extends BaseEntity implements Serializable {

    @Column(name = "id")
    @Id
    private Long id;

    private String versionCode;

    private String versionName;

    private String productCode;

    private String productName;

    @Column(name = "metric_type")
    @ColumnType(typeHandler = VersionQualityMetricTypeHandler.class)
    private VersionQualityMetricTypeEnum metricType;

    private String metricValue;
}
