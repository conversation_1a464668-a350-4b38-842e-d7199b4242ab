package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.qc.client.model.dto.TestFunctionPointEntityDO;
import com.zto.devops.qc.domain.gateway.repository.TestFunctionPointRepository;
import com.zto.devops.qc.infrastructure.converter.TestFunctionPointEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.TestFunctionPointEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TestFunctionPointMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class TestFunctionPointRepositoryImpl implements TestFunctionPointRepository {

    @Autowired
    private TestFunctionPointMapper testFunctionPointMapper;
    @Autowired
    private TestFunctionPointEntityConverter testFunctionPointEntityConverter;

    @Override
    public List<TestFunctionPointEntityDO> selectByBusinessCode(String code) {
        Example example = new Example(TestFunctionPointEntity.class);
        example.createCriteria().andEqualTo("businessCode", code).andEqualTo("enable",
                Boolean.TRUE);
        List<TestFunctionPointEntity> entityList = testFunctionPointMapper.selectByExample(example);
        return CollectionUtils.isEmpty(entityList) ? new ArrayList<>() : testFunctionPointEntityConverter.convert2DOList(entityList);
    }
}