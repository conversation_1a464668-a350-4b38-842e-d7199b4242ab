package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAbandonReasonEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

@MappedJdbcTypes(value = JdbcType.VARCHAR, includeNullJdbcType = true)
public class TestcaseAbandonReasonTypeHandler extends BaseEnumTypeHandler<TestcaseAbandonReasonEnum> {

    public TestcaseAbandonReasonTypeHandler(Class<TestcaseAbandonReasonEnum> type) {
        super(type);
    }
}
