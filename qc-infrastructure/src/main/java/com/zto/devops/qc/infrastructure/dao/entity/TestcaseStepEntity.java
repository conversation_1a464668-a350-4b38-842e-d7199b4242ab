package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
@Table(name="tm_testcase_step")
@Getter
@Setter
public class TestcaseStepEntity extends BaseEntity {

    /**
     * 自增主键
     */
    @Id
    private Long id;

    /**
     * 所属用例code
     */
    private String testcaseCode;

    /**
     * 步骤描述
     */
    private String stepDesc;

    /**
     * 预期结果
     */
    private String expectResult;

    /**
     * 排序
     */
    private Integer sort;
}