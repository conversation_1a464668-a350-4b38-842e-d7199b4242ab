package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.model.common.TransitionNodeAddEvent;
import com.zto.devops.qc.client.model.dto.TransitionNodeEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TransitionNodeVO;
import com.zto.devops.qc.client.model.issue.event.TransitionNodeAddedEvent;
import com.zto.devops.qc.domain.gateway.repository.TransitionNodeRepository;
import com.zto.devops.qc.infrastructure.converter.TransitionNodeVOConverter;
import com.zto.devops.qc.infrastructure.dao.entity.TransitionNodeEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TransitionNodeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class TransitionNodeRepositoryImpl implements TransitionNodeRepository {

    @Autowired
    private TransitionNodeMapper transitionNodeMapper;

    @Autowired
    private TransitionNodeVOConverter transitionNodeVOConverter;

    @Override
    public List<TransitionNodeVO> getTransitionNodeList(String code) {
        Example example1 = new Example(TransitionNodeEntity.class);
        example1.orderBy("gmtCreate").desc();
        example1.createCriteria().andEqualTo("businessCode", code).andEqualTo("enable", Boolean.TRUE);
        List<TransitionNodeEntity> transitionNodeEntityList = transitionNodeMapper.selectByExample(example1);
        if (CollectionUtil.isNotEmpty(transitionNodeEntityList)) {
            return transitionNodeVOConverter.converterList(transitionNodeEntityList);
        }
        return CollectionUtil.newEmptyList();
    }

    @Override
    public void handleTransitionNodeAddEvent(TransitionNodeAddEvent event) {
        TransitionNodeEntity entity = new TransitionNodeEntity();
        entity.setCode(event.getTransitionNodeCode());
        entity.setContent(event.getContent());
        entity.setReason(event.getReason());
        entity.setDomain(event.domain());
        entity.setBusinessCode(event.getBusinessCode());
        entity.setCurStatus(event.getCurStatus());
        entity.setNextStatus(event.getNextStatus());
        entity.preCreate(event.getOperator());
        transitionNodeMapper.insertSelective(entity);
    }

    @Override
    public List<TransitionNodeVO> findIssueTransitionNodeByIssueCode(String businessCode) {
        Example example = new Example(TransitionNodeEntity.class);
        example.orderBy("gmtCreate").asc();
        example.createCriteria()
                .andEqualTo("businessCode", businessCode)
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("domain", DomainEnum.ISSUE);
        List<TransitionNodeEntity> transitionNodeEntityList = transitionNodeMapper.selectByExample(example);
        return transitionNodeVOConverter.convertTransitionNodeVO(transitionNodeEntityList);
    }

    @Override
    public List<TransitionNodeEntityDO> listIssueTransitionNode(List<String> issueCodes) {
        Example example = new Example(TransitionNodeEntity.class);
        example.orderBy("gmtCreate").desc();
        example.createCriteria().andIn("businessCode", issueCodes).andEqualTo("domain", DomainEnum.ISSUE.name()).andEqualTo("enable", true);
        List<TransitionNodeEntity> entityList = transitionNodeMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : transitionNodeVOConverter.converter2DOList(entityList);
    }

    @Override
    public void handleTransitionNodeAddedEvent(TransitionNodeAddedEvent event) {
        TransitionNodeEntity entity = new TransitionNodeEntity();
        entity.setCode(event.getCode());
        entity.setContent(event.getContent());
        entity.setReason(event.getReason());
        entity.setDomain(event.getDomain());
        entity.setBusinessCode(event.getBusinessCode());
        entity.setCurStatus(event.getCurStatus());
        entity.setNextStatus(event.getNextStatus());
        entity.preCreate(event);
        transitionNodeMapper.insertSelective(entity);
    }


    @Override
    public List<TransitionNodeEntityDO> selectContentByIssueList(List<String> businessCodes) {
        List<TransitionNodeEntity> entityList = transitionNodeMapper.selectContentByIssueList(businessCodes);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : transitionNodeVOConverter.converter2DOList(entityList);
    }

    @Override
    public List<TransitionNodeEntityDO> queryRejectReasonByIssueCodeList(List<String> issueCodeList) {
        Example example = new Example(TransitionNodeEntity.class);
        example.selectProperties("businessCode", "reason", "gmtCreate");
        example.orderBy("businessCode");
        example.createCriteria().andIn("businessCode", issueCodeList)
                .andEqualTo("enable", true)
                .andEqualTo("nextStatus", IssueStatus.REJECTED.name())
                .andIsNotNull("reason");
        List<TransitionNodeEntity> entityList = transitionNodeMapper.selectByExample(example);
        return CollectionUtil.isEmpty(entityList) ? new ArrayList<>() : transitionNodeVOConverter.converter2DOList(entityList);
    }
}
