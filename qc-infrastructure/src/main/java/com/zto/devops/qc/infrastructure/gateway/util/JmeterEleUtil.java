package com.zto.devops.qc.infrastructure.gateway.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneRunningSetEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.LinkMapTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.constants.AutomaticConstant;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component.*;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Element;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

public class JmeterEleUtil {
    public static final Pattern URL_PATTERN = Pattern.compile("\\$\\{(.*?)}");

    public static Element newEle(Element element,String nodeName, Map<String,String> attrib,String text){

        Element resultEle = element.addElement(nodeName);
        if(attrib!=null) {
            attrib.forEach((String key, String value) -> {
                resultEle.addAttribute(key, value);
            });
        }
        if(text!=null){
            resultEle.setText(text);
        }
        return resultEle;
    }
    public static Element newEle(Element element,String nodeName, Map<String,String> attrib){

        return newEle(element,nodeName,attrib,null);

    }

    public static Element newEle(Element element,String nodeName){

        return newEle(element,nodeName,null,null);

    }

    /**
     * *
     * @param element
     * @param element 外层Ele,guiclass="ArgumentsPanel"这层
     * @param attrib 自定义的变量key:value
     */
    public static void addUserDefinedVariablesContent(Element element, Map<String,String> attrib,boolean onlyContent){
        Element parentEle = null;
        if(!onlyContent){

            parentEle = JmeterEleUtil.newEle(element,"Arguments",new HashMap<String, String>(){
                {
                    put("guiclass", "ArgumentsPanel");
                    put("testclass", "Arguments");
                    put("testname", "用户变量");
                    put("enabled", "true");
                } });

        }else {
            parentEle = element;
        }
        Element collectionProp = JmeterEleUtil.newEle(parentEle,"collectionProp",new HashMap<String, String>(){
            {
                put("name", "Arguments.arguments");
            } });
        if(attrib==null){
            return;
        }
        attrib.forEach((String key,String value) -> {
            Element ele = JmeterEleUtil.newEle(collectionProp,"elementProp",new HashMap<String, String>(){
                {
                    put("name", key);
                    put("elementType","Argument");
                } });
            JmeterEleUtil.newEle(ele,"stringProp",new HashMap<String, String>(){
                {
                    put("name", "Argument.name");
                } },key);
            String vStr = value;
            if(StringUtils.isNotEmpty(vStr)){
                vStr = "<![CDATA["+vStr+"]]>";
            }
            JmeterEleUtil.newEle(ele,"stringProp",new HashMap<String, String>(){
                {
                    put("name", "Argument.value");
                } },vStr);
            JmeterEleUtil.newEle(ele,"stringProp",new HashMap<String, String>(){
                {
                    put("name", "Argument.metadata");
                } },"=");
        });
        if(!onlyContent){
            JmeterEleUtil.newEle(element,"hashTree");
        }
    }


    public static Element newThreadGroup(Element planContentHashTree,String name,int loops,
                                         boolean enable,String runMethod, String runTime){

        Element threadGroupEle = JmeterEleUtil.newEle(planContentHashTree,"ThreadGroup",new HashMap<String, String>(){
            {
                String enableStr = "true";
                if(!enable){
                    enableStr = "false";
                }
                put("guiclass", "ThreadGroupGui");
                put("testclass", "ThreadGroup");
                put("testname", name);
                put("enabled", enableStr);
            } });
        JmeterEleUtil.newEle(threadGroupEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "ThreadGroup.on_sample_error");
            } },"continue");
        Element propEle = JmeterEleUtil.newEle(threadGroupEle,"elementProp",new HashMap<String, String>(){
            {
                put("name", "ThreadGroup.main_controller");
                put("elementType", "LoopController");
                put("guiclass", "LoopControlPanel");
                put("testclass", "LoopController");
                put("testname", "Loop Controller");
                put("enabled", "true");
            } });
        JmeterEleUtil.newEle(propEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "LoopController.continue_forever");
            } },"false");
        JmeterEleUtil.newEle(propEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "LoopController.loops");
            } },Integer.toString(loops));
        JmeterEleUtil.newEle(threadGroupEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "ThreadGroup.num_threads");
            } },"1");
        JmeterEleUtil.newEle(threadGroupEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "ThreadGroup.ramp_tim");
            } },"1");
        String scheduler = "false";
        String duration = "";
        String delay = "";
        if(StringUtils.isNotEmpty(runMethod)) {
            duration = SceneRunningSetEnum.SERIAL.getValue().equals(runMethod) ? "7200" : duration;
            scheduler = SceneRunningSetEnum.SERIAL.getValue().equals(runMethod) ? "true" : scheduler;
            if(StringUtils.isNotEmpty(runTime)) {
                delay = SceneRunningSetEnum.SERIAL.getValue().equals(runMethod) ? runTime : delay;
            }
        }
        JmeterEleUtil.newEle(threadGroupEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "ThreadGroup.scheduler");
            } },scheduler);
        JmeterEleUtil.newEle(threadGroupEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "ThreadGroup.duration");
            } }, duration);
        JmeterEleUtil.newEle(threadGroupEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "ThreadGroup.delay");
            } }, delay);
        JmeterEleUtil.newEle(threadGroupEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "ThreadGroup.same_user_on_next_iteration");
            } },"true");
        return JmeterEleUtil.newEle(planContentHashTree,"hashTree");
    }

    /**
     * *
     * @param parentEle
     * @param dataSourceName jdbcRequest要引用的名称
     * @param driver com.mysql.jdbc.Driver; oracle.jdbc.OracleDriver
     * @param dbUrl 例如:***************************************; **************************************
     * @param userName
     * @param password
     */
    public static void newJDBCConfig(Element parentEle,String dataSourceName,String driver,String dbUrl,String userName,String password){
        Element jdbcConfigEle = JmeterEleUtil.newEle(parentEle,"JDBCDataSource",new HashMap<String, String>(){
            {
                put("guiclass", "TestBeanGUI");
                put("testclass", "JDBCDataSource");
                put("testname", dataSourceName);
                put("enabled", "true");
            } });

        JmeterEleUtil.newEle(jdbcConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "dataSource");
            } },dataSourceName);
        JmeterEleUtil.newEle(jdbcConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "poolMax");
            } },"0");
        JmeterEleUtil.newEle(jdbcConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "timeout");
            } },"10000");
        JmeterEleUtil.newEle(jdbcConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "trimInterval");
            } },"60000");
        JmeterEleUtil.newEle(jdbcConfigEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "autocommit");
            } },"true");
        JmeterEleUtil.newEle(jdbcConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "transactionIsolation");
            } },"DEFAULT");
        JmeterEleUtil.newEle(jdbcConfigEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "preinit");
            } },"true");
        JmeterEleUtil.newEle(jdbcConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "initQuery");
            } });
        JmeterEleUtil.newEle(jdbcConfigEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "keepAlive");
            } },"true");
        JmeterEleUtil.newEle(jdbcConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "connectionAge");
            } },"5000");
        JmeterEleUtil.newEle(jdbcConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "checkQuery");
            } });
        String urlStr = dbUrl;
        if(StringUtils.isNotEmpty(urlStr)){
            urlStr = "<![CDATA["+urlStr+"]]>";
        }
        JmeterEleUtil.newEle(jdbcConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "dbUrl");
            } },urlStr);
        JmeterEleUtil.newEle(jdbcConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "driver");
            } },driver);
        JmeterEleUtil.newEle(jdbcConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "username");
            } },userName);
        JmeterEleUtil.newEle(jdbcConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "password");
            } },"<![CDATA["+password+"]]>");
        JmeterEleUtil.newEle(jdbcConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "connectionProperties");
            } });

        JmeterEleUtil.newEle(parentEle,"hashTree");

    }

    /**
     * *
     * @param parentEle
     * @param typeName JDBCSampler;JDBCPreProcessor;JDBCPostProcessor
     * @param enable
     */
    public static void newJDBCRequest(Element parentEle, String typeName, String dataSource, String sqlStr,boolean isUpdateSql, boolean enable){

        String queryType = "Select Statement";
        if(isUpdateSql){
           queryType = "Update Statement";
        }

        Element jdbcRequestEle = JmeterEleUtil.newEle(parentEle,typeName,new HashMap<String, String>(){
            {
                String enableStr = "true";
                if(!enable){
                    enableStr = "false";
                }
                put("guiclass", "TestBeanGUI");
                put("testclass", typeName);
                put("testname", typeName);
                put("enabled", enableStr);
            } });


        JmeterEleUtil.newEle(jdbcRequestEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "dataSource");
            } },dataSource);
        JmeterEleUtil.newEle(jdbcRequestEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "queryType");
            } },queryType);
        if(StringUtils.isNotEmpty(sqlStr)){
            sqlStr = "<![CDATA["+sqlStr+"]]>";
        }
        JmeterEleUtil.newEle(jdbcRequestEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "query");
            } },sqlStr);
        JmeterEleUtil.newEle(jdbcRequestEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "queryArguments");
            } });
        JmeterEleUtil.newEle(jdbcRequestEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "queryArgumentsTypes");
            } });
        JmeterEleUtil.newEle(jdbcRequestEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "variableNames");
            } });
//        JmeterEleUtil.newEle(jdbcRequestEle,"stringProp",new HashMap<String, String>(){
//            {
//                put("name", "resultVariable");
//            } },jsonObject.getString("paramName"));
        JmeterEleUtil.newEle(jdbcRequestEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "queryTimeout");
            } });
        JmeterEleUtil.newEle(jdbcRequestEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "resultSetMaxRows");
            } });
        JmeterEleUtil.newEle(jdbcRequestEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "resultSetHandler");
            } },"Store as Object");
        JmeterEleUtil.newEle(jdbcRequestEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "resultVariable");
            } },"dbResult");

        JmeterEleUtil.newEle(parentEle,"hashTree");

    }


//    public static void newPreBeanShell(Element parentEle,String preScript){
//        Element beanshellEle = JmeterEleUtil.newEle(parentEle,"BeanShellPreProcessor",new HashMap<String, String>(){
//            {
//                put("guiclass", "TestBeanGUI");
//                put("testclass", "BeanShellPreProcessor");
//                put("testname", "BeanShell 前置处理程序");
//                put("enabled", "true");
//            } });
//        JmeterEleUtil.newEle(beanshellEle,"stringProp",new HashMap<String, String>(){
//            {
//                put("name", "filename");
//            } });
//        JmeterEleUtil.newEle(beanshellEle,"stringProp",new HashMap<String, String>(){
//            {
//                put("name", "parameters");
//            } });
//        JmeterEleUtil.newEle(beanshellEle,"boolProp",new HashMap<String, String>(){
//            {
//                put("name", "resetInterpreter");
//            } },"false");
//        JmeterEleUtil.newEle(beanshellEle,"stringProp",new HashMap<String, String>(){
//            {
//                put("name", "script");
//            } },"<![CDATA["+preScript+"]]>");
//
//
//        JmeterEleUtil.newEle(parentEle,"hashTree");
//
//    }

//    public static void newPostBeanShell(Element parentEle,String aftScript){
//
//        Element beanshellEle = JmeterEleUtil.newEle(parentEle,"BeanShellPostProcessor",new HashMap<String, String>(){
//            {
//                put("guiclass", "TestBeanGUI");
//                put("testclass", "BeanShellPostProcessor");
//                put("testname", "BeanShell 后置处理程序");
//                put("enabled", "true");
//            } });
//
//        JmeterEleUtil.newEle(beanshellEle,"stringProp",new HashMap<String, String>(){
//            {
//                put("name", "filename");
//            } });
//        JmeterEleUtil.newEle(beanshellEle,"stringProp",new HashMap<String, String>(){
//            {
//                put("name", "parameters");
//            } });
//        JmeterEleUtil.newEle(beanshellEle,"boolProp",new HashMap<String, String>(){
//            {
//                put("name", "resetInterpreter");
//            } },"false");
//        JmeterEleUtil.newEle(beanshellEle,"stringProp",new HashMap<String, String>(){
//            {
//                put("name", "script");
//            } },"<![CDATA["+aftScript+"]]>");
//
//
//        JmeterEleUtil.newEle(parentEle,"hashTree");
//
//    }


    /**
     * *
     * @param typeName  JSR223Sampler,JSR223PreProcessor,JSR223PostProcessor,JSR223Assertion,JSR223Listener
     * @param parentEle
     * @param name
     * @param scriptContent
     * @param scriptFileName
     * @param comments
     * @param scriptLanguage 为空则默认为groovy
     * @param parameters
     * @param enable
     * @return
     */
    public static Element newJSR223Sampler(String typeName,Element parentEle,String name,String scriptContent,String scriptFileName,String comments,String scriptLanguage,String parameters,boolean enable){
        if(StringUtil.isEmpty(name)) {
            name = typeName;
        }
//        String scriptLanguage = "groovy";
        if(StringUtil.isEmpty(scriptLanguage)) {
            scriptLanguage = "groovy";
        }

//        boolean finalEnable = enable;
        String finalName = name;
        Element shellEle = JmeterEleUtil.newEle(parentEle,typeName,new HashMap<String, String>(){
            {
                String enableStr = "true";
                if(!enable){
                    enableStr = "false";
                }
                put("guiclass", "TestBeanGUI");
                put("testclass", typeName);
                put("testname", finalName);
                put("enabled", enableStr);
            } });
        if(StringUtil.isNotEmpty(comments)) {
            JmeterEleUtil.newEle(shellEle, "stringProp", new HashMap<String, String>() {
                {
                    put("name", "TestPlan.comments");
                }
            }, comments);
        }

        JmeterEleUtil.newEle(shellEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "scriptLanguage");
            } }, scriptLanguage);
        JmeterEleUtil.newEle(shellEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "parameters");
            } }, parameters);

        JmeterEleUtil.newEle(shellEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "filename");
            } }, scriptFileName);

        JmeterEleUtil.newEle(shellEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "cacheKey");
            } }, "false");

        if(StringUtils.isNotEmpty(scriptContent)) {
            scriptContent = "<![CDATA[" + scriptContent + "]]>";
//            scriptContent = StringEscapeUtils.escapeXml(scriptContent);
        }

        JmeterEleUtil.newEle(shellEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "script");
            } }, scriptContent);


        return JmeterEleUtil.newEle(parentEle,"hashTree");
    }

    /**
     * 计数器组件*
     * @param parentEle
     * @param start
     * @param increment
     * @param varName 变量名
     * @param format
     * @param enable
     * @param perUser
     * @param resetOntgIteration
     */
    public static void newCounter(Element parentEle,String start,String increment,String varName,String format,boolean enable,boolean perUser,boolean resetOntgIteration){
        Element counterConfigEle = JmeterEleUtil.newEle(parentEle,"CounterConfig",new HashMap<String, String>(){
            {
                String enableStr = "true";
                if(!enable){
                    enableStr = "false";
                }
                put("guiclass", "CounterConfigGui");
                put("testclass", "CounterConfig");
                put("testname", "计数器");
                put("enabled", enableStr);
            } });

        JmeterEleUtil.newEle(counterConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "CounterConfig.start");
            } }, start);
        JmeterEleUtil.newEle(counterConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "CounterConfig.end");
            } });
        JmeterEleUtil.newEle(counterConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "CounterConfig.incr");
            } }, increment);
        JmeterEleUtil.newEle(counterConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "CounterConfig.name");
            } }, varName);
        JmeterEleUtil.newEle(counterConfigEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "CounterConfig.format");
            } }, format);
        String perUserStr = "false";
        if(perUser){
            perUserStr = "true";
            JmeterEleUtil.newEle(counterConfigEle,"boolProp",new HashMap<String, String>(){
                {
                    put("name", "CounterConfig.per_user");
                } }, perUserStr);
            String resetOntgIterationStr = "false";
            if(resetOntgIteration){
                resetOntgIterationStr = "true";
            }
            JmeterEleUtil.newEle(counterConfigEle,"boolProp",new HashMap<String, String>(){
                {
                    put("name", "CounterConfig.reset_on_tg_iteration");
                } }, resetOntgIterationStr);
        }else {
            JmeterEleUtil.newEle(counterConfigEle,"boolProp",new HashMap<String, String>(){
                {
                    put("name", "CounterConfig.per_user");
                } }, perUserStr);
        }
        JmeterEleUtil.newEle(parentEle,"hashTree");
    }

//    public static Element newLoopController(Element parentEle,String continueForever,String loops,String comments,boolean enable){
//
//        Element loopControllerEle = JmeterEleUtil.newEle(parentEle,"LoopController",new HashMap<String, String>(){
//            {
//                String enableStr = "true";
//                if(!enable){
//                    enableStr = "false";
//                }
//                put("guiclass", "LoopControlPanel");
//                put("testclass", "LoopController");
//                put("testname", "循环控制器");
//                put("enabled", enableStr);
//            } });
//
//        JmeterEleUtil.newEle(loopControllerEle,"stringProp",new HashMap<String, String>(){
//            {
//                put("name", "LoopController.continue_forever");
//            } }, continueForever);
//        JmeterEleUtil.newEle(loopControllerEle,"stringProp",new HashMap<String, String>(){
//            {
//                put("name", "LoopController.loops");
//            } }, loops);
//        JmeterEleUtil.newEle(loopControllerEle,"stringProp",new HashMap<String, String>(){
//            {
//                put("name", "TestPlan.comments");
//            } }, comments);
//        return JmeterEleUtil.newEle(parentEle,"hashTree");
//
//    }

    public static void newHeaderManager(Element parentEle,Map<String,String> headers,boolean enable){

        Element headerManagerEle = JmeterEleUtil.newEle(parentEle,"HeaderManager",new HashMap<String, String>(){
            {
                String enableStr = "true";
                if(!enable){
                    enableStr = "false";
                }
                put("guiclass", "HeaderPanel");
                put("testclass", "HeaderManager");
                put("testname", "HTTP信息头管理器");
                put("enabled", enableStr);
            } });
        Element headersEle = JmeterEleUtil.newEle(headerManagerEle,"collectionProp",new HashMap<String, String>(){
            {
                put("name", "HeaderManager.headers");
            } });
        if(CollectionUtil.isNotEmpty(headers)){
            for(String keyName : headers.keySet()){
                Element elementPropEle = JmeterEleUtil.newEle(headersEle,"elementProp",new HashMap<String, String>(){
                    {
                        put("name", "");
                        put("elementType", "Header");
                    } });
                JmeterEleUtil.newEle(elementPropEle,"stringProp",new HashMap<String, String>(){
                    {
                        put("name", "Header.name");
                    } }, keyName);
                JmeterEleUtil.newEle(elementPropEle,"stringProp",new HashMap<String, String>(){
                    {
                        put("name", "Header.value");
                    } }, "<![CDATA["+headers.get(keyName)+"]]>");
            }
        }
        JmeterEleUtil.newEle(parentEle,"hashTree");
    }

    /**
     * 增加用户参数组件*
     * @param parentEle
     * @param parameters  Map<String, List<String>> 其中每一个List<String>的size都要和其它List<String>size保持一致
     * @param perIteration 默认请填false
     * @param enable
     */
    public static void newUserParameters(Element parentEle, HashMap<String, List<String>> parameters, boolean perIteration, boolean enable){
        Element userParametersEle = JmeterEleUtil.newEle(parentEle,"UserParameters",new HashMap<String, String>(){
            {
                String enableStr = "true";
                if(!enable){
                    enableStr = "false";
                }
                put("guiclass", "UserParametersGui");
                put("testclass", "UserParameters");
                put("testname", "用户参数");
                put("enabled", enableStr);
            } });
        if(CollectionUtil.isNotEmpty(parameters)) {
            int size = -1;
            for(String key:parameters.keySet()){
                List<String> values = parameters.get(key);
                if(size<0){
                    size = values.size();
                }
                if(size!=values.size()){
                    throw new ServiceException("用户参数组件中的数据列表大小不一致："+JSON.toJSONString(parameters));
                }
            }
            Element collectionPropEle = JmeterEleUtil.newEle(userParametersEle,"collectionProp",new HashMap<String, String>(){
                {
                    put("name", "UserParameters.names");
                } });
            for (String keyName : parameters.keySet()) {
                JmeterEleUtil.newEle(collectionPropEle,"stringProp",new HashMap<String, String>(){
                    {
                        put("name", JmeterToolsUtil.getAscii(keyName));
                    } }, keyName);
            }
            Element collectionPropValueEle = JmeterEleUtil.newEle(userParametersEle,"collectionProp",new HashMap<String, String>(){
                {
                    put("name", "UserParameters.thread_values");
                } });

            String valuesUser = "user_";
            for(int i=0;i<size;i++){
                int finalI = i;
                Element valueEle = JmeterEleUtil.newEle(collectionPropValueEle,"collectionProp",new HashMap<String, String>(){
                    {
                        put("name", JmeterToolsUtil.getAscii(valuesUser+finalI));
                    } });
                for (String keyName : parameters.keySet()) {
                    String valueStr = parameters.get(keyName).get(i);
                    JmeterEleUtil.newEle(valueEle,"stringProp",new HashMap<String, String>(){
                        {
                            put("name", JmeterToolsUtil.getAscii(valueStr));
                        } }, valueStr);
                }
            }

        }

        String perIterationStr = "false";
        if(perIteration){
            perIterationStr = "true";
        }
        JmeterEleUtil.newEle(userParametersEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "UserParameters.per_iteration");
            } }, perIterationStr);

        JmeterEleUtil.newEle(parentEle,"hashTree");
    }


    public static Element newIfController(String name,Element parentEle, String expression, boolean enable){
        if(StringUtil.isEmpty(expression)){
            return null;
        }
        Element ifControllerEle = JmeterEleUtil.newEle(parentEle,"IfController",new HashMap<String, String>(){
            {
                String enableStr = "true";
                if(!enable){
                    enableStr = "false";
                }
                put("guiclass", "IfControllerPanel");
                put("testclass", "IfController");
                put("testname", name);
                put("enabled", enableStr);
            } });
        JmeterEleUtil.newEle(ifControllerEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "IfController.condition");
            } }, "<![CDATA[" + expression + "]]>");

        JmeterEleUtil.newEle(ifControllerEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "IfController.evaluateAll");
            } }, "false");
        JmeterEleUtil.newEle(ifControllerEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "IfController.useExpression");
            } }, "true");

        return JmeterEleUtil.newEle(parentEle,"hashTree");
    }


    /**
     * *
     * @param parentEle
     * @param jsonExtractorComponent
     * @param enable
     */
    public static void newJSONPostProcessor(Element parentEle, JsonExtractorComponent jsonExtractorComponent, boolean enable){

        JSONArray variableExtraction = jsonExtractorComponent.getVariableExtraction();
        if(variableExtraction==null){
            return;
        }
        if(variableExtraction.size()<=0){
            return;
        }
        Map<String,String> requestVarMaps = new HashMap<>();
        Map<String,String> responseVarMaps = new HashMap<>();
        for(int i=0; i<variableExtraction.size();i++){
            JSONObject varObj = variableExtraction.getJSONObject(i);
            if(StringUtil.isEmpty(varObj.getString("name").trim())||StringUtil.isEmpty(varObj.getString("value").trim())){
                continue;
            }
            if ("responseData".equals(varObj.getString("source"))) {
                responseVarMaps.put(varObj.getString("name").trim(),varObj.getString("value").trim());
            }
            if ("requestData".equals(varObj.getString("source"))) {
                requestVarMaps.put(varObj.getString("name").trim(),varObj.getString("value").trim());
            }
            if(varObj.getString("value").trim().indexOf("$responseData")==0){
                responseVarMaps.put(varObj.getString("name").trim(),varObj.getString("value").trim().replaceFirst("responseData",""));
            }
            if(varObj.getString("value").trim().indexOf("$requestData")==0){
                requestVarMaps.put(varObj.getString("name").trim(),varObj.getString("value").trim().replaceFirst("requestData",""));
            }
        }
        List<String> varsName = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(responseVarMaps)) {
            Element jsonPostProcessorEle = JmeterEleUtil.newEle(parentEle, "JSONPostProcessor", new HashMap<String, String>() {
                {
                    String enableStr = "true";
                    if (!enable) {
                        enableStr = "false";
                    }
                    put("guiclass", "JSONPostProcessorGui");
                    put("testclass", "JSONPostProcessor");
                    put("testname", "JSON变量提取器");
                    put("enabled", enableStr);
                }
            });

            String referenceNames = "";
            String jsonPathExprs = "";
            String matchNumbers = "";
            String defaultValues = "";


        for(String varName:responseVarMaps.keySet()){
            if(StringUtil.isEmpty(referenceNames)){
                referenceNames = referenceNames + varName;
                jsonPathExprs = jsonPathExprs + responseVarMaps.get(varName);
                matchNumbers = matchNumbers + "1";
                defaultValues = defaultValues + " ";
            }else {
                referenceNames = referenceNames + ";" + varName;
                jsonPathExprs = jsonPathExprs + ";" + responseVarMaps.get(varName);
                matchNumbers = matchNumbers + ";1";
                defaultValues = defaultValues + "; ";
            }
            varsName.add(varName);
        }

        JmeterEleUtil.newEle(jsonPostProcessorEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "JSONPostProcessor.referenceNames");
            } }, referenceNames);
        JmeterEleUtil.newEle(jsonPostProcessorEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "JSONPostProcessor.jsonPathExprs");
            } }, "<![CDATA["+jsonPathExprs+"]]>");
        JmeterEleUtil.newEle(jsonPostProcessorEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "JSONPostProcessor.match_numbers");
            } }, matchNumbers);

        JmeterEleUtil.newEle(jsonPostProcessorEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "Sample.scope");
            } }, "variable");
        JmeterEleUtil.newEle(jsonPostProcessorEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "Scope.variable");
            } }, "responseData");
        JmeterEleUtil.newEle(jsonPostProcessorEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "JSONPostProcessor.defaultValues");
            } }, defaultValues);

        JmeterEleUtil.newEle(parentEle, "hashTree");
        }

        if(CollectionUtil.isNotEmpty(requestVarMaps)) {
            Element jsonPostProcessorEle = JmeterEleUtil.newEle(parentEle, "JSONPostProcessor", new HashMap<String, String>() {
                {
                    String enableStr = "true";
                    if (!enable) {
                        enableStr = "false";
                    }
                    put("guiclass", "JSONPostProcessorGui");
                    put("testclass", "JSONPostProcessor");
                    put("testname", "JSON变量提取器");
                    put("enabled", enableStr);
                }
            });

            String referenceNames = "";
            String jsonPathExprs = "";
            String matchNumbers = "";
            String defaultValues = "";


            for(String varName:requestVarMaps.keySet()){
                if(StringUtil.isEmpty(referenceNames)){
                    referenceNames = referenceNames + varName;
                    jsonPathExprs = jsonPathExprs + requestVarMaps.get(varName);
                    matchNumbers = matchNumbers + "1";
                    defaultValues = defaultValues + " ";
                }else {
                    referenceNames = referenceNames + ";" + varName;
                    jsonPathExprs = jsonPathExprs + ";" + requestVarMaps.get(varName);
                    matchNumbers = matchNumbers + ";1";
                    defaultValues = defaultValues + "; ";
                }
                varsName.add(varName);
            }

            JmeterEleUtil.newEle(jsonPostProcessorEle,"stringProp",new HashMap<String, String>(){
                {
                    put("name", "JSONPostProcessor.referenceNames");
                } }, referenceNames);
            JmeterEleUtil.newEle(jsonPostProcessorEle,"stringProp",new HashMap<String, String>(){
                {
                    put("name", "JSONPostProcessor.jsonPathExprs");
                } }, "<![CDATA["+jsonPathExprs+"]]>");
            JmeterEleUtil.newEle(jsonPostProcessorEle,"stringProp",new HashMap<String, String>(){
                {
                    put("name", "JSONPostProcessor.match_numbers");
                } }, matchNumbers);

            JmeterEleUtil.newEle(jsonPostProcessorEle,"stringProp",new HashMap<String, String>(){
                {
                    put("name", "Sample.scope");
                } }, "variable");
            JmeterEleUtil.newEle(jsonPostProcessorEle,"stringProp",new HashMap<String, String>(){
                {
                    put("name", "Scope.variable");
                } }, "requestData");
            JmeterEleUtil.newEle(jsonPostProcessorEle,"stringProp",new HashMap<String, String>(){
                {
                    put("name", "JSONPostProcessor.defaultValues");
                } }, defaultValues);

            JmeterEleUtil.newEle(parentEle, "hashTree");
        }

        JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor",parentEle,"变量提取日志",ShellParseUtil.getJSONPathScripts(varsName),null,"",null,null,true);
    }

//    /**
//     * 用户思考时间组件*
//     * @param parentEle
//     * @param delay
//     * @param enable
//     */
//    public static void newConstantTimer(Element parentEle,String delay,boolean enable){
//        Element constantTimerEle = JmeterEleUtil.newEle(parentEle,"ConstantTimer",new HashMap<String, String>(){
//            {
//                String enableStr = "true";
//                if(!enable){
//                    enableStr = "false";
//                }
//                put("guiclass", "ConstantTimerGui");
//                put("testclass", "ConstantTimer");
//                put("testname", "sleep组件");
//                put("enabled", enableStr);
//            } });
//        JmeterEleUtil.newEle(constantTimerEle,"stringProp",new HashMap<String, String>(){
//            {
//                put("name", "ConstantTimer.delay");
//            } }, delay);
//        JmeterEleUtil.newEle(parentEle,"hashTree");
//    }


    public static Element newHTTPSampler(Element parentEle, HttpRequestComponent httpRequestComponent, boolean enable){

        Element httpSamplerEle = JmeterEleUtil.newEle(parentEle,"HTTPSamplerProxy",new HashMap<String, String>(){
            {
                String enableStr = "true";
                if(!enable){
                    enableStr = "false";
                }
                put("guiclass", "HttpTestSampleGui");
                put("testclass", "HTTPSamplerProxy");
                put("testname", httpRequestComponent.getName());
                put("enabled", enableStr);
            } });
        JSONArray filesInfo = httpRequestComponent.getHttpFiles();

        if(filesInfo!=null && filesInfo.size()>0){
            Element elementPropFiles = JmeterEleUtil.newEle(httpSamplerEle,"elementProp",new HashMap<String, String>(){
                {
                    put("name", "HTTPsampler.Files");
                    put("elementType", "HTTPFileArgs");

                } });
            Element collectionPropFiles = JmeterEleUtil.newEle(elementPropFiles,"collectionProp",new HashMap<String, String>(){
                {
                    put("name", "HTTPFileArgs.files");

                } });
            for(int i=0;i<filesInfo.size();i++){
                JSONObject fileInfo = filesInfo.getJSONObject(i);
                Element elementPropInfo = JmeterEleUtil.newEle(collectionPropFiles,"elementProp",new HashMap<String, String>(){
                    {
                        put("name", "oss://"+fileInfo.getString("path"));
                        put("elementType", "HTTPFileArg");

                    } });
                JmeterEleUtil.newEle(elementPropInfo,"stringProp",new HashMap<String, String>(){
                    {
                        put("name", "File.path");
                    } }, "<![CDATA["+fileInfo.getString("path")+"]]>");
                JmeterEleUtil.newEle(elementPropInfo,"stringProp",new HashMap<String, String>(){
                    {
                        put("name", "File.paramname");
                    } }, fileInfo.getString("paramname"));
                JmeterEleUtil.newEle(elementPropInfo,"stringProp",new HashMap<String, String>(){
                    {
                        put("name", "File.mimetype");
                    } }, "<![CDATA["+fileInfo.getString("mimetype")+"]]>");
            }
        }
        if(httpRequestComponent.getType()== LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE) {
            JmeterEleUtil.newEle(httpSamplerEle, "boolProp", new HashMap<String, String>() {
                {
                    put("name", "HTTPSampler.postBodyRaw");
                }
            }, "true");
            Element argumentsEle = JmeterEleUtil.newEle(httpSamplerEle, "elementProp", new HashMap<String, String>() {
                {
                    put("name", "HTTPsampler.Arguments");
                    put("elementType","Arguments");
                }
            });
            Element collectionPropEle = JmeterEleUtil.newEle(argumentsEle, "collectionProp", new HashMap<String, String>() {
                {
                    put("name", "Arguments.arguments");
                }
            });
            Element elementPropEle = JmeterEleUtil.newEle(collectionPropEle, "elementProp", new HashMap<String, String>() {
                {
                    put("name", "");
                    put("elementType","HTTPArgument");
                }
            });
            JmeterEleUtil.newEle(elementPropEle, "boolProp", new HashMap<String, String>() {
                {
                    put("name", "HTTPArgument.always_encode");
                }
            },"false");
            JmeterEleUtil.newEle(elementPropEle, "stringProp", new HashMap<String, String>() {
                {
                    put("name", "Argument.value");
                }
            },"${devops_qc_param}");
            JmeterEleUtil.newEle(elementPropEle, "stringProp", new HashMap<String, String>() {
                {
                    put("name", "Argument.metadata");
                }
            },"=");
        }

        if(httpRequestComponent.getType()== LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_FORM_TYPE) {
            Element argumentsEle = JmeterEleUtil.newEle(httpSamplerEle, "elementProp", new HashMap<String, String>() {
                {
                    put("name", "HTTPsampler.Arguments");
                    put("elementType","Arguments");
                    put("guiclass","HTTPArgumentsPanel");
                    put("testclass","Arguments");
                    put("testname","请求参数变量");
                    put("enabled","true");
                }
            });
            Element collectionPropEle = JmeterEleUtil.newEle(argumentsEle, "collectionProp", new HashMap<String, String>() {
                {
                    put("name", "Arguments.arguments");
                }
            });
            List<HttpParameter> parameters = httpRequestComponent.getParameters();
            for(HttpParameter parameter : parameters) {
                String parameterValue = StringUtils.isBlank(parameter.getValue()) ?
                        parameter.getValue() : parameter.getValue().replaceAll("&", "&amp;");
                Element elementPropEle = JmeterEleUtil.newEle(collectionPropEle, "elementProp", new HashMap<String, String>() {
                    {
                        put("name", parameter.getName());
                        put("elementType", "HTTPArgument");
                    }
                });
                JmeterEleUtil.newEle(elementPropEle, "boolProp", new HashMap<String, String>() {
                    {
                        put("name", "HTTPArgument.always_encode");
                    }
                }, parameter.getUrlEncode() ? "true" : "false");
                JmeterEleUtil.newEle(elementPropEle, "stringProp", new HashMap<String, String>() {
                    {
                        put("name", "Argument.value");
                    }
                }, parameterValue);
                JmeterEleUtil.newEle(elementPropEle, "stringProp", new HashMap<String, String>() {
                    {
                        put("name", "Argument.metadata");
                    }
                }, parameter.getIncludeEquals() ? "=" : "");
                JmeterEleUtil.newEle(elementPropEle, "boolProp", new HashMap<String, String>() {
                    {
                        put("name", "Argument.use_equals");
                    }
                }, parameter.getIncludeEquals() ? "true" : "false");
                JmeterEleUtil.newEle(elementPropEle, "stringProp", new HashMap<String, String>() {
                    {
                        put("name", "Argument.name");
                    }
                }, parameter.getName());
            }
        }

        JmeterEleUtil.newEle(httpSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "HTTPSampler.domain");
            }
        },httpRequestComponent.getServerNameOrIp());
        JmeterEleUtil.newEle(httpSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "HTTPSampler.port");
            }
        },httpRequestComponent.getPortNumber());
        JmeterEleUtil.newEle(httpSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "HTTPSampler.protocol");
            }
        },httpRequestComponent.getProtocol());
        JmeterEleUtil.newEle(httpSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "HTTPSampler.contentEncoding");
            }
        },httpRequestComponent.getContentEncoding());
        JmeterEleUtil.newEle(httpSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "HTTPSampler.path");
            }
        },"<![CDATA["+httpRequestComponent.getPathUrl()+"]]>");
        JmeterEleUtil.newEle(httpSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "HTTPSampler.method");
            }
        },httpRequestComponent.getMethod());

        String followRedirects = "true";
        if(!httpRequestComponent.getFollowRedirects()){
            followRedirects = "false";
        }
        JmeterEleUtil.newEle(httpSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "HTTPSampler.follow_redirects");
            }
        },followRedirects);
        JmeterEleUtil.newEle(httpSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "HTTPSampler.auto_redirects");
            }
        },"false");
        JmeterEleUtil.newEle(httpSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "HTTPSampler.use_keepalive");
            }
        },"true");
        String multipart_post = "false";
        if(httpRequestComponent.getUserMultipartFormData()){
            multipart_post = "true";
        }
        JmeterEleUtil.newEle(httpSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "HTTPSampler.DO_MULTIPART_POST");
            }
        }, multipart_post);

        JmeterEleUtil.newEle(httpSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "HTTPSampler.embedded_url_re");
            }
        });

        JmeterEleUtil.newEle(httpSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "HTTPSampler.connect_timeout");
            }
        });
        JmeterEleUtil.newEle(httpSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "HTTPSampler.response_timeout");
            }
        });


        return JmeterEleUtil.newEle(parentEle,"hashTree");
    }

    public static Element newDataCennterSampler(Element parentEle, String componentName, String componentCode,String ossPath, String comments, boolean enable){

        Element dataCenterSamplerEle = JmeterEleUtil.newEle(parentEle, "DataCenterSampler",new HashMap<String, String>(){
            {
                String enableStr = "true";
                if(!enable){
                    enableStr = "false";
                }
                put("guiclass", "DataCenterSamplerGui");
                put("testclass", "DataCenterSampler");
                put("testname", componentName);
                put("enabled", enableStr);
            } });
        JmeterEleUtil.newEle(dataCenterSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "ossPath");
            }
        },ossPath);
        JmeterEleUtil.newEle(dataCenterSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "code");
            }
        },componentCode);
        JmeterEleUtil.newEle(dataCenterSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "TestPlan.comments");
            }
        },comments);
        JmeterEleUtil.newEle(parentEle,"hashTree");
        Element hashDc = JmeterEleUtil.newEle(dataCenterSamplerEle,"hashTree");
        JmeterEleUtil.newEle(hashDc,"pre");
        JmeterEleUtil.newEle(hashDc,"post");
        return hashDc;
    }

    public static Element newZMSSampler(Element parentEle, ZMSClientRequestComponent zmsRequestComponent, boolean enable){

        Element zmsSamplerEle = JmeterEleUtil.newEle(parentEle, AutomaticConstant.ZMSSampler,new HashMap<String, String>(){
            {
                String enableStr = "true";
                if(!enable){
                    enableStr = "false";
                }
                put("guiclass", "com.zto.zmsjmeter.ZMSSamplerGui");
                put("testclass", AutomaticConstant.ZMSSampler);
                put("testname", zmsRequestComponent.getName());
                put("enabled", enableStr);
            } });

        JmeterEleUtil.newEle(zmsSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "zmsZKAddr");
            }
        },zmsRequestComponent.getAddress());
        JmeterEleUtil.newEle(zmsSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "topic");
            }
        },zmsRequestComponent.getTopic());
        JmeterEleUtil.newEle(zmsSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "messageKey");
            }
        },zmsRequestComponent.getMsgKey());
        JmeterEleUtil.newEle(zmsSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "messageTag");
            }
        },zmsRequestComponent.getMsgTag());
        JmeterEleUtil.newEle(zmsSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "routeTag");
            }
        },zmsRequestComponent.getRouteTag());
        JmeterEleUtil.newEle(zmsSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "messageBody");
            }
        },"<![CDATA["+zmsRequestComponent.getMessageBody()+"]]>");

        JmeterEleUtil.newEle(zmsSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "messageSendMod");
            }
        }, zmsRequestComponent.getMessageSendMod());

        return JmeterEleUtil.newEle(parentEle,"hashTree");
    }

    public static Element newDubboSample(Element parentEle, DubboRequestComponent dubboRequestComponent, boolean enable){

        Element dubboSamplerEle = JmeterEleUtil.newEle(parentEle, AutomaticConstant.DubboSample,new HashMap<String, String>(){
            {
                String enableStr = "true";
                if(!enable){
                    enableStr = "false";
                }
                put("guiclass", "io.github.ningyu.jmeter.plugin.dubbo.gui.DubboSampleGui");
                put("testclass", AutomaticConstant.DubboSample);
                put("testname", dubboRequestComponent.getName());
                put("enabled", enableStr);
            } });
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_CONFIG_CENTER_PROTOCOL");
            }
        });
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_CONFIG_CENTER_GROUP");
            }
        });
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_CONFIG_CENTER_NAMESPACE");
            }
        });
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_CONFIG_CENTER_USER_NAME");
            }
        });
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_CONFIG_CENTER_PASSWORD");
            }
        });
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_CONFIG_CENTER_ADDRESS");
            }
        });
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_CONFIG_CENTER_TIMEOUT");
            }
        });
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_REGISTRY_PROTOCOL");
            }
        },"zookeeper");
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_REGISTRY_GROUP");
            }
        });
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_REGISTRY_USER_NAME");
            }
        });
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_REGISTRY_PASSWORD");
            }
        });
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_ADDRESS");
            }
        },dubboRequestComponent.getAddress());
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_REGISTRY_TIMEOUT");
            }
        });
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_RPC_PROTOCOL");
            }
        },"dubbo://");
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_TIMEOUT");
            }
        },String.valueOf(dubboRequestComponent.getTimeout()));
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_VERSION");
            }
        },dubboRequestComponent.getVersion());
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_RETRIES");
            }
        },String.valueOf(dubboRequestComponent.getRetries()));
        if(dubboRequestComponent.getGroup()==null){
            dubboRequestComponent.setGroup("");
        }
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_GROUP");
            }
        },dubboRequestComponent.getGroup());
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_CONNECTIONS");
            }
        },"10");
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_LOADBALANCE");
            }
        },"random");
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_ASYNC");
            }
        },"sync");
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_CLUSTER");
            }
        },"failfast");
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_INTERFACE");
            }
        },dubboRequestComponent.getInterfaceName());
        JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "FIELD_DUBBO_METHOD");
            }
        },dubboRequestComponent.getMethodName());

//        JSONArray args = dubboRequestComponent.getArgs();
//        if(args!=null){
//            JmeterEleUtil.newEle(dubboSamplerEle, "intProp", new HashMap<String, String>() {
//                {
//                    put("name", "FIELD_DUBBO_METHOD_ARGS_SIZE");
//                }
//            },String.valueOf(args.size()));
//            for(int argscount=0;argscount<args.size();argscount++){
//                JSONObject argObject = args.getJSONObject(argscount);
//                String keyName = "FIELD_DUBBO_METHOD_ARGS_PARAM_TYPE"+argscount;
//                String valueName = "FIELD_DUBBO_METHOD_ARGS_PARAM_VALUE"+argscount;
//                JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
//                    {
//                        put("name", keyName);
//                    }
//                },argObject.getString("paramType"));
//                JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
//                    {
//                        put("name", valueName);
//                    }
//                },"<![CDATA["+argObject.getString("paramValue")+"]]>");
//            }
//        }else {
//            JmeterEleUtil.newEle(dubboSamplerEle, "intProp", new HashMap<String, String>() {
//                {
//                    put("name", "FIELD_DUBBO_METHOD_ARGS_SIZE");
//                }
//            },"0");
//        }
//
//        JSONArray attachment = dubboRequestComponent.getAttachmentArgs();
//        if(attachment!=null){
//            JmeterEleUtil.newEle(dubboSamplerEle, "intProp", new HashMap<String, String>() {
//                {
//                    put("name", "FIELD_DUBBO_ATTACHMENT_ARGS_SIZE");
//                }
//            },String.valueOf(attachment.size()));
//            for(int attachmentcount=0;attachmentcount<args.size();attachmentcount++){
//                JSONObject attachmentObject = args.getJSONObject(attachmentcount);
//                String keyName = "FIELD_DUBBO_ATTACHMENT_ARGS_KEY"+attachmentcount;
//                String valueName = "FIELD_DUBBO_ATTACHMENT_ARGS_VALUE"+attachmentcount;
//                JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
//                    {
//                        put("name", keyName);
//                    }
//                },attachmentObject.getString("key"));
//                JmeterEleUtil.newEle(dubboSamplerEle, "stringProp", new HashMap<String, String>() {
//                    {
//                        put("name", valueName);
//                    }
//                },"<![CDATA["+attachmentObject.getString("value")+"]]>");
//            }
//        }else {
//            JmeterEleUtil.newEle(dubboSamplerEle, "intProp", new HashMap<String, String>() {
//                {
//                    put("name", "FIELD_DUBBO_ATTACHMENT_ARGS_SIZE");
//                }
//            },"0");
//        }

        return JmeterEleUtil.newEle(parentEle,"hashTree");
    }

    public static Element newBeanshellSampler(String typeName,Element parentEle,String name,String scriptContent,String scriptFileName,String comments,String parameters,boolean enable) {
        if(StringUtil.isEmpty(name)) {
            name = typeName;
        }
        String finalName = name;
        Element shellEle = JmeterEleUtil.newEle(parentEle,typeName,new HashMap<String, String>(){
            {
                String enableStr = "true";
                if(!enable){
                    enableStr = "false";
                }
                put("guiclass", "TestBeanGUI");
                put("testclass", typeName);
                put("testname", finalName);
                put("enabled", enableStr);
            } });
        JmeterEleUtil.newEle(shellEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "filename");
            } }, scriptFileName);
        JmeterEleUtil.newEle(shellEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "parameters");
            } }, parameters);
        JmeterEleUtil.newEle(shellEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "resetInterpreter");
            } }, "false");
        if(StringUtils.isNotEmpty(scriptContent)) {
            scriptContent = "<![CDATA[" + scriptContent + "]]>";
//            scriptContent = StringEscapeUtils.escapeXml(scriptContent);
        }
        JmeterEleUtil.newEle(shellEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "script");
            } }, scriptContent);
        if(StringUtil.isNotEmpty(comments)) {
            JmeterEleUtil.newEle(shellEle, "stringProp", new HashMap<String, String>() {
                {
                    put("name", "TestPlan.comments");
                }
            }, comments);
        }
        return JmeterEleUtil.newEle(parentEle,"hashTree");
    }

    public static Element newBeanShellAssertion(String typeName,Element parentEle,String name,String scriptContent,String scriptFileName,String comments,String parameters,boolean enable) {
        if(StringUtil.isEmpty(name)) {
            name = typeName;
        }
        String finalName = name;
        Element shellEle = JmeterEleUtil.newEle(parentEle,typeName,new HashMap<String, String>(){
            {
                String enableStr = "true";
                if(!enable){
                    enableStr = "false";
                }
                put("guiclass", "BeanShellAssertionGui");
                put("testclass", typeName);
                put("testname", finalName);
                put("enabled", enableStr);
            } });
        if(StringUtil.isNotEmpty(comments)) {
            JmeterEleUtil.newEle(shellEle, "stringProp", new HashMap<String, String>() {
                {
                    put("name", "TestPlan.comments");
                }
            }, comments);
        }
        if(StringUtils.isNotEmpty(scriptContent)) {
            scriptContent = "<![CDATA[" + scriptContent + "]]>";
//            scriptContent = StringEscapeUtils.escapeXml(scriptContent);
        }
        JmeterEleUtil.newEle(shellEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "BeanShellAssertion.query");
            } }, scriptContent);
        JmeterEleUtil.newEle(shellEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "BeanShellAssertion.filename");
            } }, scriptFileName);
        JmeterEleUtil.newEle(shellEle,"stringProp",new HashMap<String, String>(){
            {
                put("name", "BeanShellAssertion.parameters");
            } }, parameters);
        JmeterEleUtil.newEle(shellEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "BeanShellAssertion.resetInterpreter");
            } }, "false");
        return JmeterEleUtil.newEle(parentEle,"hashTree");
    }

    public static void newJSONPathAssert(Element parentEle,JSONAssertComponent jsonAssertComponent,boolean enable) {
        if(StringUtil.isBlank(jsonAssertComponent.getExpectedValue()) && (!jsonAssertComponent.isExpectNull())){
            return;
        }
        Element jsonPathAssertEle = JmeterEleUtil.newEle(parentEle, "JSONPathAssertion", new HashMap<String, String>() {
            {
                String enableStr = "true";
                if (!enable) {
                    enableStr = "false";
                }
                put("guiclass", "JSONPathAssertionGui");
                put("testclass", "JSONPathAssertion");
                put("testname", "JSON断言");
                put("enabled", enableStr);
            }
        });
        JmeterEleUtil.newEle(jsonPathAssertEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "JSON_PATH");
            }
        }, StringEscapeUtils.escapeXml(jsonAssertComponent.getJsonPath()));
        JmeterEleUtil.newEle(jsonPathAssertEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "EXPECTED_VALUE");
            }
        }, StringEscapeUtils.escapeXml(jsonAssertComponent.getExpectedValue()));

        String jsonValidationStr = "true";
        if (!jsonAssertComponent.isJsonValidation()) {
            jsonValidationStr = "false";
        }
        JmeterEleUtil.newEle(jsonPathAssertEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "JSONVALIDATION");
            } }, jsonValidationStr);

        String ifRegexStr = "true";
        if (!jsonAssertComponent.isIfRegex()) {
            ifRegexStr = "false";
        }
        JmeterEleUtil.newEle(jsonPathAssertEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "ISREGEX");
            } }, ifRegexStr);

        String expectNullStr = "true";
        if (!jsonAssertComponent.isExpectNull()) {
            expectNullStr = "false";
        }
        JmeterEleUtil.newEle(jsonPathAssertEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "EXPECT_NULL");
            } }, expectNullStr);

        String invertStr = "true";
        if (!jsonAssertComponent.isInvert()) {
            invertStr = "false";
        }
        JmeterEleUtil.newEle(jsonPathAssertEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "INVERT");
            } }, invertStr);

        JmeterEleUtil.newEle(parentEle,"hashTree");
    }

    public static void newResponseAssert(Element parentEle,ResponseAssertComponent responseAssertComponent,boolean enable) {
        if (CollectionUtil.isEmpty(responseAssertComponent.getTestStrings())) {
            return;
        }
        Element responseAssertEle = JmeterEleUtil.newEle(parentEle, "ResponseAssertion", new HashMap<String, String>() {
            {
                String enableStr = "true";
                if (!enable) {
                    enableStr = "false";
                }
                put("guiclass", "AssertionGui");
                put("testclass", "ResponseAssertion");
                put("testname", "响应断言");
                put("enabled", enableStr);
            }
        });

        JmeterEleUtil.newEle(responseAssertEle, "stringProp", new HashMap<String, String>() {
            {
                put("name", "Assertion.test_field");
            }
        }, responseAssertComponent.getTestField());

        if(StringUtil.isNotBlank(responseAssertComponent.getCustomMessage())){
            JmeterEleUtil.newEle(responseAssertEle, "stringProp", new HashMap<String, String>() {
                {
                    put("name", "Assertion.custom_message");
                }
            }, StringEscapeUtils.escapeXml(responseAssertComponent.getCustomMessage()));
        }

        String assumeSuccessStr = "true";
        if(responseAssertComponent.isAssumeSuccess()){
            assumeSuccessStr = "false";
        }
        JmeterEleUtil.newEle(responseAssertEle,"boolProp",new HashMap<String, String>(){
            {
                put("name", "Assertion.assume_success");
            } }, assumeSuccessStr);

        JmeterEleUtil.newEle(responseAssertEle, "intProp", new HashMap<String, String>() {
        {
            put("name", "Assertion.test_type");
        }
        },String.valueOf(responseAssertComponent.countTestType()));

        Element testStringsEle = JmeterEleUtil.newEle(responseAssertEle,"collectionProp",new HashMap<String, String>(){
            {
                put("name", "Asserion.test_strings");
            } });

        responseAssertComponent.getTestStrings().forEach((String value) -> {
            JmeterEleUtil.newEle(testStringsEle, "stringProp", new HashMap<String, String>() {
                {
                    put("name", String.valueOf(value.hashCode()));
                }
            }, StringEscapeUtils.escapeXml(value));
        });

        JmeterEleUtil.newEle(parentEle, "hashTree");
    }

    public static Element newSimpleController(String name, Element parentEle, boolean enable) {
        JmeterEleUtil.newEle(parentEle, "GenericController", new HashMap<String, String>() {{
            put("guiclass", "LogicControllerGui");
            put("testclass", "GenericController");
            put("testname", name);
            put("enabled", Boolean.toString(enable));
        }});
        return JmeterEleUtil.newEle(parentEle,"hashTree");
    }

    public static Element newWhileController(String name, Element parentEle, String condition, boolean enable) {
        Element ele = JmeterEleUtil.newEle(parentEle, "WhileController", new HashMap<String, String>() {{
            put("guiclass", "WhileControllerGui");
            put("testclass", "WhileController");
            put("testname", name);
            put("enabled", Boolean.toString(enable));
        }});
        JmeterEleUtil.newEle(ele,"stringProp", new HashMap<String, String>() {{
            put("name", "WhileController.condition");
        }}, "<![CDATA[" + condition + "]]>");
        return JmeterEleUtil.newEle(parentEle,"hashTree");
    }
}
