package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.qc.client.model.dto.KnowledgeBaseEntityDO;
import com.zto.devops.qc.client.model.knowledgebase.query.KnowledgeBaseByProductCodeQuery;
import com.zto.devops.qc.client.model.knowledgebase.query.KnowledgeBaseVO;
import com.zto.devops.qc.domain.gateway.repository.KnowledgeBaseRepository;
import com.zto.devops.qc.infrastructure.converter.KnowledgeBaseEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.KnowledgeBaseEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.KnowledgeBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Component
@Slf4j
public class KnowledgeBaseRepositoryImpl implements KnowledgeBaseRepository {

    @Autowired
    private KnowledgeBaseMapper knowledgeBaseMapper;

    @Autowired
    private KnowledgeBaseEntityConverter converter;


    @Override
    public KnowledgeBaseVO getKnowledgeBaseDetail(KnowledgeBaseByProductCodeQuery query) {
        log.info("KnowledgeBaseByProductCodeQuery >>> {}", query.getProductCode());
        Example example = new Example(KnowledgeBaseEntity.class);
        example.createCriteria().andEqualTo("productCode", query.getProductCode());
        List<KnowledgeBaseEntity> list = knowledgeBaseMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return converter.convert(list.get(0));
    }

    @Override
    public void insertBatch(List<KnowledgeBaseEntityDO> list) {
        knowledgeBaseMapper.insertBatch(converter.convert(list));
    }

    @Override
    public void delete() {
        knowledgeBaseMapper.deleteAll();
    }
}
