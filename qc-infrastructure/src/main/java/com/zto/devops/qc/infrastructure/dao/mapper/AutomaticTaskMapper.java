package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTaskVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteTagVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageAutomaticTaskGroupQuery;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticTaskEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface AutomaticTaskMapper extends Mapper<AutomaticTaskEntity> {

    /**
     * 查询创建时间大于60分钟的自动化任务
     *
     * @param duration
     * @return
     */
    List<String> selectAbortAutomaticTasks(@Param("duration") Integer duration);

    /**
     * 查询自动化父任务id集合
     *
     * @param query {@link PageAutomaticTaskGroupQuery}
     * @return 父任务id集合
     */
    List<String> selectAutomaticParentTaskIdList(PageAutomaticTaskGroupQuery query);

    /**
     * 根据父任务id集合，查询自动化任务列表
     *
     * @param taskIdList {@link String}
     * @return {@link AutomaticTaskVO}
     */
    List<AutomaticTaskVO> selectAutomaticTaskListByTaskId(@Param("taskIdList") List<String> taskIdList);

    /**
     * 根据父任务id，查任务列表
     *
     * @param taskId 父任务id
     * @return
     */
    List<AutomaticTaskEntity> selectListByTaskId(@Param("taskId") String taskId);

    /**
     * 根据产品code，查询任务执行空间列表
     *
     * @param productCode 产品code
     * @param keyWord     空间名
     * @return {@link ExecuteTagVO}
     */
    List<ExecuteTagVO> listExecuteEnv(@Param("productCode") String productCode, @Param("keyWord") String keyWord);

    List<String> selectApiTestWaitTasks(@Param("duration") Integer duration);
}
