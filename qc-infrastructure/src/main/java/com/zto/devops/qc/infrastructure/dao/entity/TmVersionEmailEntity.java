package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.EmailTypeHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import java.io.Serializable;
import java.util.Date;

@Data
public class TmVersionEmailEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private String emailCode;

    private String emailName;

    private String businessCode;

    @ColumnType(typeHandler = EmailTypeHandler.class)
    private EmailTypeEnum emailType;

    private String versionName;

    private String versionCode;

    private String productCode;

    private String productName;

    private String sender;

    private String senderId;

    private Date sendDate;

    private String source;

}
