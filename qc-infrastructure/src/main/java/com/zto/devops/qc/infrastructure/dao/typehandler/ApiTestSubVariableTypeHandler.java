package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @create 2023/11/6 16:40
 */
@MappedJdbcTypes(value = JdbcType.INTEGER, includeNullJdbcType = true)
public class ApiTestSubVariableTypeHandler extends BaseEnumTypeHandler<SubVariableTypeEnum> {
    public ApiTestSubVariableTypeHandler() {
        super(SubVariableTypeEnum.class);
    }

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, SubVariableTypeEnum subVariableTypeEnum, JdbcType jdbcType) throws SQLException {
        if (null == subVariableTypeEnum) {
            preparedStatement.setInt(i, 0);
        } else {
            preparedStatement.setInt(i, subVariableTypeEnum.getValue());
        }
    }

    @Override
    public SubVariableTypeEnum getNullableResult(ResultSet resultSet, String s) throws SQLException {
        int result = resultSet.getInt(s);
        return SubVariableTypeEnum.valueOf(result);
    }

    @Override
    public SubVariableTypeEnum getNullableResult(ResultSet resultSet, int i) throws SQLException {
        int result = resultSet.getInt(i);
        return SubVariableTypeEnum.valueOf(result);
    }

    @Override
    public SubVariableTypeEnum getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        int result = callableStatement.getInt(i);
        return SubVariableTypeEnum.valueOf(result);
    }
}
