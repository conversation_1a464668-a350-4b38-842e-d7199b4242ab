package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.rpc.user.ListUserQuery;
import com.zto.devops.qc.client.model.rpc.user.UserSelectVO;
import com.zto.devops.qc.domain.gateway.repository.IQueryUserMange;
import com.zto.devops.qc.domain.gateway.rpc.IUserRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IQueryUserMangeImpl implements IQueryUserMange {

    @Autowired
    private IUserRpcService iUserRpcService;

    @Override
    public String getEmailForUser(List<SendUserInfoVO> receiveUsers) {
        if (CollectionUtil.isEmpty(receiveUsers)) {
            return "";
        }
        StringBuilder emails = new StringBuilder();
        //    List<Long> userId = receiveUsers.stream().filter(r -> r.getUserId() != null).map(SendUserInfoVO::getUserId).collect(Collectors.toList());
        String email = receiveUsers.stream().filter(v -> StringUtil.isNotBlank(v.getEmail()))
                .map(SendUserInfoVO::getEmail).collect(Collectors.joining(","));
        emails.append(email);
        List<String> userEmail = receiveUsers.stream().filter(r -> StringUtil.isNotBlank(r.getEmail())).map(SendUserInfoVO::getEmail).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(userEmail)) {
            String userEmails = userEmail.stream().collect(Collectors.joining(","));
            if (StringUtil.isNotBlank(emails)) {
                emails.append(",");
            }
            emails.append(userEmails);
        }
        String emailstr = disEmail(emails.toString());
        return emailstr;
    }

    @Override
    public List<SendUserInfoVO> setEmailForUser(List<SendUserInfoVO> receiveUsers) {
        if (CollectionUtil.isEmpty(receiveUsers)) {
            return Collections.emptyList();
        }
        Set<Long> ssoUserIds = receiveUsers.stream()
                .filter(t -> null != t.getUserId() && t.getUserId() != 0L && StringUtil.isEmpty(t.getEmail()))
                .map(SendUserInfoVO::getUserId).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(ssoUserIds)) {
            return receiveUsers;
        }
        ListUserQuery query = new ListUserQuery();
        query.setSsoUserIdList(new ArrayList<>(ssoUserIds));
        query.setRows(ssoUserIds.size());
        List<UserSelectVO> users = iUserRpcService.listUserQuery(query);
        if (CollectionUtil.isEmpty(users)) {
            return receiveUsers;
        }
        Map<Long, String> userEmailMap = users.stream().filter(u -> StringUtil.isEmpty(u.getEmail())).collect(Collectors.toMap(UserSelectVO::getSsoUserId, UserSelectVO::getEmail));
        if (CollectionUtil.isEmpty(userEmailMap)) {
            return receiveUsers;
        }
        receiveUsers.forEach(r -> {
            if (null != r.getUserId() && userEmailMap.containsKey(r.getUserId())) {
                r.setEmail(userEmailMap.get(r.getUserId()));
            }
        });
        return receiveUsers;
    }

    // email 进行去重
    private String disEmail(String emails) {
        String result = emails;
        if (StringUtil.isBlank(emails)) {
            return result;
        }
        String[] split = emails.split(",");
        if (split != null && split.length > 0) {
            List<String> strings = Arrays.asList(split);
            List<String> collect = strings.stream().distinct().collect(Collectors.toList());
            result = collect.stream().collect(Collectors.joining(","));
        }
        return result;
    }
}
