package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.issue.query.FindIssueByVersionCodeQuery;
import com.zto.devops.qc.infrastructure.dao.entity.TransitionNodeEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TransitionNodeMapper extends Mapper<TransitionNodeEntity> {

    List<TransitionNodeEntity> queryLastTransitionNodeByVersionCode(FindIssueByVersionCodeQuery query);

    List<TransitionNodeEntity> selectIssueReopenList(@Param("businessCodes") List<String> businessCodes);

    /**
     * 根据缺陷的发现或修复版本统计无效缺陷个数
     *
     * @param versionCode 版本code
     * @return 无效缺陷个数
     */
    Integer countInvalidCount(@Param("versionCode") String versionCode);

    Integer countInvalidUiTestCount(@Param("versionCode") String versionCode);

    List<TransitionNodeEntity> selectContentByIssueList(@Param("businessCodes") List<String> businessCodes);
}