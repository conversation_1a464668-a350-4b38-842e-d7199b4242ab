package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.event.AttachmentAddedEvent;
import com.zto.devops.qc.client.model.issue.event.AttachmentRemovedSimpleEvent;
import com.zto.devops.qc.client.model.issue.query.FindAttachmentByCodeQuery;
import com.zto.devops.qc.client.model.issue.query.ListAttachmentsByBusinessCodeQuery;
import com.zto.devops.qc.domain.gateway.repository.AttachmentRepository;
import com.zto.devops.qc.infrastructure.converter.AttachmentEntityConverter;
import com.zto.devops.qc.infrastructure.converter.AttachmentVOSConverter;
import com.zto.devops.qc.infrastructure.dao.entity.AttachmentEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.AttachmentMapper;
import com.zto.titans.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Component
@Slf4j
public class AttachmentRepositoryImpl implements AttachmentRepository {

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Autowired
    private AttachmentVOSConverter attachmentVOSConverter;

    @Autowired
    private AttachmentEntityConverter attachmentEntityConverter;

    @Override
    public List<AttachmentVO> findListByBusinessCode(String code) {
        Example example = new Example(AttachmentEntity.class);
        example.orderBy("gmtCreate").asc();
        example.createCriteria().andEqualTo("businessCode", code).andEqualTo("enable", Boolean.TRUE);
        List<AttachmentEntity> entityList = attachmentMapper.selectByExample(example);
        List<AttachmentVO> vos = attachmentVOSConverter.convert(entityList);
        if(CollectionUtil.isNotEmpty(vos)){
            for(AttachmentVO vo : vos){
                if(StringUtil.isNotBlank(vo.getRemoteFileId())){
                    vo.setUrl("");
                }
            }
        }
        return vos;
    }

    @Override
    public List<AttachmentVO> query(ListAttachmentsByBusinessCodeQuery query) {
        Example example = new Example(AttachmentEntity.class);
        example.orderBy("gmtCreate").asc();
        example.createCriteria().andEqualTo("businessCode", query.getBusinessCode()).andEqualTo("enable", Boolean.TRUE);
        List<AttachmentEntity> entityList = attachmentMapper.selectByExample(example);
        List<AttachmentVO> vos = attachmentVOSConverter.convert(entityList);
        if(CollectionUtil.isNotEmpty(vos)){
            for(AttachmentVO vo : vos){
                if(StringUtil.isNotBlank(vo.getRemoteFileId())){
                    vo.setUrl("");
                }
            }
        }
        return vos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handle(AttachmentAddedEvent event) {
        List<AttachmentVO> attachmentVOS = event.getAttachments();
        List<AttachmentEntity> entityList = attachmentEntityConverter.convert(attachmentVOS);
        for (AttachmentEntity entity : entityList) {
            entity.preCreate(event);
            attachmentMapper.insertSelective(entity);
        }
    }

    @Override
    public AttachmentVO query(FindAttachmentByCodeQuery query) {
        if (StringUtil.isEmpty(query.getCode())) {
            return null;
        }
        Example example = new Example(AttachmentEntity.class);
        example.createCriteria().andEqualTo("code", query.getCode());
        return attachmentVOSConverter.convert(attachmentMapper.selectOneByExample(example));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handle(AttachmentRemovedSimpleEvent event) {
        AttachmentEntity entity = new AttachmentEntity();
        entity.setCode(event.getCode());
        entity.setEnable(Boolean.FALSE);
        attachmentMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public Integer getAttachmentCount(String code) {
        Example example = new Example(AttachmentEntity.class);
        example.orderBy("gmtCreate").asc();
        example.createCriteria().andEqualTo("businessCode", code).andEqualTo("enable", Boolean.TRUE);
        return attachmentMapper.selectCountByExample(example);
    }

    @Override
    public void addAttachmentList(List<AttachmentVO> attachments, String businessCode, BaseEvent event) {
        if (CollectionUtils.isEmpty(attachments)) {
            return;
        }
        List<AttachmentEntity> entityList = attachmentVOSConverter.convertVOList(attachments);
        for (AttachmentEntity attachment : entityList) {
            attachment.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
            attachment.preCreate(event);
            attachment.setBusinessCode(businessCode);
            attachmentMapper.insertSelective(attachment);
        }
    }
}
