package com.zto.devops.qc.infrastructure.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.enums.PropertyChangeType;
import com.ctrip.framework.apollo.model.ConfigChange;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
@ConfigurationProperties("qc.debug")
@Component
public class QcDebugConfig {

    private String executeUrl;

    private String abortUrl;

    private List<QcDebugServer> server;

    @Data
    public static class QcDebugServer {

        private String secret;

        private String host;

        private int status; // 0-下线，1-在线

        private long maxTaskNum;
    }

    private Config config = ConfigService.getAppConfig();

    @PostConstruct
    public void onChangeListener() {
        Pattern pattern = Pattern.compile("qc.debug.server\\[(\\d+)].([\\w+]+)");
        config.addChangeListener(configChangeEvent -> {
            for (String changedKey : configChangeEvent.changedKeys()) {
                Matcher matcher = pattern.matcher(changedKey);
                while (matcher.find()) {
                    if (null == this.server) {
                        this.server = new ArrayList<>();
                    }
                    int index = Integer.parseInt(matcher.group(1));
                    ConfigChange configChange = configChangeEvent.getChange(changedKey);
                    if (PropertyChangeType.ADDED.equals(configChange.getChangeType())) {
                        while (index > this.server.size() - 1) {
                            this.server.add(new QcDebugServer());
                        }
                    }
                    QcDebugServer qcDebugServer = this.server.get(index);
                    String fieldName = matcher.group(2);
                    try {
                        Field field = qcDebugServer.getClass().getDeclaredField(fieldName);
                        field.setAccessible(true);
                        field.set(qcDebugServer, configChange.getNewValue());
                    } catch (NoSuchFieldException | IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        });
    }
}
