package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.client.service.testmanager.apitest.model.PageSceneInfoReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.QueryLatestEditSceneInfoReq;
import com.zto.devops.qc.infrastructure.dao.entity.SceneInfoEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface SceneInfoMapper extends Mapper<SceneInfoEntity> {

    List<SceneInfoEntityDO> pageQuerySceneList(PageSceneInfoReq req);

    SceneInfoEntity queryLatestEditSceneInfo(QueryLatestEditSceneInfoReq req);

    List<SceneInfoEntity> querySceneByProductCodeAndSceneType(@Param("productCode") String productCode,@Param("sceneType") Integer sceneType);

}
