package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.project.client.model.requirement.entity.RelatedMatterStatusCountVO;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.model.issue.entity.CountUserIssueNumVO;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.infrastructure.dao.entity.IssueEntity;
import com.zto.devops.qc.client.model.parameter.IssueQueryParameter;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface IssueMapper extends Mapper<IssueEntity> {

    List<IssueVO> selectByDefinedQuery(IssueQueryParameter query);

    List<String> listStatusByDefinedQuery(IssueQueryParameter query);

    List<Map<Long, Long>> queryIssueGroupByHandle(@Param("status") String status);

    List<IssueVO> simpleQuery(IssueQueryParameter query);

    List<String> selectFindVersionCode();

    List<String> selectIssueCodeList(IssueQueryParameter parameter);

    List<IssueVO> selectDevopsCreateIssue();

//    List<IssueVO> selectIssueByVersionCode(@Param("findVersionCode")String findVersionCode, @Param("fixVersionCode")String fixVersionCode);

    List<IssueVO> findUnClosedByVersionCodes(@Param("versionCodes") List<String> versionCodes);

    List<RelatedMatterStatusCountVO> selectRelatedMatterStatusCount(@Param("requirementCodeList") List<String> requirementCodeList);

    List<IssueVO> listIssueByHandleUserId(Long handleUserId);

    List<CountUserIssueNumVO> queryFixIssueGroupByDevelopUserId(@Param("list") List<IssueStatus> statusList);

    List<CountUserIssueNumVO> queryTestIssueGroupByTestUserId(@Param("list") List<IssueStatus> statusList);
}