package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@ToString
@Data
@Table(name = "qc_method_dependence")
public class MethodDependenceEntity extends BaseEntity {

    @Column(name = "id")
    private Long id;

    /**
     * 产品编码
     */
    @Column(name = "product_code")
    private String productCode;

    /**
     * 产品名称
     */
    @Column(name = "product_name")
    private String productName;

    /**
     * 方法编码
     */
    @Column(name = "method_code")
    private String methodCode;

    /**
     * appId
     */
    @Id
    @Column(name = "app_id")
    private String appId;

    /**
     * 版本code
     */
    @Column(name = "version_code")
    private String versionCode;

    /**
     * 版本名称
     */
    @Column(name = "version_name")
    private String versionName;

    /**
     * 提交记录id
     */
    @Column(name = "commit_id")
    private String commitId;

    /**
     * 入口方法编号
     */
    @Column(name = "entry_method_code")
    private String entryMethodCode;

    /**
     * 入口方法类型
     */
    @Column(name = "entry_method_type")
    private String entryMethodType;

    /**
     * 父方法编号
     */
    @Column(name = "parent_method_code")
    private String parentMethodCode;

    /**
     * 全路径类名称
     */
    @Column(name = "full_class_name")
    private String fullClassName;

    /**
     * 方法名称
     */
    @Column(name = "method_name")
    private String methodName;

    /**
     * 方法描述
     */
    @Column(name = "method_desc")
    private String methodDesc;

    /**
     * 方法类型
     */
    @Column(name = "method_type")
    private String methodType;

    /**
     * 方法注解
     */
    @Column(name = "method_annotation")
    private String methodAnnotation;

    /**
     * 方法层级
     */
    @Column(name = "method_level")
    private Integer methodLevel;

    /**
     * 方法排序
     */
    @Column(name = "method_sort")
    private Integer methodSort;

    /**
     * 方法参数
     */
    @Column(name = "method_parameter_str")
    private String methodParameterStr;

    /**
     * 全路径接口类名称
     */
    @Column(name = "interface_full_class_name")
    private String interfaceFullClassName;

    /**
     * zcat数据源metricKey字段
     */
    @Column(name = "zcat_metric_key")
    private String zcatMetricKey;

}
