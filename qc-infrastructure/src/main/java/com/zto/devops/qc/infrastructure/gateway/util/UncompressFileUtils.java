package com.zto.devops.qc.infrastructure.gateway.util;

import com.zto.devops.qc.domain.gateway.util.UncompressFileUtilService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.ArchiveException;
import org.apache.commons.compress.archivers.examples.Expander;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;


@Slf4j
@Service
public class UncompressFileUtils implements UncompressFileUtilService {

    private static final String CURRENT_PATH = "/tmp/";

    public String decompressTarGz(String sourceFilePath, String extractPath) throws Exception {
        File sourceFile = new File(sourceFilePath);
        TarArchiveInputStream fin =
                new TarArchiveInputStream(new GzipCompressorInputStream(new FileInputStream(sourceFile)));
        File extraceFolder = new File(extractPath);
        TarArchiveEntry entry;
        String fileName = "";
        try {
            while ((entry = fin.getNextTarEntry()) != null) {
                if (entry.isDirectory()) {
                    continue;
                }
                File curfile = new File(extraceFolder, entry.getName());
                fileName = curfile.getName();
                File parent = curfile.getParentFile();
                if (!parent.exists()) {
                    parent.mkdirs();
                }
                IOUtils.copy(fin, new FileOutputStream(curfile));
            }
        } catch (Exception e) {
            log.error("解压Class文件异常", e);
        }
        return fileName;
    }

    /**
     * 返回解压后的文件夹名字
     */
    public String extract(File file) throws IOException {
        Expander expander = new Expander();
        String destFileName = CURRENT_PATH + File.separator + "XMind" + System.currentTimeMillis();
        try {
            expander.expand(file, new File(destFileName));
        } catch (ArchiveException e) {
            throw new RuntimeException(e);
        }
        return destFileName;
    }
}