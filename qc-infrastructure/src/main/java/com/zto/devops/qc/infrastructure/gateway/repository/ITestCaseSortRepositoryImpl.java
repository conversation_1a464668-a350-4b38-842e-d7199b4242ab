package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.qc.client.model.testmanager.cases.entity.PlanCaseVO;
import com.zto.devops.qc.domain.gateway.repository.ITestCaseSortRepository;
import com.zto.devops.qc.infrastructure.dao.mapper.TestcaseSortMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class ITestCaseSortRepositoryImpl implements ITestCaseSortRepository {

    @Autowired
    private TestcaseSortMapper testcaseSortMapper;

    @Override
    public List<PlanCaseVO> selectSortedPlanCaseByCode(List<String> codeList) {
        return testcaseSortMapper.selectSortedPlanCaseByCode(codeList);
    }
}
