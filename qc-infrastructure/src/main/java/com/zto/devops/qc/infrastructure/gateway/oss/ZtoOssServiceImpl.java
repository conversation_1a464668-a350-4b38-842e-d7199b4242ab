package com.zto.devops.qc.infrastructure.gateway.oss;

import com.zto.devops.framework.client.enums.impexp.ExtEnum;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.infrastructure.util.AmazonS3PublishUtils;
import com.zto.devops.qc.infrastructure.util.AmazonS3Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/4/7 18:22
 */
@Slf4j
@Component
public class ZtoOssServiceImpl implements ZtoOssService {

    @Autowired
    private AmazonS3Utils amazonS3Utils;

    @Autowired
    private AmazonS3PublishUtils amazonS3PublishUtils;

    @Override
    public Boolean cleanObject(String bucketName, String objectName) {
        return amazonS3Utils.cleanObject(bucketName, objectName);
    }

    @Override
    public void downloadFile(String bucketName, String key, String localFilePath) throws IOException {
        amazonS3Utils.downloadFile(bucketName, key, localFilePath);
    }

    @Override
    public void downloadFile(String bucketName, String key, String localFilePath, String localFileName) throws IOException {
        amazonS3Utils.downloadFile(bucketName, key, localFilePath, localFileName);
    }

    @Override
    public String generatePreSignedUrl(String bucketName, String fileName) {
        return amazonS3Utils.generatePreSignedUrl(bucketName, fileName);
    }

    @Override
    public String generatePreSignedUrlGet(String bucketName, String fileName) {
        return amazonS3Utils.generatePreSignedUrlGet(bucketName, fileName);
    }

    @Override
    public void uploadObject(String localDirectory, String bucketName) throws FileNotFoundException {
        amazonS3Utils.uploadObject(localDirectory, bucketName);
    }

    @Override
    public void uploadObject(String localDirectory, String bucketName, String ossDirectory) throws FileNotFoundException {
        amazonS3Utils.uploadObject(localDirectory,bucketName,ossDirectory);
    }

    @Override
    public Boolean createObject(String bucketName, String storageObjectVoPath, String fileName, String filePath) {
        return amazonS3Utils.createObject(bucketName, storageObjectVoPath, fileName, filePath);
    }

    @Override
    public long getFileSize(String bucketName, String keyName) {
        return amazonS3Utils.getFileSize(bucketName, keyName);
    }

    @Override
    public List<String> getListObjectKey(String bucketName, String prefix) {
        return amazonS3Utils.getListObjectKey(bucketName, prefix);
    }

    @Override
    public boolean isExist(String bucketName, String objectName) {
        return amazonS3Utils.isExist(bucketName, objectName);
    }

    @Override
    public String getObjectTextWithRange(String bucketName, String fileName) throws IOException {
        return amazonS3Utils.getObjectTextWithRange(bucketName, fileName);
    }

    @Override
    public boolean copyObject(String bucketName, String filePath, String fileName, String newFileName) {
        return amazonS3Utils.copyObject(bucketName, filePath, fileName, newFileName);
    }

    @Override
    public Map<String, String> downloadFileToZip(String bucketName, List<Map<String, String>> fileList, String fileZipName, List<String> csvJar, List<String> thirdJar, List<String> extendJar) throws IOException {
        return amazonS3Utils.downloadFileToZip(bucketName, fileList, fileZipName, csvJar, thirdJar, extendJar);
    }

    @Override
    public boolean createObject(String bucketName, String storageObjectVoFileName, String content) {
        return amazonS3Utils.createObject(bucketName, storageObjectVoFileName, content);
    }

    @Override
    public boolean copyObject(String bucketName, String oldPath, String newPath) {
        return amazonS3Utils.copyObject(bucketName, oldPath, newPath);
    }

    @Override
    public String getObjectText(String bucketName, String fileName) throws IOException {
        return amazonS3Utils.getObjectText(bucketName, fileName);
    }

    @Override
    public String uploadFile(String bucketName, InputStream inputStream, ExtEnum ext, String fileName) {
        return amazonS3Utils.uploadFile(bucketName, inputStream, ext, fileName);
    }

    @Override
    public String uploadFile(String data, ExtEnum ext, String fileName) {
        return amazonS3PublishUtils.upload(data, ext, fileName);
    }
}
