package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanCaseVO;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticSourceRecordEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface AutomaticSourceRecordMapper extends Mapper<AutomaticSourceRecordEntity> {
    List<AutomaticSourceRecordEntity> selectAutomaticSourceStatus(String productCode);

    List<AutomaticSourceRecordEntity> selectAnalysisAutomaticRecordAbort(@Param("recordAbortSecond") int recordAbortSecond, @Param("typeList")List<AutomaticStatusEnum> typeList);

    /**
     * 查询登记库code
     *
     * @param testcaseCode 所属目录code
     * @return 登记库code
     */
    List<String> selectByTestcaseCode(@Param("testcaseCode") String testcaseCode);



    /**
     * 批量查询登记库code
     *
     * @param testcaseCodeList 所属目录code集合
     * @return 登记库code
     */
    List<String> selectByTestcaseCodeList(@Param("testcaseCodeList") List<String> testcaseCodeList);


    /**
     * 查询登记库
     *
     * @param productCode  产品code
     * @param testcaseCode 所属目录code
     * @param typeList     类型
     * @return {@link TestPlanCaseVO}
     */
    List<TestPlanCaseVO> selectWithTestPlanCaseVO(@Param("productCode") String productCode,
                                                  @Param("testcaseCode") String testcaseCode,
                                                  @Param("typeList") List<AutomaticRecordTypeEnum> typeList);

    List<String> selectRelatedPlanName(@Param("automaticSourceCode") String automaticSourceCode);

    List<String> selectRelatedTaskName(@Param("automaticSourceCode") String automaticSourceCode);
}
