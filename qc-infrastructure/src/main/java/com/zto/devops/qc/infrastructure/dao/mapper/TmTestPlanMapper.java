package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.framework.infrastructure.dao.BaseMapper;
import com.zto.devops.qc.client.model.testmanager.plan.entity.PageTestPlanBaseVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.query.PlanListQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.VersionPlanQuery;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TmTestPlanMapper extends BaseMapper<TmTestPlanEntity> {


    List<TmTestPlanVO> selectTestPlanList(PlanListQuery query);

    TmTestPlanEntity selectByCode(@Param("planCode") String planCode);

    TmTestPlanEntity selectByCD(@Param("productCode") String productCode,@Param("versionCode") String versionCode,@Param("type")String type);

    List<PageTestPlanBaseVO> selectPageTestPlan(TmTestPlanEntity entity);

    List<TmTestPlanVO> selectTestPlanByCaseCode(@Param("caseCode")String caseCode);

    List<TmTestPlanEntity> selectVersionPlanList(VersionPlanQuery query);
}
