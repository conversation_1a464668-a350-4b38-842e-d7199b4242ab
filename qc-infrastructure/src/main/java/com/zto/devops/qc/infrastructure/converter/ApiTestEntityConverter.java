package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.*;
import com.zto.devops.qc.client.model.testmanager.apitest.event.*;
import com.zto.devops.qc.infrastructure.dao.entity.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface ApiTestEntityConverter {

    ApiTestVariableVO converter(ApiTestVariableEntity entity);

    @Mapping(target = "creatorId", source = "transactor.userId")
    @Mapping(target = "creator", source = "transactor.userName")
    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "gmtModified", source = "occurred")
    ApiTestVariableEntity converter(AddApiTestVariableEvent event);

    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "gmtModified", source = "occurred")
    ApiTestVariableEntity converter(EditApiTestVariableEvent event);

    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "gmtModified", source = "occurred")
    ApiTestVariableEntity converter(UpdateApiTestVariableStatusEvent event);

    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "gmtModified", source = "occurred")
    ApiTestVariableEntity converter(DeleteApiTestVariableEvent event);


    @Mapping(target = "subVariableType", expression = "java(com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum.getValue())")
    List<ApiTestVariableVO> converterToList(List<ApiTestVariableEntity> entityList);

    ApiTestEntityDO convertApiTestEntityDO(ApiTestEntity entity);

    List<ApiTestEntityDO> convertApiTestEntityDO(List<ApiTestEntity> list);

    ApiTestEntity convertApiTestEntity(ApiTestEntityDO entityDO);

    List<ApiTestEntity> convertApiTestEntity(List<ApiTestEntityDO> list);

    ApiCaseEntity convertApiCaseEntity(ApiCaseEntityDO entityDO);

    List<ApiCaseEntity> convertApiCaseEntity(List<ApiCaseEntityDO> list);

    ApiCaseEntityDO convertApiCaseEntityDO(ApiCaseEntity entity);

    List<ApiCaseEntityDO> convertApiCaseEntityDO(List<ApiCaseEntity> list);

    @Mapping(target = "apiCode", source = "mainApiCode")
    @Mapping(target = "tagName", source = "tagValue")
    @Mapping(target = "sceneRelated", expression = "java(entity.getRelatedSceneFlag() == 1)")
    ApiVO convertApiVO(ApiTestEntity entity);

    List<ApiVO> convertApiVO(List<ApiTestEntity> entityList);

    List<ApiLiteInfoVO> convertLite(List<ApiTestEntity> entityList);

    SceneInfoEntity convertSceneInfoEntity(SceneInfoEntityDO entityDO);

    SceneIndexEntity convertSceneIndexEntity(SceneIndexEntityDO entityDO);

    SceneInfoEntityDO convertSceneInfoEntityDO(SceneInfoEntity entity);

    List<SceneInfoEntityDO> converterToSceneInfoEntityList(List<SceneInfoEntity> entityList);

    List<SceneIndexEntity> converterVOList(List<SceneIndexVO> list);

    SceneIndexEntity converterVO(SceneIndexVO vo);

    SceneIndexVO converter(SceneIndexEntity entity);

    List<SceneIndexVO> converterList(List<SceneIndexEntity> list);

    List<ApiTestVariableVO> converter(List<ApiTestVariableEntity> list);

    List<ApiTestVariableEntity> convertVOList(List<ApiTestVariableVO> entityList);

    @Mapping(target = "variableStatus", expression = "java(java.lang.Boolean.valueOf(vo.getVariableStatus()))")
    ApiTestVariableEntity convert(ApiTestVariableVO vo);

    SceneApiRelationEntity convert(SceneApiRelationEntityDO entityDO);

    SceneDatabaseAuthorizeEntity convert(SceneDatabaseAuthorizeEntityDO entityDO);

    List<SceneDatabaseAuthorizeEntityDO> convertList(List<SceneDatabaseAuthorizeEntity> entityList);

    @Mapping(target = "creatorId", source = "transactor.userId")
    @Mapping(target = "creator", source = "transactor.userName")
    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "gmtModified", source = "occurred")
    ApiTestCaseEntity converter(AddApiTestCaseEvent event);

    List<TmApiTestCaseVO> converterToTmApiTestCaseVOList(List<ApiTestCaseEntity> list);

    TmApiTestCaseVO converterToTmApiTestCaseVO(ApiTestCaseEntity entity);

    ApiTestCaseEntity converterEventToVo(EditApiTestCaseEvent event);

    ApiTestCaseEntityDO convertApiTestCaseEntityDO(ApiTestCaseEntity entity);

    List<ApiTestCaseEntityDO> convertApiTestCaseEntityDO(List<ApiTestCaseEntity> list);

    @Mapping(target = "apiName", source = "relatedApiName")
    @Mapping(target = "apiAddress", source = "relatedApiAddress")
    @Mapping(target = "caseTypeNum", source = "caseType")
    @Mapping(target = "taskCode", source = "latestTaskId")
    @Mapping(target = "executeTime", source = "latestExecuteTime")
    @Mapping(target = "testResult", source = "latestExecuteResult")
    @Mapping(target = "key", source = "generateFieldSource")
    @Mapping(target = "apiConfigTypeEnum", source = "generateRules")
    PageApiTestCaseVO convertPageApiTestCaseVO(ApiTestCaseEntity entity);

    List<PageApiTestCaseVO> convertPageApiTestCaseVOList(List<ApiTestCaseEntity> list);

    ApiTestCaseEntity convertApiTestCaseEntity(ApiTestCaseEntityDO entityDO);

    List<ApiTestCaseEntity> convertApiTestCaseEntity(List<ApiTestCaseEntityDO> list);

    List<ApiCaseExceptionVO> convertApiTestCaseVO(List<ApiTestCaseEntity> entityList);

    List<SceneApiRelationEntityDO> convert2EntityDO(List<SceneApiRelationEntity> entityDO);

    @Mapping(target = "apiCode", source = "mainApiCode")
    ApiSampleCaseVO convertApiSampleCaseVO(ApiTestEntity entity);

    List<ApiSampleCaseVO> convertApiSampleCaseVO(List<ApiTestEntity> entityList);
}
