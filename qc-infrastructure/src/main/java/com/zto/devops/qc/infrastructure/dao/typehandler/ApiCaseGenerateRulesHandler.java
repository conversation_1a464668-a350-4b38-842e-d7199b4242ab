package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiConfigTypeEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedJdbcTypes(value = JdbcType.INTEGER, includeNullJdbcType = true)
public class ApiCaseGenerateRulesHandler extends BaseTypeHandler<ApiConfigTypeEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, ApiConfigTypeEnum apiConfigTypeEnum, JdbcType jdbcType) throws SQLException {
        if (null == apiConfigTypeEnum) {
            preparedStatement.setInt(i, -1);
        } else {
            preparedStatement.setInt(i, apiConfigTypeEnum.getCode());
        }
    }

    @Override
    public ApiConfigTypeEnum getNullableResult(ResultSet resultSet, String s) throws SQLException {
        int result = resultSet.getInt(s);
        return ApiConfigTypeEnum.codeOf(result);
    }

    @Override
    public ApiConfigTypeEnum getNullableResult(ResultSet resultSet, int i) throws SQLException {
        int result = resultSet.getInt(i);
        return ApiConfigTypeEnum.codeOf(result);
    }

    @Override
    public ApiConfigTypeEnum getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        int result = callableStatement.getInt(i);
        return ApiConfigTypeEnum.codeOf(result);
    }
}
