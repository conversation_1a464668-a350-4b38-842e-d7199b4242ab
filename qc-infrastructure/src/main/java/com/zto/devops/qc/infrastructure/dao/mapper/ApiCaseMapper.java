package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiCaseChildVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiCaseVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.StatApiCaseExecuteResultVO;
import com.zto.devops.qc.client.model.testmanager.apitest.query.PageApiCaseQuery;
import com.zto.devops.qc.client.service.testmanager.apitest.model.PageApiCaseChildrenReq;
import com.zto.devops.qc.infrastructure.dao.entity.ApiCaseEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ApiCaseMapper extends Mapper<ApiCaseEntity> {

    List<ApiCaseVO> selectApiCaseList(PageApiCaseQuery query);

    List<ApiCaseChildVO> selectApiCaseChildren(PageApiCaseChildrenReq req);

    /**
     * 根据api接口code 或 父用例code，统计可执行用例个数。
     *
     * @param apiCodeList 接口code列表
     * @param productCode 产品code
     * @return {@link Integer}
     */
    Integer countApiTestCaseForExecute(@Param("list") List<String> apiCodeList, @Param("productCode") String productCode);

    /**
     * 根据父用例code 和 任务code 查执行结果
     *
     * @param parentCode 父用例code
     * @param taskCode   任务code
     * @return 执行结果
     */
    String selectExecuteResultByParentCodeAndTaskCode(@Param("parentCode") String parentCode,
                                                      @Param("taskCode") String taskCode);

    /**
     * 统计父用例执行结果
     *
     * @param taskCode 任务code
     * @return {@link StatApiCaseExecuteResultVO}
     */
    List<StatApiCaseExecuteResultVO> statApiCaseExecuteResult(@Param("taskCode") String taskCode);

    int deleteByProductCode(@Param("productCode") String productCode);
}
