package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.TestFunctionPointEntityDO;
import com.zto.devops.qc.infrastructure.dao.entity.TestFunctionPointEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TestFunctionPointEntityConverter {

    List<TestFunctionPointEntityDO> convert2DOList(List<TestFunctionPointEntity> entityList);
}
