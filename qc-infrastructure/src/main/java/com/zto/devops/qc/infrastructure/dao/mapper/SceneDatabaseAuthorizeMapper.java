package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.qc.infrastructure.dao.entity.SceneDatabaseAuthorizeEntity;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface SceneDatabaseAuthorizeMapper extends Mapper<SceneDatabaseAuthorizeEntity> {


    List<SceneDatabaseAuthorizeEntity> queryAuthorizeList(@Param("productCode") String productCode);

}
