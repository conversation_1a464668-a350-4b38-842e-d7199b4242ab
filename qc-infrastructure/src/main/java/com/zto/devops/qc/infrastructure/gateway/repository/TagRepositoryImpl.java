package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.client.enums.issue.TagTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneTagEnum;
import com.zto.devops.qc.client.model.dto.TagEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.issue.event.TagAddedEvent;
import com.zto.devops.qc.client.model.issue.event.TagRemovedSimpleEvent;
import com.zto.devops.qc.domain.gateway.repository.TagRepository;
import com.zto.devops.qc.infrastructure.converter.TagVOConverter;
import com.zto.devops.qc.infrastructure.dao.entity.TagEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TagMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
public class TagRepositoryImpl implements TagRepository {

    @Autowired
    private TagMapper tagMapper;

    @Autowired
    private TagVOConverter tagVOConverter;

    @Override
    public List<TagVO> listTagsByBusinessCode(String businessCode) {
        Example example = new Example(TagEntity.class);
        example.orderBy("gmtCreate").asc();
        example
                .createCriteria()
                .andEqualTo("businessCode", businessCode)
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("type", TagTypeEnum.BUSINESS);
        List<TagEntity> entityList = tagMapper.selectByExample(example);
        return tagVOConverter.convert(entityList);
    }

    @Override
    public void addTag(TagAddedEvent event) {
        List<TagVO> tagVOS = event.getTags();
        List<TagEntity> entityList = tagVOConverter.convert(tagVOS);
        for (TagEntity entity : entityList) {
            entity.preCreate(event);
            tagMapper.insertSelective(entity);
        }
    }

    @Override
    public TagVO findTagByCode(String code) {
        Example example = new Example(TagEntity.class);
        example.createCriteria().andEqualTo("code", code);
        return tagVOConverter.convert(tagMapper.selectOneByExample(example));
    }

    @Override
    public void removeTag(TagRemovedSimpleEvent event) {
        TagEntity entity = new TagEntity();
        entity.setCode(event.getCode());
        entity.setEnable(Boolean.FALSE);
        tagMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public List<TagVO> getTagsByCodeList(List<String> codeList) {
        if (CollectionUtil.isEmpty(codeList)) {
            return Collections.emptyList();
        }
        Example example = new Example(TagEntity.class);
        example.createCriteria().andEqualTo("enable", Boolean.TRUE).andIn("businessCode", codeList);
        List<TagEntity> tagList = tagMapper.selectByExample(example);
        return tagVOConverter.convert(tagList);
    }

    @Override
    public void addTagList(List<TagVO> tags, String businessCode, BaseEvent event) {
        if (CollectionUtil.isEmpty(tags)) {
            return;
        }
        List<TagEntity> entityList = tagVOConverter.convert(tags);
        for (TagEntity tag : entityList) {
            tag.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
            tag.preCreate(event);
            tag.setBusinessCode(businessCode);
            tag.setDomain(DomainEnum.TESTCASE);
            tag.setType(TagTypeEnum.BUSINESS);
            tagMapper.insertSelective(tag);
        }
    }

    @Override
    public TagVO findTestcaseTagQuery(String code) {
        TagEntity entity = tagMapper.selectByPrimaryKey(code);
        return tagVOConverter.convert(entity);
    }

    @Override
    public Long tagTestcaseNo(String tagName, String domain, String productCode) {
        return tagMapper.tagTestcaseNo(tagName, domain, productCode);
    }

    @Override
    public List<String> findCodesByNameAndProCode(String tagName, String businessCode) {
        return tagMapper.tagTestcaseCodeList(tagName, DomainEnum.TESTCASE.name(), businessCode);
    }

    @Override
    public Integer saveBatch(List<TagEntityDO> list) {
        return tagMapper.saveBatch(tagVOConverter.convertTagEntity(list));
    }

    @Override
    public void deleteTestcaseTag(List<String> codeList) {
        tagMapper.deleteByCodes(codeList);
    }

    @Override
    public List<TagVO> queryBusinessTagWithFixVersionCode(String versionCode) {
        return tagVOConverter.convert(tagMapper.queryBusinessTagWithFixVersionCode(versionCode));
    }

    @Override
    public void saveSceneTag(String sceneCode, SceneTagEnum sceneTag) {
        TagEntity tag = new TagEntity();
        tag.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
        tag.setDomain(DomainEnum.SCENE);
        tag.setBusinessCode(sceneCode);
        tag.setTagName(sceneTag.name());
        tag.setTagAlias(sceneTag.getAlias());
        tag.setType(TagTypeEnum.BUSINESS);
        tag.preCreate(new User(0L, "系统"));
        tagMapper.insertSceneTag(tag);
    }

    @Override
    public void removeSceneTag(String sceneCode, List<SceneTagEnum> sceneTags) {
        if (CollectionUtil.isEmpty(sceneTags)) {
            return;
        }
        sceneTags.forEach(sceneTag -> {
            Example example = new Example(TagEntity.class);
            example.createCriteria()
                    .andEqualTo("domain", DomainEnum.SCENE)
                    .andEqualTo("type", TagTypeEnum.BUSINESS)
                    .andEqualTo("businessCode", sceneCode)
                    .andEqualTo("tagName", sceneTag.name())
                    .andEqualTo("tagAlias", sceneTag.getAlias());
            tagMapper.deleteByExample(example);
        });
    }

    @Override
    public List<TagVO> listTagsByBusinessCodeAndDomain(String businessCode, DomainEnum domain) {
        Example example = new Example(TagEntity.class);
        example.orderBy("gmtCreate").asc();
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("businessCode", businessCode)
                .andEqualTo("enable", Boolean.TRUE)
                .andEqualTo("type", TagTypeEnum.BUSINESS);
        if (null != domain) {
            criteria.andEqualTo("domain", domain);
        }
        List<TagEntity> entityList = tagMapper.selectByExample(example);
        return tagVOConverter.convert(entityList);
    }

    @Override
    public List<TagVO> selectByIssueCodeList(List<String> issueCodeList) {
        Example example = new Example(TagEntity.class);
        example.orderBy("businessCode");
        example.selectProperties("code", "tagName", "businessCode");
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("businessCode", issueCodeList)
                .andEqualTo("enable", Boolean.TRUE);
        List<TagEntity> entityList = tagMapper.selectByExample(example);
        return tagVOConverter.convert(entityList);
    }

    @Override
    public List<String> findBusinessCodeByTag(SceneTagEnum sceneTag) {
        Example example = new Example(TagEntity.class);
        example.createCriteria()
                .andEqualTo("tagName", sceneTag.name())
                .andEqualTo("type", TagTypeEnum.BUSINESS)
                .andEqualTo("enable", Boolean.TRUE);
        example.selectProperties("businessCode").setDistinct(true);
        return Optional.ofNullable(tagMapper.selectByExample(example))
                .orElse(Collections.emptyList())
                .stream().map(TagEntity::getBusinessCode)
                .collect(Collectors.toList());
    }
}
