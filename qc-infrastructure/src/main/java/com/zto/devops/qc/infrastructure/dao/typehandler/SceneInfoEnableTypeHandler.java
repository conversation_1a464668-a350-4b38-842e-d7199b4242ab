package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoEnableEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedJdbcTypes(value = JdbcType.INTEGER, includeNullJdbcType = true)
public class SceneInfoEnableTypeHandler extends BaseTypeHandler<SceneInfoEnableEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, SceneInfoEnableEnum sceneInfoEnableEnum, JdbcType jdbcType) throws SQLException {
        if (null == sceneInfoEnableEnum) {
            preparedStatement.setInt(i, 1);
        } else {
            preparedStatement.setInt(i, sceneInfoEnableEnum.getCode());
        }
    }

    @Override
    public SceneInfoEnableEnum getNullableResult(ResultSet resultSet, String s) throws SQLException {
        int result = resultSet.getInt(s);
        return SceneInfoEnableEnum.codeOf(result);
    }

    @Override
    public SceneInfoEnableEnum getNullableResult(ResultSet resultSet, int i) throws SQLException {
        int result = resultSet.getInt(i);
        return SceneInfoEnableEnum.codeOf(result);
    }

    @Override
    public SceneInfoEnableEnum getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        int result = callableStatement.getInt(i);
        return SceneInfoEnableEnum.codeOf(result);
    }
}
