package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanEntity;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;


@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface TmTestPlanNullIgnoreConvertor {

    TmTestPlanNullIgnoreConvertor INSTANCE = Mappers.getMapper(TmTestPlanNullIgnoreConvertor.class);

    void updateTmTestPlanEntity(TmTestPlanVO vo, @MappingTarget TmTestPlanEntity entity);
}
