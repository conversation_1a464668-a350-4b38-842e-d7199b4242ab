package com.zto.devops.qc.infrastructure.gateway.rpc;


import com.alibaba.dubbo.config.annotation.Reference;
import com.zto.devops.product.client.service.productGroup.IProductGroupService;
import com.zto.devops.qc.domain.gateway.rpc.IProductGroupRpcService;
import org.springframework.stereotype.Component;

@Component
public class IProductGroupRpcServiceImpl implements IProductGroupRpcService {

    @Reference
    private IProductGroupService productGroupService;

    @Override
    public String getGroupIdByProductCode(String productCode) {
        return productGroupService.getGroupIdByProductCode(productCode);
    }

    @Override
    public String getProductCodeByGroupId(String groupId) {
        return productGroupService.getProductCodeByGroupId(groupId);
    }
}
