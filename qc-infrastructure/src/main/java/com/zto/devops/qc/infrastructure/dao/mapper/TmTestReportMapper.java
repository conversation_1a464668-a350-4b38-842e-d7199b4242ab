package com.zto.devops.qc.infrastructure.dao.mapper;

import com.zto.devops.framework.infrastructure.dao.BaseMapper;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.model.report.entity.BaseReportInfoVO;
import com.zto.devops.qc.client.model.report.entity.RelatedBaseVO;
import com.zto.devops.qc.client.model.report.entity.ReportTestResultMsgVO;
import com.zto.devops.qc.client.model.report.entity.ReportVO;
import com.zto.devops.qc.client.model.report.query.RelatedQuery;
import com.zto.devops.qc.client.model.testmanager.report.query.PageReportMqQuery;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestReportEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TmTestReportMapper extends BaseMapper<TmTestReportEntity> {

    List<ReportVO> pageQuery(PageReportMqQuery query);

    List<ReportVO> versionReportQuery(@Param("versionCode") String versionCode);

    List<ReportVO> selectTestPlanMainByProductCode(String productCode);

    List<ReportVO> selectByVersionCode(String versionCode);

    List<RelatedBaseVO> getSimpleReportVOByVersionCode(@Param("versionCode") String versionCode);

    List<String> queryVersionCodesByProductCode(@Param("productCode") String productCode);

    BaseReportInfoVO selectBaseInfoVOByCode(@Param("code") String code);

    List<RelatedBaseVO> getSimpleReportVOByQuery(RelatedQuery query);

    TmTestReportEntity getPlanCodeAndTypeOne(@Param("planCode") String planCode, @Param("reportType") ReportType reportType);

    TmTestReportEntity getVersionCodeAndTypeOne(@Param("versionCode") String versionCode, @Param("reportType") ReportType reportType);

    List<ReportTestResultMsgVO> getTestReportByResult(@Param("updateTestResultDate") Date updateTestResultDate);

    TmTestReportEntity getByProductCodeAndUser(@Param("productCode") String productCode, @Param("reportUserId") Long reportUserId);

    /**
     * 根据版本号和报告类型，筛选已经发过邮件的报告列表
     *
     * @param versionCodeList 版本号集合
     * @param typeList        报告类型集合
     * @return {@link TmTestReportEntity}
     */
    List<TmTestReportEntity> findSentEmailList(@Param("versionCodeList") List<String> versionCodeList,
                                               @Param("typeList") List<ReportType> typeList);
}