package com.zto.devops.qc.infrastructure.gateway.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneRunningSetEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableUsageTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.LinkMapTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.constants.AutomaticConstant;
import com.zto.devops.qc.client.model.dto.ApiTestEntityDO;
import com.zto.devops.qc.client.model.dto.JmeterDocumentFullInfo;
import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestVariableVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ProxyExecDTO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskInfo;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.Scene;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component.*;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.lines.Line;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.metadata.APISecurityMeta;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.metadata.GatewayApiSettingVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticEleAndHashTree;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticNode;
import com.zto.devops.qc.client.service.testmanager.apitest.model.QueryLatestEditSceneInfoReq;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.metadata.MetaDataService;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.ApiTestRepository;
import com.zto.devops.qc.domain.gateway.util.JmxUtil;
import com.zto.devops.qc.domain.gateway.zbase.ZbaseService;
import com.zto.devops.qc.domain.util.SqlValidationUtils;
import com.zto.devops.qc.infrastructure.config.ZbaseConfig;
import com.zto.devops.qc.infrastructure.converter.ApiTestEntityConverter;
import com.zto.devops.qc.infrastructure.converter.SceneLinkInfoEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.SceneInfoEntity;
import com.zto.devops.qc.infrastructure.dao.entity.SceneLinkInfoEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.SceneInfoMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.SceneLinkInfoMapper;
import com.zto.devops.qc.infrastructure.util.AmazonS3Utils;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.dom4j.*;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.dom4j.tree.DefaultElement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.xml.sax.SAXException;
import tk.mybatis.mapper.entity.Example;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.StringWriter;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类 名 称：JmxUtil
 * 类 描 述：TODO
 * 创建时间：2022/10/12 1:26 下午
 * 创 建 人：zengyixun
 */
@Slf4j
@Component
public class JmxUtilImpl implements JmxUtil {
    public static final List<String> samplerList = Arrays.asList(AutomaticNodeTypeEnum.HTTPSamplerProxy.getDesc(), AutomaticNodeTypeEnum.BeanShellSampler.getDesc(), AutomaticNodeTypeEnum.JDBCSampler.getDesc(), AutomaticNodeTypeEnum.JavaSampler.getDesc(), AutomaticConstant.DubboSample, AutomaticConstant.DubboSampler, AutomaticConstant.ZMSSampler, AutomaticNodeTypeEnum.DataCenterSampler.getDesc());
//    public static final List<String> controllerList = Arrays.asList(AutomaticNodeTypeEnum.SwitchController.getDesc(), AutomaticNodeTypeEnum.CriticalSectionController.getDesc(), AutomaticNodeTypeEnum.LoopController.getDesc(), AutomaticNodeTypeEnum.SimpleController.getDesc(), AutomaticNodeTypeEnum.IfController.getDesc(), AutomaticNodeTypeEnum.OnceOnlyController.getDesc(), AutomaticNodeTypeEnum.TransactionController.getDesc());
    public static String publicTfName = "ztoPublicTestFragmentController";
//    public static String uniqueNodes="ModuleController;HTTPSamplerProxy;DebugSampler;JSR223Sampler;AjpSampler;AccessLogSampler;BeanShellSampler;BoltSampler;io.github.ningyu.jmeter.plugin.dubbo.sample.DubboSample;FTPSampler;JDBCSampler;JMSSampler;PublisherSampler;SubscriberSampler;JUnitSampler;JavaSampler;LDAPExtSampler;LDAPSampler;MailReaderSampler;SystemSampler;SmtpSampler;TCPSampler;com.zto.zmsjmeter.ZMSSampler;DubboSampler;ConfigTestElement;RocketMQSampler";


    @Autowired
    private AmazonS3Utils amazonS3Utils;
    @Autowired
    private QcConfigBasicService config;
    @Autowired
    private ZbaseConfig zbaseConfig;
    @Autowired
    private SceneLinkInfoMapper sceneLinkInfoMapper;
    @Autowired
    private SceneLinkInfoEntityConverter sceneLinkInfoEntityConverter;
    @Autowired
    private ZtoOssService ztoOssService;
    @Autowired
    private QcConfigBasicService qcConfigBasicService;
    @Autowired
    private ApiTestEntityConverter apiTestEntityConverter;
    @Autowired
    private SceneInfoMapper sceneInfoMapper;
    @Autowired
    private ApiTestRepository apiTestRepository;
    @Autowired
    private ZbaseService zbaseService;
    @Autowired
    private ShellParseUtil shellParseUtil;
    @Autowired
    private RedisService redisService;
    @Autowired
    private MetaDataService metaDataService;
    private List<String> controllerList = null;

    /**
     * 从一个jmx文本取得Document
     *
     * @param jmxContent
     * @return
     * @throws DocumentException
     */
//    public Document GetJmeterDocument(String jmxContent) throws DocumentException {
//        Document document = null;
//
//        SAXReader reader = new SAXReader();
//
//        try {
//            reader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
//        } catch (SAXException e) {
//            // On Apache, this should be thrown when disallowing DOCTYPE
//            log.error("A DOCTYPE was passed into the XML document", "文件错误2");
//        }
//
//        String encoding = getEncoding(jmxContent);
//
//        InputSource source = new InputSource(new StringReader(jmxContent));
//        source.setEncoding(encoding);
//
//        document = reader.read(source);
//        if (document.getXMLEncoding() == null) {
//            document.setXMLEncoding(encoding);
//
//        }
//
//        return document;
//    }

    /**
     * 以线程组为单元进行重命名*
     * @param document
     */
    private void rename(Document document){
        this.controllerList = config.getJmeterConfig().getControllerList();
        Element root = document.getRootElement();
        Map<String,List<String>> threadTcNames = new HashMap<>();
        Map<String,Element> threadElement = new HashMap<>();
//        Map<String,List<String>> testFragmentTcNames = new HashMap<>();
        Map<String,Element> testFragmentElement = new HashMap<>();
        AutomaticEleAndHashTree eleAndHashTreeTP = GetJmeterPlanEleAndHashTree(root);

        AutomaticEleAndHashTree eleAndHashTreePublicTF = null;
        if(eleAndHashTreePublicTF==null){
            Map<String,String> attrib = new HashMap<>();
            attrib.put("guiclass","TestFragmentControllerGui");
            attrib.put("testclass","TestFragmentController");
            attrib.put("testname",publicTfName);
            attrib.put("enabled","true");
            Element pTfEle = newEle(eleAndHashTreeTP.getHashTree(),"TestFragmentController",attrib);
            Element ptHashTree = newEle(eleAndHashTreeTP.getHashTree(),"hashTree");
            eleAndHashTreePublicTF = new AutomaticEleAndHashTree();
            eleAndHashTreePublicTF.setMainEle(pTfEle);
            eleAndHashTreePublicTF.setHashTree(ptHashTree);
        }

        Element mainEle = null;
        /**
         * 针对线程组为单元对下面的特定组件进行重命名*
         */
        for (Element elementT : eleAndHashTreeTP.getHashTree().elements()) {
            if (elementT != null && elementT.getName().equals(AutomaticNodeTypeEnum.ThreadGroup.getDesc()) && mainEle == null) {
                mainEle = elementT;
                String threadName = mainEle.attributeValue("testname").trim();
                int nameCount = 0;
                while (threadElement.containsKey(threadName)){
                    nameCount = nameCount + 1;
                    threadName = threadName+String.valueOf(nameCount);
                }
                mainEle.attribute("testname").setValue(threadName);

            }
            if (elementT != null && elementT.getName().equals(AutomaticConstant.TestFragmentController) && mainEle == null) {
                mainEle = elementT;
            }
            if (elementT != null && elementT.getName().equals("hashTree") && mainEle != null) {
                if (mainEle.getName().equals(AutomaticConstant.TestFragmentController)) {
                    if(testFragmentElement.containsKey(mainEle.attributeValue(AutomaticConstant.JMX_TESTNAME))){
                        throw new ServiceException(mainEle.attributeValue(AutomaticConstant.JMX_TESTNAME) + ":测试片段不能重名!");
                    }
                    List<Node> mNodes = elementT.selectNodes(".//ModuleController");
                    if (mNodes != null && mNodes.size() > 0) {
                        throw new ServiceException("测试片段["+mainEle.attributeValue(AutomaticConstant.JMX_TESTNAME) + "]下不允许包含模块控制器["+((Element)mNodes.get(0)).attributeValue(AutomaticConstant.JMX_TESTNAME)+"]");
                    }
                    mainEle.attribute("enabled").setValue("true");
                    testFragmentElement.put(mainEle.attributeValue(AutomaticConstant.JMX_TESTNAME),elementT);
                }
                if (mainEle.getName().equals(AutomaticNodeTypeEnum.ThreadGroup.getDesc())) {
                    threadElement.put(mainEle.attributeValue("testname"),elementT);
                    List<String> newTcNames = new ArrayList<>();
                    threadTcNames.put(mainEle.attributeValue("testname"),newTcNames);
//                    List<Node> mNodes = elementT.selectNodes(".//ModuleController");
                    List<String> uniqueNodes = config.getJmeterConfig().getUniqueNodeList();
                    for(String nodeTypeName : uniqueNodes){
                        List<Node> nodeTmp = elementT.selectNodes(".//"+nodeTypeName);
                        if(nodeTmp!=null && nodeTmp.size()>0){
                            for(Node node:nodeTmp){
                                String tcName = ((DefaultElement)node).attribute(AutomaticConstant.JMX_TESTNAME).getValue().trim();
                                if(!newTcNames.contains(tcName)) {
                                    newTcNames.add(tcName);
                                }else {
                                    int renameIndex = 1;
                                    String reTcName = tcName+AutomaticConstant.JMX_RENAME_PRE+renameIndex;
                                    while (newTcNames.contains(reTcName)){
                                        renameIndex = renameIndex + 1;
                                        reTcName = tcName+AutomaticConstant.JMX_RENAME_PRE+renameIndex;
                                    }

                                    ((DefaultElement)node).attribute(AutomaticConstant.JMX_TESTNAME).setValue(reTcName);
                                    newTcNames.add(reTcName);
                                }
                            }
                        }
                    }

                }
                mainEle = null;
            }
        }

        //针对各线程组下的模块控制器进行重建操作
        rebuildTestFragment(eleAndHashTreeTP,testFragmentElement,threadElement,threadTcNames,eleAndHashTreePublicTF);
    }

    private void rebuildTestFragment(AutomaticEleAndHashTree eleAndHashTreeTP,Map<String,Element> testFragmentElement,Map<String,Element> threadElement,Map<String,List<String>> threadTcNames,AutomaticEleAndHashTree eleAndHashTreePublicTF){

        for(String threadName:threadElement.keySet()){
            Element elementT = threadElement.get(threadName);
            List<Node> nodes = elementT.selectNodes(".//ModuleController");
            if(nodes==null || nodes.size()<=0){
                continue;
            }
            //定位模块控制器所在测试片段
            for(Node node : nodes) {
                String moduleName = ((Element)node).attributeValue(AutomaticConstant.JMX_TESTNAME);
                Node mcNodePath = node.selectSingleNode("collectionProp[@name='ModuleController.node_path']");
                if(mcNodePath == null){
                    throw new ServiceException("请检查你的模块控制器["+moduleName+"]结构异常!");
                }
                List<Node> childNodes  = mcNodePath.selectNodes("stringProp");
                if(childNodes.size()<=2){
                    throw new ServiceException("请检查你的模块控制器["+moduleName+"]是否引用的是测试片段中的内容!");
                }
                Node currentNode = childNodes.get(2);
                String tfName = currentNode.getText();
                if(testFragmentElement==null || testFragmentElement.size()<=0){
                    throw new ServiceException("模块控制器只能引用测试片段中的内容！你的脚本中存在模块控制器，却不存在测试片段，请修改脚本！");
                }
                if(!testFragmentElement.containsKey(tfName)){
                    throw new ServiceException(tfName + ":测试片段不存在，请检查你的模块控制器["+moduleName+"]是否引用的是测试片段中的内容!");
                }

                setNewModuleTargetEle(eleAndHashTreeTP,(Element)node,testFragmentElement,eleAndHashTreePublicTF,threadName,threadTcNames);

            }
        }

    }

    private void setNewModuleTargetEle(AutomaticEleAndHashTree eleAndHashTreeTP,Element moduleControllerEle,Map<String,Element> testFragmentElement,AutomaticEleAndHashTree eleAndHashTreePublicTF,String threadName,Map<String,List<String>> threadTcNames){
        AutomaticEleAndHashTree newTargetEleAndHashTree = null;
        Node moduleControllerNode = moduleControllerEle.selectSingleNode("collectionProp[@name='ModuleController.node_path']");
        if(moduleControllerNode==null){
            return;
        }
        List<Node> nodes  = moduleControllerNode.selectNodes("stringProp");
        if(nodes.size()<=2){
            return;
        }
        Node currentNode = nodes.get(2);
        String tfName = currentNode.getText();
        if(!testFragmentElement.containsKey(tfName)){
            return;
        }

        String moduleName = moduleControllerEle.attributeValue(AutomaticConstant.JMX_TESTNAME);
        Element tfHashTreeElement = testFragmentElement.get(tfName);
        if(nodes.size()==3){
            Map<String,String> attribNew = new HashMap<>();
            attribNew.put("guiclass","TestFragmentControllerGui");
            attribNew.put("testclass","TestFragmentController");
            String currentNodeReName = tfName+"_"+threadName+"_"+moduleName;
            attribNew.put(AutomaticConstant.JMX_TESTNAME,currentNodeReName);
            attribNew.put("enabled","true");
            newEle(eleAndHashTreeTP.getHashTree(),"TestFragmentController",attribNew);
            Element newTfHashTree = (Element)tfHashTreeElement.clone();
            eleAndHashTreeTP.getHashTree().add(newTfHashTree);
//            AutomaticEleAndHashTree sourceTF = new AutomaticEleAndHashTree();
//            sourceTF.setMainEle(pTfEle);
//            sourceTF.setHashTree(ptHashTree);

            //对新的newTfHashTree中的用例重名问题进行重建
            List<String> uniqueNodes = config.getJmeterConfig().getUniqueNodeList();
            for(String nodeTypeName : uniqueNodes){
                List<Node> nodeTmp = newTfHashTree.selectNodes(".//"+nodeTypeName);
                if(nodeTmp!=null && nodeTmp.size()>0){
                    for(Node nodeTfHashTree:nodeTmp){
                        String tcName = ((DefaultElement)nodeTfHashTree).attribute(AutomaticConstant.JMX_TESTNAME).getValue().trim();
                        if(!threadTcNames.get(threadName).contains(tcName)) {
                            threadTcNames.get(threadName).add(tcName);
                        }else {
                            int renameIndex = 1;
                            String reTcName = tcName+AutomaticConstant.JMX_RENAME_PRE+renameIndex;
                            while (threadTcNames.get(threadName).contains(reTcName)){
                                renameIndex = renameIndex + 1;
                                reTcName = tcName+AutomaticConstant.JMX_RENAME_PRE+renameIndex;
                            }
                            ((DefaultElement)nodeTfHashTree).attribute(AutomaticConstant.JMX_TESTNAME).setValue(reTcName);
                            threadTcNames.get(threadName).add(reTcName);
                        }
                    }
                }
            }


//            newTargetEleAndHashTree = new AutomaticEleAndHashTree();
//            newTargetEleAndHashTree.setMainEle(newTfMain);
//            newTargetEleAndHashTree.setHashTree(newTfHashTree);

//            String targetName = moduleName+"-"+newTargetEleAndHashTree.getMainEle().attributeValue(AutomaticConstant.JMX_TESTNAME);
//            newTargetEleAndHashTree.getMainEle().attribute(AutomaticConstant.JMX_TESTNAME).setValue(targetName);
//            modifyTcNameByElement(moduleName,newTargetEleAndHashTree.getHashTree());
//            eleAndHashTreeTP.getHashTree().add(newTargetEleAndHashTree.getMainEle());
//            eleAndHashTreeTP.getHashTree().add(newTargetEleAndHashTree.getHashTree());
            currentNode.setText(currentNodeReName);
        }

//        for(int i=3;i<nodes.size();i++){
//            currentNode = nodes.get(i);
//        }
        if(nodes.size()>3){
            Element currentLocation = tfHashTreeElement;
            List<AutomaticEleAndHashTree> eleAndHashTrees = null;
            for(int i=3;i<nodes.size();i++){
                Element elementCurrent = (Element) nodes.get(i);
//                List<Node> childNodes = tfHashTreeElement.selectNodes("."+elementCurrent.getText());
                eleAndHashTrees = searcheControllerByName(elementCurrent.getText(),currentLocation);

                if(eleAndHashTrees==null || eleAndHashTrees.size()<=0){
                    throw new ServiceException("线程组["+threadName+"]下的模块控制器"+"["+moduleName+"]引用的对象不存在!");
                }
                Iterator<AutomaticEleAndHashTree> it = eleAndHashTrees.iterator();
                while ( it.hasNext() ) {
                    AutomaticEleAndHashTree itNode = it.next();
                    String tclassname = (itNode.getMainEle()).attributeValue("testclass");
                    if (tclassname==null){
                        tclassname = "";
                    }
                    if(tclassname.indexOf("Controller")<=0){
                        it.remove();
                    }
                }

                if(eleAndHashTrees.size()>1){
                    throw new ServiceException("线程组["+threadName+"]下的模块控制器"+"["+moduleName+"]引用的对象在测试片段["+tfName+"]中名称重复了!");
                }
                currentLocation = eleAndHashTrees.get(0).getHashTree();
            }

            if(eleAndHashTrees!=null && currentLocation!=null && eleAndHashTrees!=tfHashTreeElement){
                //为这个找到的目标挂到公共测试片段中去，并重建其测试用例的名称
                AutomaticEleAndHashTree automaticEleAndHashTree = eleAndHashTrees.get(0);
                Element targetNewMain = (Element) automaticEleAndHashTree.getMainEle().clone();
                Element targetNewHashTree = (Element) automaticEleAndHashTree.getHashTree().clone();
                eleAndHashTreePublicTF.getHashTree().add(targetNewMain);
                eleAndHashTreePublicTF.getHashTree().add(targetNewHashTree);

                targetNewMain.attribute(AutomaticConstant.JMX_TESTNAME).setValue(threadName+"_"+moduleName);
                ((Element)nodes.get(2)).setText(eleAndHashTreePublicTF.getMainEle().attributeValue(AutomaticConstant.JMX_TESTNAME));
                ((Element)nodes.get(3)).setText(threadName+"_"+moduleName);
                if(nodes.size()>4){
                    for(int i=4;i<nodes.size();i++) {
                        Element delNode = (Element) nodes.get(i);
                        ((Element)moduleControllerNode).remove(delNode);
                    }
                }
                //重建名称
                List<String> uniqueNodes = config.getJmeterConfig().getUniqueNodeList();
                for(String nodeTypeName : uniqueNodes){
                    List<Node> nodeTmp = targetNewHashTree.selectNodes(".//"+nodeTypeName);
                    if(nodeTmp!=null && nodeTmp.size()>0){
                        for(Node node:nodeTmp){
                            String tcName = ((DefaultElement)node).attribute(AutomaticConstant.JMX_TESTNAME).getValue().trim();
                            if(!threadTcNames.get(threadName).contains(tcName)) {
                                threadTcNames.get(threadName).add(tcName);
                            }else {
                                int renameIndex = 1;
                                String reTcName = tcName+AutomaticConstant.JMX_RENAME_PRE+renameIndex;
                                while (threadTcNames.get(threadName).contains(reTcName)){
                                    renameIndex = renameIndex + 1;
                                    reTcName = tcName+AutomaticConstant.JMX_RENAME_PRE+renameIndex;
                                }
                                ((DefaultElement)node).attribute(AutomaticConstant.JMX_TESTNAME).setValue(reTcName);
                                threadTcNames.get(threadName).add(reTcName);
                            }
                        }
                    }
                }
            }

        }

    }

    private List<AutomaticEleAndHashTree> searcheControllerByName(String name,Element hashTreeEle){
        List<AutomaticEleAndHashTree> result = new ArrayList<>();

        List<Element> elementList = hashTreeEle.elements();

        Element mainEle = null;
        for(Element element : elementList){
////            if(controllerList.contains(element.getName())){
//            String testclass = element.attributeValue("testclass");
//            if(testclass==null){
//                testclass = "";
//            }
//            if(testclass.indexOf("Controller")>0){
            if(this.controllerList.contains(element.getName())){
                if(element.attributeValue(AutomaticConstant.JMX_TESTNAME).equals(name)){
                    mainEle = element;
                    continue;
                }
            }
            if(element.getName().equals("hashTree") && mainEle!=null){
                AutomaticEleAndHashTree eleAndHashTree = new AutomaticEleAndHashTree();
                eleAndHashTree.setMainEle(mainEle);
                eleAndHashTree.setHashTree(element);
                result.add(eleAndHashTree);
            }
            mainEle = null;
//            if(element.getName().equals("hashTree") && mainEle==null){
//                result = searcheControllerByName(name,element);
//            }
//            if(result!=null){
//                break;
//            }
        }
        return result;
    }

    private Element newEle(Element element,String nodeName, Map<String,String> attrib,String text){

        Element resultEle = element.addElement(nodeName);
        if(attrib!=null) {
            attrib.forEach((String key, String value) -> {
                resultEle.addAttribute(key, value);
            });
        }
        if(text!=null){
            resultEle.setText(text);
        }
        return resultEle;
    }
    private Element newEle(Element element,String nodeName, Map<String,String> attrib){

        return newEle(element,nodeName,attrib,null);

    }

    private Element newEle(Element element,String nodeName){

        return newEle(element,nodeName,null,null);

    }

//    private AutomaticEleAndHashTree searcheEleByName(String name,Element hashTreeEle){
//        AutomaticEleAndHashTree result = null;
//
//        List<Element> elementList = hashTreeEle.elements();
//
//        Element mainEle = null;
//        for(Element element : elementList){
//            if(controllerList.contains(element.getName())){
//                if(element.attributeValue(AutomaticConstant.JMX_TESTNAME).equals(name)){
//                    mainEle = element;
//                }
//            }
//            if(element.getName().equals("hashTree") && mainEle!=null){
//                result = new AutomaticEleAndHashTree();
//                result.setMainEle(mainEle);
//                result.setHashTree(element);
//            }
//            if(element.getName().equals("hashTree") && mainEle==null){
//                result = searcheEleByName(name,element);
//            }
//            if(result!=null){
//                break;
//            }
//        }
//        return result;
//    }

//    private String getEncoding(String text) {
//        String result = null;
//
//        String xml = text.trim();
//        if (xml.startsWith("<?xml")) {
//            int end = xml.indexOf("?>");
//            String sub = xml.substring(0, end);
//            StringTokenizer tokens = new StringTokenizer(sub, " =\"'");
//            while (tokens.hasMoreTokens()) {
//                String token = tokens.nextToken();
//                if ("encoding".equals(token)) {
//                    if (!tokens.hasMoreTokens()) {
//                        break;
//                    }
//                    result = tokens.nextToken();
//                    break;
//                }
//            }
//        }
//        return result;
//
//    }

    /**
     * 从本地jmx文件中获取Document
     *
     * @param path
     * @param fileName
     * @return
     * @throws DocumentException
     */
    private Document GetJmeterDocument(String path, String fileName) throws DocumentException {
        SAXReader reader = new SAXReader();
        try {
            reader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        } catch (SAXException e) {
            // On Apache, this should be thrown when disallowing DOCTYPE
            log.error("A DOCTYPE was passed into the XML document", "文件错误2");
        }
        Document document = reader.read(path + fileName);
        return document;
    }


    /**
     * 从指定的OSS jmx文件中获取Document
     *
     * @param bucketName
     * @param path
     * @param fileName
     * @return
     * @throws DocumentException
     */
    @Override
    public Document getJmeterDocument(String bucketName, String path, String fileName) throws IOException, DocumentException {
//        String jmxContent = this.amazonS3Utils.getObjectTextWithRange(bucketName, path + fileName);
//        if (StringUtil.isEmpty(jmxContent)) {
//            return null;
//        }
//        return GetJmeterDocument(jmxContent);
        String localPath = "/data/jmeter/local/";
        String localFileName = UUID.randomUUID().toString();
        this.ztoOssService.downloadFile(bucketName,path+fileName,localPath,localFileName);
        Document document = this.GetJmeterDocument(localPath,localFileName);
        try{
            File deleteFile = new File(localPath + localFileName);
            deleteFile.delete();
        }catch (Exception ex){
            log.info("",ex);
        }
        return document;
    }

//    /**
//     * @param document
//     * @param bucketName
//     * @param ossPath oss上的路径*
//     * @param ossJmxFileName oss上的文件名
//     */
//    @Override
//    public void saveJmxFile(Document document, String bucketName,String ossPath, String ossJmxFileName) {
////        this.amazonS3Utils.createObject(bucketName, ossJmxFileName, document.asXML());
//        /**
//         * 在本地创建jmx文件*
//         */
//
//        String path = "/data/jmeter/local/";
//        String localFileName = UUID.randomUUID().toString();
//        try {
//            OutputFormat outputFormat = OutputFormat2.createPrettyPrint();
//            outputFormat.setEncoding("UTF-8");// 编码
//            File file = new File(path);
//            if (!file.exists() && !file.isDirectory()) {
//                file.mkdirs();
//            }
//            XMLWriter xmlWriter = new XMLWriter(new FileOutputStream(new File(path + localFileName)), outputFormat);
//            xmlWriter.setEscapeText(false);
//            xmlWriter.write(document);
//            xmlWriter.close();
//        }catch (Exception ex){
//            log.error("",ex);
//            throw new ServiceException("保存脚本文件到本地磁盘时失败!");
//        }
//
//        boolean success = ztoOssService.createObject("autojmx",ossPath+"/",ossJmxFileName,path+localFileName);
////        this.saveJmxFile(jmeterDocumentFullInfo.getDocument(),"autojmx",ossPath+"/"+jmxFileName);
//
//        try{
//            File deleteFile = new File(path + localFileName);
//            deleteFile.delete();
//        }catch (Exception ex){
//            log.info("",ex);
//        }
//        if(!success){
//            throw new ServiceException("上传脚本到OSS失败!");
//        }
//        log.info("autojmx/"+ossPath+"/"+ossJmxFileName+"脚本上传成功");
//    }


    @Override
    public void createJmeterScript(String sceneCode, Integer sceneVersion, String sceneName,
                                   String ossPath,String jmxFileName, String productCode,
                                   Integer sceneType, JSONObject runningSet) {
        log.info("场景记录[" + sceneCode + "]版本号[" + sceneVersion +"]场景名称["+sceneName+"]开始生成脚本!");
        List<String> linkMapCodes = sceneLinkInfoMapper.selectLinkCodes(sceneCode,sceneVersion);
        if(CollectionUtil.isEmpty(linkMapCodes)){
            throw new ServiceException("场景记录[" + sceneCode + "]版本号[" + sceneVersion +"]场景名称["+sceneName+"]没有链路数据!");
        }

        SceneInfoEntityDO sceneInfoEntityDO = querySceneInfoByCodeAndVersion(sceneCode,sceneVersion);
        if(sceneInfoEntityDO==null){
            throw new ServiceException("场景记录["+sceneCode+"]版本号["+sceneVersion+"]不存在!");
        }

        String sceneLinkMapJsonString = null;
        try {
            sceneLinkMapJsonString = ztoOssService.getObjectTextWithRange("autojmx", sceneInfoEntityDO.getSceneOssPath() + sceneInfoEntityDO.getSceneOssFile());
        }catch (Exception ex){
            log.info("",ex);
            throw new ServiceException("从OSS读取场景结构文件失败！");
        }


        if(StringUtil.isEmpty(sceneLinkMapJsonString)){
            throw new ServiceException("场景记录["+sceneCode+"]版本号["+sceneVersion+"]OSS的文件内容为空!");
        }

        Scene scene=null;
        Map<String,Line> lineMap = new HashMap<>();
        try {
            scene = JSON.parseObject(sceneLinkMapJsonString, Scene.class);
            scene.setSceneCode(sceneCode);
        }catch (Exception ex){
            log.info("",ex);
            throw new ServiceException("场景记录["+sceneCode+"]版本号["+sceneVersion+"]场景节点格式不正确!");
        }

        String runMethod = "";
        String runTime = "";
        if(null != runningSet && !runningSet.isEmpty()) {
            runMethod = null == runningSet.getString("method") ? runMethod : runningSet.getString("method");
            runTime = null == runningSet.getString("time") ? runTime : runningSet.getString("time");
        }

        JmeterDocumentFullInfo jmeterDocumentFullInfo = createNewJmeterDocument(sceneCode,sceneVersion,sceneName,false,null, productCode,runMethod);
        this.createJdbcConfig(JSON.parseObject(sceneInfoEntityDO.getSceneBackData(), Scene.class), jmeterDocumentFullInfo);
        Map<String, com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes.Node > nodes = new HashMap<>();
        Map<String, Line> lines = new HashMap<>();
        for(int i=0;i<linkMapCodes.size();i++) {
            List<SceneLinkInfoEntity> singleLinkInfo = sceneLinkInfoMapper.selectLinkInfos(linkMapCodes.get(i));
            if(CollectionUtil.isEmpty(singleLinkInfo)){
                throw new ServiceException("场景记录["+sceneCode+"]版本号["+sceneVersion+"]链路code["+linkMapCodes.get(i)+"]是一个空链路");
            }
            /**
             * 查询链路数据文件数量*
             */
            int dataCount = 1;
            try{
                String linkDataStr = ztoOssService.getObjectTextWithRange("autojmx",ossPath+"/data/"+linkMapCodes.get(i)+".json");
                if(StringUtil.isNotEmpty(linkDataStr)){
                    JSONArray linkDataJson = JSON.parseArray(linkDataStr);
                    dataCount = linkDataJson.size();
                    if (linkDataJson.size() == 1) {
                        JSONObject singleNodes = linkDataJson.getJSONObject(0);
                        for (String nodeCode : singleNodes.keySet()) {
                            JSONArray data = singleNodes.getJSONObject(nodeCode).getJSONArray("data");
                            if (CollectionUtil.isNotEmpty(data)) {
                                dataCount *= data.size();
                            }
                        }
                    }
                }
            }catch (Exception ex){
                log.info("",ex);
                throw new ServiceException("场景记录["+sceneCode+"]版本号["+sceneVersion+"]链路code["+linkMapCodes.get(i)+"]的数据文件不存在!");
            }
            // 第一个线程组不改runTime
            if(i == 0) {
                this.createLinkThread(linkMapCodes.get(i),jmeterDocumentFullInfo, singleLinkInfo,dataCount,
                        scene,nodes,lines,ossPath, false,null,sceneType,null,null);
            } else {
                this.createLinkThread(linkMapCodes.get(i),jmeterDocumentFullInfo, singleLinkInfo,dataCount,
                        scene,nodes,lines,ossPath, false,null,sceneType,runMethod,runTime);
            }

        }

        /**
         * 在本地创建jmx文件*
         */

        String path = "/data/jmeter/linkmap/" + sceneCode + "/" + sceneVersion + "/";
        try {
            OutputFormat outputFormat = OutputFormat2.createPrettyPrint();
            outputFormat.setEncoding("UTF-8");// 编码
            File file = new File(path);
            if (!file.exists() && !file.isDirectory()) {
                file.mkdirs();
            }
            XMLWriter xmlWriter = new XMLWriter(new FileOutputStream(new File(path + jmxFileName)), outputFormat);
            xmlWriter.setEscapeText(false);
            xmlWriter.write(jmeterDocumentFullInfo.getDocument());
            xmlWriter.close();
        }catch (Exception ex){
            log.error("",ex);
            throw new ServiceException("保存脚本文件到本地磁盘时失败!");
        }

        if(sceneType==2){
            try {
                Document document = createDcJmx(sceneName,sceneCode, path, jmxFileName);
                if(document!=null) {
                    OutputFormat outputFormat = OutputFormat2.createPrettyPrint();
                    outputFormat.setEncoding("UTF-8");// 编码
                    File file = new File(path);
                    if (!file.exists() && !file.isDirectory()) {
                        file.mkdirs();
                    }
                    XMLWriter xmlWriter = new XMLWriter(new FileOutputStream(new File(path + sceneCode + ".jmx")), outputFormat);
                    xmlWriter.setEscapeText(false);
                    xmlWriter.write(document);
                    xmlWriter.close();
                }
            }catch (Exception ex){
                log.error("",ex);
                throw new ServiceException("创建造数脚本失败!");
            }
        }

        boolean success = ztoOssService.createObject("autojmx",ossPath+"/",jmxFileName,path+jmxFileName);

        try{
            File deleteFile = new File(path + jmxFileName);
            deleteFile.delete();
        }catch (Exception ex){
            log.info("",ex);
        }
        if(!success){
            throw new ServiceException("上传脚本到OSS失败!");
        }

        if(sceneType==2) {

            success = ztoOssService.createObject("autojmx", ossPath + "/", sceneCode + ".jmx", path + sceneCode + ".jmx");

            try {
                File deleteFile = new File(path + sceneCode + ".jmx");
                deleteFile.delete();
            } catch (Exception ex) {
                log.info("", ex);
            }
            if (!success) {
                throw new ServiceException("上传造数脚本到OSS失败!");
            }
        }

//        this.saveJmxFile(jmeterDocumentFullInfo.getDocument(),"autojmx",ossPath+"/"+jmxFileName);
        log.info("autojmx/"+ossPath+"/"+jmxFileName+"脚本上传成功");
        log.info("场景记录[" + sceneCode + "]版本号[" + sceneVersion +"]场景名称["+sceneName+"]生成脚本完成!");
    }

    private Document createDcJmx(String sceneName,String sceneCode,String path,String dcSourceJmxFileName) throws DocumentException {
        Document document = GetJmeterDocument(path,dcSourceJmxFileName);
        Element root = document.getRootElement();
//        Map<String,Element> jdbcRecordList = new HashMap<>();
        List<Node> dbNodes = root.selectNodes("//JDBCDataSource");

        Element mainEle = null;
        AutomaticEleAndHashTree eleAndHashTreeTP = GetJmeterPlanEleAndHashTree(root);
        for (Element elementT : eleAndHashTreeTP.getHashTree().elements()) {
            if (elementT != null && elementT.getName().equals(AutomaticNodeTypeEnum.ThreadGroup.getDesc()) && mainEle == null) {
                mainEle = elementT;
            }
            if (elementT != null && elementT.getName().equals("hashTree") && mainEle != null) {
                return createDcJmeterDocument(sceneName,sceneCode,elementT,dbNodes);
            }
        }

        return null;

    }

    private Document createDcJmeterDocument(String sceneName, String sceneCode,Element sourceEle,List<Node> dbNodes){
        Document document = DocumentHelper.createDocument();
        Element rootElement = document.addElement("jmeterTestPlan");
        rootElement.addAttribute("version","1.2");
        rootElement.addAttribute("properties","5.0");
        rootElement.addAttribute("jmeter","5.4.1");
        Element rootHashTree = rootElement.addElement("hashTree");
        Element planTest = rootHashTree.addElement("TestPlan");
        planTest.addAttribute("guiclass","TestPlanGui");
        planTest.addAttribute("testclass","TestPlan");
        planTest.addAttribute("testname",sceneName);
        planTest.addAttribute("enabled","true");
        newEle(planTest,"stringProp",new HashMap<String, String>(){
            {
                put("name", "TestPlan.comments");
            } },"");
        newEle(planTest,"boolProp",new HashMap<String, String>(){
            {
                put("name", "TestPlan.functional_mode");
            } },"false");
        newEle(planTest,"boolProp",new HashMap<String, String>(){
            {
                put("name", "tearDown_on_shutdown");
            } },"true");
        newEle(planTest,"boolProp",new HashMap<String, String>(){
            {
                put("name", "TestPlan.serialize_threadgroups");
            } },"false");

        Element planContentHashTree = rootHashTree.addElement("hashTree");

        Element tfHashTree = newTF(planContentHashTree,sceneCode);

        if(dbNodes!=null){
            for(Node node : dbNodes){
                Element dbEle = (Element) node;
                tfHashTree.add((Element) dbEle.clone());
                newEle(tfHashTree,"hashTree");
            }
        }

        List<Element> elementList = sourceEle.elements();

        for(Element ele:elementList){
            if("readTestDatas".equals(ele.attributeValue("testname")) || "readVariable".equals(ele.attributeValue("testname"))){
                ele.attribute("enabled").setValue("false");
            }
//            if("readDataCenterVariable".equals(ele.attributeValue("testname"))){
//
//            }
//            if(ele.getName().equals("JSR223Sampler")){
//                if(ele.selectSingleNode("stringProp[@name='filename']").getStringValue().indexOf("setInputParameter")>=0){
//                    ele.attribute("enabled").setValue("false");
//                }
//            }
            if(samplerList.indexOf(ele.getName())>=0){
                ele.attribute(AutomaticConstant.JMX_TESTNAME).setValue(sceneName+"_"+ele.attributeValue(AutomaticConstant.JMX_TESTNAME+"_"+sceneCode));
            }
//            if(ele.getName().equals("JSR223Sampler")){
//                if(ele.selectSingleNode("filename").getStringValue().indexOf("setOutputParameter")>=0){
//
//                }
//            }
            Element newEle = (Element) ele.clone();
            tfHashTree.add(newEle);
        }

        Node dataCenterListenerNode = tfHashTree.selectSingleNode("//JSR223Listener[@testname='dataCenterListener']");
        if(dataCenterListenerNode!=null){
            ((Element)dataCenterListenerNode).attribute("enabled").setValue("false");
        }

        Node readDataCenterVariableNode = tfHashTree.selectSingleNode("//JSR223PreProcessor[@testname='readDataCenterVariable']");
        if(readDataCenterVariableNode!=null){
            Element readDataCenterVariableEle = (Element) readDataCenterVariableNode;
            Element parametersEle = (Element) readDataCenterVariableEle.selectSingleNode("stringProp[@name='parameters']");
            parametersEle.setText(config.getDebugWorkspace()+"/${devops_qc_task_id}/"+sceneCode+"/variable/dataCenterVariable.json");
        }

        Node readInputParameterNode = tfHashTree.selectSingleNode("//JSR223PreProcessor[@testname='readInputParameter']");
        if(readInputParameterNode!=null){
            Element readInputParameterEle = (Element) readInputParameterNode;
            Element parametersEle = (Element) readInputParameterEle.selectSingleNode("stringProp[@name='parameters']");
            parametersEle.setText("${_devops_qc_dc_index_}");
            Element fileNameEle = (Element) readInputParameterEle.selectSingleNode("stringProp[@name='filename']");
            fileNameEle.setText(config.getDebugWorkspace()+"/shell/readArrayDataCenter.groovy");
        }

        List<Node> dbPreNodes = tfHashTree.selectNodes("//JSR223PreProcessor[@testname='前置-DB变量提取']");
        List<Node> dbPostNodes = tfHashTree.selectNodes("//JSR223PostProcessor[@testname='后置-DB变量提取']");
        if(dbPreNodes!=null){
            for(Node node:dbPreNodes){
                Element dbRequestEle = (Element) node;
                Element parametersEle = (Element) dbRequestEle.selectSingleNode("stringProp[@name='parameters']");
                File file = new File(parametersEle.getTextTrim());
                String jmxName = file.getName();
                parametersEle.setText(config.getDebugWorkspace()+"/${devops_qc_task_id}/"+sceneCode+"/data/"+jmxName);
            }
        }
        if(dbPostNodes!=null){
            for(Node node:dbPostNodes){
                Element dbRequestEle = (Element) node;
                Element parametersEle = (Element) dbRequestEle.selectSingleNode("stringProp[@name='parameters']");
                File file = new File(parametersEle.getTextTrim());
                String jmxName = file.getName();
                parametersEle.setText(config.getDebugWorkspace()+"/${devops_qc_task_id}/"+sceneCode+"/data/"+jmxName);
            }
        }
        List<Node> dataSourceNodes = tfHashTree.selectNodes("//stringProp[@name='dataSource']");
        if(dataSourceNodes!=null){
            for(Node dsNode:dataSourceNodes){
                Element dataSourceNode = (Element) dsNode;
                dsNode.setText(dataSourceNode.getText()+sceneCode);
            }
        }
        return document;
    }

    private Element newTF(Element parentEle,String tfName){
        Element tfEle = newEle(parentEle, "TestFragmentController",new HashMap<String, String>(){
            {
                put("guiclass", "TestFragmentControllerGui");
                put("testclass", "TestFragmentController");
                put("testname", tfName);
                put("enabled", "false");
            } });
        return newEle(parentEle,"hashTree");
    }

    @Override
    public void createJmeterScript(DebugTaskInfo debugTaskInfo) {
        Map<String, com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes.Node> nodes =
                new HashMap<>();
        Map<String, Line> lines = new HashMap<>();
        JmeterDocumentFullInfo jmeterDocumentFullInfo = createNewJmeterDocument(debugTaskInfo.getSceneCode(), debugTaskInfo.getSceneVersion(),debugTaskInfo.getSceneName(),true,
                debugTaskInfo.getTaskId(), debugTaskInfo.getProductCode(),null);
        this.createJdbcConfig(debugTaskInfo.getScene(), jmeterDocumentFullInfo);
        this.createLinkThread(
                debugTaskInfo.getLinkBaseInfo().get(0).getLinkMapCode(),
                jmeterDocumentFullInfo,
                sceneLinkInfoEntityConverter.convertSceneLinkInfoEntity(debugTaskInfo.getLinkBaseInfo()),
                1,
                debugTaskInfo.getScene(),
                nodes,
                lines,
                null,true,debugTaskInfo.getTaskId(),debugTaskInfo.getSceneType(),null,null);
        String path = "/data/jmeter/"+debugTaskInfo.getTaskId()+"/";
        try {
            OutputFormat outputFormat = OutputFormat2.createPrettyPrint();
            outputFormat.setEncoding("UTF-8");// 编码
            File file = new File(path);
            if (!file.exists() && !file.isDirectory()) {
                file.mkdirs();
            }
            XMLWriter xmlWriter = new XMLWriter(new FileOutputStream(new File(path + debugTaskInfo.getTaskId()+".jmx")), outputFormat);
            xmlWriter.setEscapeText(false);
            xmlWriter.write(jmeterDocumentFullInfo.getDocument());
            xmlWriter.close();
        }catch (Exception ex){
            log.error("",ex);
            throw new ServiceException("保存脚本文件到本地磁盘时失败!");
        }
    }

//    @Override
//    public JmeterDocumentFullInfo createJmeterScriptInfo(
//            String sceneCode,
//            Integer sceneVersion,
//            String sceneName,
//            String productCode,
//            List<SceneLinkInfoEntityDO> singleLinkInfo,
//            Scene scene) {
//        Map<String, com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes.Node> nodes =
//                new HashMap<>();
//        Map<String, Line> lines = new HashMap<>();
//        JmeterDocumentFullInfo jmeterDocumentFullInfo = createNewJmeterDocument(sceneCode, sceneVersion, sceneName);
//        this.createJdbcConfig(productCode, jmeterDocumentFullInfo);
//        this.createLinkThread(
//                singleLinkInfo.get(0).getLinkMapCode(),
//                jmeterDocumentFullInfo,
//                sceneLinkInfoEntityConverter.convertSceneLinkInfoEntity(singleLinkInfo),
//                1,
//                scene,
//                nodes,
//                lines,
//                null);
//        return jmeterDocumentFullInfo;
//    }

    private void createLinkThread(String linkCode,JmeterDocumentFullInfo jmeterDocumentFullInfo, List<SceneLinkInfoEntity> singleLinkInfo,
                                  int dataCount, Scene scene, Map<String, com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes.Node> nodes,
                                  Map<String, Line> lines,String ossPath,boolean isDebug,String taskId,Integer sceneType,String runMethod,String runTime) {

        String workspace = "";
        String taskWorkSpace = "";
        if(isDebug || sceneType.intValue()==2){
            workspace = config.getDebugWorkspace()+"/";
            taskWorkSpace = workspace + taskId + "/";
        }
        /**
         * 创建线程组*
         */
        Element parentEle = JmeterEleUtil.newThreadGroup(jmeterDocumentFullInfo.getPlanContentHashTree(),linkCode,dataCount,true,runMethod,runTime);

        /**
         * 创建HeaderManager
         */
        List<ApiTestVariableVO> headerVariables = apiTestRepository.querySceneVariable(scene.getProductCode(), scene.getSceneCode(), VariableTypeEnum.VARIABLE, SubVariableTypeEnum.HEADER, Arrays.asList(VariableUsageTypeEnum.UNKNOWN));
        if (CollectionUtil.isNotEmpty(headerVariables)) {
            Map<String, String> headerMap = headerVariables.stream().collect(Collectors.toMap(ApiTestVariableVO::getVariableKey, ApiTestVariableVO::getVariableValue));
            JmeterEleUtil.newHeaderManager(parentEle, headerMap,true);
        }

        String testDataFileName = "./data/" + linkCode + ".json";
//        if(sceneType.intValue()==2) {
//            isDebug = true;
//        }
        if(sceneType.intValue()!=2) {
            /**
             * 创建计数器与读取数据文件和变量文件*
             */
            JmeterEleUtil.newCounter(parentEle, "1", "1", "devops_qc_counter", "", true, false, false);
        }
        Element readVarEle = JmeterEleUtil.newJSR223Sampler("JSR223Sampler", parentEle, "readVariable", null, workspace + "shell/readVariable.groovy", null, null, null, true);
        JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor", readVarEle, "隐藏采样器", "prev.setIgnore();", null, "", null, null, true);

        if (isDebug) {
            testDataFileName = taskWorkSpace + "data.json";
        }
        Element readDateTestEle = JmeterEleUtil.newJSR223Sampler("JSR223Sampler",parentEle,"readTestDatas",null,workspace+"shell/readTestDatas.groovy","",null,testDataFileName,true);
        JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor",readDateTestEle,"隐藏采样器","prev.setIgnore();",null,"",null,null,true);
        if(sceneType.intValue()==2){
            Element hashTreeEle = JmeterEleUtil.newJSR223Sampler("JSR223Sampler",parentEle,"造数前置脚本",null,workspace+"shell/setInputParameter.groovy","",null,scene.getSceneCode().trim(),true);
            JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", hashTreeEle, "readDataCenterVariable", null, workspace+"shell/readDataCenterVariable.groovy", null, null, taskWorkSpace+"dataCenterVariable.json", true);
            JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", hashTreeEle, "readDataCenterInputVariable", null, workspace+"shell/readDataCenterInputVariable.groovy", null, null, scene.getSceneCode().trim(), true);
            JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", hashTreeEle, "readInputParameter", null, workspace+"shell/readInputParameter.groovy", null, null, scene.getSceneCode().trim(), true);
            HashMap<String,List<String>> userParameterMap = new HashMap<>();
            List<String> devopsInputOutputDatas = new ArrayList<>();
            devopsInputOutputDatas.add("${__eval(${devops_input_output_data})}");
            List<String> devopsVariablePropertys = new ArrayList<>();
            devopsVariablePropertys.add("${__eval(${devops_variable_property})}");
            userParameterMap.put("devops_input_output_data",devopsInputOutputDatas);
            userParameterMap.put("devops_variable_property",devopsVariablePropertys);
            JmeterEleUtil.newUserParameters(hashTreeEle,userParameterMap,false,true);
            JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", hashTreeEle, "前置-提取造数入参", null, workspace+"shell/extractInputParameter.groovy", null, null, null, true);
            JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor", hashTreeEle, "隐藏采样器", "prev.setIgnore();", null, "", null, null, true);
        }

        Map<Integer, SceneLinkInfoEntity> linkComponentMap = singleLinkInfo.stream().collect(Collectors.toMap(SceneLinkInfoEntity::getSequenceNumber, (p) -> p));
        if(CollectionUtil.isNotEmpty(linkComponentMap)){
            log.info("链路数据 >>>>> {}",JSON.toJSONString(linkComponentMap));
        }
//        for (SceneLinkInfoEntity linkInfoEntity : singleLinkInfo) {
        for(int sn=0; sn<linkComponentMap.keySet().size();sn++) {
            SceneLinkInfoEntity linkInfoEntity = linkComponentMap.get(sn);
//            Line lastLine = null;
            if (LinkMapTypeEnum.valueOf(linkInfoEntity.getLinkComponentType()) == LinkMapTypeEnum.LINE) {
                Line line = null;
                if (lines.containsKey(linkInfoEntity.getLinkComponentCode())) {
                    line = lines.get(linkInfoEntity.getLinkComponentCode());
                }else {
                    if(scene.getLines()==null){
                        throw new ServiceException("场景中线条数据为空，请检查数据正确性!");
                    }
                    if(!scene.getLines().containsKey(linkInfoEntity.getLinkComponentCode())){
                        throw new ServiceException("线条Code["+linkInfoEntity.getLinkComponentCode()+"]在场景的连线数据中不存在!");
                    }
                    line = JSON.toJavaObject(scene.getLines().getJSONObject(linkInfoEntity.getLinkComponentCode()),Line.class);
                    lines.put(linkInfoEntity.getLinkComponentCode(), line);
                }
                if(line!=null) {
                    IfController ifController = JSON.toJavaObject(line.getIfController(), IfController.class);
                    if (StringUtil.isNotEmpty(ifController.getExpression())) {
                        String ifControllerComments = "["+ifController.getName()+"]";
//                                "["+linkComponentMap.get(sn-1).getLinkComponentName()+"]-["+linkComponentMap.get(sn+1).getLinkComponentName()+"]";
                        JmeterEleUtil.newJSR223Sampler("JSR223Sampler",parentEle,"条件日志",shellParseUtil.getIfControllerLogScripts(ifController.getExpression(),ifControllerComments),null,"",null,null,true);
                        Element ifEle = JmeterEleUtil.newIfController(linkInfoEntity.getLinkComponentName(), parentEle, "${__groovy("+ifController.getExpression()+",)}", true);
                        if (ifEle != null) {
                            parentEle = ifEle;
                        }
                    }
                }

            }

            if (LinkMapTypeEnum.valueOf(linkInfoEntity.getLinkComponentType()) == LinkMapTypeEnum.NODE) {
                /**
                 * 加请求节点*
                 */
                com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes.Node currentNode = null;
                if (nodes.containsKey(linkInfoEntity.getLinkComponentCode())) {
                    currentNode = nodes.get(linkInfoEntity.getLinkComponentCode());
                }else {
                    currentNode = JSON.toJavaObject(scene.getNodes().getJSONObject(linkInfoEntity.getLinkComponentCode()),com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes.Node.class);
                    nodes.put(linkInfoEntity.getLinkComponentCode(),currentNode);
                }
                if(StringUtil.isEmpty(currentNode.getSampler().getString("type"))){
                    throw new ServiceException("节点名称["+currentNode.getName()+"]的请求类型不能为空!");
                }
                if(StringUtil.isEmpty(currentNode.getSampler().getString("name"))){
                    currentNode.getSampler().put("name",currentNode.getName());
                }
                LinkMapTypeEnum typeEnum = LinkMapTypeEnum.valueOf(currentNode.getSampler().getString("type"));
                Element currentSamplerEle = null;
                /**
                 * 节点轮询调用
                 */
                Element pollingEle = createPollingController(currentNode.getPollingController(), typeEnum, parentEle, workspace);
                if (null != pollingEle) {
                    currentNode.getSampler().put("name", currentNode.getSampler().getString("name") + "_${devops_qc_polling_counter}");
                }
                Element finalParentEle = ObjectUtils.defaultIfNull(pollingEle, parentEle);
                if(typeEnum==LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE || typeEnum==LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_FORM_TYPE){
                    currentSamplerEle = createHttpComponent(typeEnum,currentNode,finalParentEle,workspace,ossPath,isDebug,taskId,sceneType);
                }
                if(typeEnum==LinkMapTypeEnum.DUBBO_REQUEST_COMPONENT){
                    currentSamplerEle = createDubboComponent(typeEnum,currentNode,finalParentEle,workspace,ossPath,isDebug,taskId,sceneType);
                }
                if(typeEnum==LinkMapTypeEnum.ZMSClient_REQUEST_COMPONENT){
                    currentSamplerEle = createZmsComponent(typeEnum,currentNode,finalParentEle,workspace,ossPath,isDebug,taskId,sceneType);
                }
                if(typeEnum==LinkMapTypeEnum.DataCenter_REQUEST_COMPONENT){
                    currentSamplerEle = createDataCenterComponent(typeEnum,currentNode,finalParentEle,workspace,ossPath,isDebug,taskId);
                }
                if(currentSamplerEle!=null && isDebug && typeEnum!=LinkMapTypeEnum.DataCenter_REQUEST_COMPONENT) {
                    if (null == pollingEle) {
                        JmeterEleUtil.newJSR223Sampler("JSR223Listener", currentSamplerEle, "回调结果监听器", null, workspace + "shell/callback.groovy", null, null, currentNode.getCode().trim(), true);
                    } else {
                        JmeterEleUtil.newJSR223Sampler("JSR223Listener", currentSamplerEle, "保存结果监听器", null, workspace + "shell/savePollingData.groovy", null, null, currentNode.getCode().trim(), true);
                    }
                }
                if (null != pollingEle) {
                    setPollingTerminated(pollingEle, currentNode.getPollingController().getString("terminatedCondition"), workspace);
                }
            }
        }

        if(sceneType.intValue()==2){
            Element hashTreeEle = JmeterEleUtil.newJSR223Sampler("JSR223Sampler",parentEle,"造数后置脚本",null,workspace+"shell/setOutputParameter.groovy","",null,scene.getSceneCode().trim(),true);
            HashMap<String,List<String>> userParameterMap = new HashMap<>();
            List<String> devopsInputOutputDatas = new ArrayList<>();
            devopsInputOutputDatas.add("${__eval(${devops_input_output_data})}");
            userParameterMap.put("devops_input_output_data",devopsInputOutputDatas);
            JmeterEleUtil.newUserParameters(hashTreeEle,userParameterMap,false,true);
            JmeterEleUtil.newJSR223Sampler("JSR223Listener", hashTreeEle, "dataCenterListener", null, workspace+"shell/dataCenterListener.groovy", null, null, scene.getSceneCode().trim(), true);
//            JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor", hashTreeEle, "隐藏采样器", "prev.setIgnore();", null, "", null, null, true);
        }
    }

    private Element createHttpComponent(LinkMapTypeEnum typeEnum, com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes.Node currentNode,Element parentEle,String workspace,String ossPath,boolean isDebug,String taskId,Integer sceneType){
        if(StringUtil.isEmpty(currentNode.getCode())){
            throw new ServiceException("节点["+currentNode.getName()+"]的code不能为空！");
        }

        HttpRequestComponent httpRequestComponent = JSON.toJavaObject(currentNode.getSampler(),HttpRequestComponent.class);
        httpRequestComponent.setPathUrl(StringUtils.deleteWhitespace(httpRequestComponent.getPathUrl()));
        if (StringUtil.isNotBlank(httpRequestComponent.getPathUrl())
                && StringUtil.isBlank(httpRequestComponent.getServerNameOrIp())) {
            try {
                URL url = new URL(httpRequestComponent.getPathUrl());
                httpRequestComponent.setProtocol(url.getProtocol());
                httpRequestComponent.setServerNameOrIp(url.getHost());
                httpRequestComponent.setPortNumber(String.valueOf(url.getPort()));
                if (StringUtil.isNotBlank(url.getQuery())) {
                    httpRequestComponent.setPathUrl(url.getPath() + "?" + url.getQuery());
                } else {
                    httpRequestComponent.setPathUrl(url.getPath());
                }
            } catch (Exception e) {
                // ignore
            }
        }
        if (CollectionUtil.isNotEmpty(httpRequestComponent.getPathParams())) {
            httpRequestComponent.setPathUrl("${devops_qc_path}");
        }
        if (CollectionUtil.isNotEmpty(httpRequestComponent.getParameters())
                && StringUtil.isNotBlank(httpRequestComponent.getBodyData())) {
            httpRequestComponent.setPathUrl("${devops_qc_path}");
            typeEnum = LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE;
            httpRequestComponent.setType(typeEnum);
        }
        if(isDebug && httpRequestComponent.getMockSwitch()){
            httpRequestComponent.setProtocol(httpRequestComponent.getMockProtocol());
            httpRequestComponent.setServerNameOrIp(httpRequestComponent.getMockServerNameOrIp());
            httpRequestComponent.setPathUrl(httpRequestComponent.getMockPathUrl());
            httpRequestComponent.setPortNumber(httpRequestComponent.getMockPortNumber());
        }

        JSONArray filesInfo = httpRequestComponent.getHttpFiles();
        if(filesInfo!=null && filesInfo.size()>0){
            if(typeEnum==LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE && StringUtil.isBlank(httpRequestComponent.getBodyData())) {
                httpRequestComponent.setType(LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_FORM_TYPE);
                typeEnum = LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_FORM_TYPE;
            }
        }
        Element httpSamplerEle = JmeterEleUtil.newHTTPSampler(parentEle,httpRequestComponent,true);

        /**
         * 处理读取的测试数据*
         */
        if(typeEnum==LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE) {
            JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", httpSamplerEle, "前置-处理读取的数据", null, workspace+"shell/readLinkTestData.groovy", null, null, currentNode.getCode().trim(), true);
        }
        if(typeEnum==LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_FORM_TYPE) {
            JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", httpSamplerEle, "前置-处理读取的数据", null, workspace+"shell/readLinkTestDataForm.groovy", null, null, currentNode.getCode().trim(), true);
        }
        if (CollectionUtil.isNotEmpty(currentNode.getData())) {
            addDataVariableElement(workspace, currentNode.getCode().trim(), httpSamplerEle);
        }
        JSONArray preComponents = currentNode.getPreComponents();
        if(preComponents!=null && preComponents.size()>0){
            /**
             * 增加前置组件*
             */
            this.createPreComponent(currentNode.getName(),httpSamplerEle,preComponents,ossPath,isDebug,taskId,workspace,sceneType);
        }
        this.createPublicComponent(httpSamplerEle,currentNode.getCode().trim(),httpRequestComponent.getHttpRequestHeader(),typeEnum,isDebug,taskId,workspace);
        /**
         * 处理网关验签
         */
        if (!isDebug || !httpRequestComponent.getMockSwitch()) {
            this.addGatewaySignScript(workspace, currentNode, httpSamplerEle);
        }
        JSONArray postComponents = currentNode.getPostComponents();
        if(postComponents!=null && postComponents.size()>0){
            /**
             * 增加后置组件*
             */
            this.createPostComponent(currentNode.getName(),httpSamplerEle,postComponents,ossPath,isDebug,taskId,workspace,sceneType);
        }
        return httpSamplerEle;
    }

    private Element createDubboComponent(LinkMapTypeEnum typeEnum, com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes.Node currentNode,Element parentEle,String workspace,String ossPath,boolean isDebug,String taskId,Integer sceneType){
        if(StringUtil.isEmpty(currentNode.getCode())){
            throw new ServiceException("节点["+currentNode.getName()+"]的code不能为空！");
        }
        DubboRequestComponent dubboRequestComponent = JSON.toJavaObject(currentNode.getSampler(),DubboRequestComponent.class);

        dubboRequestComponent.setAddress(qcConfigBasicService.getDubboZKTestAddress());
        Element dubboSamplerEle = JmeterEleUtil.newDubboSample(parentEle,dubboRequestComponent,true);

        /**
         * 处理读取的测试数据*
         */
        JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", dubboSamplerEle, "前置-处理读取的数据", null, workspace+"shell/readLinkTestDataDubbo.groovy", null, null, currentNode.getCode().trim(), true);
        if (CollectionUtil.isNotEmpty(currentNode.getData())) {
            addDataVariableElement(workspace, currentNode.getCode().trim(), dubboSamplerEle);
        }
        JSONArray preComponents = currentNode.getPreComponents();
        if(preComponents!=null && preComponents.size()>0){
            /**
             * 增加前置组件*
             */
            this.createPreComponent(currentNode.getName(),dubboSamplerEle,preComponents,ossPath,isDebug,taskId,workspace,sceneType);
        }
        this.createPublicComponent(dubboSamplerEle,currentNode.getCode().trim(),null,typeEnum,isDebug,taskId,workspace);
        JSONArray postComponents = currentNode.getPostComponents();
        if(postComponents!=null && postComponents.size()>0){
            /**
             * 增加后置组件*
             */
            this.createPostComponent(currentNode.getName(),dubboSamplerEle,postComponents,ossPath,isDebug,taskId,workspace,sceneType);
        }
        return dubboSamplerEle;
    }

    private Element createZmsComponent(LinkMapTypeEnum typeEnum, com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes.Node currentNode,Element parentEle,String workspace,String ossPath,boolean isDebug,String taskId,Integer sceneType){
        if(StringUtil.isEmpty(currentNode.getCode())){
            throw new ServiceException("节点["+currentNode.getName()+"]的code不能为空！");
        }
        ZMSClientRequestComponent zmsClientRequestComponent = JSON.toJavaObject(currentNode.getSampler(),ZMSClientRequestComponent.class);

        zmsClientRequestComponent.setAddress(qcConfigBasicService.getZMSZKTestAddress());
        zmsClientRequestComponent.setMessageBody("${devops_qc_param}");
        if(StringUtil.isEmpty(zmsClientRequestComponent.getRouteTag())) {
            zmsClientRequestComponent.setRouteTag("${devops_qc_route_tag}");
        }
        if(StringUtil.isEmpty(zmsClientRequestComponent.getTopic())){
            throw new ServiceException("节点["+currentNode.getName()+"]ZMSSampler的topic不能为空！");
        }
        Element zmsSamplerEle = JmeterEleUtil.newZMSSampler(parentEle,zmsClientRequestComponent,true);
        /**
         * 处理读取的测试数据*
         */
        JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", zmsSamplerEle, "前置-处理读取的数据", null, workspace+"shell/readLinkTestData.groovy", null, null, currentNode.getCode().trim(), true);
        if (CollectionUtil.isNotEmpty(currentNode.getData())) {
            addDataVariableElement(workspace, currentNode.getCode().trim(), zmsSamplerEle);
        }
        JSONArray preComponents = currentNode.getPreComponents();
        if(preComponents!=null && preComponents.size()>0){
            /**
             * 增加前置组件*
             */
            this.createPreComponent(currentNode.getName(),zmsSamplerEle,preComponents,ossPath,isDebug,taskId,workspace,sceneType);
        }
        this.createPublicComponent(zmsSamplerEle,currentNode.getCode().trim(),null,typeEnum,isDebug,taskId,workspace);
        JSONArray postComponents = currentNode.getPostComponents();
        if(postComponents!=null && postComponents.size()>0){
            /**
             * 增加后置组件*
             */
            this.createPostComponent(currentNode.getName(),zmsSamplerEle,postComponents,ossPath,isDebug,taskId,workspace,sceneType);
        }
        return zmsSamplerEle;
    }

    private Element createDataCenterComponent(LinkMapTypeEnum typeEnum, com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes.Node currentNode,Element parentEle,String workspace,String ossPath,boolean isDebug,String taskId){
        if(StringUtil.isEmpty(currentNode.getCode())){
            throw new ServiceException("节点["+currentNode.getName()+"]的code不能为空！");
        }
        DataCenterRequestComponent dataCenterRequestComponent = JSON.toJavaObject(currentNode.getSampler(),DataCenterRequestComponent.class);
        QueryLatestEditSceneInfoReq req = new QueryLatestEditSceneInfoReq();
        req.setSceneCode(dataCenterRequestComponent.getDataCenterSceneCode());
        SceneInfoEntity sceneInfo = sceneInfoMapper.queryLatestEditSceneInfo(req);
        if(sceneInfo==null){
            throw new ServiceException("造数引用code["+dataCenterRequestComponent.getDataCenterSceneCode()+"]没有找到！");
        }
        if(sceneInfo.getSceneType().intValue()!=2){
            throw new ServiceException("造数节点["+dataCenterRequestComponent.getDataCenterSceneCode()+"-"+sceneInfo.getSceneName()+"]类型不正确！");
        }
        dataCenterRequestComponent.setOssPath(sceneInfo.getSceneOssPath()+"/publish/");
        dataCenterRequestComponent.setComments(sceneInfo.getSceneCode()+"_"+sceneInfo.getSceneName());
        /**
         * 处理数据集
         */
        if (CollectionUtil.isNotEmpty(currentNode.getData())) {
            Element dataVariableSampler = JmeterEleUtil.newJSR223Sampler("JSR223Sampler", parentEle, "dataVariable", null, null, null, null, null, true);
            addDataVariableElement(workspace, currentNode.getCode().trim(), dataVariableSampler);
            JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor", dataVariableSampler, "隐藏采样器", "prev.setIgnore();", null, "", null, null, true);
        }
        Element dataCenterSamplerEle = JmeterEleUtil.newDataCennterSampler(parentEle,dataCenterRequestComponent.getName(),currentNode.getCode(),dataCenterRequestComponent.getOssPath(),dataCenterRequestComponent.getComments(),true);
        /**
         * 处理前后置*
         */
        Element preEle = (Element) dataCenterSamplerEle.selectSingleNode("pre");
        Element postEle = (Element) dataCenterSamplerEle.selectSingleNode("post");
        JSONArray preComponents = currentNode.getPreComponents();
        if(preComponents!=null && preComponents.size()>0){
            /**
             * 增加前置组件*
             */
            this.createPreComponent(currentNode.getName(),preEle,preComponents,ossPath,isDebug,taskId,workspace,1);
        }

        JSONArray postComponents = currentNode.getPostComponents();
        if(postComponents!=null && postComponents.size()>0){
            /**
             * 增加后置组件*
             */
            this.createPostComponent(currentNode.getName(),postEle,postComponents,ossPath,isDebug,taskId,workspace,1);
        }

        return dataCenterSamplerEle;
    }

    private void createPublicComponent(Element samplerEle,String nodeCode,JSONArray header,LinkMapTypeEnum linkMapTypeEnum,boolean isDebug,String taskId,String workspace){
        String taskWorkSpace = "";
        if(isDebug){
            taskWorkSpace = workspace + taskId + "/";
        }
        if(linkMapTypeEnum==LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_FORM_TYPE || linkMapTypeEnum==LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE) {
            Map<String, String> headerMap = new HashMap<>();
            if(null != header) {
                header.forEach(item -> {
                    headerMap.put(((JSONObject) item).getString("name"),
                            ((JSONObject) item).getString("value"));
                });
            }
            JmeterEleUtil.newHeaderManager(samplerEle, headerMap, true);
        }
        HashMap<String,List<String>> userParameterMap = new HashMap<>();
        List<String> devopsQcHeaderList = new ArrayList<>();
        devopsQcHeaderList.add("${__eval(${devops_qc_header})}");
        List<String> devopsQcParamList = new ArrayList<>();
        devopsQcParamList.add("${__eval(${devops_qc_param})}");
        List<String> devopsQcTypeList = new ArrayList<>();
        devopsQcTypeList.add(linkMapTypeEnum.name());
        userParameterMap.put("devops_qc_header",devopsQcHeaderList);
        userParameterMap.put("devops_qc_param", devopsQcParamList);
        userParameterMap.put("devops_qc_type", devopsQcTypeList);
        if (linkMapTypeEnum == LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_FORM_TYPE
                || linkMapTypeEnum == LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE) {
            List<String> devopsQcPathList = new ArrayList<>();
            devopsQcPathList.add("${__eval(${devops_qc_path})}");
            userParameterMap.put("devops_qc_path", devopsQcPathList);
        }
        if(linkMapTypeEnum==LinkMapTypeEnum.DUBBO_REQUEST_COMPONENT) {
            List<String> devopsQcArgs = new ArrayList<>();
            devopsQcArgs.add("${__eval(${devops_qc_args})}");
            userParameterMap.put("devops_qc_args", devopsQcArgs);
            List<String> devopsQcAttachment = new ArrayList<>();
            devopsQcAttachment.add("${__eval(${devops_qc_attachment})}");
            userParameterMap.put("devops_qc_attachment", devopsQcAttachment);
        }
        JmeterEleUtil.newUserParameters(samplerEle,userParameterMap,false,true);

        if(linkMapTypeEnum==LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_FORM_TYPE || linkMapTypeEnum==LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE) {
            JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", samplerEle, "前置-循环add请求头", null, workspace + "shell/addHeader.groovy", null, null, null, true);
        }
        if(linkMapTypeEnum==LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_FORM_TYPE) {
            JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", samplerEle, "前置-循环add参数", null, workspace + "shell/addFormData.groovy", null, null, "NeedLess", true);
        }
        if(linkMapTypeEnum==LinkMapTypeEnum.DUBBO_REQUEST_COMPONENT) {
            JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", samplerEle, "前置-循环add参数", null, workspace + "shell/addDubboFormData.groovy", null, null, null, true);
        }
        JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor",samplerEle,"set请求和响应变量",null,workspace+"shell/setVariable.groovy",null,null,null,true);
    }

    private void createPreComponent(String currentNodeName,Element samplerEle,JSONArray preComponents,String ossPath,boolean isDebug,String taskId,String workspace,Integer sceneType) {
        String taskWorkSpace = "";
        if(isDebug){
            taskWorkSpace = workspace + "${devops_qc_task_id}" + "/";
        }
        for(int i=0;i<preComponents.size();i++){
            JSONObject componentJSON = preComponents.getJSONObject(i);
            if(StringUtil.isEmpty(componentJSON.getString("type"))){
                throw new ServiceException("节点["+currentNodeName+"]中有组件类型为空的情况！");
            }
            if(LinkMapTypeEnum.SLEEP==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))){
                SleepComponent sleepComponent = JSON.toJavaObject(componentJSON,SleepComponent.class);
                JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor",samplerEle,"前置-定时器",null,workspace+"shell/sleep.groovy",null,null,sleepComponent.getMilliSecond().toString(),true);
            } else if(LinkMapTypeEnum.REDIS==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))) {
                RedisComponent redisComponent = JSON.parseObject(componentJSON.toJSONString(),RedisComponent.class);
                if(redisComponent.getVariableExtraction()!=null && redisComponent.getVariableExtraction().size()>0) {
                    String varsName = AggregateIdUtil.generateId(AggregateType.SNOWFLAKE);
                    String varsFileName = "./data/" + varsName + ".json";
                    String configPath = "data/";
                    if(isDebug){
                        configPath = config.getDebugWorkspace()+"/${devops_qc_task_id}/";
                        varsFileName = taskWorkSpace+varsName+".json";
                        File file = new File("/data/jmeter/"+taskId+"/"+varsName+".json");
                        try {
                            FileUtils.writeStringToFile(file, JSONObject.toJSONString(redisComponent), "UTF-8");
                        }catch (IOException ex){
                            log.warn("",ex);
                            throw new ServiceException("节点["+currentNodeName+"]前置Redis组件提取数据文件保存时发生异常！");
                        }
                    }else {
                        ztoOssService.createObject("autojmx", ossPath + "/data/" + varsName + ".json", JSONObject.toJSONString(redisComponent));
                    }
                    JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", samplerEle, "前置-redis组件", null, workspace+"shell/redisComponent.groovy", null, null,varsFileName+" "+configPath,true);
                }
            } else if(LinkMapTypeEnum.ES==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))) {
                ESComponent esComponent = JSON.parseObject(componentJSON.toJSONString(),ESComponent.class);
                if(esComponent.getVariableExtraction()!=null && esComponent.getVariableExtraction().size()>0) {
                    ProxyExecDTO dto = new ProxyExecDTO();
                    dto.setPath(esComponent.getPath());
                    dto.setMethod(esComponent.getRequestType());
                    dto.setClusterId(esComponent.getClusterId());
                    dto.setUserName(Strings.EMPTY);
                    dto.setBodyString(JSON.parseObject(esComponent.getScript()));
//                    String sign = zbaseService.generateEsSign(dto);
                    esComponent.setProxyExecBodyJsonStr(JSON.toJSONString(dto));
                    String varsName = AggregateIdUtil.generateId(AggregateType.SNOWFLAKE);
                    String varsFileName = "./data/" + varsName + ".json";
                    if(isDebug){
                        varsFileName = taskWorkSpace+varsName+".json";
                        File file = new File("/data/jmeter/"+taskId+"/"+varsName+".json");
                        try {
                            FileUtils.writeStringToFile(file, JSONObject.toJSONString(esComponent), "UTF-8");
                        }catch (IOException ex){
                            log.warn("",ex);
                            throw new ServiceException("节点["+currentNodeName+"]前置ES组件提取数据文件保存时发生异常！");
                        }
                    }else {
                        ztoOssService.createObject("autojmx", ossPath + "/data/" + varsName + ".json", JSONObject.toJSONString(esComponent));
                    }
//                    JSONObject bodyJsonObj = null;
//                    JSONArray bodyJsonArray = null;
//                    try {
//                        bodyJsonObj = JSON.parseObject(esComponent.getProxyExecBodyJsonStr().trim());
//                    }catch (JSONException jsonException){
//                        try {
//                            bodyJsonArray = JSON.parseArray(esComponent.getProxyExecBodyJsonStr().trim());
//                        }catch (JSONException jex){
//                            log.error(jex.getMessage());
//                            throw new ServiceException("节点["+currentNodeName+"]中ES组件请求内容转JSON错误!");
//                        }
//                    }
//                    if(bodyJsonObj!=null){
//                        esComponent.setProxyExecBodyJsonStr(bodyJsonObj.toJSONString());
//                    }
//                    if(bodyJsonArray!=null){
//                        esComponent.setProxyExecBodyJsonStr(bodyJsonArray.toJSONString());
//                    }
                    JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor",samplerEle,"前置-ES组件预处理",shellParseUtil.getESRequestBodyAsString(esComponent.getProxyExecBodyJsonStr()),null,null,null,null,true);
                    JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", samplerEle, "前置-ES组件", null, workspace+"shell/esComponent.groovy", null, null,varsFileName,true);
                }
            } else if(LinkMapTypeEnum.JDBC==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))){
                if (SqlValidationUtils.hasDdlDml(componentJSON.getString("sql"))) {
                    throw new ServiceException("节点["+currentNodeName+"]中有DB组件包含DML语法");
                }
                JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor",samplerEle,"前置-DBSQL预处理",shellParseUtil.getSqlAsString(componentJSON.getString("sql")),null,null,null,null,true);
                JmeterEleUtil.newJDBCRequest(samplerEle, "JDBCPreProcessor", componentJSON.getString("dbId"), "${devops_qc_jdbc_sql}",
                        SqlValidationUtils.isUpdateSql(componentJSON.getString("sql")), true);
                if(componentJSON.containsKey("variableExtraction")) {
                    JSONArray variableExtraction = componentJSON.getJSONArray("variableExtraction");
                    if(variableExtraction!=null && variableExtraction.size()>0) {
                        String varsName = AggregateIdUtil.generateId(AggregateType.SNOWFLAKE);
                        String varsFileName = "./data/" + varsName + ".json";
                        if(isDebug){
                            varsFileName = taskWorkSpace+varsName+".json";
                            File file = new File("/data/jmeter/"+taskId+"/"+varsName+".json");
                            try {
                                FileUtils.writeStringToFile(file, JSONArray.toJSONString(variableExtraction), "UTF-8");
                            }catch (IOException ex){
                                log.warn("",ex);
                                throw new ServiceException("节点["+currentNodeName+"]DB变量提取数据文件保存时发生异常！");
                            }
//                            int ossPathSplitIndex = ossPath.lastIndexOf("/");
//                            String taskStatus = ossPath.substring(ossPathSplitIndex+1,ossPath.length());
//                            if(sceneType==2){
//                                ztoOssService.createObject("autojmx", ossPath + "/data/" + varsName + ".json", JSONArray.toJSONString(variableExtraction));
//                            }
                        }else {
                            ztoOssService.createObject("autojmx", ossPath + "/data/" + varsName + ".json", JSONArray.toJSONString(variableExtraction));
                        }
                        JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", samplerEle, "前置-DB变量提取", null, workspace+"shell/jsonPath.groovy", null, null,varsFileName,true);
                    }
                }
            } else if(LinkMapTypeEnum.BEANSHELL_SCRIPTS==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))) {
                if(!StringUtils.isBlank(componentJSON.getString("scripts"))) {
                    JmeterEleUtil.newBeanshellSampler("BeanShellPreProcessor",samplerEle,"自定义前置脚本",componentJSON.getString("scripts"),null,null,null,true);
                }
            }
        }
    }

    private void createPostComponent(String currentNodeName,Element samplerEle,JSONArray postComponents,String ossPath,boolean isDebug,String taskId,String workspace,Integer sceneType) {
        String taskWorkSpace = "";
        if(isDebug){
            taskWorkSpace = workspace + "${devops_qc_task_id}" + "/";
        }
        for(int i=0;i<postComponents.size();i++){
            JSONObject componentJSON = postComponents.getJSONObject(i);
            if(StringUtil.isEmpty(componentJSON.getString("type"))){
                throw new ServiceException("节点["+currentNodeName+"]中有组件类型为空的情况！");
            }
            if(LinkMapTypeEnum.SLEEP==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))){
                SleepComponent sleepComponent = JSON.toJavaObject(componentJSON,SleepComponent.class);
                JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor",samplerEle,"后置-定时器",null,workspace+"shell/sleep.groovy",null,null,sleepComponent.getMilliSecond().toString(),true);
            }else if(LinkMapTypeEnum.EXTRACTOR_JSON==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))){
                JsonExtractorComponent jsonExtractorComponent = JSON.toJavaObject(componentJSON,JsonExtractorComponent.class);
                JmeterEleUtil.newJSONPostProcessor(samplerEle,jsonExtractorComponent,true);
            }else if(LinkMapTypeEnum.RegularExpressionExtractor==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))) {
                JSONArray variableExtraction = componentJSON.getJSONArray("variableExtraction");
                if(variableExtraction!=null && variableExtraction.size()>0) {
                    String varsName = AggregateIdUtil.generateId(AggregateType.SNOWFLAKE);
                    String varsFileName = "./data/" + varsName + ".json";
                    if(isDebug){
                        varsFileName = taskWorkSpace+varsName+".json";
                        File file = new File("/data/jmeter/"+taskId+"/"+varsName+".json");
                        try {
                            FileUtils.writeStringToFile(file, JSONArray.toJSONString(variableExtraction), "UTF-8");
                        }catch (IOException ex){
                            log.warn("",ex);
                            throw new ServiceException("节点["+currentNodeName+"]正则表达式提取数据文件保存时发生异常！");
                        }
                    }else {
                        ztoOssService.createObject("autojmx", ossPath + "/data/" + varsName + ".json", JSONArray.toJSONString(variableExtraction));
                    }
                    JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor", samplerEle, "后置-正则表达式变量提取", null, workspace+"shell/regularExpressionExtractor.groovy", null, null,varsFileName,true);
                }
            }else if(LinkMapTypeEnum.REDIS==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))) {
                RedisComponent redisComponent = JSON.parseObject(componentJSON.toJSONString(),RedisComponent.class);
                if(redisComponent.getVariableExtraction()!=null && redisComponent.getVariableExtraction().size()>0) {
                    String varsName = AggregateIdUtil.generateId(AggregateType.SNOWFLAKE);
                    String varsFileName = "./data/" + varsName + ".json";
                    String configPath = "data/";
                    if(isDebug){
                        configPath = config.getDebugWorkspace()+"/${devops_qc_task_id}/";
                        varsFileName = taskWorkSpace+varsName+".json";
                        File file = new File("/data/jmeter/"+taskId+"/"+varsName+".json");
                        try {
                            FileUtils.writeStringToFile(file, JSONObject.toJSONString(redisComponent), "UTF-8");
                        }catch (IOException ex){
                            log.warn("",ex);
                            throw new ServiceException("节点["+currentNodeName+"]后置Redis组件提取数据文件保存时发生异常！");
                        }
                    }else {
                        ztoOssService.createObject("autojmx", ossPath + "/data/" + varsName + ".json", JSONObject.toJSONString(redisComponent));
                    }
                    JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor", samplerEle, "后置-redis组件", null, workspace+"shell/redisComponent.groovy", null, null,varsFileName+" "+configPath,true);
                }
            } else if(LinkMapTypeEnum.ES==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))) {
                ESComponent esComponent = JSON.parseObject(componentJSON.toJSONString(),ESComponent.class);
                if(esComponent.getVariableExtraction()!=null && esComponent.getVariableExtraction().size()>0) {
                    ProxyExecDTO dto = new ProxyExecDTO();
                    dto.setPath(esComponent.getPath());
                    dto.setMethod(esComponent.getRequestType());
                    dto.setClusterId(esComponent.getClusterId());
                    dto.setUserName(Strings.EMPTY);
                    dto.setBodyString(JSON.parseObject(esComponent.getScript()));
//                    String sign = zbaseService.generateEsSign(dto);
                    esComponent.setProxyExecBodyJsonStr(JSON.toJSONString(dto));
                    String varsName = AggregateIdUtil.generateId(AggregateType.SNOWFLAKE);
                    String varsFileName = "./data/" + varsName + ".json";
                    if(isDebug){
                        varsFileName = taskWorkSpace+varsName+".json";
                        File file = new File("/data/jmeter/"+taskId+"/"+varsName+".json");
                        try {
                            FileUtils.writeStringToFile(file, JSONObject.toJSONString(esComponent), "UTF-8");
                        }catch (IOException ex){
                            log.warn("",ex);
                            throw new ServiceException("节点["+currentNodeName+"]后置ES组件提取数据文件保存时发生异常！");
                        }
                    }else {
                        ztoOssService.createObject("autojmx", ossPath + "/data/" + varsName + ".json", JSONObject.toJSONString(esComponent));
                    }
                    JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor",samplerEle,"后置-ES组件预处理",shellParseUtil.getESRequestBodyAsString(esComponent.getProxyExecBodyJsonStr()),null,null,null,null,true);
                    JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor", samplerEle, "后置-ES组件", null, workspace+"shell/esComponent.groovy", null, null,varsFileName,true);
                }
            } else if(LinkMapTypeEnum.JDBC==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))){
                if (SqlValidationUtils.hasDdlDml(componentJSON.getString("sql"))) {
                    throw new ServiceException("节点["+currentNodeName+"]中有DB组件包含DML语法");
                }
                JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor",samplerEle,"后置-DBSQL预处理",shellParseUtil.getSqlAsString(componentJSON.getString("sql")),null,null,null,null,true);
                JmeterEleUtil.newJDBCRequest(samplerEle, "JDBCPostProcessor", componentJSON.getString("dbId"), "${devops_qc_jdbc_sql}",
                        SqlValidationUtils.isUpdateSql(componentJSON.getString("sql")), true);
                if(componentJSON.containsKey("variableExtraction")) {
                    JSONArray variableExtraction = componentJSON.getJSONArray("variableExtraction");
                    if(variableExtraction!=null && variableExtraction.size()>0) {
                        String varsName = AggregateIdUtil.generateId(AggregateType.SNOWFLAKE);
                        String varsFileName = "./data/" + varsName + ".json";
                        if(isDebug){
                            varsFileName = taskWorkSpace+varsName+".json";
                            File file = new File("/data/jmeter/"+taskId+"/"+varsName+".json");
                            try {
                                FileUtils.writeStringToFile(file, JSONArray.toJSONString(variableExtraction), "UTF-8");
                            }catch (IOException ex){
                                log.warn("",ex);
                                throw new ServiceException("节点["+currentNodeName+"]DB变量提取数据文件保存时发生异常！");
                            }
                        }else {
                            ztoOssService.createObject("autojmx", ossPath + "/data/" + varsName + ".json", JSONArray.toJSONString(variableExtraction));
                        }
                        JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor", samplerEle, "后置-DB变量提取", null, workspace+"shell/jsonPath.groovy", null, null,varsFileName,true);
                    }
                }
            }else if(LinkMapTypeEnum.ASSERT_BEANSHELL==LinkMapTypeEnum.valueOf(componentJSON.getString("type")) || LinkMapTypeEnum.IF_ASSERT_BEANSHELL==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))){
                if(StringUtil.isNotEmpty(componentJSON.getString("language")) && "Beanshell".equals(componentJSON.getString("language"))) {
                    if(StringUtil.isNotEmpty(componentJSON.getString("expected"))) {
                        JmeterEleUtil.newBeanShellAssertion("BeanShellAssertion",samplerEle,"BeanShell断言",componentJSON.getString("expected"),null,null,null,true);
                    }else {
                        throw new ServiceException("节点["+currentNodeName+"]的断言组件生成失败！");
                    }
                }else{
                    String assertStr = shellParseUtil.getAssertsAsString(componentJSON);
                    if(StringUtil.isNotEmpty(assertStr)){

//                    String assertGroovyFileName = AggregateIdUtil.generateId(AggregateType.SNOWFLAKE)+".groovy";
//                    ztoOssService.createObject("autojmx",ossPath+"/shell/"+assertGroovyFileName,assertStr);
                        JmeterEleUtil.newJSR223Sampler("JSR223Assertion",samplerEle,componentJSON.getString("name"),assertStr,"",null,null,null,true);
                    }else {
                        throw new ServiceException("节点["+currentNodeName+"]的断言组件生成失败！");
                    }
                }
            }else if(LinkMapTypeEnum.ASSERT_JSONPATH==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))){
                JmeterEleUtil.newJSONPathAssert(samplerEle,JSON.toJavaObject(componentJSON,JSONAssertComponent.class),true);
            }else if(LinkMapTypeEnum.ASSERT_RESPONSE==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))){
                JmeterEleUtil.newResponseAssert(samplerEle,JSON.toJavaObject(componentJSON,ResponseAssertComponent.class),true);
            }else if(LinkMapTypeEnum.BEANSHELL_SCRIPTS==LinkMapTypeEnum.valueOf(componentJSON.getString("type"))) {
                if(!StringUtils.isBlank(componentJSON.getString("scripts"))) {
                    JmeterEleUtil.newBeanshellSampler("BeanShellPostProcessor",samplerEle,"自定义后置脚本",
                            componentJSON.getString("scripts"),null,null,null,true);
                }
            }
        }
    }


    private JmeterDocumentFullInfo createNewJmeterDocument(String sceneCode, Integer sceneVersion, String sceneName, boolean isDebug,
                                                           String taskId, String productCode, String rumMethod) {
        String taskWorkSpace = "";
        if(isDebug){
            taskWorkSpace = config.getDebugWorkspace()+"/"+taskId+"/";
        }
        JmeterDocumentFullInfo jmeterDocumentFullInfo = new JmeterDocumentFullInfo();
        Document document = DocumentHelper.createDocument();
        Element rootElement = document.addElement("jmeterTestPlan");
        rootElement.addAttribute("version","1.2");
        rootElement.addAttribute("properties","5.0");
        rootElement.addAttribute("jmeter","5.4.1");
        Element rootHashTree = rootElement.addElement("hashTree");
        Element planTest = rootHashTree.addElement("TestPlan");
        planTest.addAttribute("guiclass","TestPlanGui");
        planTest.addAttribute("testclass","TestPlan");
        planTest.addAttribute("testname",sceneName+"_"+sceneCode+"_"+sceneVersion);
        planTest.addAttribute("enabled","true");
        JmeterEleUtil.newEle(planTest,"stringProp",new HashMap<String, String>(){
            {
                put("name", "TestPlan.comments");
            } },"");
        JmeterEleUtil.newEle(planTest,"boolProp",new HashMap<String, String>(){
            {
                put("name", "TestPlan.functional_mode");
            } },"false");
        JmeterEleUtil.newEle(planTest,"boolProp",new HashMap<String, String>(){
            {
                put("name", "tearDown_on_shutdown");
            } },"true");
        String serializeEnable = SceneRunningSetEnum.SERIAL.getValue().equals(rumMethod) ? "true" : "false";
        JmeterEleUtil.newEle(planTest,"boolProp",new HashMap<String, String>(){
            {
                put("name", "TestPlan.serialize_threadgroups");
            } },serializeEnable);
        Element planElementProp = JmeterEleUtil.newEle(planTest,"elementProp",new HashMap<String, String>(){
            {
                put("name", "TestPlan.user_defined_variables");
                put("elementType","Arguments");
                put("guiclass","ArgumentsPanel");
                put("testclass","Arguments");
                put("testname","测试计划变量");
                put("enabled","true");
            } });
        JmeterEleUtil.addUserDefinedVariablesContent(planElementProp,new HashMap<String, String>(){
            {
                put("ztoenv", "${__P(ztoenv,fat)}");
                put("devops_qc_config_thread_exit_flag","${__P(_devops_qc_config_thread_exit_flag_,0)}");
                put("dubbotag", "${__P(dubbotag,base)}");
                put("taskId", "${__P(taskId,default)}");
                put("devops_qc_workspace","${__P(devops_qc_workspace,)}");
            } },true);
        JmeterEleUtil.newEle(planTest,"stringProp",new HashMap<String, String>(){
            {
                put("name", "TestPlan.user_define_classpath");
            } },"");

        Element planContentHashTree = rootHashTree.addElement("hashTree");
        /**
         * 用户变量*
         */
        String finalTaskWorkSpace = taskWorkSpace;
        JmeterEleUtil.addUserDefinedVariablesContent(planContentHashTree,new HashMap<String, String>(){
            {
                String variableFile = "./variable/variable.json";
                if(isDebug){
                    variableFile = finalTaskWorkSpace + "variable.json";
                }
                put("devops_qc_variable",variableFile);
                put("devops_qc_ztoenv","${__P(ztoenv,)}");
                put("devops_qc_config_thread_exit_flag","${__P(_devops_qc_config_thread_exit_flag_,0)}");
                put("devops_qc_dubbotag","${__P(dubbotag,prod)}");
                put("devops_qc_task_id","${__P(taskId,fat)}");
//                put("variableFilePath","true");
            } },false);
        // 添加自定义变量
        this.addUserDefinedVariablesContent(productCode, sceneCode, planContentHashTree);

//        Element setupThreadGroupHashTree = JmeterEleUtil.newSetUpThreadGroup(planContentHashTree,"setupThreadGroup",true);

        jmeterDocumentFullInfo.setDocument(document);
        jmeterDocumentFullInfo.setRootHashTree(rootHashTree);
        jmeterDocumentFullInfo.setPlanTest(planTest);
        jmeterDocumentFullInfo.setPlanContentHashTree(planContentHashTree);
        return jmeterDocumentFullInfo;
    }

    /**
     * 线程组广度优先，用例深度优先
     * 测试测试片段后，此方法过期*
     *
     * @param document
     * @return
     * @throws DocumentException
     */
//    public AutomaticNode GetJmeterPlanTreeOld(Document document) throws DocumentException {
//        Element root = document.getRootElement();
//        AutomaticNode automaticRoot = null;
//        List<AutomaticNode> groupList = new ArrayList<>();
//        List<Element> list = new ArrayList<Element>();
//        list.add(root);
//
//        List<String> groupName = new ArrayList<>();
//        List<String> testCaseFullName = new ArrayList<>();
//
//        Node planNode = root.selectSingleNode("//TestPlan");
//        if (planNode == null) {
//            automaticRoot = new AutomaticNode();
//            automaticRoot.setType(AutomaticNodeTypeEnum.TestPlan.getDesc());
//            automaticRoot.setName("测试计划");
//            automaticRoot.setEnable(true);
//        }
//
//        while (list.size() > 0) {
//            Element element = null;
//            Element ele = null;
//            int flag = 0;
//
//            Iterator ite = list.iterator();
//            if (ite.hasNext()) {
//                ele = (Element) ite.next();
//                ite.remove();
//            }
//            if (ele != null) {
//                if (ele.getName().equals(AutomaticNodeTypeEnum.TestPlan.getDesc())) {
//                    automaticRoot = new AutomaticNode();
//                    automaticRoot.setType(AutomaticNodeTypeEnum.TestPlan.getDesc());
//                    automaticRoot.setName(ele.attributeValue(AutomaticConstant.JMX_TESTNAME));
//                    automaticRoot.setXpath(ele.getUniquePath());
//                    if (ele.attributeValue(AutomaticConstant.JMX_ENABLED) == null) {
//                        automaticRoot.setEnable(true);
//                    } else {
//                        if (ele.attributeValue(AutomaticConstant.JMX_ENABLED).toLowerCase().equals("true")) {
//                            automaticRoot.setEnable(true);
//                        } else {
//                            automaticRoot.setEnable(false);
//                        }
//                    }
//                    automaticRoot.setComment(ele.attributeValue(AutomaticConstant.JMX_TESTPLAN_COMMENTS));
//                    List testPlanElements = ele.elements();
//                    for (Object teO : testPlanElements) {
//                        Element te = (Element) teO;
//                        if (te.attributeValue(AutomaticConstant.JMX_NAME) != null) {
//                            if (te.attributeValue(AutomaticConstant.JMX_NAME).equals(AutomaticConstant.JMX_TESTPLAN_COMMENTS)) {
//                                automaticRoot.setComment(te.getText());
//                                break;
//                            }
//                        }
//                    }
//                }
//                AutomaticNode autoGroup = null;
//                for (Iterator i = ele.elementIterator(); (i != null) && (i.hasNext()); ) {
//                    element = (Element) i.next();
//                    list.add(element);
//                    if (element != null && element.getName().equals(AutomaticNodeTypeEnum.ThreadGroup.getDesc()) && flag == 0) {
//                        if (groupName.indexOf(element.attributeValue("testname")) >= 0) {
//                            /**
//                             * 说明用例组有重名
//                             */
//                            throw new ServiceException(element.attributeValue("testname") + ":线程组不能重名!");
//                        }
//                        groupName.add(element.attributeValue("testname"));
//                        System.out.println(element.getUniquePath() + " --" + element.getText() + element.attributeValue("testname"));
//                        flag = 1;
//                        autoGroup = new AutomaticNode();
//                        autoGroup.setType(AutomaticNodeTypeEnum.ThreadGroup.getDesc());
//                        autoGroup.setName(element.attributeValue(AutomaticConstant.JMX_TESTNAME));
//                        if (element.attributeValue(AutomaticConstant.JMX_ENABLED).toLowerCase().equals("true")) {
//                            autoGroup.setEnable(true);
//                        } else {
//                            autoGroup.setEnable(false);
//                        }
//                        autoGroup.setXpath(element.getUniquePath());
//                        List groupElements = element.elements();
//                        for (Object geO : groupElements) {
//                            Element ge = (Element) geO;
//                            if (ge.attributeValue(AutomaticConstant.JMX_NAME).equals(AutomaticConstant.JMX_TESTPLAN_COMMENTS)) {
//                                autoGroup.setComment(ge.getText());
//                                continue;
//                            }
//                            if (ge.attributeValue(AutomaticConstant.JMX_NAME).equals(AutomaticConstant.JMX_THREADGROUP_MAIN_CONTROLLER) && autoGroup.getEnable()) {
//                                if (ge.element(AutomaticConstant.JMX_INTPROP) != null) {
//                                    //抛异常，线程组不能无限循环
//                                    throw new ServiceException(autoGroup.getName() + ":线程组不能设置为无限循环!");
//                                }
//                                continue;
//                            }
//                            if (ge.attributeValue(AutomaticConstant.JMX_NAME).equals(AutomaticConstant.JMX_SCHEDULER) && autoGroup.getEnable()) {
//                                if (ge.getTextTrim().equals("true")) {
//                                    throw new ServiceException(autoGroup.getName() + ":线程组不允许设置调度器!");
//                                }
//                                continue;
//                            }
//                        }
//                        groupList.add(autoGroup);
//                        continue;
//                    }
//                    if (element != null && element.getName().equals("hashTree") && flag == 1) {
//                        System.out.println(element.getUniquePath() + " --" + element.getText());
//                        flag = 0;
//                        //对此element进行深度优先遍历，将用例提取出来
//                        Stack<Element> tcElement = new Stack<>();
//                        tcElement.push(element);
//                        List<AutomaticNode> automaticNodeList = new ArrayList<>();
//                        while (!tcElement.isEmpty()) {
//                            Element currentTcTree = tcElement.pop();
//                            Iterator<Element> iteratorTc = currentTcTree.elementIterator();//获取当前节点的子节点
//
//                            int indexType = samplerList.indexOf(currentTcTree.getName());
//                            if (indexType >= 0) {
//                                String fullTcName = autoGroup.getName() + currentTcTree.attributeValue("testname");
//                                if (testCaseFullName.indexOf(fullTcName) >= 0) {
//                                    throw new ServiceException("线程组[" + autoGroup.getName() + "]下的用例[" + currentTcTree.attributeValue("testname") + "]名称重复了!");
//                                }
//                                testCaseFullName.add(fullTcName);
//                                AutomaticNode automaticNode = new AutomaticNode();
//                                automaticNode.setName(currentTcTree.attributeValue(AutomaticConstant.JMX_TESTNAME));
//                                if (currentTcTree.attributeValue(AutomaticConstant.JMX_ENABLED).toLowerCase().equals("true")) {
//                                    automaticNode.setEnable(true);
//                                } else {
//                                    automaticNode.setEnable(false);
//                                }
//                                if (!autoGroup.getEnable()) {
//                                    automaticNode.setEnable(false);
//                                }
//                                automaticNode.setXpath(currentTcTree.getUniquePath());
//                                if (currentTcTree.getName().equals(AutomaticConstant.DubboSample)) {
//                                    automaticNode.setType(AutomaticNodeTypeEnum.DubboSample.getDesc());
//                                    Node ifNameNode = currentTcTree.selectSingleNode("stringProp[@name='FIELD_DUBBO_INTERFACE']");
//                                    if (ifNameNode != null) {
//                                        automaticNode.setInterfaceName(ifNameNode.getText());
//                                    }
//                                } else if (currentTcTree.getName().equals(AutomaticConstant.ZMSSampler)) {
//                                    automaticNode.setType(AutomaticNodeTypeEnum.ZMSSampler.getDesc());
//                                } else {
//                                    automaticNode.setType(currentTcTree.getName());
//                                }
//
//                                List tcElements = currentTcTree.elements();
//                                for (Object tceO : tcElements) {
//                                    Element tce = (Element) tceO;
//                                    if (tce.attributeValue(AutomaticConstant.JMX_NAME).equals(AutomaticConstant.JMX_TESTPLAN_COMMENTS)) {
//                                        automaticNode.setComment(tce.getText());
//                                        break;
//                                    }
//                                }
//                                System.out.println(currentTcTree.getUniquePath() + "-----" + currentTcTree.getTextTrim() + "------" + currentTcTree.attributeValue("testname"));
//                                automaticNodeList.add(automaticNode);
//                                continue;
//                            }
//
//                            while (iteratorTc.hasNext()) {//判断集合中元素是否遍历完毕
//                                Element nextTc = iteratorTc.next();
//                                if (nextTc != null) {
//                                    tcElement.push(nextTc);
//                                }
//                            }
//                        }
//                        if (automaticNodeList.size() > 0) {
//                            automaticNodeList = Lists.reverse(automaticNodeList);
//                            autoGroup.setAutomaticNodeList(automaticNodeList);
//                        }
//                    }
//                }
//            }
//        }
//        if (groupList.size() > 0) {
//            automaticRoot.setAutomaticNodeList(groupList);
//        }
//        log.info("Jmx解析结果为：>>> {}", JSON.toJSONString(automaticRoot));
//        return automaticRoot;
//    }

//    public static String readTxt(String filePath) {
//        String result = "";
//        try {
//            File file = new File(filePath);
//            if (file.isFile() && file.exists()) {
//                InputStreamReader isr = new InputStreamReader(new FileInputStream(file), "utf-8");
//                BufferedReader br = new BufferedReader(isr);
//                String lineTxt = null;
//                int num = 0;
//                long time1 = System.currentTimeMillis();
//                while ((lineTxt = br.readLine()) != null) {
//                    result = result + lineTxt;
//                }
//                long time2 = System.currentTimeMillis();
//                long time = time1 - time2;
//                System.out.println("共花费" + time + "秒");
//                br.close();
//            } else {
//                System.out.println("文件不存在!");
//            }
//        } catch (Exception e) {
//            System.out.println("文件读取错误!");
//        }
//        return result;
//    }


    /**
     * 支持测试片段，重写解析
     */
    private AutomaticEleAndHashTree GetJmeterPlanEleAndHashTree(Element root) throws ServiceException {
        AutomaticEleAndHashTree eleAndHashTree = null;
        Element rootHashTree = (Element) root.selectSingleNode("hashTree");
        for (Iterator i = rootHashTree.elementIterator(); (i != null) && (i.hasNext()); ) {
            Element elementTP = (Element) i.next();
            if (elementTP != null && elementTP.getName().equals(AutomaticNodeTypeEnum.TestPlan.getDesc())) {
                if (eleAndHashTree == null) {
                    eleAndHashTree = new AutomaticEleAndHashTree();
                }
                eleAndHashTree.setMainEle(elementTP);
            }
            if (elementTP != null && elementTP.getName().equals("hashTree") && eleAndHashTree != null) {
                eleAndHashTree.setHashTree(elementTP);
                return eleAndHashTree;
            }
        }

        return eleAndHashTree;
    }

    @Override
    public AutomaticNode getJmeterPlanTree(Document document) throws ServiceException {
        rename(document);
        Map<String,List<String>> threadTcNames = new HashMap<>();
        AutomaticNode result = GetAutoNodeByJmeterPlan(document,threadTcNames);
        if(result!=null){
            result.reSetChildEnable();
        }
        return result;
    }



    public AutomaticNode GetAutoNodeByJmeterPlan(Document document,Map<String,List<String>> threadTcNames) throws ServiceException {
        Element root = document.getRootElement();
        AutomaticNode automaticRoot = null;
//        List<String> tcNameList = new ArrayList<>();
        List<String> groupNameList = new ArrayList<>();
        List<String> controllerNameList = new ArrayList<>();
        Map<String, AutomaticNode> tfMap = new HashMap<>();
        List<Node> icNodes = root.selectNodes("//IncludeController");
        if (!CollectionUtils.isEmpty(icNodes)) {
            throw new ServiceException("脚本中不允许使用IncludeController!");
        }

//        List<Node> wNodes = root.selectNodes("//WhileController");
//        if (wNodes != null && wNodes.size() > 0) {
//            throw new ServiceException("脚本中不允许使用While控制器!");
//        }
//        List<Node> lNodes = root.selectNodes("//LoopController");
//        if (lNodes != null && lNodes.size() > 0) {
//            for (Node lNode : lNodes) {
//                Node loopsNode = lNode.selectSingleNode(".//stringProp[@name='LoopController.loops']");
//                if (loopsNode != null) {
//                    int loopInt = Integer.valueOf(loopsNode.getStringValue().trim()).intValue();
//                    if (loopInt < 1 || loopInt > 300) {
//                        throw new ServiceException("循环控制器的循环次数不能大于300次!");
//                    }
//                }
//            }
//        }

        automaticRoot = new AutomaticNode();
        AutomaticEleAndHashTree eleAndHashTreeTP = GetJmeterPlanEleAndHashTree(root);
        if (eleAndHashTreeTP == null) {
            automaticRoot.setType(AutomaticNodeTypeEnum.TestPlan.getDesc());
            automaticRoot.setName("测试计划");
            automaticRoot.setEnable(true);
            automaticRoot.setAutomaticNodeList(new ArrayList<>());
            return automaticRoot;
        }

        Element eleTP = eleAndHashTreeTP.getMainEle();
        automaticRoot.setType(AutomaticNodeTypeEnum.TestPlan.getDesc());
        automaticRoot.setName(eleTP.attributeValue(AutomaticConstant.JMX_TESTNAME));
        automaticRoot.setXpath(eleTP.getUniquePath());
        if (eleTP.attributeValue(AutomaticConstant.JMX_ENABLED) == null) {
            automaticRoot.setEnable(true);
        } else {
            if (eleTP.attributeValue(AutomaticConstant.JMX_ENABLED).toLowerCase().equals("true")) {
                automaticRoot.setEnable(true);
            } else {
                automaticRoot.setEnable(false);
            }
        }
        automaticRoot.setComment(eleTP.attributeValue(AutomaticConstant.JMX_TESTPLAN_COMMENTS));
        List testPlanElements = eleTP.elements();
        for (Object teO : testPlanElements) {
            Element te = (Element) teO;
            if (te.attributeValue(AutomaticConstant.JMX_NAME) != null) {
                if (te.attributeValue(AutomaticConstant.JMX_NAME).equals(AutomaticConstant.JMX_TESTPLAN_COMMENTS)) {
                    automaticRoot.setComment(te.getText());
                    break;
                }
            }
        }

        Element mainEle = null;
        /**
         * 先把的有测试片段提取出来*
         */
        for (Element elementT : eleAndHashTreeTP.getHashTree().elements()) {
            if (elementT != null && elementT.getName().equals(AutomaticConstant.TestFragmentController) && mainEle == null) {
                mainEle = elementT;
            }
            if (elementT != null && elementT.getName().equals("hashTree") && mainEle != null) {
                if (mainEle.getName().equals(AutomaticConstant.TestFragmentController)) {
                    AutomaticNode node = getAutoNodeByTestFragment(mainEle, elementT, groupNameList, threadTcNames, controllerNameList, tfMap);
                    if (node != null) {
                        tfMap.put(node.getName(), node);
                    }
                }
                mainEle = null;
            }
        }

        mainEle = null;
        for (Element elementT : eleAndHashTreeTP.getHashTree().elements()) {
            if (elementT != null && elementT.getName().equals(AutomaticNodeTypeEnum.ThreadGroup.getDesc()) && mainEle == null) {
                mainEle = elementT;
            }
            if (elementT != null && elementT.getName().equals("hashTree") && mainEle != null) {
                AutomaticNode node = null;
                if (mainEle.getName().equals(AutomaticNodeTypeEnum.ThreadGroup.getDesc())) {
                    node = getAutoNodeByThreadGroup(mainEle, elementT, groupNameList, threadTcNames, controllerNameList, tfMap);
                }
                if (node != null) {
                    if (automaticRoot.getAutomaticNodeList() == null) {
                        automaticRoot.setAutomaticNodeList(new ArrayList<>());
                    }
                    automaticRoot.getAutomaticNodeList().add(node);
                }
                mainEle = null;
            }
        }

        return automaticRoot;
    }

    /**
     * * 递归提取所需节点
     *
     * @param threadGroup
     * @return
     */
    private AutomaticNode getAutoNodeByThreadGroup(Element threadGroup, Element hashTree, List<String> groupNameList, Map<String,List<String>> threadTcNames, List<String> controllerNameList, Map<String, AutomaticNode> tfMap) throws ServiceException {
        List<Node> nodes = threadGroup.selectNodes(".//" + AutomaticConstant.TestFragmentController);
        if (nodes != null) {
            if (nodes.size() > 0) {
                throw new ServiceException(threadGroup.attributeValue(AutomaticConstant.JMX_TESTNAME) + ":线程组下不允许包含测试片段!");
            }
        }
        AutomaticNode threadGroupNode = new AutomaticNode();
        threadGroupNode.setAutomaticNodeList(new ArrayList<>());
        Element mainEle = null;
        threadGroupNode.setType(AutomaticNodeTypeEnum.ThreadGroup.getDesc());
        threadGroupNode.setName(threadGroup.attributeValue(AutomaticConstant.JMX_TESTNAME));
        if (threadGroup.attributeValue(AutomaticConstant.JMX_ENABLED).toLowerCase().equals("true")) {
            threadGroupNode.setEnable(true);
        } else {
            threadGroupNode.setEnable(false);
        }
        threadGroupNode.setXpath(threadGroup.getUniquePath());
        if (groupNameList.contains(threadGroupNode.getName())) {
            throw new ServiceException(threadGroupNode.getName() + ":线程组或者测试片段不能重名!");
        }
        groupNameList.add(threadGroupNode.getName());
        List groupElements = threadGroup.elements();
        for (Object geO : groupElements) {
            Element ge = (Element) geO;
            if (ge.attributeValue(AutomaticConstant.JMX_NAME).equals(AutomaticConstant.JMX_TESTPLAN_COMMENTS)) {
                threadGroupNode.setComment(ge.getText());
                continue;
            }
            if (ge.attributeValue(AutomaticConstant.JMX_NAME).equals(AutomaticConstant.JMX_THREADGROUP_MAIN_CONTROLLER) && threadGroupNode.getEnable()) {
                if (ge.element(AutomaticConstant.JMX_INTPROP) != null) {
                    //抛异常，线程组不能无限循环
                    throw new ServiceException("线程组【" + threadGroupNode.getName() + "】不能设置为无限循环!");
                }
                Node loopsNode = ge.selectSingleNode(".//stringProp[@name='LoopController.loops']");
                if (loopsNode != null) {
                    int loopInt = Integer.valueOf(loopsNode.getStringValue().trim()).intValue();
                    if (loopInt < 1 || loopInt > 100) {
                        throw new ServiceException("线程组【" + threadGroupNode.getName() + "】的循环次数不能大于100次!");
                    }
                }
                continue;
            }
        }
        for (Element element : hashTree.elements()) {
            int indexSamplerType = samplerList.indexOf(element.getName());
            if (element != null && indexSamplerType >= 0 && mainEle == null) {
                AutomaticNode node = getAutoTestCase(element, threadTcNames, tfMap,threadGroupNode.getName(),threadGroupNode.getEnable());
                if (node != null) {
                    threadGroupNode.getAutomaticNodeList().add(node);
                }
            }

            int indexControllerType = config.getJmeterConfig().getControllerList().indexOf(element.getName());
            if (element != null && indexControllerType >= 0 && mainEle == null) {
                mainEle = element;
            }

            if (element != null && element.getName().equals(AutomaticNodeTypeEnum.ModuleController.getDesc()) && mainEle == null) {
                mainEle = element;

            }

            if (element != null && element.getName().equals("hashTree") && mainEle == null) {
                List<AutomaticNode> otherNodes = getOtherAutoNode(element, threadGroupNode.getEnable(), threadTcNames, controllerNameList, tfMap,threadGroupNode.getName());
                if (otherNodes != null) {
                    for (AutomaticNode tempNode : otherNodes) {
                        threadGroupNode.getAutomaticNodeList().add(tempNode);
                    }
                }
            }
            if (element != null && element.getName().equals("hashTree") && mainEle != null) {
                AutomaticNode node = null;
                int indexCType = config.getJmeterConfig().getControllerList().indexOf(mainEle.getName());
                if (indexCType >= 0) {
                    node = getAutoNodeByController(mainEle, element, threadGroupNode.getEnable(), threadTcNames, controllerNameList, tfMap,threadGroupNode.getName());
                }
                if (mainEle.getName().equals(AutomaticNodeTypeEnum.ModuleController.getDesc())) {
                    node = getAutoByModuleController(mainEle, threadGroupNode.getEnable(), controllerNameList, tfMap);
                }
                if (node != null) {
                    threadGroupNode.getAutomaticNodeList().add(node);
                }
                mainEle = null;
            }
        }

        return threadGroupNode;
    }

    private AutomaticNode getAutoNodeByTestFragment(Element testFragment, Element hashTree, List<String> groupNameList, Map<String,List<String>> threadTcNames, List<String> controllerNameList, Map<String, AutomaticNode> tfMap) throws ServiceException {
        AutomaticNode testFragmentNode = new AutomaticNode();
        testFragmentNode.setAutomaticNodeList(new ArrayList<>());
        Element mainEle = null;
        testFragmentNode.setType(testFragment.getName());
        testFragmentNode.setName(testFragment.attributeValue(AutomaticConstant.JMX_TESTNAME));
        testFragment.attribute(AutomaticConstant.JMX_ENABLED).setValue("true");
        testFragmentNode.setEnable(true);
        testFragmentNode.setXpath(testFragment.getUniquePath());
        if (groupNameList.contains(testFragmentNode.getName())) {
            throw new ServiceException(testFragmentNode.getName() + ":线程组或者测试片段不能重名!");
        }
        groupNameList.add(testFragmentNode.getName());

        for (Element element : hashTree.elements()) {
            int indexSamplerType = samplerList.indexOf(element.getName());
            if (element != null && indexSamplerType >= 0 && mainEle == null) {
                AutomaticNode node = getAutoTestCase(element, threadTcNames, tfMap,null, testFragmentNode.getEnable());
                if (node != null) {
                    testFragmentNode.getAutomaticNodeList().add(node);
                }
            }

            int indexControllerType = config.getJmeterConfig().getControllerList().indexOf(element.getName());
            if (element != null && indexControllerType >= 0 && mainEle == null) {
                mainEle = element;
            }

            if (element != null && element.getName().equals(AutomaticNodeTypeEnum.ModuleController.getDesc()) && mainEle == null) {
                mainEle = element;

            }

            if (element != null && element.getName().equals("hashTree") && mainEle == null) {
                List<AutomaticNode> otherNodes = getOtherAutoNode(element, testFragmentNode.getEnable(), threadTcNames, controllerNameList, tfMap,null);
                if (otherNodes != null) {
                    for (AutomaticNode tempNode : otherNodes) {
                        testFragmentNode.getAutomaticNodeList().add(tempNode);
                    }
                }
            }
            if (element != null && element.getName().equals("hashTree") && mainEle != null) {
                AutomaticNode node = null;
                int indexCType = config.getJmeterConfig().getControllerList().indexOf(mainEle.getName());
                if (indexCType >= 0) {
                    node = getControllerNodeByTestFragment(mainEle, element, groupNameList, threadTcNames, controllerNameList, tfMap);
                }
                if (mainEle.getName().equals(AutomaticNodeTypeEnum.ModuleController.getDesc())) {
                    node = getAutoByModuleController(mainEle, controllerNameList, tfMap);
                }
                if (node != null) {
                    testFragmentNode.getAutomaticNodeList().add(node);
                }
                mainEle = null;
            }
        }


        return testFragmentNode;
    }

    /**
     * * 测试片段向下解析控制器时，调用此方法
     *
     * @param controllerEle
     * @param hashTree
     * @return
     * @throws ServiceException
     */
    private AutomaticNode getControllerNodeByTestFragment(Element controllerEle, Element hashTree, List<String> groupNameList, Map<String,List<String>> threadTcNames, List<String> controllerNameList, Map<String, AutomaticNode> tfMap) throws ServiceException {
        AutomaticNode controllerNode = new AutomaticNode();
        controllerNode.setAutomaticNodeList(new ArrayList<>());
        Element mainEle = null;
        controllerNode.setType(controllerEle.getName());
        controllerNode.setName(controllerEle.attributeValue(AutomaticConstant.JMX_TESTNAME));
        if (controllerEle.attributeValue(AutomaticConstant.JMX_ENABLED).toLowerCase().equals("true")) {
            controllerNode.setEnable(true);
        } else {
            controllerNode.setEnable(false);
        }
        controllerNode.setXpath(controllerEle.getUniquePath());
//        if (controllerNameList.contains(controllerNode.getName())) {
//            throw new ServiceException(controllerNode.getName() + ":控制器名称不能重名!");
//        }
//        controllerNameList.add(controllerNode.getName());
        for (Element element : hashTree.elements()) {
            int indexSamplerType = samplerList.indexOf(element.getName());
            if (element != null && indexSamplerType >= 0 && mainEle == null) {
                AutomaticNode node = getAutoTestCase(element,threadTcNames, tfMap,null, controllerNode.getEnable());
                if (node != null) {
                    controllerNode.getAutomaticNodeList().add(node);
                }
            }

            int indexControllerType = config.getJmeterConfig().getControllerList().indexOf(element.getName());
            if (element != null && indexControllerType >= 0 && mainEle == null) {
                mainEle = element;
            }

            if (element != null && element.getName().equals(AutomaticNodeTypeEnum.ModuleController.getDesc()) && mainEle == null) {
                mainEle = element;

            }

            if (element != null && element.getName().equals("hashTree") && mainEle != null) {
                AutomaticNode node = null;
                int indexCType = config.getJmeterConfig().getControllerList().indexOf(mainEle.getName());
                if (indexCType >= 0) {
                    node = getControllerNodeByTestFragment(mainEle, element, groupNameList, threadTcNames, controllerNameList, tfMap);
                }
                if (mainEle.getName().equals(AutomaticNodeTypeEnum.ModuleController.getDesc())) {
                    node = getAutoByModuleController(mainEle, controllerNameList, tfMap);
                }
                if (node != null) {
                    controllerNode.getAutomaticNodeList().add(node);
                }
                mainEle = null;
            }
        }

        return controllerNode;
    }

    /**
     * * 非测试片断直接解析控制器时使用如下方法
     *
     * @param controllerEle
     * @param hashTree
     * @param parentEnable
     * @return
     * @throws ServiceException
     */
    private AutomaticNode getAutoNodeByController(Element controllerEle, Element hashTree, boolean parentEnable, Map<String,List<String>> threadTcNames, List<String> controllerNameList, Map<String, AutomaticNode> tfMap,String threadName) throws ServiceException {
        AutomaticNode controllerNode = new AutomaticNode();
        controllerNode.setAutomaticNodeList(new ArrayList<>());
        Element mainEle = null;
        controllerNode.setType(controllerEle.getName());
        controllerNode.setName(controllerEle.attributeValue(AutomaticConstant.JMX_TESTNAME));
        if (controllerEle.attributeValue(AutomaticConstant.JMX_ENABLED).toLowerCase().equals("true")) {
            controllerNode.setEnable(true);
        } else {
            controllerNode.setEnable(false);
        }
        if (!parentEnable) {
            controllerNode.setEnable(false);
        }
        controllerNode.setXpath(controllerEle.getUniquePath());
//        if (controllerNameList.contains(controllerNode.getName())) {
//            throw new ServiceException(controllerNode.getName() + ":控制器名称不能重名!");
//        }
//        controllerNameList.add(controllerNode.getName());
        for (Element element : hashTree.elements()) {
            int indexSamplerType = samplerList.indexOf(element.getName());
            if (element != null && indexSamplerType >= 0 && mainEle == null) {
                AutomaticNode node = getAutoTestCase(element, threadTcNames, tfMap,threadName, controllerNode.getEnable());
                if (node != null) {
                    controllerNode.getAutomaticNodeList().add(node);
                }
            }

            int indexControllerType = config.getJmeterConfig().getControllerList().indexOf(element.getName());
            if (element != null && indexControllerType >= 0 && mainEle == null) {
                mainEle = element;
            }

            if (element != null && element.getName().equals(AutomaticNodeTypeEnum.ModuleController.getDesc()) && mainEle == null) {
                mainEle = element;

            }
            if (element != null && element.getName().equals("hashTree") && mainEle == null) {
                List<AutomaticNode> otherNodes = getOtherAutoNode(element, controllerNode.getEnable(), threadTcNames, controllerNameList, tfMap,threadName);
                if (otherNodes != null) {
                    for (AutomaticNode tempNode : otherNodes) {
                        controllerNode.getAutomaticNodeList().add(tempNode);
                    }
                }
            }
            if (element != null && element.getName().equals("hashTree") && mainEle != null) {
                AutomaticNode node = null;
                int indexCType = config.getJmeterConfig().getControllerList().indexOf(mainEle.getName());
                if (indexCType >= 0) {
                    node = getAutoNodeByController(mainEle, element, controllerNode.getEnable(), threadTcNames, controllerNameList, tfMap,threadName);
                }
                if (mainEle.getName().equals(AutomaticNodeTypeEnum.ModuleController.getDesc())) {
                    node = getAutoByModuleController(mainEle, parentEnable, controllerNameList, tfMap);
                }
                if (node != null) {
                    controllerNode.getAutomaticNodeList().add(node);
                }
                mainEle = null;
            }
        }

        return controllerNode;
    }

    private AutomaticNode getAutoByModuleController(Element moduleControllerEle, List<String> controllerNameList, Map<String, AutomaticNode> tfMap) throws ServiceException {
        AutomaticNode moduleControllerNode = new AutomaticNode();
        moduleControllerNode.setAutomaticNodeList(new ArrayList<>());
        moduleControllerNode.setType(moduleControllerEle.getName());
        moduleControllerNode.setName(moduleControllerEle.attributeValue(AutomaticConstant.JMX_TESTNAME));
        if (moduleControllerEle.attributeValue(AutomaticConstant.JMX_ENABLED).toLowerCase().equals("true")) {
            moduleControllerNode.setEnable(true);
        } else {
            moduleControllerNode.setEnable(false);
        }
        moduleControllerNode.setXpath(moduleControllerEle.getUniquePath());
//        if (controllerNameList.contains(moduleControllerEle.getName())) {
//            throw new ServiceException(moduleControllerNode.getName() + ":控制器名称不能重名!");
//        }
//        controllerNameList.add(moduleControllerNode.getName());
        AutomaticNode findNode = findModuleEle(moduleControllerEle, tfMap);
        if (findNode.getType().equals(AutomaticConstant.TestFragmentController)) {
            moduleControllerNode.setAutomaticNodeList(findNode.getAutomaticNodeList());
        } else {
            moduleControllerNode.getAutomaticNodeList().add(findNode);
        }

        return moduleControllerNode;
    }

    private AutomaticNode getAutoByModuleController(Element moduleControllerEle, boolean parentEnable, List<String> controllerNameList, Map<String, AutomaticNode> tfMap) throws ServiceException {
        AutomaticNode moduleControllerNode = new AutomaticNode();
        moduleControllerNode.setAutomaticNodeList(new ArrayList<>());
        moduleControllerNode.setType(moduleControllerEle.getName());
        moduleControllerNode.setName(moduleControllerEle.attributeValue(AutomaticConstant.JMX_TESTNAME));
        if (moduleControllerEle.attributeValue(AutomaticConstant.JMX_ENABLED).toLowerCase().equals("true")) {
            moduleControllerNode.setEnable(true);
        } else {
            moduleControllerNode.setEnable(false);
        }
        if (!parentEnable) {
            moduleControllerNode.setEnable(false);
        }
        moduleControllerNode.setXpath(moduleControllerEle.getUniquePath());
//        if (controllerNameList.contains(moduleControllerEle.getName())) {
//            throw new ServiceException("控制器名称【" + moduleControllerNode.getName() + "】重复了!");
//        }
//        controllerNameList.add(moduleControllerNode.getName());
        AutomaticNode findNode = findModuleEle(moduleControllerEle, tfMap);
        if (findNode.getType().equals(AutomaticConstant.TestFragmentController)) {
            moduleControllerNode.setAutomaticNodeList(findNode.getAutomaticNodeList());
        } else {
            moduleControllerNode.getAutomaticNodeList().add(findNode);
        }


        return moduleControllerNode;
    }

    private AutomaticNode findModuleEle(Element moduleControllerEle, Map<String, AutomaticNode> tfMap) throws ServiceException {
        AutomaticNode tfNode = null;
        Node node = moduleControllerEle.selectSingleNode("collectionProp[@name='ModuleController.node_path']");
        if (node == null) {
            throw new ServiceException("【" + moduleControllerEle.attributeValue(AutomaticConstant.JMX_TESTNAME) + "】没有设置引用目标!");
        }
        List<Node> nodes = node.selectNodes("stringProp");
        if (nodes.size() <= 2) {
            throw new ServiceException("【" + moduleControllerEle.attributeValue(AutomaticConstant.JMX_TESTNAME) + "】没有设置引用目标测试片段!");
        }
        String tfName = nodes.get(2).getText();
        if (!tfMap.containsKey(tfName)) {
            throw new ServiceException(tfName + ":测试片段不存在!");
        }
        tfNode = tfMap.get(tfName);
        for (int i = 3; i < nodes.size(); i++) {
            Node stringPropNode = nodes.get(i);
            if (tfNode.childIsEmpty()) {
                throw new ServiceException(stringPropNode.getText() + ":在【" + moduleControllerEle.attributeValue(AutomaticConstant.JMX_TESTNAME) + "】的引用目标中不存在!");
            }
            boolean ifFind = false;
            for (AutomaticNode childNode : tfNode.getAutomaticNodeList()) {
                if (childNode.getName().equals(stringPropNode.getText())) {
                    ifFind = true;
                    tfNode = childNode;
                    break;
                }
            }
            if (!ifFind) {
                throw new ServiceException(stringPropNode.getText() + ":在【" + moduleControllerEle.attributeValue(AutomaticConstant.JMX_TESTNAME) + "】的引用目标中没找到!");
            }
        }
        return tfNode;

    }

    private List<AutomaticNode> getOtherAutoNode(Element hashTree, boolean parentEnable, Map<String,List<String>> threadTcNames, List<String> controllerNameList, Map<String, AutomaticNode> tfMap,String threadName) throws ServiceException {
        List<AutomaticNode> result = null;
        Element mainEle = null;
        for (Element element : hashTree.elements()) {
            int indexSamplerType = samplerList.indexOf(element.getName());
            if (element != null && indexSamplerType >= 0 && mainEle == null) {
                AutomaticNode node = getAutoTestCase(element, threadTcNames, tfMap,threadName,parentEnable);
                if (node != null) {
                    if (result == null) {
                        result = new ArrayList<>();
                    }
                    result.add(node);
                }
            }

            int indexControllerType = config.getJmeterConfig().getControllerList().indexOf(element.getName());
            if (element != null && indexControllerType >= 0 && mainEle == null) {
                mainEle = element;
            }

            if (element != null && element.getName().equals(AutomaticNodeTypeEnum.ModuleController.getDesc()) && mainEle == null) {
                mainEle = element;

            }
            if (element != null && element.getName().equals("hashTree") && mainEle == null) {
                List<AutomaticNode> otherNodes = getOtherAutoNode(element, parentEnable, threadTcNames, controllerNameList, tfMap,threadName);
                if (otherNodes != null) {
                    for (AutomaticNode tempNode : otherNodes) {
                        result.add(tempNode);
                    }
                }
            }
            if (element != null && element.getName().equals("hashTree") && mainEle != null) {
                AutomaticNode node = null;
                int indexCType = config.getJmeterConfig().getControllerList().indexOf(mainEle.getName());
                if (indexCType >= 0) {
                    node = getAutoNodeByController(mainEle, element, parentEnable, threadTcNames, controllerNameList, tfMap,threadName);
                }
                if (mainEle.getName().equals(AutomaticNodeTypeEnum.ModuleController.getDesc())) {
                    node = getAutoByModuleController(mainEle, parentEnable, controllerNameList, tfMap);
                }
                if (node != null) {
                    if (result == null) {
                        result = new ArrayList<>();
                    }
                    result.add(node);
                }
                mainEle = null;
            }
        }
        return result;
    }


    private AutomaticNode getAutoTestCase(Element currentTcTree, Map<String,List<String>> threadTcNames, Map<String, AutomaticNode> tfMap,String groupName,Boolean parentEnable) throws ServiceException {
        AutomaticNode automaticNode = new AutomaticNode();
        automaticNode.setName(currentTcTree.attributeValue(AutomaticConstant.JMX_TESTNAME));
        if(groupName!=null) {
            if(!threadTcNames.containsKey(threadTcNames)){
                threadTcNames.put(groupName,new ArrayList<>());
            }
            if (threadTcNames.get(groupName).contains(automaticNode.getName())) {
                throw new ServiceException("线程组【"+groupName+"】中的用例【" + automaticNode.getName() + "】重名了!");
            }
            threadTcNames.get(groupName).add(automaticNode.getName());
        }
        if (currentTcTree.attributeValue(AutomaticConstant.JMX_ENABLED).toLowerCase().equals("true")) {
            automaticNode.setEnable(true);
        } else {
            automaticNode.setEnable(false);
        }
        if (!parentEnable) {
            automaticNode.setEnable(parentEnable);
        }
        automaticNode.setXpath(currentTcTree.getUniquePath());
        if (currentTcTree.getName().equals(AutomaticConstant.DubboSample)) {
            automaticNode.setType(AutomaticNodeTypeEnum.DubboSample.getDesc());
            Node ifNameNode = currentTcTree.selectSingleNode("stringProp[@name='FIELD_DUBBO_INTERFACE']");
            if (ifNameNode != null) {
                automaticNode.setInterfaceName(ifNameNode.getText());
            }
        } else if (currentTcTree.getName().equals(AutomaticConstant.DubboSampler)) {
            automaticNode.setType(AutomaticNodeTypeEnum.DubboSample.getDesc());
            Node ifNameNode = currentTcTree.selectSingleNode("stringProp[@name='serviceInterface']");
            if (ifNameNode != null) {
                automaticNode.setInterfaceName(ifNameNode.getText());
            }
        } else if (currentTcTree.getName().equals(AutomaticConstant.ZMSSampler)) {
            automaticNode.setType(AutomaticNodeTypeEnum.ZMSSampler.getDesc());
        } else {
            automaticNode.setType(currentTcTree.getName());
        }

        List tcElements = currentTcTree.elements();
        for (Object tceO : tcElements) {
            Element tce = (Element) tceO;
            if (tce.attributeValue(AutomaticConstant.JMX_NAME).equals(AutomaticConstant.JMX_TESTPLAN_COMMENTS)) {
                automaticNode.setComment(tce.getText());
                break;
            }
        }
        System.out.println(currentTcTree.getUniquePath() + "-----" + currentTcTree.getTextTrim() + "------" + currentTcTree.attributeValue("testname"));

        return automaticNode;
    }

    private SceneInfoEntityDO querySceneInfoByCodeAndVersion(String sceneCode, Integer sceneVersion) {
        Example example = new Example(SceneInfoEntity.class);
        example.createCriteria()
                .andEqualTo("sceneCode", sceneCode)
                .andEqualTo("sceneVersion", sceneVersion);
        return apiTestEntityConverter.convertSceneInfoEntityDO(sceneInfoMapper.selectOneByExample(example));
    }
    /**
     * 创建JDBCconfig
     *
     * @param scene
     * @param jmeterDocumentFullInfo
     */
    private void createJdbcConfig(Scene scene, JmeterDocumentFullInfo jmeterDocumentFullInfo) {
        if (null == scene || CollectionUtil.isEmpty(scene.getDbIds())) {
            return;
        }
        for (String dbId : scene.getDbIds()) {
            JSONObject physicalDbInfo = zbaseService.queryPhysicalDbInfo(dbId);
            if (Objects.isNull(physicalDbInfo)) {
                log.error("获取数据库物理信息异常.{}", dbId);
                throw new ServiceException("获取数据库物理信息异常");
            }
            JSONObject dbAccountInfo = zbaseService.queryDbAccountInfo(physicalDbInfo);
            if (Objects.isNull(dbAccountInfo) || !"SYS000".equals(dbAccountInfo.getString("statusCode"))) {
                log.error("获取数据库账户信息异常.{}, {}", physicalDbInfo, dbAccountInfo);
                throw new ServiceException("获取数据库账户信息异常");
            }
            log.info("dbAccountInfo >>> {}", dbAccountInfo.toJSONString());
            String driver;
            String dbUrl;
            if (dbAccountInfo.getString("dbType").toLowerCase().trim().equals("mysql")) {
                driver = "com.mysql.jdbc.Driver";
                dbUrl = String.format("jdbc:mysql://%s:%s/%s", dbAccountInfo.getString("ip"), dbAccountInfo.getString("port"), dbAccountInfo.getString("schema"));
            } else {
                driver = "oracle.jdbc.OracleDriver";
                dbUrl = String.format("**************************", dbAccountInfo.getString("ip"), dbAccountInfo.getString("port"), dbAccountInfo.getString("schema"));
            }
            JmeterEleUtil.newJDBCConfig(jmeterDocumentFullInfo.getPlanContentHashTree(), dbId, driver, dbUrl, dbAccountInfo.getJSONObject("result").getString("username"), dbAccountInfo.getJSONObject("result").getString("password"));
        }
    }

    private void addUserDefinedVariablesContent(String productCode, String sceneCode, Element planContentHashTree) {
        List<ApiTestVariableVO> apiTestVariableVOList = apiTestRepository.querySceneVariable(productCode, sceneCode, VariableTypeEnum.VARIABLE, SubVariableTypeEnum.CUSTOM, Arrays.asList(VariableUsageTypeEnum.UNKNOWN));
        if (CollectionUtil.isEmpty(apiTestVariableVOList)) {
            return;
        }
        JmeterEleUtil.addUserDefinedVariablesContent(planContentHashTree,new HashMap<String, String>() {
            {
                apiTestVariableVOList.stream().forEach(vo -> {
                    if (StringUtil.isNotEmpty(vo.getVariableValue()) && !vo.getVariableValue().startsWith("${__dataExclusiveLock")) {
                        put(vo.getVariableKey(), vo.getVariableValue());
                    }
                });
            }
        },false);
    }

    private void addDataVariableElement(String workspace, String nodeCode, Element parentElem) {
        JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", parentElem, "前置-读取数据集变量", null, workspace+"shell/readLinkDataVariable.groovy", null, null, nodeCode, true);
        JmeterEleUtil.newUserParameters(parentElem, new HashMap<String, List<String>>() {{
            put("devops_qc_data_variable", Collections.singletonList("${__eval(${devops_qc_data_variable})}"));
        }}, false, true);
        JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", parentElem, "前置-set数据集变量", null, workspace+"shell/setLinkDataVariable.groovy", null, null, null, true);
        // 为解决总对总验签问题
        JmeterEleUtil.newUserParameters(parentElem, new HashMap<String, List<String>>() {{
            put("devops_qc_param", Collections.singletonList("${__eval(${devops_qc_param})}"));
        }}, false, true);
    }

    private void addGatewaySignScript(String workspace, com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes.Node currentNode, Element parentElem) {
        if (StringUtil.isEmpty(currentNode.getApiCode()) || Boolean.FALSE.equals(currentNode.getGatewaySource())) {
            return;
        }
        ApiTestEntityDO apiTest = apiTestRepository.queryLatestApiTestByMainCode(currentNode.getApiCode());
        if (null == apiTest || StringUtil.isEmpty(apiTest.getGatewayApiInfo())) {
            return;
        }
        GatewayApiSettingVO setting = metaDataService.getGatewayApiSetting(apiTest.getDocProductCode(), apiTest.getGatewayApiInfo());
        APISecurityMeta.RequestMeta requestMeta = Optional.ofNullable(setting)
                .map(GatewayApiSettingVO::getSecurity)
                .map(APISecurityMeta::getRequest)
                .orElse(null);
        if (null == requestMeta) {
            throw new ServiceException("节点[" + currentNode.getName() + "]获取网关签名配置失败！");
        }
        if (!requestMeta.isNeedSignature() || StringUtil.isEmpty(setting.getSignValue())) {
            return;
        }
        JmeterEleUtil.newJSR223Sampler("JSR223PreProcessor", parentElem, "前置-处理网关验签", null, workspace+"shell/addSign.groovy", null, null, setting.getSignValue(), true);
    }

    private Element createPollingController(JSONObject jsonObject, LinkMapTypeEnum typeEnum, Element parentEle, String workspace) {
        if (null == jsonObject || LinkMapTypeEnum.DataCenter_REQUEST_COMPONENT.equals(typeEnum)) {
            return null;
        }
        PollingController pollingController = jsonObject.toJavaObject(PollingController.class);
        if (!pollingController.isSwitchOn()) {
            return null;
        }
        if (StringUtil.isEmpty(pollingController.getTerminatedCondition())) {
            throw new ServiceException("轮询终止条件不能为空！");
        }
        if (pollingController.getTimeout() > PollingController.MAX_TIMEOUT) {
            throw new ServiceException("轮询超时时间最大15分钟！");
        }
        Element simpleEle = JmeterEleUtil.newSimpleController("轮询控制器", parentEle, true);
        // 初始化轮询变量
        Element initEle = JmeterEleUtil.newJSR223Sampler("JSR223Sampler", simpleEle, "InitPollingParameter", null, workspace + "shell/initPollingParameter.groovy", null, null, null, true);
        JmeterEleUtil.newUserParameters(initEle, new HashMap<String, List<String>>() {{
            put("devops_qc_polling_start", Collections.singletonList("${__time(,)}"));
            put("devops_qc_polling_status", Collections.singletonList("START"));
            put("devops_qc_polling_timeout", Collections.singletonList(Long.toString(pollingController.getTimeout())));
            put("devops_qc_polling_result", Collections.singletonList("{}"));
            put("devops_qc_config_thread_exit_flag_save", Collections.singletonList("${devops_qc_config_thread_exit_flag}"));
        }}, false, true);
        JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor", initEle, "隐藏采样器", "prev.setIgnore();", null, null, null, null, true);
        // 条件循环控制器
        Element whileEle = JmeterEleUtil.newWhileController("条件循环控制器", simpleEle, shellParseUtil.getPollingWhileCondition(), true);
        // 计数器
        JmeterEleUtil.newCounter(whileEle, "1", "1", "devops_qc_polling_counter", "", true, false, false);
        // 设置轮询间隔时间
        String intervalContent = shellParseUtil.getPollingIntervalScript(pollingController.getInterval());
        Element intervalEle = JmeterEleUtil.newJSR223Sampler("JSR223Sampler", whileEle, "SetPollingInterval", intervalContent, null, null, null, null, true);
        JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor", intervalEle, "隐藏采样器", "prev.setIgnore();", null, null, null, null, true);
        return whileEle;
    }

    private void setPollingTerminated(Element pollingEle, String terminatedCondition, String workspace) {
        String conditionScript = shellParseUtil.getPollingConditionScript(terminatedCondition);
        Element terminateEle = JmeterEleUtil.newJSR223Sampler("JSR223Sampler", pollingEle, "SetPollingTerminated", conditionScript, null, null, null, null, true);
        JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor", terminateEle, "隐藏采样器", shellParseUtil.getPollingIgnoreScript(), null, null, null, null, true);
        JmeterEleUtil.newJSR223Sampler("JSR223PostProcessor", terminateEle, "PollingCallback", null, workspace + "shell/pollingCallback.groovy", null, null, null, true);
        String assertScript = shellParseUtil.getPollingAssertScript(terminatedCondition);
        JmeterEleUtil.newJSR223Sampler("JSR223Assertion", terminateEle, "轮询条件断言", assertScript, null, null, null, null, true);
    }

    @Override
    public void modifyThreadGroupSerialize(Document document, String method, String time) {
        if(null == document) {
            return;
        }
        Element rootElement = document.getRootElement();
        Node testPlanNode = rootElement.selectSingleNode("//boolProp[@name='TestPlan.serialize_threadgroups']");
        if(null != testPlanNode) {
            Element threadGroupsSerialize = (Element) testPlanNode;
            // 并行
            if(SceneRunningSetEnum.PARALLEL.getValue().equals(method)) {
                threadGroupsSerialize.setText("false");
            }
            // 串行
            if(SceneRunningSetEnum.SERIAL.getValue().equals(method)) {
                threadGroupsSerialize.setText("true");
            }
        }
        List<Node> list = rootElement.selectNodes("//ThreadGroup");
        if(CollectionUtil.isNotEmpty(list)) {
            int i = 0;
            // 第一个线程组不处理
            for(Node node : rootElement.selectNodes("//ThreadGroup")) {
                if(i > 0) {
                    Element groupEle = (Element) node;
                    Element groupDuration = (Element) groupEle.selectSingleNode("stringProp[@name='ThreadGroup.duration']");
                    Element groupDelay = (Element) groupEle.selectSingleNode("stringProp[@name='ThreadGroup.delay']");
                    Element groupScheduler = (Element) groupEle.selectSingleNode("stringProp[@name='ThreadGroup.scheduler']");
                    if(SceneRunningSetEnum.PARALLEL.getValue().equals(method)) {
                        groupDuration.setText("");
                        groupDelay.setText("");
                        groupScheduler.setText("false");
                    }
                    if(SceneRunningSetEnum.SERIAL.getValue().equals(method)) {
                        groupDuration.setText("7200");
                        groupDelay.setText(time);
                        groupScheduler.setText("true");
                    }
                }
                i += 1;
            }
        }
    }

    @Override
    public void modifyOssFile(Document document, String ossPath) {
        if(null == document) {
            return;
        }
        try{
            StringWriter writer = new StringWriter();
            OutputFormat outputFormat = OutputFormat2.createPrettyPrint();
            outputFormat.setEncoding("UTF-8");
            XMLWriter xmlWriter = new XMLWriter(writer, outputFormat);
            xmlWriter.setEscapeText(false);
            xmlWriter.write(document);
            xmlWriter.close();
            ztoOssService.createObject("autojmx", ossPath, writer.toString());
        } catch (Exception e) {
            log.error("异步修改场景图串并行 -> 上传oss失败" + e.toString());
        }
    }
}
