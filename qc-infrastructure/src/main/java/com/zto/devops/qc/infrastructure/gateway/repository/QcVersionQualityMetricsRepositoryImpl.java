package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.testmanager.quality.VersionQualityMetricTypeEnum;
import com.zto.devops.qc.client.model.dto.QcVersionQualityMetricsEntityDO;
import com.zto.devops.qc.domain.gateway.repository.QcVersionQualityMetricsRepository;
import com.zto.devops.qc.infrastructure.converter.QcVersionQualityMetricsConverter;
import com.zto.devops.qc.infrastructure.dao.entity.QcVersionQualityMetricsEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.QcVersionQualityMetricsMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Objects;

@Component
public class QcVersionQualityMetricsRepositoryImpl implements QcVersionQualityMetricsRepository {

    @Autowired
    private QcVersionQualityMetricsMapper qcVersionQualityMetricsMapper;
    @Autowired
    private QcVersionQualityMetricsConverter converter;

    @Override
    public void insertSelective(QcVersionQualityMetricsEntityDO entityDO) {
        qcVersionQualityMetricsMapper.insertSelective(converter.convert(entityDO));
    }

    @Override
    public void batchDeleteByVersionCodeAndTypes(String versionCode, List<VersionQualityMetricTypeEnum> types, User user) {
        if (StringUtil.isBlank(versionCode) || CollectionUtils.isEmpty(types) || Objects.isNull(user)) {
            return;
        }
        Example example = new Example(QcVersionQualityMetricsEntity.class);
        example.createCriteria()
                .andEqualTo("versionCode", versionCode)
                .andIn("metricType", types);
        qcVersionQualityMetricsMapper.deleteByExample(example);
    }
}
