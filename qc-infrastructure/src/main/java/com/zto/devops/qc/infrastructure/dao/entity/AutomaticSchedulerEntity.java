package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.infrastructure.dao.entity.BaseEntity;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.AutomaticStatusHandler;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Table(name = "tm_automatic_scheduler")
public class AutomaticSchedulerEntity extends BaseEntity {

    private Long id;

    @Id
    private String schedulerCode;

    private String schedulerName;

    private String productCode;

    @Column(name = "switch")
    private Boolean switchFlag;

    private String crontab;

    private String executeEnv;

    /**
     * 运行空间tag，用于场景用例执行
     */
    private String executeTag;

    /**
     * 运行空间code，用于研发空间tag变化时查询出记录，修改对应的executeTag
     */
    private String executeSpaceCode;

    private Boolean coverageFlag;

    private Boolean messageFlag;

    @ColumnType(typeHandler = AutomaticStatusHandler.class)
    private AutomaticStatusEnum executeResult;

    private Long executorId;

    private String executor;

    private Date executeTime;

    private String taskId;
}
