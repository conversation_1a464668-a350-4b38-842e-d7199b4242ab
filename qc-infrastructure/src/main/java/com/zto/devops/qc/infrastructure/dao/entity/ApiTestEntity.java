package com.zto.devops.qc.infrastructure.dao.entity;

import com.zto.devops.framework.client.simple.HasTransactor;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTestEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.RequestMethodEnum;
import com.zto.devops.qc.infrastructure.dao.typehandler.ApiTestEnableTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.ApiTypeHandler;
import com.zto.devops.qc.infrastructure.dao.typehandler.RequestMethodTypeHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "tm_api_test")
public class ApiTestEntity {

    @Id
    private String apiCode;

    private String apiName;

    private String productCode;

    private String appId;

    @ColumnType(typeHandler = ApiTypeHandler.class)
    private ApiTypeEnum apiType;

    @ColumnType(typeHandler = RequestMethodTypeHandler.class)
    private RequestMethodEnum reqMethod;

    private String apiAddress;

    private String apiDesc;

    private Long docId;

    private String docVersion;

    private String apiData;

    @ColumnType(typeHandler = ApiTestEnableTypeHandler.class)
    private ApiTestEnableEnum enable;

    private Long creatorId;

    private String creator;

    private Date gmtCreate;

    private Long modifierId;

    private String modifier;

    private Date gmtModified;

    private String docProductCode;

    private Integer apiTestIndex;

    private String mainApiCode;

    private String tagValue;

    private Integer modifyFlag;

    private Integer relatedSceneFlag;

    private String gatewayApiInfo;

    public void preCreate(HasTransactor hasTransactor) {
        User user = hasTransactor.getTransactor();
        if (null != user) {
            this.setCreatorId(user.getUserId());
            this.setCreator(user.getUserName());
            this.setGmtCreate(new Date());
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }

    public void preUpdate(HasTransactor hasTransactor) {
        User user = hasTransactor.getTransactor();
        if (null != user) {
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }
}
