package com.zto.devops.qc.infrastructure.gateway.util;

import com.alipay.remoting.util.StringUtils;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.zto.devops.framework.client.simple.ReactiveId;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.infrastructure.util.noticing.ReactiveEmitter;
import com.zto.devops.qc.client.enums.testmanager.channel.TestEventEnums;
import com.zto.devops.qc.client.service.testmanager.apitest.model.ApiDebugLogResp;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.util.ApiDebugLogService;
import com.zto.devops.qc.infrastructure.util.AmazonS3Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

@Service
@Slf4j
public class ApiDebugLogUtil implements ApiDebugLogService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private AmazonS3Utils amazonS3Utils;

    @ApolloConfig()
    private Config config;

    @Autowired
    private ReactiveEmitter reactiveEmitter;

    @Override
    public ApiDebugLogResp queryApiDebugLog(String taskId) {
        String logContent = redisService.getListOnLeft(taskId);
        if (StringUtils.isNotBlank(logContent)) {
            log.info("查询调试日志,taskId:{}, logContent:{}", taskId, logContent);
        }
        ApiDebugLogResp resp = new ApiDebugLogResp();
        resp.setLogContent(null == logContent ? "" : logContent + "\n");
        return resp;
    }

    @Override
    public void setApiDebugLog(String taskId, String logContent, boolean isDocument) {
        if (!isDocument) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss,SSS");
            uploadApiDebugLog(taskId, format.format(new Date()) + ": " + logContent);
        }
    }

//    @Override
//    public void abortApiDebugLog(String taskId) {
//        log.info("终止调试日志,taskId:{}", taskId);
//        try {
//            redisService.setAliveTime(taskId, 1, TimeUnit.MINUTES);
//        } catch (Exception e) {
//            log.error("终止读取jmeterLog线程失败！", e);
//        }
//    }

//    @Override
//    public void readJmeterLogExecutor(String taskId, ScheduledExecutorService executor) {
//        String path = "/data/jmeter/" + taskId + "/" + taskId + ".log";
//        log.info("开始监听jmeter.log日志,taskId{}, path:{}", taskId, path);
//        JSONObject fileObj = new JSONObject();
//        fileObj.put("lastTimeFileSize", 0L);
//        executor.scheduleWithFixedDelay(() -> {
////            log.info("线程池：1，taskId：" + taskId);
//            try (final RandomAccessFile randomFile = new RandomAccessFile(path, "rw")) {
//                // 获得变化部分的
//                randomFile.seek(fileObj.getLong("lastTimeFileSize"));
//                String tmp = "";
//                StringBuilder content = new StringBuilder();
//                while (null != (tmp = randomFile.readLine())) {
//                    content.append(new String(tmp.getBytes(StandardCharsets.ISO_8859_1),
//                            StandardCharsets.UTF_8)).append("\n");
//                }
//                if(StringUtils.isNotBlank(content.toString())) {
//                    this.uploadApiDebugLog(taskId, content.toString());
//                }
//                fileObj.put("lastTimeFileSize", randomFile.length());
//            } catch (Exception e) {
//                log.error("读取jmeter日志文件方法异常！", e);
//                this.setApiDebugLog(taskId, JSON.toJSONString(e));
//            }
//        }, 0, config.getLongProperty("api.debug.log.delay", 1000L), TimeUnit.MILLISECONDS);
//    }

    private void uploadApiDebugLog(String taskId, String logContent) {
        if (StringUtil.isEmpty(taskId) || StringUtil.isEmpty(logContent)) {
            return;
        }
        log.info("更新调试日志,taskId:{},logContent:{}", taskId, logContent);
        reactiveEmitter.emit(TestEventEnums.TEST_AUTOMATIC_DEBUG_LOG.toReactiveId(taskId), logContent);
//            redisService.addListOnRight(taskId, logContent);
//            redisService.addToListOnRightWithExpiration("debugLogAll-" + taskId, logContent, 7200L);
    }

}
