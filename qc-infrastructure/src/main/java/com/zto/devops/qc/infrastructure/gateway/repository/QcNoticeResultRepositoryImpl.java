package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.infrastructure.util.AggregateIdUtil;
import com.zto.devops.product.client.enums.NoticeTypeEnum;
import com.zto.devops.qc.client.enums.rpc.NoticeUserTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.email.TmEmailDataSourceEnum;
import com.zto.devops.qc.client.model.dto.QcNoticeResultEntityDO;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.message.EmailAttachment;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.DetailEmailVO;
import com.zto.devops.qc.domain.gateway.repository.QcNoticeResultRepository;
import com.zto.devops.qc.infrastructure.converter.QcNoticeResultConverter;
import com.zto.devops.qc.infrastructure.dao.entity.QcNoticeResultEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.QcNoticeResultMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class QcNoticeResultRepositoryImpl implements QcNoticeResultRepository {

    @Autowired
    private QcNoticeResultMapper qcNoticeResultMapper;
    @Autowired
    private QcNoticeResultConverter qcNoticeResultConverter;

    @Override
    public DetailEmailVO findSendUserInfo(DetailEmailVO vo) {
        List<QcNoticeResultEntity> qcNoticeResultEntities = null;
        if (vo.getDataSource().equals(TmEmailDataSourceEnum.SEND)) {
            qcNoticeResultEntities = selectResultDB(vo.getEmailCode());
        } else if (vo.getDataSource().equals(TmEmailDataSourceEnum.TRANSFER)) {
            qcNoticeResultEntities = selectResultDB(vo.getBusinessCode());
        }
        List<SendUserInfoVO> sendUserInfoVOS = qcNoticeResultConverter.convert(qcNoticeResultEntities);
        if (CollectionUtil.isNotEmpty(sendUserInfoVOS)) {
            vo.setSendTime(qcNoticeResultEntities.get(0).getSendTime());
            List<SendUserInfoVO> cc = sendUserInfoVOS.stream().peek(s -> s.setOverrideEquals(true)).filter(n -> NoticeUserTypeEnum.CC.name().equals(n.getUserType())).distinct().collect(Collectors.toList());
            List<SendUserInfoVO> recipient = sendUserInfoVOS.stream().peek(s -> s.setOverrideEquals(true)).filter(n -> NoticeUserTypeEnum.RECIPIENT.name().equals(n.getUserType())).distinct().collect(Collectors.toList());
            vo.setCcUsers(cc);
            vo.setReceiveUsers(recipient);
        }
        return vo;
    }

    @Async
    @Override
    public void addDB(BaseEvent event, String code, List<SendUserInfoVO> ccUsers, List<SendUserInfoVO> receiveUsers) {
        List<SendUserInfoVO> allList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(ccUsers)) {
            ccUsers.forEach(cc -> cc.setUserType(NoticeUserTypeEnum.CC.name()));
            ccUsers = ccUsers.stream().filter(n -> NoticeUserTypeEnum.CC.name().equals(n.getUserType()))
                    .distinct().collect(Collectors.toList());
            allList.addAll(ccUsers);
        }

        if (CollectionUtil.isNotEmpty(receiveUsers)) {
            receiveUsers.forEach(r -> r.setUserType(NoticeUserTypeEnum.RECIPIENT.name()));
            receiveUsers = receiveUsers.stream().filter(n -> NoticeUserTypeEnum.RECIPIENT.name().equals(n.getUserType()))
                    .distinct().collect(Collectors.toList());
            allList.addAll(receiveUsers);
        }
        if (CollectionUtil.isEmpty(allList)) {
            return;
        }
        List<QcNoticeResultEntity> entities = qcNoticeResultConverter.convertSendUserInfoVOS(allList);

        // 如果已经存在了，就做更新
        Example example = new Example(QcNoticeResultEntity.class);
        example.createCriteria().andEqualTo("businessCode", code).andEqualTo("enable", true);
        List<QcNoticeResultEntity> entities1 = qcNoticeResultMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(entities1)) {
            entities1.forEach(entity -> {
                QcNoticeResultEntity r = new QcNoticeResultEntity();
                r.setCode(entity.getCode());
                r.setEnable(false);
                qcNoticeResultMapper.updateByPrimaryKeySelective(r);
            });
        }

        entities.forEach(e -> {
            e.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
            e.setBusinessCode(code);
            e.setNoticeType("EMAIL");
            e.setSendTime(new Date());
            e.preCreate(event);
            e.preUpdate(event);
            e.setEnable(true);
            qcNoticeResultMapper.insert(e);
        });
    }

    @Override
    public Object getAttachment(List<AttachmentVO> attachments) {
        List<EmailAttachment> emailAttachments = new ArrayList<>();
        if (CollectionUtil.isEmpty(attachments)) {
            return null;
        }
        for (AttachmentVO vo : attachments) {
            EmailAttachment emailAttachment = new EmailAttachment();
            emailAttachment.setAttachmentURL(vo.getUrl());
            emailAttachment.setAttachmentName(vo.getName());
            emailAttachment.setGroup("public");
            emailAttachments.add(emailAttachment);
        }

        return emailAttachments;
    }

    @Override
    public void insertBatch(List<SendUserInfoVO> allList, String emailCode, User operator) {
        List<QcNoticeResultEntity> entities = qcNoticeResultConverter.convertSendUserInfoVOS(allList);
        entities.forEach(e -> {
            e.setCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE));
            e.setBusinessCode(emailCode);
            e.setNoticeType(NoticeTypeEnum.EMAIL.name());
            e.setSendTime(new Date());
            e.preCreate(operator);
            e.preUpdate(operator);
            e.setEnable(true);
            qcNoticeResultMapper.insert(e);
        });
    }

    private List<QcNoticeResultEntity> selectResultDB(String businessCode) {
        Example example = new Example(QcNoticeResultEntity.class);
        example.createCriteria().andEqualTo("businessCode", businessCode).andEqualTo("noticeType", "EMAIL")
                .andEqualTo("enable", true);
        return qcNoticeResultMapper.selectByExample(example);

    }

    @Override
    public List<QcNoticeResultEntityDO> selectByPlanCode(String businessCode) {
        Example example = new Example(QcNoticeResultEntity.class);
        example.createCriteria()
                .andEqualTo("businessCode", businessCode)
                .andEqualTo("noticeType", NoticeTypeEnum.EMAIL.name())
                .andEqualTo("enable", true);
        List<QcNoticeResultEntity> entityList = qcNoticeResultMapper.selectByExample(example);
        return qcNoticeResultConverter.convert2DOList(entityList);
    }
}
