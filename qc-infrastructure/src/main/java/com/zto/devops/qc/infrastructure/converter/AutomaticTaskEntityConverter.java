package com.zto.devops.qc.infrastructure.converter;

import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseExecuteRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTask;
import com.zto.devops.qc.infrastructure.dao.entity.AutomaticTaskEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TestcaseExecuteRecordEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AutomaticTaskEntityConverter {

    AutomaticTaskEntity convert2Entity(AutomaticTaskEntityDO taskEntityDO);

    List<AutomaticTaskEntityDO> convert2DOList(List<AutomaticTaskEntity> entityList);

    AutomaticTaskEntityDO convert2DO(AutomaticTaskEntity entity);

    TestcaseExecuteRecordEntity convert(TestcaseExecuteRecordEntityDO entityDO);

    AutomaticTask convert(AutomaticTaskEntity entity);

    List<TestcaseExecuteRecordEntityDO> convertList(List<TestcaseExecuteRecordEntity> entityList);

    TestcaseExecuteRecordEntityDO convertor(TestcaseExecuteRecordEntity entity);
}
