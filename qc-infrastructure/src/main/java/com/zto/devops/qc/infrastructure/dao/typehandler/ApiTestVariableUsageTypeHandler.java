package com.zto.devops.qc.infrastructure.dao.typehandler;

import com.zto.devops.framework.infrastructure.dao.handler.BaseEnumTypeHandler;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableUsageTypeEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedJdbcTypes(value = JdbcType.INTEGER, includeNullJdbcType = true)
public class ApiTestVariableUsageTypeHandler extends BaseEnumTypeHandler<VariableUsageTypeEnum> {
    public ApiTestVariableUsageTypeHandler() {
        super(VariableUsageTypeEnum.class);
    }

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, VariableUsageTypeEnum variableUsageTypeEnum, JdbcType jdbcType) throws SQLException {
        if (null == variableUsageTypeEnum) {
            preparedStatement.setInt(i, 0);
        } else {
            preparedStatement.setInt(i, variableUsageTypeEnum.getValue());
        }
    }

    @Override
    public VariableUsageTypeEnum getNullableResult(ResultSet resultSet, String s) throws SQLException {
        int result = resultSet.getInt(s);
        return VariableUsageTypeEnum.valueOf(result);
    }

    @Override
    public VariableUsageTypeEnum getNullableResult(ResultSet resultSet, int i) throws SQLException {
        int result = resultSet.getInt(i);
        return VariableUsageTypeEnum.valueOf(result);
    }

    @Override
    public VariableUsageTypeEnum getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        int result = callableStatement.getInt(i);
        return VariableUsageTypeEnum.valueOf(result);
    }
}
