package com.zto.devops.qc.infrastructure.gateway.repository;

import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.product.client.enums.MemberTypeEnum;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.qc.client.model.rpc.product.query.ListProductMemberByPIdQuery;
import com.zto.devops.qc.domain.gateway.repository.TmStateMachineExtendManager;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestPlanEntity;
import com.zto.devops.qc.infrastructure.dao.entity.TmTestReportEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.TmTestPlanMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.TmTestReportMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@Service
@Slf4j
public class TmStateMachineExtendManagerImpl implements TmStateMachineExtendManager {
    @Autowired
    private TmTestReportMapper tmTestReportMapper;

    @Autowired
    private TmTestPlanMapper tmTestPlanMapper;

    @Autowired
    private IProductRpcService iProductRpcService;

    /**
     * 超级管理员标识
     */
    private static final String SUPPER_USER_PERMISSION_KEY = "globalpermCanWriteProd";


    @Override
    public void checkPermissions(String planCode, ReportType reportType, User user) {
        Example example = new Example(TmTestReportEntity.class);
        example.createCriteria().andEqualTo("planCode", planCode).andEqualTo("status", TestPlanStatusEnum.NORMAL).
                andEqualTo("reportType", reportType);
        List<TmTestReportEntity> testReportEntityList = tmTestReportMapper.selectByExample(example);

        if (!CollectionUtil.isEmpty(testReportEntityList)) {
            throw new ServiceException("已存在该计划的"+ reportType.getValue() +"关联任务，请重新选择。");
        }
        TmTestPlanEntity testPlanEntity = tmTestPlanMapper.selectByPrimaryKey(planCode);
        checkPermissionByProduct(testPlanEntity.getProductCode(), user);
    }

    @Override
    public void checkPermissionByProduct(String productCode, User user) {

        if(CollectionUtil.isNotEmpty(user.getPermissions()) && user.getPermissions().contains(SUPPER_USER_PERMISSION_KEY)){
            return;
        }

        List<ProductMemberVO> productMembers = this.getProductMember(productCode,
                Arrays.asList(MemberTypeEnum.TESTER, MemberTypeEnum.TESTER_OWNER, MemberTypeEnum.TESTER_M));
        if (CollectionUtil.isEmpty(productMembers)) {
            throw new ServiceException("当前产品无测试人员，请添加后重试");
        }
        List<Long> userIdList = productMembers.stream().map(ProductMemberVO::getUserId).collect(Collectors.toList());
        if (!userIdList.contains(user.getUserId())) {
            throw new ServiceException("您不是当前计划所属产品的测试人员，无法创建/修改报告");
        }

    }

    public List<ProductMemberVO> getProductMember(String productCode, List<MemberTypeEnum> memberTypes) {
        List<ProductMemberVO> vo = new ArrayList<>();
        try {
            ListProductMemberByPIdQuery query = new ListProductMemberByPIdQuery();
            query.setProductCode(productCode);
            List<String> members = memberTypes.stream().map(Enum::name).collect(Collectors.toList());
            query.setMemberTypes(members);
            List<ProductMemberVO> memberVOS = iProductRpcService.findProductMemberByIdQuery(productCode,members);
            vo = memberVOS;
        } catch (Exception e) {
            log.error("query getProductMember is error ,productCode {}  error {}", productCode, e.toString(), e);
        }
        return vo;
    }

    @Override
    public boolean canReportEdit(String productCode, User user) {
        if(CollectionUtil.isNotEmpty(user.getPermissions()) && user.getPermissions().contains(SUPPER_USER_PERMISSION_KEY)){
            return true;
        }
        List<ProductMemberVO> productMembers = this.getProductMember(productCode,
                Arrays.asList(MemberTypeEnum.TESTER, MemberTypeEnum.TESTER_OWNER, MemberTypeEnum.TESTER_M));
        if (CollectionUtil.isNotEmpty(productMembers)) {
            List<Long> userIdList = productMembers.stream().map(ProductMemberVO::getUserId).collect(Collectors.toList());
            if (userIdList.contains(user.getUserId())) {
                return true;
            }
        }
        return  false;
    }

    @Override
    public void checkHasAccessReport(String planCode, User transactor) {

        Example example = new Example(TmTestReportEntity.class);
        example.createCriteria().andEqualTo("planCode", planCode)
                .andEqualTo("enable", true).andEqualTo("status", TestPlanStatusEnum.NORMAL)
                .andEqualTo("reportType", ReportType.TEST_ACCESS);
        List<TmTestReportEntity> testReportEntityList = tmTestReportMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(testReportEntityList)) {
            throw new ServiceException("请先发送准入报告再发送准出报告");
        }

    }

    @Override
    public void checkHasSmokeReport(String planCode, User transactor) {

        Example example = new Example(TmTestReportEntity.class);
        example.createCriteria().andEqualTo("planCode", planCode)
                .andEqualTo("enable", true).andEqualTo("status", TestPlanStatusEnum.NORMAL)
                .andEqualTo("reportType", ReportType.TEST_PERMIT);
        List<TmTestReportEntity> testReportEntityList = tmTestReportMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(testReportEntityList)) {
            throw new ServiceException("请先发送准入和准出报告再发送线上冒烟报告");
        }

    }
}
