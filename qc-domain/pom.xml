<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>qc-parent</artifactId>
        <groupId>com.zto.devops.qc</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>qc-domain</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zto.devops.qc</groupId>
            <artifactId>qc-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.framework</groupId>
            <artifactId>devops-domain</artifactId>
            <version>${framework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.framework</groupId>
            <artifactId>kernel-statemachine</artifactId>
            <version>${framework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
    </dependencies>

</project>
