package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.issue.command.AddRelevantUserCommand;
import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface RelevantUserVOConverter {
    RelevantUserVOConverter INSTANCE = Mappers.getMapper(RelevantUserVOConverter.class);
//    RelevantUserVO convert(RelevantUserEntity entity);
//    List<RelevantUserVO> convert(List<RelevantUserEntity> entity);
    RelevantUserVO convert(AddRelevantUserCommand command);
}