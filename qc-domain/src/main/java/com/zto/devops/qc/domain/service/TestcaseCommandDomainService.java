package com.zto.devops.qc.domain.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.zto.devops.framework.client.entity.BaseEntityDO;
import com.zto.devops.framework.client.entity.action.ActionLogDO;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.enums.impexp.ExtEnum;
import com.zto.devops.framework.client.event.RevisionEvent;
import com.zto.devops.framework.client.exception.FluentException;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.gateway.repository.IEventLogRepository;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.constants.Constant;
import com.zto.devops.qc.client.enums.constants.ImportFileTypeEunm;
import com.zto.devops.qc.client.enums.issue.TagTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneIndexTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.channel.TestEventEnums;
import com.zto.devops.qc.client.enums.testmanager.plan.*;
import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.rpc.project.VersionInfoVO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.AddSceneModuleCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.event.MoveSceneModuleEvent;
import com.zto.devops.qc.client.model.testmanager.cases.command.*;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.event.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.*;
import com.zto.devops.qc.client.model.testmanager.plan.entity.AddBatchCaseInBatchTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.BatchTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.DeletePlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.plan.event.AddBatchCaseInBatchTestPlanEvent;
import com.zto.devops.qc.client.model.testmanager.plan.event.PlanCaseOperatedLogEvent;
import com.zto.devops.qc.client.service.testmanager.cases.model.*;
import com.zto.devops.qc.domain.converter.TestcaseAttachmentDomainConverter;
import com.zto.devops.qc.domain.converter.TestcaseDomainConverter;
import com.zto.devops.qc.domain.converter.TestcaseModuleDomainConverter;
import com.zto.devops.qc.domain.converter.TestcaseTagDomainConverter;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.excel.ExcelService;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.repository.*;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.domain.gateway.xmind.XmindService;
import com.zto.devops.qc.domain.model.TestcaseAttachment;
import com.zto.titans.common.util.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TestcaseCommandDomainService extends BaseDomainService {

    @Autowired
    private TestcaseDomainConverter testcaseDomainConverter;

    @Autowired
    private TestcaseModuleDomainConverter testcaseModuleDomainConverter;

    @Autowired
    private AutomaticSourceRecordRepository automaticSourceRecordRepository;

    @Autowired
    private TestcaseRepository testcaseRepository;

    @Autowired
    private ITmTestPlanRepository iTmTestPlanRepository;

    @Autowired
    private ITmTestPlanCaseRepository iTmTestPlanCaseRepository;

    @Autowired
    private TestcaseAttachmentRepository testcaseAttachmentRepository;

    @Autowired
    private TestcaseStepRepository testcaseStepRepository;

    @Autowired
    private TestcaseTagRepository testcaseTagRepository;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private TestcaseQueryDomainService testcaseQueryDomainService;

    @Autowired
    private IEventLogRepository logManager;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    @Autowired
    private ZtoOssService ztoOssService;

    @Autowired
    private TestcaseAttachmentDomainConverter testcaseAttachmentConverter;

    @Autowired
    private TestcaseTagDomainConverter testcaseTagConverter;

    @Autowired
    private TagRepository tagRepository;

    @Autowired
    private AttachmentRepository attachmentRepository;

    @Autowired
    private ITestcaseRelationRepository testcaseRelationRepository;

    @Autowired
    private TagQueryDomainService tagQueryDomainService;

    @Autowired
    private TestcaseTagCommendDomainService testcaseTagCommendDomainService;

    @Autowired
    private IEventLogRepository eventLogManager;

    @Autowired
    private ITmTestPlanRepository testPlanRepository;

    @Autowired
    private ImpExpRepository impExpRepository;

    @Autowired
    private ExcelService excelService;

    @Autowired
    private XmindService xmindService;

    @Autowired
    private TmTestPlanCommandDomainService tmTestPlanCommandDomainService;

    @Autowired
    private ApiTestRepository apiTestRepository;

    @Autowired
    private AutomaticSchedulerRepository automaticSchedulerRepository;

    private final static String MOVE_FLAG = "MOVE_FLAG";

    private final static String COPY_FLAG = "COPY_FLAG";

    private final static String SET_CORE_REGEX = "【副本[1-9]\\d{0,2}】";

    private final static String COPY_REGEX = "【副本】";

    @Autowired
    private IProjectRpcService projectRpcService;

    private final Executor EVENT_HANDLER_EXECUTOR = new ThreadPoolExecutor(
            10,
            100,
            30L,
            TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            ThreadFactoryBuilder.create().setNamePrefix("Testcase-EventHandler-").build());

    private static ThreadPoolExecutor THREAD_POOL_TAG = new ThreadPoolExecutor(20, 100, 100,
            TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(1000),
            ThreadFactoryBuilder.create().setNamePrefix("THREAD-POOL-TAG-").build());

    private static ThreadPoolExecutor THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(4, 4, 60,
            TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(40),
            ThreadFactoryBuilder.create().setNamePrefix("THREAD-POOL-EXECUTOR-").build());

    private final int TESTCASE_ATTACHMENT_MAX_NUM = 100;

    public void batchJoinPlan(List<AddBatchCaseInBatchTestPlanVO> list, User operator) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<BatchTestPlanVO> planList = list.get(0).getTestPlanList();
        if (CollectionUtil.isEmpty(planList)) {
            return;
        }
        //校验用例和计划是否同一个版本
        checkVersion(list.get(0).getCaseCode(), planList);

        List<String> caseCodes = list.stream()
                .map(AddBatchCaseInBatchTestPlanVO::getCaseCode).collect(Collectors.toList());
        int mediumSize = qcConfigBasicService.getMediumSize();
        if (CollectionUtil.isNotEmpty(caseCodes) && caseCodes.size() > mediumSize) {
            throw new ServiceException("批量加入测试计划一次最多支持1000条数据");
        }
        doBatchJoinPlan(caseCodes, planList, operator);
    }

    public void addTestcaseAttachment(AddTestcaseAttachmentCommand command) {
        TestcaseAttachment testcaseAttachment = testcaseAttachmentRepository.loadFormDb(command.getBusinessCode());
        log.info("AddTestcaseAttachmentCommand >>> {}", command.getAggregateId());
        TestcaseAttachmentAddedEvent event = testcaseAttachmentConverter.converter(command);
        addTestcaseAttachment(event);
        apply(event);
        if (StringUtils.isNotBlank(command.getOperateCaseCode())) {
            PlanCaseOperatedLogEvent logEvent = new PlanCaseOperatedLogEvent();
            logEvent.setAggregateId(command.getAggregateId());
            logEvent.setTransactor(command.getTransactor());
            logEvent.setOccurred(command.getOccurred());
            logEvent.setCode(command.getOperateCaseCode());
            logEvent.setAction("上传了文档");
            logEvent.setMakeString(command.getName());
            apply(logEvent);
        }
    }

    public void addTestcaseTag(AddTestcaseTagCommand command) {
        log.info("AddTestcaseTagCommand >>> {}", command.getAggregateId());
        TestcaseTagAddedEvent event = testcaseTagConverter.converter(command);
        addTestcaseTag(event);
        apply(event);
    }

    public void associateTestcaseDomain(AssociateTestcaseDomainCommand command) {
        log.info("AssociateTestcaseDomainCommand >>> {}", command.getAggregateId());
        TestcaseDomainAssociatedEvent event = testcaseDomainConverter.convertor(command);
        testcaseRepository.associateTestcaseDomain(event);
        apply(event);
        if (StringUtils.isNotBlank(command.getOperateCaseCode())) {
            PlanCaseOperatedLogEvent logEvent = new PlanCaseOperatedLogEvent();
            logEvent.setTransactor(command.getTransactor());
            logEvent.setOccurred(command.getOccurred());
            logEvent.setCode(command.getOperateCaseCode());
            logEvent.setAction(String.format("关联了%s", command.getDomain().getValue()));
            logEvent.setAggregateId(command.getAggregateId());
            apply(logEvent);
        }
    }

    public void addBatchTestcaseTag(BatchAddTestcaseTagCommand command) {
        int businessCodes = qcConfigBasicService.getBusinessCount();
        if (command.getBusinessCodes().size() > businessCodes) {
            throw new ServiceException("用例数不能大于" + businessCodes);
        }
        int productTagMax = qcConfigBasicService.getProductTagMax();
        int testcaseTagMax = qcConfigBasicService.getTestcaseTagMax();

        final List<Future> futures = new ArrayList<>();
        command.getBusinessCodes().stream().forEach(code -> {
            Future<?> future = THREAD_POOL_TAG.submit(() -> {
                checkTags(code, command.getDomain(), command.getTagNames(), productTagMax,
                        testcaseTagMax);
            });
            futures.add(future);
        });
        try {
            for (Future future : futures) {
                future.get();
            }
        } catch (Exception e) {
            throw new ServiceException(e.getCause().getMessage());
        }

        log.info("BatchAddTestcaseTagCommand >>> {}", command.getAggregateId());
        TestcaseTagBatchAddedEvent event = testcaseTagConverter.converter(command);
        testcaseTagRepository.batchAddTestcaseTag(event);
        apply(event);
    }


    public void batchChangeCaseStatus(List<String> caseCodes, TestcaseStatusEnum status, TestcaseAbandonReasonEnum reason, User operator) {
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        int bigSize = qcConfigBasicService.getBigSize();
        if (CollectionUtil.isNotEmpty(caseCodes) && caseCodes.size() > bigSize) {
            throw new ServiceException("批量停用一次最多支持2000条数据");
        }
        List<List<String>> partCodes = Lists.partition(caseCodes, 200);
        partCodes.forEach(codes -> doBatchUpdateCaseStatus(codes, status, reason, operator));
    }

    @Async
    public void batchChangeVersion(List<String> caseCodes, String targetVersionCode, String targetParentCode,
                                   Boolean useOriginalGroup, Boolean deleteOriginalGroup, User operator) {
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        int minSize = qcConfigBasicService.getMinSize();
        if (caseCodes.size() > minSize) {
            throw new ServiceException("批量修改用例版本一次最多支持" + minSize + "条数据");
        }
        if (TestcaseGroupTypeEnum.ALL.name().equals(targetParentCode)
                || TestcaseGroupTypeEnum.NO_GROUP.name().equals(targetParentCode)) {
            targetParentCode = "";
        }
        // 获取产品名称
        TestcaseEntityDO simpleCase = testcaseRepository.selectProductCodeAndVersionCodeByCaseCode(caseCodes.get(0));
        String productCode = simpleCase != null ? simpleCase.getProductCode() : "";
        String sourceVersionCode = simpleCase != null ? simpleCase.getVersionCode() : "";
        if (sourceVersionCode.equals(targetVersionCode)) {
            throw new ServiceException("不可以修改至当前版本");
        }

        log.info("deleteOriginalGroup推送长链接开始-TEST_CASE_CHANGE_VERSION:" + sourceVersionCode);
        emit(TestEventEnums.TEST_CASE_CHANGE_VERSION.toReactiveId(sourceVersionCode), "RUNNING");

        List<TestCasePathVO> parentPathList = new ArrayList<>();
        if (null != deleteOriginalGroup && deleteOriginalGroup) {
            parentPathList = testcaseRepository.getParentCodeListByCaseCodeList(caseCodes);
        }

        //复用原分组
        if (useOriginalGroup) {
            //目标版本是否包含原分组
            List<TestcaseGroupVO> targetModuleList = mergeModule(caseCodes, productCode, sourceVersionCode, targetVersionCode,
                    TestcaseTypeEnum.MANUAL, operator);
            targetModuleList.forEach(target -> {
                //目标分组下用例的名称
                Set<String> finalExistNames = testcaseRepository.getExistCaseNameList(productCode, target.getParentCode(), targetVersionCode, Boolean.FALSE);
                if (CollectionUtil.isNotEmpty(target.getCodeList()) && target.getCodeList().size() > 200) {
                    // 分批操作
                    List<List<String>> partition = Lists.partition(target.getCodeList(), 200);
                    partition.parallelStream().forEach(c -> doBatchChangeVersion(c, productCode, sourceVersionCode,
                            targetVersionCode, target.getParentCode(), target.getParentPath(), finalExistNames, operator));
                } else {
                    doBatchChangeVersion(target.getCodeList(), productCode, sourceVersionCode, targetVersionCode,
                            target.getParentCode(), target.getParentPath(), finalExistNames, operator);
                }
            });
        } else {
            //分组code
            String finalParentCode = targetParentCode;
            //目标分组path
            String finalPath = testcaseRepository.getTargetPath(targetParentCode, Boolean.FALSE, TestcaseTypeEnum.MANUAL);
            //目标分组下用例的名称
            Set<String> finalExistNames = testcaseRepository.getExistCaseNameList(productCode, targetParentCode, targetVersionCode, Boolean.FALSE);
            // 分批操作
            List<List<String>> partition = Lists.partition(caseCodes, 200);
            partition.parallelStream().forEach(c -> doBatchChangeVersion(c, productCode, sourceVersionCode, targetVersionCode, finalParentCode, finalPath, finalExistNames, operator));
        }

        //清掉没有用例的分组
        if (null != deleteOriginalGroup && deleteOriginalGroup && CollectionUtil.isNotEmpty(parentPathList)) {
            deleteOriginalGroup(parentPathList, productCode, sourceVersionCode, operator);
        } else {
            log.info("deleteOriginalGroup推送长链接结束-TEST_CASE_CHANGE_VERSION:" + sourceVersionCode);
            emit(TestEventEnums.TEST_CASE_CHANGE_VERSION.toReactiveId(sourceVersionCode), "FINISH");
        }
    }

    private void deleteOriginalGroup(List<TestCasePathVO> parentCodeList, String productCode, String versionCode, User operator) {
        if (CollectionUtil.isEmpty(parentCodeList)) {
            return;
        }
        boolean finish = false;
        int flag = 0;
        for (TestCasePathVO item : parentCodeList) {
            flag++;
            finish = parentCodeList.size() == flag;
            //统计分组实际用例数
            List<CountCaseVO> realCountList = testcaseRepository.countByParentCodeList(productCode, versionCode,
                    Arrays.asList(item.getPath().split("\\.")));
            log.info(item + "_deleteOriginalGroup_countList ->{}", JsonUtil.toJSON(realCountList));
            if (CollectionUtil.isEmpty(realCountList)) {
                doDeleteModule(versionCode, item.getParentCode(), operator, true);
                break;
            }
            //末级分组是否为空
            for (int i = 0; i < realCountList.size(); i++) {
                CountCaseVO vo = realCountList.get(i);
                if (i == 0) {
                    if ((vo.getModuleCode().equals(item.getParentCode()) && vo.getCaseNum() == 0 && vo.getModuleNum() == 0)) {
                        doDeleteModule(versionCode, vo.getModuleCode(), operator, finish);
                    } else if (!vo.getModuleCode().equals(item.getParentCode())) {
                        doDeleteModule(versionCode, item.getParentCode(), operator, finish);
                        if (vo.getCaseNum() == 0 && vo.getModuleNum() == 1) {
                            doDeleteModule(versionCode, vo.getModuleCode(), operator, finish);
                        } else {
                            sendMsg(versionCode, finish);
                            break;
                        }
                    } else {
                        sendMsg(versionCode, finish);
                        break;
                    }
                } else if (vo.getCaseNum() == 0 && vo.getModuleNum() == 1) {
                    doDeleteModule(versionCode, vo.getModuleCode(), operator, finish);
                } else {
                    sendMsg(versionCode, finish);
                    break;
                }
            }
        }
        emit(TestEventEnums.TEST_CASE_CHANGE_VERSION.toReactiveId(versionCode), finish ? "FINISH" : "RUNNING");
    }

    private void doDeleteModule(String versionCode, String code, User operator, boolean finish) {
        emit(TestEventEnums.TEST_CASE_CHANGE_VERSION.toReactiveId(versionCode), "RUNNING");
        DeleteTestcaseEvent deleteTestcaseEvent = new DeleteTestcaseEvent();
        deleteTestcaseEvent.setCode(code);
        deleteTestcaseEvent.setType(TestcaseTypeEnum.MANUAL);
        deleteTestcaseEvent.setAttribute(TestcaseAttributeEnum.MODULE);
        deleteTestcaseEvent.setTransactor(operator);
        testcaseRepository.deleteTestcase(deleteTestcaseEvent);
        if (finish) {
            //推送消息告诉前端结束了
            log.info("deleteOriginalGroup推送长链接结束-TEST_CASE_CHANGE_VERSION:" + versionCode);
            emit(TestEventEnums.TEST_CASE_CHANGE_VERSION.toReactiveId(versionCode), "FINISH");
        }
    }

    private void sendMsg(String versionCode, boolean finish) {
        if (finish) {
            log.info("deleteOriginalGroup推送长链接结束-TEST_CASE_CHANGE_VERSION:" + versionCode);
            emit(TestEventEnums.TEST_CASE_CHANGE_VERSION.toReactiveId(versionCode), "FINISH");
            return;
        }
        emit(TestEventEnums.TEST_CASE_CHANGE_VERSION.toReactiveId(versionCode), "RUNNING");
    }

    @Async
    public void batchCopyCase(List<String> caseCodes, String parentCode, Boolean setCore, User operator) {
        int minSize = qcConfigBasicService.getMinSize();
        if (CollectionUtil.isNotEmpty(caseCodes) && caseCodes.size() > minSize) {
            throw new ServiceException("批量复制一次最多支持500条数据");
        }
        batchMoveOrCopyCase(caseCodes, parentCode, operator, COPY_FLAG, setCore);
    }

    public void batchModifyCaseDutyUser(List<String> caseCodes, User dutyUser, User operator) {
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        int bigSize = qcConfigBasicService.getBigSize();
        if (CollectionUtil.isNotEmpty(caseCodes) && caseCodes.size() > bigSize) {
            throw new ServiceException("批量修改责任人一次最多支持2000条数据");
        }
        List<List<String>> partCodes = Lists.partition(caseCodes, 200);
        partCodes.forEach(codes -> doBatchUpdateDutyUser(codes, dutyUser, operator));
    }


    public void batchModifyCaseGrade(List<String> caseCodes, TestcasePriorityEnum grade, User operator) {
        int bigSize = qcConfigBasicService.getBigSize();
        if (CollectionUtil.isNotEmpty(caseCodes) && caseCodes.size() > bigSize) {
            throw new ServiceException("批量修改用例等级一次最多支持2000条数据");
        }
        List<List<String>> partCodes = Lists.partition(caseCodes, 200);
        partCodes.forEach(codes -> doBatchUpdateCaseGrade(codes, grade, operator));
    }

    public void batchMoveCase(List<String> caseCodes, String newParentCode, Boolean setCore, User operator) {
        int bigSize = qcConfigBasicService.getBigSize();
        if (CollectionUtil.isNotEmpty(caseCodes) && caseCodes.size() > bigSize) {
            throw new ServiceException("批量移动一次最多支持2000条数据");
        }
        batchMoveOrCopyCase(caseCodes, newParentCode, operator, MOVE_FLAG, setCore);
    }

    public void batchRemoveCase(List<String> caseCodes, User operator) {
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        int bigSize = qcConfigBasicService.getBigSize();
        if (CollectionUtil.isNotEmpty(caseCodes) && caseCodes.size() > bigSize) {
            throw new ServiceException("批量删除一次最多支持2000条数据");
        }
        List<List<String>> partCodes = Lists.partition(caseCodes, 200);
        partCodes.forEach(codes -> doBatchRemoveCase(codes, operator));
    }


    @Async
    public void batchSetCore(Boolean useOriginalGroup, List<String> caseCodes, String targetParentCode, User
            toSimpleUser) {
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        int minSize = qcConfigBasicService.getMinSize();
        if (CollectionUtil.isNotEmpty(caseCodes) && caseCodes.size() > minSize) {
            throw new ServiceException("批量设置核心用例一次最多支持" + minSize + "条数据");
        }

        //校验产品
        TestcaseEntityDO productCaseEntity = testcaseRepository.selectTestcaseByCode(caseCodes.get(0));
        String productCode = productCaseEntity != null ? productCaseEntity.getProductCode() : "";

        if (useOriginalGroup) {
            doSetCoreUseOriginalGroup(caseCodes, toSimpleUser, productCode, productCaseEntity.getVersionCode());
        } else {
            doSetCoreUseTargetGroup(caseCodes, targetParentCode, toSimpleUser, productCode);
        }
    }

    /**
     * @param caseCodeList
     * @param operator
     * @param productCode
     */
    private void doSetCoreUseOriginalGroup(List<String> caseCodeList,
                                           User operator,
                                           String productCode,
                                           String versionCode) {
        //查原版本信息
        String versionName = "未关联版本用例";
        if (!"NONE_VERSION".equals(versionCode)) {
            VersionInfoVO versionInfoVO = projectRpcService.findVersionBaseInfoQuery(versionCode);
            versionName = null != versionInfoVO ? versionInfoVO.getName() : versionCode;
        }

        //核心用例下该版本分组
        List<TestcaseEntityDO> versionModuleVOList = testcaseRepository.selectModuleByVersionName(versionName, productCode);

        //新增版本分组
        String moduleName = versionName + (CollectionUtil.isEmpty(versionModuleVOList) ? "" : "[副本" + versionModuleVOList.size() + "]");
        AddTestcaseEvent versionModuleEvent = buildAddTestcaseEvent(moduleName, productCode, versionCode, "", "");
        testcaseRepository.addTestcase(versionModuleEvent);

        //新增目标分组
        List<TestcaseGroupVO> targetModuleList = mergeModuleForSetCore(caseCodeList,
                productCode,
                versionCode,
                operator,
                versionModuleEvent.getCode(),
                versionModuleEvent.getName()
        );

        //设置核心用例
        targetModuleList.forEach(target -> {
            if (CollectionUtil.isNotEmpty(target.getCodeList()) && target.getCodeList().size() > 200) {
                List<List<String>> partition = Lists.partition(target.getCodeList(), 200);
                partition.parallelStream().forEach(c -> doBatchSetCore(c, target.getParentCode(), target.getParentPath(), null, operator));
            } else {
                doBatchSetCore(target.getCodeList(), target.getParentCode(), target.getParentPath(), null, operator);
            }
        });
    }


    private AddTestcaseEvent buildAddTestcaseEvent(String name, String productCode, String versionCode, String
            parentCode, String path) {
        AddTestcaseEvent addTestcaseEvent = new AddTestcaseEvent();
        addTestcaseEvent.setCode(aggregateIdGenerateService.generateId(AggregateType.TEST_CASE));
        addTestcaseEvent.setAttribute(TestcaseAttributeEnum.MODULE);
        addTestcaseEvent.setType(TestcaseTypeEnum.MANUAL);
        addTestcaseEvent.setSetCore(Boolean.TRUE);
        addTestcaseEvent.setLayer(0);
        addTestcaseEvent.setName(name);
        addTestcaseEvent.setParentCode(parentCode);
        addTestcaseEvent.setPath(path);
        addTestcaseEvent.setProductCode(productCode);
        addTestcaseEvent.setVersionCode(versionCode);
        return addTestcaseEvent;
    }

    /**
     * 合并目标版本与源版本中重名分组
     *
     * @param caseCodeList      用例code集合
     * @param productCode       产品code
     * @param sourceVersionCode 源版本code
     * @param operator
     * @return
     */
    private List<TestcaseGroupVO> mergeModuleForSetCore(List<String> caseCodeList,
                                                        String productCode,
                                                        String sourceVersionCode,
                                                        User operator,
                                                        String versionModuleCode,
                                                        String versionModuleName) {
        List<TestcaseGroupVO> resultList = new ArrayList<>();
        //待移动用例分组信息
        List<TestcaseParentInfoVO> voList = testcaseRepository.selectGroupByCaseCodeList(caseCodeList,
                productCode,
                sourceVersionCode,
                TestcaseTypeEnum.MANUAL.name(),
                Boolean.FALSE);

        //未分组用例
        List<String> noGroupCaseList = testcaseRepository.selectNoGroupCaseList(caseCodeList);
        if (CollectionUtil.isNotEmpty(noGroupCaseList)) {
            TestcaseGroupVO result = new TestcaseGroupVO();
            result.setParentName(versionModuleName);
            result.setParentCode(versionModuleCode);
            result.setParentPath(versionModuleCode);
            result.setCodeList(noGroupCaseList);
            resultList.add(result);
        }

        //根据parentCode分组，校验目标用例中分组是否重复
        Map<String, List<TestcaseParentInfoVO>> groupMap = voList.stream().collect(Collectors.groupingBy(TestcaseParentInfoVO::getParentCode));
        groupMap.entrySet().forEach(item -> {
            TestcaseParentInfoVO vo = item.getValue().get(0);
            List<String> itemCaseCodeList = item.getValue().stream().map(TestcaseParentInfoVO::getCode).collect(Collectors.toList());
            if (StringUtil.isBlank(vo.getParentPath())) {
                //校验一层分组情况
                TestcaseGroupVO result = testcaseRepository.buildSingleModuleForSetCore(vo.getParentCode(),
                        versionModuleCode,
                        versionModuleCode,
                        operator);
                if (null != result) {
                    result.setCodeList(itemCaseCodeList);
                    resultList.add(result);
                }
            } else {
                //校验多层分组情况
                TestcaseGroupVO result = testcaseRepository.buildComplexModuleForSetCore(versionModuleCode,
                        versionModuleName,
                        vo.getParentCode(),
                        vo.getParentName(),
                        sourceVersionCode,
                        productCode,
                        operator);
                result.setCodeList(itemCaseCodeList);
                resultList.add(result);
            }
        });
        return resultList;
    }


    /**
     * 设置核心用例-目标分组
     *
     * @param caseCodes
     * @param targetParentCode
     * @param toSimpleUser
     * @param productCode
     */
    private void doSetCoreUseTargetGroup(List<String> caseCodes,
                                         String targetParentCode,
                                         User toSimpleUser,
                                         String productCode) {
        //校验选择的用例是否重名
        checkSameName(caseCodes);
        if (TestcaseGroupTypeEnum.ALL.name().equals(targetParentCode)
                || TestcaseGroupTypeEnum.NO_GROUP.name().equals(targetParentCode)) {
            targetParentCode = "";
        }
        //目标分组不为空，校验分组，匹配path
        String path = "";
        if (StringUtil.isNotBlank(targetParentCode)) {
            TestcaseEntityDO targetModuleEntity = testcaseRepository.findTargetModule(targetParentCode);
            if (targetModuleEntity == null) {
                throw new ServiceException("目标组不存在");
            }
            path = StringUtil.isEmpty(targetModuleEntity.getPath()) ? targetParentCode : targetModuleEntity.getPath() + "." + targetParentCode;
        }
        // 获取目标分组下核心用例的名称
        List<TestcaseEntityDO> originalCaseNameList = testcaseRepository.findTargetModuleCase(targetParentCode, productCode);
        String finalParentCode = targetParentCode;
        String finalPath = path;
        // 分批操作
        List<List<String>> partition = Lists.partition(caseCodes, 200);
        partition.parallelStream().forEach(c -> doBatchSetCore(c, finalParentCode, finalPath, originalCaseNameList, toSimpleUser));
    }

    public void changePlanCaseExecutor(ChangePlanCaseExecutorCommand command) {
        log.info("ChangePlanCaseExecutorCommand >>> {}", command.getAggregateId());
        PlanCaseExecutorChangedEvent event = testcaseDomainConverter.convertor(command);
        testcaseRepository.changePlanCaseExecutor(event);
        apply(event);
    }

    public void changeTestcaseDutyUser(ChangeTestcaseDutyUserCommand command) {
        log.info("ChangeTestcaseDutyUserCommand >>> {}", command.getAggregateId());
        TestcaseDutyUserChangedEvent event = testcaseDomainConverter.convertor(command);
        testcaseRepository.changeTestcaseDutyUser(event);
        apply(event);
    }


    /**
     * 校验Tag
     *
     * @param businessCode
     * @param domain
     * @param tagNames
     * @param productTagMax
     * @param testcaseTagMax
     */
    @SneakyThrows
    public void checkTags(String businessCode, DomainEnum domain, List<String> tagNames,
                          int productTagMax, int testcaseTagMax) {
        int count2 = testcaseTagRepository.selectCountByExample(businessCode, domain);
        if (DomainEnum.PRODUCT.equals(domain) && count2 + tagNames.size() > productTagMax) {
            throw new InterruptedException("每个产品最多可关联" + productTagMax + "个标签！已关联" + count2 +
                    "个！");
        }
        if (DomainEnum.TESTCASE.equals(domain) && count2 + tagNames.size() > testcaseTagMax) {
            throw new InterruptedException("每个用例最多可关联" + testcaseTagMax + "个标签！已关联" + count2 +
                    "个！用例编号[" + businessCode + "]");
        }
    }


    /**
     * 校验用例和计划是否同一个版本
     *
     * @param caseCode 用例code
     * @param planList 计划
     * @return
     */
    private void checkVersion(String caseCode, List<BatchTestPlanVO> planList) {
        Set<String> planCodeSet = planList.stream().map(BatchTestPlanVO::getTestPlanCode).collect(Collectors.toSet());
        List<TmTestPlanEntityDO> planEntityList = iTmTestPlanRepository.selectTmTestPlanEntityDOByPlanCodeList(planCodeSet);

        if (CollectionUtil.isEmpty(planEntityList)) {
            throw new ServiceException("关联计划不存在");
        }

        planList.forEach(plan -> {
            TmTestPlanEntityDO planEntityDO = planEntityList.stream()
                    .filter(en -> en.getCode().equals(plan.getTestPlanCode()))
                    .findAny().orElse(null);
            if (null == planEntityDO || !planEntityDO.getEnable()
                    || TestPlanNewStatusEnum.TERMINATED.equals(planEntityDO.getStatus())
                    || TestPlanNewStatusEnum.COMPLETED.equals(planEntityDO.getStatus())) {
                throw new ServiceException("测试计划不支持关联用例");
            }
            if (null == planEntityDO.getStageStatus() || CollectionUtil.isEmpty(plan.getTestStage())) {
                return;
            }
            if (plan.getTestStage().stream().anyMatch(ts -> TestPlanNewStatusEnum.COMPLETED.name().equals(planEntityDO.getStageStatus().get(ts.name())))) {
                throw new ServiceException("已完成阶段不支持关联用例");
            }
        });

        TestcaseEntityDO caseEntity = testcaseRepository.selectTestcaseByCode(caseCode);

        if (null == caseEntity) {
            throw new ServiceException("用例不存在");
        }

        if (!caseEntity.getType().name().equals(TestcaseTypeEnum.MANUAL.name()) || caseEntity.getSetCore()) {
            return;
        }
    }

    /**
     * 批量用例加入测试计划
     *
     * @param caseCodes
     * @param planList
     * @param operator
     */
    public void doBatchJoinPlan(List<String> caseCodes, List<BatchTestPlanVO> planList, User operator) {
        Map<String, Map<TestPlanStageEnum, Set<String>>> planStageCaseMap = new HashMap<>();
        for (BatchTestPlanVO plan : planList) {
            if (CollectionUtil.isEmpty(plan.getTestStage())) {
                continue;
            }
            List<TmTestPlanCaseEntityDO> planCase = iTmTestPlanCaseRepository.selectTestPlanCaseByPlanCodeAndTestStage(plan.getTestPlanCode(), plan.getTestStage());
            HashMap<TestPlanStageEnum, Set<String>> initStageCaseMap = new HashMap<>();
            plan.getTestStage().forEach(t -> initStageCaseMap.put(t, new HashSet<>()));
            if (CollectionUtil.isNotEmpty(planCase)) {
                initStageCaseMap.putAll(planCase.stream()
                        .collect(Collectors.groupingBy(TmTestPlanCaseEntityDO::getTestStage, Collectors.mapping(TmTestPlanCaseEntityDO::getCaseCode, Collectors.toSet()))));
            }
            planStageCaseMap.put(plan.getTestPlanCode(), initStageCaseMap);
        }

        // 获取case的类型
        List<TestcaseEntityDO> cases = testcaseRepository.selectTestcaseByCodeList(caseCodes);
        Map<String, TestcaseTypeEnum> caseTypeMap = cases.parallelStream().collect(Collectors.toMap(TestcaseEntityDO::getCode, TestcaseEntityDO::getType));

        // 过滤已停用的code
        caseCodes.removeIf(c -> !caseTypeMap.containsKey(c));

        // 查询草稿态的用例
        List<String> sceneCaseList = this.queryAllEditSceneCase(caseCodes);
        if (CollectionUtils.isNotEmpty(sceneCaseList)) {
            // 过滤草稿的code
            caseCodes.removeIf(sceneCaseList::contains);
        }

        // 构造需要加入计划的 TmTestPlanCaseEntity 对象
        ArrayList<TmTestPlanCaseEntityDO> waitInsert = new ArrayList<>();
        for (Map.Entry<String, Map<TestPlanStageEnum, Set<String>>> planCaseMap : planStageCaseMap.entrySet()) {
            String planCode = planCaseMap.getKey();
            for (Map.Entry<TestPlanStageEnum, Set<String>> stageCaseMap : planCaseMap.getValue().entrySet()) {
                TestPlanStageEnum stage = stageCaseMap.getKey();
                Set<String> existCodes = stageCaseMap.getValue();
                for (String caseCode : caseCodes) {
                    // 过滤已存在计划节点的用例
                    if (existCodes != null && existCodes.contains(caseCode)) {
                        continue;
                    }
                    waitInsert.add(buildPlanCaseEntity(caseCode, planCode, stage, caseTypeMap.get(caseCode), operator));
                }
            }
        }
        if (CollectionUtil.isEmpty(waitInsert)) {
            return;
        }
        // 分批插入
        List<List<TmTestPlanCaseEntityDO>> partitions = Lists.partition(waitInsert, 200);
        partitions.forEach(p -> iTmTestPlanCaseRepository.batchSave(p));
        // 日志
        EVENT_HANDLER_EXECUTOR.execute(() -> {
            List<String> operateCodes = waitInsert.stream().map(TmTestPlanCaseEntityDO::getOperateCaseCode).collect(Collectors.toList());
            String eventClassName = AddBatchCaseInBatchTestPlanEvent.class.getName();
            logManager.createLogs(operateCodes.stream().map(c -> new ActionLogDO(c, "加入了计划", "",
                    eventClassName, operator.getUserName(), operator.getUserId())).collect(Collectors.toList()));
        });
    }

    private List<String> queryAllEditSceneCase(List<String> caseCodes) {
        if (CollectionUtils.isEmpty(caseCodes)) {
            return new ArrayList<>();
        }
        List<TestcaseEntityDO> testcaseDOList = testcaseRepository.selectAutomaticCaseByCodeList(caseCodes);
        if (CollectionUtils.isNotEmpty(testcaseDOList)) {
            Set<String> autoSourceList = testcaseDOList.stream()
                    .map(TestcaseEntityDO::getAutomaticSourceCode).collect(Collectors.toSet());
            return CollectionUtils.isEmpty(autoSourceList) ? new ArrayList<>() :
                    this.queryAllEditScene(testcaseDOList, new ArrayList<>(autoSourceList));
        }
        return new ArrayList<>();
    }

    private List<String> queryAllEditScene(List<TestcaseEntityDO> testcaseDOList, List<String> autoSourceList) {
        if (CollectionUtils.isEmpty(autoSourceList)) {
            return new ArrayList<>();
        }
        List<SceneInfoEntityDO> sceneList = apiTestRepository.querySceneByAutoSourceCode(new ArrayList<>(autoSourceList));
        if (CollectionUtils.isNotEmpty(sceneList)) {
            Set<String> editList = sceneList.stream().map(SceneInfoEntityDO::getAutomaticSourceCode)
                    .collect(Collectors.toSet());
            return CollectionUtils.isEmpty(editList) ? new ArrayList<>() :
                    this.filterByAutomaticSourceCode(testcaseDOList, new ArrayList<>(editList));
        }
        return new ArrayList<>();
    }

    private List<String> filterByAutomaticSourceCode(List<TestcaseEntityDO> testcaseDOList, List<String> editList) {
        if (CollectionUtils.isEmpty(editList)) {
            return new ArrayList<>();
        }
        List<String> caseCodes = testcaseDOList.stream()
                .filter(t -> editList.contains(t.getAutomaticSourceCode()))
                .map(TestcaseEntityDO::getCode).collect(Collectors.toList());
        return CollectionUtils.isEmpty(caseCodes) ? new ArrayList<>() : caseCodes;
    }

    /**
     * 构建PlanCaseEntity
     *
     * @param caseCode
     * @param planCode
     * @param stage
     * @param caseType
     * @param operator
     * @return
     */
    private TmTestPlanCaseEntityDO buildPlanCaseEntity(String caseCode, String planCode,
                                                       TestPlanStageEnum stage, TestcaseTypeEnum caseType, User operator) {
        TmTestPlanCaseEntityDO entity = new TmTestPlanCaseEntityDO();
        entity.setCaseCode(caseCode);
        entity.setPlanCode(planCode);
        entity.setTestStage(stage);
        entity.setCaseType(caseType);
        entity.setStatus(caseType.equals(TestcaseTypeEnum.MANUAL) ? TestPlanCaseStatusEnum.INITIAL : TestPlanCaseStatusEnum.NOT_STARTED);
        entity.setOperateCaseCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        entity.setCreator(operator.getUserName());
        entity.setCreatorId(operator.getUserId());
        fillUserAndDate(entity, operator);
        return entity;
    }

    /**
     * 传递数据
     *
     * @param entity
     * @param operator
     */
    private void fillUserAndDate(BaseEntityDO entity, User operator) {
        if (entity == null) {
            return;
        }
        if (operator != null) {
            entity.setModifier(operator.getUserName());
            entity.setModifierId(operator.getUserId());
        }
        entity.setGmtModified(new Date());
    }

    /**
     * 用例添加附件
     *
     * @param event
     */
    public void addTestcaseAttachment(TestcaseAttachmentAddedEvent event) {
        int count = testcaseAttachmentRepository.selectCountByCaseCodeList(event.getBusinessCode());
        if (count >= TESTCASE_ATTACHMENT_MAX_NUM) {
            throw new ServiceException(String.format("每个用例最多可上传%s个附件！", TESTCASE_ATTACHMENT_MAX_NUM));
        }
        testcaseAttachmentRepository.addTestcaseAttachment(event);
    }

    /**
     * 用例添加标签
     *
     * @param event
     */
    public void addTestcaseTag(TestcaseTagAddedEvent event) {
        int count1 = testcaseTagRepository.selectCountByExample(event.getBusinessCode(), event.getTagName());
        if (count1 > 0) {
            return;
        }
        int count2 = testcaseTagRepository.selectCountByExample(event.getBusinessCode(), event.getDomain());
        int productTagMax = qcConfigBasicService.getProductTagMax();
        int testcaseTagMax = qcConfigBasicService.getTestcaseTagMax();
        if (DomainEnum.PRODUCT.equals(event.getDomain()) && count2 >= productTagMax) {
            throw new ServiceException(String.format("每个产品最多可新增%s个自定义标签！", productTagMax));
        }
        if (DomainEnum.TESTCASE.equals(event.getDomain()) && count2 >= testcaseTagMax) {
            throw new ServiceException(String.format("每个用例最多可关联%s个标签！", testcaseTagMax));
        }
        testcaseTagRepository.addTestcaseTag(event);
    }

    /**
     * 批量更新用例状态
     *
     * @param codes
     * @param status
     * @param reason
     * @param operator
     */
    private void doBatchUpdateCaseStatus(List<String> codes,
                                         TestcaseStatusEnum status,
                                         TestcaseAbandonReasonEnum reason,
                                         User operator) {
        if (CollectionUtil.isEmpty(codes)) {
            return;
        }
        TestcaseEntityDO entity = new TestcaseEntityDO();
        entity.setStatus(status);
        entity.setAbandonReason(reason);
        fillUserAndDate(entity, operator);
        testcaseRepository.updateTestcaseByCodeList(codes, entity);
        EVENT_HANDLER_EXECUTOR.execute(() -> {
            log.info("handle TestcaseStatusChangedEvent");
            // 维护用例本身日志信息
            String eventClassName = TestcaseStatusChangedEvent.class.getName();
            logManager.createLogs(codes.stream().map(c ->
                    new ActionLogDO(
                            c,
                            (TestcaseStatusEnum.DISABLE.equals(status)) ? "停用了用例" : "启用了用例",
                            (TestcaseStatusEnum.DISABLE.equals(status) && null != reason) ? (String.format("停用原因: %s", reason.getDesc())) : "",
                            eventClassName,
                            operator.getUserName(),
                            operator.getUserId())
            ).collect(Collectors.toList()));

            // 维护计划里的用例日志信息
            List<TmTestPlanCaseEntityDO> planCase = iTmTestPlanCaseRepository.selectTmTestPlanCaseEntityDOListByCaseCodeList(codes);
            if (CollectionUtil.isEmpty(planCase)) {
                return;
            }
            logManager.createLogs(planCase.stream().map(c ->
                    new ActionLogDO(
                            c.getOperateCaseCode(),
                            (TestcaseStatusEnum.DISABLE.equals(status)) ? "停用了用例" : "启用了用例",
                            (TestcaseStatusEnum.DISABLE.equals(status) && null != reason) ? (String.format("停用原因: %s", reason.getDesc())) : "",
                            eventClassName,
                            operator.getUserName(),
                            operator.getUserId())
            ).collect(Collectors.toList()));
        });
    }

    /**
     * 合并目标版本与源版本中重名分组
     *
     * @param caseCodeList      用例code集合
     * @param productCode       产品code
     * @param sourceVersionCode 源版本code
     * @param targetVersionCode 目标版本code
     * @param caseType          用例类型
     * @param operator
     * @return
     */
    private List<TestcaseGroupVO> mergeModule(List<String> caseCodeList, String productCode, String
            sourceVersionCode,
                                              String targetVersionCode, TestcaseTypeEnum caseType, User operator) {
        List<TestcaseGroupVO> resultList = new ArrayList<>();
        //待移动用例分组信息
        List<TestcaseParentInfoVO> voList = testcaseRepository.selectGroupByCaseCodeList(caseCodeList, productCode,
                sourceVersionCode, caseType.name(), Boolean.FALSE);
        //未分组用例
        if (CollectionUtil.isEmpty(voList)) {
            TestcaseGroupVO result = new TestcaseGroupVO();
            result.setParentName("");
            result.setParentCode("");
            result.setParentPath("");
            result.setCodeList(caseCodeList);
            resultList.add(result);
            return resultList;
        }
        //根据parentCode分组，校验目标用例中分组是否重复
        Map<String, List<TestcaseParentInfoVO>> groupMap = voList.stream().collect(Collectors.groupingBy(TestcaseParentInfoVO::getParentCode));
        groupMap.entrySet().forEach(item -> {
            TestcaseParentInfoVO vo = item.getValue().get(0);
            List<String> itemCaseCodeList = item.getValue().stream().map(TestcaseParentInfoVO::getCode).collect(Collectors.toList());
            if (StringUtil.isBlank(vo.getParentPath())) {
                //校验一层分组情况
                TestcaseGroupVO result = testcaseRepository.buildSingleModule(vo.getParentCode(), vo.getParentName(), targetVersionCode, productCode, operator);
                result.setCodeList(itemCaseCodeList);
                resultList.add(result);
            } else {
                //校验多层分组情况
                TestcaseGroupVO result = testcaseRepository.buildComplexModule(vo.getParentCode(), vo.getParentName(), sourceVersionCode, targetVersionCode, productCode, operator);
                result.setCodeList(itemCaseCodeList);
                resultList.add(result);
            }
        });
        return resultList;
    }


    /**
     * 修改版本
     *
     * @param caseCodes         用例code集合
     * @param sourceVersionCode 源版本code
     * @param targetVersionCode 目标版本code
     * @param parentCode        分组code
     * @param path              路径
     * @param existNames        已存在用例名
     * @param operator          操作人
     */
    private void doBatchChangeVersion(List<String> caseCodes, String productCode, String
            sourceVersionCode, String targetVersionCode, String parentCode, String path,
                                      Set<String> existNames, User operator) {
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        // 有重复名称的用例剔除掉
        List<String> filteredCodes = testcaseRepository.filterCodeByName(caseCodes, existNames, MOVE_FLAG, Boolean.FALSE, productCode, sourceVersionCode);
        if (CollectionUtil.isEmpty(filteredCodes)) {
            return;
        }
        // 开始更新库
        TestcaseEntityDO entity = new TestcaseEntityDO();
        entity.setVersionCode(targetVersionCode);
        entity.setParentCode(parentCode);
        entity.setPath(path);
        fillUserAndDate(entity, operator);
        testcaseRepository.updateTestcaseByFilteredCodes(entity, filteredCodes);
        // 记录日志
        EVENT_HANDLER_EXECUTOR.execute(() -> {
            log.info("handle BatchChangeCaseVersionEvent");
            String eventClassName = BatchChangeCaseVersionEvent.class.getName();
            logManager.createLogs(filteredCodes.stream().map(c -> new ActionLogDO(c, "修改了用例版本", "修改为 >>> " + targetVersionCode,
                    eventClassName, operator.getUserName(), operator.getUserId())).collect(Collectors.toList()));
        });
    }

    /**
     * 批量移动或者删除用例
     *
     * @param caseCodes
     * @param newParentCode
     * @param operator
     * @param action
     * @param setCore
     */
    private void batchMoveOrCopyCase(List<String> caseCodes, String newParentCode, User operator, String
            action, Boolean setCore) {
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        String parentCode = newParentCode;
        String parentName = "";
        if (TestcaseGroupTypeEnum.ALL.name().equals(newParentCode)
                || TestcaseGroupTypeEnum.NO_GROUP.name().equals(newParentCode)) {
            parentCode = "";
            parentName = TestcaseGroupTypeEnum.NO_GROUP.getDesc();
        }
        // 获取产品名称
        TestcaseEntityDO simpleCase = testcaseRepository.selectProductCodeAndVersionCodeByCaseCode(caseCodes.get(0));
        String productCode = simpleCase != null ? simpleCase.getProductCode() : "";
        String versionCode = simpleCase != null ? simpleCase.getVersionCode() : "";
        // 获取 parentCode, path, parentName
        String path = "";
        if (!StringUtil.isEmpty(parentCode)) {
            TestcaseEntityDO entity = testcaseRepository.selectModuleByParentCodeAndVersionCode(parentCode, versionCode, TestcaseAttributeEnum.MODULE, setCore);
            if (entity == null) {
                throw new ServiceException("目标组不存在");
            }
            path = StringUtil.isEmpty(entity.getPath()) ? parentCode : entity.getPath() + "." + parentCode;
            parentName = entity.getName();
        }
        // 获取新分组下用例的名称
        List<TestcaseEntityDO> cases = testcaseRepository.selectNewModuleCaseName(parentCode, TestcaseAttributeEnum.TESTCASE, setCore, productCode, versionCode);
        Set<String> existNames = new HashSet<>();
        if (CollectionUtil.isNotEmpty(caseCodes)) {
            existNames = cases.stream().map(TestcaseEntityDO::getName).collect(Collectors.toSet());
        }
        Set<String> finalExistNames = existNames;
        String finalParentCode = parentCode;
        String finalParentName = parentName;
        String finalPath = path;
        // 分批操作
        List<List<String>> partition = Lists.partition(caseCodes, 200);
        if (MOVE_FLAG.equals(action)) {
            partition.forEach(c -> doBatchMoveTestCase(c, finalParentCode, finalPath, finalParentName, finalExistNames, operator, setCore, productCode, versionCode));
        }
        if (COPY_FLAG.equals(action)) {
            partition.parallelStream().forEach(c -> doBatchCopyCase(c, finalParentCode, finalPath, finalParentName, finalExistNames, operator, setCore, productCode, versionCode));
        }
    }

    /**
     * 批量删除用例
     *
     * @param caseCodes
     * @param operator
     */
    private void doBatchRemoveCase(List<String> caseCodes, User operator) {

        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        updatePlanCaseRelevant(caseCodes, operator);
        TestcaseEntityDO entity = new TestcaseEntityDO();
        entity.setEnable(false);
        fillUserAndDate(entity, operator);
        testcaseRepository.updateTestcaseByCodeList(caseCodes, entity);
    }

    /**
     * 更新用例关联模块
     *
     * @param caseCodes
     * @param operator
     */
    private void updatePlanCaseRelevant(List<String> caseCodes, User operator) {
        // 查询 testPlanCase 表
        List<TmTestPlanCaseEntityDO> planCases = iTmTestPlanCaseRepository.selectTmTestPlanCaseEntityDOListByCaseCodeList(caseCodes);
        if (CollectionUtil.isEmpty(planCases)) {
            return;
        }
        // 查询 testPlan
        Set<String> planCodes = planCases.stream().map(TmTestPlanCaseEntityDO::getPlanCode).collect(Collectors.toSet());
        List<TmTestPlanEntityDO> plans = iTmTestPlanRepository.selectTmTestPlanEntityDOByPlanCodeList(planCodes);
        // 如果没找到计划，直接删除
        if (CollectionUtil.isEmpty(plans)) {
            doRemoveTestPlanCase(planCases.stream().map(TmTestPlanCaseEntityDO::getId).collect(Collectors.toList()));
            return;
        }
        // 根据计划状态，阶段状态判断
        Map<String, TmTestPlanEntityDO> planStatusMap = plans.stream().collect(Collectors.toMap(TmTestPlanEntityDO::getCode, p -> p));
        ArrayList<Long> updateIds = new ArrayList<>();
        ArrayList<Long> deleteIds = new ArrayList<>();
        for (TmTestPlanCaseEntityDO pc : planCases) {
            // 不包含计划，则直接删除
            if (!planStatusMap.containsKey(pc.getPlanCode())) {
                deleteIds.add(pc.getId());
                continue;
            }
            // 已完成，则进行更新
            TmTestPlanEntityDO testPlan = planStatusMap.get(pc.getPlanCode());
            if (TestPlanNewStatusEnum.COMPLETED.equals(testPlan.getStatus())) {
                updateIds.add(pc.getId());
                continue;
            }
            // 判断阶段
            if (pc.getTestStage() == null) {
                deleteIds.add(pc.getId());
                continue;
            }
            // 阶段是否已完成，如果完成则进行更新
            if (testPlan.getStageStatus() != null
                    && TestPlanStageStatusEnum.COMPLETED.name().equals(testPlan.getStageStatus().getOrDefault(pc.getTestStage().name(), ""))) {
                updateIds.add(pc.getId());
                continue;
            }
            deleteIds.add(pc.getId());
        }
        doRemoveTestPlanCase(deleteIds);
        doUpdateTestPlanEnable(updateIds, operator);
    }

    /**
     * 删除测试计划中的用例
     *
     * @param ids
     */
    private void doRemoveTestPlanCase(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        iTmTestPlanCaseRepository.deleteByIdList(ids);
    }

    /**
     * 更新测试计划中的用例
     *
     * @param ids
     * @param operator
     */
    private void doUpdateTestPlanEnable(List<Long> ids, User operator) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        TmTestPlanCaseEntityDO tmTestPlanCaseEntity = new TmTestPlanCaseEntityDO();
        tmTestPlanCaseEntity.setEnable(false);
        fillUserAndDate(tmTestPlanCaseEntity, operator);
        iTmTestPlanCaseRepository.updateByIdList(tmTestPlanCaseEntity, ids);
    }

    /**
     * 批量移动用例
     *
     * @param caseCodes
     * @param parentCode
     * @param path
     * @param parentName
     * @param existNames
     * @param operator
     * @param setCore
     * @param productCode
     * @param versionCode
     */
    private void doBatchMoveTestCase(List<String> caseCodes, String parentCode, String path,
                                     String parentName, Set<String> existNames, User operator, Boolean setCore,
                                     String productCode, String versionCode) {
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        // 有重复名称的用例剔除掉
        List<String> filteredCodes = testcaseRepository.filterCodeByName(caseCodes, existNames, MOVE_FLAG, setCore, productCode, versionCode);
        if (CollectionUtil.isEmpty(filteredCodes)) {
            return;
        }
        // 开始更新库
        TestcaseEntityDO entity = new TestcaseEntityDO();
        entity.setParentCode(parentCode);
        entity.setPath(path);
        fillUserAndDate(entity, operator);
        testcaseRepository.updateTestcaseByFilteredCodes(entity, filteredCodes);
        // 记录日志
        EVENT_HANDLER_EXECUTOR.execute(() -> {
            log.info("handle BatchMoveTestcaseModelEvent");
            String eventClassName = BatchMoveTestcaseModelEvent.class.getName();
            logManager.createLogs(filteredCodes.stream().map(c -> new ActionLogDO(c, "移动了用例", "移动至 >>> " + parentName,
                    eventClassName, operator.getUserName(), operator.getUserId())).collect(Collectors.toList()));
        });
    }

    /**
     * 批量复制用例
     *
     * @param caseCodes
     * @param parentCode
     * @param path
     * @param parentName
     * @param existNames
     * @param operator
     * @param setCore
     * @param productCode
     * @param versionCode
     */
    private void doBatchCopyCase(List<String> caseCodes, String parentCode, String path,
                                 String parentName, Set<String> existNames, User operator, Boolean setCore,
                                 String productCode, String versionCode) {
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        // 有重复名称的用例剔除掉
        List<String> filteredCodes = testcaseRepository.filterCodeByName(caseCodes, existNames, COPY_FLAG, setCore, productCode, versionCode);
        if (CollectionUtil.isEmpty(filteredCodes)) {
            return;
        }
        // 获取所有的用例
        List<TestcaseEntityDO> allOldCase = testcaseRepository.selectTestcaseByCodesList(filteredCodes);
        if (CollectionUtil.isEmpty(allOldCase)) {
            return;
        }
        // 新旧编码 map
        Map<String, String> codeMap = generateNewCode(filteredCodes);
        List<TestcaseEntityDO> newCases = allOldCase.stream()
                .map(tc -> buildCopyTestCase(tc, codeMap.get(tc.getCode()), parentCode, path, operator))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(newCases)) {
            return;
        }
        testcaseRepository.batchInsertCase(newCases);

        EVENT_HANDLER_EXECUTOR.execute(() -> {
            log.info("handle BatchCopyTestcaseEvent");
            // 测试步骤
            doCopyTestStep(filteredCodes, codeMap);
            // 日志
            String eventClassName = BatchCopyTestcaseEvent.class.getName();
            logManager.createLogs(filteredCodes.stream().map(c -> new ActionLogDO(codeMap.get(c), "复制了用例", "",
                    eventClassName, operator.getUserName(), operator.getUserId())).collect(Collectors.toList()));
            // 附件
            doCopyCaseAttachment(filteredCodes, codeMap);
            // tag
            doCopyTag(filteredCodes, codeMap);
        });
    }

    /**
     * 生成新编码
     *
     * @param filterCode
     * @return 旧 -> 新
     */
    private Map<String, String> generateNewCode(List<String> filterCode) {
        HashMap<String, String> codeMap = new HashMap<>();
        filterCode.forEach(c -> codeMap.put(c, aggregateIdGenerateService.generateId(AggregateType.TEST_CASE)));
        return codeMap;
    }

    /**
     * 构建entity
     *
     * @param oldEntity
     * @param newCode
     * @param parentCode
     * @param path
     * @param operator
     * @return
     */
    private TestcaseEntityDO buildCopyTestCase(TestcaseEntityDO oldEntity, String newCode, String
            parentCode, String
                                                       path, User operator) {
        TestcaseEntityDO entity = new TestcaseEntityDO();
        entity.setCode(newCode);
        entity.setCreator(operator.getUserName());
        entity.setCreatorId(operator.getUserId());
        entity.setProductCode(oldEntity.getProductCode());
        entity.setParentCode(parentCode);
        entity.setAttribute(TestcaseAttributeEnum.TESTCASE);
        entity.setName("【副本】" + (null != oldEntity.getName() ? oldEntity.getName().substring(0, Math.min(oldEntity.getName().length(), 146)) : ""));
        entity.setType(oldEntity.getType());
        entity.setPriority(oldEntity.getPriority());
        entity.setStatus(TestcaseStatusEnum.NORMAL);
        entity.setPrecondition(oldEntity.getPrecondition());
        entity.setDutyUserId(oldEntity.getDutyUserId() != null ? oldEntity.getDutyUserId() : 0L);
        entity.setDutyUser(StringUtil.isNotEmpty(oldEntity.getDutyUser()) ? oldEntity.getDutyUser() : "");
        entity.setComment(StringUtil.isNotEmpty(oldEntity.getComment()) ? oldEntity.getComment() : "");
        entity.setAbandonReason(oldEntity.getAbandonReason());
        entity.setNodeType(oldEntity.getNodeType() == null ? AutomaticNodeTypeEnum.NULL : oldEntity.getNodeType());
        entity.setPath(path);
        entity.setTestcaseModulePath("");
        entity.setAutomaticSourceCode("");
        entity.setInterfaceName("");
        entity.setVersionCode(oldEntity.getVersionCode());
        entity.setSetCore(oldEntity.getSetCore());
        //TODO:: sort 字段
        return entity;
    }

    /**
     * 测试步骤
     *
     * @param caseCodes
     * @param codeMap
     */
    private void doCopyTestStep(List<String> caseCodes, Map<String, String> codeMap) {
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        List<TestcaseStepEntityDO> steps = testcaseStepRepository.selectTestcaseStepByCaseCodeList(caseCodes);
        if (CollectionUtil.isEmpty(steps)) {
            return;
        }
        List<List<TestcaseStepEntityDO>> parts = Lists.partition(steps, 200);
        parts.forEach(s -> {
            s.forEach(step -> step.setTestcaseCode(codeMap.get(step.getTestcaseCode())));
            testcaseStepRepository.saveBatch(s);
        });
    }

    /**
     * 附件
     *
     * @param caseCodes
     * @param codeMap
     */
    private void doCopyCaseAttachment(List<String> caseCodes, Map<String, String> codeMap) {
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }

        List<AttachmentEntityDO> attaches = testcaseAttachmentRepository.selectTestcaseAttachmentByCaseCodeList(caseCodes);
        if (CollectionUtil.isEmpty(attaches)) {
            return;
        }
        attaches.forEach(att -> {
            att.setBusinessCode(codeMap.get(att.getBusinessCode()));
            att.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
            testcaseAttachmentRepository.insertSelective(att);
        });
    }

    /**
     * tag
     *
     * @param caseCode
     * @param codeMap
     */
    private void doCopyTag(List<String> caseCode, Map<String, String> codeMap) {
        if (CollectionUtil.isEmpty(caseCode)) {
            return;
        }
        List<TagEntityDO> tags = testcaseTagRepository.selectTestcaseTagByCaseCodeList(caseCode);
        if (CollectionUtil.isEmpty(tags)) {
            return;
        }
        tags.forEach(t -> {
            t.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
            t.setBusinessCode(codeMap.get(t.getBusinessCode()));
            testcaseTagRepository.insertSelective(t);
        });
    }

    /**
     * 批量更新责任人
     *
     * @param codes
     * @param dutyUser
     * @param operator
     */
    private void doBatchUpdateDutyUser(List<String> codes, User dutyUser, User operator) {
        if (CollectionUtil.isEmpty(codes) || dutyUser == null) {
            return;
        }
        testcaseRepository.batchUpdateDutyUser(codes, dutyUser, operator);
        EVENT_HANDLER_EXECUTOR.execute(() -> {
            log.info("handle BatchModifyCaseDutyUserEvent");
            String eventClassName = BatchModifyCaseDutyUserEvent.class.getName();
            logManager.createLogs(codes.stream().map(c -> new ActionLogDO(c, "变更责任人", "修改为 >>> " + dutyUser.getUserName(),
                    eventClassName, operator.getUserName(), operator.getUserId())).collect(Collectors.toList()));
        });
    }

    /**
     * 批量修改用例等级
     *
     * @param codes
     * @param grade
     * @param operator
     */
    private void doBatchUpdateCaseGrade(List<String> codes, TestcasePriorityEnum grade, User operator) {
        if (CollectionUtil.isEmpty(codes) || grade == null) {
            return;
        }
        testcaseRepository.doBatchUpdateCaseGrade(codes, grade, operator);
        EVENT_HANDLER_EXECUTOR.execute(() -> {
            log.info("handle BatchModifyCaseGradeEvent");
            String eventClassName = BatchModifyCaseGradeEvent.class.getName();
            logManager.createLogs(codes.stream().map(c -> new ActionLogDO(c, "变更等级", "修改为 >>> " + grade.getValue(),
                    eventClassName, operator.getUserName(), operator.getUserId())).collect(Collectors.toList()));
        });
    }

    /**
     * 校验用例是否重名
     *
     * @param caseCodes
     */
    public void checkSameName(List<String> caseCodes) {
        List<String> caseNameList = testcaseRepository.selectDistinctCaseNameByCodeList(caseCodes, Boolean.FALSE);
        if (CollectionUtil.isEmpty(caseNameList)) {
            throw new ServiceException("用例不存在！");
        }
        if (caseNameList.size() != caseCodes.size()) {
            throw new ServiceException("选择的用例名称重复！");
        }
    }

    /**
     * 设置核心用例
     *
     * @param caseCodes
     * @param parentCode
     * @param path
     * @param originalList
     * @param operator
     */
    private void doBatchSetCore(List<String> caseCodes,
                                String parentCode,
                                String path,
                                List<TestcaseEntityDO> originalList,
                                User operator) {
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        // 获取所有待复制用例
        List<TestcaseEntityDO> sourceCaseList = testcaseRepository.selectTestcaseByCodesList(caseCodes);
        if (CollectionUtil.isEmpty(sourceCaseList)) {
            return;
        }
        //目标分组用例名统计
        Map<String, List<TestcaseEntityDO>> originalMap = null;
        if (CollectionUtil.isNotEmpty(originalList)) {
            originalMap = originalList.stream().map(orginal -> {
                orginal.setName(orginal.getName().replaceAll(SET_CORE_REGEX, "").replaceAll(COPY_REGEX, ""));
                return orginal;
            }).collect(Collectors.groupingBy(TestcaseEntityDO::getName));
        }
        // 新旧编码 map
        Map<String, String> codeMap = generateNewCode(caseCodes);
        List<TestcaseEntityDO> newCases = buildNewCaseList(codeMap, sourceCaseList, originalMap, parentCode, path, operator);
        if (CollectionUtil.isEmpty(newCases)) {
            return;
        }
        testcaseRepository.batchInsertCase(newCases);

        EVENT_HANDLER_EXECUTOR.execute(() -> {
            //测试步骤
            doCopyTestStep(caseCodes, codeMap);
            // 日志
            String eventClassName = BatchSetCoreEvent.class.getName();
            logManager.createLogs(caseCodes.stream().map(c -> new ActionLogDO(codeMap.get(c), "设置了核心用例", "",
                    eventClassName, operator.getUserName(), operator.getUserId())).collect(Collectors.toList()));
            // 附件
            doCopyCaseAttachment(caseCodes, codeMap);
            // tag
            doCopyTag(caseCodes, codeMap);
        });
    }

    /**
     * 组装新用例
     *
     * @param sourceCaseList 源用例列表
     * @param originalMap    目标分组用例名map
     * @param parentCode     目标分组code
     * @param path           目标分组path
     * @param operator       操作人
     * @return {@link TestcaseEntityDO}
     */
    private List<TestcaseEntityDO> buildNewCaseList(Map<String, String> codeMap,
                                                    List<TestcaseEntityDO> sourceCaseList,
                                                    Map<String, List<TestcaseEntityDO>> originalMap,
                                                    String parentCode,
                                                    String path,
                                                    User operator) {
        List<TestcaseEntityDO> newCaseList = new ArrayList<>(sourceCaseList.size());
        //用例名
        for (TestcaseEntityDO sourceCase : sourceCaseList) {
            String cleanName = sourceCase.getName().replaceAll(SET_CORE_REGEX, "").replaceAll(COPY_REGEX, "");
            AtomicReference<String> newName = new AtomicReference<>(sourceCase.getName());
            //重命名
            if (MapUtils.isNotEmpty(originalMap)) {
                originalMap.entrySet().forEach(entry -> {
                    if (cleanName.equals(entry.getKey())) {
                        int step = (entry.getValue().size());
                        newName.set(cleanName + "【副本" + (step > 1 ? step : "") + "】");
                    }
                });
            }
            //复制其他参数
            newCaseList.add(buildSetCore(sourceCase, codeMap.get(sourceCase.getCode()), newName.get(), parentCode, path, operator));
        }
        return newCaseList;
    }

    /**
     * 组装核心用例entity
     *
     * @param oldEntity
     * @param newName
     * @param parentCode
     * @param path
     * @param operator
     * @return
     */
    private TestcaseEntityDO buildSetCore(TestcaseEntityDO oldEntity, String code, String newName, String
            parentCode, String path, User operator) {
        TestcaseEntityDO entity = new TestcaseEntityDO();
        BeanUtils.copyProperties(oldEntity, entity);
        entity.setId(null);
        entity.setCode(code);
        entity.setParentCode(parentCode);
        entity.setPath(path);
        entity.setName(StringUtil.isNotBlank(newName) ? newName.substring(0, Math.min(newName.length(), 56)) : "");
        entity.setSetCore(Boolean.TRUE);
        entity.setCreator(operator.getUserName());
        entity.setCreatorId(operator.getUserId());
        entity.setGmtCreate(new Date());
        return entity;
    }

    public void xmindCaseAddCommand(XmindCaseAddCommand command) {
        XmindCaseAddEvent event = testcaseDomainConverter.convert(command);
        testcaseRepository.addTestCaseSteps(event);
        apply(event);
    }

    public void moveTestcase(MoveTestcaseCommand command) {
        log.info("MoveTestcaseCommand >>> {}", command.getAggregateId());
        TestcaseEntityDO entityDO = testcaseRepository.loadFormDb(command.getAggregateId());
        if (StringUtils.defaultString(entityDO.getParentCode()).equals(command.getParentCode())) {
            return;
        }
        TestcaseMovedEvent event = testcaseDomainConverter.converter(command);
        event.setAggregateId(command.getAggregateId());
        event.setCode(command.getAggregateId());
        event.setName(entityDO.getName());
        event.setProductCode(entityDO.getProductCode());
        event.setVersionCode(entityDO.getVersionCode());
        String path = StringUtils.EMPTY;

        // parentCode转化
        String parentCode = event.getParentCode();
        if (parentCode.equals(TestcaseGroupTypeEnum.ALL.name()) || parentCode.equals(TestcaseGroupTypeEnum.NO_GROUP.name())) {
            parentCode = StringUtils.EMPTY;
        }
        if (StringUtils.isNotBlank(parentCode)) {
            TestcaseEntityDO moduleDO = testcaseRepository.selectModule(parentCode, event.getVersionCode(), event.getSetCore());
            if (null == moduleDO) {
                throw new ServiceException("不可移动到该目录下！");
            }
            path = (StringUtils.isBlank(moduleDO.getPath()) ? StringUtils.EMPTY : moduleDO.getPath() + ".") + event.getParentCode();
        }

        //校验是否重名
        int count = testcaseRepository.countCaseByCaseName(event.getProductCode(),
                event.getVersionCode(),
                event.getName(),
                parentCode,
                TestcaseTypeEnum.MANUAL,
                event.getSetCore());
        if (count > 0) {
            throw new ServiceException("该分组下用例名称重复！");
        }

        //更新用例
        TestcaseEntityDO toUpdateDO = new TestcaseEntityDO();
        toUpdateDO.setCode(event.getCode());
        toUpdateDO.setParentCode(parentCode);
        toUpdateDO.setPath(path);
        toUpdateDO.preUpdate(event);
        testcaseRepository.updatePath(toUpdateDO);
        apply(event);
    }

    public void deleteTestcaseCommand(DeleteTestcaseCommand command) throws FluentException {
        this.deleteTestcaseCommand(null, command, null, null, null);
    }

    @Async
    public void batchXmindDelete(BatchXmindDeleteReq req, User currentUser) {
        List<XmindDeleteVO> deleteList = req.getCodeList();
        if (CollectionUtils.isEmpty(deleteList)) {
            return;
        }
        log.info("开始批量删除分组和用例！");
        for (XmindDeleteVO deleteVO : deleteList) {
            //校验
            FindTestcaseEntityByCodeQuery query = new FindTestcaseEntityByCodeQuery();
            query.setCode(deleteVO.getCode());
            TestcaseVO dto = testcaseQueryDomainService.findTestcaseEntityByCodeQuery(query);
            if (null == dto) {
                continue;
            }
            if (!dto.getAttribute().equals(deleteVO.getAttribute())) {
                log.error("xmind删除操作，数据类型不匹配！XmindDeleteVO: {}", JSON.toJSONString(deleteVO));
                continue;
            }
            DeleteTestcaseCommand command = new DeleteTestcaseCommand(deleteVO.getCode());
            command.setAttribute(deleteVO.getAttribute());
            command.setTransactor(currentUser);
            this.deleteTestcaseCommand(command);
        }
    }

    public void deleteTestcaseCommand(String automaticSourceCode, DeleteTestcaseCommand command, List<String> planCodes, List<String> codeUpdateList, User operator) throws FluentException {
        log.info("DeleteTestcaseCommand>>>>>>>>>>>>>>>>aggregateId = :{}", command.getAggregateId());
        TestcaseEntityDO testcaseEntityDO = testcaseRepository.loadFormDb(command.getAggregateId());
        DeleteTestcaseEvent event = new DeleteTestcaseEvent();
        event.setAggregateId(command.getAggregateId());
        event.setCode(command.getAggregateId());
        event.setAttribute(command.getAttribute());
        event.setOccurred(command.getOccurred());
        event.setTransactor(command.getTransactor());
        event.setType(testcaseEntityDO.getType());
        this.deleteTestcase(automaticSourceCode, event, planCodes, codeUpdateList, operator);
        apply(event);
    }

    public void deleteTestcase(String automaticSourceCode, DeleteTestcaseEvent event, List<String> planCodes, List<String> codeUpdateList, User operator) {
        log.info("---------------进入DeleteTestcaseEvent-----" + JsonUtil.toJSON(event));
        List<String> codeList = new ArrayList<>();
        //用例删除
        if (event.getAttribute().name().equals(TestcaseAttributeEnum.TESTCASE.name())) {
            log.info("---------------进入用例删除-----");
            TestcaseEntityDO entity = testcaseDomainConverter.convertorDeleteToEntity(event);
            if (StringUtil.isNotEmpty(entity.getCode())) {
                entity.setEnable(false);
                testcaseRepository.updateByPrimaryKeySelective(entity);
                codeList.add(entity.getCode());
            }
        } else {
            log.info("---------------进入模块删除-----");
            //模块删除
            TestcaseEntityDO module = testcaseRepository.loadFormDb(event.getCode());
            if (null == module) {
                return;
            }
            List<TestcaseEntityDO> children = testcaseRepository.queryChildCaseAndModuleByPath(module.getCode(), module.getPath());
            if (TestcaseTypeEnum.AUTO.equals(event.getType())) {
                children.addAll(testcaseRepository.queryChildCaseAndModuleByTestcaseModulePath(module.getCode(), module.getPath()));
            }
            children.add(module);
            List<String> caseCodeList = children.stream().map(TestcaseEntityDO::getCode).collect(Collectors.toList());
            codeList.addAll(caseCodeList);

            TestcaseEntityDO testcaseEntity = new TestcaseEntityDO();
            testcaseEntity.setEnable(false);
            testcaseEntity.setModifier(event.getTransactor().getUserName());
            testcaseEntity.setModifierId(event.getTransactor().getUserId());
            testcaseEntity.setGmtModified(event.getOccurred());

            List<List<String>> partition = Lists.partition(caseCodeList, 500);
            partition.forEach(t -> testcaseRepository.updateByCodeList(testcaseEntity, t));

            if (TestcaseTypeEnum.AUTO.equals(event.getType())) {
                batchDeleteAutomaticSourceRecord(caseCodeList, event);
            }
        }
        if (CollectionUtil.isNotEmpty(codeList)) {
//            EVENT_HANDLER_EXECUTOR.execute(() -> {});
//            异步会引起用例重复问题
            batchDeleteTestPlan(codeList, event, planCodes);
            /**
             * 如果登记库是测试工厂场类型的，需要增加新用例进入测试计划，因为测试工厂一但发布或者重新生成用例脚本，一定会发生变化，
             * 所以一定会走到这里的删除逻辑，所以在这里收集planCodes的信息，对这些plan进行新增用例到计划中的操作
             */
            batchAddSceneTestCaseToPlan(automaticSourceCode, planCodes, codeUpdateList, operator);
        }
    }

    private void deleteTestPlan(String caseCode) {
        //根据测试用例code查询关联计划
        List<TmTestPlanCaseEntityDO> testPlanCaseEntityList = iTmTestPlanCaseRepository.getTestPlanCaseByCode(caseCode);
        if (CollectionUtil.isEmpty(testPlanCaseEntityList)) {
            return;
        }
        for (TmTestPlanCaseEntityDO en : testPlanCaseEntityList) {
            //查询测试计划  进行中 or未开始的计划 删除关联关系
            TmTestPlanEntityDO tmTestPlanEntity = iTmTestPlanRepository.getTestPlanByCode(en.getPlanCode());
            if (tmTestPlanEntity.getStatus().name().equals(TestPlanNewStatusEnum.COMPLETED.name())) {
                en.setEnable(false);
                iTmTestPlanCaseRepository.updateByPrimaryKeySelective(en);

            } else {
                if (Objects.nonNull(tmTestPlanEntity.getStageStatus()) && !tmTestPlanEntity.getStageStatus().isEmpty() && !en.getTestStage().name().equals("NULL_TEST")) {
                    if ((TestPlanStageStatusEnum.INITIAL.name().equals(tmTestPlanEntity.getStageStatus().get("SMOKE_TEST"))
                            || TestPlanStageStatusEnum.IN_PROGRESS.name().equals(tmTestPlanEntity.getStageStatus().get("SMOKE_TEST"))) && en.getTestStage().name().equals("SMOKE_TEST")) {
                        iTmTestPlanCaseRepository.delete(en);

                    }
                    if (TestPlanStageStatusEnum.COMPLETED.name().equals(tmTestPlanEntity.getStageStatus().get("SMOKE_TEST")) && en.getTestStage().name().equals("SMOKE_TEST")) {
                        en.setEnable(false);
                        iTmTestPlanCaseRepository.updateByPrimaryKeySelective(en);
                    }
                    if ((TestPlanStageStatusEnum.INITIAL.name().equals(tmTestPlanEntity.getStageStatus().get("FUNCTIONAL_TEST"))
                            || TestPlanStageStatusEnum.IN_PROGRESS.name().equals(tmTestPlanEntity.getStageStatus().get("FUNCTIONAL_TEST"))) && en.getTestStage().name().equals("FUNCTIONAL_TEST")) {
                        iTmTestPlanCaseRepository.delete(en);
                    }
                    if (TestPlanStageStatusEnum.COMPLETED.name().equals(tmTestPlanEntity.getStageStatus().get("FUNCTIONAL_TEST")) && en.getTestStage().name().equals("FUNCTIONAL_TEST")) {
                        en.setEnable(false);
                        iTmTestPlanCaseRepository.updateByPrimaryKeySelective(en);
                    }
                    if ((TestPlanStageStatusEnum.INITIAL.name().equals(tmTestPlanEntity.getStageStatus().get("ONLINE_SMOKE_TEST"))
                            || TestPlanStageStatusEnum.IN_PROGRESS.name().equals(tmTestPlanEntity.getStageStatus().get("ONLINE_SMOKE_TEST"))) && en.getTestStage().name().equals("ONLINE_SMOKE_TEST")) {
                        iTmTestPlanCaseRepository.delete(en);
                    }
                    if (TestPlanStageStatusEnum.COMPLETED.name().equals(tmTestPlanEntity.getStageStatus().get("ONLINE_SMOKE_TEST")) && en.getTestStage().name().equals("ONLINE_SMOKE_TEST")) {
                        en.setEnable(false);
                        iTmTestPlanCaseRepository.updateByPrimaryKeySelective(en);
                    }
                } else {
                    iTmTestPlanCaseRepository.delete(en);
                }
            }
        }
    }

    public void batchAddSceneTestCaseToPlan(String automaticSourceCode, List<String> planCodes, List<String> codeUpdateList, User operator) {
        if (automaticSourceCode == null) {
            return;
        }
        /**
         * 如果登记库是测试工厂场景类型的，需要增加新用例进入测试计划*
         */
        List<TestcaseVO> testCases = testcaseRepository.ListCaseByAutomaticCode(automaticSourceCode);
        if (testCases != null) {
            List<String> codeAddList = new ArrayList<>();
            for (TestcaseVO testcaseVO : testCases) {
                if (TestcaseTypeEnum.AUTO != testcaseVO.getType()) {
                    continue;
                }
                if (TestcaseAttributeEnum.TESTCASE != testcaseVO.getAttribute()) {
                    continue;
                }
                if (TestcaseStatusEnum.DISABLE == testcaseVO.getStatus()) {
                    continue;
                }
                if (codeUpdateList != null) {
                    if (codeUpdateList.contains(testcaseVO.getCode())) {
                        continue;
                    }
                }
                codeAddList.add(testcaseVO.getCode());
            }
            for (String planInfo : planCodes) {
                String[] planInfoSplit = planInfo.split(":");
                if (planInfoSplit.length == 2) {
                    tmTestPlanCommandDomainService.batchJoinPlan(codeAddList, planInfoSplit[0].trim(), TestPlanStageEnum.getEnumByName(planInfoSplit[1].trim()), operator);
                }
            }
        }
    }

    public void batchDeleteTestPlan(List<String> codeList, DeleteTestcaseEvent event, List<String> planCodes) {

        List<DeletePlanCaseVO> deletePlanCaseVOS = iTmTestPlanCaseRepository.selectPlanCaseByCodeList(codeList);
        if (CollectionUtil.isEmpty(deletePlanCaseVOS)) {
            return;
        }
        LinkedList<Long> logicDeleteList = new LinkedList<>();
        LinkedList<Long> physicsDeleteList = new LinkedList<>();

        TmTestPlanCaseEntityDO tmTestPlanCaseEntity = new TmTestPlanCaseEntityDO();
        tmTestPlanCaseEntity.setEnable(false);
        tmTestPlanCaseEntity.setModifier(event.getTransactor().getUserName());
        tmTestPlanCaseEntity.setModifierId(event.getTransactor().getUserId());
        tmTestPlanCaseEntity.setGmtModified(event.getOccurred());

        for (DeletePlanCaseVO vo : deletePlanCaseVOS) {
            if (vo.getPlanStatus().equals(TestPlanNewStatusEnum.COMPLETED)) {
                logicDeleteList.add(vo.getId());
            } else {
                if (MapUtils.isNotEmpty(vo.getPlanStageStatus()) && vo.getPlanStageStatus().get(vo.getTestStage().name()).equals(TestPlanStageStatusEnum.COMPLETED.name())) {
                    logicDeleteList.add(vo.getId());
                } else {
                    physicsDeleteList.add(vo.getId());
                    if (!vo.getPlanStatus().equals(TestPlanNewStatusEnum.TERMINATED)) {
                        if (planCodes != null) {
                            if (!planCodes.contains(vo.getPlanCode() + ":" + vo.getTestStage().name())) {
                                planCodes.add(vo.getPlanCode() + ":" + vo.getTestStage().name());
                            }
                        }
                    }
                }
            }
        }

        if (CollectionUtil.isNotEmpty(logicDeleteList)) {
            List<List<Long>> partition = Lists.partition(logicDeleteList, 1000);
            partition.forEach(t -> {
                iTmTestPlanCaseRepository.updateByIdList(tmTestPlanCaseEntity, t);
            });
        }
        if (CollectionUtil.isNotEmpty(physicsDeleteList)) {
            List<List<Long>> partition = Lists.partition(physicsDeleteList, 1000);
            partition.forEach(t -> iTmTestPlanCaseRepository.deleteByIdList(t));
        }
    }

    private void batchDeleteAutomaticSourceRecord(List<String> testcaseEntities, DeleteTestcaseEvent event) {

        AutomaticSourceRecordEntityDO automaticSourceRecordEntity = new AutomaticSourceRecordEntityDO();
        automaticSourceRecordEntity.setEnable(false);
        automaticSourceRecordEntity.setModifier(event.getTransactor().getUserName());
        automaticSourceRecordEntity.setModifierId(event.getTransactor().getUserId());
        automaticSourceRecordEntity.setGmtModified(event.getOccurred());

        List<String> list = automaticSourceRecordRepository.selectByTestcaseCodeList(testcaseEntities);

        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        automaticSourceRecordRepository.updateByCodeList(automaticSourceRecordEntity, list);
    }

    public void copyOrCutModuleAndTestcase(XmindCaseEditCommand command) {
        if (command.getOperation().equals(XmindOperationEnum.COPY)) {
            if (command.getAttribute().equals(TestcaseAttributeEnum.TESTCASE)) {
                batchCopyCase(Arrays.asList(command.getId()), command.getParentCode(), command.getSetCore(), command.getTransactor());
            } else if (command.getAttribute().equals(TestcaseAttributeEnum.MODULE)) {
                checkCopyModule(command);
                TestcaseQuery query = new TestcaseQuery();
                query.setCode(command.getId());
                query.setProductCode(command.getProductCode());
                query.setVersionCode(command.getVersionCode());
                query.setSetCore(command.getSetCore());
                query.setType(TestcaseTypeEnum.MANUAL);
                query.setGroupType(TestcaseGroupTypeEnum.GROUP);
                List<ListTestcaseVO> testcaseVOList = getAll(query);
                if (CollectionUtil.isNotEmpty(testcaseVOList)) {
                    copyModule(testcaseVOList, command.getParentCode(), command.getSetCore(), command.getNewPath(), command.getTransactor());
                }
            }
        } else if (command.getOperation().equals(XmindOperationEnum.CUT)) {
            if (command.getAttribute().equals(TestcaseAttributeEnum.TESTCASE)) {
                TestcaseMovedEvent testcaseMovedEvent = new TestcaseMovedEvent();
                testcaseMovedEvent.setCode(command.getId());
                testcaseMovedEvent.setName(command.getTopic());
                testcaseMovedEvent.setProductCode(command.getProductCode());
                testcaseMovedEvent.setParentCode(command.getParentCode());
                this.moveTestcase(testcaseMovedEvent);
            } else if (command.getAttribute().equals(TestcaseAttributeEnum.MODULE)) {
                checkMoveModule(command);
                MoveModuleEvent moveModuleEvent = testcaseDomainConverter.converter(command);
                moveModuleEvent.setType(TestcaseTypeEnum.MANUAL);
                this.moveModule(moveModuleEvent);
            }
        }
    }

    private void checkCopyModule(XmindCaseEditCommand command) {
        if (command.getParentCode().equals(TestcaseGroupTypeEnum.NO_GROUP.name())) {
            throw new ServiceException("不能复制到未分组用例下");
        }
        String name = String.join("", "【副本】", command.getTopic());
        int count = testcaseRepository.selectModuleCountByName(command, name);
        if (count > 0) {
            throw new ServiceException(name + "-该分组名已存在");
        }
        if (null == command.getNewPath() && !command.getParentCode().equals(TestcaseGroupTypeEnum.ALL.name())) {
            TestcaseEntityDO testcaseEntityDO = testcaseRepository.selectTestcaseByCode(command.getParentCode());
            if (StringUtil.isNotEmpty(testcaseEntityDO.getPath())) {
                command.setNewPath(testcaseEntityDO.getPath());
            }
        }
        // 如果是第一层，newPath = null,如果是第二层，newPath = ""
        if (null == command.getNewPath() || command.getParentCode().equals(TestcaseGroupTypeEnum.ALL.name())) {
            return;
        }
        FindTestcaseModulePathQuery query = new FindTestcaseModulePathQuery();
        query.setCode(command.getId());
        query.setType(TestcaseTypeEnum.MANUAL.name());
        query.setAttribute(command.getAttribute().name());
        query.setOldPath(StringUtil.isEmpty(command.getOldPath()) ? command.getId() :
                command.getOldPath() + "." + command.getId());
        query.setNewPath(StringUtil.isEmpty(command.getNewPath()) ? command.getParentCode() :
                command.getNewPath() + "." + command.getParentCode());
        int maxLayer = testcaseQueryDomainService.findTestcaseModulePathQuery(query);
        if (maxLayer > 10) {
            throw new ServiceException("层级结构不能超过十层");
        }
    }

    private List<ListTestcaseVO> getAll(TestcaseQuery query) {
        if (TestcaseGroupTypeEnum.ALL.equals(query.getGroupType())
                || TestcaseGroupTypeEnum.NO_GROUP.equals(query.getGroupType())) {
            return new ArrayList<>();
        }
        List<ListTestcaseVO> modules = new LinkedList<>();
        query.setTestcaseAttribute(TestcaseAttributeEnum.MODULE);
        query.setAutomaticSourceCode(StringUtils.defaultString(query.getAutomaticSourceCode()));
        modules.addAll(testcaseRepository.selectTestCaseModule(query));
        if (CollectionUtil.isNotEmpty(modules)) {
            modules.stream().forEach(vo -> getChildren(vo, query));
        }
        return modules;
    }

    private void getChildren(ListTestcaseVO vo, TestcaseQuery query) {
        query.setParentCode(vo.getCode());
        PageTestcaseQuery pageTestcaseQuery = new PageTestcaseQuery();
        BeanUtils.copyProperties(query, pageTestcaseQuery);
        List<ListTestcaseVO> testcaseList = testcaseRepository.pageTestcase(pageTestcaseQuery);
        if (CollectionUtil.isNotEmpty(testcaseList)) {
            vo.setChildren(testcaseList);
        }
        List<ListTestcaseVO> childModules = testcaseRepository.selectTestCaseModuleList(pageTestcaseQuery);
        if (CollectionUtil.isNotEmpty(childModules)) {
            testcaseList.addAll(childModules);
            vo.setChildren(testcaseList);
            childModules.stream().forEach(childVo -> getChildren(childVo, query));
        }
    }

    /**
     * 递归复制分组&用例
     *
     * @param list
     * @param parentCode
     * @param setCore
     * @param operator
     */
    private void copyModule(List<ListTestcaseVO> list, String parentCode, Boolean setCore, String path, User
            operator) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        if (parentCode.equals(TestcaseGroupTypeEnum.ALL.name())) {
            parentCode = StringUtils.EMPTY;
        }
        String newPath = (StringUtils.isBlank(path) ? StringUtils.EMPTY : path + ".") + parentCode;
        List<ListTestcaseVO> moduleVOs = list.stream().filter(testcaseVO -> (testcaseVO.getAttribute().equals(TestcaseAttributeEnum.MODULE)))
                .collect(Collectors.toList());
        List<TestcaseEntityDO> oldModules = Lists.newLinkedList();
        copyList(moduleVOs, oldModules, TestcaseEntityDO.class);
        List<String> modules = list.stream().filter(testcaseVO -> (testcaseVO.getAttribute().equals(TestcaseAttributeEnum.MODULE)))
                .map(ListTestcaseVO::getCode)
                .collect(Collectors.toList());
        List<String> caseCodes = list.stream().filter(testcaseVO -> (testcaseVO.getAttribute().equals(TestcaseAttributeEnum.TESTCASE)))
                .map(ListTestcaseVO::getCode)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(caseCodes)) {
            batchCopyCase(caseCodes, parentCode, setCore, operator);
        }
        if (CollectionUtil.isEmpty(modules)) {
            return;
        }
        Map<String, String> codeMap = generateNewCode(modules);
        String finalParentCode = parentCode;
        List<TestcaseEntityDO> newModules = oldModules.stream()
                .map(tc -> buildCopyModule(tc, codeMap.get(tc.getCode()), finalParentCode, newPath, operator))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(newModules)) {
            return;
        }
        testcaseRepository.batchInsertCase(newModules);

        moduleVOs.stream().forEach(module -> {
            if (CollectionUtil.isNotEmpty(module.getChildren())) {
                copyModule(module.getChildren(), codeMap.get(module.getCode()), module.getSetCore(), newPath, operator);
            }
        });
    }

    public void copyList(Object obj, List<TestcaseEntityDO> list2, Class<TestcaseEntityDO> classObj) {
        if ((!Objects.isNull(obj)) && (!Objects.isNull(list2))) {
            List list1 = (List) obj;
            list1.forEach(item -> {
                try {
                    TestcaseEntityDO data = classObj.newInstance();
                    BeanUtils.copyProperties(item, data);
                    list2.add(data);
                } catch (InstantiationException e) {
                } catch (IllegalAccessException e) {
                }
            });
        }
    }

    private TestcaseEntityDO buildCopyModule(TestcaseEntityDO oldEntity, String newCode, String parentCode, String
            path, User operator) {
        TestcaseEntityDO entity = new TestcaseEntityDO();
        entity.setCode(newCode);
        entity.setCreator(operator.getUserName());
        entity.setCreatorId(operator.getUserId());
        entity.setProductCode(oldEntity.getProductCode());
        entity.setParentCode(parentCode);
        entity.setAttribute(TestcaseAttributeEnum.MODULE);
        entity.setName("【副本】" + (null != oldEntity.getName() ? oldEntity.getName().substring(0, Math.min(oldEntity.getName().length(), 56)) : ""));
        entity.setType(oldEntity.getType());
        entity.setPriority(oldEntity.getPriority());
        entity.setStatus(TestcaseStatusEnum.NORMAL);
        entity.setPrecondition(oldEntity.getPrecondition());
        entity.setDutyUserId(oldEntity.getDutyUserId() != null ? oldEntity.getDutyUserId() : 0L);
        entity.setDutyUser(StringUtil.isNotEmpty(oldEntity.getDutyUser()) ? oldEntity.getDutyUser() : "");
        entity.setComment(StringUtil.isNotEmpty(oldEntity.getComment()) ? oldEntity.getComment() : "");
        entity.setAbandonReason(oldEntity.getAbandonReason());
        entity.setNodeType(oldEntity.getNodeType() == null ? AutomaticNodeTypeEnum.NULL : oldEntity.getNodeType());
        entity.setPath(path);
        entity.setTestcaseModulePath("");
        entity.setAutomaticSourceCode("");
        entity.setInterfaceName("");
        entity.setVersionCode(oldEntity.getVersionCode());
        entity.setSetCore(oldEntity.getSetCore());
        return entity;
    }

    public void moveTestcase(TestcaseMovedEvent event) {
        String path = StringUtils.EMPTY;
        // parentCode转化
        String parentCode = event.getParentCode();
        if (parentCode.equals(TestcaseGroupTypeEnum.ALL.name()) || parentCode.equals(TestcaseGroupTypeEnum.NO_GROUP.name())) {
            parentCode = StringUtils.EMPTY;
        }
        if (StringUtil.isNotBlank(parentCode)) {
            TestcaseEntityDO entity = testcaseRepository.selectTestCaseEntity(event, parentCode);
            if (null == entity) {
                throw new ServiceException("不可移动到该目录下！");
            }
            path = (StringUtils.isBlank(entity.getPath()) ? StringUtils.EMPTY : entity.getPath() + ".") + event.getParentCode();
        }
        int count = testcaseRepository.selectTestCaseCount(event, parentCode);
        if (count > 0) {
            throw new ServiceException("该分组下用例名称重复！");
        }
        TestcaseEntityDO entity = new TestcaseEntityDO();
        entity.setCode(event.getCode());
        entity.setParentCode(parentCode);
        entity.setPath(path);
        entity.preUpdate(event);
        testcaseRepository.updateByPrimaryKeySelective(entity);
    }

    private void checkMoveModule(XmindCaseEditCommand command) {
        if (command.getParentCode().equals(TestcaseGroupTypeEnum.NO_GROUP.name())) {
            throw new ServiceException("不能剪切/移动到未分组用例下");
        }
        if (command.getOldParentCode().equals(command.getParentCode())) {
            throw new ServiceException("目标分组相同，无需剪切/移动");
        }
        // 如果是第一层，newPath = null,如果是第二层，newPath = ""
        if (null == command.getNewPath() || command.getParentCode().equals(TestcaseGroupTypeEnum.ALL.name())) {
            return;
        }
        if (command.getId().equals(command.getParentCode()) || command.getNewPath().contains(command.getId())) {
            throw new ServiceException("不能剪切/移动到子节点");
        }
        FindTestcaseModulePathQuery query = new FindTestcaseModulePathQuery();
        query.setCode(command.getId());
        query.setType(TestcaseTypeEnum.MANUAL.name());
        query.setAttribute(command.getAttribute().name());
        query.setOldPath(StringUtil.isEmpty(command.getOldPath()) ? command.getId() :
                command.getOldPath() + "." + command.getId());
        query.setNewPath(StringUtil.isEmpty(command.getNewPath()) ? command.getParentCode() :
                command.getNewPath() + "." + command.getParentCode());
        int maxLayer = testcaseRepository.findTestcaseModulePathQuery(query);
        if (maxLayer > 10) {
            throw new ServiceException("层级结构不能超过十层");
        }
    }

    public void dragAutomaticRecord(DragAutomaticRecordCommand command) {
        log.info("DragAutomaticRecordCommand >>> {}", command.getAggregateId());
        AutomaticSourceRecordEntityDO entityDO = automaticSourceRecordRepository.find(command.getAggregateId());
        AutomaticRecordDraggedEvent event = testcaseModuleDomainConverter.convertor(command);
        event.setAggregateId(command.getAggregateId());
        event.setProductCode(entityDO.getProductCode());
        event.setTestcaseCode(entityDO.getTestcaseCode());
        doDragAutomaticRecord(event);
        apply(event);
    }

    public void doDragAutomaticRecord(AutomaticRecordDraggedEvent event) {
        if (!StringUtils.defaultString(event.getTestcaseCode()).equals(event.getParentCode())) {
            String newPath = event.getCode();
            if (StringUtils.isNotEmpty(event.getParentCode())) {
                TestcaseEntityDO testcaseEntityDO = testcaseRepository.findModulePathByParentCode(event.getParentCode());
                if (null == testcaseEntityDO) {
                    throw new ServiceException("不可移动到该节点下！");
                }
                newPath = (StringUtils.isBlank(testcaseEntityDO.getPath()) ? StringUtils.EMPTY : testcaseEntityDO.getPath() + ".")
                        + event.getParentCode() + "." + event.getCode();
            }
            AutomaticSourceRecordEntityDO recordEntity = new AutomaticSourceRecordEntityDO();
            recordEntity.setCode(event.getCode());
            recordEntity.setTestcaseCode(event.getParentCode());
            automaticSourceRecordRepository.updateByPrimaryKeySelective(recordEntity);
            testcaseRepository.updateTestcaseModulePath(event.getCode(), newPath, event.getTransactor());
        }
        if (DragModuleActionEnum.INNER.equals(event.getAction())) {
            return;
        }
        List<String> sortedList = testcaseRepository.selectSortedCodeList(event.getProductCode(), event.getParentCode(), TestcaseTypeEnum.AUTO);
        testcaseRepository.deleteOldSorted(sortedList);
        sortedList.remove(event.getCode());
        int target = sortedList.indexOf(event.getTargetCode());
        if (DragModuleActionEnum.AFTER.equals(event.getAction())) {
            target += 1;
        }
        sortedList.add(target, event.getCode());
        testcaseRepository.insertNewSorted(sortedList);
    }

    public void dragTestcaseModuleCommand(DragTestcaseModuleCommand command) {
        log.info("DragTestcaseModuleCommand >>> {}", command.getAggregateId());
        AutomaticSourceRecordEntityDO entityDO = automaticSourceRecordRepository.find(command.getAggregateId());
        if (null != entityDO) {
            throw new ServiceException("登记库下分组不可移动！");
        }
        TestcaseEntityDO testcaseEntityDO = testcaseRepository.loadFormDb(command.getAggregateId());
        TestcaseModuleDraggedEvent event = testcaseDomainConverter.converter(command);
        event.setProductCode(testcaseEntityDO.getProductCode());
        event.setOldParentCode(testcaseEntityDO.getParentCode());
        event.setOldPath(testcaseEntityDO.getPath());
        event.setName(testcaseEntityDO.getName());
        doDragModule(event);
        apply(event);
    }

    public void doDragModule(TestcaseModuleDraggedEvent event) {
        if (!event.getOldParentCode().equals(event.getParentCode())) {
            String newPath = StringUtils.EMPTY;
            if (StringUtils.isNotEmpty(event.getParentCode())) {
                TestcaseEntityDO pathEntityDO = testcaseRepository.findModulePathByParentCode(event.getParentCode());
                if (null == pathEntityDO) {
                    throw new ServiceException("不可移动到该节点下！");
                }
                String oldPrefixPath = (StringUtils.isBlank(event.getOldPath()) ? StringUtils.EMPTY : event.getOldPath() + ".") + event.getCode();
                String maxLengthPath = testcaseRepository.selectMaxLengthPathByPath(oldPrefixPath);
                Integer oldLayer = StringUtils.defaultString(maxLengthPath).split("\\.").length + 1;
                Integer subLayer = pathEntityDO.getPath().split("\\.").length + 1 - event.getOldPath().split("\\.").length;
                if (oldLayer + subLayer > 10) {
                    throw new ServiceException("层级结构不能超过十层！");
                }
                newPath = pathEntityDO.getPath();
            }
            int count = testcaseRepository.countModuleByTypeAndNameAndParentCode(event.getProductCode(), event.getType(), event.getName(), event.getParentCode());
            if (count > 0) {
                throw new ServiceException("该目录下分组名称重复！");
            }
            MoveModuleEvent moveModuleEvent = new MoveModuleEvent();
            moveModuleEvent.setCode(event.getCode());
            moveModuleEvent.setParentCode(event.getParentCode());
            moveModuleEvent.setNewPath(newPath);
            moveModuleEvent.setOldPath(event.getOldPath());
            moveModuleEvent.setType(event.getType());
            moveModuleEvent.setTransactor(event.getTransactor());
            moveModule(moveModuleEvent);
        }
        if (DragModuleActionEnum.INNER.equals(event.getAction())) {
            return;
        }
        List<String> sortedList = testcaseRepository.selectSortedCodeList(event.getProductCode(), event.getParentCode(), event.getType());
        testcaseRepository.deleteOldSorted(sortedList);
        sortedList.remove(event.getCode());
        int target = sortedList.indexOf(event.getTargetCode());
        if (DragModuleActionEnum.AFTER.equals(event.getAction())) {
            target += 1;
        }
        sortedList.add(target, event.getCode());
        testcaseRepository.insertNewSorted(sortedList);
    }

    public void moveModule(MoveModuleEvent event) {
        String parentCode = StringUtils.defaultString(event.getParentCode());
        if (TestcaseGroupTypeEnum.ALL.name().equals(parentCode)) {
            parentCode = StringUtils.EMPTY;
        }
        if (event.getType().equals(TestcaseTypeEnum.SOURCERECORD)) {
            String newPath;
            if (StringUtil.isNotEmpty(parentCode)) {
                newPath = (StringUtils.isBlank(event.getNewPath()) ? StringUtils.EMPTY : event.getNewPath() + ".") + parentCode + "." + event.getCode();
            } else {
                newPath = event.getCode();
            }
            AutomaticSourceRecordEntityDO recordEntity = new AutomaticSourceRecordEntityDO();
            recordEntity.setCode(event.getCode());
            recordEntity.setTestcaseCode(parentCode);
            recordEntity.setModifier(event.getTransactor().getUserName());
            recordEntity.setModifierId(event.getTransactor().getUserId());
            automaticSourceRecordRepository.updateByPrimaryKeySelective(recordEntity);
            testcaseRepository.updateTestcaseModulePath(event.getCode(), newPath, event.getTransactor());
        } else {
            String newPath = (StringUtils.isBlank(event.getNewPath()) ? StringUtils.EMPTY : event.getNewPath() + ".") + parentCode;
            TestcaseEntityDO entity = new TestcaseEntityDO();
            entity.setCode(event.getCode());
            entity.setParentCode(parentCode);
            entity.setPath(newPath);
            entity.preUpdate(event);
            testcaseRepository.updateByPrimaryKeySelective(entity);
            String oldPrefixPath = (StringUtils.isBlank(event.getOldPath()) ? StringUtils.EMPTY : event.getOldPath() + ".") + event.getCode();
            String newPrefixPath = (StringUtils.isBlank(newPath) ? StringUtils.EMPTY : newPath + ".") + event.getCode();
            testcaseRepository.replacePath(oldPrefixPath, newPrefixPath);
            if (TestcaseTypeEnum.AUTO.equals(event.getType())) {
                testcaseRepository.replaceTestcaseModulePath(oldPrefixPath, newPrefixPath);
            }
        }
    }

    public void xmindCaseEditCommand(XmindCaseEditCommand command) {
        XmindCaseEditEvent event = testcaseDomainConverter.convert(command);
        if (command.getOperation().equals(XmindOperationEnum.UPDATE)) {
            this.updateXmind(event);
        } else if (command.getOperation().equals(XmindOperationEnum.DELETE)) {
            testcaseRepository.deleteXmindSteps(event);
        }
        event.setAggregateId(command.getAggregateId());
        apply(event);
    }

    public void updateXmind(XmindCaseEditEvent event) {
        // 前置条件
        if (event.getId().contains(TestCaseTagNameEnum.PRECONDITION.name())) {
            testcaseRepository.updateXmindDetail(event, TestCaseTagNameEnum.PRECONDITION);
            return;
        }
        // 备注
        if (event.getId().contains(TestCaseTagNameEnum.REMARK.name())) {
            testcaseRepository.updateXmindDetail(event, TestCaseTagNameEnum.REMARK);
            return;
        }
        // 步骤
        if (event.getId().contains(TestCaseTagNameEnum.STEP.name())) {
            testcaseRepository.updateXmindStepExcept(event, TestCaseTagNameEnum.STEP);
            return;
        }
        // 预期结果
        if (event.getId().contains(TestCaseTagNameEnum.EXPECT.name())) {
            testcaseRepository.updateXmindStepExcept(event, TestCaseTagNameEnum.EXPECT);
            return;
        }
        // 标题
        testcaseRepository.updateXmindDetail(event, TestCaseTagNameEnum.TOPIC);
    }

    public void addTestcaseCommand(AddTestcaseCommand command) {
        log.info("addTestcaseCommand >>> {}", JSON.toJSONString(command));
        AddTestcaseEvent event = testcaseDomainConverter.convert(command);
        testcaseRepository.addTestcase(event);
        tagRepository.addTagList(event.getTags(), event.getCode(), event);
        attachmentRepository.addAttachmentList(event.getAttachments(), event.getCode(), event);
        testcaseRelationRepository.addTestcaseRelationList(event.getVos(), event.getCode(), event);
        testcaseStepRepository.addTestcaseStepList(event.getTestSteps(), event.getCode(), event);
        apply(event);
    }

    public void editTestcaseCommand(EditTestcaseCommand command) {
        log.info("editTestcaseCommand >>> {}", JSON.toJSONString(command));
        EditTestcaseEvent event = testcaseDomainConverter.convert(command);
        TestcaseEntityDO entityDO = testcaseRepository.loadFormDb(event.getCode());
        EditTestcaseCommand source = testcaseDomainConverter.convert(entityDO);
        RevisionEvent.setChangeLog(source, command, EditTestcaseCommand.class, event);
        testcaseRepository.editTestcase(event);
        if (TestcaseTypeEnum.MANUAL.equals(event.getType())) {
            testcaseStepRepository.addTestcaseStepList(event.getTestSteps(), event.getCode(), event);
        }
        apply(event);
        EVENT_HANDLER_EXECUTOR.execute(() -> {
            log.info("handle EditTestcaseEvent");
            if (!Boolean.TRUE.equals(event.getIfUpdateStatus())) {
                return;
            }
            List<String> operateCaseCodeList =
                    iTmTestPlanCaseRepository.selectOperateCaseCodeByCaseCode(event.getCode());
            if (CollectionUtil.isEmpty(operateCaseCodeList)) {
                return;
            }
            List<ActionLogDO> logs = new ArrayList<>();
            for (String operateCaseCode : operateCaseCodeList) {
                String eventAction = TestcaseStatusEnum.DISABLE.equals(event.getStatus()) ? "停用了用例" : "启用了用例";
                ActionLogDO log = new ActionLogDO(
                        operateCaseCode,
                        eventAction,
                        "",
                        event.getClass().getName(),
                        event.getTransactor().getUserName(),
                        event.getTransactor().getUserId());
                logs.add(log);
            }
            logManager.createLogs(logs);
        });
    }

    public void changeTestcaseStatus(ChangeTestcaseStatusCommand command) {
        TestcaseStatusChangedEvent event = testcaseDomainConverter.converter(command);
        testcaseRepository.changeTestcaseStatus(event);
        EVENT_HANDLER_EXECUTOR.execute(() -> {
            this.logChangeTestcaseStatus(event);
        });
        apply(event);
    }

    public void changeVersion(ChangeVersionCommand command) {
        log.info("ChangeVersionCommand >>> {}", command.getAggregateId());
        TestcaseEntityDO testcase = testcaseRepository.loadFormDb(command.getAggregateId());

        if (StringUtils.defaultString(testcase.getParentCode()).equals(command.getTargetParentCode())) {
            return;
        }
        TestcaseChangeVersionEvent event = testcaseDomainConverter.converter(command);
        event.setName(testcase.getName());
        event.setProductCode(testcase.getProductCode());
        event.setSourceVersionCode(testcase.getVersionCode());
        event.setType(testcase.getType());
        event.setSetCore(Boolean.FALSE);
        TestcaseGroupVO vo = buildParentInfo(event);
        testcaseRepository.changeVersion(event, vo);
        apply(event);
    }

    public void logChangeTestcaseStatus(TestcaseStatusChangedEvent event) {
        List<String> operateCaseCodeList = iTmTestPlanCaseRepository.selectOperateCaseCodeByCaseCode(event.getCode());
        if (CollectionUtils.isEmpty(operateCaseCodeList)) {
            return;
        }
        List<ActionLogDO> logs = new ArrayList<>();
        for (String operateCaseCode : operateCaseCodeList) {
            ActionLogDO log = new ActionLogDO(
                    operateCaseCode,
                    (TestcaseStatusEnum.DISABLE.equals(event.getStatus())) ? "停用了用例" : "启用了用例",
                    (TestcaseStatusEnum.DISABLE.equals(event.getStatus()) && null != event.getAbandonReason()) ? (String.format("停用原因: %s", event.getAbandonReason().getDesc())) : "",
                    event.getClass().getName(),
                    event.getTransactor().getUserName(),
                    event.getTransactor().getUserId());
            logs.add(log);
        }
        logManager.createLogs(logs);
    }

    /**
     * 组装用例分组信息
     *
     * @param event {@link TestcaseChangeVersionEvent}
     * @return
     */
    private TestcaseGroupVO buildParentInfo(TestcaseChangeVersionEvent event) {
        //复用原分组
        if (event.getUseOriginalGroup()) {
            //待移动用例组信息
            List<TestcaseParentInfoVO> voList = testcaseRepository.selectGroupByCaseCodeList(Collections.singletonList(event.getCode()), event.getProductCode(),
                    event.getSourceVersionCode(), event.getType().name(), event.getSetCore());
            if (CollectionUtil.isEmpty(voList)) {
                throw new ServiceException("源分组不存在！");
            }
            TestcaseParentInfoVO vo = voList.get(0);
            if (StringUtil.isBlank(vo.getParentPath())) {
                //校验一层分组情况
                return testcaseRepository.buildSingleModule(vo.getParentCode(), vo.getParentName(), event.getTargetVersionCode(),
                        event.getProductCode(), event.getTransactor());
            } else {
                //校验多层分组情况
                return testcaseRepository.buildComplexModule(vo.getParentCode(), vo.getParentName(), event.getSourceVersionCode(), event.getTargetVersionCode(),
                        event.getProductCode(), event.getTransactor());
            }
        }

        //未分组用例
        if (StringUtils.isBlank(event.getTargetParentCode())) {
            TestcaseGroupVO result = new TestcaseGroupVO();
            result.setParentCode("");
            result.setParentPath("");
            return result;
        }

        //匹配目标分组
        TestcaseEntityDO entity = testcaseRepository.findTargetModule(event);
        if (null == entity) {
            throw new ServiceException("目标分组不存在！");
        }
        String path = (StringUtils.isBlank(entity.getPath()) ? StringUtils.EMPTY : entity.getPath() + ".") + event.getTargetParentCode();
        TestcaseGroupVO result = new TestcaseGroupVO();
        result.setParentPath(path);
        result.setParentCode(event.getTargetParentCode());
        return result;
    }

    public ImportResultResp importTestcase(ImportTestcaseReq req) throws ServiceException {
        ImportDataResp importDataResp = readFile(req.getFileType(), req.getFileUrl());
        if (Objects.isNull(importDataResp) || !importDataResp.getImportFlag()) {
            return ImportResultResp.buildErrorResult(importDataResp);
        }

        ImportResultResp resp = new ImportResultResp(Boolean.TRUE);
        List<TestCaseExcelColumn> importList = importDataResp.getData();
        checkImportListCount(importList);
        resp.setTotalNum(importList.size());

        Map<String, ListTestcaseModuleVO> parentFullNameTestcaseModuleVOMap = getParentFullNameTestcaseModuleVOMap(req);

        checkImportListContent(req.getProductCode(), importList, parentFullNameTestcaseModuleVOMap, req.getVersionCode());

        List<TestCaseExcelColumn> successImportList = importList.stream().filter(vo -> StringUtil.isBlank(vo.getCheckFailReason())).collect(Collectors.toList());
        resp.setSuccessNum(successImportList.size());

        sendAddTestcaseTagCommand(req, successImportList);

        List<AddTestcaseEvent> eventList = getAddTestcaseEventList(req, successImportList, parentFullNameTestcaseModuleVOMap);

        sendAddTestcaseEventThread(eventList, req.getPlanCode());

        List<TestCaseExcelColumn> failImportList = importList.stream().filter(vo -> StringUtil.isNotBlank(vo.getCheckFailReason())).collect(Collectors.toList());
        resp.setFailNum(failImportList.size());

        if (CollectionUtil.isEmpty(failImportList)) {
            return resp;
        }
        try {
            String failExcelName = spellFailExcelName(req.getFileName());
            resp.setFailFileName(failExcelName);

            // 上传失败记录 返回url
            ByteArrayInputStream inputStream = getFailListInputStream(failImportList);
            long fileSize = inputStream.available();
            String url = ztoOssService.uploadFile(Constant.OSS_PriBucketName, inputStream, ExtEnum.XLSX, failExcelName);
            // 添加导入、导出对象
            insertImpExpEntity(req, fileSize, url);
            String userName = req.getUser() != null ? req.getUser().getUserName() : "";
            resp.setFailFileUrl(projectRpcService.getFileUrl(userName, url));
        } catch (Exception e) {
            log.error("生成导入失败记录发生错误 is {} {} {}", JsonUtil.toJSON(req), e, e.toString());
        }
        return resp;
    }

    private ImportDataResp readFile(ImportFileTypeEunm type, String fileUrl) throws ServiceException {
        switch (type) {
            case EXCEL:
                return excelService.excelWriterTestcase(fileUrl);
            case XMIND:
                return xmindService.xMindWriterTestcase(fileUrl);
            default:
                return null;
        }
    }

    private void checkImportListCount(List<TestCaseExcelColumn> list) {
        int importCount = qcConfigBasicService.getTestcaseImportCount();
        if (CollectionUtil.isNotEmpty(list) && list.size() > importCount) {
            throw new ServiceException("数据超过" + importCount + "！请删减后重新提交");
        }
    }

    private Map<String, ListTestcaseModuleVO> getParentFullNameTestcaseModuleVOMap(ImportTestcaseReq req) {
        SimpleTestcaseModuleQuery query = new SimpleTestcaseModuleQuery();
        query.setType(TestcaseTypeEnum.MANUAL);
        query.setProductCode(req.getProductCode());
        query.setVersionCode(req.getVersionCode());
        query.setSetCore(req.getSetCore());
        query.setTransactor(req.getUser());

        List<ListTestcaseModuleVO> testcaseModuleVOList = testcaseQueryDomainService.simpleTestcaseModuleQuery(query);
        Map<String, ListTestcaseModuleVO> mapParentFullName = new HashMap<>();
        if (CollectionUtil.isNotEmpty(testcaseModuleVOList)) {
            mapParentFullName = testcaseModuleVOList.stream().collect(Collectors.toMap(ListTestcaseModuleVO::getFullName, Function.identity(), (key1, key2) -> key2));
        }
        return mapParentFullName;
    }

    private void checkImportListContent(String
                                                productCode, List<TestCaseExcelColumn> excelColumnList, Map<String, ListTestcaseModuleVO> parentFullNameTestcaseModuleVOMap, String
                                                versionCode) {
        Map<String, List<String>> parentFullNameCaseNameListMap = new HashMap<>();
        Pattern p = Pattern.compile("^\\b([1-9]|[1-9][0-9]|1[0-9][0-9]|200)\\b[,|\\.|:|：|，|、][\\w\\W]*");
        loop1:
        for (TestCaseExcelColumn excelColumn : excelColumnList) {
            if (StringUtil.isBlank(excelColumn.getCheckFailReason())) {
                // 有 bug ，解析出来的字段对不上
                if (StringUtil.isNotBlank(excelColumn.getName())) {
                    // 重名
                    if (excelColumn.getName().length() > 150) {
                        excelColumn.setCheckFailReason("标题不能超过150字！");
                        continue;
                    }
                    List<String> value = parentFullNameCaseNameListMap.get(excelColumn.getParentFullName());
                    if (value == null) {
                        value = new ArrayList<>();
                    }
                    if (value.contains(excelColumn.getName())) {
                        excelColumn.setCheckFailReason("已有相同的用例名称！");
                        continue;
                    } else {
                        value.add(excelColumn.getName());
                        parentFullNameCaseNameListMap.put(excelColumn.getParentFullName(), value);
                    }
                } else {
                    excelColumn.setCheckFailReason("用例名称不可以为空！");
                    continue;
                }
                if (StringUtil.isNotBlank(excelColumn.getParentFullName())) {
                    if (excelColumn.getParentFullName().split(">").length > 10) {
                        excelColumn.setCheckFailReason("用例分组超过规定层级！");
                        continue;
                    }

//                ListTestcaseModuleVO testcaseModuleVO = parentFullNameTestcaseModuleVOMap.get(excelColumn.getParentFullName());
//                if (testcaseModuleVO == null) {
//                    excelColumn.setCheckFailReason("用例分组不存在！");
//                    continue;
//                }
                }

                if (StringUtil.isNotBlank(excelColumn.getPrecondition())) {
                    if (excelColumn.getPrecondition().length() > 500) {
                        excelColumn.setCheckFailReason("前置条件超过规定长度！");
                        continue;
                    }
                }

                if (StringUtil.isNotBlank(excelColumn.getStepDesc()) || StringUtil.isNotBlank(excelColumn.getExpectResult())) {
                    List<String> specialCharacterList = Arrays.asList(",", ".", ":", "：", "，", "、");

                    if (StringUtil.isNotBlank(excelColumn.getStepDesc()) && StringUtil.isNotBlank(excelColumn.getExpectResult())) {
                        List<String> stepDescList = getStringList(excelColumn.getStepDesc());

                        if (stepDescList.size() > 500) {
                            excelColumn.setCheckFailReason("步骤描述超过500上限！");
                            continue;
                        }

                        List<String> stepNumList = new ArrayList<>();
                        for (String stepDesc : stepDescList) {
                            Matcher m = p.matcher(stepDesc);
                            if (!m.matches()) {
                                excelColumn.setCheckFailReason("步骤描述格式有误！");
                                continue loop1;
                            } else if (stepDesc.length() > 500) {
                                excelColumn.setCheckFailReason("单个步骤描述最多500个字符！");
                                continue loop1;
                            } else {
                                int index = getSpecialCharacterIndex(specialCharacterList, stepDesc);
                                if (index > 0) {
                                    String stepNum = stepDesc.substring(0, index);
                                    if (stepNumList.contains(stepNum)) {
                                        excelColumn.setCheckFailReason("步骤编号" + stepNum + "重复！");
                                        continue loop1;
                                    } else {
                                        stepNumList.add(stepNum);
                                    }
                                }
                            }
                        }
                    }
                    if (StringUtil.isNotBlank(excelColumn.getExpectResult())) {
                        List<String> expectResultList = getStringList(excelColumn.getExpectResult());

                        if (expectResultList.size() > 500) {
                            excelColumn.setCheckFailReason("预期结果超过500上限！");
                            continue;
                        }

                        List<String> resultNumList = new ArrayList<>();
                        for (String expectResult : expectResultList) {
                            Matcher m = p.matcher(expectResult);
                            if (!m.matches()) {
                                excelColumn.setCheckFailReason("预期结果格式有误！");
                                continue loop1;
                            } else if (expectResult.length() > 1000) {
                                excelColumn.setCheckFailReason("单个预期结果最多1000个字符！");
                                continue loop1;
                            } else {
                                int index = getSpecialCharacterIndex(specialCharacterList, expectResult);
                                if (index > 0) {
                                    String resultNum = expectResult.substring(0, index);
                                    if (resultNumList.contains(resultNum)) {
                                        excelColumn.setCheckFailReason("预期结果编号" + resultNum + "重复！");
                                        continue loop1;
                                    } else {
                                        resultNumList.add(resultNum);
                                    }
                                }
                            }
                        }
                    }
                }

                if (StringUtil.isNotBlank(excelColumn.getComment())) {
                    if (excelColumn.getComment().length() > 500) {
                        excelColumn.setCheckFailReason("备注超过规定长度！");
                        continue;
                    }
                } else {
                    excelColumn.setComment("");
                }
                if (StringUtil.isNotBlank(excelColumn.getTagName())) {
                    String[] tagNameArray = excelColumn.getTagName().split(",");
                    if (CollectionUtil.isNotEmpty(tagNameArray) && tagNameArray.length > 10) {
                        excelColumn.setCheckFailReason("关联的用例标签不能超过十个！");
                        continue;
                    }
                    for (String tagName : tagNameArray) {
                        if (tagName.length() > 10) {
                            excelColumn.setCheckFailReason("标签名称不能超过10字！");
                            continue loop1;
                        }
                    }
                }
            }
        }

        List<TestCaseExcelColumn> tempCaseExcelColumnList = excelColumnList.stream().filter(vo -> StringUtil.isBlank(vo.getCheckFailReason())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(tempCaseExcelColumnList)) {
            parentFullNameCaseNameListMap.clear();
            Set<String> parentCodeList = new HashSet<>();
            List<String> parentFullNameList = tempCaseExcelColumnList.stream()
                    .map(TestCaseExcelColumn::getParentFullName)
                    .distinct()
                    .collect(Collectors.toList());
            for (String parentFullName : parentFullNameList) {
                if (StringUtil.isEmpty(parentFullName)) {
                    parentCodeList.add("");
                    continue;
                }
                ListTestcaseModuleVO parent = parentFullNameTestcaseModuleVOMap.get(parentFullName.trim());
                if (parent != null) {
                    parentCodeList.add(parent.getCode());
                } else {
                    parentCodeList.add("");
                }
            }

            List<TestcaseVO> tempTestcaseVOList = new ArrayList<>();
            List<List<String>> parentCodePartition = Lists.partition(new ArrayList(parentCodeList), 200);
            for (List<String> codes : parentCodePartition) {
                if (CollectionUtil.isNotEmpty(codes)) {
                    SimpleTestcaseQuery simpleTestcaseQuery = new SimpleTestcaseQuery();
                    simpleTestcaseQuery.setParentCodeList(codes);
                    simpleTestcaseQuery.setAttribute(TestcaseAttributeEnum.TESTCASE);
                    simpleTestcaseQuery.setType(TestcaseTypeEnum.MANUAL);
                    simpleTestcaseQuery.setProductCode(productCode);
                    simpleTestcaseQuery.setVersionCode(versionCode);
                    List<TestcaseVO> tempList = testcaseQueryDomainService.simpleTestcaseQuery(simpleTestcaseQuery);
                    if (CollectionUtil.isNotEmpty(tempList)) {
                        tempTestcaseVOList.addAll(tempList);
                    }
                }
            }

            if (CollectionUtil.isNotEmpty(tempTestcaseVOList)) {
                parentFullNameCaseNameListMap = tempTestcaseVOList.stream()
                        .collect(Collectors.groupingBy(
                                TestcaseVO::getParentCode,
                                Collectors.mapping(TestcaseVO::getName, Collectors.toList())));
            }

            for (TestCaseExcelColumn testCaseExcelColumn : tempCaseExcelColumnList) {
                String parentCode = "";
                ListTestcaseModuleVO parent = parentFullNameTestcaseModuleVOMap.get(StringUtil.isNotEmpty(testCaseExcelColumn.getParentFullName()) ? testCaseExcelColumn.getParentFullName().trim() : testCaseExcelColumn.getParentFullName());
                if (parent != null) {
                    parentCode = parent.getCode();
                }
                List<String> nameList = parentFullNameCaseNameListMap.get(parentCode);
                if (CollectionUtil.isNotEmpty(nameList) && nameList.contains(testCaseExcelColumn.getName().trim())) {
                    testCaseExcelColumn.setCheckFailReason("名称不能重复！");
                    continue;
                }
            }
        }
    }

    private List<String> getStringList(String s) {
        Pattern p = Pattern.compile("^\\b([1-9]|[1-9][0-9]|1[0-9][0-9]|200)\\b[,|\\.|:|：|，|、][\\w\\W]*");
        if (!p.matcher(s).matches()) {
            return Arrays.asList("1." + s);
        }
        s = s.replaceFirst("[1-9]|[1-9][0-9]|1[0-9][0-9]|200", "")
                .replaceAll("\\r\\n(1[0-9][0-9]|200)|\\n(1[0-9][0-9]|200)|\\r(1[0-9][0-9]|200)", "######")
                .replaceAll("\\r\\n([1-9][0-9])|\\n([1-9][0-9])|\\r([1-9][0-9])", "######")
                .replaceAll("\\r\\n([1-9])|\\n([1-9])|\\r([1-9])", "######");
        //根据换行符截取
        String[] textStr = s.split("######");
        //System.out.println("输出一下数量="+textStr.length);
        List<String> list = Arrays.asList(textStr);
        //list去除空
        list = list.stream().filter(str -> StringUtil.isNotBlank(str)).map(String::trim).collect(Collectors.toList());
        // 排顺序
        list = sortStringList(list);
        return list;
    }

    /**
     * 步骤排序
     */
    private List<String> sortStringList(List<String> list) {
        List<String> stringList = new ArrayList<>();
        int num = 1;
        for (String str : list) {
            stringList.add(num + str);
            num++;
        }
        return stringList;
    }

    private void sendAddTestcaseTagCommand(ImportTestcaseReq req, List<TestCaseExcelColumn> successCaseExcelColumnList) {
        List<TagVO> tagVOList = tagQueryDomainService.listTagsByBusinessCodeQuery(req.getProductCode());
        Map<String, TagVO> mapTagVO = new HashMap<>();
        if (CollectionUtil.isNotEmpty(tagVOList)) {
            mapTagVO = tagVOList.stream()
                    .collect(Collectors.toMap(TagVO::getTagName, Function.identity(), (key1, key2) -> key2));
        }

        if (CollectionUtil.isNotEmpty(successCaseExcelColumnList)) {
            Set<String> tempTagName = new HashSet<>();

            Set<String> addTagName = new HashSet<>();
            for (TestCaseExcelColumn testCaseExcelColumn : successCaseExcelColumnList) {
                if (StringUtil.isNotBlank(testCaseExcelColumn.getTagName())) {
                    String[] tagNameArray = testCaseExcelColumn.getTagName().split(",");
                    tempTagName.addAll(Arrays.asList(tagNameArray));
                }
            }
            for (String tagName : tempTagName) {
                TagVO tagVO = mapTagVO.get(tagName);
                if (tagVO == null) {
                    addTagName.add(tagName);
                }
            }

            // 创建新标签
            for (String tagName : addTagName) {
                AddTestcaseTagCommand command =
                        new AddTestcaseTagCommand(aggregateIdGenerateService.generateId("SNOWFLAKE"));
                command.setBusinessCode(req.getProductCode());
                command.setDomain(DomainEnum.PRODUCT);
                command.setTagName(tagName);
                command.setType(TagTypeEnum.BUSINESS);
                command.setTransactor(req.getUser());
                testcaseTagCommendDomainService.addTestcaseTagCommand(command);
            }
        }
    }

    private List<AddTestcaseEvent> getAddTestcaseEventList(ImportTestcaseReq req, List<TestCaseExcelColumn> successCaseExcelColumnList,
                                                           Map<String, ListTestcaseModuleVO> parentFullNameTestcaseModuleVOMap) {
        List<AddTestcaseEvent> eventList = new ArrayList<>();
        Map<String, ListTestcaseModuleVO> createdModules = new HashMap<>();
        processItemsSequentially(req, successCaseExcelColumnList, parentFullNameTestcaseModuleVOMap, eventList, createdModules);
        return eventList;
    }

    private void processItemsSequentially(ImportTestcaseReq req,
                                          List<TestCaseExcelColumn> successCaseExcelColumnList,
                                          Map<String, ListTestcaseModuleVO> parentFullNameTestcaseModuleVOMap,
                                          List<AddTestcaseEvent> eventList,
                                          Map<String, ListTestcaseModuleVO> createdModules) {
        for (TestCaseExcelColumn excelColumn : successCaseExcelColumnList) {
            processParentModules(excelColumn.getParentFullName(), req, parentFullNameTestcaseModuleVOMap, eventList, createdModules);
            AddTestcaseEvent caseEvent = createTestCaseEvent(excelColumn, req, parentFullNameTestcaseModuleVOMap);
            eventList.add(caseEvent);
        }
    }

    private AddTestcaseEvent createTestCaseEvent(TestCaseExcelColumn testCaseExcelColumn, ImportTestcaseReq req, Map<String, ListTestcaseModuleVO> parentFullNameTestcaseModuleVOMap) {
        AddTestcaseEvent event = new AddTestcaseEvent();
        event.setCode(aggregateIdGenerateService.generateId("TEST_CASE"));
        event.setProductCode(req.getProductCode());
        event.setVersionCode(req.getVersionCode());
        event.setSetCore(req.getSetCore());
        event.setName(testCaseExcelColumn.getName());
        event.setParentCode("");
        event.setPath("");
        ListTestcaseModuleVO parent = parentFullNameTestcaseModuleVOMap.get(testCaseExcelColumn.getParentFullName());
        if (parent != null) {
            event.setParentCode(parent.getCode());
            if (StringUtil.isBlank(parent.getPath())) {
                event.setPath(parent.getCode());
            } else {
                event.setPath(parent.getPath() + "." + parent.getCode());
            }
        }
        event.setAttribute(TestcaseAttributeEnum.TESTCASE);
        event.setType(TestcaseTypeEnum.MANUAL);
        event.setPriority(
                StringUtil.isNotEmpty(testCaseExcelColumn.getPriorityDesc()) ? TestcasePriorityEnum.getEnum(
                        testCaseExcelColumn.getPriorityDesc())
                        != null
                        ? TestcasePriorityEnum.getEnum(
                        testCaseExcelColumn.getPriorityDesc().trim())
                        : TestcasePriorityEnum.MIDDLE : TestcasePriorityEnum.MIDDLE);
        event.setStatus(TestcaseStatusEnum.NORMAL);
        event.setPrecondition("");
        if (StringUtil.isNotBlank(testCaseExcelColumn.getPrecondition())) {
            event.setPrecondition(testCaseExcelColumn.getPrecondition().trim());
        }
        event.setComment("");
        if (StringUtil.isNotBlank(testCaseExcelColumn.getComment())) {
            event.setComment(testCaseExcelColumn.getComment().trim());
        }

        if (StringUtil.isNotBlank(testCaseExcelColumn.getStepDesc()) || StringUtil.isNotBlank(testCaseExcelColumn.getExpectResult())) {
            List<TestcaseStepVO> testSteps = new ArrayList<>();

            //解析步骤描述（1.。。。2.。。。）
            Map<String, String> stepsList = new HashMap<>();
            if (StringUtil.isNotBlank(testCaseExcelColumn.getStepDesc())) {
                stepsList = getListByReg(testCaseExcelColumn.getStepDesc().trim());
            }
            //解析预期结果（1.。。。2.。。。）
            Map<String, String> expectResultList = new HashMap<>();
            if (StringUtil.isNotBlank(testCaseExcelColumn.getExpectResult())) {
                expectResultList = getListByReg(testCaseExcelColumn.getExpectResult().trim());
            }
            Set<String> numSet = new HashSet<>();
            numSet.addAll(stepsList.keySet());
            numSet.addAll(expectResultList.keySet());

            for (String num : numSet) {
                TestcaseStepVO testcaseStepVO = new TestcaseStepVO();
                testcaseStepVO.setSort(Integer.parseInt(num));
                testcaseStepVO.setStepDesc(stepsList.get(num) != null ? stepsList.get(num) : "");
                //预期结果,如果预期结果为空，或者不存在，设置"",预期结果多余步骤，直接省略
                testcaseStepVO.setExpectResult(expectResultList.get(num) != null ? expectResultList.get(num) : "");

                testSteps.add(testcaseStepVO);
            }
            event.setTestSteps(testSteps);
        }
        if (StringUtil.isNotBlank(testCaseExcelColumn.getTagName())) {
            List<TagVO> tags = new ArrayList<>();
            String[] tagNameArray = testCaseExcelColumn.getTagName().split(",");
            for (String tagName : tagNameArray) {
                TagVO tag = new TagVO();
                tag.setBusinessCode(event.getCode());
                tag.setTagName(tagName);
                tag.setDomain(DomainEnum.TESTCASE);
                tag.setType(TagTypeEnum.BUSINESS);
                tags.add(tag);
            }
            event.setTags(tags);
        }
        event.setType(TestcaseTypeEnum.MANUAL);
        event.setTransactor(req.getUser());
        return event;
    }

    private void processParentModules(String parentFullName,
                                      ImportTestcaseReq req,
                                      Map<String, ListTestcaseModuleVO> parentFullNameTestcaseModuleVOMap,
                                      List<AddTestcaseEvent> eventList,
                                      Map<String, ListTestcaseModuleVO> createdModules) {
        if (StringUtil.isBlank(parentFullName)) {
            return;
        }
        String[] pathSegments = parentFullName.split(">");
        StringBuilder currentPath = new StringBuilder();

        for (int i = 0; i < pathSegments.length; i++) {
            currentPath.append(i > 0 ? ">" : "").append(pathSegments[i]);
            String fullName = currentPath.toString();

            if (!createdModules.containsKey(fullName) && !parentFullNameTestcaseModuleVOMap.containsKey(fullName)) {
                AddTestcaseEvent moduleEvent = createModuleEvent(fullName, req, parentFullNameTestcaseModuleVOMap);
                if (Objects.isNull(moduleEvent)) {
                    continue;
                }
                eventList.add(moduleEvent);
                ListTestcaseModuleVO vo = convertToModuleVO(moduleEvent, fullName);
                parentFullNameTestcaseModuleVOMap.put(fullName, vo);
                createdModules.put(fullName, vo);
            }
        }
    }

    private ListTestcaseModuleVO convertToModuleVO(AddTestcaseEvent event, String fullName) {
        ListTestcaseModuleVO listTestcaseModuleVO = new ListTestcaseModuleVO();
        listTestcaseModuleVO.setCode(event.getCode());
        listTestcaseModuleVO.setName(event.getName());
        listTestcaseModuleVO.setProductCode(event.getProductCode());
        listTestcaseModuleVO.setVersionCode(event.getVersionCode());
        listTestcaseModuleVO.setSetCore(event.getSetCore());
        listTestcaseModuleVO.setParentCode(event.getParentCode());
        listTestcaseModuleVO.setType(TestcaseTypeEnum.MANUAL);
        listTestcaseModuleVO.setStatus(TestcaseStatusEnum.NORMAL);
        listTestcaseModuleVO.setFullName(fullName);
        listTestcaseModuleVO.setPath(event.getPath());
        return listTestcaseModuleVO;
    }

    private AddTestcaseEvent createModuleEvent(String fullName, ImportTestcaseReq req, Map<String, ListTestcaseModuleVO> parentFullNameTestcaseModuleVOMap) {
        String[] nameArray = fullName.split(">");
        if (nameArray.length == 0) {
            log.error("创建模块失败，模块名称为空,req:{}", JSON.toJSON(req));
            return null;
        }
        AddTestcaseEvent event = new AddTestcaseEvent();
        event.setCode(aggregateIdGenerateService.generateId("TEST_CASE"));
        event.setProductCode(req.getProductCode());
        event.setVersionCode(req.getVersionCode());
        event.setSetCore(req.getSetCore());
        event.setName(nameArray[nameArray.length - 1]);
        event.setParentCode("");
        event.setPath("");
        event.setPriority(TestcasePriorityEnum.MIDDLE);
        event.setPrecondition("");
        event.setComment("");
        ListTestcaseModuleVO parent = parentFullNameTestcaseModuleVOMap.get(nameArray.length > 1
                ? StringUtils.join(Arrays.asList(Arrays.copyOf(nameArray, nameArray.length - 1)), ">")
                : "");
        if (parent != null) {
            event.setParentCode(parent.getCode());
            if (StringUtil.isBlank(parent.getPath())) {
                event.setPath(parent.getCode());
            } else {
                event.setPath(parent.getPath() + "." + parent.getCode());
            }
        }
        event.setAttribute(TestcaseAttributeEnum.MODULE);
        event.setType(TestcaseTypeEnum.MANUAL);
        event.setStatus(TestcaseStatusEnum.NORMAL);
        event.setTransactor(req.getUser());
        return event;
    }


    //根据类似1. 2. 3.截取字符串(换成根据换行符截取)
    private Map<String, String> getListByReg(String s) {
        List<String> specialCharacterList = Arrays.asList(",", ".", ":", "：", "，", "、");
        List<String> list = getStringList(s);
        Map<String, String> listNew = new HashMap<>();
        //判断第一个.出现的index
        for (int i = 0; i < list.size(); i++) {
            String str = list.get(i);
            int index = getSpecialCharacterIndex(specialCharacterList, str);

            //判断截取的前一段字符串是否是数字，是数字才截取,不是数字的话拼接到上一个字符串里面，并且用,隔开
            String num = str.substring(0, index);
            String value = str.substring(index + 1);
            listNew.put(num, value);
        }

        return listNew;
    }

    public static int getSpecialCharacterIndex(List<String> specialCharacterList, String str) {
        int index = -1;
        for (String specialCharacter : specialCharacterList) {
            int tempIndex = str.indexOf(specialCharacter);
            if (tempIndex > 0) {
                if (index < 0) {
                    index = tempIndex;
                } else {
                    if (tempIndex < index) {
                        index = tempIndex;
                    }
                }
            }
        }
        return index;
    }

    private void sendAddTestcaseEventThread(List<AddTestcaseEvent> eventList, String planCode) {
        if (THREAD_POOL_EXECUTOR.isShutdown()) {
            THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(4, 4, 60, TimeUnit.SECONDS,
                    new ArrayBlockingQueue<Runnable>(40), new ThreadPoolExecutor.AbortPolicy());
        }
        THREAD_POOL_EXECUTOR.execute(() -> {
            this.addTestcase(eventList, planCode);
        });
    }

    public void addTestcase(List<AddTestcaseEvent> eventList, String planCode) {
        log.info("addTestcase--start");
        try {
            List<TestcaseEntityDO> entityList = new ArrayList<>();
            List<TagEntityDO> tagEntityList = new ArrayList<>();
            List<TestcaseStepEntityDO> testcaseStepEntityList = new ArrayList<>();
            List<TmTestPlanCaseEntityDO> planCaseEntityList = new ArrayList<>();
            TmTestPlanEntityDO planEntity = null;
            List<ActionLogDO> logs = new ArrayList<>();
            // 查计划
            if (StringUtils.isNotEmpty(planCode)) {
                planEntity = testPlanRepository.getTestPlanByCode(planCode);
            }

            for (AddTestcaseEvent event : eventList) {
                TestcaseEntityDO entity = testcaseDomainConverter.convertor(event);
                entity.setAutomaticSourceCode("");
                if (null == event.getDutyUserId() || StringUtil.isEmpty(event.getDutyUser())) {
                    entity.setDutyUser(event.getTransactor().getUserName());
                    entity.setDutyUserId(event.getTransactor().getUserId());
                }
                entity.setSort(0);
                // 标题、前置条件、备注前后去空格
                entity.setName(StringUtils.isEmpty(entity.getName()) ?
                        "" : entity.getName().trim());
                entity.setPrecondition(StringUtils.isEmpty(entity.getPrecondition()) ?
                        "" : entity.getPrecondition().trim());
                entity.setComment(StringUtils.isEmpty(entity.getComment()) ?
                        "" : entity.getComment().trim());
                entity.setVersionCode(event.getVersionCode());
                entity.setSetCore(event.getSetCore());
                entity.setGmtCreate(new Date());
                entityList.add(entity);

                // 用例关联计划
                if (planEntity != null && TestcaseAttributeEnum.TESTCASE.equals(event.getAttribute())) {
                    planCaseEntityList.addAll(addPlanCaseInList(planEntity, event));
                }

                if (CollectionUtil.isNotEmpty(event.getTags())) {
                    List<TagEntityDO> tempTagEntityList = testcaseDomainConverter.convert(event.getTags());
                    for (TagEntityDO tag : tempTagEntityList) {
                        tag.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                        tag.preCreate(event);
                        tag.setBusinessCode(event.getCode());
                        tagEntityList.add(tag);
                    }
                }

                if (CollectionUtil.isNotEmpty(event.getTestSteps())) {
                    List<TestcaseStepEntityDO> tempEntityList = testcaseDomainConverter.convertToStepList(event.getTestSteps());
                    for (TestcaseStepEntityDO step : tempEntityList) {
                        step.preCreate(event);
                        step.setTestcaseCode(entity.getCode());
                        // 步骤、预期结果前后去空格
                        step.setStepDesc(StringUtils.isEmpty(step.getStepDesc()) ?
                                "" : step.getStepDesc().trim());
                        step.setExpectResult(StringUtils.isEmpty(step.getExpectResult()) ?
                                "" : step.getExpectResult().trim());
                        testcaseStepEntityList.add(step);
                    }
                }

                ActionLogDO log = new ActionLogDO(event.getCode(), event.action(), event.makeString() != null ? event.makeString() : "", AddTestcaseEvent.class.getName(),
                        event.getTransactor().getUserName(), event.getTransactor().getUserId());
                logs.add(log);
            }

            if (CollectionUtil.isNotEmpty(entityList)) {
                List<List<TestcaseEntityDO>> partitionList = Lists.partition(entityList, 100);
                for (List<TestcaseEntityDO> partition : partitionList) {
                    testcaseRepository.saveBatch(partition);
                }
            }

            if (CollectionUtil.isNotEmpty(planCaseEntityList)) {
                List<List<TmTestPlanCaseEntityDO>> partitionList = Lists.partition(planCaseEntityList, 100);
                for (List<TmTestPlanCaseEntityDO> partition : partitionList) {
                    iTmTestPlanCaseRepository.batchSave(partition);
                }
            }

            if (CollectionUtil.isNotEmpty(tagEntityList)) {
                List<List<TagEntityDO>> partitionList = Lists.partition(tagEntityList, 200);
                for (List<TagEntityDO> partition : partitionList) {
                    tagRepository.saveBatch(partition);
                }
            }

            if (CollectionUtil.isNotEmpty(testcaseStepEntityList)) {
                List<List<TestcaseStepEntityDO>> partitionList = Lists.partition(testcaseStepEntityList, 200);
                for (List<TestcaseStepEntityDO> partition : partitionList) {
                    testcaseStepRepository.saveBatch(partition);
                }
            }
            eventLogManager.createLogs(logs);
        } catch (Exception e) {
            log.error("addTestcase error");
            throw new RuntimeException();
        }
        log.info("addTestcase--end");
    }

    private List<TmTestPlanCaseEntityDO> addPlanCaseInList(TmTestPlanEntityDO planEntity, AddTestcaseEvent event) {
        if (planEntity == null) {
            return CollectionUtil.newEmptyList();
        }
        List<TmTestPlanCaseEntityDO> planCaseEntityList = new ArrayList<>();
        String planCode = planEntity.getCode();
        if (!TestPlanStrategyEnum.ALL_TEST.equals(planEntity.getTestStrategy())
                && !TestPlanStrategyEnum.ALLOW_EXIT_TEST.equals(planEntity.getTestStrategy())
                && !TestPlanStrategyEnum.STANDARD_TEST.equals(planEntity.getTestStrategy())) {
            TmTestPlanCaseEntityDO planCaseEntityNullTest = fillPlanCaseEntity(planCode, event);
            planCaseEntityNullTest.setTestStage(TestPlanStageEnum.NULL_TEST);
            planCaseEntityList.add(planCaseEntityNullTest);
            return planCaseEntityList;
        }
        if (planEntity.getStageStatus() != null) {
            Map<String, Object> stageMap = planEntity.getStageStatus();
            if (!TestPlanStageStatusEnum.COMPLETED.name().equals(stageMap.get("FUNCTIONAL_TEST"))) {
                TmTestPlanCaseEntityDO planCaseEntityFunction = fillPlanCaseEntity(planCode, event);
                planCaseEntityFunction.setTestStage(TestPlanStageEnum.FUNCTIONAL_TEST);
                planCaseEntityList.add(planCaseEntityFunction);
            }
            if (TestcasePriorityEnum.HIGH.equals(event.getPriority())) {
                if (!TestPlanStageStatusEnum.COMPLETED.name().equals(stageMap.get("SMOKE_TEST"))) {
                    TmTestPlanCaseEntityDO planCaseEntitySmoke = fillPlanCaseEntity(planCode, event);
                    planCaseEntitySmoke.setTestStage(TestPlanStageEnum.SMOKE_TEST);
                    planCaseEntityList.add(planCaseEntitySmoke);
                }
                if (!TestPlanStageStatusEnum.COMPLETED.name().equals(stageMap.get("ONLINE_SMOKE_TEST"))) {
                    TmTestPlanCaseEntityDO planCaseEntityOnlineSmoke = fillPlanCaseEntity(planCode, event);
                    planCaseEntityOnlineSmoke.setTestStage(TestPlanStageEnum.ONLINE_SMOKE_TEST);
                    planCaseEntityList.add(planCaseEntityOnlineSmoke);
                }
            }
        }
        return planCaseEntityList;
    }

    private TmTestPlanCaseEntityDO fillPlanCaseEntity(String planCode, AddTestcaseEvent event) {
        TmTestPlanCaseEntityDO planCaseEntity = new TmTestPlanCaseEntityDO();
        planCaseEntity.setPlanCode(planCode);
        planCaseEntity.setCaseCode(event.getCode());
        planCaseEntity.setCaseType(TestcaseTypeEnum.MANUAL);
        planCaseEntity.setStatus(TestPlanCaseStatusEnum.INITIAL);
        planCaseEntity.setOperateCaseCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        planCaseEntity.preCreate(event);
        return planCaseEntity;
    }

    private String spellFailExcelName(String fileName) {
        List<String> extList = Arrays.asList(".xmind", ".xlsx", ".xls");
        int index = -1;
        index = fileName.lastIndexOf(".");
        String ext = "";
        if (index >= 0) {
            ext = fileName.substring(index).toLowerCase();
        }
        if (extList.contains(ext)) {
            fileName = fileName.substring(0, index);
        }
        return fileName.replaceAll("\\.", "_").replaceAll(" ", "") + "_失败记录";
    }

    private ByteArrayInputStream getFailListInputStream(List<TestCaseExcelColumn> failList) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, TestCaseExcelColumn.class).sheet("列表").doWrite(failList);
        return new ByteArrayInputStream(outputStream.toByteArray());
    }

    private void insertImpExpEntity(ImportTestcaseReq req, long fileSize, String url) {
        impExpRepository.insert(req, fileSize, url);
    }

    public void editTestcaseTitle(EditTestcaseTitleCommand command, TestcaseVO testcaseVO) {
        log.info("EditTestcaseTitleCommand>>>>>>>>>>>>>>>>aggregateId = :{}", command.getAggregateId());
        EditTestcaseTitleEvent event = testcaseDomainConverter.convertor(command);
        event.setAggregateId(command.getAggregateId());
        event.setType(testcaseVO.getType());
        event.setParentCode(testcaseVO.getParentCode());
        event.setAttribute(testcaseVO.getAttribute());
        event.setProductCode(testcaseVO.getProductCode());
        testcaseRepository.editTestcaseTitle(event);
        apply(event);

    }

    public void releaseTestcaseRelationCommand(ReleaseTestcaseRelationCommand command) {
        log.info("ReleaseTestcaseRelationCommand >>> {}", command.getAggregateId());
        TestcaseRelationReleasedEvent event = testcaseDomainConverter.convertor(command);
        testcaseRepository.releaseTestcaseRelation(event);
        event.setAggregateId(command.getAggregateId());
        apply(event);
        if (StringUtils.isNotBlank(command.getOperateCaseCode())) {
            PlanCaseOperatedLogEvent logEvent = new PlanCaseOperatedLogEvent();
            logEvent.setTransactor(command.getTransactor());
            logEvent.setOccurred(command.getOccurred());
            logEvent.setCode(command.getOperateCaseCode());
            logEvent.setAction(String.format("解除了关联%s", command.getDomain().getValue()));
            logEvent.setAggregateId(command.getAggregateId());
            apply(logEvent);
        }
    }

    public void moveAutomaticRecordCommand(MoveAutomaticRecordCommand command) {

        log.info("MoveAutomaticRecordCommand>>>>>>>>>>>>>>>>aggregateId = :{}", command.getAggregateId());
        MoveModuleEvent event = testcaseModuleDomainConverter.converter(command);
        event.setAggregateId(command.getAggregateId());
        testcaseRepository.moveModule(event);
        apply(event);

    }

    public void moveModuleCommand(MoveModuleCommand command) {

        log.info("MoveModuleCommand>>>>>>>>>>>>>>>>aggregateId = :{}", command.getAggregateId());
        MoveModuleEvent event = testcaseModuleDomainConverter.converter(command);
        event.setAggregateId(command.getAggregateId());
        testcaseRepository.moveModule(event);
        apply(event);

    }

    public void handleMoveSceneModuleEvent(MoveSceneModuleEvent event) {
        log.info("移动场景图或分组同步自动化用例库 >>> {}", JSON.toJSONString(event));
        MoveModuleEvent moveModuleEvent = new MoveModuleEvent();
        moveModuleEvent.setParentCode(event.getParentTestcaseCode());
        moveModuleEvent.setTransactor(event.getTransactor());
        if (StringUtils.isNotEmpty(event.getParentTestcaseCode())) {
            TestcaseEntityDO targetModule = testcaseRepository.loadFormDb(event.getParentTestcaseCode());
            if (null == targetModule) {
                log.error("目标分组不存在！{}", event.getParentTestcaseCode());
                return;
            }
            moveModuleEvent.setNewPath(targetModule.getPath());
        }
        if (SceneIndexTypeEnum.MODULE.equals(event.getSceneIndexType())) {
            TestcaseEntityDO testcaseModule = testcaseRepository.loadFormDb(event.getTestcaseCode());
            if (null == testcaseModule) {
                log.error("当前分组不存在！{}", event.getTestcaseCode());
                return;
            }
            moveModuleEvent.setOldPath(testcaseModule.getPath());
            moveModuleEvent.setCode(event.getTestcaseCode());
            moveModuleEvent.setType(TestcaseTypeEnum.AUTO);
        } else {
            SceneInfoEntityDO sceneInfo = apiTestRepository.queryLatestSceneInfo(event.getCode(), SceneInfoStatusEnum.publish);
            if (null == sceneInfo) {
                log.warn("当前场景图未发布！{}", event.getCode());
                return;
            }
            moveModuleEvent.setCode(sceneInfo.getAutomaticSourceCode());
            moveModuleEvent.setType(TestcaseTypeEnum.SOURCERECORD);
        }
        this.moveModule(moveModuleEvent);
    }

    /**
     * 场景新增分组，同步新增自动化用例库分组
     */
    public String addModuleBySceneIndex(AddSceneModuleCommand command, String parentCode) {
        if (StringUtil.isBlank(command.getProductCode()) || StringUtil.isBlank(command.getSceneIndexName())) {
            log.info(parentCode + "_addModuleBySceneIndex_name_is_blank");
            return Strings.EMPTY;
        }
        if (command.getSceneIndexName().length() > 60) {
            log.info(parentCode + "_addModuleBySceneIndex_name_too_long_{}", command.getSceneIndexName());
            return Strings.EMPTY;
        }
        String path = Strings.EMPTY;
        if (StringUtil.isNotEmpty(parentCode)) {
            TestcaseEntityDO parentModule = testcaseRepository.selectTestcaseByCode(parentCode);
            path = null == parentModule ? Strings.EMPTY : (StringUtil.isBlank(parentModule.getPath()) ? parentCode : parentModule.getPath() + "." + parentCode);
        } else {
            // 是否已有 用例工厂 分组
            TestcaseEntityDO firstModule = getModuleByNameAndParent("", "用例工厂", command.getProductCode());
            if (null == firstModule) {
                // 新增分组-用例工厂
                AddTestcaseEvent addFirstModuleEvent =
                        this.addNewAutoModule("用例工厂", command.getProductCode(), command.getTransactor(), "", 1);
                // 新增分组-发布
                AddTestcaseEvent event =
                        this.addNewAutoModule("发布-场景", command.getProductCode(), command.getTransactor(), addFirstModuleEvent.getCode(), 2);
                parentCode = event.getCode();
                path = event.getParentCode() + "." + event.getCode();
            } else {
                // 是否已有 发布 分组
                TestcaseEntityDO secondLevelModule = getModuleByNameAndParent(firstModule.getCode(), "发布-场景", command.getProductCode());
                if (null != secondLevelModule) {
                    parentCode = secondLevelModule.getCode();
                    path = secondLevelModule.getParentCode() + "." + secondLevelModule.getCode();
                } else {
                    AddTestcaseEvent event =
                            this.addNewAutoModule("发布-场景", command.getProductCode(), command.getTransactor(), firstModule.getCode(), 2);
                    parentCode = event.getCode();
                    path = event.getParentCode() + "." + event.getCode();
                }
            }
        }
        //是否存在同名分组
        List<TestcaseEntityDO> sameNameModuleList = testcaseRepository.selectSameNameModuleByParentCode(parentCode, command.getSceneIndexName());
        if (CollectionUtil.isNotEmpty(sameNameModuleList)) {
            log.info(parentCode + "_addModuleBySceneIndex_repeat_name_{}!", command.getSceneIndexName());
            return Strings.EMPTY;
        }
        //数据保存
        AddTestcaseEvent addTestcaseEvent = new AddTestcaseEvent();
        addTestcaseEvent.setCode(aggregateIdGenerateService.generateId(AggregateType.TEST_CASE));
        addTestcaseEvent.setSetCore(Boolean.FALSE);
        addTestcaseEvent.setProductCode(command.getProductCode());
        addTestcaseEvent.setParentCode(parentCode);
        addTestcaseEvent.setName(command.getSceneIndexName());
        addTestcaseEvent.setPath(path);
        addTestcaseEvent.setType(TestcaseTypeEnum.AUTO);
        addTestcaseEvent.setAttribute(TestcaseAttributeEnum.MODULE);
        testcaseRepository.addTestcase(addTestcaseEvent);
        return addTestcaseEvent.getCode();
    }

    private TestcaseEntityDO getModuleByNameAndParent(String parentCode, String name, String productCode) {
        TestcaseEntityDO entityDO = new TestcaseEntityDO();
        entityDO.setParentCode(parentCode);
        entityDO.setName(name);
        entityDO.setProductCode(productCode);
        entityDO.setVersionCode("NONE_VERSION");
        entityDO.setType(TestcaseTypeEnum.AUTO);
        entityDO.setAttribute(TestcaseAttributeEnum.MODULE);
        return testcaseRepository.selectModuleInfoByName(entityDO);
    }

    private AddTestcaseEvent addNewAutoModule(String name, String productCode, User transactor,
                                              String parentCode, Integer layer) {
        AddTestcaseEvent event = new AddTestcaseEvent();
        event.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.TEST_CASE));
        event.setCode(event.getAggregateId());
        event.setProductCode(productCode);
        event.setName(name);
        event.setType(TestcaseTypeEnum.AUTO);
        event.setAttribute(TestcaseAttributeEnum.MODULE);
        event.setLayer(layer);
        event.setParentCode(parentCode);
        event.setTransactor(transactor);
        testcaseRepository.addTestcase(event);
        apply(event);
        return event;
    }

    @Async
    public void clearDisableTestcaseJob() {
        int duration = qcConfigBasicService.getClearTestcaseDuration();
        Date durationDate = DateUtil.offsetDay(new Date(), -duration).toJdkDate();
        log.info("clearDisableTestcaseJob durationDate: {}", durationDate);
        int num = 1;
        while (true) {
            PageHelper.startPage(num, 2000);
            List<String> codeList = testcaseRepository.queryDisableCaseCodeByDurationDate(durationDate);
            if (CollectionUtil.isEmpty(codeList)) {
                break;
            }
            List<TmTestPlanCaseEntityDO> planCaseList = iTmTestPlanCaseRepository.queryPlanCaseByCaseCode(codeList);
            if (CollectionUtil.isEmpty(planCaseList)) {
                testcaseRepository.batchDeleteCaseByCode(codeList);
                automaticSchedulerRepository.deleteSchedulerCaseByCodes(codeList);
                continue;
            }
            List<String> planCodeList = planCaseList.stream()
                    .map(TmTestPlanCaseEntityDO::getPlanCode)
                    .distinct().collect(Collectors.toList());
            List<String> completedPlanCodeList = iTmTestPlanRepository.filterCompletedPlanCode(planCodeList);
            planCodeList.removeAll(completedPlanCodeList);
            if (CollectionUtil.isEmpty(planCodeList)) {
                testcaseRepository.batchDeleteCaseByCode(codeList);
                automaticSchedulerRepository.deleteSchedulerCaseByCodes(codeList);
                continue;
            }
            Map<String, List<String>> planCaseMap = planCaseList.stream()
                    .collect(Collectors.groupingBy(TmTestPlanCaseEntityDO::getCaseCode,
                            Collectors.mapping(TmTestPlanCaseEntityDO::getPlanCode, Collectors.toList())));
            for (Map.Entry<String, List<String>> entry : planCaseMap.entrySet()) {
                if (entry.getValue().stream().anyMatch(planCodeList::contains)) {
                    codeList.remove(entry.getKey());
                }
            }
            if (CollectionUtil.isNotEmpty(codeList)) {
                testcaseRepository.batchDeleteCaseByCode(codeList);
                automaticSchedulerRepository.deleteSchedulerCaseByCodes(codeList);
            }
            num++;
        }
    }
}
