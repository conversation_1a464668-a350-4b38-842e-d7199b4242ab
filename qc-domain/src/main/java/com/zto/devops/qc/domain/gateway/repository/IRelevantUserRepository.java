package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import com.zto.devops.qc.client.model.common.handler.AbstractCurrentHandlerChangedEvent;
import com.zto.devops.qc.client.model.dto.RelevantUserEntityDO;
import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import com.zto.devops.qc.client.model.issue.event.RelevantUserAddedEvent;
import com.zto.devops.qc.client.model.issue.event.RelevantUserRemovedSimpleEvent;
import com.zto.devops.qc.client.model.parameter.TaskBaseParameter;
import com.zto.devops.qc.client.model.parameter.TaskResultParameter;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/4/9
 * @Version 1.0
 */
public interface IRelevantUserRepository {

    List<TaskResultParameter> queryMyObj(TaskBaseParameter taskBaseParameter);

    List<RelevantUserEntityDO> queryMyTaskTotal(Long userId, List<String> types, List<String> issueStatuses, String productCode);

    Long countUserTypeIssueCount(String userId, List<String> status);

    void ccIssue(RelevantUserAddedEvent event);

    void unCCIssueCommand(RelevantUserRemovedSimpleEvent event);

    List<RelevantUserVO> findByBusinessCode(String code);

    List<TaskResultParameter> queryMyIssue(TaskBaseParameter taskBaseParameter);

    void saveByEvent(String businessCode, User operator, DomainEnum domainEnum, String actionCode, RelevantUserTypeEnum relevantUserTypeEnum);

    void save(RelevantUserEntityDO entityDO);

    void updateDelete(String businessCode, User operator, DomainEnum domainEnum);

    void updateByEvent(AbstractCurrentHandlerChangedEvent event);

    /**
     * 根据缺陷code，查询抄送人列表
     *
     * @param issueCodeList 缺陷code
     * @return {@link  RelevantUserVO}
     */
    List<RelevantUserVO> queryCCByIssueCodeList(List<String> issueCodeList);

    RelevantUserEntityDO findRelevantUser(RelevantUserTypeEnum type, String businessCode, String userId);

    void updateAccessTagByCode(String code, String accessTag);
}
