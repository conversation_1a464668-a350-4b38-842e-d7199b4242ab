package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.fsm.Event;
import com.zto.devops.framework.common.fsm.State;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.constants.HandlerTypeEnum;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.qc.client.enums.rpc.ProductRoleEnum;
import com.zto.devops.qc.client.model.dto.IssueEntityDO;
import com.zto.devops.qc.client.model.dto.StatisticsVersionIssueEntityDO;
import com.zto.devops.qc.client.model.dto.TransitionNodeEntityDO;
import com.zto.devops.qc.client.model.issue.command.ReopenIssueCommand;
import com.zto.devops.qc.client.model.issue.command.StartFixIssueCommand;
import com.zto.devops.qc.client.model.issue.command.ValidatedAccessClosedCommand;
import com.zto.devops.qc.client.model.issue.entity.CurrentHandlerVO;
import com.zto.devops.qc.client.model.issue.event.IssueReopenEvent;
import com.zto.devops.qc.client.model.issue.event.IssueStartFixedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueValidatedAccessClosedEvent;
import com.zto.devops.qc.client.service.issue.model.FindStatisticsIssueReq;
import com.zto.devops.qc.client.service.issue.model.StatisticsIssueResp;
import com.zto.devops.qc.domain.converter.IssueAddedEventConverter;
import com.zto.devops.qc.domain.converter.RelevantUserConverter;
import com.zto.devops.qc.domain.gateway.mq.IssueMqSender;
import com.zto.devops.qc.domain.gateway.repository.*;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.model.Issue;
import com.zto.devops.qc.domain.statemachine.IssueContext;
import com.zto.devops.qc.domain.statemachine.IssueStateMachineUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IssueDomainService extends BaseDomainService {
    @Autowired
    private IssueAddedEventConverter issueAddedEventConverter;
    @Autowired
    private RelevantUserConverter relevantUserConverter;
    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;
    @Autowired
    private IIssueRepository issueRepository;
    @Autowired
    private IProductRpcService iProductRpcService;
    @Autowired
    private IssueStorageService issueStorageService;
    @Autowired
    private IRelevantUserRepository iRelevantUserRepository;
    @Autowired
    private TransitionNodeRepository transitionNodeRepository;
    @Autowired
    private StatisticsVersionIssueRepository statisticsVersionIssueRepository;

    @Autowired
    private IssueMqSender issueMqSender;

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private IssueCommandDomainService issueCommandDomainService;

    public void validatedAccessClosed(ValidatedAccessClosedCommand command) {
        Issue issue = issueRepository.loadFormDb(command.getAggregateId());
        IssueContext context =  getIssueContext(command.getTransactor().getUserId(), command.getPermissions(), issue);
        IssueStatus status = issue.getStatus();
        List<Event> events = IssueStateMachineUtil.getInstance().readyEvent(status.toState(), context);
        if (!events.contains(IssueEvent.TEST_PASS_CLOSE.toEvent())) {
            throw new ServiceException("当前用户没有权限操作！");
        }
        State state = getState(status.toState(), IssueEvent.TEST_PASS_CLOSE.toEvent(), context);

        IssueValidatedAccessClosedEvent event = new IssueValidatedAccessClosedEvent();
        event.setCode(command.getAggregateId());
        event.setAggregateId(command.getAggregateId());
        event.setTransactor(command.getTransactor());
        event.setStatus(IssueStatus.valueOf(state.getCode()));
        event.setCloseTime(new Date());
        event.setFixVersion(command.getFixVersion());
        event.setFixVersion(command.getFixVersion());
        event.setFixVersionCode(command.getFixVersionCode());
        event.setOccurred(new Date());
        event.setContent(command.getContent());
        event.setCurStatus(status);
        event.setNextStatus(IssueStatus.valueOf(state.getCode()));
        event.setTransitionNodeCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        Set<CurrentHandlerVO> currentHandler = new HashSet<>();
        currentHandler.add(relevantUserConverter.convertCurrentHandlerVO(issue.getHandler()));
        event.setDeleteHandler(currentHandler);
        //测试通过之后，把当前处理人置为false，没有新的当前处理人了。
        event.setTransactor(command.getTransactor());
        issueStorageService.updateIssue(event);
        issueCommandDomainService.handleCurrentChangeEvent(event);
        issueCommandDomainService.handleMyOperatedEvent(event.getBusinessCode(), event.getOperator(), event.domain(), event.actionCode(), RelevantUserTypeEnum.HANDLED_USER);
        issueCommandDomainService.handleTransitionNodeAddEvent(event);
        issueMqSender.handleIssueValidatedAccessClosedEvent(event);
        apply(event);
    }

    public void startFixIssue(StartFixIssueCommand command) {
        Issue issue = issueRepository.loadFormDb(command.getAggregateId());
        IssueStatus status = issue.getStatus();

        IssueContext context = getIssueContext(command.getTransactor().getUserId(), command.getPermissions(), issue);
        List<Event> events = IssueStateMachineUtil.getInstance()
                .readyEvent(status.toState(),context
                        );

        if (!events.contains(IssueEvent.FIX.toEvent())) {
            throw new ServiceException("当前用户没有权限操作！");
        }
        State state = getState(status.toState(), IssueEvent.FIX.toEvent(), context);
        //更新 issue 当前处理人...
        IssueStartFixedEvent event = new IssueStartFixedEvent();
        event.setAggregateId(command.getAggregateId());
        event.setCode(command.getAggregateId());
        event.setTransactor(command.getTransactor());
        event.setStatus(IssueStatus.valueOf(state.getCode()));
        event.setStartFixTime(new Date());
        event.setOccurred(new Date());
        event.setContent(command.getContent());
        event.setCurStatus(status);
        event.setNextStatus(IssueStatus.valueOf(state.getCode()));
        event.setTransitionNodeCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        issueStorageService.updateIssue(event);
        // 修复时，当前处理人不变动，这里不修改当前处理人数据。
        issueCommandDomainService.handleTransitionNodeAddEvent(event);
        issueMqSender.handleIssueStartFixedEvent(event);
        apply(event);
    }

    public void reopenIssue(ReopenIssueCommand command) {
        Issue issue = issueRepository.loadFormDb(command.getAggregateId());
        IssueStatus status = issue.getStatus();
        IssueContext context = getIssueContext(command.getTransactor().getUserId(), command.getPermissions(), issue);
        List<Event> events = IssueStateMachineUtil.getInstance().readyEvent(status.toState(), context);
        if (!events.contains(IssueEvent.REOPEN.toEvent())) {
            throw new ServiceException("当前用户没有权限操作！");
        }
        State state = getState(status.toState(), IssueEvent.REOPEN.toEvent(), context);

        IssueReopenEvent event = new IssueReopenEvent();
        event.setIsValid(Boolean.TRUE);
        event.setTransactor(command.getTransactor());
        event.setCode(command.getAggregateId());
        event.setAggregateId(command.getAggregateId());
        event.setStatus(IssueStatus.valueOf(state.getCode()));
        event.setReopenTime(new Date());
        event.setContent(command.getContent());
        event.setOccurred(new Date());
        event.setCurStatus(status);
        event.setHandler(issue.getDeveloper());
        event.setNextStatus(IssueStatus.valueOf(state.getCode()));
        event.setTransitionNodeCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        // 缺陷重新打开，把当前聚合的开发人员的人，作为当前处理人
        /*event.setLastHandler(CurrentHandlerVO
                .buildSet(CurrentHandlerVO.CurrentHandlerVOBuilder.create().withBusinessCode(command.getAggregateId())
                        .withCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE)).withDomain(this.currentDomain().name())
                        .withType(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name()).withUserId(this.developer.getUserId())
                        .withUserName(this.developer.getUserName()).build()));*/

        event.setLastHandler(CurrentHandlerVO.buildSet(CurrentHandlerVO.builder()
                .businessCode(command.getAggregateId()).code(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE)).action(event.getStatus().name())
                .domain(DomainEnum.ISSUE.name()).type(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name())
                .userId(String.valueOf(issue.getDeveloper().getUserId())).userName(issue.getDeveloper().getUserName()).handleType(HandlerTypeEnum.USER.name())
                .build()));

        event.setTransactor(command.getTransactor());
        issueStorageService.updateIssue(event);
        event.setStatus(IssueStatus.WAIT_FIX);
        issueCommandDomainService.handleCurrentChangeEvent(event);
        issueCommandDomainService.handleMyOperatedEvent(event.getBusinessCode(), event.getOperator(), event.domain(), event.actionCode(), RelevantUserTypeEnum.HANDLED_USER);
        issueCommandDomainService.handleTransitionNodeAddEvent(event);
        issueMqSender.handleIssueReopenEvent(event);
        apply(event);

    }

    private State getState(State states, Event event, IssueContext context) {
        try {
            return IssueStateMachineUtil.getInstance().fireEvent(states, event, context);
        } catch (Exception e) {
            throw new ServiceException("请刷新数据");
        }
    }

    private IssueContext getIssueContext(Long userId, List<String> permissions, Issue issue) {
        List<ProductRoleEnum> roles = new ArrayList<>();
        if (issue.getProduct() != null) {
            roles = iProductRpcService.getProductRole(issue.getProduct().getCode(), userId);
        }
        return IssueContext.convert(issue, userId, permissions, roles);
    }

    public StatisticsIssueResp getStatisticsIssue(FindStatisticsIssueReq req) {
        StatisticsIssueResp result = new StatisticsIssueResp();
        List<String> productCode = null;
        if (StringUtil.isNotBlank(req.getProductCode())) {
            productCode = Arrays.asList(req.getProductCode());
            result.setProductCode(req.getProductCode());
        }
        List<StatisticsVersionIssueEntityDO> list = statisticsVersionIssueRepository.selectByActualPublishDateAndProductCode(req.getStartDate(), req.getEndDate(), productCode);
        Long produceIssueNum = getProduceIssueNum(req.getStartDate(), req.getEndDate(), productCode);
        result.setProduceIssueNum(produceIssueNum);
        if (CollectionUtil.isEmpty(list)) {
            return result;
        }
        result.setUrgencyRepairTime(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getUrgencyRepairTime).sum());
        result.setHighRepairTime(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getHighRepairTime).sum());
        result.setMiddleRepairTime(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getMiddleRepairTime).sum());

        result.setUrgencyNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getUrgencyNum).sum());
        result.setHighNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getHighNum).sum());
        result.setMiddleNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getMiddleNum).sum());
        result.setLowNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getLowNum).sum());

        result.setUrgencyPunctualNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getUrgencyPunctualNum).sum());
        result.setHighPunctualNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getHighPunctualNum).sum());
        result.setMiddlePunctualNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getMiddlePunctualNum).sum());

        result.setUrgencyReopenNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getUrgencyReopenNum).sum());
        result.setHighReopenNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getHighReopenNum).sum());
        result.setMiddleReopenNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getMiddleReopenNum).sum());
        result.setLowReopenNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getLowReopenNum).sum());
        result.setUrgencyEffectiveNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getUrgencyEffectiveNum).sum());
        result.setHighEffectiveNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getHighEffectiveNum).sum());
        result.setMiddleEffectiveNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getMiddleEffectiveNum).sum());
        result.setLowEffectiveNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getLowEffectiveNum).sum());
        result.setRequirementIssueNum(list.stream().mapToLong(StatisticsVersionIssueEntityDO::getRequirementIssueNum).sum());
        return result;
    }

    public Long getProduceIssueNum(Date startDate, Date endDate, List<String> productCode) {
        //发现阶段为『运营阶段』的有效缺陷。
        List<IssueEntityDO> entityList =
                issueRepository.selectByProductCodeAndFindStageAndGmtCreate(productCode, IssueFindStage.OPERATE_STAGE, startDate, endDate);
        if (CollectionUtil.isEmpty(entityList)) {
            return 0L;
        }
        List<String> issueCodes = entityList.stream().map(IssueEntityDO::getCode).collect(Collectors.toList());
        List<TransitionNodeEntityDO> transitionNodeAll = transitionNodeRepository.listIssueTransitionNode(issueCodes);
        Map<String, List<TransitionNodeEntityDO>> transitionNodeAllMap =
                transitionNodeAll.stream().collect(Collectors.groupingBy(TransitionNodeEntityDO::getBusinessCode));
        List<IssueEntityDO> effectiveList = getEffective(entityList, transitionNodeAllMap);
        return Long.valueOf(effectiveList.size());
    }

    /**
     * 获取有效缺陷
     *
     * @param issueEntityList      缺陷列表
     * @param transitionNodeAllMap 缺陷-流转信息
     *                             key：缺陷code。value:流转信息，按时间倒序排列
     */
    private List<IssueEntityDO> getEffective(List<IssueEntityDO> issueEntityList,
                                             Map<String, List<TransitionNodeEntityDO>> transitionNodeAllMap) {
        if (CollectionUtil.isEmpty(issueEntityList)) {
            return Collections.emptyList();
        }
        if (transitionNodeAllMap == null || transitionNodeAllMap.isEmpty()) {
            return issueEntityList;
        }
        // 有效
        List<IssueEntityDO> result = new ArrayList<>();
        for (IssueEntityDO item : issueEntityList) {
            if (item.getStatus() == null || item.getStatus() != IssueStatus.CLOSED) {
                result.add(item);
                continue;
            }
            List<TransitionNodeEntityDO> itemList = transitionNodeAllMap.get(item.getCode());
            if (CollectionUtil.isEmpty(itemList) || itemList.size() < 2) {
                result.add(item);
                continue;
            }
            TransitionNodeEntityDO transitionNode = itemList.get(0);
            if (transitionNode.getCurStatus() == IssueStatus.REJECTED && transitionNode.getNextStatus() == IssueStatus.CLOSED) {
                boolean addFlg = true;
                for (TransitionNodeEntityDO node : itemList) {
                    //关闭原因为非BUG、重复提交  则为无效缺陷
                    boolean invalidFlg = node.getNextStatus() != null
                            && node.getNextStatus() == IssueStatus.REJECTED
                            && node.getReason() != null
                            && (Reason.NO_ISSUE.equals(node.getReason()) || Reason.REPEAT_SUBMIT.equals(node.getReason()));
                    if (invalidFlg) {//无效的
                        addFlg = false;
                        //跳出当前循环，外面循环继续
                        break;
                    }
                }
                if (addFlg) {
                    result.add(item);
                }
            } else {
                result.add(item);
            }
        }
        return result;
    }

}
