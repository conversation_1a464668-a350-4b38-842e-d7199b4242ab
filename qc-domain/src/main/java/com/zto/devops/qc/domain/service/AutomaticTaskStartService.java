package com.zto.devops.qc.domain.service;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.client.model.jenkins.JenkinsBuild;
import com.zto.devops.qc.client.model.jenkins.JenkinsBuildDetail;
import com.zto.devops.qc.client.model.rpc.pipeline.AutomaticTaskCDExecutedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.command.ExecuteCallbackCommand;
import com.zto.devops.qc.domain.gateway.jenkins.IJenkinsService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.ApiTestRepository;
import com.zto.devops.qc.domain.gateway.repository.IAutomaticTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AutomaticTaskStartService {

    @Autowired
    private AutomaticTaskCommandDomainService automaticTaskCommandDomainService;

    @Autowired
    private IAutomaticTaskRepository automaticTaskRepository;

    @Autowired
    private IJenkinsService jenkinsService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ApiTestRepository apiTestRepository;

    @PostConstruct
    protected void handle() {
        ThreadFactory threadFactory = ThreadFactoryBuilder.create()
                .setNamePrefix("AutomaticTaskStartService-thread-")
                .build();
        Executors.newSingleThreadExecutor(threadFactory).execute(() -> {
            handleAutomaticTaskExecutedEvent();
            handleAutomaticTaskCDExecutedEvent();
        });
    }

    private void handleAutomaticTaskExecutedEvent() {
        try {
            log.info("start handle 【AutomaticTaskExecutedEvent】");
            List<AutomaticStatusEnum> statusEnums = Arrays.asList(AutomaticStatusEnum.NOT_STARTED,
                    AutomaticStatusEnum.SUBMITTED, AutomaticStatusEnum.IN_PROGRESS);
            List<AutomaticTaskEntityDO> list =
                    automaticTaskRepository.selectAutomaticTaskByStatusList(null, false, statusEnums);
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            list.stream()
                    .filter(task -> {
                        if (AutomaticTaskTrigModeEnum.SINGLE_API_MANUAL.equals(task.getTrigMode())
                                || AutomaticTaskTrigModeEnum.SINGLE_API_AUTO.equals(task.getTrigMode())) {
                            return false;
                        }
                        int count = apiTestRepository.countApiTestCaseByAutomaticSourceCode(task.getAutomaticSourceCode());
                        return count == 0;
                    })
                    .collect(Collectors.groupingBy(AutomaticTaskEntityDO::getType)).forEach((type, tasks) -> {
                List<String> processed = new ArrayList<>();
                tasks.stream().filter(task -> StringUtils.isNotEmpty(task.getBuildId())).forEach(task -> {
                    processed.add(task.getBuildId());
                    JenkinsBuildDetail buildDetail = jenkinsService.getBuildDetail(type, task.getBuildId());
                    reExecuteCallback(buildDetail, task);
                });
                List<JenkinsBuild> builds = jenkinsService.getBuilds(type);
                if (CollectionUtils.isNotEmpty(builds)) {
                    builds.stream().filter(build -> !processed.contains(build.getNumber().toString()))
                            .forEach(build -> {
                                String buildId = build.getNumber().toString();
                                JenkinsBuildDetail buildDetail = jenkinsService.getBuildDetail(type, buildId);
                                String code = Optional.ofNullable(buildDetail)
                                        .map(JenkinsBuildDetail::getParameters)
                                        .map(o -> o.get("codeid"))
                                        .orElse(null);
                                if (StringUtils.isEmpty(code)) {
                                    return;
                                }
                                tasks.stream().filter(task -> task.getCode().equals(code)).forEach(task -> {
                                    task.setBuildId(buildId);
                                    reExecuteCallback(buildDetail, task);
                                });
                            });
                }
                tasks.stream().filter(task -> StringUtils.isEmpty(task.getBuildId()))
                        .forEach(automaticTaskCommandDomainService::reExecuteAutomaticTask);
            });
        } catch (Exception e) {
            log.error("handle error 【AutomaticTaskExecutedEvent】", e);
        }
    }

    private void reExecuteCallback(JenkinsBuildDetail buildDetail, AutomaticTaskEntityDO task) {
        if (null == buildDetail || buildDetail.getBuilding()) {
            return;
        }
        AutomaticStatusEnum status = null;
        String ossPath = jenkinsService.getOssPath(task.getStartTime(), task.getCode());
        if ("SUCCESS".equals(buildDetail.getResult())) {
            String resultFile = jenkinsService.getResultFile(ossPath, "/callback.json");
            if (StringUtils.isNotEmpty(resultFile)) {
                try {
                    JSONObject json = jenkinsService.getCaseFileContent(resultFile);
                    status = AutomaticStatusEnum.getEnumByName(json.getString("status"));
                } catch (Exception e) {
                    log.error("get oss file error", e);
                }
            }
            if (null == status) {
                status = AutomaticStatusEnum.FAIL;
            }
        } else if ("FAILURE".equals(buildDetail.getResult())) {
            status = AutomaticStatusEnum.ERROR;
        } else {
            status = AutomaticStatusEnum.TERMINATION;
        }
        ExecuteCallbackCommand command = new ExecuteCallbackCommand(task.getCode());
        command.setBuildId(task.getBuildId());
        command.setStatus(status);
        command.setOssPath(ossPath);
        automaticTaskCommandDomainService.callback(command);
    }

    private void handleAutomaticTaskCDExecutedEvent() {
        try {
            log.info("start handle 【AutomaticTaskCDExecutedEvent】");
            Map<Object, Object> map = redisService.hashEntries("AutomaticTaskCDExecutedEvent");
            if (null == map || map.isEmpty()) {
                return;
            }
            for (Map.Entry<Object, Object> entry : map.entrySet()) {
                AutomaticTaskCDExecutedEvent event =
                        JSON.parseObject(entry.getValue().toString()).toJavaObject(AutomaticTaskCDExecutedEvent.class);
                if (null != event) {
                    automaticTaskCommandDomainService.onAutomaticTaskCDExecutedEvent(event);
                }
            }
        } catch (Exception e) {
            log.error("handle error 【AutomaticTaskCDExecutedEvent】", e);
        }
    }
}
