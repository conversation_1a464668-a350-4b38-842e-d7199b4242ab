package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseExecuteRecordEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.XmindCaseEditCommand;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.event.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.*;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface TestcaseRepository {

    ExecuteCaseVO getExecuteCase(String caseCode, String automaticTaskCode);

    List<TestcaseVO> getTestcaseNameListByPath(List<String> path);

    TestcaseVO findCaseOrModuleByCodeQuery(String code);

    List<TestcaseVO> ListCaseByAutomaticCode(String code);

    List<TestcaseVO> findTestcaseByNameQuery(FindTestcaseByNameQuery query);

    List<TestcaseEntityDO> getTestcaseNew(ListTestcaseNewQuery query);

    /**
     * 查询勾选测试用例
     *
     * @param query {@link ListTestcaseExpQuery}
     * @return {@link TestcaseEntityDO}
     */
    List<TestcaseEntityDO> getCheckTestCaseList(ListTestcaseExpQuery query);

    /**
     * 根据条件查询分组
     *
     * @param query
     * @return
     */
    List<TestcaseEntityDO> simpleTestcaseModuleQuery(SimpleTestcaseModuleQuery query);

    /**
     * 获取用例执行次数
     *
     * @param codeList
     * @return
     */
    List<TestcaseExecuteNumVO> selectExecuteNumByTestcaseCodeList(List<String> codeList);

    /**
     * 根据用例code查询信息
     *
     * @param caseCode
     * @return
     */
    TestcaseEntityDO selectTestcaseByCode(String caseCode);

    /**
     * 查询根据用例codeList获取与用例查询信息
     *
     * @param caseCodes
     * @return
     */
    List<TestcaseEntityDO> selectTestcaseByCodeList(List<String> caseCodes);

    /**
     * 根据用例codeList更新entity信息
     *
     * @param codes
     * @param entity
     */
    void updateTestcaseByCodeList(List<String> codes, TestcaseEntityDO entity);

    /**
     * 根据用例code查询版本与产品信息
     *
     * @param caseCodes
     * @return
     */
    TestcaseEntityDO selectProductCodeAndVersionCodeByCaseCode(String caseCodes);

    /**
     * 根据parentCode分组
     *
     * @param caseCodeList 用例code集合
     * @param productCode  产品code
     * @param versionCode  版本code
     * @param type         用例类型
     * @return {@link TestcaseGroupVO}
     */
    List<TestcaseParentInfoVO> selectGroupByCaseCodeList(List<String> caseCodeList,
                                                         String productCode,
                                                         String versionCode,
                                                         String type,
                                                         Boolean setCore);

    TestcaseGroupVO buildSingleModule(String parentCode,
                                      String parentName,
                                      String targetVersionCode,
                                      String productCode,
                                      User operator);

    /**
     * 组装多级分组信息
     *
     * @param parentName        末级分组名
     * @param sourceVersionCode 原版本code
     * @param targetVersionCode 目标版本code
     * @param productCode       产品code
     * @param operator          操作人
     * @return {@link TestcaseGroupVO}
     */
    TestcaseGroupVO buildComplexModule(String parentCode,
                                       String parentName,
                                       String sourceVersionCode,
                                       String targetVersionCode,
                                       String productCode,
                                       User operator);

    TestcaseGroupVO buildSingleModuleForSetCore(String sourceModuleCode,
                                                String parentCode,
                                                String parentPath,
                                                User operator);

    TestcaseGroupVO buildComplexModuleForSetCore(String versionModuleCode,
                                                 String versionModuleName,
                                                 String parentCode,
                                                 String parentName,
                                                 String sourceVersionCode,
                                                 String productCode,
                                                 User operator);

    /**
     * 获取目标分组已存在用例名
     *
     * @param productCode
     * @param targetParentCode
     * @param versionCode
     * @return
     */
    Set<String> getExistCaseNameList(String productCode, String targetParentCode, String versionCode, Boolean setCore);

    /**
     * 过滤掉目标组里存在的同名的用例
     *
     * @param caseCodes
     * @param existNames
     * @return
     */
    List<String> filterCodeByName(List<String> caseCodes, Set<String> existNames, String action, Boolean setCore,
                                  String productCode, String sourceVersionCode);

    /**
     * 根据过滤得到code开始更新库
     *
     * @param entityDO
     * @param filteredCodes
     */
    void updateTestcaseByFilteredCodes(TestcaseEntityDO entityDO, List<String> filteredCodes);

    /**
     * 获取目标分组路径
     *
     * @param targetParentCode
     * @return
     */
    String getTargetPath(String targetParentCode, Boolean setCore, TestcaseTypeEnum typeEnum);

    /**
     * 根据父code与版本code查询分组
     *
     * @param parentCode
     * @param versionCode
     * @param testcaseAttributeEnum
     * @param setCore
     * @return
     */
    TestcaseEntityDO selectModuleByParentCodeAndVersionCode(String parentCode, String versionCode, TestcaseAttributeEnum testcaseAttributeEnum, Boolean setCore);

    /**
     * 获取新分组下的用例名称
     *
     * @param parentCode
     * @param testcaseAttributeEnum
     * @param setCore
     * @param productCode
     * @param versionCode
     * @return
     */
    List<TestcaseEntityDO> selectNewModuleCaseName(String parentCode, TestcaseAttributeEnum testcaseAttributeEnum, Boolean setCore, String productCode, String versionCode);

    /**
     * 获取用例信息TESTCASE根据codeList
     *
     * @param filteredCodes
     * @return
     */
    List<TestcaseEntityDO> selectTestcaseByCodesList(List<String> filteredCodes);

    /**
     * 批量修改责任人
     *
     * @param codes
     * @param dutyUser
     * @param operator
     */
    void batchUpdateDutyUser(List<String> codes, User dutyUser, User operator);

    /**
     * 批量修改用例等级
     *
     * @param codes
     * @param grade
     * @param operator
     */
    void doBatchUpdateCaseGrade(List<String> codes, TestcasePriorityEnum grade, User operator);

    /**
     * 根据用例codeList,查询手工用例名（去重）
     *
     * @param caseCodeList
     * @return
     */
    List<String> selectDistinctCaseNameByCodeList(List<String> caseCodeList, Boolean setCore);

    /**
     * 获取目标分组
     *
     * @param targetParentCode
     * @return
     */
    TestcaseEntityDO findTargetModule(String targetParentCode);

    /**
     * 获取目标分组用例
     *
     * @param targetParentCode
     * @return
     */
    List<TestcaseEntityDO> findTargetModuleCase(String targetParentCode, String productCode);

    /**
     * 变更测试计划用例执行人
     *
     * @param event
     */
    void changePlanCaseExecutor(PlanCaseExecutorChangedEvent event);

    /**
     * 变更测试用例责任人
     *
     * @param event
     */
    void changeTestcaseDutyUser(TestcaseDutyUserChangedEvent event);

    /**
     * 匹配目标分组
     *
     * @param event
     * @return
     */
    TestcaseEntityDO findTargetModule(TestcaseChangeVersionEvent event);

    /**
     * 变更版本信息
     *
     * @param event
     */
    void changeVersion(TestcaseChangeVersionEvent event, TestcaseGroupVO vo);


    /**
     * 查询用例创建人或责任人数量
     *
     * @param list
     * @param userId
     * @return
     */
    Integer selectCountCreatorOrDutyUser(List<String> list, Long userId);

    /**
     * 查询用例状态数量
     *
     * @param list
     * @return
     */
    CheckTestcaseStatusVO selectCountTestcaseStatus(List<String> list);


    /**
     * 加载用例数据
     *
     * @param caseCode 用例code
     * @return {@link TestcaseEntityDO}
     */
    TestcaseEntityDO loadFormDb(String caseCode);

    List<SimpleTestcaseVO> selectList(ListTestcaseCodeQuery query);

    /**
     * 测试用例关联Domain
     *
     * @param event
     */
    void associateTestcaseDomain(TestcaseDomainAssociatedEvent event);

    /**
     * 查分组-  自动化解析来源code为空
     *
     * @param parentCode  分组code
     * @param versionCode 版本code
     * @param setCore     是否核心用例
     * @return {@link TestcaseEntityDO}
     */
    TestcaseEntityDO selectModule(String parentCode, String versionCode, Boolean setCore);

    /**
     * 统计分组下，同名用例数
     *
     * @param productCode  产品code
     * @param versionCode  版本code
     * @param name         用例名
     * @param parentCode   用例分组code
     * @param testcaseType 用例类型
     * @param setCore      是否核心用例
     * @return 重名数
     */
    int countCaseByCaseName(String productCode,
                            String versionCode,
                            String name,
                            String parentCode,
                            TestcaseTypeEnum testcaseType,
                            Boolean setCore);

    /**
     * 更新分组路径
     *
     * @param entityDO
     */
    void updatePath(TestcaseEntityDO entityDO);

    /**
     * 查分组下用例code结合
     *
     * @param pageTestcaseQuery
     * @return
     */
    List<String> selectModuleCodeList(PageTestcaseQuery pageTestcaseQuery);

    void batchInsertCase(List<TestcaseEntityDO> list);

    List<ListTestcaseModuleVO> selectModuleList(ListTestcaseModuleQuery query);

    List<SimpleTestcaseVO> selectAllTestCasePath(PageTestcaseQuery pageTestcaseQuery);

    List<ListTestcaseVO> selectTestCaseModuleList(PageTestcaseQuery pageTestcaseQuery);

    Integer selectTestCaseCount(TestcaseQuery query);

    List<TestcaseEntityDO> selectTestCase(TestcaseQuery query);

    Long countNoGroupTestcase(PageTestcaseQuery query);

    Integer countByNodeTypeAndCodeList(AutomaticNodeTypeEnum moduleController, List<String> codeList);

    List<TestHeartCaseVO> selectAllHeartCase(FindHeartCaseByUserQuery query);

    List<ListTestcaseVO> pageTestcase(PageTestcaseQuery query);

    List<TagVO> getTagsByCodeList(List<String> codeList);

    void relatedCaseExecutionStatus(List<ListTestcaseVO> testcaseList, PageTestcaseQuery query);

    List<String> getParentCodeList(List<String> codeList);

    TestcaseEntityDO getTestcaseVO(PageXmindDetailQuery query);

    TestcaseVO getTestcaseVOByCodeQuery(FindTestcaseEntityByCodeQuery query);

    void updateByPrimaryKeySelective(TestcaseEntityDO entity);

    void updateByCodeList(TestcaseEntityDO entity, List<String> caseCodeList);

    String selectTestcaseModulePath(FindTestcaseModulePathQuery query);

    int selectModuleCountByName(XmindCaseEditCommand command, String name);

    List<ListTestcaseVO> selectTestCaseModule(TestcaseQuery query);

    List<TestcaseVO> listCaseByAutomaticCode(String automaticSourceCode);

    void addTestcase(AddTestcaseEvent event);

    void editTestcase(EditTestcaseEvent event);

    String selectAllCodeByParentCode(String code);

    TestcaseEntityDO selectTestCaseEntity(TestcaseMovedEvent event, String parentCode);

    int selectTestCaseCount(TestcaseMovedEvent event, String parentCode);

    List<TestcaseExecuteRecordVO> selectRecordByCode(String code, AutomaticTaskTrigModeEnum type);

    int findTestcaseModulePathQuery(FindTestcaseModulePathQuery query);

    void updateByAutomaticSourceCode(MoveModuleEvent event, String newPath);

    void replacePath(String oldPath, String newPath);

    void replaceTestcaseModulePath(String oldPath, String newPath);

    TestcaseEntityDO findModulePathByParentCode(String parentCode);

    void updateTestcaseModulePath(String automaticSourceCode, String newPath, User user);

    List<String> selectSortedCodeList(String productCode, String parentCode, TestcaseTypeEnum auto);

    void deleteOldSorted(List<String> sortedList);

    void insertNewSorted(List<String> sortedList);

    String selectMaxLengthPathByPath(String oldPrefixPath);

    int countModuleByTypeAndNameAndParentCode(String productCode, TestcaseTypeEnum type, String name, String parentCode);

    void updateXmindDetail(XmindCaseEditEvent event, TestCaseTagNameEnum type);

    void updateXmindStepExcept(XmindCaseEditEvent event, TestCaseTagNameEnum type);

    void deleteXmindSteps(XmindCaseEditEvent event);

    List<TestcaseEntityDO> selectAutomaticCaseByCodeList(List<String> testcaseCodeList);

    List<TestcaseEntityDO> selectByCodeList(List<String> codeList);

    List<String> selectOperateCaseCodeByTaskCode(String code);

    List<TestcaseEntityDO> selectByAutomaticSourceCode(String automaticSourceCode, Boolean enable);

    void insertBatchExecuteRecord(TestcaseExecuteRecordEntityDO executeRecordEntityDO, List<String> list);

    void updateByAutomaticTaskCode(String code);

    void deleteTestcase(DeleteTestcaseEvent event);

    void changeTestcaseStatus(TestcaseStatusChangedEvent event);

    List<SimpleTestCase> selectSimpleTestCase(PageSimpleTestCaseQuery query);

    List<ListExecuteCaseVO> selectByAutomaticTaskCode(ListExecuteCaseQuery query);

    List<ListExecuteCaseVO> selectWithListExecuteCaseVO(List<String> list);

    void saveBatch(List<TestcaseEntityDO> list);

    List<TestcaseVO> selectSimpleTestcaseQuery(SimpleTestcaseQuery query);

    TestcaseVO handle(FindTestcaseEntityByCodeQuery query);

    void editTestcaseTitle(EditTestcaseTitleEvent event);

    List<RelatedCasePlanVO> selectRelatedPlanList(List<String> codeList);

    void releaseTestcaseRelation(TestcaseRelationReleasedEvent event);

    List<TestcaseEntityDO> queryChildCaseAndModuleByPath(String code, String path);

    List<TestcaseEntityDO> queryChildCaseAndModuleByTestcaseModulePath(String code, String path);

    List<TestcaseEntityDO> selectByStatusAndCodeList(TestcaseStatusEnum status, List<String> caseCodes);

    Integer findTestcaseModulePath(FindTestcaseModulePathQuery query);

    void moveModule(MoveModuleEvent event);

    List<TestcaseEntityDO> findListByCodeIn(List<String> codeList);

    List<TestcaseEntityDO> findDisableListByCodeIn(List<String> codeList);

    void insertTestcaseExecuteRecord(TestcaseExecuteRecordEntityDO entityDO);

    void addTestCaseSteps(XmindCaseAddEvent event);

    List<ListExecuteCaseVO> selectByVersionCode(String versionCode);

    List<ListExecuteCaseVO> selectAutoByPlanCode(String planCode, String testStage);

    List<ListExecuteCaseVO> selectManualByPlanCode(String planCode, String testStage);

    void updateStatusFromLinkMap(String automaticSourceCode, String linkMapCode, TestcaseStatusEnum status);

    TestcaseEntityDO selectModuleInfoByName(TestcaseEntityDO entityDO);

    void updateStatusFromSceneInfo(String automaticSourceCode, TestcaseStatusEnum status);

    /**
     * 根据用例code，查询parentCode集合
     *
     * @param caseCodes 用例code集合
     * @return parentCodeList
     */
    List<TestCasePathVO> getParentCodeListByCaseCodeList(List<String> caseCodes);

    /**
     * 统计分组下未删除用例数
     *
     * @param productCode
     * @param versionCode
     * @param parentCodeList
     * @return
     */
    List<CountCaseVO> countByParentCodeList(String productCode, String versionCode, List<String> parentCodeList);

    /**
     * 根据版本名称，查询用例模块
     *
     * @param versionName 版本名称
     * @param productCode 产品code
     * @return {@link TestcaseEntityDO}
     */
    List<TestcaseEntityDO> selectModuleByVersionName(String versionName, String productCode);

    List<String> selectNoGroupCaseList(List<String> caseCodeList);

    /**
     * 根据版本，分组统计未分组用例
     *
     * @param query {@link PageTestcaseQuery}
     * @return
     */
    List<CountNoGroupCaseVO> countNoGroupTestcaseByVersionCodeList(PageTestcaseQuery query);

    List<TestcaseEntityDO> selectTestCaseByAutoSourceCodes(List<String> autoSourceCodes);

    /**
     * 根据分组名称，查询顶级分组code
     *
     * @param moduleName
     * @return
     */
    String selectTopModuleCodeByName(String moduleName, String productCode);

    /**
     * 根据场景分组，更新自动化登记库name
     *
     * @param testcaseCode 分组code
     * @param moduleName   分组名
     * @param operator     操作人
     */
    void updateNameBySceneIndex(String testcaseCode, String moduleName, User operator);

    /**
     * 仅删除该分组及其下级分组
     *
     * @param testcaseCode 分组code
     * @param operator     操作人
     */
    void deleteModule(String testcaseCode, User operator);

    List<TestcaseEntityDO> selectSameNameModuleByParentCode(String parentCode, String moduleName);

    /**
     * 根据产品code，查询用例工厂默认分组
     *
     * @param productCode      产品code
     * @param topModuleName    顶级分组名
     * @param secondModuleName 二级分组名
     * @return 二级分组
     */
    TestcaseEntityDO selectSceneDefaultModule(String productCode, String topModuleName, String secondModuleName);

    /**
     * 根据登记库更新用例状态
     *
     * @param automaticSourceCodes
     * @param enable
     * @param transactor
     */
    void updateTestcaseEnableByAutomaticSourceCode(List<String> automaticSourceCodes, Boolean enable, User transactor, TestcaseStatusEnum status);

    List<TestcaseEntityDO> queryCaseListByAutomaticCodes(List<String> automaticSourceCodes);

    List<String> queryDisableCaseCodeByDurationDate(Date durationDate);

    void batchDeleteCaseByCode(List<String> list);
}
