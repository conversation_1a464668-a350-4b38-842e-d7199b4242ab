package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.model.dto.TestcaseRelationEntityDO;
import com.zto.devops.qc.client.model.issue.entity.CaseVO;
import com.zto.devops.qc.client.model.issue.entity.RelationTestcaseListVO;
import com.zto.devops.qc.client.model.issue.event.TestcaseRelationAddEvent;
import com.zto.devops.qc.client.model.issue.query.RelationTestcaseListQuery;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseByBusinessCodeVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseIssueVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseRelationVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListRelationIssueQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListRelationRequirementQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/3/17
 * @Version 1.0
 */
public interface ITestcaseRelationRepository {

    List<TestcaseByBusinessCodeVO> selectTestCaseByCodeList(List<String> codeList, DomainEnum domain);

    List<CaseVO> getCaseNameList(String code);

    List<RelationTestcaseListVO> getRelationTestcaseList(RelationTestcaseListQuery query);

    void addTestCase(TestcaseRelationAddEvent event);

    void addTestcaseRelationList(List<TestcaseRelationVO> list, String testcaseCode, BaseEvent event);

    List<TestcaseIssueVO> selectIssueByTestcaseCode(ListRelationIssueQuery query);

    List<TestcaseRelationEntityDO> selectTestcaseRelationEntities(ListRelationRequirementQuery query);
}
