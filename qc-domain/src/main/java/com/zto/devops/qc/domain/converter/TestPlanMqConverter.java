package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.testPlan.entity.TestPlanDataResp;
import com.zto.devops.qc.client.model.testPlan.event.AddSafetyTestPlanEvent;
import com.zto.devops.qc.client.model.testPlan.event.EditSafetyTestPlanEvent;
import com.zto.devops.qc.client.model.testmanager.plan.entity.SimpleTmTestPlanVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface TestPlanMqConverter {
    TestPlanDataResp convert(AddSafetyTestPlanEvent event);

    TestPlanDataResp convert(EditSafetyTestPlanEvent event);

    TestPlanDataResp convert(SimpleTmTestPlanVO planVO);
}
