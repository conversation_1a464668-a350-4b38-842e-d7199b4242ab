package com.zto.devops.qc.domain.service;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.query.FindAttachmentByCodeQuery;
import com.zto.devops.qc.client.model.issue.query.ListAttachmentsByBusinessCodeQuery;
import com.zto.devops.qc.domain.gateway.repository.AttachmentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AttachmentQueryDomainService {

    @Autowired
    private AttachmentRepository attachmentRepository;


    public List<AttachmentVO> query(ListAttachmentsByBusinessCodeQuery query) {
        return attachmentRepository.query(query);
    }

    public AttachmentVO query(FindAttachmentByCodeQuery query) {
        return attachmentRepository.query(query);
    }
}
