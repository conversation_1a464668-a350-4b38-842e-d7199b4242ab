package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.model.dto.TmTestPlanCaseEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.PlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.SimpleTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindSortedPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageTestcaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.XmindFilterQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.DeletePlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseCodeQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseModuleQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.PlanCaseResultCountQuery;

import java.util.List;

public interface ITmTestPlanCaseRepository {

    /**
     * 根据计划code与测试阶段查询用例
     *
     * @param testPlanCode
     * @param testStage
     * @return
     */
    List<TmTestPlanCaseEntityDO> selectTestPlanCaseByPlanCodeAndTestStage(String testPlanCode, List<TestPlanStageEnum> testStage);

    /**
     * 批量更新测试计划中的用例
     *
     * @param entityDOList
     */
    void batchSave(List<TmTestPlanCaseEntityDO> entityDOList);

    /**
     * 根据用例codeList查询测试计划中的用例
     *
     * @param codes
     * @return
     */
    List<TmTestPlanCaseEntityDO> selectTmTestPlanCaseEntityDOListByCaseCodeList(List<String> codes);

    /**
     * 根据测试计划code，阶段，用例类型，查询用例列表
     *
     * @param query {@link PageTestcaseQuery}
     * @return {@link TmTestPlanCaseEntityDO}
     */
    List<TmTestPlanCaseEntityDO> selectListByStageAndType(PageTestcaseQuery query);

    List<String> selectOperateCaseCodeByCaseCode(String caseCode);

    List<TmTestPlanCaseEntityDO> getTestPlanCaseByCode(String code);

    void updateByPrimaryKeySelective(TmTestPlanCaseEntityDO tmTestPlanCaseEntityDO);

    void delete(TmTestPlanCaseEntityDO tmTestPlanCaseEntityDO);

    List<DeletePlanCaseVO> selectPlanCaseByCodeList(List<String> codeList);

    void updateByIdList(TmTestPlanCaseEntityDO tmTestPlanCaseEntityDO, List<Long> idList);

    void deleteByIdList(List<Long> idList);

    List<TmTestPlanCaseEntityDO> getTestPlanCaseList(XmindFilterQuery filterQuery, List<String> codeList);

    List<PlanCaseVO> selectPlanCase(FindPlanCaseQuery query);

    List<TmTestPlanCaseEntityDO> getDistinctListByPlanCode(String planCode);

    List<TestPlanCaseStatusEnum> findStatusList(PlanCaseResultCountQuery query);

    List<TestPlanCaseVO> selectPlanCaseModuleList(ListPlanCaseModuleQuery listPlanCaseModuleQuery);

    List<TestPlanCaseVO> selectPlanCaseAutomaticModuleList(ListPlanCaseModuleQuery listPlanCaseModuleQuery);

    List<SimpleTestcaseVO> selectTestcaseCodeList(ListPlanCaseCodeQuery query);

    List<TestPlanCaseVO> getTestPlanCaseList(ListPlanCaseQuery query);

    List<PlanCaseVO> selectSimplePlanCase(FindSortedPlanCaseQuery query);

    List<TestPlanCaseVO> selectAllPlanCasePath(ListPlanCaseModuleQuery query);

    void deleteByCaseCodeList(List<String> caseCodes, String planCode, TestPlanStageEnum stage);

    List<TmTestPlanCaseEntityDO> selectByPlanCodeAndTestStageAndCodeList(String planCode,
                                                                         TestPlanStageEnum stage,
                                                                         List<String> caseCodes);

    void updateByPlanCodeAndTestStageAndCodeList(String planCode,
                                                 TestPlanStageEnum stage,
                                                 List<String> caseCodes,
                                                 TmTestPlanCaseEntityDO entityDO);

    List<String> selectVersionCodeByPlanCode(String planCode);

    /**
     * 校验当前阶段用例是否未开始、是否未关联用例
     * @param code 计划code
     * @param stageList 阶段集合
     * @return
     */
    Boolean verifyCaseNotStartedByPlanCodeAndTestStage(String code, List<TestPlanStageEnum> stageList);

    List<TestPlanCaseVO> queryDeletedPlanCase(ListPlanCaseQuery listPlanCaseQuery);

    List<TmTestPlanCaseEntityDO> queryPlanCaseByCaseCode(List<String> caseCodeList);

    /**
     * 根据计划code
     *
     * @param planCode
     * @return
     */
    List<TmTestPlanCaseEntityDO> selectByPlanCode(String planCode);
}
