package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.dto.ApiGlobalConfigurationEntityDO;

import java.util.List;

public interface ApiGlobalConfigurationRepository {

    void batchInsertApiGlobalConfiguration(List<ApiGlobalConfigurationEntityDO> list);

    int deleteApiGlobalConfigurationByProductCode(String productCode, User user);

    List<ApiGlobalConfigurationEntityDO> selectApiGlobalConfigurationByProductCode(String productCode);

    List<ApiGlobalConfigurationEntityDO> queryEnabledConfigByProductCode(String productCode);

    List<String> queryApiConfigAllApiCode(String productCode);

    void updateApiConfigMainApiCode(String apiCode, String mainApiCode);
}
