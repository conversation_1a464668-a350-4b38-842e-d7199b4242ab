package com.zto.devops.qc.domain.service;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.dubbo.common.Constants;
import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Message;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.zto.devops.framework.client.entity.UserInfo;
import com.zto.devops.framework.client.entity.action.ActionLogDO;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.domain.gateway.repository.IEventLogRepository;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.testPlan.TestPlanTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.DebugTaskRespTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.channel.TestEventEnums;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStrategyEnum;
import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.rpc.pipeline.AutomaticTaskCDExecutedEvent;
import com.zto.devops.qc.client.model.rpc.pipeline.NamespaceResp;
import com.zto.devops.qc.client.model.rpc.product.SimpleQueryVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.ExecuteAutomaticTaskCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.ExecuteCallbackCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.TerminateAutomaticTaskCommand;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticNode;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTask;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteCaseCallbackVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskExecutedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskTerminatedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.ExecuteCallbackCDEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.ExecuteCallbackEvent;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListAutomaticTaskCaseQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanCaseVO;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.AutomaticSchedulerExecutionCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticPreExecutionUpdateEvent;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticSchedulerExecutionEvent;
import com.zto.devops.qc.client.service.testmanager.apitest.model.ListExecuteDetailReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExecuteAutomaticTaskReq;
import com.zto.devops.qc.domain.converter.AutomaticTaskDomainConverter;
import com.zto.devops.qc.domain.converter.AutomaticTaskEventConverter;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.debug.IQcDebugService;
import com.zto.devops.qc.domain.gateway.jenkins.IJenkinsService;
import com.zto.devops.qc.domain.gateway.message.QcRobotMessageService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.*;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AutomaticTaskCommandDomainService extends BaseDomainService {

    @Autowired
    private ApiTestRepository apiTestRepository;

    @Autowired
    private ApiTestCommandDomainService apiTestCommandDomainService;

    @Autowired
    private IEventLogRepository eventLogManager;

    @Autowired
    private AutomaticSourceRecordRepository automaticSourceRecordRepository;

    @Autowired
    private IAutomaticTaskRepository automaticTaskRepository;

    @Autowired
    private TestcaseRepository testcaseRepository;

    @Autowired
    private AutomaticSchedulerRepository automaticSchedulerRepository;

    @Autowired
    private AutomaticTaskDomainConverter automaticTaskDomainConverter;

    @Autowired
    private AutomaticSchedulerMsgCommandDomainService automaticSchedulerMsgCommandDomainService;

    @Autowired
    private AutomaticTaskEventConverter automaticTaskEventConverter;

    @Autowired
    private IJenkinsService jenkinsService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private CoverageDomainService coverageDomainService;

    @Autowired
    private TmTestPlanQueryDomainService tmTestPlanQueryDomainService;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private ITmTestPlanRepository tmTestPlanRepository;

    @Autowired
    private ITmTestPlanCaseRepository tmTestPlanCaseRepository;

    @Autowired
    private AutomaticTaskQueryDomainService automaticTaskQueryDomainService;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    @Autowired
    private IProductRpcService iProductRpcService;

    @Autowired
    private IPipelineRpcService iPipelineRpcService;

    @Autowired
    private IPipelineRpcService pipelineRpcService;

    @Autowired
    private LinkMapDomainService linkMapDomainService;

    @Autowired
    private QcRobotMessageService qcRobotMessageService;

    @Autowired
    private IQcDebugService qcDebugService;

    private final Executor AUTOMATIC_TASK_EXECUTOR = new ThreadPoolExecutor(
            10,
            200,
            30L,
            TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            ThreadFactoryBuilder.create().setNamePrefix("Automatic-Task-").build());

    private static ThreadPoolExecutor AUTOMATIC_TASK_ABORT = new ThreadPoolExecutor(
            1, 10, 10, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(100),
            ThreadFactoryBuilder.create().setNamePrefix("Automatic-Task-Abort").build()
    );

    private static final String preStrFlowCode = "FlowCode";

    private static final String preStrTransactor = "User";

    private static final String preStrCode = "code";

    private static final String flag = "1";

    private static final String NODE_CODE = "NODE_CODE";

    public void callback(ExecuteCallbackCommand command) {
        AUTOMATIC_TASK_EXECUTOR.execute(() -> {
            Transaction transaction = Cat.newTransaction("AutomaticCallback", command.getAggregateId());
            try {
                log.info("ExecuteCallbackCommand >>> {} ，status:{}  ossPath: {} ", command.getAggregateId(), command.getStatus(), command.getOssPath());
                ExecuteCallbackEvent event = automaticTaskEventConverter.converter(command);
                AutomaticTask task = automaticTaskRepository.loadFromDb(command.getAggregateId());
                this.createExecuteCallbackEvent(event, task);
                this.executeCallback(event);
                apply(event);
                this.onExecuteCallbackEvent(
                        task.getTrigMode(),
                        task.getTaskId(),
                        task.getVersionCode(),
                        task.getTestStage());
                transaction.setStatus(Message.SUCCESS);
                //取消全量代码覆盖率 3.374.0
                //this.submitCoverageExecute(event);
            } catch (Exception e) {
                transaction.setStatus(e);
            } finally {
                transaction.complete();
            }
        });
    }

    public void createExecuteCallbackEvent(ExecuteCallbackEvent event, AutomaticTask task) {
        event.setTestPlanCode(task.getTestPlanCode());
        event.setTestStage(task.getTestStage());
        event.setProductCode(task.getProductCode());
        event.setType(task.getType());
        event.setTaskId(task.getTaskId());
        event.setEnv(task.getEnv());
        event.setExecuteTag(task.getExecuteTag());
        event.setCoverageFlag(task.getCoverageFlag());
        event.setTransactor(new User(task.getCreatorId(), task.getCreator()));
        event.setVersionCode(task.getVersionCode());
        if (AutomaticStatusEnum.TERMINATION.equals(task.getStatus())) {
            event.setStatus(AutomaticStatusEnum.TERMINATION);
        }
        event.setTrigMode(task.getTrigMode());
        event.setOssPath(jenkinsService.getOssPath(task.getStartTime(), task.getCode()));
    }

    public void tcExecuteResultCallback(ExecuteCallbackCommand command) {
        AUTOMATIC_TASK_EXECUTOR.execute(() -> {
            ExecuteCallbackEvent event = automaticTaskEventConverter.converter(command);
            AutomaticTask task = automaticTaskRepository.loadFromDb(command.getAggregateId());
            if (Objects.isNull(task)) {
                return;
            }
            this.createExecuteCallbackEvent(event, task);
            String caseFile = jenkinsService.getResultFile(event.getOssPath(), "/callback.json");
            List<ExecuteCaseCallbackVO> execTestCaseList = new ArrayList<>();
            if (StringUtils.isNotEmpty(caseFile)) {
                try {
                    JSONObject json = jenkinsService.getCaseFileContent(caseFile);
                    execTestCaseList = JSON.parseArray(json.getString("execTestCaseList"), ExecuteCaseCallbackVO.class);
                } catch (Exception e) {
                    log.error("读取oss文件异常！{}", caseFile, e);
                }
            }
            this.updateTestcaseStatus(event, execTestCaseList);
            AutomaticTaskEntityDO entityDO = new AutomaticTaskEntityDO();
            entityDO.setCode(task.getCode());
            entityDO.setExecLogFile(task.getExecLogFile());
            entityDO.setReportFile(task.getReportFile());
            automaticTaskRepository.updateCaseFileByAutomaticTask(entityDO);
            emit(TestEventEnums.TEST_AUTOMATIC_EXECUTE.toReactiveId(event.getProductCode()), "TASK_END");
        });
    }

    private void executeCallback(ExecuteCallbackEvent event) {
        List<ExecuteCaseCallbackVO> execTestCaseList = new ArrayList<>();
        AutomaticTaskEntityDO entityDO = new AutomaticTaskEntityDO();
        entityDO.setCode(event.getCode());
        entityDO.setStatus(event.getStatus());
        entityDO.setGmtModified(event.getOccurred());
        entityDO.setTrigMode(event.getTrigMode());
        entityDO.setBuildId(StringUtils.defaultIfBlank(event.getBuildId(), null));
        if (AutomaticStatusEnum.IN_PROGRESS.equals(event.getStatus())) {
            automaticTaskRepository.updateByAutomaticTaskEntityDO(entityDO);
            //更新测试用例执行记录表
            automaticTaskRepository.updateResultByAutomaticTaskCode(event.getCode(), TestPlanCaseStatusEnum.IN_PROGRESS);
            emit(TestEventEnums.TEST_AUTOMATIC_EXECUTE.toReactiveId(event.getProductCode()), "TASK_RUNNING");
            if (StringUtils.isNotBlank(event.getTestPlanCode()) && null != event.getTestStage()) {
                automaticTaskRepository.updatePlanCaseStatus(event.getCode(), event.getStatus(),
                        event.getTestPlanCode(), event.getTestStage());
                emit(TestEventEnums.TEST_AUTOMATIC_EXECUTE.toReactiveId(event.getTestPlanCode()), "PLAN_RUNNING");
            }
            if (Arrays.asList(AutomaticTaskTrigModeEnum.SCHEDULER_RUN, AutomaticTaskTrigModeEnum.SCHEDULER_ONCE)
                    .contains(event.getTrigMode())) {
                this.updateScheduler(event.getTaskId(), null);
            }
            return;
        }
        entityDO.setFinishTime(event.getOccurred());
        if (AutomaticRecordTypeEnum.JMETER.equals(event.getType())
                || AutomaticRecordTypeEnum.JMETER_GIT.equals(event.getType())) {
            entityDO.setResultFile(jenkinsService.getResultFile(event.getOssPath(), "/jtl/task.jtl"));
            entityDO.setExecLogFile(jenkinsService.getResultFile(event.getOssPath(), "/jmeter.log"));
            entityDO.setReportFile(jenkinsService.getResultFile(event.getOssPath(), "/html/index.html"));
        }
        if (AutomaticRecordTypeEnum.TESTNG.equals(event.getType())) {
            entityDO.setExecLogFile(jenkinsService.getResultFile(event.getOssPath(), "/testng.log"));
            entityDO.setReportFile(jenkinsService.getResultFile(event.getOssPath(), "/allure-report/index.html"));
        }
        if (AutomaticRecordTypeEnum.PYTEST.equals(event.getType())) {
            entityDO.setExecLogFile(jenkinsService.getResultFile(event.getOssPath(), "/pytest.log"));
            entityDO.setReportFile(jenkinsService.getResultFile(event.getOssPath(), "/allure-report/index.html"));
        }
        entityDO.setCaseFile(jenkinsService.getResultFile(event.getOssPath(), "/callback.json"));

        log.info("Automatic Callback Event: {}", JSONObject.toJSONString(event));
        log.info("Automatic Callback Entity: {}", JSONObject.toJSONString(entityDO));

        if (StringUtils.isNotEmpty(entityDO.getCaseFile())) {
            try {
                JSONObject json = jenkinsService.getCaseFileContent(entityDO.getCaseFile());
                AutomaticStatusEnum status = AutomaticStatusEnum.getEnumByName(json.getString("status"));
                if (null != status && !AutomaticStatusEnum.TERMINATION.equals(event.getStatus())) {
                    entityDO.setStatus(status);
                }
                execTestCaseList = JSON.parseArray(json.getString("execTestCaseList"), ExecuteCaseCallbackVO.class);
            } catch (Exception e) {
                log.error("读取oss文件异常！{}", entityDO.getCaseFile(), e);
                entityDO.setStatus(AutomaticStatusEnum.ERROR);
                entityDO.setComment("读取oss文件异常！");
            }
        }
        if (AutomaticStatusEnum.ERROR.equals(event.getStatus()) || AutomaticStatusEnum.TERMINATION.equals(event.getStatus())) {
            entityDO.setErrorLogFile(event.getOssPath() + "/jenkins.log");
        } else if (CollectionUtils.isEmpty(execTestCaseList)) {
            // execTestCaseList为空,全是跳过,定义为失败
            entityDO.setStatus(AutomaticStatusEnum.FAIL);
        }
        if (CollectionUtils.isNotEmpty(execTestCaseList)) {
            // 未执行统一为跳过
            execTestCaseList.stream().forEach(vo -> {
                if (TestPlanCaseStatusEnum.NOT_STARTED.equals(vo.getStatus())
                        || TestPlanCaseStatusEnum.UNKNOWN.equals(vo.getStatus())) {
                    vo.setStatus(TestPlanCaseStatusEnum.SKIPPED);
                }
            });
            // 所有都是跳过，定义为失败
            List<ExecuteCaseCallbackVO> noSkipList = execTestCaseList.stream().filter(
                            vo -> (!TestPlanCaseStatusEnum.SKIPPED.equals(vo.getStatus()) &&
                                    !TestPlanCaseStatusEnum.SKIP.equals(vo.getStatus())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(noSkipList)) {
                entityDO.setStatus(AutomaticStatusEnum.FAIL);
            } else {
                // 部分成功。部分跳过定义为成功
                List<ExecuteCaseCallbackVO> filterList = noSkipList.stream().filter(
                                vo -> !TestPlanCaseStatusEnum.SUCCESS.equals(vo.getStatus()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterList)) {
                    entityDO.setStatus(AutomaticStatusEnum.SUCCESS);
                }
            }
        }
        this.updateTestcaseStatus(event, execTestCaseList);
        automaticTaskRepository.updateByAutomaticTaskEntityDO(entityDO);
        automaticTaskRepository.updateCaseFileByAutomaticTask(entityDO);
        emit(TestEventEnums.TEST_AUTOMATIC_EXECUTE.toReactiveId(event.getProductCode()), "TASK_END");
        if (StringUtils.isNotBlank(event.getTestPlanCode()) && null != event.getTestStage()) {
            automaticTaskRepository.updatePlanCaseStatus(event.getCode(), event.getStatus(),
                    event.getTestPlanCode(), event.getTestStage());
            emit(TestEventEnums.TEST_AUTOMATIC_EXECUTE.toReactiveId(event.getTestPlanCode()), "PLAN_END");
        }
        if (Arrays.asList(AutomaticTaskTrigModeEnum.SCHEDULER_RUN, AutomaticTaskTrigModeEnum.SCHEDULER_ONCE)
                .contains(event.getTrigMode())) {
            this.updateScheduler(event.getTaskId(), null);
            automaticSchedulerMsgCommandDomainService.sendMessage(event.getTaskId());
            qcRobotMessageService.sendScheduledFailMessageEvent(event);
        }
    }

    private void updateTestcaseStatus(ExecuteCallbackEvent event, List<ExecuteCaseCallbackVO> execTestCaseList) {
        automaticTaskRepository.updateResultByAutomaticTaskCode(event.getCode(),
                AutomaticStatusEnum.TERMINATION.equals(event.getStatus()) ?
                        TestPlanCaseStatusEnum.TERMINATION : TestPlanCaseStatusEnum.SKIP);
        if (CollectionUtils.isEmpty(execTestCaseList)) {
            return;
        }
        for (ExecuteCaseCallbackVO vo : execTestCaseList) {
            TestcaseExecuteRecordEntityDO entityDO = new TestcaseExecuteRecordEntityDO();
            entityDO.setResult(vo.getStatus());
            entityDO.setStartTime(vo.getStartTime());
            entityDO.setFinishTime(vo.getFinishTime());
            entityDO.setGmtModified(event.getOccurred());
            if (AutomaticRecordTypeEnum.JMETER.equals(event.getType()) || AutomaticRecordTypeEnum.JMETER_GIT.equals(event.getType())) {
                entityDO.setResultFile(jenkinsService.getResultFile(event.getOssPath(), "/jtl/" + vo.getCode() + ".json"));
            }
            if (AutomaticRecordTypeEnum.TESTNG.equals(event.getType()) || AutomaticRecordTypeEnum.PYTEST.equals(event.getType())) {
                entityDO.setResultFile(jenkinsService.getResultFile(event.getOssPath(), "/" + vo.getCode() + ".json"));
            }
            automaticTaskRepository.updateTestcaseStatus(entityDO, vo.getCode(), event.getCode());
        }
    }

    public void executeAutoTask(UserInfo currentUser, ExecuteAutomaticTaskReq req) {
        //查询可执行的用例（有登记库源与父模块的用例）;
        ListAutomaticTaskCaseQuery query = new ListAutomaticTaskCaseQuery();
        query.setTestcaseCodeList(req.getTestcaseCodeList());
        List<TestcaseVO> list = automaticTaskQueryDomainService.queryExecutableCaseList(req.getTestcaseCodeList());
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("没有可执行的用例！");
        }
        // 判断执行环境
        String env = this.getExecuteEnv(req);
        String taskId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        Map<String, List<TestcaseVO>> map = list.stream()
                .collect(Collectors.groupingBy(TestcaseVO::getAutomaticSourceCode));
        map.forEach((automaticSourceCode, testcaseList) -> {
            List<String> testcaseCodeList = testcaseList.stream()
                    .map(TestcaseVO::getCode).collect(Collectors.toList());
            String code = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
            ExecuteAutomaticTaskCommand command = new ExecuteAutomaticTaskCommand(code);
            command.setTaskId(taskId);
            //动态多环境且没有部署直接打到base空间执行，如果base空间不存在，抛异常
            command.setEnv(env);
            command.setTag(req.getTag());
            command.setAutomaticSourceCode(automaticSourceCode);
            command.setTestcaseCodeList(testcaseCodeList);
            command.setVersionCode(req.getVersionCode());
            command.setTrigMode(req.getTrigMode());
            command.setCoverageFlag(Boolean.FALSE);
            command.setProductCode(req.getProductCode());
            if (StringUtils.isNotBlank(req.getTestPlanCode()) && null != req.getTestStage()) {
                command.setTestPlanCode(req.getTestPlanCode());
                command.setTestStage(req.getTestStage());
            }
            command.setTransactor(null != currentUser ? currentUser.toSimpleUser() : null);
            this.executeAutomaticTask(command);
        });
    }

    private String getExecuteEnv(ExecuteAutomaticTaskReq req) {
        //动态多环境且没有部署直接打到base空间执行，如果base空间不存在，抛异常
        if (req.getAutoEnv() && !req.getIsDeploy()) {
            NamespaceResp baseName = null;
            try {
                baseName = iPipelineRpcService.findBaseNamespace(req.getProductCode());
            } catch (Exception e) {
                log.info("查询发布域异常{}", e);
            }
            if (baseName == null) {
                throw new ServiceException("未找到动态多环境base空间");
            }
            return baseName.getName();
        }
        return req.getEnv();
    }

    public void executeAutomaticTask(ExecuteAutomaticTaskCommand command) {
        log.info("ExecuteAutomaticTaskCommand >>> {}", command.getAggregateId());
        AutomaticTaskExecutedEvent event = automaticTaskDomainConverter.converter(command);
        this.setProductName(event);
        this.doAddAutomaticTask(event);
        this.execute(event);
        apply(event);
    }

    private void setProductName(AutomaticTaskExecutedEvent event) {
        SimpleQueryVO product = iProductRpcService.getProductVO(event.getProductCode());
        if (null != product) {
            event.setProductName(product.getProductName());
        }
    }

    private void doAddAutomaticTask(AutomaticTaskExecutedEvent event) {
        AutomaticSourceRecordEntityDO automaticSourceRecord =
                automaticSourceRecordRepository.find(event.getAutomaticSourceCode());
        automaticTaskDomainConverter.converter(automaticSourceRecord, event);
        AutomaticTaskEntityDO taskEntityDO = automaticTaskDomainConverter.converter(event);
        taskEntityDO.setExecuteTag(event.getTag());
        taskEntityDO.setProductCode(automaticSourceRecord.getProductCode());
        taskEntityDO.setSourceAddress(automaticSourceRecord.getAddress());
        taskEntityDO.setFilename(automaticSourceRecord.getFileName());
        taskEntityDO.setType(automaticSourceRecord.getType());
        taskEntityDO.setBranchName(automaticSourceRecord.getBranch());
        taskEntityDO.setWorkDir(automaticSourceRecord.getWorkSpace());
        taskEntityDO.setCommitId(automaticSourceRecord.getCommitId());
        taskEntityDO.setStatus(AutomaticStatusEnum.NOT_STARTED);
        taskEntityDO.setExecuteMode(AutomaticExecuteModeEnum.API);
        taskEntityDO.setSchedulerCode(event.getSchedulerCode());
        taskEntityDO.setStartTime(event.getOccurred());
        taskEntityDO.preCreate(event);
        //tm_automatic_task添加记录
        automaticTaskRepository.insertSelective(taskEntityDO);
        TestcaseExecuteRecordEntityDO executeRecordEntityDO = automaticTaskDomainConverter.converter(taskEntityDO);
        executeRecordEntityDO.setResult(TestPlanCaseStatusEnum.SUBMITTED);
        List<List<String>> partition = Lists.partition(event.getTestcaseCodeList(), 200);
        for (List<String> list : partition) {
            testcaseRepository.insertBatchExecuteRecord(executeRecordEntityDO, list);
        }
        //长链接存储状态
        emit(TestEventEnums.TEST_AUTOMATIC_EXECUTE.toReactiveId(event.getProductCode()), "TASK_START");
        AUTOMATIC_TASK_EXECUTOR.execute(() -> {
            if (StringUtils.isNotBlank(event.getTestPlanCode()) && null != event.getTestStage()) { //开始执行
                testcaseRepository.updateByAutomaticTaskCode(event.getCode());
                emit(TestEventEnums.TEST_AUTOMATIC_EXECUTE.toReactiveId(event.getTestPlanCode()), "PLAN_START");
            }
            if (!Arrays.asList(AutomaticTaskTrigModeEnum.HEART_BEAT, AutomaticTaskTrigModeEnum.SCHEDULER_RUN)
                    .contains(event.getTrigMode())) {
                this.addActionLog(event);
            }
            if (Arrays.asList(AutomaticTaskTrigModeEnum.SCHEDULER_RUN, AutomaticTaskTrigModeEnum.SCHEDULER_ONCE)
                    .contains(event.getTrigMode())) {//定时任务更新人
                this.updateScheduler(event.getTaskId(), event.getTransactor());
            }
        });
    }

    private void addActionLog(AutomaticTaskExecutedEvent event) {
        List<String> businessCodeList = new ArrayList<>(event.getTestcaseCodeList());
        List<String> operateCaseCodeList = testcaseRepository.selectOperateCaseCodeByTaskCode(event.getCode());
        if (CollectionUtils.isNotEmpty(operateCaseCodeList)) {
            businessCodeList.addAll(operateCaseCodeList);
        }
        List<ActionLogDO> logs = new ArrayList<>();
        for (String businessCode : businessCodeList) {
            ActionLogDO log = new ActionLogDO(
                    businessCode,
                    "执行了用例",
                    String.format("任务ID：%s", event.getTaskId()),
                    event.getClass().getName(),
                    event.getTransactor().getUserName(),
                    event.getTransactor().getUserId());
            logs.add(log);
        }
        eventLogManager.createLogs(logs);
    }

    /**
     * 更新定时任务
     *
     * @param taskId 任务code
     * @param user
     */
    private void updateScheduler(String taskId, User user) {
        //查子任务
        List<AutomaticTaskEntityDO> taskList = automaticTaskRepository.selectListByTaskId(taskId);
        if (CollectionUtils.isEmpty(taskList)) {
            log.info("更新定时任务，任务列表为空，任务code -> {}", taskId);
            return;
        }

        //开始时间正序
        taskList = taskList.stream()
                .sorted(Comparator.comparing(AutomaticTaskEntityDO::getStartTime))
                .collect(Collectors.toList());
        String schedulerCode = taskList.get(0).getSchedulerCode();
        if (StringUtils.isBlank(schedulerCode)) {
            log.info("更新定时任务，定时任务code为空，任务code -> {}", taskId);
            return;
        }

        //更新定时任务
        AutomaticSchedulerEntityDO entity = new AutomaticSchedulerEntityDO();
        entity.setSchedulerCode(schedulerCode);
        entity.setTaskId(taskId);
        entity.setExecuteResult(getStatus(taskList));
        if (null != user) {
            entity.setExecutor(user.getUserName());
            entity.setExecutorId(user.getUserId());
        }
        if (null == entity.getExecuteTime()) {
            entity.setExecuteTime(taskList.get(0).getStartTime());
        }
        automaticSchedulerRepository.updateByPrimaryKeySelective(entity);
    }

    /**
     * 根据子任务状态，设置定时任务状态
     *
     * @param taskList
     * @return
     */
    private AutomaticStatusEnum getStatus(List<AutomaticTaskEntityDO> taskList) {
        //组装定时任务状态
        List<AutomaticStatusEnum> statusList =
                taskList.stream().map(AutomaticTaskEntityDO::getStatus).distinct().collect(Collectors.toList());

        if (null == statusList) {
            return AutomaticStatusEnum.UNKNOWN;
        }

        if (CollectionUtils.isEmpty(statusList)) {
            return AutomaticStatusEnum.UNKNOWN;
        }
        //1）已终止：有一个子任务是终止，则父任务为终止
        if (statusList.contains(AutomaticStatusEnum.TERMINATION)) {
            return AutomaticStatusEnum.TERMINATION;
        }
        //2）排队中：没有终止的子任务时，有一个为排队中则状态为排队中
        if (statusList.contains(AutomaticStatusEnum.NOT_STARTED)) {
            return AutomaticStatusEnum.NOT_STARTED;
        }
        //3）待执行：没有终止与排队中任务时，有一个任务为待执行则为待执行
        if (statusList.contains(AutomaticStatusEnum.SUBMITTED)) {
            return AutomaticStatusEnum.SUBMITTED;
        }
        //4）执行中：在没有终止/排队/待执行的情况下，有一个任务为执行中，都为执行中
        if (statusList.contains(AutomaticStatusEnum.IN_PROGRESS)) {
            return AutomaticStatusEnum.IN_PROGRESS;
        }
        //5）成功：子任务全部执行完，且全部成功为成功
        if (statusList.size() == 1 && statusList.contains(AutomaticStatusEnum.SUCCESS)) {
            return AutomaticStatusEnum.SUCCESS;
        }
        //6）错误：子任务全部执行完，有一个错误，则为错误
        if (statusList.contains(AutomaticStatusEnum.ERROR)) {
            return AutomaticStatusEnum.ERROR;
        }
        //7）失败：子任务全部执行完，没有错误，有一个为失败就是失败
        if (statusList.contains(AutomaticStatusEnum.FAIL)) {
            return AutomaticStatusEnum.FAIL;
        }
        return AutomaticStatusEnum.UNKNOWN;
    }

    private void execute(AutomaticTaskExecutedEvent event) {
        Transaction transaction = Cat.newTransaction("AutomicExcetue", event.getAutomaticSourceCode());
        AUTOMATIC_TASK_EXECUTOR.execute(() -> {
            try {
                // 查场景信息
                SceneInfoEntityDO sceneInfoEntityDO =
                        apiTestRepository.queryLatestSceneInfoByAutomaticSourceCode(event.getAutomaticSourceCode());
                if (null != sceneInfoEntityDO) {
                    event.setSceneCaseFlag(Boolean.TRUE);
                    apiTestCommandDomainService.uploadVariableFileByProductCode(event.getProductCode(),
                            event.getSourceAddress(), sceneInfoEntityDO.getSceneBackData(), event.getEnv(), sceneInfoEntityDO.getSceneCode());
                    //上传redisConfig文件
                    linkMapDomainService.uploadRedisConfig(sceneInfoEntityDO, event.getTaskId(), event.getProductCode());
                }
                if (AutomaticRecordTypeEnum.API_TEST.equals(event.getType())) {
                    // 单接口执行
                    List<ApiTestCaseEntityDO> caseDataList = apiTestRepository.queryExceptionCaseByCodeList(event.getTestcaseCodeList());
                    if (CollectionUtils.isEmpty(caseDataList)) {
                        return;
                    }
                    // 更新父用例状态
                    apiTestCommandDomainService.updateParentCaseExecuteResultByTaskCode(event.getAggregateId());
                    // 提交任务
                    apiTestCommandDomainService.submitApiTestTask(caseDataList.get(0).getParentCaseCode(),
                            event.getTag(), event.getEnv(), caseDataList, event.getCode());
                } else {
                    // 查询登记库源
                    List<TestcaseEntityDO> testcaseList =
                            automaticTaskRepository.getTestcaseByAutomaticSourceCode(event.getAutomaticSourceCode());
                    List<AutomaticNode> execInfo =
                            getAutomaticNodeTree(testcaseList, StringUtils.EMPTY, event.getTestcaseCodeList()).stream()
                                    .filter(node -> Boolean.TRUE.equals(node.getEnable()))
                                    .collect(Collectors.toList());
                    // 执行
                    AutomaticTaskEntityDO entityDO = jenkinsService.execute(event, execInfo);
                    automaticTaskRepository.updateByPrimaryKeySelective(entityDO);
                }
                emit(TestEventEnums.TEST_AUTOMATIC_EXECUTE.toReactiveId(event.getProductCode()), "TASK_RUNNING"); // 运行中
                if (Arrays.asList(AutomaticTaskTrigModeEnum.SCHEDULER_RUN, AutomaticTaskTrigModeEnum.SCHEDULER_ONCE)
                        .contains(event.getTrigMode())) { // 定时任务发送消息
                    this.updateScheduler(event.getTaskId(), event.getTransactor());
                    redisService.setKey("Message::" + event.getTaskId(), "1",
                            qcConfigBasicService.getJobAutoTestAbortDuration() + 10, TimeUnit.MINUTES);
                    automaticSchedulerMsgCommandDomainService.sendMessage(event.getTaskId());
                }
                this.onExecuteCallbackEvent(
                        event.getTrigMode(),
                        event.getTaskId(),
                        event.getVersionCode(),
                        event.getTestStage());
                transaction.setStatus(Message.SUCCESS);
            } catch (Exception e) {
                log.error("执行任务失败，任务ID：{}", event.getTaskId(), e);
                transaction.setStatus(e);
            } finally {
                transaction.complete();
            }

        });
    }

    private List<AutomaticNode> getAutomaticNodeTree(List<TestcaseEntityDO> list, String parentCode, List<String> codeList) {
        return list.parallelStream()
                .filter(entity -> parentCode.equals(entity.getParentCode()))
                .map(entity -> {
                    boolean enable = TestcaseStatusEnum.NORMAL.equals(entity.getStatus());
                    if (TestcaseAttributeEnum.TESTCASE.equals(entity.getAttribute()) && enable) {
                        enable = codeList.contains(entity.getCode());
                    }
                    List<AutomaticNode> automaticNodeList = null;
                    if (TestcaseAttributeEnum.MODULE.equals(entity.getAttribute())) {
                        automaticNodeList = getAutomaticNodeTree(list, entity.getCode(), codeList);
                        if (CollectionUtils.isEmpty(automaticNodeList)) {
                            return null;
                        }
                        enable = enable && automaticNodeList.parallelStream().anyMatch(node -> Boolean.TRUE.equals(node.getEnable()));
                    }
                    AutomaticNode automaticNode = new AutomaticNode();
                    automaticNode.setName(trimTrailingSpaces(entity.getName())); // todo 去空格
                    automaticNode.setType(entity.getNodeType().getDesc());
                    automaticNode.setCodeId(entity.getCode());
                    automaticNode.setEnable(enable);
                    automaticNode.setAutomaticNodeList(automaticNodeList);
                    return automaticNode;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static String trimTrailingSpaces(String input) {
        if (input == null) {
            return null;
        }
        int length = input.length();
        int startIndex = 0;
        int endIndex = length - 1;
        while (startIndex < length && Character.isWhitespace(input.charAt(startIndex))) {
            startIndex++;
        }
        while (endIndex >= startIndex && Character.isWhitespace(input.charAt(endIndex))) {
            endIndex--;
        }
        if (startIndex > endIndex) {
            return "";
        }
        return input.substring(startIndex, endIndex + 1);
    }

    public void terminateAutomaticTaskCommand(TerminateAutomaticTaskCommand command) {
        log.info("terminateAutomaticTaskCommand >>> {}", JSON.toJSONString(command));
        AutomaticTask automaticTask = automaticTaskRepository.loadFromDb(command.getAggregateId());
        AutomaticTaskTerminatedEvent event = new AutomaticTaskTerminatedEvent();
        event.setAggregateId(command.getAggregateId());
        event.setTransactor(command.getTransactor());
        automaticTaskEventConverter.convert(automaticTask, event);
        if (StringUtils.isNotEmpty(event.getBuildId())) {
            jenkinsService.terminate(event);
        }
        AutomaticTaskEntityDO entityDO = new AutomaticTaskEntityDO();
        entityDO.setCode(event.getCode());
        entityDO.setStatus(AutomaticStatusEnum.TERMINATION);
        entityDO.setFinishTime(new Date());
        entityDO.preUpdate(event);
        automaticTaskRepository.updateByAutomaticTaskEntityDO(entityDO);
        emit(TestEventEnums.TEST_AUTOMATIC_EXECUTE.toReactiveId(event.getProductCode()), "TASK_END");
        AUTOMATIC_TASK_EXECUTOR.execute(() -> {
            if (AutomaticTaskTrigModeEnum.SINGLE_API_MANUAL.equals(automaticTask.getTrigMode())
                    || AutomaticTaskTrigModeEnum.SINGLE_API_AUTO.equals(automaticTask.getTrigMode())) {
                automaticTaskRepository.updateResultNotFinished(event.getCode(), TestPlanCaseStatusEnum.TERMINATION);
                apiTestCommandDomainService.updateParentCaseExecuteResultByTaskCode(event.getCode());
                qcDebugService.abort(event.getCode());
            } else {
                automaticTaskRepository.updateResultByAutomaticTaskCode(event.getCode(), TestPlanCaseStatusEnum.TERMINATION);
            }
            if (StringUtils.isNotBlank(event.getTestPlanCode()) && null != event.getTestStage()) {
                automaticTaskRepository.updatePlanCaseStatus(event.getCode(), AutomaticStatusEnum.TERMINATION,
                        event.getTestPlanCode(), event.getTestStage());
                emit(TestEventEnums.TEST_AUTOMATIC_EXECUTE.toReactiveId(event.getTestPlanCode()), "PLAN_END");
            }
            this.addActionLog(event);
            if (Arrays.asList(AutomaticTaskTrigModeEnum.SCHEDULER_RUN, AutomaticTaskTrigModeEnum.SCHEDULER_ONCE)
                    .contains(event.getTrigMode())) {
                this.updateScheduler(event.getTaskId(), event.getTransactor());
                automaticSchedulerMsgCommandDomainService.sendMessage(event.getTaskId());
            }
            this.onExecuteCallbackEvent(
                    automaticTask.getTrigMode(),
                    automaticTask.getTaskId(),
                    automaticTask.getVersionCode(),
                    automaticTask.getTestStage());
        });
        apply(event);
    }

    public void batchTerminateAutoTask(String code, Boolean parentFlag, User user) {
        //查询排队中、待执行、执行中的任务
        List<AutomaticStatusEnum> statusEnums = Arrays.asList(AutomaticStatusEnum.NOT_STARTED,
                AutomaticStatusEnum.SUBMITTED, AutomaticStatusEnum.IN_PROGRESS);
        List<AutomaticTaskEntityDO> entityList =
                automaticTaskRepository.selectAutomaticTaskByStatusList(code, parentFlag, statusEnums);
        if (CollectionUtils.isEmpty(entityList)) {
            log.info("状态筛选，没有需要终止的任务，code:{}, parentFlag:{}", code, parentFlag);
            return;
        }
        //终止任务
        entityList.forEach(item -> {
            AUTOMATIC_TASK_EXECUTOR.execute(() -> {
                TerminateAutomaticTaskCommand command = new TerminateAutomaticTaskCommand(item.getCode());
                command.setTransactor(user);
                this.terminateAutomaticTaskCommand(command);
            });
        });
    }

    private void addActionLog(AutomaticTaskTerminatedEvent event) {
        List<String> businessCodeList = new ArrayList<>();
        List<String> testcaseCodeList = automaticTaskRepository.selectTestcaseCodeList(event.getCode());
        if (CollectionUtils.isNotEmpty(testcaseCodeList)) {
            businessCodeList.addAll(testcaseCodeList);
        }
        List<String> operateCaseCodeList = testcaseRepository.selectOperateCaseCodeByTaskCode(event.getCode());
        if (CollectionUtils.isNotEmpty(operateCaseCodeList)) {
            businessCodeList.addAll(operateCaseCodeList);
        }
        List<ActionLogDO> logs = new ArrayList<>();
        for (String businessCode : businessCodeList) {
            ActionLogDO log = new ActionLogDO(
                    businessCode,
                    "终止了用例执行",
                    String.format("任务ID：%s", event.getTaskId()),
                    event.getClass().getName(),
                    event.getTransactor().getUserName(),
                    event.getTransactor().getUserId());
            logs.add(log);
        }
        eventLogManager.createLogs(logs);
    }

    private void onExecuteCallbackEvent(
            AutomaticTaskTrigModeEnum trigMode, String taskId, String versionCode, TestPlanStageEnum testStage) {
        if (!AutomaticTaskTrigModeEnum.DEPLOY.equals(trigMode)) {
            // 非部署触发模式，直接忽略
            return;
        }
        log.info("CD ExecuteCallbackEvent TaskId: >>> {}", taskId);
        ExecuteCallbackCDEvent executeCallbackCDEvent = new ExecuteCallbackCDEvent();
        executeCallbackCDEvent.setAggregateId(taskId);
        executeCallbackCDEvent.setTaskId(taskId);
        if (redisService.hasKey(preStrFlowCode + taskId)) {
            executeCallbackCDEvent.setFlowCode(redisService.getKey(preStrFlowCode + taskId));
        }
        if (redisService.hasKey(preStrTransactor + taskId)) {
            User user = JSON.parseObject(redisService.getKey(preStrTransactor + taskId), User.class);
            executeCallbackCDEvent.setTransactor(user);
        }
        if (redisService.hasKey("DubboTag::" + taskId)) {
            RpcContext.getContext().setAttachment(Constants.TAG_KEY, redisService.getKey("DubboTag::" + taskId));
        }
        if (redisService.hasKey(NODE_CODE + taskId)) {
            executeCallbackCDEvent.setNodeCode(redisService.getKey(NODE_CODE + taskId));
        }
        // 依次执行当前版本内每个测试阶段的用例
        List<AutomaticTaskEntityDO> versionTaskList =
                automaticTaskRepository.selectByTaskIdAndVersionCode(taskId, versionCode);
        Map<TestPlanStageEnum, List<AutomaticTaskEntityDO>> versionTaskMap =
                versionTaskList.stream().collect(Collectors.groupingBy(AutomaticTaskEntityDO::getTestStage));
        if (versionTaskMap.get(testStage).stream()
                .anyMatch(task -> AutomaticStatusEnum.NOT_STARTED.equals(task.getStatus())
                        || AutomaticStatusEnum.SUBMITTED.equals(task.getStatus())
                        || AutomaticStatusEnum.IN_PROGRESS.equals(task.getStatus()))) {
            log.info("VersionTaskList is IN_PROGRESS >>> {} {} {}", taskId, versionCode, testStage);
            return;
        }
        for (TestPlanStageEnum testPlanStage : TestPlanStageEnum.values()) {
            List<AutomaticTaskEntityDO> nexTaskList = versionTaskMap.get(testPlanStage);
            if (CollectionUtils.isNotEmpty(nexTaskList)
                    && nexTaskList.stream()
                    .anyMatch(task -> AutomaticStatusEnum.NOT_STARTED.equals(task.getStatus()))) {
                log.info("NextTaskList >>> {} {} {}", taskId, versionCode, testPlanStage);
                nexTaskList.forEach(task -> {
                    AutomaticTaskExecutedEvent automaticTaskExecutedEvent = automaticTaskEventConverter.convert(task);
                    automaticTaskExecutedEvent.setAggregateId(automaticTaskExecutedEvent.getCode());
                    automaticTaskExecutedEvent.setTestcaseCodeList(
                            automaticTaskRepository.selectTestcaseCodeList(task.getCode()));
                    automaticTaskExecutedEvent.setTransactor(executeCallbackCDEvent.getTransactor());
                    automaticTaskExecutedEvent.setOccurred(task.getStartTime());
                    this.execute(automaticTaskExecutedEvent);
                    log.info("executeTask >>> {}", task.getCode());
                    redisService.setKey(preStrCode + task.getCode(), flag, 5, TimeUnit.MINUTES);
                });
                return;
            }
        }
        // 依次执行每个版本内每个测试阶段的用例
        List<AutomaticTaskEntityDO> taskList = automaticTaskRepository.selectByTaskIdAndVersionCode(taskId, null);
        String nextVersionCode = taskList.stream()
                .filter(task -> AutomaticStatusEnum.NOT_STARTED.equals(task.getStatus()))
                .map(AutomaticTaskEntityDO::getVersionCode)
                .distinct()
                .findFirst()
                .orElse(null);
        if (StringUtils.isNotEmpty(nextVersionCode)) {
            Map<TestPlanStageEnum, List<AutomaticTaskEntityDO>> taskMap = taskList.stream()
                    .filter(task -> nextVersionCode.equals(task.getVersionCode()))
                    .collect(Collectors.groupingBy(AutomaticTaskEntityDO::getTestStage));
            for (TestPlanStageEnum testPlanStage : TestPlanStageEnum.values()) {
                List<AutomaticTaskEntityDO> nexTaskList = taskMap.get(testPlanStage);
                if (CollectionUtils.isNotEmpty(nexTaskList)) {
                    log.info("NextTaskList >>> {} {} {}", taskId, versionCode, testPlanStage);
                    nexTaskList.forEach(task -> {
                        AutomaticTaskExecutedEvent automaticTaskExecutedEvent =
                                automaticTaskEventConverter.convert(task);
                        automaticTaskExecutedEvent.setAggregateId(automaticTaskExecutedEvent.getCode());
                        automaticTaskExecutedEvent.setTestcaseCodeList(
                                automaticTaskRepository.selectTestcaseCodeList(task.getCode()));
                        automaticTaskExecutedEvent.setTransactor(executeCallbackCDEvent.getTransactor());
                        automaticTaskExecutedEvent.setOccurred(task.getStartTime());
                        this.execute(automaticTaskExecutedEvent);
                        log.info("executeTask >>> {}", task.getCode());
                        redisService.setKey(preStrCode + task.getCode(), flag, 5, TimeUnit.MINUTES);
                    });
                    return;
                }
            }
        }
        List<TestPlanCaseStatusEnum> statusList = automaticTaskRepository.getTestcaseResultByTaskId(taskId);
        Map<TestPlanCaseStatusEnum, Long> statusMap =
                statusList.stream().collect(Collectors.groupingBy(e -> e, Collectors.counting()));
        executeCallbackCDEvent.setTotalCaseCount(statusList.size());
        executeCallbackCDEvent.setPassedCaseCount(statusMap
                .getOrDefault(TestPlanCaseStatusEnum.PASSED, 0L)
                .intValue()
                + statusMap.getOrDefault(TestPlanCaseStatusEnum.SUCCESS, 0L).intValue());
        executeCallbackCDEvent.setFailedCaseCount(
                statusMap.getOrDefault(TestPlanCaseStatusEnum.FAILED, 0L).intValue());
        executeCallbackCDEvent.setTerminatedCaseCount(
                statusMap.getOrDefault(TestPlanCaseStatusEnum.TERMINATION, 0L).intValue());
        if (taskList.stream().anyMatch(task -> AutomaticStatusEnum.TERMINATION.equals(task.getStatus()))) {
            executeCallbackCDEvent.setOtherCaseCount(executeCallbackCDEvent.getTotalCaseCount()
                    - executeCallbackCDEvent.getPassedCaseCount()
                    - executeCallbackCDEvent.getTerminatedCaseCount());
            executeCallbackCDEvent.setStatus(AutomaticStatusEnum.TERMINATION);
            pipelineRpcService.handleExecuteCallbackCDEvent(executeCallbackCDEvent);
            return;
        }
        executeCallbackCDEvent.setOtherCaseCount(executeCallbackCDEvent.getTotalCaseCount()
                - executeCallbackCDEvent.getPassedCaseCount()
                - executeCallbackCDEvent.getFailedCaseCount());
        if (taskList.stream().anyMatch(task -> AutomaticStatusEnum.ERROR.equals(task.getStatus())
                || AutomaticStatusEnum.FAIL.equals(task.getStatus()))) {
            executeCallbackCDEvent.setStatus(AutomaticStatusEnum.FAIL);
            pipelineRpcService.handleExecuteCallbackCDEvent(executeCallbackCDEvent);
            return;
        }
        executeCallbackCDEvent.setStatus(AutomaticStatusEnum.SUCCESS);
        pipelineRpcService.handleExecuteCallbackCDEvent(executeCallbackCDEvent);
    }

    public void onAutomaticTaskCDExecutedEvent(AutomaticTaskCDExecutedEvent event) {
        if (StringUtils.isEmpty(event.getDubboTag())) {
            event.setDubboTag(RpcContext.getContext().getAttachment(Constants.TAG_KEY));
        }
        redisService.hashSet("AutomaticTaskCDExecutedEvent", event.getAggregateId(), JSON.toJSONString(event));
        AUTOMATIC_TASK_EXECUTOR.execute(() -> {
            try {
                if (StringUtils.isNotEmpty(event.getDubboTag())) {
                    RpcContext.getContext().setAttachment(Constants.TAG_KEY, event.getDubboTag());
                }
                log.info("AutomaticTaskCDExecutedEvent >>> {}", JSON.toJSONString(event));
                if ("RETRY".equals(event.getExecuteType())) {
                    pipelineRetryTask(event);
                    return;
                }
                pipelineTriggerTask(event);
            } finally {
                redisService.hashDelete("AutomaticTaskCDExecutedEvent", event.getAggregateId());
            }
        });
    }

    private void pipelineTriggerTask(AutomaticTaskCDExecutedEvent event) {
        String taskId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        ExecuteCallbackCDEvent executeCallbackCDEvent = new ExecuteCallbackCDEvent();
        executeCallbackCDEvent.setAggregateId(event.getAggregateId());
        executeCallbackCDEvent.setTaskId(taskId);
        executeCallbackCDEvent.setFlowCode(event.getFlowCode());
        executeCallbackCDEvent.setStatus(AutomaticStatusEnum.SUCCESS);
        executeCallbackCDEvent.setTransactor(event.getTransactor());
        executeCallbackCDEvent.setNodeCode(event.getNodeCode());
        if (CollectionUtils.isEmpty(event.getVersionCodeList())) {
            log.warn("VersionCodeList is Empty >>> {}", taskId);
            pipelineRpcService.handleExecuteCallbackCDEvent(executeCallbackCDEvent);
            return;
        }
        String firstVersionCode = null;
        int count = 0;
        for (String versionCode : new HashSet<>(event.getVersionCodeList())) {
            if (null == firstVersionCode) {
                firstVersionCode = versionCode;
            }
            try {
                count = count + this.createTask(event, versionCode, taskId);
            } catch (Exception e) {
                log.error("createTask error", e);
                count = -1;
                break;
            }
        }
        log.info("createTask count >>> {}", count);
        if (count == 0) {
            pipelineRpcService.handleExecuteCallbackCDEvent(executeCallbackCDEvent);
            return;
        }
        if (count < 0) {
            executeCallbackCDEvent.setStatus(AutomaticStatusEnum.FAIL);
            pipelineRpcService.handleExecuteCallbackCDEvent(executeCallbackCDEvent);
            return;
        }
        redisService.setKey(preStrFlowCode + taskId, event.getFlowCode(), 5L, TimeUnit.HOURS);
        redisService.setKey(
                preStrTransactor + taskId, JSON.toJSONString(event.getTransactor()), 5L, TimeUnit.HOURS);
        if (StringUtils.isNotEmpty(event.getDubboTag())) {
            redisService.setKey("DubboTag::" + taskId, event.getDubboTag(), 5L, TimeUnit.HOURS);
        }
        redisService.setKey(NODE_CODE + taskId, event.getNodeCode(), 5L, TimeUnit.HOURS);
        // 依次执行每个版本内每个测试阶段的用例
        List<AutomaticTaskEntityDO> list =
                automaticTaskRepository.selectByTaskIdAndVersionCode(taskId, firstVersionCode);
        Map<TestPlanStageEnum, List<AutomaticTaskEntityDO>> map =
                list.stream().collect(Collectors.groupingBy(AutomaticTaskEntityDO::getTestStage));
        for (TestPlanStageEnum testPlanStage : TestPlanStageEnum.values()) {
            List<AutomaticTaskEntityDO> firstTaskList = map.get(testPlanStage);
            if (CollectionUtils.isNotEmpty(firstTaskList)) {
                log.info("FirstTaskList >>> {} {} {}", taskId, firstVersionCode, testPlanStage);
                executeCallbackCDEvent.setStatus(AutomaticStatusEnum.IN_PROGRESS);
                pipelineRpcService.handleExecuteCallbackCDEvent(executeCallbackCDEvent);
                firstTaskList.forEach(task -> {
                    AutomaticTaskExecutedEvent automaticTaskExecutedEvent =
                            automaticTaskEventConverter.convert(task);
                    automaticTaskExecutedEvent.setAggregateId(automaticTaskExecutedEvent.getCode());
                    automaticTaskExecutedEvent.setTestcaseCodeList(
                            automaticTaskRepository.selectTestcaseCodeList(task.getCode()));
                    automaticTaskExecutedEvent.setTransactor(event.getTransactor());
                    automaticTaskExecutedEvent.setTaskId(taskId);
                    automaticTaskExecutedEvent.setOccurred(task.getStartTime());
                    this.execute(automaticTaskExecutedEvent);
                    log.info("executeTask >>> {}", task.getCode());
                    redisService.setKey(preStrCode + task.getCode(), flag, 5, TimeUnit.MINUTES);
                });
                return;
            }
        }
    }

    private int createTask(AutomaticTaskCDExecutedEvent event, String versionCode, String taskId) {
        TmTestPlanEntityDO testPlan = tmTestPlanRepository.selectByCD(event.getProductCode(), versionCode, TestPlanTypeEnum.TEST_PLAN.name());
        if (testPlan == null) {
            log.warn("TestPlan is null >>> {} {}", event.getProductCode(), versionCode);
            return 0;
        }
        List<TestPlanStageEnum> testPlanStageList = new ArrayList<>();
        if (testPlan.getTestStrategy() == TestPlanStrategyEnum.ALL_TEST
                || testPlan.getTestStrategy() == TestPlanStrategyEnum.ALLOW_EXIT_TEST
                || testPlan.getTestStrategy() == TestPlanStrategyEnum.STANDARD_TEST) {
            if ("SMOKE_TEST".equals(event.getExecuteType())) {
                testPlanStageList.add(TestPlanStageEnum.SMOKE_TEST);
            }
            if ("FUNCTIONAL_TEST".equals(event.getExecuteType())) {
                testPlanStageList.add(TestPlanStageEnum.FUNCTIONAL_TEST);
            }
            if ("ALL".equals(event.getExecuteType())) {
                testPlanStageList.add(TestPlanStageEnum.SMOKE_TEST);
                testPlanStageList.add(TestPlanStageEnum.FUNCTIONAL_TEST);
            }
        } else {
            testPlanStageList.add(TestPlanStageEnum.NULL_TEST);
        }
        log.info("TestPlanStageList info >>> {}", testPlanStageList);
        if (CollectionUtils.isEmpty(testPlanStageList)) {
            return 0;
        }
        AtomicInteger count = new AtomicInteger();
        testPlanStageList.forEach(testPlanStage -> {
            ListPlanCaseQuery queryTestCase = new ListPlanCaseQuery();
            queryTestCase.setProductCode(event.getProductCode());
            queryTestCase.setPlanCode(testPlan.getCode());
            queryTestCase.setCaseTypeList(Collections.singletonList(TestcaseTypeEnum.AUTO));
            queryTestCase.setTestPlanStageList(Collections.singletonList(testPlanStage));
            List<TestPlanCaseVO> testPlanCaseVOList = tmTestPlanCaseRepository.getTestPlanCaseList(queryTestCase);
            if (CollectionUtils.isEmpty(testPlanCaseVOList)) {
                log.warn("TestPlanCaseList is null >>> {}", JSON.toJSONString(queryTestCase));
                return;
            }
            List<String> tcCodeList =
                    testPlanCaseVOList.stream().map(TestPlanCaseVO::getCaseCode).collect(Collectors.toList());
            ListAutomaticTaskCaseQuery queryAutoTestCase = new ListAutomaticTaskCaseQuery();
            queryAutoTestCase.setTestcaseCodeList(tcCodeList);
            List<TestcaseVO> listQuery = automaticTaskQueryDomainService.listAutomaticTaskCase(queryAutoTestCase);
            if (CollectionUtils.isEmpty(listQuery)) {
                log.warn("AutoTestCase is null >>> {}", JSON.toJSONString(queryAutoTestCase));
                return;
            }
            listQuery.stream()
                    .collect(Collectors.groupingBy(TestcaseVO::getAutomaticSourceCode))
                    .forEach((automaticSourceCode, testcaseList) -> {
                        List<String> testcaseCodeList =
                                testcaseList.stream().map(TestcaseVO::getCode).collect(Collectors.toList());
                        String code = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
                        AutomaticTaskExecutedEvent automaticTaskExecutedEvent = new AutomaticTaskExecutedEvent();
                        automaticTaskExecutedEvent.setAggregateId(code);
                        automaticTaskExecutedEvent.setCode(code);
                        automaticTaskExecutedEvent.setTaskId(taskId);
                        automaticTaskExecutedEvent.setAutomaticSourceCode(automaticSourceCode);
                        automaticTaskExecutedEvent.setEnv(event.getSpaceName());
                        automaticTaskExecutedEvent.setTag(event.getSpaceTag());
                        automaticTaskExecutedEvent.setTestcaseCodeList(testcaseCodeList);
                        automaticTaskExecutedEvent.setVersionCode(versionCode);
                        automaticTaskExecutedEvent.setTrigMode(AutomaticTaskTrigModeEnum.DEPLOY);
                        automaticTaskExecutedEvent.setTestPlanCode(testPlan.getCode());
                        automaticTaskExecutedEvent.setTransactor(event.getTransactor());
                        automaticTaskExecutedEvent.setTestStage(testPlanStage);
                        automaticTaskExecutedEvent.setOccurred(new Date());
                        this.doAddAutomaticTask(automaticTaskExecutedEvent);
                        log.info("createTask >>> {}", JSON.toJSONString(automaticTaskExecutedEvent));
                        count.getAndIncrement();
                    });
        });
        return count.get();
    }

    public void autoTestAbort() {
        log.info("开始终止自动化超时任务");
        List<String> codes = automaticTaskRepository.getAbortAutomaticTasks(qcConfigBasicService.getJobAutoTestAbortDuration());
        if (CollectionUtil.isEmpty(codes)) {
            log.info("无需要终止的自动化任务。");
            return;
        }
        for (String code : codes) {
            try {
                AUTOMATIC_TASK_ABORT.execute(() -> {
                    TerminateAutomaticTaskCommand command = new TerminateAutomaticTaskCommand(code);
                    User transactor = new User();
                    transactor.setUserId(000000L);
                    transactor.setUserName("abort");
                    command.setTransactor(transactor);
                    terminateAutomaticTaskCommand(command);
                });
            } catch (Exception e) {
                log.warn("终止自动化任务异常。code : {}", code);
                e.printStackTrace();
            }
        }
        log.info("结束终止自动化超时任务");
    }

    public void executeAutoTestCronTask() {
        Calendar currentTime = Calendar.getInstance();
        currentTime.set(Calendar.SECOND, 59);
        log.info("executeAutoTestCronTask_begin_{}", DateUtils.format(currentTime.getTime()));
        List<AutomaticPreExecutionEntityDO> entityList = automaticSchedulerRepository.getCodeListFromPreExecute(currentTime.getTime());
        if (CollectionUtil.isNotEmpty(entityList)) {
            entityList.forEach(this::executeTaskByCode);
        }
        log.info("executeAutoTestCronTask_end_{}", DateUtils.format(currentTime.getTime()));
    }

    private void executeTaskByCode(AutomaticPreExecutionEntityDO entity) {
        log.info("executeAutoTestCronTask_doExecute_preCode: {}", Objects.isNull(entity) ? "blank" : entity.getPreCode());
        if (Objects.isNull(entity)) {
            return;
        }
        try {
            AutomaticSchedulerExecutionCommand command =
                    new AutomaticSchedulerExecutionCommand(entity.getSchedulerCode());
            command.setPreCode(entity.getPreCode());
            User transactor = new User();
            transactor.setUserId(000000L);
            transactor.setUserName("系统");
            command.setTransactor(transactor);
            command.setTrigMode(AutomaticTaskTrigModeEnum.SCHEDULER_RUN);
            handler(command);
        } catch (Exception e) {
            log.error("执行自动化-定时任务异常！preCode : {}", entity.getPreCode(), e);
        }
    }

    private void handler(AutomaticSchedulerExecutionCommand command) {
        AutomaticSchedulerExecutionEvent event = automaticTaskEventConverter.converter(command);
        executeScheduler(event);
        apply(event);
        AutomaticPreExecutionUpdateEvent updateEvent = automaticTaskEventConverter.convert(command);
        automaticSchedulerRepository.updatePreExecution(updateEvent);
        apply(updateEvent);
    }

    private void executeScheduler(AutomaticSchedulerExecutionEvent event) {
        AutomaticSchedulerEntityDO entity = automaticSchedulerRepository.selectByPrimaryKey(event.getSchedulerCode());
        if (null == entity || (!entity.getSwitchFlag() && AutomaticTaskTrigModeEnum.SCHEDULER_RUN.equals(event.getTrigMode()))) {
            log.info("无定时任务或定时任务未开启！schedulerCode:{}", event.getSchedulerCode());
            return;
        }
        if (AutomaticTaskTrigModeEnum.SCHEDULER_RUN.equals(event.getTrigMode()) && Arrays.asList(
                        AutomaticStatusEnum.NOT_STARTED, AutomaticStatusEnum.SUBMITTED, AutomaticStatusEnum.IN_PROGRESS)
                .contains(entity.getExecuteResult())) {
            log.info("定时任务未结束，此次不执行！schedulerCode:{}", event.getSchedulerCode());
            return;
        }
        event.setExecuteEnv(entity.getExecuteEnv());
        event.setExecuteTag(entity.getExecuteTag());
        event.setCoverageFlag(entity.getCoverageFlag());
        log.info("AutomaticSchedulerExecutionEvent >>> {}", JSON.toJSONString(event));
        List<String> caseCodes = automaticSchedulerRepository.getCaseCodesBySchedulerCode(event.getSchedulerCode());
        if (CollectionUtil.isEmpty(caseCodes)) {
            log.info("定时任务无关联用例！");
            return;
        }
        List<TestcaseVO> testcaseVOList = automaticSchedulerRepository.getTestcaseByCaseCodes(caseCodes);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(testcaseVOList)) {
            log.info("无关联用例需要执行！");
            return;
        }
        executeAutomaticScheduler(testcaseVOList, event);
    }

    private void executeAutomaticScheduler(List<TestcaseVO> list, AutomaticSchedulerExecutionEvent event) {
        Map<String, List<TestcaseVO>> map = list.stream()
                .collect(Collectors.groupingBy(TestcaseVO::getAutomaticSourceCode));
        String taskId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        map.forEach((automaticSourceCode, testcaseList) -> {
            List<String> testcaseCodeList =
                    testcaseList.stream().map(TestcaseVO::getCode).collect(Collectors.toList());
            String code = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
            ExecuteAutomaticTaskCommand command = new ExecuteAutomaticTaskCommand(code);
            command.setTaskId(taskId);
            command.setAutomaticSourceCode(automaticSourceCode);
            command.setSchedulerCode(event.getSchedulerCode());
            command.setEnv(event.getExecuteEnv());
            command.setTag(event.getExecuteTag());
            command.setCoverageFlag(event.getCoverageFlag());
            command.setTransactor(event.getTransactor());
            command.setTestcaseCodeList(testcaseCodeList);
            command.setTrigMode(event.getTrigMode());
            executeAutomaticTask(command);
            log.info("=========发送自动化用例执行command" + JSON.toJSONString(command));
        });
    }

    public void reExecuteAutomaticTask(AutomaticTaskEntityDO task) {
        AutomaticTaskExecutedEvent event = automaticTaskEventConverter.convert(task);
        event.setAggregateId(event.getCode());
        event.setTestcaseCodeList(automaticTaskRepository.selectTestcaseCodeList(task.getCode()));
        event.setTransactor(new User(task.getCreatorId(), task.getCreator()));
        event.setOccurred(task.getStartTime());
        this.execute(event);
    }

    public void executeApiTestTask() {
        log.info("开始处理单接口排队中任务");
        List<String> codes = automaticTaskRepository.getApiTestWaitTasks(qcConfigBasicService.getJobAutoTestAbortDuration());
        if (CollectionUtil.isEmpty(codes)) {
            log.info("没有排队中的单接口任务");
            return;
        }
        for (String code : codes) {
            AUTOMATIC_TASK_EXECUTOR.execute(() -> {
                try {
                    AutomaticTaskEntityDO task = automaticTaskRepository.getAutomaticTaskByCode(code);
                    List<String> caseCodeList = automaticTaskRepository.selectTestcaseCodeList(code);
                    List<ApiTestCaseEntityDO> caseDataList = apiTestRepository.queryExceptionCaseByCodeList(caseCodeList);
                    if (CollectionUtils.isEmpty(caseDataList)) {
                        log.warn("没有找到任务关联的单接口用例！{}", code);
                    }
                    apiTestCommandDomainService.submitApiTestTask(caseDataList.get(0).getParentCaseCode(),
                            task.getExecuteTag(), task.getEnv(), caseDataList, code);
                } catch (Exception e) {
                    log.error("单接口用例执行失败！{}", code, e);
                }
            });
        }
        log.info("结束处理单接口排队中任务");
    }

    public void apiTestCallback(String taskCode, DebugTaskRespTypeEnum type) {
        log.info("单接口执行结束回调 >>> {}", taskCode);
        AutomaticTaskEntityDO task = automaticTaskRepository.getAutomaticTaskByCode(taskCode);
        task.setFinishTime(new Date());
        if (!AutomaticStatusEnum.TERMINATION.equals(task.getStatus())) {
            if (DebugTaskRespTypeEnum.SUCCESS.equals(type)) {
                task.setStatus(AutomaticStatusEnum.SUCCESS);
            } else if (DebugTaskRespTypeEnum.FAILED.equals(type)) {
                task.setStatus(AutomaticStatusEnum.FAIL);
            } else {
                task.setStatus(AutomaticStatusEnum.ERROR);
            }
        }
        String logFile = String.format("%s/%s.log", task.getCode(), task.getCode());
        task.setExecLogFile(logFile);
        automaticTaskRepository.updateByAutomaticTaskEntityDO(task);
        qcDebugService.removeTaskId(task.getCode());
        AUTOMATIC_TASK_EXECUTOR.execute(() -> {
            ListExecuteDetailReq listExecuteDetailReq = new ListExecuteDetailReq();
            listExecuteDetailReq.setCode(task.getCode());
            List<TestcaseExecuteRecordEntityDO> list = automaticTaskRepository.getTestcaseRecordByTaskCode(listExecuteDetailReq);
            if (CollectionUtil.isNotEmpty(list)) {
                list.forEach(testcaseRecord -> {
                    testcaseRecord.setExecLogFile(logFile);
                    if (TestPlanCaseStatusEnum.NOT_STARTED.equals(testcaseRecord.getResult())
                            || TestPlanCaseStatusEnum.SUBMITTED.equals(testcaseRecord.getResult())
                            || TestPlanCaseStatusEnum.IN_PROGRESS.equals(testcaseRecord.getResult())) {
                        testcaseRecord.setResult(TestPlanCaseStatusEnum.SKIPPED);
                    }
                    automaticTaskRepository.updateTestcaseStatus(testcaseRecord, testcaseRecord.getTestcaseCode(), testcaseRecord.getAutomaticTaskCode());
                });
            }
            apiTestCommandDomainService.updateParentCaseExecuteResultByTaskCode(task.getCode());
            emit(TestEventEnums.TEST_AUTOMATIC_EXECUTE.toReactiveId(task.getProductCode()), "TASK_END");
            if (StringUtils.isNotBlank(task.getTestPlanCode()) && null != task.getTestStage()) {
                automaticTaskRepository.updatePlanCaseStatus(task.getCode(), task.getStatus(), task.getTestPlanCode(), task.getTestStage());
                emit(TestEventEnums.TEST_AUTOMATIC_EXECUTE.toReactiveId(task.getTestPlanCode()), "PLAN_END");
            }
            if (Arrays.asList(AutomaticTaskTrigModeEnum.SCHEDULER_RUN, AutomaticTaskTrigModeEnum.SCHEDULER_ONCE).contains(task.getTrigMode())) {
                this.updateScheduler(task.getTaskId(), null);
                automaticSchedulerMsgCommandDomainService.sendMessage(task.getTaskId());
                ExecuteCallbackEvent event = new ExecuteCallbackEvent();
                event.setTaskId(task.getTaskId());
                event.setProductCode(task.getProductCode());
                qcRobotMessageService.sendScheduledFailMessageEvent(event);
            }
            this.onExecuteCallbackEvent(task.getTrigMode(), task.getTaskId(), task.getVersionCode(), task.getTestStage());
        });
    }

    public void retryAutomaticTask(List<String> taskCodeList, UserInfo transactor) {
        List<AutomaticTaskEntityDO> taskList = automaticTaskRepository.selectListByCodeList(taskCodeList).stream()
                .filter(task -> !Arrays.asList(AutomaticStatusEnum.NOT_STARTED, AutomaticStatusEnum.SUBMITTED,
                        AutomaticStatusEnum.IN_PROGRESS).contains(task.getStatus()))
                .filter(task -> !AutomaticTaskTrigModeEnum.DEPLOY.equals(task.getTrigMode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskList)) {
            throw new ServiceException("没有可重试的自动化任务！");
        }
        AUTOMATIC_TASK_EXECUTOR.execute(() -> taskList.forEach(task -> {
            log.info("自动化任务重试：{}", task.getCode());
            doRetryAutomaticTask(task, transactor.toSimpleUser());
        }));
    }

    private void doRetryAutomaticTask(AutomaticTaskEntityDO task, User transactor) {
        task.setStatus(AutomaticStatusEnum.NOT_STARTED);
        task.preCreate(transactor);
        task.setStartTime(new Date());
        automaticTaskRepository.updateByAutomaticTaskEntityDO(task);
        TestcaseExecuteRecordEntityDO caseRecord = new TestcaseExecuteRecordEntityDO();
        caseRecord.setResult(TestPlanCaseStatusEnum.SUBMITTED);
        caseRecord.preCreate(transactor);
        caseRecord.setStartTime(new Date());
        automaticTaskRepository.updateTestcaseStatus(caseRecord, null, task.getCode());
        List<String> caseCodeList = getRetryCaseList(task);
        if (CollectionUtils.isEmpty(caseCodeList)) {
            // 没有可执行用例
            ExecuteCallbackEvent event = new ExecuteCallbackEvent();
            event.setCode(task.getCode());
            event.setTaskId(task.getTaskId());
            event.setType(task.getType());
            event.setStatus(AutomaticStatusEnum.FAIL);
            event.setTrigMode(task.getTrigMode());
            event.setProductCode(task.getProductCode());
            event.setTestPlanCode(task.getTestPlanCode());
            event.setTestStage(task.getTestStage());
            event.setOccurred(new Date());
            this.executeCallback(event);
            return;
        }
        // 提交执行
        AutomaticTaskExecutedEvent event = automaticTaskEventConverter.convert(task);
        event.setAggregateId(event.getCode());
        event.setTestcaseCodeList(caseCodeList);
        event.setTransactor(transactor);
        event.setOccurred(new Date());
        this.execute(event);
    }

    private List<String> getRetryCaseList(AutomaticTaskEntityDO task) {
        List<String> oldList = automaticTaskRepository.selectTestcaseCodeList(task.getCode());
        if (StringUtils.isEmpty(task.getSchedulerCode()) && !AutomaticTaskTrigModeEnum.DEPLOY.equals(task.getTrigMode())) {
            ListAutomaticTaskCaseQuery query = new ListAutomaticTaskCaseQuery();
            query.setTestcaseCodeList(oldList);
            List<TestcaseVO> list = automaticTaskQueryDomainService.listAutomaticTaskCase(query);
            return list.stream().map(TestcaseVO::getCode).collect(Collectors.toList());
        }
        // 全量替换执行用例
        List<TestcaseEntityDO> autoList =
                testcaseRepository.queryCaseListByAutomaticCodes(Collections.singletonList(task.getAutomaticSourceCode()));
        if (CollectionUtils.isEmpty(autoList)) {
            return Collections.emptyList();
        }
        List<String> autoCodeList = autoList.stream().map(TestcaseEntityDO::getCode).collect(Collectors.toList());
        List<String> newList = null;
        if (StringUtils.isNotEmpty(task.getSchedulerCode())) {
            // 定时任务
            List<String> schedulerCodeList = automaticSchedulerRepository.getCaseCodesBySchedulerCode(task.getSchedulerCode());
            newList = schedulerCodeList.stream().filter(autoCodeList::contains).collect(Collectors.toList());
        }
        if (AutomaticTaskTrigModeEnum.DEPLOY.equals(task.getTrigMode())) {
            // 流水线
            List<TmTestPlanCaseEntityDO> planCaseList =
                    tmTestPlanCaseRepository.selectByPlanCodeAndTestStageAndCodeList(task.getTestPlanCode(), task.getTestStage(), autoCodeList);
            newList = planCaseList.stream().map(TmTestPlanCaseEntityDO::getCaseCode).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(newList)) {
            return Collections.emptyList();
        }
        ListAutomaticTaskCaseQuery query = new ListAutomaticTaskCaseQuery();
        query.setTestcaseCodeList(newList);
        List<TestcaseVO> list = automaticTaskQueryDomainService.listAutomaticTaskCase(query);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        newList = list.stream().map(TestcaseVO::getCode).collect(Collectors.toList());
        updateExecuteCaseList(oldList, newList, task);
        return newList;
    }

    private void updateExecuteCaseList(List<String> oldList, List<String> newList, AutomaticTaskEntityDO task) {
        List<String> deleteList = oldList.stream().filter(i -> !newList.contains(i)).collect(Collectors.toList());
        automaticTaskRepository.deleteTaskCase(task.getCode(), deleteList);
        List<String> insertList = newList.stream().filter(i -> !oldList.contains(i)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertList)) {
            TestcaseExecuteRecordEntityDO record = automaticTaskDomainConverter.converter(task);
            record.setResult(TestPlanCaseStatusEnum.SUBMITTED);
            List<List<String>> partition = Lists.partition(insertList, 200);
            for (List<String> list : partition) {
                testcaseRepository.insertBatchExecuteRecord(record, list);
            }
        }
    }

    private void pipelineRetryTask(AutomaticTaskCDExecutedEvent event) {
        ExecuteCallbackCDEvent callbackEvent = new ExecuteCallbackCDEvent();
        callbackEvent.setAggregateId(event.getAggregateId());
        callbackEvent.setTaskId(event.getTaskId());
        callbackEvent.setFlowCode(event.getFlowCode());
        callbackEvent.setStatus(AutomaticStatusEnum.SUCCESS);
        callbackEvent.setTransactor(event.getTransactor());
        callbackEvent.setNodeCode(event.getNodeCode());
        if (StringUtils.isEmpty(event.getTaskId())) {
            log.warn("taskId is empty >>> {}", event.getAggregateId());
            pipelineRpcService.handleExecuteCallbackCDEvent(callbackEvent);
            return;
        }
        List<AutomaticTaskEntityDO> taskList;
        if (CollectionUtils.isNotEmpty(event.getTaskCodeList())) {
            taskList = automaticTaskRepository.selectListByCodeList(event.getTaskCodeList());
        } else {
            taskList = automaticTaskRepository.selectByTaskIdAndVersionCode(event.getTaskId(), null);
        }
        List<AutomaticTaskEntityDO> retryTaskList = taskList.stream()
                .filter(task -> Arrays.asList(AutomaticStatusEnum.FAIL, AutomaticStatusEnum.TERMINATION,
                        AutomaticStatusEnum.ERROR).contains(task.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(retryTaskList)) {
            log.warn("retryTaskList is empty >>> {}", event.getTaskId());
            List<TestPlanCaseStatusEnum> statusList = automaticTaskRepository.getTestcaseResultByTaskId(event.getTaskId());
            Map<TestPlanCaseStatusEnum, Long> statusMap =
                    statusList.stream().collect(Collectors.groupingBy(e -> e, Collectors.counting()));
            callbackEvent.setTotalCaseCount(statusList.size());
            callbackEvent.setPassedCaseCount(statusMap
                    .getOrDefault(TestPlanCaseStatusEnum.PASSED, 0L)
                    .intValue()
                    + statusMap.getOrDefault(TestPlanCaseStatusEnum.SUCCESS, 0L).intValue());
            callbackEvent.setFailedCaseCount(
                    statusMap.getOrDefault(TestPlanCaseStatusEnum.FAILED, 0L).intValue());
            callbackEvent.setOtherCaseCount(callbackEvent.getTotalCaseCount()
                    - callbackEvent.getPassedCaseCount()
                    - callbackEvent.getFailedCaseCount());
            pipelineRpcService.handleExecuteCallbackCDEvent(callbackEvent);
            return;
        }
        callbackEvent.setStatus(AutomaticStatusEnum.IN_PROGRESS);
        pipelineRpcService.handleExecuteCallbackCDEvent(callbackEvent);
        redisService.setKey(preStrFlowCode + event.getTaskId(), event.getFlowCode(), 5L, TimeUnit.HOURS);
        redisService.setKey(
                preStrTransactor + event.getTaskId(), JSON.toJSONString(event.getTransactor()), 5L, TimeUnit.HOURS);
        if (StringUtils.isNotEmpty(event.getDubboTag())) {
            redisService.setKey("DubboTag::" + event.getTaskId(), event.getDubboTag(), 5L, TimeUnit.HOURS);
        }
        redisService.setKey(NODE_CODE + event.getTaskId(), event.getNodeCode(), 5L, TimeUnit.HOURS);
        retryTaskList.forEach(task -> {
            log.info("流水线自动化重试：{}", task.getCode());
            doRetryAutomaticTask(task, event.getTransactor());
        });
    }
}
