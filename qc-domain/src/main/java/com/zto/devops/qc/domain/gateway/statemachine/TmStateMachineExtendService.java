package com.zto.devops.qc.domain.gateway.statemachine;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;

public interface TmStateMachineExtendService {

    void checkPermissions(String planCode,ReportType reportType, User user);

    void checkPermissionByProduct(String productCode, User user);

    boolean canReportEdit(String productCode, User user);

    void checkHasAccessReport(String planCode, User transactor);

    void checkHasSmokeReport(String planCode, User transactor);

}
