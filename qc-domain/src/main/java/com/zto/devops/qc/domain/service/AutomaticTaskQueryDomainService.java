package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.rpc.project.SimpleListVersionVO;
import com.zto.devops.qc.client.model.rpc.project.SimpleVersionListQuery;
import com.zto.devops.qc.client.model.rpc.project.SimpleVersionVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListAutomaticTaskCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListExecuteEnvQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageAutomaticTaskGroupQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.AmazonS3ConfigVO;
import com.zto.devops.qc.domain.converter.TestcaseDomainConverter;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.repository.AutomaticSourceRecordRepository;
import com.zto.devops.qc.domain.gateway.repository.IAutomaticTaskRepository;
import com.zto.devops.qc.domain.gateway.repository.TestcaseRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AutomaticTaskQueryDomainService {

    @Autowired
    private AutomaticSourceRecordRepository automaticSourceRecordRepository;
    @Autowired
    private IAutomaticTaskRepository automaticTaskRepository;
    @Autowired
    private TestcaseRepository testcaseRepository;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;
    @Autowired
    private IProjectRpcService iProjectRpcService;

    @Autowired
    private TestcaseDomainConverter testcaseDomainConverter;

    public PageAutomaticTaskGroupVO pageAutomaticTaskGroup(PageAutomaticTaskGroupQuery query) {
        log.info("PageAutomaticTaskGroupQuery >>> {}", JSON.toJSONString(query));

        //父任务id列表
        PageInfo<String> pageInfo = automaticTaskRepository.selectParentTaskIdList(query);
        List<String> taskIdList = pageInfo.getList();
        if (CollectionUtils.isEmpty(taskIdList)) {
            return PageAutomaticTaskGroupVO.buildSelf(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), new ArrayList<>());
        }

        //任务列表(关联用例）
        List<AutomaticTaskVO> taskList = automaticTaskRepository.selectTaskList(taskIdList);
        if (CollectionUtils.isEmpty(taskList)) {
            return PageAutomaticTaskGroupVO.buildSelf(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), new ArrayList<>());
        }

        //任务分组
        List<AutomaticTaskGroupVO> groupList = AutomaticTaskGroupVO.buildListByGroup(taskList);
        if (CollectionUtils.isNotEmpty(groupList)) {
            //任务数据填充
            groupList.forEach(groupVO -> {
                String versionName = getSimpleVersionName(groupVO.getVersionCode());
                //子任务填充文件名
                groupVO.getChildren().forEach(child -> {
                    child.setExecLogFile(getFullFilepath(child.getExecLogFile()));
                    child.setReportFile(getFullFilepath(child.getReportFile()));
                    child.setErrorLogFile(getFullFilepath(child.getErrorLogFile()));
                    child.setVersionName(versionName);
                });
                groupVO.setVersionName(versionName);
                groupVO.setEnv(groupVO.getChildren().get(0).getEnv());
            });
        }
        return PageAutomaticTaskGroupVO.buildSelf(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), groupList);
    }

    public List<TestcaseVO> queryExecutableCaseList(List<String> testcaseCodeList) {
        ListAutomaticTaskCaseQuery query = new ListAutomaticTaskCaseQuery();
        query.setTestcaseCodeList(testcaseCodeList);
        //查询可执行的用例（有登记库源与父模块的用例）
        return this.listAutomaticTaskCase(query);
    }

    public List<TestcaseVO> listAutomaticTaskCase(ListAutomaticTaskCaseQuery query) {
        log.info("ListAutomaticTaskCaseQuery >>> {}", query.getTestcaseCodeList());
        //查询当前自动化用例
        List<TestcaseEntityDO> testcaseDOList = testcaseRepository.selectAutomaticCaseByCodeList(query.getTestcaseCodeList());
        if (CollectionUtils.isEmpty(testcaseDOList)) {
            return Collections.emptyList();
        }
        List<String> moduleCodeList = testcaseDOList.parallelStream()
                .filter(en -> StringUtils.isNotEmpty(en.getPath()))
                .flatMap(en -> Arrays.stream(en.getPath().split("\\.")))
                .distinct().collect(Collectors.toList());
        List<String> filterList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(moduleCodeList)) {
            //查询testcase表中用例code的父模块为未删除未停用的父模块
            List<TestcaseEntityDO> list = testcaseRepository.selectByCodeList(moduleCodeList);
            filterList.addAll(list.parallelStream()
                    .filter(en -> !en.getEnable() || TestcaseStatusEnum.DISABLE.equals(en.getStatus()))
                    .map(TestcaseEntityDO::getCode)
                    .collect(Collectors.toList()));
        }
        //获取自动化记录源code
        List<String> automaticSourceCodeList = testcaseDOList.parallelStream()
                .map(TestcaseEntityDO::getAutomaticSourceCode)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(automaticSourceCodeList)) {
            List<AutomaticSourceRecordEntityDO> recordEntityDOList = automaticSourceRecordRepository.selectByCodeList(automaticSourceCodeList, Boolean.FALSE);
            filterList.addAll(recordEntityDOList.parallelStream().map(AutomaticSourceRecordEntityDO::getCode).collect(Collectors.toList()));
        }
        return testcaseDOList.parallelStream()
                .filter(en -> !filterList.contains(en.getAutomaticSourceCode())
                        && Arrays.stream(en.getPath().split("\\.")).noneMatch(filterList::contains))
                .map(en -> testcaseDomainConverter.converter(en))
                .collect(Collectors.toList());
    }

    /**
     * 获取完整文件路径
     *
     * @param filepath 文件路径
     * @return 完整文件路径
     */
    private String getFullFilepath(String filepath) {
        AmazonS3ConfigVO configVO = qcConfigBasicService.getAmazonS3Config();
        String callbackBucketName = qcConfigBasicService.getCallbackBucketName();
        if (StringUtils.isBlank(filepath)) {
            return null;
        }
        return MessageFormat.format("{0}/{1}/{2}", configVO.getEndPoint(), callbackBucketName, filepath);
    }

    /**
     * 查询版本名称
     *
     * @param versionCode 版本code
     * @return 版本名称
     */
    private String getSimpleVersionName(String versionCode) {
        String result = "";
        if (StringUtils.isBlank(versionCode)) {
            return result;
        }
        SimpleVersionListQuery simpleVersionListQuery = new SimpleVersionListQuery();
        simpleVersionListQuery.setCode(Collections.singletonList(versionCode));
        try {
            SimpleListVersionVO versionVOList = iProjectRpcService.simpleVersionListQuery(simpleVersionListQuery);
            if (null != versionVOList && CollectionUtils.isNotEmpty(versionVOList.getSimpleVersionVOList())) {
                SimpleVersionVO versionVO = versionVOList.getSimpleVersionVOList().get(0);
                if (null != versionVO) {
                    result = versionVO.getName();
                }
            }
        } catch (Exception e) {
            log.error("SimpleVersionQuery ERROR {}", versionCode, e);
        }
        return result;
    }

    public List<ExecuteTagVO> listExecuteEnv(ListExecuteEnvQuery query) {
        return automaticTaskRepository.listExecuteEnv(query);
    }
}
