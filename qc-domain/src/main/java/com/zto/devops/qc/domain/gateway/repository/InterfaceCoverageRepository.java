package com.zto.devops.qc.domain.gateway.repository;

import com.github.pagehelper.PageInfo;
import com.zto.devops.qc.client.model.dto.InterfaceCoverageEntityDO;
import com.zto.devops.qc.client.service.coverage.model.req.PageInterfaceCoverageInfoReq;
import com.zto.devops.qc.client.service.coverage.model.req.ZcatMqBodyReq;
import com.zto.devops.qc.client.service.coverage.model.resp.InterfaceCoverageInfoResp;
import com.zto.devops.qc.domain.model.coverage.ZcatMetricsVO;

import java.math.BigDecimal;
import java.util.List;

public interface InterfaceCoverageRepository {

    PageInfo<InterfaceCoverageInfoResp> queryPageInterfaceCoverageInfo(PageInterfaceCoverageInfoReq req);

    BigDecimal queryInterfaceCoverageRate(String commitId);

    Integer queryInterfaceCoverageCountByCommitId(String commitId);

    List<InterfaceCoverageEntityDO> queryInterfaceCoverageListByCommitId(String commitId);

    void batchSave(List<InterfaceCoverageEntityDO> list);

    void deleteByCommitId(String commitId);

    List<InterfaceCoverageEntityDO> queryAllInterfaceCoverages(String appId);

    void updateByZcatMetricKey(ZcatMetricsVO vo, String appId);

    void updateIsCoveredByZcatMetricKey(ZcatMqBodyReq req);

    List<InterfaceCoverageEntityDO> queryInterfaceCoverageList(String versionCode);

    List<InterfaceCoverageEntityDO> queryInterfacesAnnotations(String versionCode, String interfaceMethodType);
}
