package com.zto.devops.qc.domain.service;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.common.util.time.DateUtil;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.constants.InterfaceCoverageConstant;
import com.zto.devops.qc.client.enums.constants.GroupTypeEnum;
import com.zto.devops.qc.client.enums.rpc.FlowLaneTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.GenerateTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.MethodTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.devops.qc.client.model.dto.InterfaceCoverageEntityDO;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.rpc.pipeline.ApplicationVO;
import com.zto.devops.qc.client.model.rpc.pipeline.query.FindOssFileUrlQuery;
import com.zto.devops.qc.client.model.rpc.project.VersionInfoVO;
import com.zto.devops.qc.client.model.rpc.project.VersionVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoveragePublishVO;
import com.zto.devops.qc.client.service.coverage.model.req.PageInterfaceCoverageInfoReq;
import com.zto.devops.qc.client.service.coverage.model.req.QueryInterfaceCoverageRateReq;
import com.zto.devops.qc.client.service.coverage.model.req.ZcatMqBodyReq;
import com.zto.devops.qc.client.service.coverage.model.resp.InterfaceCoverageInfoResp;
import com.zto.devops.qc.client.service.coverage.model.resp.InterfaceCoverageRateResp;
import com.zto.devops.qc.client.service.coverage.model.resp.VerifyGenerateConditionResp;
import com.zto.devops.qc.client.service.coverage.model.resp.VersionInterfaceResp;
import com.zto.devops.qc.domain.gateway.http.HttpService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.CoverageRepository;
import com.zto.devops.qc.domain.gateway.repository.InterfaceCoverageRepository;
import com.zto.devops.qc.domain.gateway.repository.MethodDependenceRepository;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.domain.gateway.scan.ScanService;
import com.zto.devops.qc.domain.gateway.util.UncompressFileUtilService;
import com.zto.devops.qc.domain.gateway.zcat.ZCatService;
import com.zto.devops.qc.domain.model.coverage.Metrics;
import com.zto.devops.qc.domain.model.coverage.ZcatMetricsVO;
import com.zto.devops.qc.domain.model.scan.ScanObject;
import com.zto.devops.qc.domain.util.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/11/15 15:55
 */
@Service
@Slf4j
public class InterfaceCoverageDomainService extends BaseDomainService {

    @Autowired
    private RedisService redisService;
    @Autowired
    private IProjectRpcService projectRpcService;
    @Autowired
    private IPipelineRpcService pipelineRpcService;
    @Autowired
    @Lazy
    private CoverageDomainService coverageDomainService;
    @Autowired
    private HttpService httpService;
    @Autowired
    private UncompressFileUtilService uncompressFileUtilService;
    @Autowired
    private ScanService scanService;
    @Autowired
    private CoverageRepository coverageRepository;
    @Autowired
    private InterfaceCoverageRepository interfaceCoverageRepository;
    @Autowired
    private MethodDependenceRepository methodDependenceRepository;
    @Autowired
    private ZCatService zCatService;

    private List<String> defaultEntryTypes = Arrays.asList("JOB", "HTTP", "DUBBO", "MQ");

    private static final String AGENT_PREFIX = "AGENT_IP";

    private static final String ACCEPTING_VERSIONS = "ACCEPTING_VERSIONS";

    private static ThreadPoolExecutor THREAD_POOL_GET_CALLS_NUM = new ThreadPoolExecutor(1, 10, 4, TimeUnit.MINUTES,
            new ArrayBlockingQueue<Runnable>(100), ThreadFactoryBuilder.create().setNamePrefix("GET_CALLS_NUM-").build());

    private static ThreadPoolExecutor THREAD_POOL_GET_CALLS_NUM_EXECUTE = new ThreadPoolExecutor(1, 20, 4, TimeUnit.MINUTES,
            new ArrayBlockingQueue<Runnable>(100), ThreadFactoryBuilder.create().setNamePrefix("GET_CALLS_NUM_EXECUTE-").build());

    private static ThreadPoolExecutor THREAD_POOL_BATCH_DELETE_EXECUTE = new ThreadPoolExecutor(1, 20, 10, TimeUnit.MINUTES,
            new ArrayBlockingQueue<Runnable>(100), ThreadFactoryBuilder.create().setNamePrefix("BATCH_DELETE_EXECUTE-").build());

    public void interfaceCoverageHandle(CoveragePublishVO entity, User user) {
        long start = System.currentTimeMillis();
        log.info("Start interfaceCoverageHandle.appId : {}, versionCode : {}, commitId : {}", entity.getAppId(), entity.getVersionCode(), entity.getCommitId());
        int count = methodDependenceRepository.queryCountByCommitId(entity.getCommitId());
        if (count > 0) {
            log.info("End interfaceCoverageHandle. Not first deploy.appId : {}, versionCode : {}", entity.getAppId(), entity.getVersionCode());
            return;
        }
        VersionVO version = projectRpcService.findVersionQuery(entity.getVersionCode());
        if (null == version) {
            log.warn("interfaceCoverageHandle findVersionQuery result empty.appId : {}, versionCode : {}", entity.getAppId(), entity.getVersionCode());
            return;
        }
        CoverageRecordGenerateParameter parameter;
        VerifyGenerateConditionResp resp;
        // 获取应用配置信息
        ApplicationVO applicationDetail;
        try {
            applicationDetail = coverageDomainService.getApplicationDetail(entity.getAppId(), version.getProductCode());
            // 只处理java应用
            if (null == applicationDetail || !Arrays.asList("1", "30", "44").contains(applicationDetail.getApplicationTypeCode())) {
                log.warn("interfaceCoverageHandle getApplicationDetail result empty or not java app.appId : {}", entity.getAppId());
                return;
            }
            // 未配置扫描路径，不处理
            if (MapUtils.isEmpty(applicationDetail.getTypeInput()) || Objects.isNull(applicationDetail.getTypeInput().get("staticCodeScanIncludes"))
                    || StringUtil.isEmpty(applicationDetail.getTypeInput().get("staticCodeScanIncludes").toString())
                    || Objects.isNull(applicationDetail.getTypeInput().get("staticCodeScanExcludes"))
                    || StringUtil.isEmpty(applicationDetail.getTypeInput().get("staticCodeScanExcludes").toString())) {
                log.warn("interfaceCoverageHandle getApplicationDetail not have scan config.appId : {}", entity.getAppId());
                return;
            }
        } catch (Exception e) {
            log.warn("调用Pipeline 获取应用配置信息 异常。appId : {}", entity.getAppId(), e);
            return;
        }
        try {
            // 校验app是否需要下载并扫描制品、生成覆盖率报告
            VersionInfoVO versionInfoVO = coverageDomainService.checkVersionInfo(entity.getVersionCode());
            if (null == versionInfoVO) {
                log.warn("interfaceCoverageHandle checkVersionInfo result empty.appId : {}, versionCode : {}", entity.getAppId(), entity.getVersionCode());
                return;
            }
            parameter = buildParameter(entity, user, version.getProductCode(), applicationDetail.getProductName());
            resp = coverageDomainService.verifyGenerateCondition(parameter, versionInfoVO);
            if (CollectionUtil.isEmpty(resp.getDataList())) {
                log.warn("interfaceCoverageHandle verifyGenerateCondition resp empty.appId : {}, versionCode : {}", entity.getAppId(), entity.getVersionCode());
                return;
            }
            if (CollectionUtil.isEmpty(resp.getAppIdList())) {
                log.warn("interfaceCoverageHandle verifyGenerateCondition result getAppIdList empty.appId : {}, versionCode : {}", entity.getAppId(), entity.getVersionCode());
                return;
            }
        } catch (Exception e) {
            log.warn("校验app是否需要下载并扫描制品、生成覆盖率报告 异常。appId : {}, versionCode : {}", entity.getAppId(), entity.getVersionCode(), e);
            return;
        }
        // 部署完成，下载并扫描制品，并落库接口依赖表
        String path = InterfaceCoverageConstant.LOCAL_PATH + entity.getVersionCode() + "-" + entity.getAppId() + "-" + entity.getCommitId() + "/";
        try {
            String fileNameJar = downloadArtifact(entity.getPackageName(), path);
            ScanObject scanObject = buildScanObject(path + fileNameJar, applicationDetail);
            boolean scanResult = scanService.staticScanAndSave(scanObject, entity, parameter);
            if (!scanResult) {
                log.warn("staticScanAndSave false. appId : {}, versionCode : {}", entity.getAppId(), entity.getVersionCode());
                return;
            }
        } catch (Exception e) {
            log.warn("部署完成，下载并扫描制品，并落库接口依赖表 异常。appId : {}, versionCode : {}", entity.getAppId(), entity.getVersionCode(), e);
            return;
        } finally {
            FileUtils.deleteDir(new File(path));
        }
        try {
            // 当前commitId，第一次部署，主动生成覆盖率报告
            coverageDomainService.generateCoverageReportByValidData(parameter, resp.getDataList());
        } catch (Exception e) {
            log.warn("应用：{}， 版本：{}，当前commitId:{}，第一次部署，主动生成覆盖率报告 异常。", entity.getAppId(), entity.getVersionCode(), entity.getCommitId(), e);
            return;
        }
        log.info("End interfaceCoverageHandle success. appId : {}, versionCode : {}, commitId : {}, use : [{}]", entity.getAppId(), entity.getVersionCode(), entity.getCommitId(), System.currentTimeMillis() - start);
    }

    private ScanObject buildScanObject(String jarPath, ApplicationVO applicationDetail) {
        ScanObject scanObject = new ScanObject();
        scanObject.setJarPath(jarPath);
        scanObject.setIncludePackages(Arrays.asList(String.valueOf(applicationDetail.getTypeInput().get("staticCodeScanIncludes"))));
        scanObject.setExcludePackages(Arrays.asList(String.valueOf(applicationDetail.getTypeInput().get("staticCodeScanExcludes")).replace(" ", "").replace("\"", "").split(",")));
        scanObject.setExcludeLibs((Objects.nonNull(applicationDetail.getTypeInput().get("staticCodeScanExcludeDependencies"))
                && StringUtil.isNotEmpty(applicationDetail.getTypeInput().get("staticCodeScanExcludeDependencies").toString())) ? Arrays.asList(String.valueOf(applicationDetail.getTypeInput().get("staticCodeScanExcludeDependencies")).replace(" ", "").replace("\"", "").split(",")) : Collections.emptyList());
        scanObject.setEntryTypes((Objects.nonNull(applicationDetail.getTypeInput().get("staticCodeScanEntryType"))
                && ((List<String>) applicationDetail.getTypeInput().get("staticCodeScanEntryType")).size() > 0) ? (List<String>) applicationDetail.getTypeInput().get("staticCodeScanEntryType") : defaultEntryTypes);
        scanObject.setScanType("analyzer");
        return scanObject;
    }

    private CoverageRecordGenerateParameter buildParameter(CoveragePublishVO entity, User user, String productCode, String productName) {
        CoverageRecordGenerateParameter parameter = new CoverageRecordGenerateParameter();
        parameter.setProductCode(productCode);
        parameter.setProductName(productName);
        parameter.setDiffType(DiffTypeEnum.INCREMENT);
        parameter.setRecordType(RecordTypeEnum.BRANCH);
        parameter.preCreate(user);
        parameter.setVersionCode(entity.getVersionCode());
        parameter.setVersionName(entity.getVersionName());
        parameter.setAppIdList(Arrays.asList(entity.getAppId()));
        parameter.setGenerateType(GenerateTypeEnum.AUTO);
        parameter.setTaskId(entity.getVersionCode() + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss"));
        parameter.setFlowLaneType(FlowLaneTypeEnum.FLOW_TEST.name());
        return parameter;
    }

    /**
     * 解压制品
     *
     * @param fileName
     * @param classPath
     * @return
     */
    private String downloadArtifact(String fileName, String classPath) throws Exception {
        FindOssFileUrlQuery query = new FindOssFileUrlQuery();
        query.setFileName(fileName);
        String downloadUrl = pipelineRpcService.downloadUrl(query);
        if (StringUtil.isEmpty(downloadUrl)) {
            throw new ServiceException("下载包地址为空");
        }
        String fileNameGz = downloadClassTarGz(classPath, downloadUrl);
        String fileNameJar = decompressTarGz(classPath, fileNameGz);
        return fileNameJar;
    }

    /**
     * 查询接口列表
     *
     * @param req
     * @return
     */
    public PageInfo<InterfaceCoverageInfoResp> queryPageInterfaceCoverageInfo(PageInterfaceCoverageInfoReq req) {
        if (StringUtil.isEmpty(req.getCommitId())) {
            CoveragePublishVO entity = new CoveragePublishVO();
            entity.setAppId(req.getAppId());
            entity.setVersionCode(req.getVersionCode());
            CoveragePublishVO coveragePublishVO = coverageRepository.getLatestPublishRecordByEntity(entity);
            req.setCommitId(coveragePublishVO.getCommitId());
        }
        return interfaceCoverageRepository.queryPageInterfaceCoverageInfo(req);
    }

    /**
     * 查询接口覆盖率
     *
     * @param req
     * @return
     */
    public InterfaceCoverageRateResp queryInterfaceCoverageRate(QueryInterfaceCoverageRateReq req) {
        InterfaceCoverageRateResp resp = new InterfaceCoverageRateResp();
        resp.setAppId(req.getAppId());
        resp.setVersionCode(req.getVersionCode());
        CoveragePublishVO entity = new CoveragePublishVO();
        entity.setAppId(req.getAppId());
        entity.setVersionCode(req.getVersionCode());
        CoveragePublishVO coveragePublishVO = coverageRepository.getLatestPublishRecordByEntity(entity);
        if (coveragePublishVO == null) {
            resp.setInterfaceCoverageRate(null);
        } else {
            BigDecimal rate = interfaceCoverageRepository.queryInterfaceCoverageRate(coveragePublishVO.getCommitId());
            resp.setInterfaceCoverageRate(rate);
        }
        return resp;
    }

    /**
     * 根据同步zcat的测试数据，修改接口测试结果状态
     *
     * @param req
     * @return
     */
    public Boolean syncInterfaceTestedInfo(ZcatMqBodyReq req) {
        String agentIpKey = AGENT_PREFIX + "_" + req.getAppId() + "_" + req.getLocation();
        if (!redisService.hasKey(agentIpKey)) {
            log.warn("IP对应的versionCode没找到！agentIpKey ： {}", agentIpKey);
            return false;
        }
        String versionCode = redisService.getKey(agentIpKey);
        if (StringUtil.isEmpty(versionCode)) {
            log.warn("对应的versionCode没找到！agentIpKey ： {}", agentIpKey);
            return false;
        }
        req.setVersionCode(versionCode);
        interfaceCoverageRepository.updateIsCoveredByZcatMetricKey(req);
        return true;
    }

    /**
     * 下载压缩包
     *
     * @param classPath
     * @param downloadUrl
     * @return
     */
    private String downloadClassTarGz(String classPath, String downloadUrl) {
        File file = new File(classPath);
        if (!file.exists() && !file.isDirectory()) {
            file.mkdirs();
        }
        String fileName = httpService.downloadFromUrl(downloadUrl, classPath);
        if (StringUtil.isEmpty(fileName)) {
            throw new ServiceException("下载包文件失败");
        }
        return fileName;
    }

    /**
     * 解压tar包
     *
     * @param classPath
     * @param gzFileName
     * @return
     * @throws Exception
     */
    private String decompressTarGz(String classPath, String gzFileName) throws Exception {
        return uncompressFileUtilService.decompressTarGz(classPath + gzFileName, classPath);
    }

    public void cacheAcceptingVersions(List<String> versionCodes) {
        if (CollectionUtil.isEmpty(versionCodes)) {
            return;
        }
        for (String versionCode : versionCodes) {
            redisService.opsForZSetAddLast(ACCEPTING_VERSIONS, versionCode);
        }
    }

    public void batchDelete() {
        THREAD_POOL_BATCH_DELETE_EXECUTE.execute(() -> {
            if (redisService.hasKey(ACCEPTING_VERSIONS)) {
                Set<String> acceptingVersions = redisService.opsForZSetRang(ACCEPTING_VERSIONS, 0, -1);
                if (CollectionUtil.isEmpty(acceptingVersions)) {
                    log.info("There are no accepting versions to delete.");
                    return;
                }
                log.info("Begin to batchDelete. size : {}", acceptingVersions.size());
                for (String acceptedVersion : acceptingVersions) {
                    scanService.batchDeleteHistoryDataByVersionCode(acceptedVersion);
                }
                redisService.delete(ACCEPTING_VERSIONS);
                log.info("End to batchDelete. ");
            } else {
                log.info("There are no key.");
            }
        });
    }

    private void getInterfaceCallsNumber() {
        Map<String, Set<Metrics>> appIdDataMap = this.getInterfaceCoverageData("");
        if (MapUtils.isEmpty(appIdDataMap)) {
            return;
        }
        queryZcatAndSave(appIdDataMap);
    }

    public Map<String, Set<Metrics>> getInterfaceCoverageData(String appId) {
        List<InterfaceCoverageEntityDO> entityDOList = interfaceCoverageRepository.queryAllInterfaceCoverages(appId);
        if (CollectionUtil.isEmpty(entityDOList)) {
            return null;
        }
        Map<String, Set<Metrics>> appIdDataMap = new HashMap<>();
        for (InterfaceCoverageEntityDO entityDO : entityDOList) {
            String group = getGroup(entityDO.getInterfaceMethodType());
            if (StringUtil.isEmpty(group)) {
                continue;
            }
            Metrics vo = new Metrics(group, entityDO.getZcatMetricKey());
            appIdDataMap.computeIfAbsent(entityDO.getAppId(), k -> new HashSet<>()).add(vo);
        }
        return appIdDataMap;
    }

    public void queryZcatAndSave(Map<String, Set<Metrics>> appIdDataMap) {
        String queryDate = com.zto.devops.qc.domain.util.DateUtil.getYesterday(new Date());
        for (Map.Entry<String, Set<Metrics>> appIdData : appIdDataMap.entrySet()) {
            THREAD_POOL_GET_CALLS_NUM_EXECUTE.execute(() -> {
                List<ZcatMetricsVO> metricsVOS = queryZcat(appIdData, queryDate);
                if (CollectionUtil.isNotEmpty(metricsVOS)) {
                    saveAndCache(appIdData.getKey(), metricsVOS, queryDate);
                }
            });
        }
    }

    public String getGroup(String type) {
        if (type.equals(MethodTypeEnum.DUBBO.name())) {
            return GroupTypeEnum.RPC_DUBBO_SERVICE.getValue();
        }
        if (type.equals(MethodTypeEnum.HTTP.name())) {
            return GroupTypeEnum.RPC_HTTP_SERVICE.getValue();
        }
        if (type.equals(MethodTypeEnum.MQ.name())) {
            return GroupTypeEnum.ZMS_MQ_LISTENER.getValue();
        }
        if (type.equals(MethodTypeEnum.JOB.name())) {
            return GroupTypeEnum.SCHEDULE_ZSS.getValue();
        }
        return "";
    }

    public List<ZcatMetricsVO> queryZcat(Map.Entry<String, Set<Metrics>> appIdData, String queryDate) {
        try {
            Map<String, Object> reqMap = new HashMap<>();
            reqMap.put("appId", appIdData.getKey());
            reqMap.put("date", queryDate);
            reqMap.put("metrics", appIdData.getValue());
            return zCatService.getInterfaceCallsNumber(reqMap);
        } catch (Exception e) {
            log.error("getInterfaceCallsNumber query error. appId : {}", e, appIdData.getKey());
            return null;
        }
    }

    public void saveAndCache(String appId, List<ZcatMetricsVO> metricsVOS, String queryDate) {
        for (ZcatMetricsVO metricsVO : metricsVOS) {
            try {
                interfaceCoverageRepository.updateByZcatMetricKey(metricsVO, appId);
                String countKey = InterfaceCoverageConstant.INTERFACE_CALLS_COUNT + "_" + appId + "_" + metricsVO.getMetricKey() + "_" + queryDate;
                String failKey = InterfaceCoverageConstant.INTERFACE_CALLS_FAIL + "_" + appId + "_" + metricsVO.getMetricKey() + "_" + queryDate;
                long remainMinutes = com.zto.devops.qc.domain.util.DateUtil.getTodayRemainMinutes();
                redisService.setKey(countKey, String.valueOf(metricsVO.getCount()), remainMinutes, TimeUnit.MINUTES);
                redisService.setKey(failKey, String.valueOf(metricsVO.getFail()), remainMinutes, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("getInterfaceCallsNumber saveAndCache error. metricKey : {}", metricsVO.getMetricKey(), e);
            }
        }
    }

    public void getInterfaceCallsNumberFromZcat() {
        THREAD_POOL_GET_CALLS_NUM.execute(this::getInterfaceCallsNumber);
    }

    public VersionInterfaceResp queryInterfaceRelatedToVersion(String versionCode) {
        if (StringUtil.isEmpty(versionCode)) {
            return new VersionInterfaceResp();
        }
        List<InterfaceCoverageEntityDO> entities = interfaceCoverageRepository.queryInterfacesAnnotations(versionCode, "DUBBO");
        if (CollectionUtil.isEmpty(entities)) {
            return new VersionInterfaceResp();
        }
        Set<String> paths = entities.stream()
                .filter(e -> StringUtil.isNotEmpty(e.getInterfaceMethodAnnotation()))
                .map(e -> {
                    JSONObject obj = JSONObject.parseObject(e.getInterfaceMethodAnnotation());
                    if (obj.containsKey("name")) {
                        return obj.getString("name");
                    }
                    return "";
                }).collect(Collectors.toList()).stream().filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        VersionInterfaceResp resp = new VersionInterfaceResp();
        resp.setPaths(new ArrayList<>(paths));
        return resp;
    }

}
