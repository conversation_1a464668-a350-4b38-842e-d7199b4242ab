package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.dto.AttachmentEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseAttachmentAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseAttachmentRemovedEvent;
import com.zto.devops.qc.domain.model.TestcaseAttachment;

import java.util.List;

public interface TestcaseAttachmentRepository {

    /**
     * 上传测试用例的附件
     *
     * @param event
     */
    void addTestcaseAttachment(TestcaseAttachmentAddedEvent event);

    /**
     * 查询业务code附件数量
     *
     * @param businessCode
     * @return
     */
    int selectCountByCaseCodeList(String businessCode);

    /**
     * 回溯测试用例的附件
     *
     * @param s
     * @return
     */
    TestcaseAttachment loadFormDb(String s);

    /**
     * 根据用例集合查询附件信息
     *
     * @param caseCodes
     * @return
     */
    List<AttachmentEntityDO> selectTestcaseAttachmentByCaseCodeList(List<String> caseCodes);

    /**
     * 用例添加附件 - 复制用例使用
     *
     * @param entityDO
     */
    void insertSelective(AttachmentEntityDO entityDO);

    void removeTestcaseAttachment(TestcaseAttachmentRemovedEvent event);

}
