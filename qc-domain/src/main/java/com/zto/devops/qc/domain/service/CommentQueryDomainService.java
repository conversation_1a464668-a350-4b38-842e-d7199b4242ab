package com.zto.devops.qc.domain.service;

import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import com.zto.devops.qc.client.service.comment.model.ListCommentsByBusinessCodeReq;
import com.zto.devops.qc.domain.gateway.repository.CommentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CommentQueryDomainService {

    @Autowired
    private CommentRepository commentRepository;

    public List<CommentVO> queryCommentVOS(ListCommentsByBusinessCodeReq req) {
        return commentRepository.queryCommentByBusinessCode(req.getBusinessCode());
    }

}
