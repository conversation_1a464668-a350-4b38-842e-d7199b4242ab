package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.testmanager.report.OperationEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.message.TestPlanOrReportMsg;
import com.zto.devops.qc.client.model.testmanager.report.command.*;
import com.zto.devops.qc.client.model.testmanager.report.entity.*;
import com.zto.devops.qc.client.model.testmanager.report.event.*;
import com.zto.devops.qc.domain.converter.TmTestReportDomainConverter;
import com.zto.devops.qc.domain.gateway.message.ITestReportOrPlanMsgAuditor;
import com.zto.devops.qc.domain.gateway.repository.*;
import com.zto.devops.qc.domain.gateway.statemachine.TmStateMachineExtendService;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TmTestReportCommandDomainService extends BaseDomainService {

    @Autowired
    private TmStateMachineExtendService tmStateMachineExtendService;

    @Autowired
    private TmTestReportDomainConverter tmTestReportDomainConverter;

    @Autowired
    private ITmTestReportRepository tmTestReportRepository;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private IEmailRepository iEmailRepository;

    @Autowired
    private ITestReportOrPlanMsgAuditor testReportOrPlanMsgAuditor;

    @Autowired
    private TmModuleRepository tmModuleRepository;

    @Autowired
    private TmStateMachineExtendManager tmStateMachineExtendManager;

    @Autowired
    private CoverageRepository coverageRepository;

    @Autowired
    private ReportMqSender reportMqSender;

    @Autowired
    private QcVersionQualityMetricsCommandDomainService metricsCommandDomainService;

    public void addTmAccessReportCommand(AddTmAccessReportCommand command) {
        log.info("Command_AddTmAccessReportCommand :{}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        TmAccessReportAddEvent event = tmTestReportDomainConverter.converter(command);
        checkReportType(event.getPlanCode(), event.getReportType());
        tmTestReportRepository.saveTmAccessReport(event);
        apply(event);
    }

    public void editTmAccessReportCommand(EditTmAccessReportCommand command) {
        log.info("Command_EditTmAccessReportCommand:{}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        TmAccessReportEditEvent event = tmTestReportDomainConverter.converter(command);
        tmTestReportRepository.updateTmAccessReport(event);
        apply(event);
    }

    public void addTmOnlineSmokeReportCommand(AddTmOnlineSmokeReportCommand command) {
        log.info("Command_AddTmPermitReportCommand :{}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        tmStateMachineExtendService.checkHasSmokeReport(command.getPlanCode(), command.getTransactor());
        TmOnlineSmokeReportAddEvent event = tmTestReportDomainConverter.converter(command);
        checkReportType(event.getPlanCode(), event.getReportType());
        tmTestReportRepository.saveTmOnlineSmokeReport(event);
        apply(event);
    }

    public void editTmOnlineSmokeReportCommand(EditTmOnlineSmokeReportCommand command) {
        log.info("Command_EditTmPermitReportCommand :{}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        tmStateMachineExtendService.checkHasSmokeReport(command.getPlanCode(), command.getTransactor());
        TmOnlineSmokeReportEditEvent event = tmTestReportDomainConverter.converter(command);
        tmTestReportRepository.updateTmOnlineSmokeReport(event);
        apply(event);
    }

    public void addAndSendReviewReport(AddAndSendReviewReportCommand command) {
        tmStateMachineExtendService.checkPermissions(command.getPlanCode(), command.getReportType(), command.getTransactor());
        if (null != command.getReviewInfo()) {
            checkUserRep(command.getReviewInfo());
        }
        ReviewReportAddedEvent event = tmTestReportDomainConverter.convert(command);
        event.setReviewInfo(fillReviewInfo(event.getReviewInfo(), event.getReportCode(), command.getReviewInfo()));
        event.setReviewOpinions(fillReviewOpinions(event.getReviewOpinions(), event.getReportCode()));
        event.setReviewRenewals(fillReviewRenewals(event.getReviewRenewals(), event.getReportCode()));
        event.setAttachments(fillAttachmentVOVO(event.getAttachments(), event.getReportCode()));
        tmTestReportRepository.saveTestReviewReport(event);
        apply(event);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertTestReportSendEvent(command);
        testReportSendEvent.setOperation(OperationEnum.I);
        testReportSendEvent.setAggregateId(command.getAggregateId());
        iEmailRepository.addEmail(testReportSendEvent);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        apply(testReportSendEvent);
    }

    public void editAndSendReviewReport(EditAndSendReviewReportCommand command) {
        log.info("Command------------------------>EditAndSendReviewReportCommand:{}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.canReportEdit(command.getProductCode(), command.getTransactor());
        if (null != command.getReviewInfo()) {
            checkUserRep(command.getReviewInfo());
        }

        //TODO 限制每一类人的数量

        ReviewReportEditedEvent event = tmTestReportDomainConverter.convert(command);
        event.setAggregateId(command.getAggregateId());
        event.setReviewInfo(fillReviewInfo(event.getReviewInfo(), event.getReportCode(), command.getReviewInfo()));
        event.setReviewOpinions(fillReviewOpinions(event.getReviewOpinions(), event.getReportCode()));
        event.setReviewRenewals(fillReviewRenewals(event.getReviewRenewals(), event.getReportCode()));
        event.setAttachments(fillAttachmentVOVO(event.getAttachments(), event.getReportCode()));
        tmTestReportRepository.updateTestReviewReport(event);
        apply(event);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertTestReportSendEvent(command);
        testReportSendEvent.setOperation(OperationEnum.U);
        testReportSendEvent.setAggregateId(command.getAggregateId());
        iEmailRepository.addEmail(testReportSendEvent);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        apply(testReportSendEvent);
    }

    private void checkUserRep(ReviewInfoVO reviewInfo) {
        if (null != reviewInfo.getDevelopReviewUserList() && CollectionUtil.isNotEmpty(reviewInfo.getDevelopReviewUserList())) {
            Boolean flag = reviewInfo.getDevelopReviewUserList().stream().map(ReviewUserVO::getSsoUserId).distinct().count() < reviewInfo.getDevelopReviewUserList().size();
            if (flag) {
                throw new ServiceException("开发参评人重复！请修改后提交");
            }
            if (reviewInfo.getDevelopReviewUserList().size() > 20) {
                throw new ServiceException("开发参评人不能超过20个！请修改后提交");
            }
        }
        if (null != reviewInfo.getTestReviewUserList() && CollectionUtil.isNotEmpty(reviewInfo.getTestReviewUserList())) {
            Boolean flag = reviewInfo.getTestReviewUserList().stream().map(ReviewUserVO::getSsoUserId).distinct().count() < reviewInfo.getTestReviewUserList().size();
            if (flag) {
                throw new ServiceException("测试参评人重复！请修改后提交");
            }
            if (reviewInfo.getTestReviewUserList().size() > 20) {
                throw new ServiceException("测试参评人不能超过20个！请修改后提交");
            }
        }
        if (null != reviewInfo.getProductReviewUserList() && CollectionUtil.isNotEmpty(reviewInfo.getProductReviewUserList())) {
            Boolean flag = reviewInfo.getProductReviewUserList().stream().map(ReviewUserVO::getSsoUserId).distinct().count() < reviewInfo.getProductReviewUserList().size();
            if (flag) {
                throw new ServiceException("产品参评人重复！请修改后提交");
            }
            if (reviewInfo.getProductReviewUserList().size() > 20) {
                throw new ServiceException("产品参评人不能超过20个！请修改后提交");
            }
        }
    }

    private ReviewInfoDTO fillReviewInfo(ReviewInfoDTO reviewInfoVO, String code, ReviewInfoVO reviewInfo) {
        if (Objects.nonNull(reviewInfoVO)) {
            reviewInfoVO.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
            reviewInfoVO.setReportCode(code);
            if (CollectionUtil.isNotEmpty(reviewInfo.getDevelopReviewUserList())) {
                reviewInfoVO.setDevelopReviewUsers(JsonUtil.toJSON(reviewInfo.getDevelopReviewUserList()));
            }
            if (CollectionUtil.isNotEmpty(reviewInfo.getTestReviewUserList())) {
                reviewInfoVO.setTestReviewUsers(JsonUtil.toJSON(reviewInfo.getTestReviewUserList()));
            }
            if (CollectionUtil.isNotEmpty(reviewInfo.getProductReviewUserList())) {
                reviewInfoVO.setProductReviewUsers(JsonUtil.toJSON(reviewInfo.getProductReviewUserList()));
            }
            return reviewInfoVO;
        }
        return null;
    }

    private List<ReviewOpinionVO> fillReviewOpinions(List<ReviewOpinionVO> reviewOpinionVOS, String code) {
        if (CollectionUtil.isNotEmpty(reviewOpinionVOS)) {
            List<ReviewOpinionVO> collect = reviewOpinionVOS.stream().map(reviewOpinionVO -> {
                reviewOpinionVO.setReportCode(code);
                reviewOpinionVO.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                return reviewOpinionVO;
            }).collect(Collectors.toList());
            return collect;
        }
        return null;
    }

    private List<ReviewRenewalVO> fillReviewRenewals(List<ReviewRenewalVO> reviewRenewalVOS, String code) {
        if (CollectionUtil.isNotEmpty(reviewRenewalVOS)) {
            List<ReviewRenewalVO> collect = reviewRenewalVOS.stream().peek(renewalVO -> {
                renewalVO.setReportCode(code);
                renewalVO.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
            }).collect(Collectors.toList());
            return collect;
        }
        return null;
    }

    private List<AttachmentVO> fillAttachmentVOVO(List<AttachmentVO> attachmentVOS, String code) {
        if (CollectionUtil.isNotEmpty(attachmentVOS)) {
            return attachmentVOS.stream().peek(attachmentVO -> {
                attachmentVO.setBusinessCode(code);
                attachmentVO.setDomain(DomainEnum.TEST_REPORT);
                attachmentVO.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
            }).collect(Collectors.toList());
        }
        return null;
    }

    public void addTmPermitReportCommand(AddTmPermitReportCommand command) {
        log.info("Command_AddTmPermitReportCommand :{}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        tmStateMachineExtendService.checkHasAccessReport(command.getPlanCode(), command.getTransactor());
        TmPermitReportAddEvent event = tmTestReportDomainConverter.converter(command);
        event.setModuleTestVOS(fillModuleTestVO(event.getModuleTestVOS(), event.getReportCode()));
        checkReportType(event.getPlanCode(), event.getReportType());
        tmTestReportRepository.saveTmPermitReport(event);
        apply(event);
        tmModuleRepository.addList(event);
    }

    public void editTmPermitReportCommand(EditTmPermitReportCommand command) {
        log.info("Command_EditTmPermitReportCommand :{}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        tmStateMachineExtendService.checkHasAccessReport(command.getPlanCode(), command.getTransactor());
        TmPermitReportEditedEvent event = tmTestReportDomainConverter.converter(command);
        event.setModuleTestVOS(fillModuleTestVO(event.getModuleTestVOS(), event.getReportCode()));
        tmTestReportRepository.updateTmPermitReport(event);
        tmModuleRepository.addList(event);
    }

    private List<TmModuleTestVO> fillModuleTestVO(List<TmModuleTestVO> moduleTestVOS, String code) {
        if (CollectionUtil.isNotEmpty(moduleTestVOS)) {
            List<TmModuleTestVO> collect = moduleTestVOS.stream().peek(moduleTestVO -> {
                moduleTestVO.setReportCode(code);
                moduleTestVO.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
            }).collect(Collectors.toList());
            return collect;
        }
        return null;
    }

    private void checkReportType(String planCode, ReportType reportType) {
        List<TmTestReportVO> reportList = tmTestReportRepository.getReportListByPlanCodeAndReportType(planCode, reportType);
        if (CollectionUtil.isNotEmpty(reportList)) {
            throw new ServiceException("当前版本已有[" + reportType.getValue() + "]");
        }
    }

    public void addAndSendSimpleTestReport(AddAndSendSimpleTestReportCommand command) {
        log.info("AddAndSendSimpleTestReportCommand --- {}", JsonUtil.toJSON(command));
        tmStateMachineExtendManager.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        SimpleReportAddedEvent event = tmTestReportDomainConverter.convert(command);
        event.setAggregateId(command.getAggregateId());
        checkReportType(event.getPlanCode(), event.getReportType());
        tmTestReportRepository.addSimpleTestReport(event);
        apply(event);

        //保存覆盖率原因
//        CoverageReasonEditEvent reasonEditEvent = tmTestReportDomainConverter.convertAddSimpleReasonEvent(command);
//        reasonEditEvent.setAggregateId(command.getAggregateId());
//        coverageRepository.updateBatch(reasonEditEvent);
//        apply(reasonEditEvent);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertTestReportSendEvent(command);
        testReportSendEvent.setOperation(OperationEnum.I);
        testReportSendEvent.setAggregateId(command.getAggregateId());
        iEmailRepository.addEmail(testReportSendEvent);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        doComputeMetrics(testReportSendEvent);
        apply(testReportSendEvent);
    }

    public void editAndSendSimpleTestReport(EditAndSendSimpleTestReportCommand command) {
        log.info("EditAndSendSimpleTestReportCommand --- {}", JsonUtil.toJSON(command));
        tmStateMachineExtendManager.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        SimpleTestReportEditEvent event = tmTestReportDomainConverter.convert(command);
        event.setAggregateId(command.getAggregateId());
        tmTestReportRepository.editSimpleTestReport(event);
        apply(event);

        //保存覆盖率原因
//        CoverageReasonEditEvent reasonEditEvent = tmTestReportDomainConverter.convertEditSimpleReasonEvent(command);
//        reasonEditEvent.setAggregateId(command.getAggregateId());
//        coverageRepository.updateBatch(reasonEditEvent);
//        apply(reasonEditEvent);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertTestReportSendEvent(command);
        testReportSendEvent.setOperation(OperationEnum.U);
        iEmailRepository.addEmail(testReportSendEvent);
        testReportSendEvent.setAggregateId(command.getAggregateId());
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        doComputeMetrics(testReportSendEvent);
        apply(testReportSendEvent);
    }

    public void addReviewReportCommand(AddReviewReportCommand command) {
        log.info("Command------------------------>AddReviewReportCommand :{}", JsonUtil.toJSON(command));
        if (null != command.getReviewInfo()) {
            checkUserRep(command.getReviewInfo());
        }
        tmStateMachineExtendService.checkPermissions(command.getPlanCode(), command.getReportType(), command.getTransactor());
        ReviewReportAddedEvent event = tmTestReportDomainConverter.converter(command);
        event.setReviewInfo(fillReviewInfo(event.getReviewInfo(), event.getReportCode(), command.getReviewInfo()));
        event.setReviewOpinions(fillReviewOpinions(event.getReviewOpinions(), event.getReportCode()));
        event.setReviewRenewals(fillReviewRenewals(event.getReviewRenewals(), event.getReportCode()));
        event.setAttachments(fillAttachmentVOVO(event.getAttachments(), event.getReportCode()));
        tmTestReportRepository.saveTestReviewReport(event);
        apply(event);
    }

    public void editReviewReportCommand(EditReviewReportCommand command) {
        log.info("Command------------------------>EditReviewReportCommand :{}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.canReportEdit(command.getProductCode(), command.getTransactor());
        if (null != command.getReviewInfo()) {
            checkUserRep(command.getReviewInfo());
        }
        ReviewReportEditedEvent event = tmTestReportDomainConverter.convert(command);
        event.setReviewInfo(fillReviewInfo(event.getReviewInfo(), event.getReportCode(), command.getReviewInfo()));
        event.setReviewOpinions(fillReviewOpinions(event.getReviewOpinions(), event.getReportCode()));
        event.setReviewRenewals(fillReviewRenewals(event.getReviewRenewals(), event.getReportCode()));
        event.setAttachments(fillAttachmentVOVO(event.getAttachments(), event.getReportCode()));
        tmTestReportRepository.updateTestReviewReport(event);
        apply(event);
    }

    public void addExternalTestReportCommand(AddExternalTestReportCommand command) {
        log.info("Command------------------------>AddExternalTestReportCommand :{}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissions(command.getPlanCode(), command.getReportType(), command.getTransactor());
        ExternalReportAddEvent event = tmTestReportDomainConverter.convert(command);
        tmTestReportRepository.saveExternalTestReport(event);
        apply(event);
    }

    public void editExternalTestReportCommand(EditExternalTestReportCommand command) {
        log.info("Command------------------------>EditExternalTestReportCommand :{}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.canReportEdit(command.getProductCode(), command.getTransactor());
        ExternalReportEditEvent event = tmTestReportDomainConverter.convert(command);
        tmTestReportRepository.updateExternalTestReport(event);
        apply(event);
    }

    public void addMobileTestReportCommand(AddMobileTestReportCommand command) {
        log.info("Command------------------------>AddMobileTestReportCommand :{}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissions(command.getPlanCode(), command.getReportType(), command.getTransactor());
        MobileTestReportAddedEvent event = tmTestReportDomainConverter.convert(command);
        tmTestReportRepository.addMobileTestReport(event);
        apply(event);
    }

    public void editMobileTestReportCommand(EditMobileTestReportCommand command) {
        log.info("Command------------------------>EditMobileTestReportCommand :{}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.canReportEdit(command.getProductCode(), command.getTransactor());
        MobileTestReportEditEvent event = tmTestReportDomainConverter.convert(command);
        tmTestReportRepository.updateMobileTestReport(event);
        apply(event);
    }

    public void addSimpleTestReportCommand(AddSimpleTestReportCommand command) {
        log.info("addSimpleTestReportCommand >>> {}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        SimpleReportAddedEvent event = tmTestReportDomainConverter.convert(command);
        checkReportType(event.getPlanCode(), event.getReportType());
        tmTestReportRepository.addSimpleTestReport(event);
        apply(event);
    }

    public void editSimpleTestReportCommand(EditSimpleTestReportCommand command) {
        log.info("editSimpleTestReportCommand >>> {}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        SimpleTestReportEditEvent event = tmTestReportDomainConverter.convert(command);
        tmTestReportRepository.editSimpleTestReport(event);
        apply(event);
    }

    public void addAndSendTmAccessTestReportCommand(AddAndSendTmAccessTestReportCommand command) {
        log.info("addAndSendTmAccessTestReportCommand >>> {}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        TmAccessReportAddEvent event = tmTestReportDomainConverter.convert(command);
        checkReportType(event.getPlanCode(), event.getReportType());
        tmTestReportRepository.saveTmAccessReport(event);
        apply(event);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertEmailSendEvent(command);
        iEmailRepository.addEmail(testReportSendEvent);
        testReportSendEvent.setOperation(OperationEnum.I);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        apply(testReportSendEvent);
    }

    public void editAndSendTmAccessTestReportCommand(EditAndSendTmAccessTestReportCommand command) {
        log.info("editAndSendTmAccessTestReportCommand >>> {}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        TmAccessReportEditEvent event = tmTestReportDomainConverter.convert(command);
        tmTestReportRepository.updateTmAccessReport(event);
        apply(event);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertEmailSendEvent(command);
        iEmailRepository.addEmail(testReportSendEvent);
        testReportSendEvent.setOperation(OperationEnum.U);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        apply(testReportSendEvent);
    }

    public void addAndSendExternalTestReportCommand(AddAndSendExternalTestReportCommand command) {
        log.info("addAndSendExternalTestReportCommand >>> {}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissions(command.getPlanCode(), command.getReportType(), command.getTransactor());
        ExternalReportAddEvent event = tmTestReportDomainConverter.convert(command);
        event.setAttachments(fillAttachmentVOVO(event.getAttachments(), event.getReportCode()));
        tmTestReportRepository.saveExternalTestReport(event);
        apply(event);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertTestReportSendEvent(command);
        testReportSendEvent.setOperation(OperationEnum.I);
        iEmailRepository.addEmail(testReportSendEvent);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        apply(testReportSendEvent);
    }

    public void editAndSendExternalTestReportCommand(EditAndSendExternalTestReportCommand command) {
        log.info("editAndSendExternalTestReportCommand >>> {}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.canReportEdit(command.getProductCode(), command.getTransactor());
        ExternalReportEditEvent event = tmTestReportDomainConverter.convert(command);
        event.setAttachments(fillAttachmentVOVO(event.getAttachments(), event.getReportCode()));
        tmTestReportRepository.updateExternalTestReport(event);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertTestReportSendEvent(command);
        testReportSendEvent.setOperation(OperationEnum.U);
        iEmailRepository.addEmail(testReportSendEvent);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        apply(testReportSendEvent);
    }

    public void addAndSendMobileTestReportCommand(AddAndSendMobileTestReportCommand command) {
        log.info("addAndSendMobileTestReportCommand >>> {}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissions(command.getPlanCode(), command.getReportType(), command.getTransactor());
        MobileTestReportAddedEvent event = tmTestReportDomainConverter.convert(command);
        tmTestReportRepository.addMobileTestReport(event);
        apply(event);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertTestReportSendEvent(command);
        testReportSendEvent.setOperation(OperationEnum.I);
        iEmailRepository.addEmail(testReportSendEvent);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        apply(testReportSendEvent);
    }

    public void editAndSendMobileTestReportCommand(EditAndSendMobileTestReportCommand command) {
        log.info("editAndSendMobileTestReportCommand >>> {}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.canReportEdit(command.getProductCode(), command.getTransactor());
        MobileTestReportEditEvent event = tmTestReportDomainConverter.convert(command);
        tmTestReportRepository.updateMobileTestReport(event);
        apply(event);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertTestReportSendEvent(command);
        testReportSendEvent.setOperation(OperationEnum.U);
        iEmailRepository.addEmail(testReportSendEvent);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        apply(testReportSendEvent);
    }

    public void addAndSendTmOnlineSmokeTestReportCommand(AddAndSendTmOnlineSmokeTestReportCommand command) {
        log.info("addAndSendTmOnlineSmokeTestReportCommand >>> {}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        tmStateMachineExtendService.checkHasSmokeReport(command.getPlanCode(), command.getTransactor());
        TmOnlineSmokeReportAddEvent event = tmTestReportDomainConverter.convert(command);
        checkReportType(event.getPlanCode(), event.getReportType());
        tmTestReportRepository.saveTmOnlineSmokeReport(event);
        apply(event);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertEmailSendEvent(command);
        testReportSendEvent.setOperation(OperationEnum.I);
        iEmailRepository.addEmail(testReportSendEvent);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        apply(testReportSendEvent);
    }

    public void editAndSendTmOnlineSmokeTestReportCommand(EditAndSendTmOnlineSmokeTestReportCommand command) {
        log.info("editAndSendTmOnlineSmokeTestReportCommand >>> {}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        tmStateMachineExtendService.checkHasSmokeReport(command.getPlanCode(), command.getTransactor());
        TmOnlineSmokeReportEditEvent event = tmTestReportDomainConverter.convert(command);
        tmTestReportRepository.updateTmOnlineSmokeReport(event);
        apply(event);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertEmailSendEvent(command);
        testReportSendEvent.setOperation(OperationEnum.U);
        iEmailRepository.addEmail(testReportSendEvent);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        apply(testReportSendEvent);
    }

    public void addAndSendTmPermitTestReportCommand(AddAndSendTmPermitTestReportCommand command) {
        log.info("addAndSendTmPermitTestReportCommand >>> {}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        tmStateMachineExtendService.checkHasAccessReport(command.getPlanCode(), command.getTransactor());
        TmPermitReportAddEvent event = tmTestReportDomainConverter.convert(command);
        event.setModuleTestVOS(fillModuleTestVO(event.getModuleTestVOS(), event.getReportCode()));
        checkReportType(event.getPlanCode(), event.getReportType());
        tmTestReportRepository.saveTmPermitReport(event);
        apply(event);

        tmModuleRepository.addList(event);

        //保存覆盖率原因
//        CoverageReasonEditEvent reasonEditEvent = tmTestReportDomainConverter.convertAddPermitReasonEvent(command);
//        coverageRepository.updateBatch(reasonEditEvent);
//        apply(reasonEditEvent);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertEmailSendEvent(command);
        testReportSendEvent.setOperation(OperationEnum.I);
        iEmailRepository.addEmail(testReportSendEvent);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        doComputeMetrics(testReportSendEvent);
        apply(testReportSendEvent);
    }

    public void editAndSendTmPermitTestReportCommand(EditAndSendTmPermitTestReportCommand command) {
        log.info("editAndSendTmPermitTestReportCommand >>> {}", JsonUtil.toJSON(command));
        tmStateMachineExtendService.checkPermissionByProduct(command.getProductCode(), command.getTransactor());
        tmStateMachineExtendService.checkHasAccessReport(command.getPlanCode(), command.getTransactor());
        TmPermitReportEditedEvent event = tmTestReportDomainConverter.convert(command);
        event.setModuleTestVOS(fillModuleTestVO(event.getModuleTestVOS(), event.getReportCode()));
        tmTestReportRepository.updateTmPermitReport(event);
        apply(event);

        tmModuleRepository.addList(event);

        //保存覆盖率原因
//        CoverageReasonEditEvent reasonEditEvent = tmTestReportDomainConverter.convertEditPermitReasonEvent(command);
//        coverageRepository.updateBatch(reasonEditEvent);
//        apply(reasonEditEvent);

        TestReportSendEvent testReportSendEvent = tmTestReportDomainConverter.convertEmailSendEvent(command);
        testReportSendEvent.setOperation(OperationEnum.U);
        iEmailRepository.addEmail(testReportSendEvent);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleReportAddedEvent(testReportSendEvent);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
        reportMqSender.handleTestReportSendEvent(testReportSendEvent);
        doComputeMetrics(testReportSendEvent);
        apply(testReportSendEvent);
    }

    private void doComputeMetrics(TestReportSendEvent event) {
        try {
            metricsCommandDomainService.computeIssueMetrics(event);
        } catch (Exception e) {
            log.error("doComputeMetrics error:{}", event.getReportCode(), e);
        }
    }
}
