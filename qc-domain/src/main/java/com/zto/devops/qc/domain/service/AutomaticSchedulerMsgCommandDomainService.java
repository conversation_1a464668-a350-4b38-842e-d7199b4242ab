package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.model.dto.AutomaticSchedulerEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.client.model.rpc.product.SimpleQueryVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticSchedulerMsgEvent;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.AutomaticSchedulerMsgRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AutomaticSchedulerMsgCommandDomainService extends BaseDomainService {

    @Autowired
    private AutomaticSchedulerMsgRepository automaticSchedulerMsgRepository;

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private RedisService redisService;

    public void sendMessage(String taskId) {
        if (!redisService.hasKey("Message::" + taskId)) {
            log.warn("任务：{}已处理过消息通知！", taskId);
            return;
        }
        List<AutomaticTaskEntityDO> taskList = automaticSchedulerMsgRepository.getAllTaskByTaskId(taskId);
        if(CollectionUtil.isEmpty(taskList)) {
            redisService.delete("Message::" + taskId);
            return;
        }
        List<AutomaticTaskEntityDO> completeTasks = taskList.stream().filter(t->
                t.getStatus().equals(AutomaticStatusEnum.NOT_STARTED) ||
                        t.getStatus().equals(AutomaticStatusEnum.SUBMITTED) ||
                        t.getStatus().equals(AutomaticStatusEnum.IN_PROGRESS)
        ).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(completeTasks)) {
            return;
        }
        AutomaticSchedulerEntityDO schedulerEntityDOByCode = automaticSchedulerMsgRepository.getSchedulerEntityDOByCode(taskList.get(0).getSchedulerCode());
        if(null == schedulerEntityDOByCode || !schedulerEntityDOByCode.getMessageFlag()) {
            redisService.delete("Message::" + taskId);
            return;
        }
        redisService.delete("Message::" + taskId);
        sendAutomaticSchedulerMsgEvent(taskList, taskId, schedulerEntityDOByCode);
    }

    private void sendAutomaticSchedulerMsgEvent(List<AutomaticTaskEntityDO> taskList, String taskId,
                                                AutomaticSchedulerEntityDO schedulerEntityDO) {
        Set<String> receivedUsers = automaticSchedulerMsgRepository.getAllReceivedUsers(schedulerEntityDO.getSchedulerCode(), schedulerEntityDO.getCreatorId());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = format.format(taskList.stream()
                .sorted(Comparator.comparing(AutomaticTaskEntityDO::getStartTime))
                .collect(Collectors.toList()).get(0).getStartTime());
        String finishTime = format.format(taskList.stream()
                .sorted(Comparator.comparing(AutomaticTaskEntityDO::getFinishTime, Comparator.reverseOrder()))
                .collect(Collectors.toList()).get(0).getFinishTime());
        AutomaticSchedulerMsgEvent event = new AutomaticSchedulerMsgEvent();
        event.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        event.setTaskId(taskId);
        event.setCreator(schedulerEntityDO.getCreator());
        event.setSchedulerName(schedulerEntityDO.getSchedulerName());
        event.setExecuteResult(schedulerEntityDO.getExecuteResult());
        event.setStartTime(startTime);
        event.setFinishTime(finishTime);
        event.setReceivedUsers(receivedUsers);
        event.setProductCode(schedulerEntityDO.getProductCode());
        setProductNameByCode(event);
        apply(event);
        log.info("======发送定时任务AutomaticSchedulerMsgEvent" + JSON.toJSONString(event));
    }

    private void setProductNameByCode(AutomaticSchedulerMsgEvent event) {
        try{
            SimpleQueryVO productVO = productRpcService.getProductVO(event.getProductCode());
            event.setProductName(productVO.getProductName());
        }catch (Exception e) {
            e.printStackTrace();
            log.error("FindProductByIdQuery ERROR {}", event.getProductCode(), e);
        }
    }

}
