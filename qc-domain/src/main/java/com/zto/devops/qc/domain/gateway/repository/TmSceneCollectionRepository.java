package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.dto.SceneCollectionEntityDO;

import java.util.List;

public interface TmSceneCollectionRepository {

    void saveFavoritesPreData(List<SceneCollectionEntityDO> sharePreDataSceneAndModuleByProduct);

    int deleteFavoritesPreData(List<SceneCollectionEntityDO> sharePreDataSceneAndModuleByProduct, User user);
    int deleteFavoritesPreDataByProductCode(String productCode, User user);

    List<SceneCollectionEntityDO> selectFavoritesPreData(Long userId);
}
