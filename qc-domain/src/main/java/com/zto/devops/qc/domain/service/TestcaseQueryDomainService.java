package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.zto.devops.framework.client.dto.Page;
import com.zto.devops.framework.client.entity.UserInfo;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.common.util.time.DateUtil;
import com.zto.devops.qc.client.enums.issue.ColorEnumType;
import com.zto.devops.qc.client.enums.rpc.MemberTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.enums.testmanager.report.CaseFinalResultEnum;
import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.qc.client.model.rpc.product.SimpleQueryVO;
import com.zto.devops.qc.client.model.rpc.product.query.ListProductMemberByPIdQuery;
import com.zto.devops.qc.client.model.rpc.project.*;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.*;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.AmazonS3ConfigVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageVersionRateVO;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageVersionRateQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.query.VersionPlanQuery;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListTestcaseReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListXmindDetailResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.QueryAllXmindCaseReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.StatisticVersionCaseReq;
import com.zto.devops.qc.domain.converter.TestcaseDomainConverter;
import com.zto.devops.qc.domain.converter.TestcaseStepDomainConverter;
import com.zto.devops.qc.domain.converter.TestcaseTagDomainConverter;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.jenkins.IJenkinsService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.*;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class TestcaseQueryDomainService {

    @Autowired
    private IJenkinsService jenkinsService;

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private ITmTestPlanCaseRepository tmTestPlanCaseRepository;

    @Autowired
    private TagRepository tagRepository;

    @Autowired
    private ITestcaseRelationRepository iTestcaseRelationRepository;

    @Autowired
    private ITestcaseExecuteRecordRepository testcaseExecuteRecordRepository;

    @Autowired
    private TestcaseRepository testcaseRepository;

    @Autowired
    private TestcaseStepRepository testcaseStepRepository;

    @Autowired
    private TestcaseDomainConverter testcaseDomainConverter;

    @Autowired
    private IAutomaticTaskRepository automaticTaskRepository;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    @Autowired
    private IProjectRpcService iProjectRpcService;

    @Autowired
    private TestcaseTagDomainConverter testcaseTagConverter;

    @Autowired
    private TestcaseStepDomainConverter testcaseStepDomainConverter;

    @Autowired
    private TestcaseTagRepository testcaseTagRepository;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ApiTestRepository apiTestRepository;

    @Autowired
    private AutomaticSourceRecordRepository automaticSourceRecordRepository;

    @Autowired
    private CoverageQueryDomainService coverageQueryDomainService;

    @Autowired
    private TmTestPlanQueryDomainService tmTestPlanQueryDomainService;

    @ApolloConfig
    private Config config;

    public ExecuteCaseVO findExecuteCaseQuery(FindExecuteCaseQuery query) {
        log.info("FindExecuteCaseQuery >>> {}", JSON.toJSONString(query));
        ExecuteCaseVO vo = getExecuteCaseByTaskType(query);
        if (null != vo) {
            if (StringUtils.isNotBlank(vo.getResultFile())) {
                try {
                    List<ExecuteCaseResultContentVO> resultContentVOList =
                            jenkinsService.getExecuteCaseResultContent(vo.getResultFile());
                    vo.setResultContent(resultContentVOList);
                    if (CollectionUtil.isNotEmpty(resultContentVOList)) {
                        vo.setPassedCount(resultContentVOList.stream().filter(v -> v.getStatus().equals(CaseFinalResultEnum.PASSED.name())).count());
                        vo.setFailedCount(resultContentVOList.stream().filter(v -> v.getStatus().equals(CaseFinalResultEnum.FAILED.name())).count());
                    }
                } catch (Exception e) {
                    log.error("Get Result Content ERROR {}", vo.getResultFile(), e);
                }
            }
            try {
                SimpleQueryVO productVO = productRpcService.getProductVO(vo.getProductCode());
                vo.setProductName(null == productVO ? null : productVO.getProductName());
            } catch (Exception e) {
                log.error("FindProductByIdQuery ERROR {}", vo.getProductCode(), e);
            }
            if (StringUtils.isNotBlank(vo.getVersionCode())) {
                try {
                    vo.setVersionName(this.getSimpleVersionName(vo.getVersionCode()));
                } catch (Exception e) {
                    log.error("SimpleVersionQuery ERROR {}", vo.getVersionCode(), e);
                }
            }
            vo.setResultFile(getFullFilepath(vo.getResultFile()));
            vo.setReportFile(getFullFilepath(vo.getReportFile()));
            vo.setExecLogFile(getFullFilepath(vo.getExecLogFile()));
        }
        return vo;
    }

    private ExecuteCaseVO getExecuteCaseByTaskType(FindExecuteCaseQuery query) {
        ExecuteCaseVO vo = testcaseRepository.getExecuteCase(query.getTestcaseCode(), query.getAutomaticTaskCode());
        if (null != vo) {
            vo.setParentFullName(this.findParentFullNameQuery(vo.getPath()));
        }
        return vo;
    }

    public List<String> findParentFullNameQuery(String path) {
        log.info("FindParentFullNameQuery >>> {}", path);
        if (StringUtils.isBlank(path)) {
            return Collections.emptyList();
        }
        List<String> paths = Arrays.asList(path.split("\\."));
        List<TestcaseVO> pList = testcaseRepository.getTestcaseNameListByPath(paths);
        return pList.stream()
                .sorted(Comparator.comparingInt(entity -> paths.indexOf(entity.getCode())))
                .map(TestcaseVO::getName)
                .collect(Collectors.toList());
    }

    /**
     * 获取完整文件路径
     *
     * @param filepath 文件路径
     * @return 完整文件路径
     */
    private String getFullFilepath(String filepath) {
        AmazonS3ConfigVO configVO = qcConfigBasicService.getAmazonS3Config();
        String callbackBucketName = qcConfigBasicService.getCallbackBucketName();
        if (StringUtils.isBlank(filepath)) {
            return null;
        }
        return MessageFormat.format("{0}/{1}/{2}", configVO.getEndPoint(), callbackBucketName, filepath);
    }

    /**
     * 查询版本名称
     *
     * @param versionCode 版本code
     * @return 版本名称
     */
    private String getSimpleVersionName(String versionCode) {
        String result = "";
        if (StringUtils.isBlank(versionCode)) {
            return result;
        }
        SimpleVersionListQuery simpleVersionListQuery = new SimpleVersionListQuery();
        simpleVersionListQuery.setCode(Collections.singletonList(versionCode));
        try {
            SimpleListVersionVO versionVOList = iProjectRpcService.simpleVersionListQuery(simpleVersionListQuery);
            if (null != versionVOList && CollectionUtils.isNotEmpty(versionVOList.getSimpleVersionVOList())) {
                SimpleVersionVO versionVO = versionVOList.getSimpleVersionVOList().get(0);
                if (null != versionVO) {
                    result = versionVO.getName();
                }
            }
        } catch (Exception e) {
            log.error("SimpleVersionQuery ERROR {}", versionCode, e);
        }
        return result;
    }

    public TestcaseVO findCaseOrModuleByCodeQuery(FindCaseOrModuleByCodeQuery query) {
        log.info("FindCaseOrModuleByCodeQuery >>> {}", query.getCode());
        return testcaseRepository.findCaseOrModuleByCodeQuery(query.getCode());
    }

    public List<TestcaseVO> findTestcaseByNameQuery(FindTestcaseByNameQuery query) {
        return testcaseRepository.findTestcaseByNameQuery(query);
    }

    public Page pageXmindDetailQuery(PageXmindDetailQuery query) {
        Page<ListXmindDetailVO> page = new Page();
        // 复用pageTestcase接口方法查出所有节点
        List<ListTestcaseVO> nodeList = getListXmindDetailVO(query);
        com.github.pagehelper.Page<ListXmindDetailVO> pageInfo = PageHelper.startPage(query.getPage(), query.getSize());
        List<ListXmindDetailVO> list = getTestcaseGroup(nodeList, query);
        // 剔除未分组
        List<String> codeList = list.stream().filter(x -> !TestcaseGroupTypeEnum.NO_GROUP.name().equals(x.getId()))
                .map(ListXmindDetailVO::getId).collect(Collectors.toList());
        // 产品的测试负责人、产品的测试人员、超级管理员有权限
        boolean productPermission = productRpcService.checkTestUserPermission(query.getTransactor(), query.getProductCode());
        // 判断list是否为空
        if (CollectionUtil.isNotEmpty(codeList)) {
            // 过滤掉不是parent的code
            List<String> parentList = getParentCodeList(codeList);
            list.forEach(t -> {
                buildDetailVOList(parentList, t, productPermission, query);
            });
        } else {
            // 用例、空分组、ALL、NO_GROUP
            caseOrEmptyModule(query, productPermission, list);
        }
        // 拼装外层ALL、GROUP、NO_GROUP
        if (null != query.getGroupType()) {
            list = buildChildDetail(query, list);
        }
        if (CollectionUtil.isEmpty(list)) {
            page.setTotal(0);
            page.setSize(query.getSize());
            page.setPage(pageInfo.getPageNum());
            page.setList(Collections.emptyList());
            return page;
        }

        //147版本新增需求 如果未分組用例下沒有用例 不返回未分組用例
        ListXmindDetailVO listXmindDetailVO = list.get(0);
        if (CollectionUtil.isNotEmpty(listXmindDetailVO.getChildren())) {
            listXmindDetailVO.getChildren().removeIf(t -> TestcaseGroupTypeEnum.NO_GROUP.name().equals(t.getId()) && t.getTestcaseCount() <= 0);
            listXmindDetailVO.getChildren().sort(Comparator.nullsFirst(Comparator.comparingLong(ListXmindDetailVO::getIncId)));
        }

        list.sort(Comparator.nullsFirst(Comparator.comparingLong(ListXmindDetailVO::getIncId)));
        page.setList(list);
        page.setTotal(pageInfo.getTotal());
        page.setSize(pageInfo.getPageSize());
        page.setPage(pageInfo.getPageNum());
        return page;
    }


    private void caseOrEmptyModule(PageXmindDetailQuery query, Boolean productPermission,
                                   List<ListXmindDetailVO> list) {
        // 有caseCode，用例或空分组
        if (StringUtils.isNotEmpty(query.getCaseCode())
                && !TestcaseGroupTypeEnum.ALL.name().equals(query.getCaseCode())
                && !TestcaseGroupTypeEnum.NO_GROUP.name().equals(query.getCaseCode())) {
            // 根据code查询用例详情
            TestcaseEntityDO testCase = testcaseRepository.getTestcaseVO(query);
            ListXmindDetailVO detailVO = new ListXmindDetailVO();
            detailVO.setParentCode(query.getCaseCode());
            if (testCase.getAttribute().equals(TestcaseAttributeEnum.MODULE)) {
                // 该节点是空分组
                detailVO.setHasChilds(false);
                buildXmindDetailVO(detailVO);
            } else {
                Boolean permission = productPermission
                        || judgeTopicEdit(testCase.getCreatorId(), testCase.getDutyUserId(), query);
                // 该节点是用例
                List<ListXmindDetailVO> childs = getTestCaseChildList(testCase, permission);
                list.addAll(childs);
            }
        } else {
            // ALL或NO_GROUP
            list.forEach(t -> {
                if (t.getId().equals(TestcaseGroupTypeEnum.NO_GROUP.name())) {
                    t.setHasChilds(judgeHasChild(t));
                    buildXmindDetailVO(t);
                    t.setDisabled(true);
                }
            });
        }
    }

    private List<String> getParentCodeList(List<String> codeList) {
        return testcaseRepository.getParentCodeList(codeList);
    }

    private List<ListXmindDetailVO> getTestCaseChildList(TestcaseEntityDO testCase, Boolean permission) {
        // 该节点是用例
        List<ListXmindDetailVO> childs = new ArrayList<>();
        // 前置条件
        ListXmindDetailVO detailPrecondition = new ListXmindDetailVO();
        detailPrecondition.setParentCode(testCase.getCode());
        buildChildPrecondition(detailPrecondition, testCase);
        detailPrecondition.setDisabled(!permission);
        childs.add(detailPrecondition);
        // 备注
        ListXmindDetailVO detailRemark = new ListXmindDetailVO();
        detailRemark.setParentCode(testCase.getCode());
        buildChildRemark(detailRemark, testCase);
        detailRemark.setDisabled(!permission);
        childs.add(detailRemark);
        // 步骤+预期结果
        ListXmindDetailVO detailStep = new ListXmindDetailVO();
        detailStep.setParentCode(testCase.getCode());
        // 查用例步骤
        List<TestcaseStepVO> stepList = testcaseStepRepository.getTestcaseStepVOList(testCase);
        if (CollectionUtils.isNotEmpty(stepList)) {
            List<ListXmindDetailVO> steps = buildChildSteps(detailStep, stepList, permission);
            childs.addAll(steps);
        }
        return childs;
    }

    private void buildChildPrecondition(ListXmindDetailVO detailVO, TestcaseEntityDO testCase) {
        detailVO.setTopic(testCase.getPrecondition());
        detailVO.setTagName(TestCaseTagNameEnum.PRECONDITION.getDesc());
        detailVO.setTagValue(TestCaseTagNameEnum.PRECONDITION);
        detailVO.setHasChilds(false);
        detailVO.setId(testCase.getCode() + TestCaseTagNameEnum.PRECONDITION.name());
        buildXmindDetailVO(detailVO);
    }

    private List<ListXmindDetailVO> buildChildSteps(ListXmindDetailVO detailVO, List<TestcaseStepVO> stepList, Boolean permission) {
        List<ListXmindDetailVO> childs = new ArrayList<>();
        stepList.forEach(t -> {
            ListXmindDetailVO vo = new ListXmindDetailVO();
            vo.setParentCode(detailVO.getParentCode());
            vo.setTopic(t.getStepDesc());
            vo.setHasChilds(true);
            vo.setTagName(TestCaseTagNameEnum.STEP.getDesc());
            vo.setTagValue(TestCaseTagNameEnum.STEP);
            vo.setId(t.getTestcaseCode() + TestCaseTagNameEnum.STEP.name() + t.getSort());
            buildXmindDetailVO(vo);
            vo.setDisabled(!permission);
            buildChildExcept(vo, t, permission);
            vo.setExpanded(true);
            childs.add(vo);
        });
        return childs;
    }

    private void buildChildExcept(ListXmindDetailVO vo, TestcaseStepVO step, Boolean permission) {
        List<ListXmindDetailVO> exceptList = new ArrayList<>();
        ListXmindDetailVO except = new ListXmindDetailVO();
        except.setParentCode(vo.getId());
        except.setTopic(step.getExpectResult());
        except.setHasChilds(false);
        except.setTagName(TestCaseTagNameEnum.EXPECT.getDesc());
        except.setTagValue(TestCaseTagNameEnum.EXPECT);
        except.setId(step.getTestcaseCode() + TestCaseTagNameEnum.EXPECT.name() + step.getSort());
        buildXmindDetailVO(except);
        except.setDisabled(!permission);
        exceptList.add(except);
        vo.setChildren(exceptList);
    }

    private void buildChildRemark(ListXmindDetailVO detailVO, TestcaseEntityDO testCase) {
        detailVO.setTopic(testCase.getComment());
        detailVO.setTagName(TestCaseTagNameEnum.REMARK.getDesc());
        detailVO.setTagValue(TestCaseTagNameEnum.REMARK);
        detailVO.setHasChilds(false);
        detailVO.setId(testCase.getCode() + TestCaseTagNameEnum.REMARK.name());
        buildXmindDetailVO(detailVO);
    }

    private List<ListXmindDetailVO> getTestcaseGroup(List<ListTestcaseVO> topModule, PageXmindDetailQuery query) {
        try {
            getListTestcaseGroup(topModule, query);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return testcaseDomainConverter.convertToList(topModule);
    }

    private void buildDetailVOList(List<String> childList, ListXmindDetailVO vo,
                                   Boolean permission, PageXmindDetailQuery query) {
        // 是parent
        if (childList.contains(vo.getId())) {
            vo.setHasChilds(true);
            buildXmindDetailVO(vo);
        } else {
            // 不是parent
            if (vo.getAttribute().equals(TestcaseAttributeEnum.MODULE)) {
                vo.setHasChilds(judgeHasChild(vo));
                buildXmindDetailVO(vo);
                vo.setDisabled(vo.getId().equals(TestcaseGroupTypeEnum.NO_GROUP.name()));
            } else {
                vo.setHasChilds(true);
                buildXmindDetailVO(vo);
                vo.setDisabled(!permission && !judgeTopicEdit(vo.getCreatorId(), vo.getDutyUserId(), query));
            }
        }
    }

    private Boolean judgeTopicEdit(Long creatorId, Long dutyUserId, PageXmindDetailQuery query) {
        // 用例创建人、用例责任人
        return Optional.ofNullable(query.getTransactor())
                .map(User::getUserId)
                .map(userId -> userId.equals(creatorId) || userId.equals(dutyUserId))
                .orElse(false);
    }

    private Boolean judgeHasChild(ListXmindDetailVO detailVO) {
        if (detailVO.getId().startsWith(TestcaseGroupTypeEnum.NO_GROUP.name())) {
            return detailVO.getTestcaseCount() != 0;
        }
        return false;
    }

    private void buildXmindDetailVO(ListXmindDetailVO vo) {
        vo.setTagEdit(false);
        vo.setExpanded(false);
        vo.setDisabled(false);
        vo.setDirection(DirectionEnum.RIGHT);
        vo.setChildren(new ArrayList<>());
    }

    private void getListTestcaseGroup(List<ListTestcaseVO> topModule, PageTestcaseQuery query) {
        if (!TestcaseGroupTypeEnum.ALL.equals(query.getGroupType())) {
            query.setTestcaseAttribute(TestcaseAttributeEnum.TESTCASE);
            List<AutomaticNodeTypeEnum> nodeTypeList = query.getNodeTypeList();
            if (CollectionUtil.isNotEmpty(nodeTypeList)) {
                query.setNodeTypeList(nodeTypeList.stream()
                        .filter(n -> TestcaseAttributeEnum.TESTCASE.equals(n.getAttribute()))
                        .collect(Collectors.toList()));
            }
            List<ListTestcaseVO> testcaseList = testcaseRepository.pageTestcase(query);
            if (Boolean.TRUE.equals(query.getFilterModule()) && null != query.getFilterModuleCodeList()) {
                testcaseList.removeIf(item -> query.getFilterModuleCodeList().stream()
                        .noneMatch(s -> StringUtils.defaultString(item.getPath()).contains(s)));
            }
            if (TestcaseTypeEnum.MANUAL.equals(query.getType())) {
                List<String> codeList = testcaseList.stream().map(ListTestcaseVO::getCode).collect(Collectors.toList());
                List<TagVO> allTags = getTagsByCodeList(codeList);
                testcaseList.forEach(t -> {
                    List<TagVO> tags = allTags.stream().filter(e -> t.getCode().equals(e.getBusinessCode())).collect(Collectors.toList());
                    t.setTags(tags);
                    List<TestcaseButtonEnum> buttons = getTestcaseButtons(t.getType(), t.getStatus(), "list", query.getSetCore());
                    t.setButtons(buttons);
                });
            }
            // 如果关联计划加上用例执行状态
            relatedCaseExecutionStatus(testcaseList, query);
            topModule.addAll(testcaseList);
        } else {
            PageHelper.clearPage();
        }
    }

    private List<TagVO> getTagsByCodeList(List<String> codeList) {
        if (CollectionUtil.isEmpty(codeList)) {
            return Collections.emptyList();
        }
        return testcaseRepository.getTagsByCodeList(codeList);
    }

    private List<ListXmindDetailVO> buildChildDetail(PageXmindDetailQuery query, List<ListXmindDetailVO> list) {
        ListXmindDetailVO detailVO = new ListXmindDetailVO();
        if (query.getGroupType().equals(TestcaseGroupTypeEnum.ALL)) {
            //版本用例库
            if (StringUtils.isBlank(query.getPlanCode()) || query.getSetCore()) {
                list.forEach(x -> x.setParentCode(TestcaseGroupTypeEnum.ALL.name()));
                detailVO.setTestcaseCount(list.stream().mapToInt(ListXmindDetailVO::getTestcaseCount).sum());
                detailVO.setId(TestcaseGroupTypeEnum.ALL.name());
                detailVO.setTopic(TestcaseGroupTypeEnum.ALL.getDesc());
                detailVO.setAttribute(TestcaseAttributeEnum.ROOT);
                detailVO.setHasChilds(CollectionUtils.isNotEmpty(list));
                buildXmindDetailVO(detailVO);
                detailVO.setChildren(list);
                return Arrays.asList(detailVO);
            } else {
                //测试计划用例库
                return buildGroupByVersion(list);
            }
        }
        if (query.getGroupType().equals(TestcaseGroupTypeEnum.GROUP)) {
            if (query.getIsRoot()) {
                TestcaseEntityDO parentCase = testcaseRepository.getTestcaseVO(query);
                list.forEach(x -> x.setParentCode(query.getParentCode()));
                detailVO.setId(query.getParentCode());
                detailVO.setTopic(parentCase.getName());
                detailVO.setAttribute(TestcaseAttributeEnum.ROOT);
                detailVO.setHasChilds(CollectionUtils.isNotEmpty(list));
                buildXmindDetailVO(detailVO);
                detailVO.setChildren(list);
                return Arrays.asList(detailVO);
            }
        }
        if (query.getGroupType().equals(TestcaseGroupTypeEnum.NO_GROUP)) {
            if (query.getIsRoot()) {
                list.forEach(x -> x.setParentCode(TestcaseGroupTypeEnum.NO_GROUP.name()));
                detailVO.setTopic(TestcaseGroupTypeEnum.NO_GROUP.getDesc());
                detailVO.setAttribute(TestcaseAttributeEnum.ROOT);
                detailVO.setHasChilds(CollectionUtils.isNotEmpty(list));
                detailVO.setId(TestcaseGroupTypeEnum.NO_GROUP.name() + (detailVO.getHasChilds() ? ("___" + list.get(0).getVersionCode()) : ""));
                buildXmindDetailVO(detailVO);
                detailVO.setChildren(list);
                return Arrays.asList(detailVO);
            }
        }
        return list;
    }

    /**
     * 测试计划-按版本维度展开
     *
     * @param list
     * @return
     */
    private List<ListXmindDetailVO> buildGroupByVersion(List<ListXmindDetailVO> list) {
        ListXmindDetailVO detailVO = new ListXmindDetailVO();
        Map<String, List<ListXmindDetailVO>> versionMap = list.stream().collect(Collectors.groupingBy(ListXmindDetailVO::getVersionCode));
        if (MapUtils.isEmpty(versionMap)) {
            return list;
        }
        Map<String, String> versionInfoMap = tmTestPlanQueryDomainService.getVersionNameList(versionMap.keySet());
        List<ListXmindDetailVO> versionModuleList = new ArrayList<>(versionMap.size());
        versionMap.forEach((versionCode, dataList) -> {
            String moduleName = versionCode.equals("NONE_VERSION")
                    ? "未关联版本用例"
                    : (MapUtils.isEmpty(versionInfoMap) ? versionCode : versionInfoMap.get(versionCode));
            ListXmindDetailVO versionModule = new ListXmindDetailVO();
            dataList.forEach(x -> x.setParentCode(versionCode));
            versionModule.setId(versionCode);
            versionModule.setTopic(moduleName);
            versionModule.setAttribute(TestcaseAttributeEnum.ROOT);
            versionModule.setHasChilds(CollectionUtils.isNotEmpty(dataList));
            buildXmindDetailVO(versionModule);
            versionModule.setChildren(dataList);
            versionModule.setTestcaseCount(dataList.stream().mapToInt(ListXmindDetailVO::getTestcaseCount).sum());
            versionModuleList.add(versionModule);
        });
        versionModuleList.forEach(x -> x.setParentCode(TestcaseGroupTypeEnum.ALL.name()));
        detailVO.setId(TestcaseGroupTypeEnum.ALL.name());
        detailVO.setTopic(TestcaseGroupTypeEnum.ALL.getDesc());
        detailVO.setAttribute(TestcaseAttributeEnum.ROOT);
        detailVO.setHasChilds(CollectionUtils.isNotEmpty(versionModuleList));
        buildXmindDetailVO(detailVO);
        detailVO.setChildren(versionModuleList);
        detailVO.setTestcaseCount(versionModuleList.stream().mapToInt(ListXmindDetailVO::getTestcaseCount).sum());
        return Arrays.asList(detailVO);
    }

    private List<ListTestcaseVO> getListXmindDetailVO(PageXmindDetailQuery query) {
        buildPageTestcaseQuery(query);
        // 直接展开 or 作为根节点展开
        if (StringUtils.isEmpty(query.getParentCode())) {
            query.setParentCode(query.getCaseCode());
        }
        return getListTestcaseNoGroup(query);
    }

    private List<ListTestcaseVO> getListTestcaseNoGroup(PageTestcaseQuery query) {
        if (TestcaseGroupTypeEnum.ALL.equals(query.getGroupType()) || TestcaseGroupTypeEnum.NO_GROUP.equals(query.getGroupType())) {
            query.setParentCode(StringUtils.EMPTY);
        }
        List<ListTestcaseVO> topModule = new LinkedList<>();
        if (query.getPage() == 1) {
            if (!TestcaseGroupTypeEnum.NO_GROUP.equals(query.getGroupType())) {
                query.setTestcaseAttribute(TestcaseAttributeEnum.MODULE);
                query.setAutomaticSourceCode(StringUtils.defaultString(query.getAutomaticSourceCode()));
                if (TestcaseTypeEnum.SOURCERECORD.equals(query.getType())) {
                    query.setAutomaticSourceCode(query.getParentCode());
                    query.setType(TestcaseTypeEnum.AUTO);
                    query.setParentCode(StringUtils.EMPTY);
                }
                topModule.addAll(testcaseRepository.selectTestCaseModuleList(query));
                appendModuleCaseCount(topModule, query);
            }
            if (TestcaseGroupTypeEnum.ALL.equals(query.getGroupType()) && TestcaseTypeEnum.MANUAL.equals(query.getType())) {
                if (CollectionUtil.isNotEmpty(query.getPlanCaseVersionCodeList())) {
                    //测试计划xmind，按版本维度展开
                    List<CountNoGroupCaseVO> noGroupCaseList = testcaseRepository.countNoGroupTestcaseByVersionCodeList(query);
                    if (CollectionUtil.isNotEmpty(noGroupCaseList)) {
                        query.getPlanCaseVersionCodeList().forEach(versionCode -> {
                            List<CountNoGroupCaseVO> versionCountList = noGroupCaseList.stream().filter(item -> (item.getVersionCode().equals(versionCode))).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(versionCountList)) {
                                ListTestcaseVO noGroup = new ListTestcaseVO();
                                noGroup.setVersionCode(versionCode);
                                noGroup.setCode(TestcaseGroupTypeEnum.NO_GROUP.name() + "___" + versionCode);
                                noGroup.setName(TestcaseGroupTypeEnum.NO_GROUP.getDesc());
                                noGroup.setType(TestcaseTypeEnum.MANUAL);
                                noGroup.setAttribute(TestcaseAttributeEnum.MODULE);
                                noGroup.setStatus(TestcaseStatusEnum.NORMAL);
                                noGroup.setTestcaseCount(CollectionUtil.isNotEmpty(versionCountList) ? versionCountList.get(0).getCaseCount() : 0);
                                noGroup.setHasChildren(noGroup.getTestcaseCount() > 0);
                                noGroup.setChildren(new ArrayList<>());
                                topModule.add(0, noGroup);
                            }
                        });
                    }
                } else {
                    ListTestcaseVO noGroup = new ListTestcaseVO();
                    noGroup.setVersionCode(query.getVersionCode());
                    noGroup.setCode(TestcaseGroupTypeEnum.NO_GROUP.name());
                    noGroup.setName(TestcaseGroupTypeEnum.NO_GROUP.getDesc());
                    noGroup.setType(TestcaseTypeEnum.MANUAL);
                    noGroup.setAttribute(TestcaseAttributeEnum.MODULE);
                    noGroup.setStatus(TestcaseStatusEnum.NORMAL);
                    noGroup.setTestcaseCount(testcaseRepository.countNoGroupTestcase(query).intValue());
                    noGroup.setHasChildren(noGroup.getTestcaseCount() > 0);
                    noGroup.setChildren(new ArrayList<>());
                    topModule.add(0, noGroup);
                }
            }
            if (query.isFilter()) {
                topModule.removeIf(t -> t.getTestcaseCount() == 0);
            }
            if (StringUtil.isNotBlank(query.getSceneTopModuleCode())) {
                topModule.sort(Comparator.comparingInt(o -> {
                    if (query.getSceneTopModuleCode().equals(o.getCode())) {
                        return -1;
                    }
                    if (StringUtil.isNotBlank(o.getPath()) && o.getPath().contains(query.getSceneTopModuleCode())) {
                        return -1;
                    } else {
                        return 0;
                    }
                }));
            }
        }
        return topModule;
    }

    private void appendModuleCaseCount(List<ListTestcaseVO> modules, PageTestcaseQuery query) {
        if (CollectionUtil.isEmpty(modules)) {
            return;
        }
        List<AutomaticNodeTypeEnum> nodeTypeList = query.getNodeTypeList();
        if (CollectionUtil.isNotEmpty(nodeTypeList)) {
            query.setNodeTypeList(nodeTypeList.stream()
                    .filter(n -> TestcaseAttributeEnum.TESTCASE.equals(n.getAttribute()))
                    .collect(Collectors.toList()));
        }
        List<SimpleTestcaseVO> allPath = testcaseRepository.selectAllTestCasePath(query);
        query.setNodeTypeList(nodeTypeList);
        if (Boolean.TRUE.equals(query.getFilterModule())) {
            List<String> moduleCodeList = testcaseRepository.selectModuleCodeList(query);
            query.setFilterModuleCodeList(moduleCodeList);
            allPath.removeIf(item -> moduleCodeList.stream().noneMatch(s -> StringUtils.defaultString(item.getPath()).contains(s)));
        }
        Map<String, AtomicInteger> moduleCntMap = modules
                .stream()
                .collect(Collectors.toMap(ListTestcaseVO::getCode, item -> new AtomicInteger(item.getTestcaseCount())));
        allPath.stream().parallel().forEach(item -> doModuleCnt(item, moduleCntMap));
        modules.forEach(item -> item.setTestcaseCount(moduleCntMap.getOrDefault(item.getCode(), new AtomicInteger(0)).intValue()));
    }

    public List<String> listTestcaseCode(ListTestcaseCodeQuery query) {
        log.info("ListTestcaseCodeQuery >>> {}", JSON.toJSONString(query));
        if (TestcaseTypeEnum.SOURCERECORD.equals(query.getType())) {
            query.setType(TestcaseTypeEnum.AUTO);
        }
        if (TestcaseGroupTypeEnum.ALL.name().equals(query.getParentCode())) {
            query.setParentCode(null);
        }
        if (TestcaseGroupTypeEnum.NO_GROUP.name().equals(query.getParentCode())) {
            query.setParentCode(StringUtils.EMPTY);
        }
        if (TestcaseTypeEnum.MANUAL.equals(query.getType()) && CollectionUtils.isEmpty(query.getSetCoreList())) {
            query.setSetCoreList(Collections.singletonList(Boolean.FALSE));
        }
        List<AutomaticNodeTypeEnum> moduleNodeTypeList = null;
        if (CollectionUtils.isNotEmpty(query.getNodeTypeList())) {
            moduleNodeTypeList = query.getNodeTypeList().stream()
                    .filter(n -> TestcaseAttributeEnum.MODULE.equals(n.getAttribute()))
                    .collect(Collectors.toList());
            if (query.getNodeTypeList().contains(AutomaticNodeTypeEnum.OTHER)) {
                moduleNodeTypeList.addAll(Arrays.asList(AutomaticNodeTypeEnum.TestPlan, AutomaticNodeTypeEnum.ThreadGroup));
            }
            query.setNodeTypeList(query.getNodeTypeList().stream()
                    .filter(n -> TestcaseAttributeEnum.TESTCASE.equals(n.getAttribute()))
                    .collect(Collectors.toList()));
        }
        List<SimpleTestcaseVO> testcaseList = testcaseRepository.selectList(query);
        if (CollectionUtils.isNotEmpty(moduleNodeTypeList)) {
            PageTestcaseQuery pageTestcaseQuery = new PageTestcaseQuery();
            pageTestcaseQuery.setProductCode(query.getProductCode());
            pageTestcaseQuery.setNodeTypeList(moduleNodeTypeList);
            pageTestcaseQuery.setPlanPattern(query.getPlanPattern());
            List<String> moduleCodeList = testcaseRepository.selectModuleCodeList(pageTestcaseQuery);
            testcaseList.removeIf(item -> moduleCodeList.stream().noneMatch(s -> StringUtils.defaultString(item.getPath()).contains(s)));
        }

        return testcaseList.stream().map(SimpleTestcaseVO::getCode).collect(Collectors.toList());
    }

    public List<ListTestcaseModuleVO> listModule(ListTestcaseModuleQuery query) {
        log.info("ListTestcaseModuleQuery >>> {}", JSON.toJSONString(query));
        //标记场景分组
        if (query.getType().equals(TestcaseTypeEnum.AUTO)) {
            String code = testcaseRepository.selectTopModuleCodeByName("用例工厂", query.getProductCode());
            query.setSceneTopModuleCode(StringUtil.isBlank(code) ? Strings.EMPTY : code);
        }
        List<ListTestcaseModuleVO> moduleList = testcaseRepository.selectModuleList(query);
        Map<String, Long> countMap = new HashMap<>();
        if (!Boolean.TRUE.equals(query.getMenuPattern())) {
            PageTestcaseQuery pageTestcaseQuery = new PageTestcaseQuery();
            pageTestcaseQuery.setProductCode(query.getProductCode());
            pageTestcaseQuery.setType(query.getType());
            pageTestcaseQuery.setSetCore(query.getSetCore());
            pageTestcaseQuery.setVersionCode(query.getVersionCode());
            pageTestcaseQuery.setPlanPattern(query.getPlanPattern());
            pageTestcaseQuery.setPlanCode(query.getPlanCode());
            pageTestcaseQuery.setTestStage(query.getTestStage());
            pageTestcaseQuery.setFactoryPattern(query.getFactoryPattern());
            List<SimpleTestcaseVO> allPath = testcaseRepository.selectAllTestCasePath(pageTestcaseQuery);
            countMap = allPath.parallelStream().flatMap(vo -> {
                Stream<String> s1 = Arrays.stream(vo.getPath().split("\\."));
                if (StringUtils.isNotEmpty(vo.getTestcaseModulePath())) {
                    Stream<String> s2 = Arrays.stream(vo.getTestcaseModulePath().split("\\."));
                    return Stream.concat(s1, s2).distinct();
                }
                return s1.distinct();
            }).collect(Collectors.groupingBy(s -> s, Collectors.counting()));
        }
        if (Boolean.TRUE.equals(query.getMenuPattern()) && StringUtils.isNotEmpty(query.getSceneTopModuleCode())) {
            moduleList.removeIf(vo -> query.getSceneTopModuleCode().equals(vo.getCode())
                    || vo.getPath().contains(query.getSceneTopModuleCode()));
        }
        Map<String, Long> finalCountMap = countMap;
        if (Boolean.TRUE.equals(query.getPlanPattern())) {
            moduleList.removeIf(vo -> !finalCountMap.containsKey(vo.getCode()));
        }
        Map<String, List<ListTestcaseModuleVO>> moduleMap = moduleList.parallelStream()
                .collect(Collectors.groupingBy(ListTestcaseModuleVO::getParentCode));
        moduleList.parallelStream().forEach(vo -> {
            List<ListTestcaseModuleVO> children = moduleMap.get(vo.getCode());
            vo.setChildren(children);
            int testcaseCount = finalCountMap.getOrDefault(vo.getCode(), 0L).intValue();
            vo.setTestcaseCount(testcaseCount);
        });
        List<ListTestcaseModuleVO> tree = moduleMap.getOrDefault(StringUtils.EMPTY, new ArrayList<>(0));
        if (TestcaseTypeEnum.MANUAL.equals(query.getType())) {
            int noGroupTestcaseCount = finalCountMap.getOrDefault(StringUtils.EMPTY, 0L).intValue();
            if (noGroupTestcaseCount > 0) {
                ListTestcaseModuleVO noGroup = new ListTestcaseModuleVO();
                noGroup.setCode(TestcaseGroupTypeEnum.NO_GROUP.name());
                noGroup.setName(TestcaseGroupTypeEnum.NO_GROUP.getDesc());
                noGroup.setAttribute(TestcaseAttributeEnum.MODULE);
                noGroup.setTestcaseCount(noGroupTestcaseCount);
                tree.add(0, noGroup);
            }
            if (Boolean.TRUE.equals(query.getMenuPattern())) {
                return tree;
            }
        }
        int allTestcaseCount = tree.parallelStream().mapToInt(ListTestcaseModuleVO::getTestcaseCount).sum();
        ListTestcaseModuleVO all = new ListTestcaseModuleVO();
        all.setCode(TestcaseGroupTypeEnum.ALL.name());
        all.setName(TestcaseGroupTypeEnum.ALL.getDesc());
        all.setTestcaseCount(allTestcaseCount);
        all.setChildren(tree);
        return Collections.singletonList(all);
    }

    public Page pageTestcase(PageTestcaseQuery query) {
        Page<ListTestcaseVO> page = new Page();
        buildPageTestcaseQuery(query);
        List<ListTestcaseVO> topModule = getListTestcaseNoGroup(query);
        com.github.pagehelper.Page<ListTestcaseVO> pageInfo = PageHelper.startPage(query.getPage(), query.getSize());
        getListTestcaseGroup(topModule, query);
        buildEnableChecked(topModule, query);
        if (CollectionUtil.isEmpty(topModule)) {
            page.setTotal(0);
            page.setSize(query.getSize());
            page.setPage(pageInfo.getPageNum());
            page.setList(Collections.emptyList());
            return page;
        }
        page.setList(topModule);
        page.setTotal(pageInfo.getTotal());
        page.setSize(pageInfo.getPageSize());
        page.setPage(pageInfo.getPageNum());
        return page;
    }

    private void buildEnableChecked(List<ListTestcaseVO> topModule, PageTestcaseQuery query) {
        int moduleControllerCount = 0;
        if (TestcaseTypeEnum.AUTO.equals(query.getType()) && StringUtils.isNotEmpty(query.getAutomaticSourceCode())
                && StringUtils.isNotEmpty(query.getParentCode())) {
            TestcaseEntityDO entityDO = testcaseRepository.selectTestcaseByCode(query.getParentCode());
            if (null != entityDO && StringUtils.isNotEmpty(entityDO.getPath())) {
                List<String> codeList = new ArrayList<>(Arrays.asList(entityDO.getPath().split("\\.")));
                codeList.add(query.getParentCode());
                moduleControllerCount = testcaseRepository.countByNodeTypeAndCodeList(AutomaticNodeTypeEnum.ModuleController, codeList);
            }
        }
        Boolean enableChecked = moduleControllerCount == 0;
        Boolean sceneFlag = checkSceneFlag(query);
        topModule.forEach(t -> {
            t.setEnableChecked(enableChecked);
            if (null != sceneFlag) {
                t.setSceneFlag(sceneFlag);
            }
        });
    }

    /**
     * 校验当前查询，是否属于用例工厂
     *
     * @param query {@link PageTestcaseQuery}
     * @return
     */
    private Boolean checkSceneFlag(PageTestcaseQuery query) {
        if (!query.getType().equals(TestcaseTypeEnum.AUTO) || TestcaseGroupTypeEnum.ALL.equals(query.getGroupType())) {
            return null;
        }
        if (StringUtils.isNotBlank(query.getAutomaticSourceCode())) {
            int countNumScene = apiTestRepository.countEnableBySourceCode(query.getAutomaticSourceCode());
            int countNumApi = apiTestRepository.countApiEnableBySourceCode(query.getAutomaticSourceCode());
            return (countNumScene > 0) || (countNumApi > 0);
        }
        if (StringUtils.isNotBlank(query.getParentCode())) {
            TestcaseEntityDO entityDO = testcaseRepository.selectTestcaseByCode(query.getParentCode());
            return null != entityDO
                    && (entityDO.getCode().equals(query.getSceneTopModuleCode())
                    || (StringUtil.isNotBlank(entityDO.getPath()) && entityDO.getPath().contains(query.getSceneTopModuleCode())));
        }
        return Boolean.FALSE;
    }

    private void buildPageTestcaseQuery(PageTestcaseQuery query) {
        if (StringUtils.isNotBlank(query.getCodeOrTitle())) {
            String search =
                    query.getCodeOrTitle().replaceAll("/", "//").replaceAll("%", "/%").replaceAll("_", "/_");
            query.setCodeOrTitle(search);
            query.setFilter(true);
        }
        boolean isFilter = CollectionUtil.isNotEmpty(query.getTagList()) || CollectionUtil.isNotEmpty(query.getPriorityList())
                || CollectionUtil.isNotEmpty(query.getStatusList()) || CollectionUtil.isNotEmpty(query.getDutyUserList())
                || CollectionUtil.isNotEmpty(query.getCreatorList()) || CollectionUtil.isNotEmpty(query.getNodeTypeList())
                || query.getCreateTimeBegin() != null || query.getCreateTimeEnd() != null
                || query.getModifyTimeBegin() != null || query.getModifyTimeEnd() != null
                || query.getIsHeart() != null || CollectionUtil.isNotEmpty(query.getAutomaticTypeList())
                || (query.getPlanPattern() == null && StringUtils.isNotBlank(query.getPlanCode()));
        if (isFilter) {
            query.setFilter(true);
        }
        if (CollectionUtils.isNotEmpty(query.getNodeTypeList())) {
            if (query.getNodeTypeList().contains(AutomaticNodeTypeEnum.OTHER)) {
                query.getNodeTypeList().addAll(Arrays.asList(AutomaticNodeTypeEnum.TestPlan, AutomaticNodeTypeEnum.ThreadGroup));
            }
            if (query.getNodeTypeList().stream().anyMatch(n -> TestcaseAttributeEnum.MODULE.equals(n.getAttribute()))) {
                query.setFilterModule(true);
            }
        }
        if (CollectionUtils.isNotEmpty(query.getIsHeart())) {
            query.setIsHeartList(query.getIsHeart().stream().map(HeartCaseFilterEnum::getValue).collect(Collectors.toList()));
        }
        //标记场景分组
        if (query.getType().equals(TestcaseTypeEnum.AUTO)) {
            String code = testcaseRepository.selectTopModuleCodeByName("用例工厂", query.getProductCode());
            query.setSceneTopModuleCode(StringUtil.isBlank(code) ? Strings.EMPTY : code);
        }
    }


    private void relatedCaseExecutionStatus(List<ListTestcaseVO> testcaseList, PageTestcaseQuery query) {
        if (StringUtil.isNotBlank(query.getPlanCode()) && null != query.getTestStage() && null != query.getType()) {
            List<TmTestPlanCaseEntityDO> planCaseList = tmTestPlanCaseRepository.selectListByStageAndType(query);
            if (CollectionUtil.isNotEmpty(planCaseList)) {
                Map<String, TestPlanCaseStatusEnum> caseMap = planCaseList.stream()
                        .collect(Collectors.toMap(TmTestPlanCaseEntityDO::getCaseCode, TmTestPlanCaseEntityDO::getStatus));
                testcaseList.stream().forEach(testcase -> {
                    testcase.setExecutionStatus(caseMap.get(testcase.getCode()));
                    testcase.setExecutionStatusDesc(caseMap.get(testcase.getCode()) != null ?
                            caseMap.get(testcase.getCode()).getValue() : "");
                });
            }
        }
    }

    private void doModuleCnt(SimpleTestcaseVO vo, Map<String, AtomicInteger> moduleCntMap) {
        if (StringUtils.isNotEmpty(vo.getPath())) {
            String[] split = vo.getPath().split("\\.");
            Arrays.stream(split).distinct().forEach(s -> {
                if (moduleCntMap.containsKey(s)) {
                    moduleCntMap.get(s).incrementAndGet();
                }
            });
        }
        if (StringUtils.isNotEmpty(vo.getTestcaseModulePath())) {
            String[] split = vo.getTestcaseModulePath().split("\\.");
            Arrays.stream(split).distinct().forEach(s -> {
                if (moduleCntMap.containsKey(s)) {
                    moduleCntMap.get(s).incrementAndGet();
                }
            });
        }
    }

    private ListTestcaseVO getChildren(ListTestcaseVO vo, TestcaseQuery query) {
        query.setParentCode(vo.getCode());
        PageTestcaseQuery pageTestcaseQuery = new PageTestcaseQuery();
        BeanUtils.copyProperties(query, pageTestcaseQuery);
        List<ListTestcaseVO> testcaseList = testcaseRepository.pageTestcase(pageTestcaseQuery);
        List<String> codeList = testcaseList.stream().map(ListTestcaseVO::getCode).collect(Collectors.toList());
        List<TagVO> allTags = tagRepository.getTagsByCodeList(codeList);
        testcaseList.forEach(t -> {
            List<TagVO> tags = allTags.stream().filter(e -> t.getCode().equals(e.getBusinessCode())).collect(Collectors.toList());
            t.setTags(tags);
            List<TestcaseButtonEnum> buttons = getTestcaseButtons(t.getType(), t.getStatus(), "list", query.getSetCore());
            t.setButtons(buttons);
        });
        if (CollectionUtil.isNotEmpty(testcaseList)) {
            vo.setChildren(testcaseList);
        }
        vo.setTestcaseCount(testcaseRepository.selectTestCaseCount(query));
        List<ListTestcaseVO> childModules = testcaseRepository.selectTestCaseModuleList(pageTestcaseQuery);
        if (CollectionUtil.isNotEmpty(childModules)) {
            for (int i = 0; i < childModules.size(); i++) {
                ListTestcaseVO listTestcaseVO = getChildren(childModules.get(i), query);
                if (null != listTestcaseVO) {
                    testcaseList.add(listTestcaseVO);
                    vo.setChildren(testcaseList);
                }
            }
        }
        if (vo.getTestcaseCount() == 0) {
            return null;
        }
        return vo;
    }

    /**
     * 获取用例可操作按钮
     *
     * @param type   类型
     * @param status 用例状态
     * @return 可操作按钮
     */
    private List<TestcaseButtonEnum> getTestcaseButtons(TestcaseTypeEnum type, TestcaseStatusEnum status,
                                                        String buttonPositionType, Boolean setCore) {
        List<TestcaseButtonEnum> buttons = new ArrayList<>();
        if (StringUtil.isNotEmpty(buttonPositionType) && buttonPositionType.equals("details")) {
            buttons.add(TestcaseButtonEnum.RELATED_REQUIREMENT);
        }
        if (TestcaseTypeEnum.MANUAL.equals(type)) {
            buttons.add(TestcaseButtonEnum.COPY);
            buttons.add(TestcaseButtonEnum.MOVE);
            buttons.add(TestcaseButtonEnum.DELETE);

            if (null != setCore && !setCore) {
                buttons.add(TestcaseButtonEnum.SET_CORE);
                if (StringUtil.isNotEmpty(buttonPositionType) && buttonPositionType.equals("list")) {
                    buttons.add(TestcaseButtonEnum.CHANGE_VERSION);
                }
            }
            if (TestcaseStatusEnum.NORMAL.equals(status)) {
                buttons.add(TestcaseButtonEnum.JOIN_TEST_PLAN);
                buttons.add(TestcaseButtonEnum.DISABLE);
            }
            if (TestcaseStatusEnum.DISABLE.equals(status)) {
                buttons.add(TestcaseButtonEnum.ENABLE);
            }
        }
        if (TestcaseTypeEnum.AUTO.equals(type) && TestcaseStatusEnum.NORMAL.equals(status)) {
            buttons.add(TestcaseButtonEnum.JOIN_TEST_PLAN);
        }
        buttons.sort(Comparator.comparingInt(TestcaseButtonEnum::ordinal));
        return buttons;
    }

    public void buildButtonPermission(List<ListTestcaseVO> list, ListTestcaseReq req, UserInfo userInfo) {
        if (null == userInfo) {
            return;
        }
        User user = userInfo.toSimpleUser();
        //是否是测试角色
        Boolean permission = checkUserProductPermission(req.getProductCode(), user);
        // 一次查询当前用户修改的所有用例
        FindHeartCaseByUserQuery query = new FindHeartCaseByUserQuery();
        query.setProductCode(req.getProductCode());
        query.setUserId(user.getUserId());
        List<TestHeartCaseVO> caseVOList = findHeartCaseByUserQuery(query);
        // caseVOList为空，直接判断当前用户角色
        if (CollectionUtils.isEmpty(caseVOList)) {
            list.stream().filter(v -> TestcaseAttributeEnum.TESTCASE.equals(v.getAttribute())).forEach(vo -> {
                vo.setSwitchStatus(HeartCaseSwitchEnum.getEnum(permission));
            });
            return;
        }
        // caseVOList不为空
        list.stream().filter(v -> TestcaseAttributeEnum.TESTCASE.equals(v.getAttribute())).forEach(vo -> {
            // 用例禁用不能操作
            if (vo.getStatus().equals(TestcaseStatusEnum.DISABLE)) {
                vo.setSwitchStatus(HeartCaseSwitchEnum.DISABLE);
                return;
            }
            vo.setSwitchStatus(permission ? HeartCaseSwitchEnum.ENABLE : heartCaseVOQuery(vo, caseVOList));
        });
    }

    private List<TestHeartCaseVO> findHeartCaseByUserQuery(FindHeartCaseByUserQuery query) {
        log.info("FindHeartCaseByUserQuery >>" + JsonUtil.toJSON(query));
        return testcaseRepository.selectAllHeartCase(query);
    }

    private HeartCaseSwitchEnum heartCaseVOQuery(ListTestcaseVO vo, List<TestHeartCaseVO> caseVOList) {
        if (!vo.getSetHeart()) {
            return HeartCaseSwitchEnum.DISABLE;
        }
        // 当前用户所有创建/修改用例匹配当前code
        List<TestHeartCaseVO> list = caseVOList.stream().filter(v ->
                v.getCaseCode().equals(vo.getCode())).collect(Collectors.toList());
        return HeartCaseSwitchEnum.getEnum(CollectionUtils.isNotEmpty(list));
    }

    /**
     * 检查当前用户是否有产品权限
     *
     * @param productCode
     * @param user
     * @return
     */
    private Boolean checkUserProductPermission(String productCode, User user) {
        ListProductMemberByPIdQuery productQuery = new ListProductMemberByPIdQuery();
        Long userId = user.getUserId();
        List<String> memberTypes = Arrays.asList(
                MemberTypeEnum.TESTER_OWNER.name(),
                MemberTypeEnum.TESTER_M.name(),
                MemberTypeEnum.TESTER.name(),
                MemberTypeEnum.SUPPER.name());
        productQuery.setProductCode(productCode);
        productQuery.setMemberTypes(memberTypes);
        Boolean productPermission = productRpcService.checkProductPermission(userId, productQuery);
        return productPermission;
    }

    public ListTestcaseExecuteRecordVO getExecuteRecords(PageExecuteRecordQuery query) {
        log.info("ListExecuteRecordQuery >>> {}", query.getCode());
        ListTestcaseExecuteRecordVO recordVO = new ListTestcaseExecuteRecordVO();
        com.github.pagehelper.Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        List<TestcaseExecuteRecordVO> vos = testcaseRepository.selectRecordByCode(query.getCode(), query.getType());
        if (CollectionUtils.isNotEmpty(vos)) {
            vos.forEach(vo -> {
                vo.setExecLogFile(getFullFilepath(vo.getExecLogFile()));
                vo.setReportFile(getFullFilepath(vo.getReportFile()));
                vo.setResultDesc(vo.getResult().getValue());
                vo.setTestStageDesc(null == vo.getTestStage() ? null : vo.getTestStage().getValue());
            });
        }
        recordVO.setList(vos);
        recordVO.setTotal(page.getTotal());
        return recordVO;
    }

    public TestcaseVO findTestcaseEntityByCodeQuery(FindTestcaseEntityByCodeQuery query) {
        return testcaseRepository.getTestcaseVOByCodeQuery(query);
    }

    public Integer findTestcaseModulePathQuery(FindTestcaseModulePathQuery query) {
        String path = testcaseRepository.selectTestcaseModulePath(query);
        if (StringUtil.isEmpty(path) || StringUtil.isEmpty(path.replace(query.getOldPath(), ""))) {
            return 0;
        }
        String fullPath = query.getNewPath() + "." + query.getCode() + path.replace(query.getOldPath(),
                "");
        return fullPath.split("\\.").length;
    }

    public List<String> xmindFilterQuery(XmindFilterQuery filterQuery) {
        PageTestcaseQuery query = testcaseDomainConverter.convert(filterQuery);
        buildPageTestcaseQuery(query);
        query.setParentCode(null);
        List<ListTestcaseVO> filterCaseList = testcaseRepository.pageTestcase(query);
        // 筛选parentCode
        if (null != filterQuery.getParentCode()) {
            filterCaseList = filterCaseList.stream()
                    .filter(t -> t.getPath().contains(filterQuery.getParentCode()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(filterCaseList)) {
            List<String> codeList = filterCaseList.stream().map(ListTestcaseVO::getCode).distinct().collect(Collectors.toList());
            // 筛选执行人、执行结果
            if (CollectionUtil.isNotEmpty(filterQuery.getExecutorIdList())
                    || CollectionUtil.isNotEmpty(filterQuery.getResultList())) {
                codeList = filterCodeList(filterQuery, codeList);
            }
            return codeList;
        }
        return CollectionUtil.newEmptyList();
    }

    private List<String> filterCodeList(XmindFilterQuery filterQuery, List<String> codeList) {
        List<TmTestPlanCaseEntityDO> entityList = tmTestPlanCaseRepository.getTestPlanCaseList(filterQuery, codeList);
        if (CollectionUtil.isEmpty(entityList)) {
            return CollectionUtil.newEmptyList();
        }
        return entityList.stream().map(TmTestPlanCaseEntityDO::getCaseCode).distinct().collect(Collectors.toList());
    }


    public List<ListTestcaseVO> handle(ListTestcaseExpQuery query) {
        log.info("ListTestcaseExpQuery >>> {}", JSON.toJSONString(query));
        List<ListTestcaseVO> list = new ArrayList<>();
        switch (query.getGroupType()) {
            case ALL:   //只查询顶级目录和目录对应的用例数量  手动用例包含未分组用例
                query.setParentCode(null);
                break;
            case GROUP: //查询直系分组(包含用例数量)和用例
                query.setPath(query.getParentCode());
                query.setParentCode(null);
                break;
            case NO_GROUP: //查询所有父节点为空的用例
                query.setParentCode(StringUtils.EMPTY);
                break;
        }

        query.setTestcaseAttribute(TestcaseAttributeEnum.TESTCASE);

        List<TestcaseEntityDO> testcaseList;
        if (query.getExportType().equals(ExportTestCaseEnum.FILTER)) {
            testcaseList = testcaseRepository.getTestcaseNew(testcaseDomainConverter.convertor(query));
        } else {
            testcaseList = testcaseRepository.getCheckTestCaseList(query);
        }
        if (CollectionUtils.isEmpty(testcaseList)) {
            return Collections.emptyList();
        }
        Map<String, ListTestcaseModuleVO> mapParentCode = getParentCodeTestcaseModuleVOMap(query.getProductCode(), query.getTransactor());

        List<String> caseCodeList = testcaseList.stream().map(TestcaseEntityDO::getCode).distinct().collect(Collectors.toList());

        List<TagVO> tagVOS = getTags(caseCodeList);
        Map<String, List<TagVO>> businessCodeTagVOListMap = tagVOS.stream().collect(Collectors.groupingBy(TagVO::getBusinessCode));
        List<TestcaseStepVO> stepVOS = getSteps(caseCodeList);
        Map<String, List<TestcaseStepVO>> testcaseCodeStepVOListMap = stepVOS.stream().collect(Collectors.groupingBy(TestcaseStepVO::getTestcaseCode));

        Map<String, Integer> caseCodeExecuteNumMap = getExecuteNum(caseCodeList);

        testcaseList.stream().forEach(t -> {
            ListTestcaseVO vo = new ListTestcaseVO();
            testcaseDomainConverter.converter(t, vo);

//            int executeNum = getExecuteNum(vo.getCode());
            Integer executeNum = caseCodeExecuteNumMap.get(vo.getCode());
            vo.setExecuteNum(executeNum != null ? executeNum : 0);
            if (TestcaseTypeEnum.MANUAL.equals(vo.getType())) {
                List<TagVO> tags = businessCodeTagVOListMap.get(vo.getCode());
//                List<TagVO> tags = getTags(vo.getCode());
                vo.setTags(tags);
            }

            ListTestcaseModuleVO listTestcaseModuleVO = mapParentCode.get(t.getParentCode());
            if (listTestcaseModuleVO != null) {
                vo.setFullName(listTestcaseModuleVO.getFullName());
                vo.setParentIds(listTestcaseModuleVO.getParentIds());
            }

//            List<TestcaseStepVO> steps = getSteps(vo.getCode());
            List<TestcaseStepVO> steps = testcaseCodeStepVOListMap.get(vo.getCode());
            vo.setTestSteps(steps);

            list.add(vo);
        });
//        if (query.getPage() > 0 && query.getSize() > 0) {
//            int start = (query.getPage() - 1) * query.getSize();
//            int end = query.getPage() * query.getSize();
//            return list.subList(start >= list.size() ? list.size() - 1 : start, Math.min(end, list.size()));
//        }
        return list;
    }

    private Map<String, ListTestcaseModuleVO> getParentCodeTestcaseModuleVOMap(String productCode, User user) {
        SimpleTestcaseModuleQuery query = new SimpleTestcaseModuleQuery();
        query.setType(TestcaseTypeEnum.MANUAL);
        query.setProductCode(productCode);
        query.setTransactor(user);

        List<ListTestcaseModuleVO> testcaseModuleVOList = simpleTestcaseModuleQuery(query);
        Map<String, ListTestcaseModuleVO> mapParentCode = new HashMap<>();
        if (CollectionUtil.isNotEmpty(testcaseModuleVOList)) {
            mapParentCode = testcaseModuleVOList.stream().collect(Collectors.toMap(ListTestcaseModuleVO::getCode, Function.identity(), (key1, key2) -> key2));
        }
        return mapParentCode;
    }


    public List<ListTestcaseModuleVO> simpleTestcaseModuleQuery(SimpleTestcaseModuleQuery query) {
        log.info("SimpleTestcaseModuleQuery >>> {}", JSON.toJSONString(query));

        List<TestcaseEntityDO> list = testcaseRepository.simpleTestcaseModuleQuery(query);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<ListTestcaseModuleVO> listTestcaseModuleVOList = getTestcaseModuleTree(list, StringUtils.EMPTY);
        List<ListTestcaseModuleVO> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(listTestcaseModuleVOList)) {
            initFullName(result, null, listTestcaseModuleVOList);
        }
        return result;
    }

    /**
     * 获取用例模块树
     *
     * @param list       {@link TestcaseEntityDO}
     * @param parentCode 父级code
     * @return {@link ListTestcaseModuleVO}
     */
    private List<ListTestcaseModuleVO> getTestcaseModuleTree(List<TestcaseEntityDO> list, String parentCode) {
        return list.parallelStream()
                .filter(entity -> parentCode.equals(entity.getParentCode()))
                .map(entity -> {
                    ListTestcaseModuleVO vo = new ListTestcaseModuleVO();
                    testcaseDomainConverter.converter(entity, vo);
                    List<ListTestcaseModuleVO> children = getTestcaseModuleTree(list, vo.getCode());
                    vo.setChildren(children);
                    return vo;
                })
                .collect(Collectors.toList());
    }

    private void initFullName(List<ListTestcaseModuleVO> result,
                              ListTestcaseModuleVO parent,
                              List<ListTestcaseModuleVO> testcaseModuleVOList) {

        for (ListTestcaseModuleVO testcaseModuleVO : testcaseModuleVOList) {
            if (parent == null) {
                testcaseModuleVO.setFullName(testcaseModuleVO.getName());
                testcaseModuleVO.setParentIds(String.valueOf(testcaseModuleVO.getId()));
            } else {
                testcaseModuleVO.setParentIds(String.format("%s,%s", parent.getParentIds(), testcaseModuleVO.getId()));
                testcaseModuleVO.setFullName(parent.getFullName() + ">" + testcaseModuleVO.getName());
            }

            if (CollectionUtil.isNotEmpty(testcaseModuleVO.getChildren())) {
                initFullName(result, testcaseModuleVO, testcaseModuleVO.getChildren());
            }

            result.add(testcaseModuleVO);
        }
    }


    /**
     * 获取标签
     *
     * @param codeList 用例code
     * @return 标签
     */
    private List<TagVO> getTags(List<String> codeList) {
        List<TagEntityDO> tagList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(codeList)) {
            List<List<String>> bizCodePartition = Lists.partition(new ArrayList(codeList), 200);
            for (List<String> codes : bizCodePartition) {
                if (CollectionUtil.isNotEmpty(codes)) {
                    List<TagEntityDO> tempList = testcaseTagRepository.selectTestcaseTagByCaseCodeList(codes);
                    if (CollectionUtil.isNotEmpty(tempList)) {
                        tagList.addAll(tempList);
                    }
                }
            }
        }

        return testcaseTagConverter.convert(tagList);
    }

    /**
     * 获取步骤
     *
     * @param codeList 用例code
     * @return 标签
     */
    private List<TestcaseStepVO> getSteps(List<String> codeList) {
        List<TestcaseStepEntityDO> stepList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(codeList)) {
            List<List<String>> bizCodePartition = Lists.partition(new ArrayList(codeList), 200);
            for (List<String> codes : bizCodePartition) {
                if (CollectionUtil.isNotEmpty(codes)) {
                    List<TestcaseStepEntityDO> tempList = testcaseStepRepository.selectTestcaseStepByCaseCodeList(codes);
                    if (CollectionUtil.isNotEmpty(tempList)) {
                        stepList.addAll(tempList);
                    }
                }
            }
        }

        return testcaseStepDomainConverter.converter(stepList);
    }


    /**
     * 获取用例执行次数
     *
     * @param codeList 用例code
     * @return 执行次数
     */
    private Map<String, Integer> getExecuteNum(List<String> codeList) {
        Map<String, Integer> codeExecuteNumMap = new HashMap<>();

        List<TestcaseExecuteNumVO> caseExecuteNumVOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(codeList)) {
            List<List<String>> bizCodePartition = Lists.partition(new ArrayList(codeList), 200);
            for (List<String> codes : bizCodePartition) {
                if (CollectionUtil.isNotEmpty(codes)) {
                    List<TestcaseExecuteNumVO> tempList = testcaseRepository.selectExecuteNumByTestcaseCodeList(codes);
                    if (CollectionUtil.isNotEmpty(tempList)) {
                        caseExecuteNumVOList.addAll(tempList);
                    }
                }
            }
        }

        codeExecuteNumMap = caseExecuteNumVOList.stream().collect(Collectors.toMap(TestcaseExecuteNumVO::getCode, TestcaseExecuteNumVO::getExecuteNum));

        return codeExecuteNumMap;
    }

    public PlanCaseVO planCaseDetail(FindPlanCaseQuery query) {
        log.info("FindPlanCaseQuery >>> {}", JSON.toJSONString(query));
        List<PlanCaseVO> list = tmTestPlanCaseRepository.selectPlanCase(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        PlanCaseVO vo = list.get(0);
        List<String> parentFullName = findParentFullNameQuery(vo.getPath());
        vo.setParentFullName(parentFullName);
        if (TestcaseTypeEnum.MANUAL.equals(vo.getType())) {
            ListTestcaseStepQuery stepQuery = new ListTestcaseStepQuery(vo.getCaseCode());
            List<TestcaseStepVO> testSteps = testcaseStepRepository.listTestcaseStepQuery(stepQuery);
            vo.setTestSteps(testSteps);
            ListTestcaseTagQuery tagQuery = new ListTestcaseTagQuery(vo.getCaseCode());
            List<TagVO> tags = testcaseTagRepository.listTestcaseTagQuery(tagQuery);
            vo.setTags(tags);
        }
        if (TestcaseTypeEnum.AUTO.equals(vo.getType())) {
            ExecuteCaseVO currentResult = testcaseExecuteRecordRepository.selectCurrentResult(query);
            if (null != currentResult && StringUtils.isNotBlank(currentResult.getResultFile())) {
                List<ExecuteCaseResultContentVO> resultContentVOList =
                        jenkinsService.getExecuteCaseResultContent(currentResult.getResultFile());
                vo.setResultContent(resultContentVOList);
                vo.setAutomaticTaskId(currentResult.getAutomaticTaskId());
                vo.setResultFile(getFullFilepath(currentResult.getResultFile()));
                vo.setReportFile(getFullFilepath(currentResult.getReportFile()));
                vo.setExecLogFile(getFullFilepath(currentResult.getExecLogFile()));
            }
        }
        boolean productPermission = checkTestUserPermission(query.getTransactor(), vo.getProductCode());
        // 计划负责人、用例执行人、用例创建人、用例责任人
        boolean userPermission = Optional.ofNullable(query.getTransactor())
                .map(User::getUserId)
                .map(userId -> userId.equals(vo.getPlanDirectorId())
                        || userId.equals(vo.getExecutorId())
                        || userId.equals(vo.getCreatorId())
                        || userId.equals(vo.getDutyUserId()))
                .orElse(false);
        boolean permission = (productPermission || userPermission) && vo.getEnable();
        List<TestcaseEditFieldEnum> fields = getPlanCaseFields(permission, vo.getType());
        vo.setFields(fields);
        boolean planStatusPermission =
                checkPlanStatusPermission(query.getTestStage(), vo.getPlanStatus(), vo.getStageStatus());
        if (permission && planStatusPermission) {
            List<TestcaseButtonEnum> buttons = new ArrayList<>();
            buttons.add(TestcaseButtonEnum.REMOVE_TEST_PLAN);
            vo.setButtons(buttons);
        }
        buildVersionInfo(vo);
        return vo;
    }


    public List<TestcaseIssueVO> handle(ListRelationIssueQuery query) {
        log.info("ListRelationIssueQuery >>> {}", query.getCode());
        return iTestcaseRelationRepository.selectIssueByTestcaseCode(query);
    }


    public List<TestcaseRequirementVO> handle(ListRelationRequirementQuery query) {
        log.info("ListRelationRequirementQuery >>> {}", query.getCodeList());
        List<TestcaseRelationEntityDO> testcaseRelationEntities = iTestcaseRelationRepository.selectTestcaseRelationEntities(query);
        if (CollectionUtils.isEmpty(testcaseRelationEntities)) {
            return Collections.emptyList();
        }
        RequirementByCodeListQuery requirementByCodeListQuery = new RequirementByCodeListQuery();
        List<String> collect = testcaseRelationEntities.stream().map(TestcaseRelationEntityDO::getBusinessCode).collect(Collectors.toList());
        requirementByCodeListQuery.setCodeList(collect);
        List<TestcaseRequirementVO> list;
        try {
            List<RequirementDetailVo> vos = iProjectRpcService.requirementByCodeListQuery(requirementByCodeListQuery);
            if (CollectionUtils.isEmpty(vos)) {
                return Collections.emptyList();
            }
            list = vos.stream().map(this::covert).collect(Collectors.toList());
        } catch (Exception e) {
            throw new ServiceException("执行项目域ListRelationRequirementQuery异常:" + e.getMessage());
        }
        return list;
    }


    private TestcaseRequirementVO covert(RequirementDetailVo vo) {
        TestcaseRequirementVO testcaseRequirementVO = new TestcaseRequirementVO();
        testcaseRequirementVO.setCode(vo.getCode());
        testcaseRequirementVO.setTitle(vo.getTitle());
        testcaseRequirementVO.setStatus(vo.getStatus());
        testcaseRequirementVO.setStatusDesc(vo.getStatusName());
        testcaseRequirementVO.setDockingUserName(vo.getDockingUserName());
        testcaseRequirementVO.setDockingUserId(vo.getDockingUserId());
        return testcaseRequirementVO;
    }

    /**
     * 获取计划用例可编辑字段
     *
     * @param permission 权限
     * @param type       类型
     * @return 可编辑字段
     */
    private List<TestcaseEditFieldEnum> getPlanCaseFields(Boolean permission, TestcaseTypeEnum type) {
        List<TestcaseEditFieldEnum> fields = new ArrayList<>();
        if (TestcaseTypeEnum.MANUAL.equals(type)) {
            fields.add(TestcaseEditFieldEnum.PREVIEW_ATTACHMENT);
            fields.add(TestcaseEditFieldEnum.DOWNLOAD_ATTACHMENT);
            if (permission) {
                fields.add(TestcaseEditFieldEnum.UPLOAD_ATTACHMENT);
                fields.add(TestcaseEditFieldEnum.DELETE_ATTACHMENT);
                fields.add(TestcaseEditFieldEnum.EXECUTOR);
                fields.add(TestcaseEditFieldEnum.ADD_ISSUE);
                fields.add(TestcaseEditFieldEnum.RELATED_ISSUE);
            }
        }
        if (TestcaseTypeEnum.AUTO.equals(type) && permission) {
            fields.add(TestcaseEditFieldEnum.EXECUTOR);
            fields.add(TestcaseEditFieldEnum.ADD_ISSUE);
            fields.add(TestcaseEditFieldEnum.RELATED_ISSUE);
        }
        fields.add(TestcaseEditFieldEnum.RESULT_COMMENT);
        return fields;
    }

    private void buildVersionInfo(PlanCaseVO vo) {
        try {
            VersionVO versionVO = iProjectRpcService.findVersionQuery(vo.getVersionCode());
            vo.setProductName(versionVO.getProductName());
            vo.setVersionName(versionVO.getName());
        } catch (Exception e) {
            throw new ServiceException("执行项目域SimpleVersionQuery异常:" + e.getMessage());
        }
    }

    /**
     * 校验计划状态权限
     *
     * @param currentStage 当前计划阶段
     * @param planStatus   计划状态
     * @param stageStatus  阶段状态
     * @return true/false
     */
    private boolean checkPlanStatusPermission(
            TestPlanStageEnum currentStage, TestPlanNewStatusEnum planStatus, Map<String, Object> stageStatus) {
        // 计划未开始
        if (TestPlanNewStatusEnum.NOT_STARTED.equals(planStatus)) {
            return true;
        }
        // 计划已完成或已终止
        if (TestPlanNewStatusEnum.COMPLETED.equals(planStatus) || TestPlanNewStatusEnum.TERMINATED.equals(planStatus)) {
            return false;
        }
        // 阶段未开始或进行中
        if (MapUtils.isNotEmpty(stageStatus)) {
            TestPlanNewStatusEnum status =
                    TestPlanNewStatusEnum.getEnumByName((String) stageStatus.get(currentStage.name()));
            return TestPlanNewStatusEnum.NOT_STARTED.equals(status) || TestPlanNewStatusEnum.IN_PROGRESS.equals(status);
        }
        return true;
    }

    /**
     * 校验产品测试用户权限
     *
     * @param currentUser 当前用户
     * @param productCode 产品code
     * @return true/false
     */
    public boolean checkTestUserPermission(User currentUser, String productCode) {
        if (null == currentUser || StringUtils.isBlank(productCode)) {
            return false;
        }
        // 产品的测试负责人、产品的测试人员、超级管理员
        List<String> memberTypes = Arrays.asList(
                MemberTypeEnum.TESTER_OWNER.name(),
                MemberTypeEnum.TESTER_M.name(),
                MemberTypeEnum.TESTER.name(),
                MemberTypeEnum.SUPPER.name());
        List<ProductMemberVO> productMembers = null;
        try {
            productMembers = productRpcService.findProductMemberByIdQuery(productCode, memberTypes);
        } catch (Exception e) {
            log.error("ListProductMemberByPIdQuery ERROR", e);
        }
        if (CollectionUtils.isEmpty(productMembers)) {
            return false;
        }
        return productMembers.stream().anyMatch(member -> member.getUserId().equals(currentUser.getUserId()));
    }

    public CheckCreatorOrDutyUserVO handle(CheckCreatorOrDutyUserQuery query) {
        log.info("CheckCreatorOrDutyUserQuery >>> {}", JSON.toJSONString(query));
        CheckCreatorOrDutyUserVO vo = new CheckCreatorOrDutyUserVO();
        if (CollectionUtils.isEmpty(query.getTestcaseCodeList())) {
            return vo;
        }
        vo.setTotal(query.getTestcaseCodeList().size());
        if (null == query.getTransactor()) {
            vo.setRefused(vo.getTotal());
            return vo;
        }
        Integer passed = testcaseRepository.selectCountCreatorOrDutyUser(
                query.getTestcaseCodeList(), query.getTransactor().getUserId());
        vo.setPassed(passed);
        vo.setRefused(vo.getTotal() - vo.getPassed());
        return vo;
    }


    public CheckTestcaseStatusVO handle(CheckTestcaseStatusQuery query) {
        log.info("CheckTestcaseStatusQuery >>> {}", query.getTestcaseCodeList());
        if (CollectionUtils.isEmpty(query.getTestcaseCodeList())) {
            return new CheckTestcaseStatusVO();
        }
        CheckTestcaseStatusVO vo = testcaseRepository.selectCountTestcaseStatus(query.getTestcaseCodeList());
        // 查询是否有草稿
        List<TestcaseEntityDO> testcaseDOList = testcaseRepository.selectAutomaticCaseByCodeList(query.getTestcaseCodeList());
        vo.setEdit(countEditedCase(testcaseDOList));
        return vo;
    }

    private Integer countEditedCase(List<TestcaseEntityDO> testcaseDOList) {
        if (CollectionUtils.isEmpty(testcaseDOList)) {
            return 0;
        }
        Set<String> autoSourceList = testcaseDOList.stream()
                .map(TestcaseEntityDO::getAutomaticSourceCode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(autoSourceList)) {
            return 0;
        }
        List<SceneInfoEntityDO> sceneList = apiTestRepository.querySceneByAutoSourceCode(new ArrayList<>(autoSourceList));
        if (CollectionUtils.isEmpty(sceneList)) {
            return 0;
        }
        Set<String> editList = sceneList.stream().map(SceneInfoEntityDO::getAutomaticSourceCode)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(editList)) {
            return 0;
        }
        testcaseDOList = testcaseDOList.stream().filter(t -> editList.contains(t.getAutomaticSourceCode()))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(testcaseDOList) ? 0 : testcaseDOList.size();
    }

    public List<ListTestcaseVO> handle(TestcaseQuery query) {
        buildTestcaseQuery(query);
        List<ListTestcaseVO> topModule = getListTestcaseAllGroup(query);
        PageTestcaseQuery pageTestcaseQuery = new PageTestcaseQuery();
        BeanUtils.copyProperties(query, pageTestcaseQuery);
        buildEnableChecked(topModule, pageTestcaseQuery);
        return topModule;
    }


    public TestcaseVO handle(FindTestcaseQuery query) {
        log.info("FindTestcaseByCodeQuery >>> {}", query.getCode());
        TestcaseEntityDO entity = testcaseRepository.loadFormDb(query.getCode());
        if (null == entity || !entity.getEnable() || !TestcaseAttributeEnum.TESTCASE.equals(entity.getAttribute())) {
            return null;
        }
        TestcaseVO vo = testcaseDomainConverter.converter(entity);
        vo.setParentFullName(handler(new FindParentFullNameQuery(vo.getPath())));
        if (TestcaseTypeEnum.MANUAL.equals(vo.getType())) {
            ListTestcaseStepQuery stepQuery = new ListTestcaseStepQuery(query.getCode());
            List<TestcaseStepVO> testSteps = testcaseStepRepository.handle(stepQuery);
            vo.setTestSteps(testSteps);
            ListTestcaseTagQuery tagQuery = new ListTestcaseTagQuery(query.getCode());
            List<TagVO> tags = testcaseTagRepository.handle(tagQuery);
            vo.setTags(tags);
            if (StringUtils.isNotEmpty(vo.getVersionCode()) && !"NONE_VERSION".equals(vo.getVersionCode())) {
                SimpleVersionQuery simpleVersionQuery = new SimpleVersionQuery();
                simpleVersionQuery.setCode(vo.getVersionCode());
                SimpleVersionVO versionVO = iProjectRpcService.simpleVersionQuery(simpleVersionQuery);
                vo.setVersionName(versionVO.getName());
            }
        }
        if (TestcaseTypeEnum.AUTO.equals(vo.getType()) && StringUtils.isNotEmpty(vo.getAutomaticSourceCode())) {
            AutomaticSourceRecordEntityDO automaticSourceRecordEntity = automaticSourceRecordRepository.selectByPrimaryKey(vo.getAutomaticSourceCode());
            if (null != automaticSourceRecordEntity) {
                vo.setAutomaticSourceType(automaticSourceRecordEntity.getType());
            }
        }
        boolean productPermission = productRpcService.checkTestUserPermission(query.getTransactor(), vo.getProductCode());
        // 用例创建人、用例责任人
        boolean userPermission = Optional.ofNullable(query.getTransactor())
                .map(User::getUserId)
                .map(userId -> userId.equals(vo.getCreatorId()) || userId.equals(vo.getDutyUserId()))
                .orElse(false);
        List<TestcaseEditFieldEnum> fields = getTestcaseFields(productPermission || userPermission, vo.getType());
        vo.setFields(fields);
        List<TestcaseButtonEnum> buttons = getTestcaseButtons(vo.getType(), vo.getStatus(), "details", vo.getSetCore());
        vo.setButtons(buttons);
        return vo;
    }

    /**
     * 获取文件名 [用例导出列表-yyyyMMdd] + [今日导出文件数流水号]
     *
     * @return
     */
    public String getTestcaseExpFileName() {
        String key = "用例导出列表-" + DateUtil.dateToString(new Date(), DateUtil.COMPACT_DATE_FORMAT);
        DecimalFormat decimalFormat = new DecimalFormat("0000");
        Integer index = 0;
        //初始设置key
        if (!redisService.hasKey(key) || StringUtil.isEmpty(redisService.getKey(key))) {
            //当天剩余时间（毫秒）
            Long seconds = DateUtil.getEndTimeOfDay(new Date()).getTime() - System.currentTimeMillis();
            redisService.setKey(key, String.valueOf(index), seconds, TimeUnit.MILLISECONDS);
            return key + decimalFormat.format(index);
        }
        //流水号累加
        Integer intValue = Integer.parseInt(redisService.getKey(key));
        index = intValue;
        redisService.setKey(key, String.valueOf(++intValue));
        return key + decimalFormat.format(index);
    }

    public List<String> handler(FindParentFullNameQuery query) {
        log.info("FindParentFullNameQuery >>> {}", query.getPath());
        if (StringUtils.isBlank(query.getPath())) {
            return Collections.emptyList();
        }
        List<String> paths = Arrays.asList(query.getPath().split("\\."));
        List<TestcaseEntityDO> pList = testcaseRepository.selectTestcaseByCodeList(paths);
        return pList.stream()
                .sorted(Comparator.comparingInt(entity -> paths.indexOf(entity.getCode())))
                .map(TestcaseEntityDO::getName)
                .collect(Collectors.toList());
    }

    /**
     * 获取用例可编辑字段
     *
     * @param permission 权限
     * @param type       类型
     * @return 可编辑字段
     */
    private List<TestcaseEditFieldEnum> getTestcaseFields(Boolean permission, TestcaseTypeEnum type) {
        List<TestcaseEditFieldEnum> fields = new ArrayList<>();
        if (TestcaseTypeEnum.MANUAL.equals(type)) {
            fields.add(TestcaseEditFieldEnum.PREVIEW_ATTACHMENT);
            fields.add(TestcaseEditFieldEnum.DOWNLOAD_ATTACHMENT);
            fields.add(TestcaseEditFieldEnum.EDIT_VERSION);
            if (permission) {
                fields.add(TestcaseEditFieldEnum.TITLE);
                fields.add(TestcaseEditFieldEnum.PRIORITY);
                fields.add(TestcaseEditFieldEnum.DUTY_USER);
                fields.add(TestcaseEditFieldEnum.TAG);
                fields.add(TestcaseEditFieldEnum.PRECONDITION);
                fields.add(TestcaseEditFieldEnum.TEST_STEP);
                fields.add(TestcaseEditFieldEnum.COMMENT);
                fields.add(TestcaseEditFieldEnum.UPLOAD_ATTACHMENT);
                fields.add(TestcaseEditFieldEnum.DELETE_ATTACHMENT);
            }
        }
        if (TestcaseTypeEnum.AUTO.equals(type) && permission) {
            fields.add(TestcaseEditFieldEnum.PRIORITY);
            fields.add(TestcaseEditFieldEnum.DUTY_USER);
        }
        return fields;
    }

    private void buildTestcaseQuery(TestcaseQuery query) {
        if (StringUtils.isNotBlank(query.getCodeOrTitle())) {
            String search =
                    query.getCodeOrTitle().replaceAll("/", "//").replaceAll("%", "/%").replaceAll("_", "/_");
            query.setCodeOrTitle(search);
            query.setFilter(true);
        }
        boolean isFilter = CollectionUtil.isNotEmpty(query.getTagList()) || CollectionUtil.isNotEmpty(query.getPriorityList())
                || CollectionUtil.isNotEmpty(query.getStatusList()) || CollectionUtil.isNotEmpty(query.getDutyUserList())
                || CollectionUtil.isNotEmpty(query.getCreatorList()) || CollectionUtil.isNotEmpty(query.getNodeTypeList())
                || query.getCreateTimeBegin() != null || query.getCreateTimeEnd() != null
                || query.getModifyTimeBegin() != null || query.getModifyTimeEnd() != null
                || query.getIsHeart() != null || CollectionUtil.isNotEmpty(query.getAutomaticTypeList());
        if (isFilter) {
            query.setFilter(true);
        }
        if (CollectionUtils.isNotEmpty(query.getNodeTypeList())) {
            if (query.getNodeTypeList().contains(AutomaticNodeTypeEnum.OTHER)) {
                query.getNodeTypeList().addAll(Arrays.asList(AutomaticNodeTypeEnum.TestPlan, AutomaticNodeTypeEnum.ThreadGroup));
            }
            if (query.getNodeTypeList().stream().anyMatch(n -> TestcaseAttributeEnum.MODULE.equals(n.getAttribute()))) {
                query.setFilterModule(true);
            }
        }
    }

    private List<ListTestcaseVO> getListTestcaseAllGroup(TestcaseQuery query) {
        if (TestcaseGroupTypeEnum.ALL.equals(query.getGroupType())
                || TestcaseGroupTypeEnum.NO_GROUP.equals(query.getGroupType())) {
            query.setParentCode(StringUtils.EMPTY);
        }
        List<ListTestcaseVO> topModule = new LinkedList<>();
        query.setTestcaseAttribute(TestcaseAttributeEnum.MODULE);
        query.setAutomaticSourceCode(StringUtils.defaultString(query.getAutomaticSourceCode()));
        PageTestcaseQuery pageTestcaseQuery = new PageTestcaseQuery();
        BeanUtils.copyProperties(query, pageTestcaseQuery);
        topModule.addAll(testcaseRepository.selectTestCaseModuleList(pageTestcaseQuery));
        if (CollectionUtil.isNotEmpty(topModule)) {
            topModule.stream().forEach(vo -> getChildren(vo, query));
        }
        if (TestcaseGroupTypeEnum.ALL.equals(query.getGroupType()) && TestcaseTypeEnum.MANUAL.equals(query.getType())) {
            ListTestcaseVO noGroup = new ListTestcaseVO();
            noGroup.setCode(TestcaseGroupTypeEnum.NO_GROUP.name());
            noGroup.setName(TestcaseGroupTypeEnum.NO_GROUP.getDesc());
            noGroup.setType(TestcaseTypeEnum.MANUAL);
            noGroup.setAttribute(TestcaseAttributeEnum.MODULE);
            noGroup.setStatus(TestcaseStatusEnum.NORMAL);
            noGroup.setTestcaseCount(testcaseRepository.countNoGroupTestcase(pageTestcaseQuery).intValue());
            List<ListTestcaseVO> testcaseList = testcaseRepository.pageTestcase(pageTestcaseQuery);
            List<String> codeList = testcaseList.stream().map(ListTestcaseVO::getCode).collect(Collectors.toList());
            List<TagVO> allTags = getTagsByCodeList(codeList);
            testcaseList.forEach(t -> {
                List<TagVO> tags = allTags.stream().filter(e -> t.getCode().equals(e.getBusinessCode())).collect(Collectors.toList());
                t.setTags(tags);
                List<TestcaseButtonEnum> buttons = getTestcaseButtons(t.getType(), t.getStatus(), "list", query.getSetCore());
                t.setButtons(buttons);
            });
            noGroup.setChildren(testcaseList);
            topModule.add(0, noGroup);
        }
        return topModule;
    }


    public PageSimpleTestCaseVO pageSimpleTestCaseQuery(PageSimpleTestCaseQuery query) {
        PageSimpleTestCaseVO vo = new PageSimpleTestCaseVO();
        if (StringUtil.isEmpty(query.getProductCode())) {
            return vo;
        }
        com.github.pagehelper.Page<SimpleTestCase> pager = PageHelper.startPage(query.getPage(), query.getSize());
        pager.count(false);
        vo.setList(testcaseRepository.selectSimpleTestCase(query));
        vo.setTotal(pager.getTotal());
        return vo;
    }

    public List<ListExecuteCaseVO> listExecuteCaseQuery(ListExecuteCaseQuery query) {
        log.info("ListExecuteCaseQuery >>> {}", query.getAutomaticTaskCode());
        if (CollectionUtils.isNotEmpty(query.getStatus())) {
            if (query.getStatus().contains(TestPlanCaseStatusEnum.PASSED)) {
                query.getStatus().add(TestPlanCaseStatusEnum.SUCCESS);
            }
            if (query.getStatus().contains(TestPlanCaseStatusEnum.SKIPPED)) {
                query.getStatus().add(TestPlanCaseStatusEnum.SKIP);
            }
        }
        List<ListExecuteCaseVO> caseList = testcaseRepository.selectByAutomaticTaskCode(query);
        if (CollectionUtils.isEmpty(caseList)) {
            return Collections.emptyList();
        }
        caseList.parallelStream().forEach(vo -> {
            vo.setExecLogFile(getFullFilepath(vo.getExecLogFile()));
            vo.setReportFile(getFullFilepath(vo.getReportFile()));
            if (null == vo.getName()) {
                vo.setName("用例已删除，仅展示记录");
                vo.setAttribute(TestcaseAttributeEnum.TESTCASE);
            }
        });
        List<String> codeList = caseList.parallelStream()
                .filter(vo -> StringUtils.isNotEmpty(vo.getPath()))
                .flatMap(vo -> Arrays.stream(vo.getPath().split("\\.")))
                .distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(codeList)) {
            return caseList;
        }
        List<ListExecuteCaseVO> moduleList = testcaseRepository.selectWithListExecuteCaseVO(codeList);
        moduleList.parallelStream().forEach(vo -> {
            List<ListExecuteCaseVO> moduleChildren = moduleList.parallelStream()
                    .filter(child -> child.getParentCode().equals(vo.getCode()))
                    .collect(Collectors.toList());
            List<ListExecuteCaseVO> caseChildren = caseList.parallelStream()
                    .filter(child -> child.getParentCode().equals(vo.getCode()))
                    .collect(Collectors.toList());
            moduleChildren.addAll(caseChildren);
            vo.setChildren(moduleChildren);
            Integer testcaseCount = (int) caseList.parallelStream()
                    .filter(child -> child.getPath().contains(vo.getCode()))
                    .count();
            vo.setTestcaseCount(testcaseCount);
        });
        return moduleList.parallelStream()
                .filter(vo -> StringUtils.EMPTY.equals(vo.getParentCode()))
                .collect(Collectors.toList());
    }

    public List<TestcaseVO> simpleTestcaseQuery(SimpleTestcaseQuery query) {
        return testcaseRepository.selectSimpleTestcaseQuery(query);

    }

    public TestcaseVO handle(FindTestcaseEntityByCodeQuery query) {
        return testcaseRepository.handle(query);
    }

    public QueryCaseRelatedPlanVO caseRelatePlanQuery(CaseRelatePlanQuery query) {
        QueryCaseRelatedPlanVO result = new QueryCaseRelatedPlanVO();

        List<RelatedCasePlanVO> casePlanList = testcaseRepository.selectRelatedPlanList(query.getCodeList());
        Set<String> caseCodeSet = casePlanList.stream().map(RelatedCasePlanVO::getCaseCode).collect(Collectors.toSet());
        Map<String, List<RelatedCasePlanVO>> planMap = casePlanList.stream().collect(Collectors.groupingBy(RelatedCasePlanVO::getPlanCode));
        if (CollectionUtil.isNotEmpty(planMap)) {
            List<RelatedCasePlanVO> planList = new ArrayList<>(planMap.size());
            planMap.entrySet().forEach(item -> {
                planList.add(item.getValue().get(0));
            });
            result.setPlanList(planList);
        }
        result.setTotalNum(query.getCodeList().size());
        result.setRelatedNum(CollectionUtil.isEmpty(caseCodeSet) ? 0 : caseCodeSet.size());
        result.setChangeVersionFlag(result.getRelatedNum() == 0);
        return result;
    }

    public GroupsInformationVO handle(GroupsInformationByCodeQuery query) {
        TestcaseEntityDO testcase = testcaseRepository.loadFormDb(query.getCode());
        String fullName;
        if (StringUtils.isNotEmpty(testcase.getPath())) {
            List<String> nameList = findParentFullNameQuery(testcase.getPath());
            nameList.add(testcase.getName());
            Collections.reverse(nameList);// 前端会取反
            fullName = StringUtils.join(nameList, "/");
        }  else {
            fullName = testcase.getName();
        }
        List<TestcaseEntityDO> children = testcaseRepository.queryChildCaseAndModuleByPath(testcase.getCode(), testcase.getPath());
        long childrenModuleNum = children.stream()
                .filter(i -> TestcaseAttributeEnum.MODULE.equals(i.getAttribute()))
                .count();
        long childrenCaseNum = children.stream()
                .filter(i -> TestcaseAttributeEnum.TESTCASE.equals(i.getAttribute()))
                .count();
        GroupsInformationVO groupCount = new GroupsInformationVO();
        groupCount.setFullName(fullName);
        groupCount.setChildrenCaseNum((int) childrenCaseNum);
        groupCount.setChildrenModuleNum((int) childrenModuleNum);
        return groupCount;
    }

    public Integer findTestcaseModulePath(FindTestcaseModulePathQuery query) {
        return testcaseRepository.findTestcaseModulePath(query);

    }

    public TestCaseStatusNumVO statisticCaseStatusQuery(StatisticCaseStatusQuery query) {
        //查询所有用例
        List<TestcaseEntityDO> entityList = testcaseRepository.findListByCodeIn(query.getCaseCodeList());
        //查询用例path状态
        List<TestcaseEntityDO> moduleList = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(entityList)) {
            Set<String> codeList = new HashSet<>();
            entityList.parallelStream()
                    .forEach(entity -> codeList.addAll(Arrays.asList(entity.getPath().split("\\."))));
            moduleList = testcaseRepository.findDisableListByCodeIn(new ArrayList<>(codeList));
        }
        //用例状态check
        List<TestcaseEntityDO> filterList = null;
        if (CollectionUtil.isNotEmpty(moduleList)) {
            filterList = entityList.stream().filter(entity -> (entity.getStatus().equals(TestcaseStatusEnum.NORMAL) && entity.getSetHeart().equals(Boolean.TRUE)))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(filterList)) {
                for (TestcaseEntityDO module : moduleList) {
                    for (TestcaseEntityDO entity : filterList) {
                        if (entity.getPath().contains(module.getCode())) {
                            entity.setStatus(TestcaseStatusEnum.DISABLE);
                            break;
                        }
                    }
                }
            }
        }
        Long heartNum = 0L;
        Long disableNum = 0L;
        Long totalNum = 0L;
        //组装返回值
        if (CollectionUtil.isNotEmpty(entityList)) {
            totalNum = Long.valueOf(entityList.size());
            heartNum = entityList.stream().filter(entity -> (entity.getSetHeart().equals(Boolean.TRUE))).count();

            if (CollectionUtil.isNotEmpty(filterList)) {
                entityList.addAll(filterList);
            }
            disableNum = entityList.stream().filter(entity -> (entity.getStatus().equals(TestcaseStatusEnum.DISABLE))).count();
        }
        TestCaseStatusNumVO vo = new TestCaseStatusNumVO();
        vo.setTotalNum(totalNum);
        vo.setHeartNum(heartNum);
        vo.setDisableNum(disableNum);
        return vo;
    }


    public StatisticVersionCaseNumVO statisticVersionCase(StatisticVersionCaseReq req) {
        StatisticVersionCaseNumVO vo = new StatisticVersionCaseNumVO();
        //所有用例
        List<ListExecuteCaseVO> allVOList = new ArrayList<>();
        //testcaseRepository.selectByVersionCode(req.getVersionCode());
        //手工用例数量
        List<ListExecuteCaseVO> manualVOList = new ArrayList<>();
        //allVOList.stream().filter(t->t.getType().name().equals(TestcaseTypeEnum.MANUAL.name())).collect(Collectors.toList());
        //自动化用例数量
        List<ListExecuteCaseVO> autoVOList = new ArrayList<>();
        //根据版本查询计划
        VersionPlanQuery query = new VersionPlanQuery();
        query.setVersionCode(req.getVersionCode());
        List<TmTestPlanVO> versionPlanVOList = tmTestPlanQueryDomainService.getVersionPlan(query);
        if (null != versionPlanVOList && versionPlanVOList.size() > 0) {
            for (TmTestPlanVO tmTestPlanVO : versionPlanVOList) {
                autoVOList.addAll(testcaseRepository.selectAutoByPlanCode(tmTestPlanVO.getCode(), null));
                //核心用例
                List<ListExecuteCaseVO> manualCoreVOList = testcaseRepository.selectManualByPlanCode(tmTestPlanVO.getCode(), null);
                manualVOList.addAll(manualCoreVOList);
                allVOList.addAll(manualCoreVOList);
            }
            allVOList.addAll(autoVOList);

        }
        //手工用例未测数量
        List<ListExecuteCaseVO> manualNotTestVOList = manualVOList.stream().filter(t -> null == t.getResult() || t.getResult().name().equals(TestPlanCaseStatusEnum.INITIAL.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.NOT_STARTED.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.SUBMITTED.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.UNKNOWN.name())).collect(Collectors.toList());
        //手工用例通过数量
        List<ListExecuteCaseVO> manualPassVOList = manualVOList.stream().filter(t -> null != t.getResult() && t.getResult().name().equals(TestPlanCaseStatusEnum.PASSED.name())).collect(Collectors.toList());
        //自动化用例通过数量
        List<ListExecuteCaseVO> autoPassVOList = autoVOList.stream().filter(t -> null != t.getResult() && (t.getResult().name().equals(TestPlanCaseStatusEnum.PASSED.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.SUCCESS.name()))).collect(Collectors.toList());
        //自动化用例未测数量
        List<ListExecuteCaseVO> autoNotTestVOList = autoVOList.stream().filter(t -> null == t.getResult() || t.getResult().name().equals(TestPlanCaseStatusEnum.INITIAL.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.NOT_STARTED.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.SUBMITTED.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.UNKNOWN.name())).collect(Collectors.toList());

        //构建测试覆盖率
        buildCoverageRateVO(req.getVersionCode(), vo);
        //构建手工测试用例
        buildTestcaseRateVO(manualVOList, manualNotTestVOList, manualPassVOList, true, vo);

        //构建自动化测试用例
        buildTestcaseRateVO(autoVOList, autoNotTestVOList, autoPassVOList, false, vo);

        //用例分布饼图
        StatisticCaseVO statisticCaseVO = new StatisticCaseVO();
        statisticCaseVO.setTotalNum(manualVOList.size() + autoVOList.size());
        statisticCaseVO.setManualTestName("手工用例：");
        statisticCaseVO.setAutoTestName("自动化用例：");
        statisticCaseVO.setManualTestCaseNum(manualVOList.size());
        statisticCaseVO.setManualTestCaseStatistic(divide(manualVOList.size(), manualVOList.size() + autoVOList.size()) + "%");
        statisticCaseVO.setManualTestCaseColor(ColorEnumType.BLUE);
        statisticCaseVO.setAutoTestCaseNum(autoVOList.size());
        statisticCaseVO.setAutoTestCaseStatistic(divide(autoVOList.size(), manualVOList.size() + autoVOList.size()) + "%");
        statisticCaseVO.setAutoTestCaseColor(ColorEnumType.GREEN);
        vo.setStatisticCaseVO(statisticCaseVO);
        //用例状态分布饼图
        buildStatisticAllCaseVO(allVOList, req.getType(), vo);
        return vo;
    }

    private void buildStatisticAllCaseVO(List<ListExecuteCaseVO> allVOList, TestcaseTypeEnum type, StatisticVersionCaseNumVO vo) {
        StatisticAllCaseVO statisticAllCaseVO = new StatisticAllCaseVO();
        StatisticThingVO passTestCase = new StatisticThingVO();
        StatisticThingVO failTestCase = new StatisticThingVO();
        StatisticThingVO blockingTestCase = new StatisticThingVO();
        StatisticThingVO jumpOverTestCase = new StatisticThingVO();
        StatisticThingVO notTestCase = new StatisticThingVO();
        //全部
        if (type.name().equals(TestcaseTypeEnum.NULL.name())) {
            statisticAllCaseVO.setTotalNum(allVOList.size());
            List<ListExecuteCaseVO> allPassVOList = allVOList.stream().filter(t -> null != t.getResult() && (t.getResult().name().equals(TestPlanCaseStatusEnum.PASSED.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.SUCCESS.name()))).collect(Collectors.toList());
            //所有用例失败
            List<ListExecuteCaseVO> allFailedVOList = allVOList.stream().filter(t -> null != t.getResult() && t.getResult().name().equals(TestPlanCaseStatusEnum.FAILED.name())).collect(Collectors.toList());
            //用例受阻数量
            List<ListExecuteCaseVO> allBlockVOList = allVOList.stream().filter(t -> null != t.getResult() && t.getResult().name().equals(TestPlanCaseStatusEnum.BLOCK.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.BROKEN.name())).collect(Collectors.toList());
            //用例跳过
            List<ListExecuteCaseVO> allSkipVOList = allVOList.stream().filter(t -> null != t.getResult() && t.getResult().name().equals(TestPlanCaseStatusEnum.SKIP.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.SKIPPED.name())).collect(Collectors.toList());
            //用例未测数量
            List<ListExecuteCaseVO> allNotTestVOList = allVOList.stream().filter(t -> null == t.getResult() || t.getResult().name().equals(TestPlanCaseStatusEnum.INITIAL.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.NOT_STARTED.name())).collect(Collectors.toList());
            passTestCase.setTotalNum(allPassVOList.size());
            passTestCase.setName("通过");
            passTestCase.setColor(ColorEnumType.GREEN);

            failTestCase.setTotalNum(allFailedVOList.size());
            failTestCase.setName("失败");
            failTestCase.setColor(ColorEnumType.RED);

            blockingTestCase.setTotalNum(allBlockVOList.size());
            blockingTestCase.setName("受阻");
            blockingTestCase.setColor(ColorEnumType.YELLOW);

            jumpOverTestCase.setTotalNum(allSkipVOList.size());
            jumpOverTestCase.setName("跳过");
            jumpOverTestCase.setColor(ColorEnumType.BLUE);

            notTestCase.setTotalNum(allNotTestVOList.size());
            notTestCase.setName("未测试");
            notTestCase.setColor(ColorEnumType.GRAY);

            if (allVOList.size() != 0) {
                passTestCase.setProportion(divide(allPassVOList.size(), allVOList.size()) + "%");
                failTestCase.setProportion(divide(allFailedVOList.size(), allVOList.size()) + "%");
                blockingTestCase.setProportion(divide(allBlockVOList.size(), allVOList.size()) + "%");
                jumpOverTestCase.setProportion(divide(allSkipVOList.size(), allVOList.size()) + "%");
                notTestCase.setProportion(divide(allNotTestVOList.size(), allVOList.size()) + "%");
            } else {
                passTestCase.setProportion("0%");
                failTestCase.setProportion("0%");
                blockingTestCase.setProportion("0%");
                jumpOverTestCase.setProportion("0%");
                notTestCase.setProportion("0%");
            }
        } else if (type.name().equals(TestcaseTypeEnum.MANUAL.name())) {
            //手工用例数量
            List<ListExecuteCaseVO> manualVOList = allVOList.stream().filter(t -> t.getType().name().equals(TestcaseTypeEnum.MANUAL.name())).collect(Collectors.toList());
            statisticAllCaseVO.setTotalNum(manualVOList.size());
            //手工用例通过数量
            List<ListExecuteCaseVO> manualPassVOList = manualVOList.stream().filter(t -> null != t.getResult() && (t.getResult().name().equals(TestPlanCaseStatusEnum.PASSED.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.SUCCESS.name()))).collect(Collectors.toList());
            //手工用例失败数量
            List<ListExecuteCaseVO> manualFailedVOList = manualVOList.stream().filter(t -> null != t.getResult() && t.getResult().name().equals(TestPlanCaseStatusEnum.FAILED.name())).collect(Collectors.toList());
            //手工用例受阻数量
            List<ListExecuteCaseVO> manualBlockVOList = manualVOList.stream().filter(t -> null != t.getResult() && (t.getResult().name().equals(TestPlanCaseStatusEnum.BLOCK.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.BROKEN.name()))).collect(Collectors.toList());
            //手工用例受阻数量
            List<ListExecuteCaseVO> manualSkipVOList = manualVOList.stream().filter(t -> null != t.getResult() && (t.getResult().name().equals(TestPlanCaseStatusEnum.SKIP.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.SKIPPED.name()))).collect(Collectors.toList());
            //手工用例未测数量
            List<ListExecuteCaseVO> manualNotTestVOList = manualVOList.stream().filter(t -> null == t.getResult() || t.getResult().name().equals(TestPlanCaseStatusEnum.INITIAL.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.NOT_STARTED.name())).collect(Collectors.toList());

            passTestCase.setTotalNum(manualPassVOList.size());
            passTestCase.setName("通过");
            passTestCase.setColor(ColorEnumType.GREEN);

            failTestCase.setTotalNum(manualFailedVOList.size());
            failTestCase.setName("失败");
            failTestCase.setColor(ColorEnumType.RED);

            blockingTestCase.setTotalNum(manualBlockVOList.size());
            blockingTestCase.setName("受阻");
            blockingTestCase.setColor(ColorEnumType.YELLOW);

            jumpOverTestCase.setTotalNum(manualSkipVOList.size());
            jumpOverTestCase.setName("跳过");
            jumpOverTestCase.setColor(ColorEnumType.BLUE);

            notTestCase.setTotalNum(manualNotTestVOList.size());
            notTestCase.setName("未测试");
            notTestCase.setColor(ColorEnumType.GRAY);

            if (allVOList.size() != 0) {
                passTestCase.setProportion(divide(manualPassVOList.size(), allVOList.size()) + "%");
                failTestCase.setProportion(divide(manualFailedVOList.size(), allVOList.size()) + "%");
                blockingTestCase.setProportion(divide(manualBlockVOList.size(), allVOList.size()) + "%");
                jumpOverTestCase.setProportion(divide(manualSkipVOList.size(), allVOList.size()) + "%");
                notTestCase.setProportion(divide(manualNotTestVOList.size(), allVOList.size()) + "%");
            } else {
                passTestCase.setProportion("0%");
                failTestCase.setProportion("0%");
                blockingTestCase.setProportion("0%");
                jumpOverTestCase.setProportion("0%");
                notTestCase.setProportion("0%");
            }
        } else if (type.name().equals(TestcaseTypeEnum.AUTO.name())) {
            //自动化用例数量
            List<ListExecuteCaseVO> autoVOList = allVOList.stream().filter(t -> t.getType().name().equals(TestcaseTypeEnum.AUTO.name())).collect(Collectors.toList());
            statisticAllCaseVO.setTotalNum(autoVOList.size());
            //自动化用例通过数量
            List<ListExecuteCaseVO> autoPassVOList = autoVOList.stream().filter(t -> null != t.getResult() && (t.getResult().name().equals(TestPlanCaseStatusEnum.PASSED.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.SUCCESS.name()))).collect(Collectors.toList());
            //自动化用例失败数量
            List<ListExecuteCaseVO> autoFailedVOList = autoVOList.stream().filter(t -> null != t.getResult() && t.getResult().name().equals(TestPlanCaseStatusEnum.FAILED.name())).collect(Collectors.toList());
            //自动化用例受阻数量
            List<ListExecuteCaseVO> autoBlockVOList = autoVOList.stream().filter(t -> null != t.getResult() && (t.getResult().name().equals(TestPlanCaseStatusEnum.BLOCK.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.BROKEN.name()))).collect(Collectors.toList());
            //自动化用例受阻数量
            List<ListExecuteCaseVO> autoSkipVOList = autoVOList.stream().filter(t -> null != t.getResult() && (t.getResult().name().equals(TestPlanCaseStatusEnum.SKIP.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.SKIPPED.name()))).collect(Collectors.toList());
            //自动化用例未测数量
            List<ListExecuteCaseVO> autoNotTestVOList = autoVOList.stream().filter(t -> null == t.getResult() || t.getResult().name().equals(TestPlanCaseStatusEnum.INITIAL.name()) || t.getResult().name().equals(TestPlanCaseStatusEnum.NOT_STARTED.name())).collect(Collectors.toList());
            passTestCase.setTotalNum(autoPassVOList.size());
            passTestCase.setName("通过");
            passTestCase.setColor(ColorEnumType.GREEN);

            failTestCase.setTotalNum(autoFailedVOList.size());
            failTestCase.setName("失败");
            failTestCase.setColor(ColorEnumType.RED);

            blockingTestCase.setTotalNum(autoBlockVOList.size());
            blockingTestCase.setName("受阻");
            blockingTestCase.setColor(ColorEnumType.YELLOW);

            jumpOverTestCase.setTotalNum(autoSkipVOList.size());
            jumpOverTestCase.setName("跳过");
            jumpOverTestCase.setColor(ColorEnumType.BLUE);

            notTestCase.setTotalNum(autoNotTestVOList.size());
            notTestCase.setName("未测试");
            notTestCase.setColor(ColorEnumType.GRAY);

            if (allVOList.size() != 0) {
                passTestCase.setProportion(divide(autoPassVOList.size(), allVOList.size()) + "%");
                failTestCase.setProportion(divide(autoFailedVOList.size(), allVOList.size()) + "%");
                blockingTestCase.setProportion(divide(autoBlockVOList.size(), allVOList.size()) + "%");
                jumpOverTestCase.setProportion(divide(autoSkipVOList.size(), allVOList.size()) + "%");
                notTestCase.setProportion(divide(autoNotTestVOList.size(), allVOList.size()) + "%");
            } else {
                passTestCase.setProportion("0%");
                failTestCase.setProportion("0%");
                blockingTestCase.setProportion("0%");
                jumpOverTestCase.setProportion("0%");
                notTestCase.setProportion("0%");
            }
        }
        statisticAllCaseVO.setPassTestCase(passTestCase);
        statisticAllCaseVO.setFailTestCase(failTestCase);
        statisticAllCaseVO.setBlockingTestCase(blockingTestCase);
        statisticAllCaseVO.setJumpOverTestCase(jumpOverTestCase);
        statisticAllCaseVO.setNotTestCase(notTestCase);
        vo.setStatisticAllCaseVO(statisticAllCaseVO);
    }


    private void buildTestcaseRateVO(List<ListExecuteCaseVO> totalList, List<ListExecuteCaseVO> notTestList, List<ListExecuteCaseVO> passTestList, boolean bo, StatisticVersionCaseNumVO vo) {
        StatisticThingVO statisticThingVO = new StatisticThingVO();
        statisticThingVO.setTotalNum(totalList.size());
        if (totalList.size() > notTestList.size()) {
            statisticThingVO.setNotTestedNum(totalList.size() - notTestList.size());
        } else {
            statisticThingVO.setNotTestedNum(0);
        }
        statisticThingVO.setColor(ColorEnumType.BLACK);
        statisticThingVO.setProportionName("通过率");
        if (totalList.size() != 0) {
            double rate = divide(passTestList.size(), totalList.size());
            statisticThingVO.setProportion(doubleTrans(rate));
            if (rate >= 80) {
                statisticThingVO.setProportionColor(ColorEnumType.GREEN);
            } else if (rate >= 60 && rate < 80) {
                statisticThingVO.setProportionColor(ColorEnumType.BLACK);
            } else if (rate < 60 && rate > 0) {
                statisticThingVO.setProportionColor(ColorEnumType.RED);
            } else {
                statisticThingVO.setProportionColor(ColorEnumType.BLACK);
                statisticThingVO.setProportion("--");
            }
        } else {
            statisticThingVO.setProportionColor(ColorEnumType.BLACK);
            statisticThingVO.setProportion("0");
        }
        if (bo) {
            statisticThingVO.setName("手工用例");
            vo.setManualCaseNum(statisticThingVO);
        } else {
            statisticThingVO.setName("自动化用例");
            vo.setAutoCaseNum(statisticThingVO);
        }

    }


    private void buildCoverageRateVO(String versionCode, StatisticVersionCaseNumVO vo) {
        //代码覆盖率
        CoverageVersionRateQuery rateQuery = new CoverageVersionRateQuery(versionCode);
        CoverageVersionRateVO rateVO = coverageQueryDomainService.getCoverageVersionRate(rateQuery);
        //分支
        CoverageRateVO branch = new CoverageRateVO();
        branch.setName("分支覆盖率");
        branch.setCoverageRate(rateVO.getBranchVersionRate() + "");
        if (rateVO.getBranchVersionRate() >= 80) {
            branch.setColor(ColorEnumType.GREEN);
        } else if (rateVO.getBranchVersionRate() >= 60 && rateVO.getBranchVersionRate() < 80) {
            branch.setColor(ColorEnumType.BLACK);
        } else if (rateVO.getBranchVersionRate() < 60 && rateVO.getBranchVersionRate() > 0) {
            branch.setColor(ColorEnumType.RED);
        } else {
            branch.setColor(ColorEnumType.BLACK);
            branch.setCoverageRate("--");
        }

        vo.setBranch(branch);
        //主干
        CoverageRateVO master = new CoverageRateVO();
        master.setName("主干覆盖率");
        master.setCoverageRate(rateVO.getMasterVersionRate() + "");
        if (rateVO.getMasterVersionRate() >= 80) {
            master.setColor(ColorEnumType.GREEN);
        } else if (rateVO.getMasterVersionRate() >= 60 && rateVO.getMasterVersionRate() < 80) {
            master.setColor(ColorEnumType.BLACK);
        } else if (rateVO.getMasterVersionRate() < 60 && rateVO.getMasterVersionRate() > 0) {
            master.setColor(ColorEnumType.RED);
        } else {
            master.setColor(ColorEnumType.BLACK);
            master.setCoverageRate("--");
        }
        vo.setMaster(master);
    }


/*    public static double divide(int divisor, int dividend){
        if(dividend==0){
            return 0;
        }
        BigDecimal dividendBigDecimal = new BigDecimal(dividend);
        BigDecimal divisorBigDecimal = new BigDecimal(divisor);
        BigDecimal resultBigDecimal = dividendBigDecimal.divide(divisorBigDecimal, 2, RoundingMode.HALF_UP);
        return  resultBigDecimal.doubleValue()*100;
    }*/

    private double divide(int divisor, int dividend) {
        if (dividend == 0) {
            return 0;
        }
        double rateStr = (new BigDecimal((float) divisor / dividend).setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue()) * 100;
        String str = String.format("%.2f", rateStr);
        return Double.parseDouble(str);
    }

    public static String doubleTrans(double d) {
        if (Math.round(d) - d == 0) {
            return String.valueOf((long) d);
        }
        return String.valueOf(d);
    }

    public List<String> selectVersionCodeByPlanCode(String planCode) {
        return tmTestPlanCaseRepository.selectVersionCodeByPlanCode(planCode);
    }

    public List<ListXmindDetailResp> queryAllXmindCase(QueryAllXmindCaseReq req) {
        // 查出parentCode下所有用例和分组
        List<TestcaseEntityDO> caseModuleDOs = this.queryAllCaseModule(req);
        if (CollectionUtils.isEmpty(caseModuleDOs)) {
            // 如果parentCode是用例，需返回步骤等下级节点
            TestcaseEntityDO caseDO = this.getTestcaseByCaseCode(req.getParentCode());
            if (null == caseDO) {
                return new ArrayList<>();
            }
            caseModuleDOs = Collections.singletonList(caseDO);
            Map<String, List<TestcaseStepVO>> caseStepMap = this.getTestCaseStepDetail(caseModuleDOs);
            return testcaseDomainConverter.convertXmindList(this.getTestCaseStepList(caseStepMap, caseDO));
        }
        // 一键展开用例数需少于1000
        checkTestCaseCount(caseModuleDOs);
        // 所有用例和分组按parentCode分组
        Map<String, List<TestcaseEntityDO>> parentMap = caseModuleDOs.stream()
                .collect(Collectors.groupingBy(TestcaseEntityDO::getParentCode));
        // 一次性查出所有步骤，按用例code分组
        Map<String, List<TestcaseStepVO>> caseStepMap = this.getTestCaseStepDetail(caseModuleDOs);
        // 根据parentCode找到第一层
        List<TestcaseEntityDO> firstModule = this.getFirstModuleByMap(parentMap, req.getParentCode());
        // 从第一层开始迭代
        List<ListXmindDetailVO> resultList = this.xmindQueryIterator(parentMap,
                firstModule, req.getParentCode(), req.getPlanCode(), caseStepMap);
        if (TestcaseGroupTypeEnum.ALL.name().equals(req.getParentCode())) {
            resultList.stream().forEach(t -> t.setParentCode(TestcaseGroupTypeEnum.ALL.name()));
        }
        if (StringUtils.isNotBlank(req.getPlanCode())) {
            // 版本用例需要拼装版本名为分组
            if (TestcaseGroupTypeEnum.ALL.name().equals(req.getParentCode())) {
                resultList = buildVersionXmindModule(resultList);
            }
        }
        return testcaseDomainConverter.convertXmindList(resultList);
    }

    private List<ListXmindDetailVO> buildVersionXmindModule(List<ListXmindDetailVO> list) {
        Map<String, List<ListXmindDetailVO>> moduleMap = list.stream()
                .collect(Collectors.groupingBy(ListXmindDetailVO::getVersionCode));
        Set<String> versionCodeList = moduleMap.keySet();
        Map<String, String> versionMap = tmTestPlanQueryDomainService.getVersionNameList(versionCodeList);
        versionMap.put("NONE_VERSION", "未关联版本用例");
        List<ListXmindDetailVO> resultList = new ArrayList<>();
        if (MapUtils.isNotEmpty(versionMap)) {
            for (String versionCode : versionCodeList) {
                ListXmindDetailVO detailVO = new ListXmindDetailVO();
                detailVO.setId(versionCode);
                detailVO.setTopic(versionMap.get(versionCode));
                detailVO.setParentCode(TestcaseGroupTypeEnum.ALL.name());
                detailVO.setAttribute(TestcaseAttributeEnum.ROOT);
                detailVO.setDirection(DirectionEnum.RIGHT);
                detailVO.setDisabled(false);
                detailVO.setExpanded(true);
                detailVO.setTagEdit(false);
                detailVO.setChildren(moduleMap.get(versionCode));
                detailVO.setHasChilds(CollectionUtils.isNotEmpty(moduleMap.get(versionCode)));
                detailVO.setTestcaseCount(moduleMap.get(versionCode).size());
                resultList.add(detailVO);
            }
        }
        return resultList;
    }

    private void checkTestCaseCount(List<TestcaseEntityDO> caseModuleDOs) {
        List<TestcaseEntityDO> caseCodes = caseModuleDOs.stream()
                .filter(t -> TestcaseAttributeEnum.TESTCASE.equals(t.getAttribute()))
                .collect(Collectors.toList());
        int maxSize = config.getIntProperty("qc.xmind.expand.max", 1000);
        if (caseCodes.size() > maxSize) {
            throw new ServiceException("一键展开最多支持目录下" + maxSize + "条用例！");
        }
    }

    private TestcaseEntityDO getTestcaseByCaseCode(String caseCode) {
        PageXmindDetailQuery query = new PageXmindDetailQuery();
        query.setCaseCode(caseCode);
        TestcaseEntityDO testCase = testcaseRepository.getTestcaseVO(query);
        if (null != testCase && TestcaseAttributeEnum.TESTCASE.equals(testCase.getAttribute())) {
            return testCase;
        }
        return null;
    }

    private List<TestcaseEntityDO> getFirstModuleByMap(Map<String, List<TestcaseEntityDO>> parentMap,
                                                       String parentCode) {
        List<TestcaseEntityDO> firstModule = parentMap.get(parentCode);
        // 全部、未分组下的用例parentCode为""
        if (StringUtils.isNotBlank(parentCode)) {
            if (TestcaseGroupTypeEnum.NO_GROUP.name().equals(parentCode)
                    || TestcaseGroupTypeEnum.ALL.name().equals(parentCode)
                    || parentCode.startsWith(TestcaseGroupTypeEnum.NO_GROUP.name() + "___")
                    || parentCode.startsWith("VER")
                    || parentCode.equals("NONE_VERSION")) {
                firstModule = parentMap.get("");
            }
        }
        return CollectionUtils.isEmpty(firstModule) ? new ArrayList<>() : firstModule;
    }

    private Map<String, List<TestcaseStepVO>> getTestCaseStepDetail(List<TestcaseEntityDO> caseModuleDOs) {
        if (CollectionUtils.isNotEmpty(caseModuleDOs)) {
            List<String> caseCodes = caseModuleDOs.stream()
                    .filter(t -> TestcaseAttributeEnum.TESTCASE.equals(t.getAttribute()))
                    .map(TestcaseEntityDO::getCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(caseCodes)) {
                // 根据用例codeList查询所有步骤信息
                List<TestcaseStepVO> caseStepList = testcaseStepRepository.selectStepByCaseCodes(caseCodes);
                if (CollectionUtils.isNotEmpty(caseStepList)) {
                    // 将步骤信息根据用例code进行分组，方便取出
                    return caseStepList.stream().collect(Collectors.groupingBy(TestcaseStepVO::getTestcaseCode));
                }
            }
        }
        return new HashMap<>();
    }

    private List<ListXmindDetailVO> xmindQueryIterator(Map<String, List<TestcaseEntityDO>> parentMap,
                                                       List<TestcaseEntityDO> caseDOList,
                                                       String type, String planCode,
                                                       Map<String, List<TestcaseStepVO>> caseStepMap) {
        List<ListXmindDetailVO> xmindDetailVOList = new ArrayList<>();
        List<ListXmindDetailVO> noGroupList = new ArrayList<>();
        if (CollectionUtils.isEmpty(caseDOList)) {
            return xmindDetailVOList;
        }
        for (TestcaseEntityDO caseDO : caseDOList) {
            ListXmindDetailVO detailVO = this.buildXmindDetailVOByCase(caseDO);
            // 如果是分组继续迭代下一层，如果是用例则组装步骤等下层节点
            if (TestcaseAttributeEnum.MODULE.equals(caseDO.getAttribute())) {
                List<TestcaseEntityDO> caseChildren = parentMap.get(caseDO.getCode());
                detailVO.setChildren(this.xmindQueryIterator(parentMap, caseChildren, null, planCode, caseStepMap));
                detailVO.setHasChilds(CollectionUtils.isNotEmpty(caseChildren));
                // 统计testcaseCount
                this.countTestCase(detailVO);
            } else if (TestcaseAttributeEnum.TESTCASE.equals(caseDO.getAttribute())) {
                detailVO.setTestcaseStatus(caseDO.getStatus());
                detailVO.setExecutionStatus(caseDO.getExecutionStatus());
                if (null != caseDO.getExecutionStatus()) {
                    detailVO.setExecutionStatusDesc(caseDO.getExecutionStatus().getValue());
                }
                detailVO.setTestcaseCount(0);
                detailVO.setChildren(this.getTestCaseStepList(caseStepMap, caseDO));
                detailVO.setHasChilds(true);
            }
            // 未分组用例
            if (TestcaseAttributeEnum.TESTCASE.equals(caseDO.getAttribute())
                    && "".equals(caseDO.getParentCode())) {
                detailVO.setParentCode(TestcaseGroupTypeEnum.NO_GROUP.name());
                noGroupList.add(detailVO);
            } else {
                xmindDetailVOList.add(detailVO);
            }
        }
        // 传入的type是ALL，未分组用例不为空则组装
        // 传入的type是NO_GROUP，直接返回noGroupList
        if (TestcaseGroupTypeEnum.ALL.name().equals(type)) {
            if (CollectionUtils.isNotEmpty(noGroupList)) {
                xmindDetailVOList.addAll(this.buildNoGroupModule(noGroupList));
            }
        } else if (TestcaseGroupTypeEnum.NO_GROUP.name().equals(type)) {
            xmindDetailVOList.addAll(noGroupList);
        }
        // 计划中需要过滤空分组
        if (StringUtils.isNotBlank(planCode)) {
            xmindDetailVOList = xmindDetailVOList.stream().filter(t -> {
                if (TestcaseAttributeEnum.MODULE.equals(t.getAttribute())) {
                    return t.getTestcaseCount() > 0;
                }
                return true;
            }).collect(Collectors.toList());
        }
        return xmindDetailVOList;
    }

    /**
     * 组装xmind用例
     */
    private ListXmindDetailVO buildXmindDetailVOByCase(TestcaseEntityDO caseDO) {
        ListXmindDetailVO detailVO = new ListXmindDetailVO();
        detailVO.setVersionCode(caseDO.getVersionCode());
        detailVO.setId(caseDO.getCode());
        detailVO.setTopic(caseDO.getName());
        detailVO.setParentCode(caseDO.getParentCode());
        detailVO.setDirection(DirectionEnum.RIGHT);
        detailVO.setDisabled(false);
        detailVO.setExpanded(true);
        detailVO.setTagEdit(false);
        detailVO.setAttribute(caseDO.getAttribute());
        detailVO.setCreator(caseDO.getCreator());
        detailVO.setCreatorId(caseDO.getCreatorId());
        detailVO.setDutyUser(caseDO.getDutyUser());
        detailVO.setDutyUserId(caseDO.getDutyUserId());
        detailVO.setPath(caseDO.getPath());
        detailVO.setPriority(caseDO.getPriority());
        detailVO.setPriorityDesc(null == caseDO.getPriority() ? "" : caseDO.getPriority().getValue());
        detailVO.setTestcaseStatus(caseDO.getStatus());
        detailVO.setTestcaseStatusDesc(null == caseDO.getStatus() ? null : caseDO.getStatus().getDesc());
        return detailVO;
    }

    /**
     * 组装未分组用例
     */
    private List<ListXmindDetailVO> buildNoGroupModule(List<ListXmindDetailVO> noGroupList) {
        ListXmindDetailVO detailVO = new ListXmindDetailVO();
        detailVO.setId(TestcaseGroupTypeEnum.NO_GROUP.name());
        detailVO.setTopic("未分组用例");
        detailVO.setVersionCode("NONE_VERSION");
        detailVO.setParentCode(TestcaseGroupTypeEnum.ALL.name());
        detailVO.setAttribute(TestcaseAttributeEnum.MODULE);
        detailVO.setTestcaseStatus(TestcaseStatusEnum.NORMAL);
        detailVO.setTestcaseStatusDesc(TestcaseStatusEnum.NORMAL.getDesc());
        detailVO.setDirection(DirectionEnum.RIGHT);
        detailVO.setDisabled(false);
        detailVO.setExpanded(true);
        detailVO.setTagEdit(false);
        detailVO.setChildren(noGroupList);
        detailVO.setHasChilds(CollectionUtils.isNotEmpty(noGroupList));
        detailVO.setTestcaseCount(noGroupList.size());
        return Collections.singletonList(detailVO);
    }

    /**
     * 统计用例数
     */
    private void countTestCase(ListXmindDetailVO detailVO) {
        List<ListXmindDetailVO> list = detailVO.getChildren();
        detailVO.setTestcaseCount(0);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<ListXmindDetailVO> moduleList = list.stream()
                .filter(t -> TestcaseAttributeEnum.MODULE.equals(t.getAttribute()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(moduleList)) {
            detailVO.setTestcaseCount(detailVO.getChildren().size());
            return;
        }
        list.stream().forEach(t -> {
            detailVO.setTestcaseCount(detailVO.getTestcaseCount() + t.getTestcaseCount());
        });
        // 每层分组用例数 + 用例数
        int caseCount = list.size() - moduleList.size();
        detailVO.setTestcaseCount(detailVO.getTestcaseCount() + caseCount);
    }

    private List<ListXmindDetailVO> getTestCaseStepList(Map<String, List<TestcaseStepVO>> caseStepMap,
                                                        TestcaseEntityDO caseDO) {
        List<ListXmindDetailVO> testCaseStepList = new ArrayList<>();
        // 前置条件
        ListXmindDetailVO precondition = this.buildXmindVOByType(
                caseDO.getCode() + TestCaseTagNameEnum.PRECONDITION.name(),
                caseDO.getPrecondition(), caseDO.getCode(), TestCaseTagNameEnum.PRECONDITION);
        testCaseStepList.add(precondition);
        // 备注
        ListXmindDetailVO remark = this.buildXmindVOByType(
                caseDO.getCode() + TestCaseTagNameEnum.REMARK.name(),
                caseDO.getComment(), caseDO.getCode(), TestCaseTagNameEnum.REMARK);
        testCaseStepList.add(remark);
        if (null == caseStepMap) {
            return testCaseStepList;
        }
        //步骤
        List<TestcaseStepVO> stepList = caseStepMap.get(caseDO.getCode());
        if (CollectionUtils.isNotEmpty(stepList)) {
            stepList = stepList.stream().sorted(Comparator.comparingInt(TestcaseStepVO::getSort))
                    .collect(Collectors.toList());
            for (TestcaseStepVO vo : stepList) {
                String stepId = caseDO.getCode() + TestCaseTagNameEnum.STEP.name() + vo.getSort();
                ListXmindDetailVO step = this.buildXmindVOByType(stepId,
                        vo.getStepDesc(), caseDO.getCode(), TestCaseTagNameEnum.STEP);
                ListXmindDetailVO except = this.buildXmindVOByType(
                        caseDO.getCode() + TestCaseTagNameEnum.EXPECT.name() + vo.getSort(),
                        vo.getExpectResult(), stepId, TestCaseTagNameEnum.EXPECT);
                step.setChildren(Collections.singletonList(except));
                step.setHasChilds(true);
                testCaseStepList.add(step);
            }
        }
        return testCaseStepList;
    }

    private ListXmindDetailVO buildXmindVOByType(String id, String topic, String parentCode,
                                                 TestCaseTagNameEnum tag) {
        ListXmindDetailVO detailVO = new ListXmindDetailVO();
        detailVO.setId(id);
        detailVO.setTopic(topic);
        detailVO.setParentCode(parentCode);
        detailVO.setTagValue(tag);
        detailVO.setTagName(tag.getDesc());
        detailVO.setTagEdit(false);
        detailVO.setExpanded(false);
        detailVO.setDisabled(false);
        detailVO.setDirection(DirectionEnum.RIGHT);
        detailVO.setChildren(new ArrayList<>());
        detailVO.setHasChilds(false);
        return detailVO;
    }

    private List<TestcaseEntityDO> queryAllCaseModule(QueryAllXmindCaseReq req) {
        TestcaseQuery query = new TestcaseQuery();
        query.setSetCore(req.getSetCore());
        if (StringUtils.isBlank(req.getPlanCode())) {
            query.setVersionCode(req.getVersionCode());
        }
        query.setProductCode(req.getProductCode());
        query.setTypeString(null == req.getType() ? null : req.getType().name());
        query.setParentCode(req.getParentCode());
        query.setTestStageString(null == req.getTestStage() ? null : req.getTestStage().name());
        query.setPlanCode(req.getPlanCode());
        return testcaseRepository.selectTestCase(query);
    }

}
