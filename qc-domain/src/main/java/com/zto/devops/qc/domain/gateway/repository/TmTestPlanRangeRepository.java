package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanRangeTypeEnum;
import com.zto.devops.qc.client.model.dto.TmTestPlanRangeEntityDO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.SimpleTmTestPlanVO;

import java.util.List;

public interface TmTestPlanRangeRepository {
    /**
     * 根据planCode查询列表
     *
     * @param planCode
     * @param enable
     * @return
     */
    List<TmTestPlanRangeEntityDO> selectByPlanCode(String planCode, Boolean enable);

    TmTestPlanRangeEntityDO selectByPlanCodeAndTestRange(String planCode, TestPlanRangeTypeEnum rangeType);

    void updateByPrimaryKey(TmTestPlanRangeEntityDO rangeEntityDO);

    List<String> selectAssociatedTestPlanCode();

    List<TmTestPlanRangeEntityDO> selectBySimpleTmTestPlanVO(SimpleTmTestPlanVO vo);
}
