package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.testmanager.coverage.BucketEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.CoverageMergeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.rpc.pipeline.VersionContainSubVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageExecVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoveragePublishVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordBasicVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordGenerateVO;
import com.zto.devops.qc.client.model.testmanager.coverage.query.ReportDto;
import com.zto.devops.qc.domain.converter.CoverageDomainConverter;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.repository.CoverageRepository;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.util.ReportUtil;
import com.zto.devops.qc.domain.util.XmlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.logging.Logger;

@Service
@Slf4j
public class CoverageMergeService {

    @Autowired
    private IPipelineRpcService pipelineRpcService;
    @Autowired
    private ZtoOssService ossService;
    @Autowired
    private CoverageDomainConverter coverageConverter;
    @Autowired
    private CoverageRepository coverageRepository;
    @Autowired
    @Lazy
    private CoverageDomainService coverageDomainService;

    private static final Logger logger = Logger.getLogger(CoverageDomainService.class.getName());

    /**
     * @param coverageRecord 覆盖率运行记录
     * @param execPath       本地exec文件路径
     * @param publicPath     public路径
     */
    public void syncMergeSubVersionExecAndReport(CoverageRecordGenerateVO coverageRecord, String execPath, String publicPath, Logger logger) {
        //1、根据覆盖率记录的版本编码versionCode+appId获取版本详情信息
        VersionContainSubVO versionContainSubVO = pipelineRpcService.getVersionByVersionCodeAndAppId(coverageRecord.getVersionCode(), coverageRecord.getAppId());
        //2、版本若不是大版本或者没有子版本直接返回
        if (versionContainSubVO == null || !Objects.equals(versionContainSubVO.getMergeType(), "MERGED_MAJOR_VERSION")
                || CollectionUtil.isEmpty(versionContainSubVO.getSubVersionCodes())) {
            logger.info("版本不是大版本或者没有子版本直接返回。");
            return;
        }
        log.info("---------syncMergeSubVersionExecAndReport-------大版本:{}应用:{}初次生成覆盖率，合并子版本覆盖率计算---", coverageRecord.getVersionCode(), coverageRecord.getAppId());
        //3、根据子版本的版本编码和appid等信息获取【子版本的覆盖率记录】
        List<String> subVersionCodes = versionContainSubVO.getSubVersionCodes();

        List<CoverageRecordBasicVO> subNewestCoverageRecords = this.listNewestCoverageRecordVOs(subVersionCodes,
                coverageRecord.getAppId(), coverageRecord.getRecordType());
        if (CollectionUtil.isEmpty(subNewestCoverageRecords)) {
            logger.info("子版本覆盖率记录为空。");
            return;
        }
        //4、遍历【子版本的覆盖率记录】，下载它们的exec文件到execPath路径下
        this.batchDownloadAllExec(subNewestCoverageRecords, execPath, logger);
        //5、遍历【子版本的覆盖率记录】，获取它们的ReportDto集合
        List<ReportDto> reportDtoList = this.getReportDtos(subNewestCoverageRecords, publicPath);
        log.info("----syncMergeSubVersionExecAndReport---reportDtoList:{}", JSON.toJSONString(reportDtoList));
        //6、对ReportDto集合进行合并
        ReportDto reportDto = ReportUtil.mergeReports(reportDtoList);
        log.info("----syncMergeSubVersionExecAndReport---reportDto:{}", JSON.toJSONString(reportDto));
        coverageRecord.setReportDto(reportDto);
        coverageRecord.setLastCommitId(coverageRecord.getCommitId());
        log.info("---------syncMergeSubVersionExecAndReport-------大版本:{}应用:{}初次生成覆盖率，合并子版本覆盖率计算--结束---", coverageRecord.getVersionCode(), coverageRecord.getAppId());
    }

    private List<ReportDto> getReportDtos(List<CoverageRecordBasicVO> coverageRecords, String parentPublicPath) {
        List<ReportDto> list = new ArrayList<>();
        for (CoverageRecordBasicVO coverageRecord : coverageRecords) {
            ReportDto reportDto = this.getReportDto(coverageRecord, parentPublicPath);
            if (Objects.nonNull(reportDto)) {
                list.add(reportDto);
            }
        }
        return list;
    }

    private ReportDto getReportDto(CoverageRecordBasicVO coverageRecord, String parentPublicPath) {
        try {
            String publicPath = coverageRecord.getVersionCode() + "-" + coverageRecord.getBranchName() + "-" + coverageRecord.getAppId() + "/" + coverageRecord.getBranchName();
            logger.info("下载并解析上一个版本xml报告 开始");
            String remoteXmlKey = publicPath + "/" + coverageRecord.getCommitId() + "/xmlreport/" + coverageRecord.getCommitId() + ".xml";
            String localXmlPath = CoverageDomainService.LOCAL_FILE_PATH + parentPublicPath + "/" + coverageRecord.getCommitId() + "/xmlreport/";
            try {
                ossService.downloadFile(BucketEnum.COVERAGE_XML_BUCKET.getValue(), remoteXmlKey, localXmlPath);
            } catch (Exception e) {
                logger.warning("下载上一个版本xml报告异常。remoteXmlKey = " + remoteXmlKey + "。" + e.getMessage());
//                throw new ServiceException("下载上一个版本xml报告异常。请稍后再试。" + e.getMessage());
            }
            if (new File(localXmlPath + "/" + coverageRecord.getCommitId() + ".xml").exists()) {
                logger.warning("下载并解析上一个版本xml报告。有上一个版本xml报告的 ---");
                return XmlUtils.dom4jParseXml(localXmlPath + "/" + coverageRecord.getCommitId() + ".xml");
            } else {
                logger.warning("本地xml报告解析异常。localXmlPath = " + localXmlPath);
            }
            logger.info("下载并解析上一个版本xml报告 结束。");
        } catch (Exception e) {
            log.error("下载并解析xml报告异常", e);
//            throw new ServiceException("下载并解析xml报告异常。请稍后再试。" + e.getMessage());
        }
        return null;
    }

    /**
     * 批量下载exec文件
     */
    private void batchDownloadAllExec(List<CoverageRecordBasicVO> coverageRecords, String execPath, Logger logger) {
        //遍历【子版本的覆盖率记录】，下载它们的exec文件到execPath路径下
        for (CoverageRecordBasicVO coverageRecord : coverageRecords) {
            //下载子版本的exec文件
            CoverageRecordGenerateVO coverageRecordGenerateVO = coverageConverter.converter(coverageRecord);
            this.downloadAllExec(coverageRecordGenerateVO, execPath, logger);
        }
    }

    private void downloadAllExec(CoverageRecordGenerateVO coverageRecordGenerateVO, String execPath, Logger logger) {
        //下载当前记录的exec文件
        this.onlyDownloadCurrentRecordExec(coverageRecordGenerateVO, execPath);
        //下载中间记录的exec文件
        this.fillCoverageRecordGenerateVO(coverageRecordGenerateVO);
        this.downloadAllMiddleExec(coverageRecordGenerateVO, execPath, logger);
    }

    /**
     * 下载当前记录的exec文件
     */
    private void onlyDownloadCurrentRecordExec(CoverageRecordGenerateVO entity, String execPath) {
        logger.info("开始下载exec文件。");
        // 只需要当前commitId的所有exec文件
        List<CoverageExecVO> execEntityList = coverageRepository.getExecList(entity.getVersionCode(), entity.getAppId(), entity.getCommitId(), entity.getFlowLaneType(), entity.getDiffType());
        if (CollectionUtil.isEmpty(execEntityList)) {
            logger.warning("无exec文件可下载");
            return;
        }
        for (CoverageExecVO execEntity : execEntityList) {
            if (entity.getMergeDump() != null && entity.getMergeDump().equals(CoverageMergeEnum.UN_MERGE.getValue())) {
                logger.info("无需合并历史覆盖率，仅下载最新的exec文件");
                try {
                    ossService.downloadFile(execEntity.getBucketName(), execEntity.getExecPath() + execEntity.getExecName(), execPath);
                } catch (Exception e) {
                    log.error("下载exec文件异常。bucketName = " + execEntity.getBucketName() + "execName = " + execEntity.getExecName() + "。" + e.getMessage());
                }
                break;
            } else {
                try {
                    ossService.downloadFile(execEntity.getBucketName(), execEntity.getExecPath() + execEntity.getExecName(), execPath);
                } catch (Exception e) {
                    log.error("下载历史exec文件异常。bucketName = " + execEntity.getBucketName() + "execName = " + execEntity.getExecName() + "。" + e.getMessage());
                }
            }
        }
    }

    /**
     * 下载盖率中间所有的exec文件
     */
    private void downloadAllMiddleExec(CoverageRecordGenerateVO entity, String execPath, Logger logger) {
        if (StringUtil.isEmpty(entity.getFirstBranchCommitId())) {
            logger.info("未获取到分支第一次部署记录的commitId，无需下载历史所有的exec文件");
            return;
        }
        String oldCommitId = entity.getFirstBranchCommitId();
        String newCommitId = entity.getCommitId();
        if (StringUtil.isEmpty(newCommitId) || StringUtil.isEmpty(oldCommitId)) {
            logger.info("newCommitId or oldCommitId为空。无需下载额外exec文件。");
            return;
        }
        List<CoveragePublishVO> middlePublishes = coverageRepository.getMiddlePublishEntity(entity);

        if (CollectionUtil.isEmpty(middlePublishes)) {
            logger.info("获取中间部署记录为空。无需下载额外exec文件。");
            return;
        }
        Set<String> middleCommitIdList = new HashSet<>();
        for (CoveragePublishVO publishEntity : middlePublishes) {
            middleCommitIdList.add(publishEntity.getCommitId());
        }
        entity.setMiddleCommitIdList(middleCommitIdList);
        this.doDownloadExec(execPath, middleCommitIdList, entity.getAppId(), entity.getVersionCode(),
                entity.getBranchName(), entity.getFlowLaneType(), entity.getDiffType());
    }

    private void doDownloadExec(String execPath, Set<String> commitIdList, String appId,
                                String versionCode, String branchName, String flowLaneType, DiffTypeEnum diffTypeEnum) {
        if (CollectionUtil.isEmpty(commitIdList)) {
            return;
        }
        List<CoverageExecVO> execEntityList = coverageRepository.getMiddleExecList(commitIdList, appId,
                versionCode, branchName, flowLaneType, diffTypeEnum);
        if (CollectionUtil.isEmpty(execEntityList)) {
            return;
        }
        logger.info("开始下载中间版本exec文件");
        for (CoverageExecVO execEntity : execEntityList) {
            try {
                ossService.downloadFile(execEntity.getBucketName(), execEntity.getExecPath() + execEntity.getExecName(), execPath);
            } catch (Exception e) {
                log.error("开始下载中间版本exec文件异常。bucketName = " + execEntity.getBucketName() + ",execName = " + execEntity.getExecName() + "。errorMsg = " + e.getMessage());
            }
        }
    }

    /**
     * 根据子版本的版本编码和appid等信息获取【子版本的覆盖率记录】
     */
    private List<CoverageRecordBasicVO> listNewestCoverageRecordVOs(List<String> versionCodes, String appId, RecordTypeEnum recordType) {
        if (CollectionUtil.isEmpty(versionCodes)) {
            return Collections.EMPTY_LIST;
        }
        List<CoverageRecordBasicVO> result = new ArrayList<>();
        for (String versionCode : versionCodes) {
            CoverageRecordGenerateParameter parameter = new CoverageRecordGenerateParameter();
            parameter.setVersionCode(versionCode);
            parameter.setAppIdList(Collections.singletonList(appId));
            parameter.setRecordType(recordType);
            List<CoverageRecordBasicVO> initialCoverageRecords = coverageRepository.getInitialCoverageRecords(parameter);
            if (CollectionUtil.isNotEmpty(initialCoverageRecords)) {
                result.add(initialCoverageRecords.get(0));
            }
        }
        return result;
    }

    private void fillCoverageRecordGenerateVO(CoverageRecordGenerateVO vo) {
        if (vo.getDiffType().name().equals(DiffTypeEnum.FULL.name())) {
            logger.info("全量覆盖率");
            vo.setMergeDump(CoverageMergeEnum.UN_MERGE.getValue());
        } else {
            logger.info("增量覆盖率，开始获取分支第一次部署记录commitId。");
            vo.setMergeDump(CoverageMergeEnum.MERGE.getValue());
            String firstBranchCommitId = coverageDomainService.getFirstBranchCommitId(vo);
            vo.setFirstBranchCommitId(firstBranchCommitId);
        }
    }
}
