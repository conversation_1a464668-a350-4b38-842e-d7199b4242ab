package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.dto.KnowledgeBaseEntityDO;
import com.zto.devops.qc.client.model.knowledgebase.query.KnowledgeBaseByProductCodeQuery;
import com.zto.devops.qc.client.model.knowledgebase.query.KnowledgeBaseVO;

import java.util.List;

public interface KnowledgeBaseRepository {

    KnowledgeBaseVO getKnowledgeBaseDetail(KnowledgeBaseByProductCodeQuery query);

    void insertBatch(List<KnowledgeBaseEntityDO> list );

    /**
     * 删除所有
     */
    void delete();

}
