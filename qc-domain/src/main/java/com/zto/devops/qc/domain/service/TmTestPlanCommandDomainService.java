package com.zto.devops.qc.domain.service;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zto.devops.framework.client.audit.request.TaskBaseUpdateRequest;
import com.zto.devops.framework.client.audit.request.TaskCreateRequest;
import com.zto.devops.framework.client.audit.resp.AuditResp;
import com.zto.devops.framework.client.entity.BaseEntityDO;
import com.zto.devops.framework.client.entity.action.ActionLogDO;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.audit.*;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.gateway.repository.IEventLogRepository;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.gateway.util.TaskService;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.devops.qc.client.enums.rpc.FlowStatusEnum;
import com.zto.devops.qc.client.enums.rpc.MemberTypeEnum;
import com.zto.devops.qc.client.enums.rpc.NoticeUserTypeEnum;
import com.zto.devops.qc.client.enums.rpc.VersionTypeEnum;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.enums.testPlan.TestPlanTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.email.EmailSourceEnum;
import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.*;
import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.message.TestPlanOrReportMsg;
import com.zto.devops.qc.client.model.report.entity.SecurityTestResultVO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.rpc.pipeline.FlowBaseDetailVO;
import com.zto.devops.qc.client.model.rpc.pipeline.event.DeploymentAcceptedReportEvent;
import com.zto.devops.qc.client.model.rpc.pipeline.event.DeploymentBackedEvent;
import com.zto.devops.qc.client.model.rpc.pipeline.event.DeploymentDelayAcceptedEvent;
import com.zto.devops.qc.client.model.rpc.pipeline.event.VersionStatusChangedEvent;
import com.zto.devops.qc.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.qc.client.model.rpc.product.SimpleProductQueryListVO;
import com.zto.devops.qc.client.model.rpc.product.SimpleQueryVO;
import com.zto.devops.qc.client.model.rpc.project.*;
import com.zto.devops.qc.client.model.rpc.project.enums.VersionMergeTypeEnum;
import com.zto.devops.qc.client.model.rpc.user.ListUserQuery;
import com.zto.devops.qc.client.model.rpc.user.UserSelectVO;
import com.zto.devops.qc.client.model.testPlan.event.AddSafetyTestPlanEvent;
import com.zto.devops.qc.client.model.testPlan.event.EditSafetyTestPlanEvent;
import com.zto.devops.qc.client.model.testmanager.plan.command.*;
import com.zto.devops.qc.client.model.testmanager.plan.entity.BatchTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.event.*;
import com.zto.devops.qc.client.model.testmanager.plan.query.FindPlanByProductQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.ListPlanCaseCodeQuery;
import com.zto.devops.qc.domain.converter.TmTestPlanDomainConverter;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.message.ITestReportOrPlanMsgAuditor;
import com.zto.devops.qc.domain.gateway.mq.TestPlanMQSender;
import com.zto.devops.qc.domain.gateway.report.SecurityTestReportService;
import com.zto.devops.qc.domain.gateway.repository.*;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IUserRpcService;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/4/13
 * @Version 1.0
 */
@Component
@Slf4j
public class TmTestPlanCommandDomainService extends BaseDomainService {

    @Autowired
    private QcConfigBasicService qcConfigBasicService;
    @Autowired
    private SecurityTestReportService securityTestReportService;
    @Autowired
    private ITmTestPlanRepository tmTestPlanRepository;
    @Autowired
    private TmTestPlanRangeRepository tmTestPlanRangeRepository;
    @Autowired
    private ITmTestPlanCaseRepository tmTestPlanCaseRepository;
    @Autowired
    private IEmailRepository emailRepository;
    @Autowired
    private TestcaseRepository testcaseRepository;
    @Autowired
    private IEventLogRepository logManager;
    @Autowired
    private QcNoticeResultRepository qcNoticeResultRepository;
    @Autowired
    private TestPlanMQSender testPlanSender;
    @Autowired
    private ITestReportOrPlanMsgAuditor testReportOrPlanMsgAuditor;
    @Autowired
    private TmTestPlanDomainConverter tmTestPlanDomainConverter;
    @Autowired
    private IPipelineRpcService iPipelineRpcService;
    @Autowired
    private TestFunctionPointRepository testFunctionPointRepository;
    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;
    @Autowired
    private ITmDelayAcceptRecordRepository tmDelayAcceptRecordRepository;
    @Autowired
    private IProductRpcService productRpcService;
    @Autowired
    private IUserRpcService userRpcService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private IProjectRpcService iProjectRpcService;
    @Autowired
    private TmTestPlanQueryDomainService tmTestPlanQueryDomainService;
    private final Executor poolExecutor = new ThreadPoolExecutor(
            10,
            100,
            30L,
            TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            ThreadFactoryBuilder.create().setNamePrefix("TmTestPlanExecution-pool-").build());


    public void handleAddSafeTestPlanCommand(AddSafeTestPlanCommand command) {
        TmTestPlanVO vo = command.getTestPlanVO();
        if (Objects.isNull(vo)) {
            throw new ServiceException("参数异常");
        }
        if (StringUtil.isEmpty(vo.getCode())) {
            vo.setCode(command.getAggregateId());
        }
        vo.setType(TestPlanNewTypeEnum.SAFETY_TEST);
        vo.setTestStrategy(TestPlanStrategyEnum.NULL_TEST);
        SafeTestPlanAddedEvent event = new SafeTestPlanAddedEvent();
        event.setAggregateId(command.getAggregateId());
        event.setTransactor(command.getTransactor());
        event.setOccurred(new Date());
        event.setTestPlanVO(command.getTestPlanVO());
        tmTestPlanRepository.addTestPlan(vo, event);
        //发送mq给安全平台
        sendAddSafetyTestPlanMq(command);
        apply(event);
    }

    public void deleteTestPlan(VersionDeleteEvent deleteEvent) {
        log.info("删除测试计划开始, versionCode:{}", deleteEvent.getCode());
        tmTestPlanRepository.deleteTestPlanByVersionCode(deleteEvent);
    }

    private void sendAddSafetyTestPlanMq(AddSafeTestPlanCommand command) {
        TmTestPlanVO vo = command.getTestPlanVO();
        AddSafetyTestPlanEvent mq = new AddSafetyTestPlanEvent();
        mq.setAggregateId(command.getAggregateId());
        if (StringUtil.isBlank(vo.getTestInformation())) {
            mq.setTestInformation("测试信息");
        }
        mq.setCode(command.getAggregateId());
        mq.setTestPlanCode(command.getAggregateId());
        mq.setStatus(vo.getStatus().name());
        mq.setPermissionsTest(vo.getPermissionsTest());
        mq.setPriority(vo.getPriority().name());
        mq.setLastTestDate(vo.getLastTestDate());
        mq.setTestInformation(vo.getTestInformation());
        mq.setPriorityTestInformation(vo.getPermissionsTestInformation());
        mq.setRecipients(Collections.emptyList());
        mq.setCcUsers(Collections.emptyList());
        mq.setProductCode(vo.getProductCode());
        mq.setProductName(vo.getProductName());
        mq.setVersionCode(vo.getVersionCode());
        mq.setVersionName(vo.getVersionName());
        mq.setPlanName(vo.getPlanName());
        mq.setTransactor(command.getTransactor());
        mq.setOccurred(new Date());
        mq.setStatus(TestPlanStatusEnum.NORMAL.name());
        mq.setTestPlanMainCode(vo.getRelationPlanCode());
        testPlanSender.handleAddSafetyTestPlanEvent(mq);
        apply(mq);
    }

    public void handleAddCommonTestPlanCommand(AddCommonTestPlanCommand command) {
        log.info("进入AddCommonTestPlanCommand.{}", JsonUtil.toJSON(command));
        TmTestPlanVO vo = command.getTestPlanVO();
        if (Objects.isNull(vo)) {
            throw new ServiceException("参数异常");
        }
        vo.setType(TestPlanNewTypeEnum.TEST_PLAN);
        CommonTestPlanAddedEvent event = new CommonTestPlanAddedEvent();
        event.setAggregateId(command.getAggregateId());
        event.setTransactor(command.getTransactor());
        event.setOccurred(new Date());
        event.setTestPlanVO(vo);
        tmTestPlanRepository.addTestPlan(vo, event);
        testPlanSender.handleCommonTestPlanAddedEvent(event);
        apply(event);
        //如果是大版本，需将小版本的测试计划复制进来
        this.syncVersionTestPlanCopyMinorToMajor(event);
    }

    private void syncVersionTestPlanCopyMinorToMajor(CommonTestPlanAddedEvent event){
        TmTestPlanVO testPlan = event.getTestPlanVO();
        VersionVO version = iProjectRpcService.getVersionContainsSubVersions(testPlan.getVersionCode());
        if (Objects.isNull(version)
                || !Objects.equals(VersionMergeTypeEnum.MERGED_MAJOR_VERSION, version.getMergeType())
                || CollectionUtil.isEmpty(version.getSubVersions()) ) {
            return;
        }
        log.info("----syncVersionTestPlanCopyMinorToMajor---{},{}", testPlan.getVersionCode(), testPlan.getVersionName());
        List<VersionVO> subVersions = version.getSubVersions().stream()
                .filter(Objects::nonNull)
                .filter(o -> VersionTypeEnum.COMMON_TYPE.name().equals(o.getType()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(subVersions)) {
            return;
        }
        User operator = new User(0L, "系统(由版本合并生成)");
        for (VersionVO fromVersion : subVersions) {
            TmTestPlanEntityDO fromTestPlan = tmTestPlanRepository.selectByCD(fromVersion.getProductCode(), fromVersion.getCode(), TestPlanTypeEnum.TEST_PLAN.name());
            this.copyTestPlan(fromTestPlan, testPlan.getCode(), testPlan.getStageStatus(), operator);
        }
    }
    private void copyTestPlan(TmTestPlanEntityDO fromTestPlan, String toTestPlanCode, Map<String, Object> toStageStatus, User operator) {
        if (Objects.isNull(fromTestPlan)) {
            return;
        }
        if (Objects.isNull(toStageStatus) || toStageStatus.isEmpty()) {
            //from的所有计划用例都复制到TestPlanStageEnum.NULL_TEST
            this.doCopyTestPlan(fromTestPlan, toTestPlanCode, 1, operator);
        } else {
            if (Objects.isNull(fromTestPlan.getStageStatus()) || fromTestPlan.getStageStatus().isEmpty() || fromTestPlan.getStageStatus().size() <=1) {
                //from的所有计划用例都复制到TestPlanStageEnum.SMOKE_TEST
                this.doCopyTestPlan(fromTestPlan, toTestPlanCode, 2, operator);
            } else {
                //复制到对应阶段
                this.doCopyTestPlan(fromTestPlan, toTestPlanCode, 3, operator);
            }
        }
    }
    /**
     * @param copyType：
     *                1、from的所有计划用例都复制到TestPlanStageEnum.NULL_TEST
     *                2、from的所有计划用例都复制到TestPlanStageEnum.SMOKE_TEST
     *                3、from的所有计划用例复制到各自对应阶段
     * */
    private void doCopyTestPlan(TmTestPlanEntityDO fromTestPlan, String toTestPlanCode, int copyType, User operator) {
        List<TmTestPlanCaseEntityDO>  planCases=tmTestPlanCaseRepository.selectByPlanCode(fromTestPlan.getCode());
        if (CollectionUtil.isEmpty(planCases)) {
            return;
        }
        Map<TestPlanStageEnum, List<String>> map = new HashMap<>();
        try {
            if (copyType == 1) {
                //1、from的所有计划用例都复制到TestPlanStageEnum.NULL_TEST
                List<String> caseCodes = planCases.stream().map(TmTestPlanCaseEntityDO::getCaseCode).distinct().collect(Collectors.toList());
                map.put(TestPlanStageEnum.NULL_TEST, caseCodes);
            } else if (copyType == 2) {
                //2、from的所有计划用例都复制到TestPlanStageEnum.SMOKE_TEST
                List<String> caseCodes = planCases.stream().map(TmTestPlanCaseEntityDO::getCaseCode).distinct().collect(Collectors.toList());
                map.put(TestPlanStageEnum.SMOKE_TEST, caseCodes);
            } else if (copyType == 3) {
                //3、from的所有计划用例复制到各自对应阶段
                map = planCases.stream()
                        .collect(Collectors.groupingBy(TmTestPlanCaseEntityDO::getTestStage,
                                Collectors.mapping(TmTestPlanCaseEntityDO::getCaseCode, Collectors.toList())));

            }
            if (map.isEmpty()) {
                return;
            }
            map.forEach((testStage, caseCodes) -> {
                BatchTestPlanVO planVo = new BatchTestPlanVO();
                planVo.setTestPlanCode(toTestPlanCode);
                planVo.setTestStage(Collections.singletonList(testStage));
                doBatchJoinPlan(caseCodes, Collections.singletonList(planVo), operator);
            });
        } catch (Exception e) {
            log.error("----syncVersionTestPlanCopyMinorToMajor-doCopyTestPlan error--{},{}, :", fromTestPlan.getVersionCode(), fromTestPlan.getVersionName(), e);
        }
    }
    public void changeStageStatus(EditPlanStageStatusCommand command) {
        EditPlanStageStatusEvent event = tmTestPlanDomainConverter.convert(command);
        event.setAggregateId(command.getAggregateId());
        event.setTransactor(command.getTransactor());
        event.setOccurred(new Date());
        TmTestPlanEntityDO entityDO = tmTestPlanRepository.getTestPlanByCode(event.getPlanCode());
        Map<String, Object> map = new HashMap<>();
        if (entityDO.getStageStatus() == null) {
            map.put(TestPlanStageEnum.SMOKE_TEST.name(), TestPlanStageStatusEnum.INITIAL);
            map.put(TestPlanStageEnum.FUNCTIONAL_TEST.name(), TestPlanStageStatusEnum.INITIAL);
            map.put(TestPlanStageEnum.ONLINE_SMOKE_TEST.name(), TestPlanStageStatusEnum.INITIAL);
        } else {
            map = entityDO.getStageStatus();
        }
        map = setMapData(map, event);
        TmTestPlanEntityDO updateEntityDO = new TmTestPlanEntityDO();
        updateEntityDO.setStageStatus(map);
        updateEntityDO.preUpdate(event);
        tmTestPlanRepository.updateStageStatusByPrimaryKey(updateEntityDO, event.getPlanCode());
        apply(event);
    }

    public void doChangeStageStatus(VersionStatusChangedEvent event) {
        log.info("VersionStatusChangedEvent_doChangeStageStatus_event:{}", event);
        if (Objects.isNull(event) || CollectionUtil.isEmpty(event.getVersionCodes())) {
            return;
        }
        String versionCode = event.getVersionCodes().get(0);
        if (StringUtil.isBlank(versionCode)) {
            log.info("VersionStatusChangedEvent_doChangeStageStatus_versionCode_is_blank");
            return;
        }

        TmTestPlanEntityDO planEntityDO = tmTestPlanRepository.getTestPlanByVersion(versionCode);
        if (Objects.isNull(planEntityDO.getStatus()) || TestPlanNewStatusEnum.TERMINATED.equals(planEntityDO.getStatus())) {
            log.info("VersionStatusChangedEvent_doChangeStageStatus_plan_is_TERMINATED_versionCode:{}", versionCode);
            return;
        }

        //回归不通过，覆盖率确认状态刷新
        if (event.getPreStatus().equals(FlowStatusEnum.REGRESSING)
                && event.getStatus().equals(FlowStatusEnum.TESTING)) {
            tmTestPlanRepository.updateCheckFlag(planEntityDO.getCode(), null, Boolean.FALSE, new User());
        }

        //标准测试以外，不做处理
        if (!TestPlanStrategyEnum.STANDARD_TEST.equals(planEntityDO.getTestStrategy()) || MapUtils.isEmpty(planEntityDO.getStageStatus())) {
            log.info("VersionStatusChangedEvent_doChangeStageStatus_is_not_STANDARD_TEST_versionCode:{}", versionCode);
            return;
        }

        //冒烟结果-通过
        if (event.getPreStatus().equals(FlowStatusEnum.SMOKING)
                && event.getStatus().equals(FlowStatusEnum.TESTING)
                && !TestPlanStageStatusEnum.COMPLETED.name().equals(planEntityDO.getStageStatus().getOrDefault(TestPlanStageEnum.SMOKE_TEST.name(), ""))) {
            log.info("VersionStatusChangedEvent_doChangeStageStatus_冒烟通过_versionCode:{}", versionCode);
            EditPlanStageStatusCommand command = new EditPlanStageStatusCommand(planEntityDO.getCode());
            command.setPlanCode(planEntityDO.getCode());
            command.setStage(TestPlanStageEnum.SMOKE_TEST);
            command.setType(TestPlanButttonTypeEnum.END);
            changeStageStatus(command);
            return;
        }

        //冒烟/测试结果-不通过退回
        if (Arrays.asList(FlowStatusEnum.SMOKING, FlowStatusEnum.TESTING).contains(event.getPreStatus())
                && event.getStatus().equals(FlowStatusEnum.DEVELOPING)
                && TestPlanStageStatusEnum.COMPLETED.name().equals(planEntityDO.getStageStatus().getOrDefault(TestPlanStageEnum.SMOKE_TEST.name(), ""))) {
            log.info("VersionStatusChangedEvent_doChangeStageStatus_冒烟/测试退回_versionCode:{}", versionCode);
            EditPlanStageStatusCommand command = new EditPlanStageStatusCommand(planEntityDO.getCode());
            command.setPlanCode(planEntityDO.getCode());
            command.setStage(TestPlanStageEnum.SMOKE_TEST);
            command.setType(TestPlanButttonTypeEnum.START);
            changeStageStatus(command);
            return;
        }

        //回归结果-通过
        if (event.getPreStatus().equals(FlowStatusEnum.REGRESSING)
                && event.getStatus().equals(FlowStatusEnum.REGRESSED)
                && !TestPlanStageStatusEnum.COMPLETED.name().equals(planEntityDO.getStageStatus().getOrDefault(TestPlanStageEnum.FUNCTIONAL_TEST.name(), ""))) {
            log.info("VersionStatusChangedEvent_doChangeStageStatus_回归通过_versionCode:{}", versionCode);
            EditPlanStageStatusCommand command = new EditPlanStageStatusCommand(planEntityDO.getCode());
            command.setPlanCode(planEntityDO.getCode());
            command.setStage(TestPlanStageEnum.FUNCTIONAL_TEST);
            command.setType(TestPlanButttonTypeEnum.END);
            changeStageStatus(command);
            return;
        }

        //回归结果-不通过退回
        if (event.getPreStatus().equals(FlowStatusEnum.REGRESSING)
                && event.getStatus().equals(FlowStatusEnum.TESTING)
                && TestPlanStageStatusEnum.COMPLETED.name().equals(planEntityDO.getStageStatus().getOrDefault(TestPlanStageEnum.FUNCTIONAL_TEST.name(), ""))) {
            log.info("VersionStatusChangedEvent_doChangeStageStatus_回归退回_versionCode:{}", versionCode);
            EditPlanStageStatusCommand command = new EditPlanStageStatusCommand(planEntityDO.getCode());
            command.setPlanCode(planEntityDO.getCode());
            command.setStage(TestPlanStageEnum.FUNCTIONAL_TEST);
            command.setType(TestPlanButttonTypeEnum.START);
            changeStageStatus(command);
            return;
        }

        //验收结果-通过
        if (event.getPreStatus().equals(FlowStatusEnum.ACCEPTING)
                && event.getStatus().equals(FlowStatusEnum.ACCEPTED)
                && !TestPlanStageStatusEnum.COMPLETED.name().equals(planEntityDO.getStageStatus().getOrDefault(TestPlanStageEnum.ONLINE_SMOKE_TEST.name(), ""))) {
            log.info("VersionStatusChangedEvent_doChangeStageStatus_验收通过_versionCode:{}", versionCode);
            EditPlanStageStatusCommand command = new EditPlanStageStatusCommand(planEntityDO.getCode());
            command.setPlanCode(planEntityDO.getCode());
            command.setStage(TestPlanStageEnum.ONLINE_SMOKE_TEST);
            command.setType(TestPlanButttonTypeEnum.END);
            changeStageStatus(command);
        }
    }

    public Map<String, Object> setMapData(Map<String, Object> map, EditPlanStageStatusEvent event) {
        if (event.getStage().equals(TestPlanStageEnum.SMOKE_TEST)) {
            if (event.getType().equals(TestPlanButttonTypeEnum.START)) {
                map.replace(TestPlanStageEnum.SMOKE_TEST.name(), TestPlanStageStatusEnum.IN_PROGRESS);
            }
            if (event.getType().equals(TestPlanButttonTypeEnum.END)) {
                map.replace(TestPlanStageEnum.SMOKE_TEST.name(), TestPlanStageStatusEnum.COMPLETED);
                if (map.get(TestPlanStageEnum.FUNCTIONAL_TEST.name()).equals(TestPlanStageStatusEnum.INITIAL.name())) {
                    map.replace(TestPlanStageEnum.FUNCTIONAL_TEST.name(), TestPlanStageStatusEnum.IN_PROGRESS);
                }
            }
        }
        if (event.getStage().equals(TestPlanStageEnum.FUNCTIONAL_TEST)) {
            if (event.getType().equals(TestPlanButttonTypeEnum.START)) {
                map.replace(TestPlanStageEnum.FUNCTIONAL_TEST.name(), TestPlanStageStatusEnum.IN_PROGRESS);
            }
            if (event.getType().equals(TestPlanButttonTypeEnum.END)) {
                map.replace(TestPlanStageEnum.FUNCTIONAL_TEST.name(), TestPlanStageStatusEnum.COMPLETED);
                if (map.get(TestPlanStageEnum.ONLINE_SMOKE_TEST.name()).equals(TestPlanStageStatusEnum.INITIAL.name())) {
                    map.replace(TestPlanStageEnum.ONLINE_SMOKE_TEST.name(), TestPlanStageStatusEnum.IN_PROGRESS);
                }
            }
        }
        if (event.getStage().equals(TestPlanStageEnum.ONLINE_SMOKE_TEST)) {
            if (event.getType().equals(TestPlanButttonTypeEnum.START)) {
                map.replace(TestPlanStageEnum.ONLINE_SMOKE_TEST.name(), TestPlanStageStatusEnum.IN_PROGRESS);
            }
            if (event.getType().equals(TestPlanButttonTypeEnum.END)) {
                map.replace(TestPlanStageEnum.ONLINE_SMOKE_TEST.name(), TestPlanStageStatusEnum.COMPLETED);
            }
        }
        return map;
    }

    public void changePlanStatus(EditPlanStatusCommand command) {
        TmTestPlanEntityDO entityDO = tmTestPlanRepository.getTestPlanByCode(command.getPlanCode());
        if (entityDO == null) {
            throw new ServiceException("测试计划不存在！");
        }
        command.setPlanType(entityDO.getType());
        command.setRelationPlanCode(entityDO.getRelationPlanCode());
        if (!TestPlanNewTypeEnum.SAFETY_TEST.equals(entityDO.getType())) {
            doChangeStatus(command);
            return;
        }
        if (TestPlanNewStatusEnum.IN_PROGRESS.equals(command.getStatus())) {
            List<TmEmailEntityDO> entityDOList =
                    emailRepository.selectByBusinessCodeAndEmailType(command.getPlanCode(), EmailTypeEnum.SAFETY_TEST);
            if (CollectionUtil.isEmpty(entityDOList)) {
                throw new ServiceException("请先发送安全测试计划邮件再开始测试");
            }
        }
        if (TestPlanNewStatusEnum.COMPLETED.equals(command.getStatus())) {
            SecurityTestResult securityTestResult = getSecurityTestResult(command.getPlanCode());
            if (securityTestResult.equals(SecurityTestResult.NOPASS)) {
                throw new ServiceException("安全测试未通过，请到安全平台处理完漏洞后再完成测试");
            }
            command.setSecurityTestResult(securityTestResult);
        }
        doChangeStatus(command);
    }

    private void doChangeStatus(EditPlanStatusCommand command) {
        EditPlanStatusEvent event = tmTestPlanDomainConverter.convert(command);
        event.setAggregateId(command.getAggregateId());
        event.setTransactor(command.getTransactor());
        event.setOccurred(new Date());
        TmTestPlanEntityDO testPlanEntityDO = tmTestPlanRepository.getTestPlanByCode(event.getPlanCode());
        TmTestPlanEntityDO entityDO = tmTestPlanDomainConverter.convert(event);
        entityDO.preUpdate(event);
        if (event.getStatus().equals(TestPlanNewStatusEnum.TERMINATED)) {
            entityDO.setHistoryStatus(testPlanEntityDO.getStatus());
        }
        if (event.getStatus().equals(TestPlanNewStatusEnum.RESTART)) {
            entityDO.setStatus(testPlanEntityDO.getHistoryStatus());
        }
        tmTestPlanRepository.updateHistoryStatusByPrimaryKey(entityDO, event.getPlanCode());

        if (TestPlanNewTypeEnum.SAFETY_TEST.equals(event.getPlanType()) && event.getStatus().equals(TestPlanNewStatusEnum.COMPLETED)) {
            TmTestPlanRangeEntityDO rangeEntityDO =
                    tmTestPlanRangeRepository.selectByPlanCodeAndTestRange(event.getRelationPlanCode(), TestPlanRangeTypeEnum.SAFETY_SCANNING);
            if (rangeEntityDO == null) {
                log.error("安全测试计划范围TmTestPlanRangeEntity查询不存在:" + event.getPlanCode());
                return;
            }
            rangeEntityDO.setStatus(event.getSecurityTestResult().name());
            rangeEntityDO.setExecutorId(event.getTransactor().getUserId());
            rangeEntityDO.setExecutor(event.getTransactor().getUserName());
            rangeEntityDO.preUpdate(event);
            tmTestPlanRangeRepository.updateByPrimaryKey(rangeEntityDO);
        }
        testPlanSender.handleEditPlanStatusEvent(event);
        apply(event);
    }

    private SecurityTestResult getSecurityTestResult(String planCode) {
        SecurityTestResultVO securityTestResultVO = securityTestReportService.querySecurityTestResult(planCode);
        if (securityTestResultVO == null) {
            return SecurityTestResult.NOPASS;
        }
        if (securityTestResultVO.getAllowrelease() != null && securityTestResultVO.getAllowrelease()) {
            return SecurityTestResult.PASS;
        }
        return SecurityTestResult.NOPASS;
    }

    public void batchJoinPlan(List<String> caseCodes, String planCode, TestPlanStageEnum stage, User operator) {
        if (CollectionUtil.isEmpty(caseCodes) || StringUtil.isEmpty(planCode) || stage == null) {
            return;
        }
        int bigSize = qcConfigBasicService.getBigSize();
        if (CollectionUtil.isNotEmpty(caseCodes) && caseCodes.size() > bigSize) {
            throw new ServiceException("测试计划关联用例最多支持2000条数据");
        }
        TmTestPlanEntityDO entityDO = tmTestPlanRepository.getTestPlanByCode(planCode);
        if (null == entityDO || !entityDO.getEnable() || TestPlanNewStatusEnum.TERMINATED.equals(entityDO.getStatus())
                || TestPlanNewStatusEnum.COMPLETED.equals(entityDO.getStatus())) {
            throw new ServiceException("测试计划不支持关联用例");
        }
        if (null != entityDO.getStageStatus() &&
                TestPlanStageStatusEnum.COMPLETED.name().equals(entityDO.getStageStatus().get(stage.name()))) {
            throw new ServiceException("已完成阶段不支持关联用例");
        }
        BatchTestPlanVO planVo = new BatchTestPlanVO();
        planVo.setTestPlanCode(planCode);
        planVo.setTestStage(Collections.singletonList(stage));
        doBatchJoinPlan(caseCodes, Collections.singletonList(planVo), operator);
    }

    private void doBatchJoinPlan(List<String> caseCodes, List<BatchTestPlanVO> planList, User operator) {
        Map<String, Map<TestPlanStageEnum, Set<String>>> planStageCaseMap = new HashMap<>();
        for (BatchTestPlanVO plan : planList) {
            if (CollectionUtil.isEmpty(plan.getTestStage())) {
                continue;
            }
            List<TmTestPlanCaseEntityDO> planCaseDO =
                    tmTestPlanCaseRepository.selectTestPlanCaseByPlanCodeAndTestStage(plan.getTestPlanCode(), plan.getTestStage());
            HashMap<TestPlanStageEnum, Set<String>> initStageCaseMap = new HashMap<>();
            plan.getTestStage().forEach(t -> initStageCaseMap.put(t, new HashSet<>()));
            if (CollectionUtil.isNotEmpty(planCaseDO)) {
                initStageCaseMap.putAll(planCaseDO.stream()
                        .collect(Collectors.groupingBy(TmTestPlanCaseEntityDO::getTestStage,
                                Collectors.mapping(TmTestPlanCaseEntityDO::getCaseCode, Collectors.toSet())))
                );
            }
            planStageCaseMap.put(plan.getTestPlanCode(), initStageCaseMap);
        }

        // 获取case的类型
        List<TestcaseEntityDO> cases = testcaseRepository.selectByStatusAndCodeList(TestcaseStatusEnum.NORMAL, caseCodes);
        Map<String, TestcaseTypeEnum> caseTypeMap =
                cases.parallelStream().collect(Collectors.toMap(TestcaseEntityDO::getCode, TestcaseEntityDO::getType));

        // 过滤已停用的code
        caseCodes.removeIf(c -> !caseTypeMap.containsKey(c));

        // 构造需要加入计划的 TmTestPlanCaseEntity 对象
        ArrayList<TmTestPlanCaseEntityDO> waitInsert = new ArrayList<>();
        for (Map.Entry<String, Map<TestPlanStageEnum, Set<String>>> planCaseMap : planStageCaseMap.entrySet()) {
            String planCode = planCaseMap.getKey();
            for (Map.Entry<TestPlanStageEnum, Set<String>> stageCaseMap : planCaseMap.getValue().entrySet()) {
                TestPlanStageEnum stage = stageCaseMap.getKey();
                Set<String> existCodes = stageCaseMap.getValue();
                for (String caseCode : caseCodes) {
                    // 过滤已存在计划节点的用例
                    if (existCodes != null && existCodes.contains(caseCode)) {
                        continue;
                    }
                    waitInsert.add(buildPlanCaseEntity(caseCode, planCode, stage, caseTypeMap.get(caseCode), operator));
                }
            }
        }
        if (CollectionUtil.isEmpty(waitInsert)) {
            return;
        }
        // 分批插入
        List<List<TmTestPlanCaseEntityDO>> partitions = Lists.partition(waitInsert, 200);
        partitions.forEach(p -> tmTestPlanCaseRepository.batchSave(p));
        // 日志
        poolExecutor.execute(() -> {
            List<String> operateCodes = waitInsert.stream().map(TmTestPlanCaseEntityDO::getOperateCaseCode).collect(Collectors.toList());
            String eventClassName = AddBatchCaseInBatchTestPlanEvent.class.getName();
            logManager.createLogs(operateCodes.stream().map(c -> new ActionLogDO(c, "加入了计划", "",
                    eventClassName, operator.getUserName(), operator.getUserId())).collect(Collectors.toList()));
        });
    }

    private TmTestPlanCaseEntityDO buildPlanCaseEntity(String caseCode, String planCode,
                                                       TestPlanStageEnum stage, TestcaseTypeEnum caseType, User operator) {
        TmTestPlanCaseEntityDO entity = new TmTestPlanCaseEntityDO();
        entity.setCaseCode(caseCode);
        entity.setPlanCode(planCode);
        entity.setTestStage(stage);
        entity.setCaseType(caseType);
        entity.setStatus(caseType.equals(TestcaseTypeEnum.MANUAL) ? TestPlanCaseStatusEnum.INITIAL : TestPlanCaseStatusEnum.NOT_STARTED);
        entity.setOperateCaseCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        entity.setCreator(operator.getUserName());
        entity.setCreatorId(operator.getUserId());
        fillUserAndDate(entity, operator);
        return entity;
    }

    private void fillUserAndDate(BaseEntityDO entity, User operator) {
        if (entity == null) {
            return;
        }
        if (operator != null) {
            entity.setModifier(operator.getUserName());
            entity.setModifierId(operator.getUserId());
        }
        entity.setGmtModified(new Date());
    }

    public void batchRemoveFromPlan(List<String> caseCodes, String planCode,
                                    TestPlanStageEnum stage, User operator) {
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        int bigSize = qcConfigBasicService.getBigSize();
        if (CollectionUtil.isNotEmpty(caseCodes) && caseCodes.size() > bigSize) {
            throw new ServiceException("批量移除测试计划一次最多支持2000条数据");
        }
        if (StringUtil.isEmpty(planCode) || stage == null) {
            throw new ServiceException("计划编码或阶段参数异常");
        }
        TmTestPlanEntityDO planEntityDO = tmTestPlanRepository.selectEnableByPrimaryKey(planCode);
        if (planEntityDO == null) {
            throw new ServiceException("未找到计划");
        }
        doRemoveFromPlan(caseCodes, planEntityDO, stage, operator);
    }

    private void doRemoveFromPlan(List<String> caseCodes, TmTestPlanEntityDO plan, TestPlanStageEnum stage, User operator) {
        // 更新操作
        if (TestPlanNewStatusEnum.COMPLETED.equals(plan.getStatus()) ||
                (plan.getStageStatus() != null &&
                        TestPlanStageStatusEnum.COMPLETED.name().equals(plan.getStageStatus().get(stage.name())))) {
            List<TmTestPlanCaseEntityDO> entityDOList = tmTestPlanCaseRepository.selectByPlanCodeAndTestStageAndCodeList(plan.getCode(), stage, caseCodes);
            Set<String> operateCodes = entityDOList.stream().map(TmTestPlanCaseEntityDO::getOperateCaseCode).collect(Collectors.toSet());

            TmTestPlanCaseEntityDO entity = new TmTestPlanCaseEntityDO();
            entity.setEnable(false);
            fillUserAndDate(entity, operator);
            tmTestPlanCaseRepository.updateByPlanCodeAndTestStageAndCodeList(plan.getCode(), stage, caseCodes, entity);
            if (CollectionUtil.isNotEmpty(operateCodes)) {
                // 记录日志
                poolExecutor.execute(() -> {
                    String eventClassName = RemoveCaseFromTestPlanEvent.class.getName();
                    logManager.createLogs(operateCodes.stream().map(c -> new ActionLogDO(c, "移出测试计划", "",
                            eventClassName, operator.getUserName(), operator.getUserId())).collect(Collectors.toList()));
                });
            }
        } else {
            // 删除操作
            tmTestPlanCaseRepository.deleteByCaseCodeList(caseCodes, plan.getCode(), stage);
        }
    }

    public void sendPlanEmail(SendTestPlanCommand command) {
        SendTestPlanEvent sendEvent = tmTestPlanDomainConverter.convertor(command.getTmTestPlanSendEmailVO());
        sendEvent.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        sendEvent.setEmailCode(sendEvent.getAggregateId());
        sendEvent.setName(sendEvent.getBusinessName());
        sendEvent.setTransactor(command.getTransactor());
        sendEvent.setOccurred(new Date());
        sendEvent.setSenderId(command.getTransactor().getUserId());
        sendEvent.setSender(command.getTransactor().getUserName());
        sendEvent.setSendDate(sendEvent.getOccurred());
        doSendPlanEmail(sendEvent);
        apply(sendEvent);
    }

    public void doSendPlanEmail(SendTestPlanEvent event) {
        TmEmailEntityDO entityDO = tmTestPlanDomainConverter.convert(event);
        entityDO.preCreate(event);
        entityDO.setEmailSource(EmailSourceEnum.TEST_PLAN);
        emailRepository.insertSelective(entityDO);
        insertNoticeResult(event);
        TestPlanOrReportMsg testPlanOrReportMsg = testReportOrPlanMsgAuditor.doHandleSendTestPlanEvent(event);
        testReportOrPlanMsgAuditor.sendMessage(testPlanOrReportMsg);
    }

    public void insertNoticeResult(SendTestPlanEvent event) {
        List<SendUserInfoVO> allList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(event.getCcUsers())) {
            List<SendUserInfoVO> list = event.getCcUsers();
            list.forEach(cc -> cc.setUserType(NoticeUserTypeEnum.CC.name()));
            list = list.stream().filter(n -> NoticeUserTypeEnum.CC.name().equals(n.getUserType()))
                    .distinct().collect(Collectors.toList());
            allList.addAll(list);
        }
        if (CollectionUtil.isNotEmpty(event.getReceiveUsers())) {
            List<SendUserInfoVO> list = event.getReceiveUsers();
            list.forEach(r -> r.setUserType(NoticeUserTypeEnum.RECIPIENT.name()));
            list = list.stream().filter(n -> NoticeUserTypeEnum.RECIPIENT.name().equals(n.getUserType()))
                    .distinct().collect(Collectors.toList());
            allList.addAll(list);
        }
        if (CollectionUtil.isEmpty(allList)) {
            return;
        }
        qcNoticeResultRepository.insertBatch(allList, event.getEmailCode(), event.getTransactor());
    }

    public void checkTestPlanName(String planName, String productCode, String planCode) {
        FindPlanByProductQuery query = new FindPlanByProductQuery(planName, productCode, planCode);
        Boolean checkResult = tmTestPlanRepository.checkTestPlanName(query);
        if (checkResult) {
            throw new ServiceException("计划标题不可重复，当前标题已存在，请重新编辑标题后提交！");
        }
    }

    public void addMobileSpecialTestPlanCommand(AddMobileSpecialTestPlanCommand command) {
        TmTestPlanVO vo = command.getTestPlanVO();
        if (Objects.isNull(vo)) {
            throw new ServiceException("参数异常");
        }
        if (Objects.isNull(command.getTransactor())) {
            throw new ServiceException("登录人信息异常");
        }
        vo.setType(TestPlanNewTypeEnum.MOBILE_SPECIAL);
        vo.setTestStrategy(TestPlanStrategyEnum.NULL_TEST);
        vo.setStatus(TestPlanNewStatusEnum.NOT_STARTED);
        vo.setPlanDirectorId(command.getTransactor().getUserId());
        vo.setPlanDirectorName(command.getTransactor().getUserName());
        MobileSpecialTestPlanAddedEvent event = new MobileSpecialTestPlanAddedEvent();
        event.setAggregateId(command.getAggregateId());
        event.setTransactor(command.getTransactor());
        event.setOccurred(new Date());
        event.setTestPlanVO(vo);
        tmTestPlanRepository.addTestPlan(vo, event);
        testPlanSender.handleMobileSpecialTestPlanAddedEvent(event);
        apply(event);
    }

    public void changeCaseExecuteResultCommand(ChangeCaseExecuteResultCommand command) {
        ChangeCaseExecuteResultEvent changeCaseExecuteResultEvent = tmTestPlanDomainConverter.convert(command);
        changeCaseExecuteResultEvent.setTransactor(command.getTransactor());
        changeCaseExecuteResultEvent.setOccurred(new Date());
        tmTestPlanRepository.changeCaseExecuteResult(changeCaseExecuteResultEvent);
        changeCaseExecuteResultEvent.setAggregateId(command.getAggregateId());
        poolExecutor.execute(() -> {
            log.info("消费ChangeCaseExecuteResultEvent。{}", JsonUtil.toJSON(changeCaseExecuteResultEvent));
            if (CollectionUtil.isEmpty(changeCaseExecuteResultEvent.getCaseIds())) {
                return;
            }
            changeCaseExecuteResultEvent.getCaseIds().forEach(t ->
                    {
                        TestcaseExecuteRecordEntityDO testcaseExecuteRecordEntity = new TestcaseExecuteRecordEntityDO();
                        testcaseExecuteRecordEntity.setTestPlanCode(changeCaseExecuteResultEvent.getPlanCode());
                        testcaseExecuteRecordEntity.setTestcaseCode(t);
                        testcaseExecuteRecordEntity.setTestStage(changeCaseExecuteResultEvent.getTestStage());
                        testcaseExecuteRecordEntity.setResult(changeCaseExecuteResultEvent.getExecuteStatus());
                        testcaseExecuteRecordEntity.preCreate(changeCaseExecuteResultEvent);
                        testcaseRepository.insertTestcaseExecuteRecord(testcaseExecuteRecordEntity);
                    }
            );
            if (CollectionUtil.isEmpty(changeCaseExecuteResultEvent.getOperateCaseCodeList())) {
                return;
            }
            logManager.createLogs(changeCaseExecuteResultEvent.getOperateCaseCodeList().stream().map(c -> new ActionLogDO(c, "变更了用例执行结果", String.format("修改为 >>> %s",
                    changeCaseExecuteResultEvent.getExecuteStatus().getValue()), changeCaseExecuteResultEvent.getClass().getName(),
                    changeCaseExecuteResultEvent.getTransactor().getUserName(), changeCaseExecuteResultEvent.getTransactor().getUserId())).collect(Collectors.toList()));
        });
        apply(changeCaseExecuteResultEvent);
    }

    public void changePlanCaseResultCommentCommand(ChangePlanCaseResultCommentCommand command) {
        log.info("ChangePlanCaseResultCommentCommand >>> {}", command);
        PlanCaseResultCommentChangedEvent event = tmTestPlanDomainConverter.convert(command);
        tmTestPlanRepository.changePlanCaseResultComment(event);
        event.setAggregateId(command.getAggregateId());
        apply(event);
    }

    public void editCommonTestPlanCommand(EditCommonTestPlanCommand command) {
        TmTestPlanVO vo = command.getTestPlanVO();
        if (Objects.isNull(vo)) {
            throw new ServiceException("参数异常");
        }
        CommonTestPlanEditedEvent event = new CommonTestPlanEditedEvent();
        event.setTransactor(command.getTransactor());
        event.setOccurred(new Date());
        event.setTestPlanVO(command.getTestPlanVO());
        event.setAggregateId(command.getAggregateId());
        tmTestPlanRepository.editTestPlan(vo, event);
        testPlanSender.handleCommonTestPlanEditedEvent(event);
        apply(event);
    }

    public void editMobileSpecialTestPlanCommand(EditMobileSpecialTestPlanCommand command) {
        TmTestPlanVO vo = command.getTestPlanVO();
        if (Objects.isNull(vo)) {
            throw new ServiceException("参数异常");
        }
        if (Objects.isNull(command.getTransactor())) {
            throw new ServiceException("登录人信息异常");
        }
        vo.setType(TestPlanNewTypeEnum.MOBILE_SPECIAL);
        vo.setTestStrategy(TestPlanStrategyEnum.NULL_TEST);
        vo.setPlanDirectorId(command.getTransactor().getUserId());
        vo.setPlanDirectorName(command.getTransactor().getUserName());
        MobileSpecialTestPlanEditedEvent event = new MobileSpecialTestPlanEditedEvent();
        event.setTransactor(command.getTransactor());
        event.setOccurred(new Date());
        event.setTestPlanVO(vo);
        event.setAggregateId(command.getAggregateId());
        tmTestPlanRepository.editTestPlan(vo, event);
        testPlanSender.handleMobileSpecialTestPlanEditedEvent(event);
        apply(event);
    }

    public void editSafeTestPlanCommand(EditSafeTestPlanCommand command) {
        TmTestPlanVO vo = command.getTestPlanVO();
        if (Objects.isNull(vo)) {
            throw new ServiceException("参数异常");
        }
        SafeTestPlanEditedEvent event = new SafeTestPlanEditedEvent();
        event.setTransactor(command.getTransactor());
        event.setOccurred(new Date());
        event.setTestPlanVO(command.getTestPlanVO());
        event.setAggregateId(command.getAggregateId());
        tmTestPlanRepository.editTestPlan(vo, event);
        apply(event);
        sendEditSafetyTestPlanMq(command);
    }

    private void sendEditSafetyTestPlanMq(EditSafeTestPlanCommand command) {
        TmTestPlanVO vo = command.getTestPlanVO();
        EditSafetyTestPlanEvent mq = new EditSafetyTestPlanEvent();
        if (StringUtil.isBlank(vo.getTestInformation())) {
            mq.setTestInformation("测试信息");
        }
        mq.setCode(command.getAggregateId());
        mq.setTestPlanCode(command.getAggregateId());
        mq.setPermissionsTest(vo.getPermissionsTest());
        mq.setPriority(vo.getPriority().name());
        mq.setLastTestDate(vo.getLastTestDate());
        mq.setTestInformation(vo.getTestInformation());
        mq.setPriorityTestInformation(vo.getPermissionsTestInformation());
        mq.setRecipients(Collections.emptyList());
        mq.setCcUsers(Collections.emptyList());
        mq.setProductCode(vo.getProductCode());
        mq.setProductName(vo.getProductName());
        mq.setVersionCode(vo.getVersionCode());
        mq.setVersionName(vo.getVersionName());
        mq.setPlanName(vo.getPlanName());
        mq.setTransactor(command.getTransactor());
        mq.setOccurred(new Date());
        mq.setStatus(TestPlanStatusEnum.NORMAL);
        log.info("同步消息EditSafetyTestPlanEvent ++++++==" + JSONObject.toJSONString((mq)));
        testPlanSender.handleEditSafetyTestPlanEvent(mq);
        mq.setAggregateId(command.getAggregateId());
        apply(mq);
    }

    public void initPlanStageStatus(DeploymentBackedEvent event) {
        log.info("initPlanStageStatus_event:{}", JsonUtil.toJSON(event));
        FlowBaseDetailVO resp = iPipelineRpcService.flowDetail(event.getCode());
        if (Objects.isNull(resp)) {
            log.error("initPlanStageStatus_resp_is_null_flowCode:{}!", event.getCode());
            return;
        }
        String versionCode = resp.getVersionCode();
        if (StringUtil.isEmpty(versionCode)) {
            log.error("initPlanStageStatus_versionCode_is_blank_flowCode:{}!", event.getCode());
            return;
        }
        TmTestPlanEntityDO planEntityDO = tmTestPlanRepository.getTestPlanByVersion(versionCode);
        if (Objects.isNull(planEntityDO)) {
            log.error("initPlanStageStatus_plan_is_null_flowCode:{}_versionCode:{}!", event.getCode(), versionCode);
            return;
        }

        //版本回退，覆盖率确认状态刷新
        tmTestPlanRepository.updateCheckFlag(planEntityDO.getCode(), null, Boolean.FALSE, event.getTransactor());

        if (Objects.isNull(planEntityDO.getTestStrategy())) {
            log.error("initPlanStageStatus_plan_testStrategy_is_null_versionCode :{}!", versionCode);
            return;
        }
        if (Objects.isNull(planEntityDO.getStatus()) || TestPlanNewStatusEnum.TERMINATED.equals(planEntityDO.getStatus())) {
            log.error("initPlanStageStatus_plan_is_terminated_versionCode :{}!", versionCode);
            return;
        }
        if (TestPlanStrategyEnum.STANDARD_TEST.equals(planEntityDO.getTestStrategy()) && MapUtils.isNotEmpty(planEntityDO.getStageStatus())) {
            Map<String, Object> stageStatus = new HashMap<>();
            stageStatus.put(TestPlanStageEnum.SMOKE_TEST.name(), TestPlanStageStatusEnum.IN_PROGRESS);
            stageStatus.put(TestPlanStageEnum.FUNCTIONAL_TEST.name(), TestPlanStageStatusEnum.INITIAL);
            stageStatus.put(TestPlanStageEnum.ONLINE_SMOKE_TEST.name(), TestPlanStageStatusEnum.INITIAL);
            TmTestPlanEntityDO toUpdate = new TmTestPlanEntityDO();
            toUpdate.setStatus(TestPlanNewStatusEnum.IN_PROGRESS);
            toUpdate.setStageStatus(stageStatus);
            toUpdate.preUpdate(event);
            tmTestPlanRepository.updateStageStatusByPrimaryKey(toUpdate, planEntityDO.getCode());
            return;
        }
        if (Arrays.asList(TestPlanStrategyEnum.SIMPLE_TEST, TestPlanStrategyEnum.EXPLORE_TEST).contains(planEntityDO.getTestStrategy())
                && TestPlanNewStatusEnum.COMPLETED.equals(planEntityDO.getStatus())) {
            TmTestPlanEntityDO toUpdate = new TmTestPlanEntityDO();
            toUpdate.setStatus(TestPlanNewStatusEnum.IN_PROGRESS);
            toUpdate.preUpdate(event);
            tmTestPlanRepository.updateStageStatusByPrimaryKey(toUpdate, planEntityDO.getCode());
        }
    }

    public void doSaveDelayAcceptVersion(DeploymentDelayAcceptedEvent event) {
        log.info("DeploymentDelayAcceptedEvent_doSaveDelayAcceptVersion_event:{}", event);
        if (Objects.isNull(event) || !FlowStatusEnum.DELAY_ACCEPTING.equals(event.getStatus())) {
            return;
        }
        TmDelayAcceptRecordEntityDO localEntity = tmDelayAcceptRecordRepository.selectByFlowCode(event.getCode());
        if (Objects.nonNull(localEntity)) {
            return;
        }
        FlowBaseDetailVO resp = iPipelineRpcService.flowDetail(event.getCode());
        if (Objects.isNull(resp)) {
            return;
        }
        TmDelayAcceptRecordEntityDO entityDO = new TmDelayAcceptRecordEntityDO();
        entityDO.setFlowCode(resp.getFlowCode());
        entityDO.setProductCode(resp.getProductCode());
        entityDO.setVersionCode(resp.getVersionCode());
        entityDO.setVersionName(resp.getVersionName());
        entityDO.setAcceptRemark(event.getAcceptRemark());
        entityDO.setIsSend(Boolean.FALSE);
        entityDO.setEnable(Boolean.TRUE);
        entityDO.preCreate(event.transactor());
        entityDO.setGmtCreate(event.occurred());
        buildUserCode(resp.getProductCode(), event.getTransactor().getUserId(), entityDO);
        tmDelayAcceptRecordRepository.insert(entityDO);
    }

    private void buildUserCode(String productCode, Long operatorId, TmDelayAcceptRecordEntityDO entityDO) {
        Set<Long> userIds = new HashSet<>();
        if (Objects.nonNull(operatorId)) {
            userIds.add(operatorId);
        }
        List<ProductMemberVO> productMemberVOS = productRpcService.getProductMember(productCode, Arrays.asList(MemberTypeEnum.TESTER_M.name(), MemberTypeEnum.TESTER_OWNER.name()));
        if (CollectionUtil.isNotEmpty(productMemberVOS)) {
            userIds.addAll(productMemberVOS.stream().map(ProductMemberVO::getUserId).collect(Collectors.toSet()));
        }
        if (CollectionUtil.isEmpty(userIds)) {
            return;
        }
        entityDO.setApprover(String.join(",", transferUserId(new ArrayList<>(userIds))));
    }

    public void doUpdateAcceptedVersion(DeploymentAcceptedReportEvent event) {
        if (Objects.isNull(event) || !FlowStatusEnum.ACCEPTED.equals(event.getStatus())) {
            log.info("DeploymentAcceptedReportEvent_doUpdateAcceptedVersion_event:{}", event);
            return;
        }
        //修改待办状态为已完成
        TmDelayAcceptRecordEntityDO entityDO = tmDelayAcceptRecordRepository.selectByFlowCode(event.getCode());
        if (Objects.isNull(entityDO)) {
            log.info("DeploymentAcceptedReportEvent_doUpdateAcceptedVersion_entity_is_null");
            return;
        }
        if (StringUtil.isBlank(entityDO.getAuditFlowCode())) {
            log.info("DeploymentAcceptedReportEvent_doUpdateAcceptedVersion_no_audit:{}", entityDO.getFlowCode());
            tmDelayAcceptRecordRepository.updateEnableByFlowCode(event.getCode());
            return;
        }
        User handler = Objects.isNull(event.transactor()) ? new User(0L, "system") : event.getTransactor();
        AuditResp resp = doSendCloseTaskRequest(entityDO.getAuditFlowCode(), entityDO.getProductCode(), entityDO.getVersionName(), handler, event.occurred());
        if (resp.isStatus()) {
            tmDelayAcceptRecordRepository.updateEnableByFlowCode(event.getCode());
        } else {
            log.error("taskBaseUpdate_error:{}", JSON.toJSON(resp));
        }
    }

    private AuditResp doSendCloseTaskRequest(String auditFlowCode, String productCode, String versionName, User operator, Date occurred) {
        TaskBaseUpdateRequest request = new TaskBaseUpdateRequest();
        request.setAuditFlowCode(auditFlowCode);
        request.setHandlerCodes(transferUserId(Collections.singletonList(operator.getUserId())));
        request.setHandleDate(occurred);
        request.setTaskStatus(TaskStatusEnum.DONE);
        request.setProcessEnd(Boolean.TRUE);
        request.setUserType(UserInfoTypeEnum.ZTO_ZHONGTIAN_SYSTEM);
        request.setOperator(operator);
        request.setBizType(qcConfigBasicService.getDelayAcceptAuditBizType());
        request.setPcURL(String.format(qcConfigBasicService.getDelayAcceptAuditDoneSkipUrl(), productCode, versionName));
        return taskService.taskBaseUpdate(request);
    }

    /**
     * 置换员工编号
     *
     * @param userIds
     * @return
     */
    private List<String> transferUserId(List<Long> userIds) {
        if (CollectionUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        ListUserQuery query = new ListUserQuery();
        query.setSsoUserIdList(userIds);
        List<UserSelectVO> users = userRpcService.listUserQuery(query);
        if (CollectionUtil.isEmpty(users)) {
            return new ArrayList<>();
        }
        return users.stream().map(UserSelectVO::getUserCode).distinct().collect(Collectors.toList());
    }

    public void sendAudit() {
        //查待发送版本明细
        List<TmDelayAcceptRecordEntityDO> doList = tmDelayAcceptRecordRepository.selectListBySendFlag(Boolean.FALSE);
        if (CollectionUtil.isEmpty(doList)) {
            return;
        }

        List<String> productCodeList = doList.stream().map(TmDelayAcceptRecordEntityDO::getProductCode).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(productCodeList)) {
            return;
        }

        SimpleProductQueryListVO simpleProductQueryListVO = productRpcService.simpleProductQueryList(productCodeList);
        List<SimpleQueryVO> simpleQueryVOlist = simpleProductQueryListVO.getSimpleQueryVOlist();
        if (CollectionUtil.isEmpty(simpleQueryVOlist)) {
            return;
        }

        Map<String, String> productMap = simpleQueryVOlist.stream()
                .collect(Collectors.toMap(
                        SimpleQueryVO::getProductCode,
                        SimpleQueryVO::getProductName,
                        (existing, replacement) -> existing
                ));

        List<String> failCodeList = new ArrayList<>();
        //创建待办任务
        doList.forEach(item -> {
            com.zto.devops.framework.client.audit.request.TaskCreateRequest request = new TaskCreateRequest();
            request.setProductCode(item.getProductCode());
            request.setBusinessCode(item.getFlowCode());
            request.setBusinessType(BizTypeEnum.DELAY_ACCEPT);
            request.setBizType(qcConfigBasicService.getDelayAcceptAuditBizType());
            request.setBizTypeName(qcConfigBasicService.getDelayAcceptAuditBizName());
            request.setPcURL(String.format(qcConfigBasicService.getDelayAcceptAuditSkipUrl(), item.getProductCode(), item.getFlowCode()));
            request.setBizTitle(String.format(productMap.get(item.getProductCode()) + qcConfigBasicService.getDelayAcceptAuditTitle(), item.getVersionName()));
            request.setTaskType(TaskTypeEnum.NORMAL);
            request.setSubmitTime(new Date());
            request.setSubmitterCode(qcConfigBasicService.getDelayAcceptAuditSubmitterCode());
            request.setSubmitterName(qcConfigBasicService.getDelayAcceptAuditSubmitterName());
            request.setEmergencyLevel(TaskEmergencyLevelEnum.NORMAL);
            request.setUserType(UserInfoTypeEnum.ZTO_ZHONGTIAN_SYSTEM);
            request.setRecevieDate(new Date());
            request.setAssigneesCode(Arrays.asList(item.getApprover().split(",")));
            request.setOperator(new User(item.getCreatorId(), item.getCreator()));
            AuditResp resp = taskService.taskCreate(request);
            if (resp.isStatus()) {
                TmDelayAcceptRecordEntityDO toUpdateDO = TmDelayAcceptRecordEntityDO.buildUpdate(item.getFlowCode(), Boolean.TRUE, resp.getData());
                tmDelayAcceptRecordRepository.updateSendSuccess(toUpdateDO);
            } else {
                log.error("taskCreate_error:{}", JSON.toJSON(resp));
                failCodeList.add(item.getFlowCode());
            }
        });
        //根据任务结果，更新待发送状态
        tmDelayAcceptRecordRepository.updateIsSendByFlowCodeList(failCodeList, Boolean.FALSE);
    }

    public void closeDelayAcceptAudit(List<String> versionCodeList) {
        //需要关闭任务的版本
        if (CollectionUtil.isEmpty(versionCodeList)) {
            List<TmDelayAcceptRecordEntityDO> doList = tmDelayAcceptRecordRepository.selectListBySendFlag(Boolean.TRUE);
            if (CollectionUtil.isEmpty(doList)) {
                return;
            }
            versionCodeList = doList.stream().map(TmDelayAcceptRecordEntityDO::getVersionCode).filter(StringUtil::isNotBlank).collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(versionCodeList)) {
            log.info("closeDelayAcceptAudit_versionCodeList_is_null");
            return;
        }

        //版本详细信息
        SimpleVersionListQuery query = new SimpleVersionListQuery();
        query.setCode(versionCodeList);
        SimpleListVersionVO simpleListVersionVO = iProjectRpcService.simpleVersionListQuery(query);
        if (Objects.isNull(simpleListVersionVO) || CollectionUtil.isEmpty(simpleListVersionVO.getSimpleVersionVOList())) {
            log.info("closeDelayAcceptAudit_simpleListVersionVO_is_null");
            return;
        }
        List<SimpleVersionVO> acceptedVersionList = simpleListVersionVO.getSimpleVersionVOList()
                .stream().filter(item -> (FlowStatusEnum.ACCEPTED.equals(item.getStatus())))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(acceptedVersionList)) {
            log.info("closeDelayAcceptAudit_acceptedVersionList_is_null");
            return;
        }
        List<String> acceptedVersionCodeList = acceptedVersionList.stream().map(SimpleVersionVO::getCode).filter(StringUtil::isNotBlank).collect(Collectors.toList());

        //需要关闭的工单信息
        List<TmDelayAcceptRecordEntityDO> toCloseList = tmDelayAcceptRecordRepository.selectByVersionCodeList(acceptedVersionCodeList);
        if (CollectionUtil.isEmpty(toCloseList)) {
            log.info("closeDelayAcceptAudit_toCloseList_is_null");
            return;
        }

        //关闭工单
        Map<String, List<TmDelayAcceptRecordEntityDO>> verisonCodeMap = toCloseList.stream().collect(Collectors.groupingBy(TmDelayAcceptRecordEntityDO::getVersionCode));
        for (SimpleVersionVO version : acceptedVersionList) {
            if (verisonCodeMap.containsKey(version.getCode())) {
                List<TmDelayAcceptRecordEntityDO> toClose = verisonCodeMap.get(version.getCode());
                if (CollectionUtil.isEmpty(toClose) || Objects.isNull(toClose.get(0))) {
                    continue;
                }
                if (StringUtil.isBlank(toClose.get(0).getAuditFlowCode())) {
                    tmDelayAcceptRecordRepository.updateEnableByFlowCode(toClose.get(0).getFlowCode());
                    continue;
                }
                AuditResp resp = doSendCloseTaskRequest(toClose.get(0).getAuditFlowCode(), version.getProductCode(), version.getCode(),
                        new User(0L, "system"), new Date());
                if (resp.isStatus()) {
                    tmDelayAcceptRecordRepository.updateEnableByFlowCode(toClose.get(0).getFlowCode());
                } else {
                    log.error("closeDelayAcceptAudit_error:{}", JSON.toJSON(resp));
                }
            }
        }
    }
}
