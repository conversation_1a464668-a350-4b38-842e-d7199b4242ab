package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindTagTestcaseNoQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindTestcaseTagQuery;
import com.zto.devops.qc.domain.gateway.repository.TagRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class TagQueryDomainService {

    @Autowired
    private TagRepository tagRepository;

    public List<TagVO> listTagsByBusinessCodeQuery(String businessCode) {
        log.info("listTagsByBusinessCodeQuery >>> {}", businessCode);
        if (StringUtils.isEmpty(businessCode)) {
            return Collections.emptyList();
        }
        return tagRepository.listTagsByBusinessCode(businessCode);
    }

    public TagVO findTestcaseTagQuery(FindTestcaseTagQuery query) {
        log.info("FindTestcaseTagQuery >>> {}", query.getCode());
        return tagRepository.findTestcaseTagQuery(query.getCode());
    }

    public Long findTagTestcaseNoQuery(FindTagTestcaseNoQuery query) {
        log.info("FindTagTestcaseNoQuery >>> {}", query);
        return tagRepository.tagTestcaseNo(query.getTagName(), query.getDomain().name(), query.getProductCode());
    }

    public List<TagVO> listBusinessTagWithFixVersionCode(String versionCode){
        return tagRepository.queryBusinessTagWithFixVersionCode(versionCode);
    }

    public List<TagVO> listTagsByBusinessCodeAndDomainQuery(String businessCode, DomainEnum domain) {
        log.info("listTagsByBusinessCodeAndDomainQuery >>> {},{}", businessCode,domain);
        if (StringUtils.isEmpty(businessCode)) {
            return Collections.emptyList();
        }
        return tagRepository.listTagsByBusinessCodeAndDomain(businessCode,domain);
    }


}
