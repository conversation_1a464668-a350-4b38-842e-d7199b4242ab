package com.zto.devops.qc.domain.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 项目名称：qc-parent
 * 类 名 称：Column
 * 类 描 述：TODO
 * 创建时间：2021/11/12 2:46 下午
 * 创 建 人：bulecat
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface Column {
     String desc() default "";
}
