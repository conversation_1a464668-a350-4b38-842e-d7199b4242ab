package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.simple.LockSeal;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.gateway.util.LockService;
import com.zto.devops.qc.client.enums.constants.LockStoreEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTestEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneTagEnum;
import com.zto.devops.qc.client.model.dto.ApiTestEntityDO;
import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.client.model.rpc.pipeline.ApplicationResp;
import com.zto.devops.qc.client.model.rpc.pipeline.FlowBranchVO;
import com.zto.devops.qc.client.model.rpc.pipeline.PageApplicationVO;
import com.zto.devops.qc.client.model.rpc.pipeline.query.PageApplicationQuery;
import com.zto.devops.qc.client.model.rpc.project.VersionVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.MetaDataDocMsgVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.metadata.MetaDataDocHttpVO;
import com.zto.devops.qc.domain.gateway.metadata.MetaDataService;
import com.zto.devops.qc.domain.gateway.repository.ApiTestRepository;
import com.zto.devops.qc.domain.gateway.repository.TagRepository;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.domain.util.GenerateApiCaseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ApiTestDocDataService {

    @Autowired
    private MetaDataService metaDataService;

    @Autowired
    private IPipelineRpcService pipelineRpcService;

    @Autowired
    private ApiTestRepository apiTestRepository;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private TagRepository tagRepository;

    @Autowired
    private IProjectRpcService projectRpcService;

    @Autowired
    private LockService lockService;

    public void syncApiMetaDataByProduct(String productCode, List<ApiTypeEnum> typeList) {
        log.info("syncApiMetaDataByProduct >>> {} {}", productCode, typeList);
        try {
            List<MetaDataDocHttpVO> docList = typeList.stream().map(type -> {
                if (ApiTypeEnum.HTTP.equals(type)) {
                    return metaDataService.getMetaDataDocHttp(productCode);
                }
                if (ApiTypeEnum.DUBBO.equals(type)) {
                    return metaDataService.getMetaDataDocDubbo(productCode);
                }
                return null;
            }).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(docList)) {
                log.warn("获取api文档失败！ [{}]", productCode);
                return;
            }
            List<String> appIds = Optional.ofNullable(pipelineRpcService.getApplicationList(productCode))
                    .orElse(Collections.emptyList()).stream()
                    .map(ApplicationResp::getAppId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(appIds)) {
                log.warn("获取appId列表为空！ [{}]", productCode);
                return;
            }
            String docProductCode = metaDataService.getMetaDataProductCode(productCode);
            List<ApiTestEntityDO> apiTestList = apiTestRepository.queryApiTestByProductAndType(productCode, typeList);
            Map<Long, ApiTestEntityDO> docIdMap = apiTestList.stream()
                    .collect(Collectors.toMap(ApiTestEntityDO::getDocId, i -> i));
            Map<String, String> versionMap = apiTestList.stream()
                    .filter(i -> StringUtils.isNotEmpty(i.getMainApiCode()))
                    .collect(Collectors.toMap(i -> i.getApiType().name() + i.getApiAddress() + i.getGatewayApiInfo(),
                            ApiTestEntityDO::getMainApiCode, (i1, i2) -> i1));
            apiTestRepository.deleteApiTestByProductAndType(productCode, typeList);
            docList.stream().filter(doc -> {
                if (StringUtils.isNotEmpty(docProductCode) && StringUtils.isNotEmpty(doc.getMockUrl())) {
                    String[] arr = doc.getMockUrl().split("/");
                    if (doc.getMockUrl().startsWith("/") && arr.length > 2) {
                        return docProductCode.equals(arr[2]);
                    }
                    if (!doc.getMockUrl().startsWith("/") && arr.length > 1) {
                        return docProductCode.equals(arr[1]);
                    }
                }
                return true;
            }).forEach(doc -> {
                try {
                    MetaDataDocHttpVO detail = metaDataService.getMetaDataDocDetail(doc.getId());
                    if (null != detail) {
                        doc = detail;
                    }
                    ApiTestEntityDO data = GenerateApiCaseUtil.convertMetaDataDocToApiTest(doc);
                    if (ApiTestEnableEnum.DELETED.equals(data.getEnable())) {
                        log.warn("全量同步跳过已删除接口！[{}]", doc.getId());
                        return;
                    }
                    if (StringUtils.isNotEmpty(data.getAppId()) && !appIds.contains(data.getAppId())) {
                        log.warn("产品[{}]和appId[{}]不匹配！[{}]", productCode, data.getAppId(), doc.getId());
                        return;
                    }
                    if (docIdMap.containsKey(data.getDocId())) {
                        data.setApiCode(docIdMap.get(data.getDocId()).getApiCode());
                        data.setMainApiCode(docIdMap.get(data.getDocId()).getMainApiCode());
                    } else {
                        data.setApiCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                    }
                    if (StringUtils.isEmpty(data.getMainApiCode())) {
                        String versionKey = data.getApiType().name()  + data.getApiAddress() + data.getGatewayApiInfo();
                        if (versionMap.containsKey(versionKey)) {
                            data.setMainApiCode(versionMap.get(versionKey));
                        } else {
                            data.setMainApiCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                            versionMap.put(versionKey, data.getMainApiCode());
                        }
                    }
                    data.setProductCode(productCode);
                    data.setDocProductCode(StringUtils.defaultString(docProductCode));
                    apiTestRepository.insertApiTest(data);
                    apiTestRepository.refreshApiTestModifyFlag(data.getMainApiCode());
                } catch (Exception e) {
                    log.error("同步接口文档失败！[{}]", doc.getId(), e);
                }
            });
        } catch (Exception e) {
            log.error("产品接口文档同步失败！[{}]", productCode, e);
        }
    }

    public void updateApiMetaData(MetaDataDocMsgVO metaDataDocMsg) {
        log.info("updateApiMetaData >>> {}", metaDataDocMsg);
        if ("delete".equals(metaDataDocMsg.getOpt())) {
            List<ApiTestEntityDO> docList = apiTestRepository.getApiTestByDocId(metaDataDocMsg.getId(), null);
            if (CollectionUtils.isEmpty(docList)) {
                log.warn("未找到对应docId数据！ [{}]", metaDataDocMsg.getId());
                return;
            }
            docList.forEach(apiTest -> {
                if (Boolean.TRUE.equals(metaDataDocMsg.getAllVersion())) {
                    apiTestRepository.deleteApiTestByMainApiCode(apiTest.getMainApiCode());
                    apiTestRepository.deleteApiCaseByApiCode(apiTest.getMainApiCode());
                    syncSceneByApiTest(apiTest.getMainApiCode());
                } else {
                    apiTestRepository.deleteApiTest(apiTest);
                    boolean result = apiTestRepository.refreshApiTestModifyFlag(apiTest.getMainApiCode());
                    if (!result) {
                        apiTestRepository.deleteApiCaseByApiCode(apiTest.getMainApiCode());
                        syncSceneByApiTest(apiTest.getMainApiCode());
                    }
                }
            });
            return;
        }
        MetaDataDocHttpVO doc = metaDataService.getMetaDataDocDetail(metaDataDocMsg.getId());
        if (null == doc) {
            log.warn("获取docId文档失败！ [{}]", metaDataDocMsg.getId());
            return;
        }
        if (StringUtils.isEmpty(doc.getProductCode())) {
            log.warn("docId文档获取产品失败！[{}]", doc.getId());
            return;
        }
        String[] array = StringUtils.split(doc.getProductCode(), ",");
        Arrays.stream(array).distinct().forEach(docProductCode -> {
            String productCode = metaDataService.getLubanProductCode(docProductCode);
            if (StringUtils.isEmpty(productCode)) {
                log.warn("未查询到对应一站式产品code！[{}]", docProductCode);
                return;
            }
            ApiTestEntityDO data = GenerateApiCaseUtil.convertMetaDataDocToApiTest(doc);
            if (StringUtils.isNotEmpty(data.getAppId())) {
                PageApplicationQuery query = new PageApplicationQuery();
                query.setProductCode(productCode);
                query.setAppId(data.getAppId());
                query.setNeedPermissions(Boolean.FALSE);
                PageApplicationVO vo = pipelineRpcService.getPageApplicationVO(query);
                if (null == vo || CollectionUtils.isEmpty(vo.getList())) {
                    log.warn("产品[{}]和appId[{}]不匹配！", query.getProductCode(), query.getAppId());
                    return;
                }
            }
            data.setProductCode(productCode);
            data.setDocProductCode(docProductCode);
            LockSeal lockSeal = null;
            String versionKey =
                    data.getProductCode() + data.getApiType().name() + data.getApiAddress() + data.getGatewayApiInfo();
            try {
                do {
                    lockSeal = lockService.acquireLock(LockStoreEnum.DOC_CONSUME.getValue(),
                            Collections.singletonList(versionKey), 30000L);
                    if (null == lockSeal) {
                        Thread.sleep(1000L);
                    }
                } while (lockSeal == null);
                List<ApiTestEntityDO> docList = apiTestRepository.getApiTestByDocId(data.getDocId(), data.getProductCode());
                if (CollectionUtils.isEmpty(docList)) {
                    data.setApiCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                    List<ApiTestEntityDO> allVersion = apiTestRepository.getAllVersionApiTest(data);
                    if (CollectionUtils.isNotEmpty(allVersion)) {
                        data.setMainApiCode(allVersion.get(0).getMainApiCode());
                    } else {
                        data.setMainApiCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                        data.setModifyFlag(1);
                    }
                    apiTestRepository.insertApiTest(data);
                } else {
                    data.setApiCode(docList.get(0).getApiCode());
                    data.setMainApiCode(docList.get(0).getMainApiCode());
                    if ("web".equals(metaDataDocMsg.getSource())) {
                        data.setEnable(docList.get(0).getEnable());
                    }
                    apiTestRepository.updateApiTest(data);
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } finally {
                lockService.releaseLock(lockSeal);
            }
            if (null == data.getModifyFlag() && StringUtils.isNotEmpty(data.getMainApiCode())) {
                apiTestRepository.refreshApiTestModifyFlag(data.getMainApiCode());
                syncApiCaseByApiTest(data.getMainApiCode());
                syncSceneByApiTest(data.getMainApiCode());
            }
        });
    }

    private void syncSceneByApiTest(String apiCode) {
        try {
            List<SceneInfoEntityDO> sceneList = apiTestRepository.querySceneByProductCodeAndSceneType(null, null);
            if (CollectionUtils.isEmpty(sceneList)) {
                return;
            }
            sceneList.forEach(scene -> {
                SceneInfoEntityDO edit = apiTestRepository.queryLatestSceneInfo(scene.getSceneCode(), SceneInfoStatusEnum.edit);
                boolean apiUpdated = checkSceneNodeApiUpdated(edit, apiCode);
                SceneInfoEntityDO publish = apiTestRepository.queryLatestSceneInfo(scene.getSceneCode(), SceneInfoStatusEnum.publish);
                apiUpdated = checkSceneNodeApiUpdated(publish, apiCode) || apiUpdated;
                if (apiUpdated) {
                    tagRepository.saveSceneTag(scene.getSceneCode(), SceneTagEnum.API_UPDATE);
                }
            });
        } catch (Exception e) {
            log.error("syncSceneByApiTest {}", apiCode, e);
        }
    }

    private boolean checkSceneNodeApiUpdated(SceneInfoEntityDO sceneInfo, String apiCode) {
        JSONObject jsonObject = Optional.ofNullable(sceneInfo)
                .map(SceneInfoEntityDO::getSceneBackData)
                .map(JSONObject::parseObject)
                .orElse(null);
        if (null == jsonObject) {
            return false;
        }
        List<String> updateNodeList = new ArrayList<>();
        jsonObject.getJSONObject("nodes").keySet().forEach(key -> {
            JSONObject node = jsonObject.getJSONObject("nodes").getJSONObject(key);
            if (apiCode.equals(node.getString("apiCode"))) {
                updateNodeList.add(key);
            }
        });
        boolean apiUpdated = !updateNodeList.isEmpty();
        if (apiUpdated) {
            jsonObject.put("updateNodeList", updateNodeList);
            SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
            entityDO.setId(sceneInfo.getId());
            entityDO.setSceneBackData(jsonObject.toJSONString());
            entityDO.setGmtModified(sceneInfo.getGmtModified());
            apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
        }
        return apiUpdated;
    }

    private void syncApiCaseByApiTest(String apiCode) {
        try {
            List<String> caseList = apiTestRepository.queryApiCaseCodeByApiCode(apiCode);
            if (CollectionUtils.isEmpty(caseList)) {
                return;
            }
            apiTestRepository.updateApiTestTagByMainApiCode(apiCode, SceneTagEnum.API_UPDATE.name());
            caseList.forEach(caseCode ->
                    apiTestRepository.addApiTestCaseTag(caseCode, SceneTagEnum.API_UPDATE, new User(0L, "系统")));
            apiTestRepository.updateApiCaseRelatedByApiCode(apiCode);
        } catch (Exception e) {
            log.error("syncApiCaseByApiTest {}", apiCode, e);
        }
    }

    public void archiveApiTestVersion(String versionCode) {
        log.info("archiveApiTestVersion >>> {}", versionCode);
        if (StringUtils.isBlank(versionCode)) {
            return;
        }
        VersionVO versionVO = projectRpcService.findVersionInfoQuery(versionCode);
        if (versionVO == null || !"ACCEPTED".equals(versionVO.getStatus())) {
            log.warn("版本状态非【已验收】不做处理！{}", versionVO);
            return;
        }
        FlowBranchVO flowBranchVO = pipelineRpcService.queryBranchByVersionCode(versionCode);
        if (null == flowBranchVO) {
            log.warn("未查询到版本对应分支！{}", versionVO);
            return;
        }
        List<String> branches = new ArrayList<>();
        if (StringUtils.isNotBlank(flowBranchVO.getFeatureBranch())
                && !"master".equals(flowBranchVO.getFeatureBranch())) {
            branches.add(flowBranchVO.getFeatureBranch());
        }
        if (StringUtils.isNotBlank(flowBranchVO.getReleaseBranch())
                && !"master".equals(flowBranchVO.getReleaseBranch())) {
            branches.add(flowBranchVO.getReleaseBranch());
        }
        branches.forEach(branchName -> {
            log.info("归档接口版本 >>> {}", branchName);
            List<ApiTestEntityDO> apiTestList = apiTestRepository.queryApiTestByDocVersion(branchName);
            if (CollectionUtils.isEmpty(apiTestList)) {
                return;
            }
            apiTestList.forEach(apiTest -> {
                apiTest.setEnable(ApiTestEnableEnum.ARCHIVED);
                apiTestRepository.updateApiTest(apiTest);
            });
        });
    }
}
