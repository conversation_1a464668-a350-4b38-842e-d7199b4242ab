package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.model.issue.command.AddAttachmentCommand;
import com.zto.devops.qc.client.model.issue.command.RemoveAttachmentCommand;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.event.AttachmentAddedEvent;
import com.zto.devops.qc.client.model.issue.event.AttachmentRemovedSimpleEvent;
import com.zto.devops.qc.client.model.issue.query.FindAttachmentByCodeQuery;
import com.zto.devops.qc.domain.converter.AttachmentDomainConverter;
import com.zto.devops.qc.domain.gateway.repository.AttachmentRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AttachmentCommandDomainService extends BaseDomainService {

    @Autowired
    private AttachmentRepository attachmentRepository;

    @Autowired
    private AttachmentQueryDomainService attachmentQueryDomainService;

    private final static Integer ATTACHMENT_COUNT = 100;

    private final static AttachmentDomainConverter ATTACHMENT_DOMAIN_CONVERTER = AttachmentDomainConverter.INSTANCE;


    public void handle(AddAttachmentCommand command) {
        List<AttachmentVO> attachments = attachmentRepository.findListByBusinessCode(command.getAggregateId());
        AttachmentVO attachmentVO = ATTACHMENT_DOMAIN_CONVERTER.convert(command);
        if (null != attachments) {
            List<AttachmentVO> attachmentValidList = attachments.stream().filter(i -> DomainEnum.ISSUE.equals(i.getDomain())).collect(Collectors.toList());
            if (attachmentValidList.size() > ATTACHMENT_COUNT) {
                throw new ServiceException("附件最多可上传100个");
            }
        }
        List<AttachmentVO> attachmentVOSet = new ArrayList<>();
        attachmentVOSet.add(attachmentVO);
        AttachmentAddedEvent attachmentAddedEvent = new AttachmentAddedEvent();
        attachmentAddedEvent.setAggregateId(command.getAggregateId());
        attachmentAddedEvent.setBusinessCode(command.getAggregateId());
        attachmentAddedEvent.setAttachments(attachmentVOSet);
        attachmentAddedEvent.setTransactor(command.getTransactor());
        attachmentAddedEvent.setOccurred(new Date());
        log.info("----------------AddAttachmentCommand.getCode(): {}", command.getCode());
        attachmentRepository.handle(attachmentAddedEvent);
        apply(attachmentAddedEvent);
    }


    public void handle(RemoveAttachmentCommand command) {
        log.info("RemoveAttachmentCommand >>> {}", JSON.toJSONString(command));
        AttachmentRemovedSimpleEvent event = new AttachmentRemovedSimpleEvent();
        FindAttachmentByCodeQuery query = new FindAttachmentByCodeQuery();
        query.setCode(command.getCode());
        AttachmentVO attachment = attachmentQueryDomainService.query(query);
        if (attachment != null) {
            event.setAttachmentName(attachment.getName());
        }
        event.setAggregateId(command.getAggregateId());
        event.setCode(command.getCode());
        event.setOccurred(new Date());
        event.setTransactor(command.getTransactor());
        attachmentRepository.handle(event);
        apply(event);
    }


}
