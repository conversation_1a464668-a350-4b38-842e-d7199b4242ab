package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.quality.VersionQualityMetricTypeEnum;
import com.zto.devops.qc.client.model.dto.QcVersionQualityMetricsEntityDO;

import java.util.List;

public interface QcVersionQualityMetricsRepository {
    void insertSelective(QcVersionQualityMetricsEntityDO item);

    void batchDeleteByVersionCodeAndTypes(String versionCode, List<VersionQualityMetricTypeEnum> types, User user) ;
}
