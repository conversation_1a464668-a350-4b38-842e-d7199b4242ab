package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.report.ReportButtonEnum;
import com.zto.devops.qc.client.enums.report.SecurityLevel;
import com.zto.devops.qc.client.enums.report.SecurityStatus;
import com.zto.devops.qc.client.enums.report.TestResultEunm;
import com.zto.devops.qc.client.enums.rpc.MemberTypeEnum;
import com.zto.devops.qc.client.enums.rpc.NoticeUserTypeEnum;
import com.zto.devops.qc.client.enums.rpc.ProductSourceEnum;
import com.zto.devops.qc.client.enums.testPlan.PlanButtonEnum;
import com.zto.devops.qc.client.enums.testPlan.TestPlanEditEnum;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.enums.testPlan.TestPlanTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanDatePartitionEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.enums.testmanager.report.*;
import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.report.entity.*;
import com.zto.devops.qc.client.model.report.query.FindReportCaseStatisticVOSQuery;
import com.zto.devops.qc.client.model.report.query.ListReportDateByVersionCodesQuery;
import com.zto.devops.qc.client.model.report.query.PageReportQuery;
import com.zto.devops.qc.client.model.report.query.ReportTestResultQuery;
import com.zto.devops.qc.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.qc.client.model.rpc.product.SimpleQueryVO;
import com.zto.devops.qc.client.model.rpc.project.*;
import com.zto.devops.qc.client.model.rpc.user.ListUserQuery;
import com.zto.devops.qc.client.model.rpc.user.UserSelectVO;
import com.zto.devops.qc.client.model.testPlan.query.*;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanRangeVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.query.FindTmTestPlanQuery;
import com.zto.devops.qc.client.model.testmanager.plan.query.PlanListQuery;
import com.zto.devops.qc.client.model.testmanager.report.entity.BaseReportInfoVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ExternalTestReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.MobileTestReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewInfoUserHandleVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewUserVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.SimpleTestReportDetailVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.*;
import com.zto.devops.qc.client.model.testmanager.report.query.*;
import com.zto.devops.qc.client.service.report.model.AllReportDateResp;
import com.zto.devops.qc.client.service.report.model.SecurityTestResultResp;
import com.zto.devops.qc.domain.converter.TmTestPlanDomainConverter;
import com.zto.devops.qc.domain.converter.TmTestReportDomainConverter;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.report.SecurityTestReportService;
import com.zto.devops.qc.domain.gateway.repository.*;
import com.zto.devops.qc.domain.gateway.rpc.*;
import com.zto.devops.qc.domain.gateway.statemachine.TmStateMachineExtendService;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TmTestReportQueryDomainService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private IZUIRpcService izuiRpcService;

    @Autowired
    private IPipelineRpcService iPipelineRpcService;

    @Autowired
    private QcNoticeResultRepository qcNoticeResultRepository;

    @Autowired
    private TestFunctionPointRepository testFunctionPointRepository;

    @Autowired
    private ITmTestReportRepository iTmTestReportRepository;

    @Autowired
    private CaseStatisticsRepository caseStatisticsRepository;

    @Autowired
    private IProjectRpcService iProjectRpcService;

    @Autowired
    private TmTestPlanDomainConverter tmTestPlanDomainConverter;

    @Autowired
    private TmTestPlanQueryDomainService tmTestPlanQueryDomainService;

    @Autowired
    private ITmTestReportRepository tmTestReportRepository;

    @Autowired
    private ITmTestPlanRepository tmTestPlanRepository;

    @Autowired
    private TestReportRepository oldTestReportRepository;

    @Autowired
    private IEmailRepository emailRepository;

    @Autowired
    private TmTestReportDomainConverter tmTestReportDomainConverter;

    @Autowired
    private ITmTestPlanCaseRepository tmTestPlanCaseRepository;

    @Autowired
    private AttachmentRepository attachmentRepository;

    @Autowired
    private IProjectRpcService projectRpcService;

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private TmStateMachineExtendService tmStateMachineExtendService;

    @Autowired
    private IUserRpcService userRpcService;

    @Autowired
    private SecurityTestReportService securityTestReportService;

    @Autowired
    private ITmDelayAcceptRecordRepository tmDelayAcceptRecordlRepository;

    public TmAccessReportDetailVO detailAccessReport(AccessReportDetailQuery query) {
        TmAccessReportDetailVO result = new TmAccessReportDetailVO();
        if (Objects.isNull(query)) {
            return result;
        }

        //查询报告信息
        TmTestReportEntityDO report = null;
        if (StringUtils.isNotBlank(query.getReportCode())) {
            report = tmTestReportRepository.getReportByReportCode(query.getReportCode());
        } else {
            report = tmTestReportRepository.getReportByPlanCodeAndReportType(query.getPlanCode(), ReportType.TEST_ACCESS);
        }

        //数据转化
        if (!Objects.isNull(report)) {
            result = tmTestReportDomainConverter.convertAccess(report);
        } else if (StringUtils.isNotBlank(query.getPlanCode())) {
            result.setPlanCode(query.getPlanCode());
        }

        //设置默认参数
        result.buildSelf(query);

        //组装基础信息
        getBaseInfo(result);
        return result;
    }

    /**
     * 根据计划code，查询报告基础信息
     */
    public BaseDetailVO getBaseInfo(BaseDetailVO vo) {
        if (StringUtils.isBlank(vo.getPlanCode())) {
            return vo;
        }
        TmTestPlanEntityDO entityDO = tmTestPlanRepository.selectTmTestPlanByCode(vo.getPlanCode());
        if (Objects.isNull(entityDO)) {
            return vo;
        }
        vo.setProductCode(entityDO.getProductCode());
        vo.setProductName(entityDO.getProductName());
        vo.setDeptName(entityDO.getDeptName());
        vo.setDeptId(entityDO.getDeptId());
        vo.setPlanCode(entityDO.getCode());
        vo.setPlanName(entityDO.getPlanName());
        vo.setProductOwnerId(entityDO.getProductDirectorId());
        vo.setProductOwnerName(StringUtils.defaultIfBlank(entityDO.getProductDirectorName(), null));
        vo.setVersionName(entityDO.getVersionName());
        vo.setVersionCode(entityDO.getVersionCode());
        vo.setPublishDate(entityDO.getPublishDate());
        vo.setPresentationDate(entityDO.getAccessDate());
        vo.setAccessDatePartition(entityDO.getAccessDatePartition());
        vo.setPermitDatePartition(entityDO.getPermitDatePartition());
        vo.setApprovalExitDate(entityDO.getPermitDate());
        vo.setReportName(entityDO.getVersionName() + vo.getReportType().getValue());

        //实时查询版本数据
        this.buildVersionInfo(vo);

        if (StringUtils.isEmpty(vo.getReportCode())) {
            return vo;
        }
        List<EmailMemberVO> emailMemberVOS = emailRepository.queryEmailMembers(vo.getReportCode());
        if (CollectionUtils.isEmpty(emailMemberVOS)) {
            return vo;
        }
        List<EmailMemberVO> receiveUsers = emailMemberVOS.stream().filter(t -> NoticeUserTypeEnum.RECIPIENT.name().equals(t.getUserTypeCode())).collect(Collectors.toList());
        List<EmailMemberVO> ccUsers = emailMemberVOS.stream().filter(t -> NoticeUserTypeEnum.CC.name().equals(t.getUserTypeCode())).collect(Collectors.toList());
        vo.setReceiveUsers(receiveUsers);
        vo.setCcUsers(ccUsers);
        return vo;
    }

    public TmSmokeReportDetailVO detailOnlineSmokeReport(OnlineSmokeReportDetailQuery query) {
        TmSmokeReportDetailVO result = new TmSmokeReportDetailVO();
        if (Objects.isNull(query)) {
            return result;
        }

        TmTestReportEntityDO report = null;

        //查询报告信息
        if (StringUtils.isNotBlank(query.getReportCode())) {
            report = tmTestReportRepository.getReportByReportCode(query.getReportCode());
        } else {
            report = tmTestReportRepository.getReportByPlanCodeAndReportType(query.getPlanCode(), ReportType.ONLINE_SMOKE);
        }

        //数据转化
        if (!Objects.isNull(report)) {
            result = tmTestReportDomainConverter.convertSmoke(report);
        } else if (StringUtils.isNotBlank(query.getPlanCode())) {
            result.setPlanCode(query.getPlanCode());
        }
        //组装默认参数
        result.buildSelf(query);

        //组装基本信息
        getBaseInfo(result);
        result.setZuiFlag(containsZUI(result.getProductCode(), result.getVersionCode()));

        //延迟验收期间发版记录
        result.setPublishRecordList(buildDelayRecord(result.getVersionCode()));
        return result;
    }

    private List<PublishVersionVO> buildDelayRecord(String versionCode) {
        if (StringUtils.isBlank(versionCode)) {
            return null;
        }
        //查延迟验收表
        TmDelayAcceptRecordEntityDO entityDO = tmDelayAcceptRecordlRepository.selectByVersionCode(versionCode);
        if (Objects.isNull(entityDO)) {
            return null;
        }
        //查发版记录
        FindVersionByActualPublishDateQuery query = new FindVersionByActualPublishDateQuery();
        query.setBeginTime(entityDO.getGmtCreate());
        query.setEndTime(new Date());
        query.setProductCode(entityDO.getProductCode());
        List<VersionVO> publishVOList = projectRpcService.findListByActualPublishDate(query);
        if (CollectionUtils.isEmpty(publishVOList)) {
            return PublishVersionVO.buildDelayResp(entityDO);
        }
        List<PublishVersionVO> voList = new ArrayList<>(publishVOList.size());
        publishVOList.forEach(item -> voList.add(PublishVersionVO.buildPublish(item)));
        voList.add(PublishVersionVO.buildDelayAccept(entityDO));
        return voList.stream().sorted(Comparator.comparing(PublishVersionVO::getOperationTime).reversed()).collect(Collectors.toList());
    }

    public TmPermitReportDetailVO detailPermitReport(PermitReportDetailQuery query) {
        TmPermitReportDetailVO result = new TmPermitReportDetailVO();
        if (Objects.isNull(query)) {
            return result;
        }
        TmTestReportEntityDO report = null;

        //查询报告信息
        if (StringUtils.isNotBlank(query.getReportCode())) {
            report = tmTestReportRepository.getReportByReportCode(query.getReportCode());
        } else {
            report = tmTestReportRepository.getReportByPlanCodeAndReportType(query.getPlanCode(), ReportType.TEST_PERMIT);
        }

        //数据转化
        if (!Objects.isNull(report)) {
            result = tmTestReportDomainConverter.convertPermit(report);
        } else if (StringUtils.isNotBlank(query.getPlanCode())) {
            result.setPlanCode(query.getPlanCode());
        }

        //组装默认参数
        result.buildSelf(query);

        //组装基本信息
        getBaseInfo(result);
        result.setZuiFlag(containsZUI(result.getProductCode(), result.getVersionCode()));

        //查询模块信息
        result.setModuleTestVOS(this.getModuleTestVOS(result.getReportCode(), result.getPlanCode()));

        // 返回安全计划code
        result.setSafePlanCode(this.getSafePlanCode(result.getPlanCode()));

        VersionVO versionVO = projectRpcService.findVersionQuery(result.getVersionCode());
        result.setNeedUiTest(versionVO.getNeedUiTest());

        return result;
    }

    private List<TmModuleTestVO> getModuleTestVOS(String reportCode, String planCode) {
        List<TmModuleTestVO> resultList = null;
        if (StringUtils.isNotBlank(reportCode)) {
            //已经创建过报告，以报告数据为准
            resultList = tmTestReportRepository.findReportModuleTestVOS(reportCode);

        } else if (StringUtils.isNotBlank(planCode)) {
            //未创建报告，查询计划-功能测试点，作为参考
            resultList = tmTestReportRepository.findPlanModuleTestVOS(planCode);
        }
        return CollectionUtils.isEmpty(resultList) ? new ArrayList<>() : resultList;
    }

    public CaseExecuteResultVO queryCaseExecuteResult(CaseExecuteResultQuery query) {
        List<TmTestPlanCaseEntityDO> entityList = tmTestPlanCaseRepository.getDistinctListByPlanCode(query.getPlanCode());
        if (CollectionUtils.isEmpty(entityList)) {
            return CaseExecuteResultVO.init();
        }

        //总数
        CaseNumVO totalNumVO = buildTotalNumVO(entityList, query.getDistinctFlag());

        //计划用例数
        CaseNumVO planNumVO = buildPlanNumVO(entityList, query.getDistinctFlag(), query.getTestStageList());

        //通过用例数
        CaseNumVO passNumVO = buildPassNumVO(entityList, query.getDistinctFlag(), query.getTestStageList());

        //最终结果
        CaseFinalResultEnum caseFinalResultEnum = getFinalResult(entityList, query.getTestStageList());

        //不同状态集合
        List<StatisticCaseResultVO> statisticCaseResultVOList = buildCaseResultList(entityList, query.getDistinctFlag(), query.getTestStageList());

        CaseExecuteResultVO caseExecuteResultVO = new CaseExecuteResultVO();
        caseExecuteResultVO.setTotalNumVO(totalNumVO);
        caseExecuteResultVO.setPlanNumVO(planNumVO);
        caseExecuteResultVO.setPassNumVO(passNumVO);
        caseExecuteResultVO.setCaseFinalResultDesc(caseFinalResultEnum.getValue());
        caseExecuteResultVO.setCaseFinalResult(caseFinalResultEnum);
        caseExecuteResultVO.setStatisticCaseResultList(statisticCaseResultVOList);
        return caseExecuteResultVO;
    }

    /**
     * 分组统计用例执行结果-总数
     *
     * @param entityList   源数据集合
     * @param distinctFlag 是否需要去重
     * @return {@link CaseNumVO}
     */
    private CaseNumVO buildTotalNumVO(List<TmTestPlanCaseEntityDO> entityList, Boolean distinctFlag) {
        //根据用例类型分组
        Map<TestcaseTypeEnum, List<TmTestPlanCaseEntityDO>> typeMap = entityList.stream().collect(Collectors.groupingBy(TmTestPlanCaseEntityDO::getCaseType));
        if (MapUtils.isEmpty(typeMap)) {
            return CaseNumVO.init();
        }

        List<TypeNumVO> totalVOS = new ArrayList<>(2);
        List<TestcaseTypeEnum> typeList = TestcaseTypeEnum.getReportShowType();
        typeList.forEach(type -> {
            if (StringUtils.isNotBlank(type.getValue())) {
                AtomicReference<Long> totalNum = new AtomicReference<>(0L);
                typeMap.entrySet().forEach(entry -> {
                    if (type.equals(entry.getKey())) {
                        List<TmTestPlanCaseEntityDO> values = entry.getValue();
                        if (CollectionUtils.isNotEmpty(values)) {
                            //准入，准出，线上冒烟，总数去重
                            if (distinctFlag) {
                                Set<String> distinctTotalSet = values.stream().map(TmTestPlanCaseEntityDO::getCaseCode).collect(Collectors.toSet());
                                totalNum.set(CollectionUtils.isEmpty(distinctTotalSet) ? 0L : distinctTotalSet.size());
                            } else {
                                totalNum.set(CollectionUtils.isEmpty(values) ? 0L : values.size());
                            }
                        }
                    }
                });
                totalVOS.add(TypeNumVO.buildSelf(type, totalNum.get()));
            }
        });
        return CaseNumVO.buildSelf(totalVOS);
    }

    /**
     * * 分组统计用例执行结果-计划
     *
     * @param entityList    源数据集合
     * @param distinctFlag  是否需要去重
     * @param stageEnumList 测试阶段
     * @return {@link CaseNumVO}
     */
    private CaseNumVO buildPlanNumVO(List<TmTestPlanCaseEntityDO> entityList, Boolean distinctFlag, List<TestPlanStageEnum> stageEnumList) {
        if (CollectionUtils.isNotEmpty(stageEnumList)) {
            entityList = entityList.stream().filter(entity -> stageEnumList.contains(entity.getTestStage())).collect(Collectors.toList());
        }
        return buildTotalNumVO(entityList, distinctFlag);
    }

    /**
     * 分组统计用例执行结果-通过
     *
     * @param entityList   源数据集合
     * @param distinctFlag 是否需要去重
     * @return {@link CaseNumVO}
     */
    private CaseNumVO buildPassNumVO(List<TmTestPlanCaseEntityDO> entityList, Boolean distinctFlag, List<TestPlanStageEnum> stageEnumList) {
        if (CollectionUtils.isNotEmpty(stageEnumList)) {
            entityList = entityList.stream().filter(entity -> stageEnumList.contains(entity.getTestStage())).collect(Collectors.toList());
        }
        entityList = entityList.stream()
                .filter(entity -> (TestPlanCaseStatusEnum.PASSED.equals(entity.getStatus()) || TestPlanCaseStatusEnum.SUCCESS.equals(entity.getStatus())))
                .collect(Collectors.toList());
        return buildTotalNumVO(entityList, distinctFlag);
    }

    /**
     * 获取最终结果
     *
     * @param entityList
     * @param stageEnumList
     * @return
     */
    public CaseFinalResultEnum getFinalResult(List<TmTestPlanCaseEntityDO> entityList, List<TestPlanStageEnum> stageEnumList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return CaseFinalResultEnum.NOT_ASSOCIATED_CASES;
        }
        //按阶段筛选
        if (CollectionUtils.isNotEmpty(stageEnumList)) {
            entityList = entityList.stream().filter(entity -> stageEnumList.contains(entity.getTestStage())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(entityList)) {
            return CaseFinalResultEnum.NOT_ASSOCIATED_CASES;
        }

        //根据状态分组
        Map<TestPlanCaseStatusEnum, List<TmTestPlanCaseEntityDO>> statusMap = entityList.stream().collect(Collectors.groupingBy(TmTestPlanCaseEntityDO::getStatus));

        //最终结果
        if ((statusMap.keySet().size() == 1 && (statusMap.keySet().contains(TestPlanCaseStatusEnum.PASSED) || statusMap.keySet().contains(TestPlanCaseStatusEnum.SUCCESS)))
                || (statusMap.keySet().size() == 2 && statusMap.keySet().contains(TestPlanCaseStatusEnum.PASSED) && statusMap.keySet().contains(TestPlanCaseStatusEnum.SUCCESS))) {
            return CaseFinalResultEnum.PASSED;

        } else if (isInitial(statusMap)) {
            return CaseFinalResultEnum.NOT_TEST;

        } else {
            return CaseFinalResultEnum.FAILED;
        }
    }

    /**
     * 总体结果是否未执行判断
     *
     * @param statusMap 执行结果map
     * @return {@link Boolean}
     */
    private Boolean isInitial(Map<TestPlanCaseStatusEnum, List<TmTestPlanCaseEntityDO>> statusMap) {
        //未测= 手工未测+自动化未执行，待执行，执行中
        for (TestPlanCaseStatusEnum status : statusMap.keySet()) {
            if (status.equals(TestPlanCaseStatusEnum.PASSED)
                    || status.equals(TestPlanCaseStatusEnum.FAILED)
                    || status.equals(TestPlanCaseStatusEnum.BLOCK)
                    || status.equals(TestPlanCaseStatusEnum.SKIP)
                    || status.equals(TestPlanCaseStatusEnum.BROKEN)
                    || status.equals(TestPlanCaseStatusEnum.SKIPPED)
                    || status.equals(TestPlanCaseStatusEnum.UNKNOWN)
                    || status.equals(TestPlanCaseStatusEnum.SUCCESS)
                    || status.equals(TestPlanCaseStatusEnum.TERMINATION)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 组装测试结果集合
     *
     * @param entityList
     * @param distinctFlag
     * @param stageEnumList
     * @return
     */
    private List<StatisticCaseResultVO> buildCaseResultList(List<TmTestPlanCaseEntityDO> entityList, Boolean distinctFlag, List<TestPlanStageEnum> stageEnumList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return StatisticCaseResultVO.initList();
        }

        List<StatisticCaseResultVO> statisticCaseResultVOList = new ArrayList<>();
        //按阶段筛选
        if (CollectionUtils.isNotEmpty(stageEnumList)) {
            entityList = entityList.stream().filter(entity -> stageEnumList.contains(entity.getTestStage())).collect(Collectors.toList());
        }

        //计划用例数
        Long planNum = 0L;
        if (distinctFlag) {
            Set<String> distinctPlanSet = entityList.stream().map(TmTestPlanCaseEntityDO::getCaseCode).collect(Collectors.toSet());
            planNum = CollectionUtils.isNotEmpty(distinctPlanSet) ? distinctPlanSet.size() : 0L;
        } else {
            planNum = Long.valueOf(entityList.size());
        }

        //根据状态分组
        Map<TestPlanCaseStatusEnum, List<TmTestPlanCaseEntityDO>> statusMap = entityList.stream().collect(Collectors.groupingBy(TmTestPlanCaseEntityDO::getStatus));

        //各状态统计值
        for (TestPlanCaseStatusEnum testPlanCaseStatus : TestPlanCaseStatusEnum.getShowValues()) {
            StatisticCaseResultVO statisticCaseResultVO = new StatisticCaseResultVO();
            statisticCaseResultVO.setResult(testPlanCaseStatus);
            statisticCaseResultVO.setResultDesc(testPlanCaseStatus.getValue());
            Long num = 0L;
            List<TmTestPlanCaseEntityDO> caseList = getResultList(testPlanCaseStatus, statusMap);
            if (CollectionUtils.isNotEmpty(caseList)) {
                if (distinctFlag) {
                    Set<String> distinctStateSet = caseList.stream().map(TmTestPlanCaseEntityDO::getCaseCode).collect(Collectors.toSet());
                    num = CollectionUtils.isNotEmpty(distinctStateSet) ? distinctStateSet.size() : 0L;
                } else {
                    num = Long.valueOf(caseList.size());
                }
            }
            statisticCaseResultVO.setNum(num);
            statisticCaseResultVO.setTotalNum(planNum);
            if (planNum != 0) {
                statisticCaseResultVO.setRatio(new BigDecimal(num * 100).divide(new BigDecimal(planNum), 2, RoundingMode.HALF_UP).doubleValue());
            }
            statisticCaseResultVOList.add(statisticCaseResultVO);
        }
        return statisticCaseResultVOList;
    }

    /**
     * 手工和自动化用例状态转换
     *
     * @param testPlanCaseStatus 当前统计状态
     * @param statusMap          执行结果map
     * @return {@link TmTestPlanEntityDO}
     */
    private List<TmTestPlanCaseEntityDO> getResultList(TestPlanCaseStatusEnum testPlanCaseStatus, Map<TestPlanCaseStatusEnum, List<TmTestPlanCaseEntityDO>> statusMap) {
        List<TmTestPlanCaseEntityDO> resultList = new ArrayList<>();
        //成功= 手工通过 + 自动化成功
        if (testPlanCaseStatus.equals(TestPlanCaseStatusEnum.PASSED)) {
            resultList.addAll(statusMap.getOrDefault(TestPlanCaseStatusEnum.PASSED, new ArrayList<>()));
            resultList.addAll(statusMap.getOrDefault(TestPlanCaseStatusEnum.SUCCESS, new ArrayList<>()));
            return resultList;
        }

        // 未测= 手工未测+自动化待执行+未执行+执行中
        if (testPlanCaseStatus.equals(TestPlanCaseStatusEnum.INITIAL)) {
            resultList.addAll(statusMap.getOrDefault(TestPlanCaseStatusEnum.INITIAL, new ArrayList<>()));
            resultList.addAll(statusMap.getOrDefault(TestPlanCaseStatusEnum.NOT_STARTED, new ArrayList<>()));
            resultList.addAll(statusMap.getOrDefault(TestPlanCaseStatusEnum.SUBMITTED, new ArrayList<>()));
            resultList.addAll(statusMap.getOrDefault(TestPlanCaseStatusEnum.IN_PROGRESS, new ArrayList<>()));
            return resultList;
        }

        //其他
        return statusMap.get(testPlanCaseStatus);
    }

    public ExternalTestReportDetailVO detailExternalTestReport(FindTmExternalTestReportDetailQuery query) {
        TmTestReportEntityDO report = null;
        ExternalTestReportDetailVO vo = new ExternalTestReportDetailVO();
        vo.setPlanCode(query.getPlanCode());
        vo.setReportUserId(query.getTransactor().getUserId());
        vo.setReportUserName(query.getTransactor().getUserName());

        if (StringUtils.isNotBlank(query.getReportCode())) {
            report = tmTestReportRepository.getTmReportByPrimaryKey(query.getReportCode());
        } else {
            report = tmTestReportRepository.getReportByPlanCodeAndReportType(query.getPlanCode(), query.getReportType());
        }

        if (report != null) {
            vo = tmTestReportDomainConverter.convertExternalVO(report);
            vo.setAttachments(attachmentRepository.findListByBusinessCode(report.getReportCode()));
        }

        this.setExternalTestReportBasicInfo(vo);
        vo.setZuiFlag(containsZUI(vo.getProductCode(), vo.getVersionCode()));
        // 填充安全测试计划code
        vo.setSafePlanCode(this.getSafePlanCode(vo.getPlanCode()));
        if (tmStateMachineExtendService.canReportEdit(vo.getProductCode(), query.getTransactor())) {
            vo.setButtonVOS(ReportButtonEnum.EDIT_REPORT);
            vo.setFieldVOS(Arrays.asList(ReportEditFieldEnum.attachments, ReportEditFieldEnum.summary));
        }
        VersionVO versionVO = projectRpcService.findVersionQuery(vo.getVersionCode());
        vo.setNeedUiTest(versionVO == null ? false : versionVO.getNeedUiTest());
        return vo;
    }

    public void setExternalTestReportBasicInfo(BaseReportInfoVO baseReportInfoVO) {
        TmTestPlanEntityDO entityDO = tmTestPlanRepository.selectTmTestPlanByCode(baseReportInfoVO.getPlanCode());
        if (null == entityDO) {
            return;
        }
        StringBuilder reportName = new StringBuilder();
        if (StringUtils.isNotBlank(entityDO.getVersionName())) {
            reportName.append(entityDO.getVersionName());
        }
        reportName.append(ReportType.CHECED_TEST.getValue());
        baseReportInfoVO.setReportName(reportName.toString());
        baseReportInfoVO.setReportType(ReportType.CHECED_TEST);
        baseReportInfoVO.setReportTypeDesc(ReportType.CHECED_TEST.getValue());
        baseReportInfoVO.setProductCode(entityDO.getProductCode());
        baseReportInfoVO.setProductName(entityDO.getProductName());
        baseReportInfoVO.setDeptName(entityDO.getDeptName());
        baseReportInfoVO.setDeptId(entityDO.getDeptId());
        baseReportInfoVO.setPlanCode(entityDO.getCode());
        baseReportInfoVO.setPlanName(entityDO.getPlanName());
        baseReportInfoVO.setProductSource(entityDO.getProductSource());
        baseReportInfoVO.setProductSourceDesc(ProductSourceEnum.getValueByName(entityDO.getProductSource()));
        baseReportInfoVO.setVersionName(entityDO.getVersionName());
        baseReportInfoVO.setVersionCode(entityDO.getVersionCode());
        baseReportInfoVO.setProductOwnerId(entityDO.getProductDirectorId());
        baseReportInfoVO.setProductOwnerName(entityDO.getProductDirectorName());
        baseReportInfoVO.setPlanOnlineDate(entityDO.getPublishDate());
        baseReportInfoVO.setPlanPresentationDate(entityDO.getAccessDate());
        baseReportInfoVO.setPlanApprovalExitDate(entityDO.getPermitDate());
        baseReportInfoVO.setStartDate(entityDO.getStartDate());
        //实时查询版本数据
        this.buildVersionInfo(baseReportInfoVO);
        if (StringUtils.isEmpty(baseReportInfoVO.getReportCode())) {
            return;
        }
        List<EmailMemberVO> emailMemberVOS = emailRepository.queryEmailMembers(baseReportInfoVO.getReportCode());
        if (CollectionUtils.isEmpty(emailMemberVOS)) {
            return;
        }
        List<EmailMemberVO> receiveUsers = emailMemberVOS.stream()
                .filter(t -> NoticeUserTypeEnum.RECIPIENT.name().equals(t.getUserTypeCode()))
                .collect(Collectors.toList());
        List<EmailMemberVO> ccUsers = emailMemberVOS.stream()
                .filter(t -> NoticeUserTypeEnum.CC.name().equals(t.getUserTypeCode()))
                .collect(Collectors.toList());
        baseReportInfoVO.setReceiveUsers(receiveUsers);
        baseReportInfoVO.setCcUsers(ccUsers);
    }

    /**
     * 实时查询版本数据
     *
     * @param vo {@link BaseReportInfoVO}
     * @return {@link BaseReportInfoVO}
     */
    public BaseReportInfoVO buildVersionInfo(BaseReportInfoVO vo) {
        SimpleVersionQuery versionQuery = new SimpleVersionQuery();
        versionQuery.setCode(vo.getVersionCode());
        SimpleVersionVO versionVO = projectRpcService.simpleVersionQuery(versionQuery);
        if (null == versionVO) {
            return vo;
        }
        vo.setProductCode(versionVO.getProductCode());
        vo.setProductName(versionVO.getProductName());
        vo.setDeptId(versionVO.getDeptId());
        vo.setDeptName(versionVO.getDeptName());
        vo.setVersionName(versionVO.getName());
        vo.setVersionCode(versionVO.getCode());
        vo.setPlanPresentationDate(versionVO.getPresentationDate());
        vo.setPlanApprovalExitDate(versionVO.getApprovalExitDate());
        vo.setPlanOnlineDate(versionVO.getPublishDate());
        vo.setStartDate(versionVO.getStartDate());
        return vo;
    }

    public void buildVersionInfo(BaseDetailVO vo) {
        SimpleVersionQuery versionQuery = new SimpleVersionQuery();
        versionQuery.setCode(vo.getVersionCode());
        SimpleVersionVO versionVO = projectRpcService.simpleVersionQuery(versionQuery);
        if (null == versionVO) {
            return;
        }
        vo.setReportName(versionVO.getName() + vo.getReportType().getValue());
        vo.setProductCode(versionVO.getProductCode());
        vo.setProductName(versionVO.getProductName());
        vo.setDeptId(versionVO.getDeptId());
        vo.setDeptName(versionVO.getDeptName());
        vo.setVersionName(versionVO.getName());
        vo.setVersionCode(versionVO.getCode());
        if (null != versionVO.getPublishDate()) {
            vo.setPublishDate(versionVO.getPublishDate());
        }
        if (null != versionVO.getPresentationDate()) {
            vo.setPresentationDate(versionVO.getPresentationDate());
        }
        if (null != versionVO.getApprovalExitDate()) {
            vo.setApprovalExitDate(versionVO.getApprovalExitDate());
        }
    }

    /**
     * 查询准出报告时增加安全计划code
     */
    public String getSafePlanCode(String planCode) {
        // 返回安全计划code
        TmTestPlanEntityDO entityDO = tmTestPlanRepository.getSafePlanCode(planCode);
        if (null != entityDO) {
            return entityDO.getCode();
        }
        return null;
    }

    public ReviewReportDetailVO detailReviewReport(FindReviewReportDetailQuery query) {
        TmTestReportEntityDO report = null;
        ReviewReportDetailVO vo = new ReviewReportDetailVO();
        vo.setPlanCode(query.getPlanCode());
        vo.setReportUserId(query.getTransactor().getUserId());
        vo.setReportUserName(query.getTransactor().getUserName());

        if (StringUtils.isNotBlank(query.getReportCode())) {
            report = tmTestReportRepository.getTmReportByPrimaryKey(query.getReportCode());
        } else {
            report = tmTestReportRepository.getReportByPlanCodeAndReportType(query.getPlanCode(), query.getReportType());
        }

        if (report != null) {
            vo = tmTestReportDomainConverter.convertReviewVO(report);
            vo.setAttachments(attachmentRepository.findListByBusinessCode(report.getReportCode()));
            vo.setReviewInfo(setReviewInfoUserHandle(report.getReportCode()));
            vo.setReviewOpinions(tmTestReportRepository.getReviewOpinionVOS(report.getReportCode()));
            vo.setReviewRenewals(tmTestReportRepository.getReviewRenewalVOS(report.getReportCode()));
        }
        setReviewReportBasicInfo(vo);
        if (tmStateMachineExtendService.canReportEdit(vo.getProductCode(), query.getTransactor())) {
            vo.setButtonVOS(ReportButtonEnum.EDIT_REPORT);
            vo.setFieldVOS(Arrays.asList(ReportEditFieldEnum.reviewInfoVOS, ReportEditFieldEnum.reviewOpinionVOS,
                    ReportEditFieldEnum.reviewRenewalVOS, ReportEditFieldEnum.attachments));
        }
        // 返回最近一次参评人信息
        if (StringUtils.isNotBlank(query.getProductCode()) && null != query.getReportUserId()) {
            ReviewInfoUserHandleVO reviewInfoUserHandleVO = new ReviewInfoUserHandleVO();
            if (null != vo.getReviewInfo()) {
                reviewInfoUserHandleVO = vo.getReviewInfo();
            }
            if (CollectionUtils.isEmpty(reviewInfoUserHandleVO.getDevelopReviewUserList()) &&
                    CollectionUtils.isEmpty(reviewInfoUserHandleVO.getTestReviewUserList()) &&
                    CollectionUtils.isEmpty(reviewInfoUserHandleVO.getProductReviewUserList())) {
                ReviewInfoEntityDO reviewInfo = getHistoryReviewInfo(query.getProductCode(), query.getReportUserId());
                if (Objects.nonNull(reviewInfo)) {
                    if (StringUtils.isNotBlank(reviewInfo.getDevelopReviewUsers())) {
                        List<ReviewUserVO> list = handleUserInfo(reviewInfo.getDevelopReviewUsers());
                        reviewInfoUserHandleVO.setDevelopReviewUserList(list);
                    }
                    if (StringUtils.isNotBlank(reviewInfo.getProductReviewUsers())) {
                        List<ReviewUserVO> list = handleUserInfo(reviewInfo.getProductReviewUsers());
                        reviewInfoUserHandleVO.setProductReviewUserList(list);
                    }
                    if (StringUtils.isNotBlank(reviewInfo.getTestReviewUsers())) {
                        List<ReviewUserVO> list = handleUserInfo(reviewInfo.getTestReviewUsers());
                        reviewInfoUserHandleVO.setTestReviewUserList(list);
                    }
                    vo.setReviewInfo(reviewInfoUserHandleVO);
                }
            }
        }
        return vo;
    }

    private ReviewInfoEntityDO getHistoryReviewInfo(String productCode, Long reportUserId) {
        TmTestReportEntityDO entityDO = tmTestReportRepository.getReportByProductCodeAndUser(productCode, reportUserId);
        if (null == entityDO) {
            return null;
        }
        return tmTestReportRepository.getReviewInfoUserByCode(entityDO.getReportCode());
    }

    public void setReviewReportBasicInfo(BaseReportInfoVO baseReportInfoVO) {
        TmTestPlanEntityDO entityDO = tmTestPlanRepository.selectTmTestPlanByCode(baseReportInfoVO.getPlanCode());
        if (null == entityDO) {
            return;
        }
        StringBuilder reportName = new StringBuilder();
        if (StringUtils.isNotBlank(entityDO.getVersionName())) {
            reportName.append(entityDO.getVersionName());
        }
        reportName.append(ReportType.CASE_REVIEW.getValue());
        baseReportInfoVO.setReportName(reportName.toString());
        baseReportInfoVO.setReportType(ReportType.CASE_REVIEW);
        baseReportInfoVO.setReportTypeDesc(ReportType.CASE_REVIEW.getValue());
        baseReportInfoVO.setProductCode(entityDO.getProductCode());
        baseReportInfoVO.setProductName(entityDO.getProductName());
        baseReportInfoVO.setDeptName(entityDO.getDeptName());
        baseReportInfoVO.setDeptId(entityDO.getDeptId());
        baseReportInfoVO.setPlanCode(entityDO.getCode());
        baseReportInfoVO.setPlanName(entityDO.getPlanName());
        baseReportInfoVO.setProductSource(entityDO.getProductSource());
        baseReportInfoVO.setVersionName(entityDO.getVersionName());
        baseReportInfoVO.setVersionCode(entityDO.getVersionCode());
        baseReportInfoVO.setProductOwnerId(entityDO.getProductDirectorId());
        baseReportInfoVO.setProductOwnerName(entityDO.getProductDirectorName());
        //实时查询版本数据
        buildVersionInfo(baseReportInfoVO);
        if (StringUtils.isEmpty(baseReportInfoVO.getReportCode())) {
            return;
        }
        List<EmailMemberVO> emailMemberVOS = emailRepository.queryEmailMembers(baseReportInfoVO.getReportCode());
        if (cn.hutool.core.collection.CollectionUtil.isEmpty(emailMemberVOS)) {
            return;
        }
        List<EmailMemberVO> receiveUsers = emailMemberVOS.stream()
                .filter(t -> NoticeUserTypeEnum.RECIPIENT.name().equals(t.getUserTypeCode()))
                .collect(Collectors.toList());
        List<EmailMemberVO> ccUsers = emailMemberVOS.stream()
                .filter(t -> NoticeUserTypeEnum.CC.name().equals(t.getUserTypeCode()))
                .collect(Collectors.toList());
        baseReportInfoVO.setReceiveUsers(receiveUsers);
        baseReportInfoVO.setCcUsers(ccUsers);
    }

    private ReviewInfoUserHandleVO setReviewInfoUserHandle(String code) {
        ReviewInfoEntityDO entityDO = tmTestReportRepository.getReviewInfoUserByCode(code);
        ReviewInfoUserHandleVO vo = tmTestReportDomainConverter.convertReviewInfoHandle(entityDO);
        //TODO 处理  ReviewUserVO
        if (Objects.nonNull(entityDO)) {
            if (StringUtils.isNotBlank(entityDO.getDevelopReviewUsers())) {
                List<ReviewUserVO> list = handleUserInfo(entityDO.getDevelopReviewUsers());
                vo.setDevelopReviewUserList(list);
            }
            if (StringUtils.isNotBlank(entityDO.getProductReviewUsers())) {
                List<ReviewUserVO> list = handleUserInfo(entityDO.getProductReviewUsers());
                vo.setProductReviewUserList(list);
            }
            if (StringUtils.isNotBlank(entityDO.getTestReviewUsers())) {
                List<ReviewUserVO> list = handleUserInfo(entityDO.getTestReviewUsers());
                vo.setTestReviewUserList(list);
            }
        }
        return vo;
    }

    private List<ReviewUserVO> handleUserInfo(String jsonUser) {
        List<ReviewUserVO> list = new ArrayList<>();
        log.info("-------------------handleUserInfo {}", jsonUser);
        JSONArray jsonArray = JSONArray.parseArray(jsonUser);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            // 将string类型直接封装成对象
            ReviewUserVO reviewUserVO = JSONObject.parseObject(object.toJSONString(), ReviewUserVO.class);
            list.add(reviewUserVO);
        }
        List<Long> userIdList = list.stream().map(ReviewUserVO::getSsoUserId).collect(Collectors.toList());
        ListUserQuery query = new ListUserQuery();
        query.setSsoUserIdList(userIdList);
        query.setRows(userIdList.size());
        try {
            List<UserSelectVO> userSelectVOList = userRpcService.listUserQuery(query);
            list = tmTestReportDomainConverter.convertUser(userSelectVOList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return list;
    }

    public MobileTestReportDetailVO findTmMobileTestReportDetailQuery(FindTmMobileTestReportDetailQuery query) {
        MobileTestReportDetailVO vo = new MobileTestReportDetailVO();
        vo.setPlanCode(query.getPlanCode());
        vo.setReportUserId(query.getTransactor().getUserId());
        vo.setReportUserName(query.getTransactor().getUserName());
        TmTestReportEntityDO testReportEntity;
        if (StringUtils.isNotBlank(query.getReportCode())) {
            testReportEntity = tmTestReportRepository.getTmReportByPrimaryKey(query.getReportCode());
        } else {
            testReportEntity = tmTestReportRepository.getReportByPlanCodeAndReportType(query.getPlanCode(), query.getReportType());
        }
        if (testReportEntity != null) {
            vo = tmTestReportDomainConverter.convertMobileVO(testReportEntity);
            vo.setAttachments(attachmentRepository.findListByBusinessCode(testReportEntity.getReportCode()));
            vo.setModuleTestVOS(this.getModuleTestVOS(testReportEntity.getReportCode(), testReportEntity.getPlanCode()));
        }
        this.setMobileTestReportBasicInfo(vo);
        if (tmStateMachineExtendService.canReportEdit(vo.getProductCode(), query.getTransactor())) {
            vo.setButtonVOS(ReportButtonEnum.EDIT_REPORT);
            vo.setFieldVOS(Arrays.asList(ReportEditFieldEnum.actualTestStart, ReportEditFieldEnum.actualTestEnd,
                    ReportEditFieldEnum.moduleTestVOS, ReportEditFieldEnum.summary));
        }
        return vo;
    }

    private void setMobileTestReportBasicInfo(BaseReportInfoVO baseReportInfoVO) {
        TmTestPlanEntityDO testPlanEntity = tmTestPlanRepository.selectTmTestPlanByCode(baseReportInfoVO.getPlanCode());
        if (null == testPlanEntity) {
            return;
        }
        StringBuilder reportName = new StringBuilder();
        if (StringUtils.isNotBlank(testPlanEntity.getVersionName())) {
            reportName.append(testPlanEntity.getVersionName());
        }
        reportName.append(ReportType.SPECIAL_MOBILE.getValue());
        baseReportInfoVO.setReportName(reportName.toString());
        baseReportInfoVO.setReportType(ReportType.SPECIAL_MOBILE);
        baseReportInfoVO.setReportTypeDesc(ReportType.SPECIAL_MOBILE.getValue());
        baseReportInfoVO.setProductCode(testPlanEntity.getProductCode());
        baseReportInfoVO.setProductName(testPlanEntity.getProductName());
        baseReportInfoVO.setDeptName(testPlanEntity.getDeptName());
        baseReportInfoVO.setDeptId(testPlanEntity.getDeptId());
        baseReportInfoVO.setPlanCode(testPlanEntity.getCode());
        baseReportInfoVO.setPlanName(testPlanEntity.getPlanName());
        baseReportInfoVO.setProductSource(testPlanEntity.getProductSource());
        baseReportInfoVO.setVersionName(testPlanEntity.getVersionName());
        baseReportInfoVO.setVersionCode(testPlanEntity.getVersionCode());
        baseReportInfoVO.setProductOwnerId(testPlanEntity.getProductDirectorId());
        baseReportInfoVO.setProductOwnerName(testPlanEntity.getProductDirectorName());
        baseReportInfoVO.setRelationPlanCode(testPlanEntity.getRelationPlanCode());
        //实时查询版本数据
        this.buildVersionInfo(baseReportInfoVO);
        if (StringUtils.isEmpty(baseReportInfoVO.getReportCode())) {
            return;
        }
        List<EmailMemberVO> emailMemberVOS = emailRepository.queryEmailMembers(baseReportInfoVO.getReportCode());
        if (CollectionUtils.isEmpty(emailMemberVOS)) {
            return;
        }
        List<EmailMemberVO> receiveUsers = emailMemberVOS.stream()
                .filter(t -> NoticeUserTypeEnum.RECIPIENT.name().equals(t.getUserTypeCode()))
                .collect(Collectors.toList());
        List<EmailMemberVO> ccUsers = emailMemberVOS.stream()
                .filter(t -> NoticeUserTypeEnum.CC.name().equals(t.getUserTypeCode()))
                .collect(Collectors.toList());
        baseReportInfoVO.setReceiveUsers(receiveUsers);
        baseReportInfoVO.setCcUsers(ccUsers);
    }

    public TmTestPlanEntityDO findSafetyPlanByVersionQuery(String versionCode) {
        return tmTestPlanRepository.getSafetyPlanByVersion(versionCode);
    }

    public TestPlanRangeVO getSafeTestPlanRange(String planCode) {
        TestPlanRangeVO vo = new TestPlanRangeVO();
        TmTestPlanRangeEntityDO rangeEntity = tmTestPlanRepository.getSafePlanRangeByPlanCode(planCode);
        if (rangeEntity == null) {
            log.error("安全测试计划范围TmTestPlanRangeEntity查询不存在:" + planCode);
            return vo;
        }
        vo.setPlanCode(rangeEntity.getPlanCode());
        vo.setExecutor(rangeEntity.getExecutor());
        vo.setExecutorId(rangeEntity.getExecutorId());
        vo.setStatus(rangeEntity.getStatus());
        vo.setTestRange(rangeEntity.getTestRange());
        return vo;
    }

    public SecurityTestResultResp selectSecurityTestResult(String planCode) {
        List<SecurityHoleVO> securityHoles = new ArrayList<>();
        SecurityTestResultVO securityTestResultVO = securityTestReportService.querySecurityTestResult(planCode);
        if (securityTestResultVO == null) {
            return SecurityTestResultResp.buildSelf(securityHoles);
        }
        //组装安全漏洞
        List<SecurityIssue> issueList = securityTestResultVO.getIssues();
        if (CollectionUtils.isNotEmpty(issueList)) {
            for (SecurityIssue securityIssue : issueList) {
                SecurityHoleVO securityHoleVO = new SecurityHoleVO();
                securityHoleVO.setName(securityIssue.getName());
                securityHoleVO.setHarm(securityIssue.getHarm());
                securityHoleVO.setNote(securityIssue.getRemarks());
                securityHoleVO.setSecId(securityIssue.getUuid());
                SecurityLevel securityLevel = SecurityLevel.getEnumByValue(securityIssue.getLevel());
                if (securityLevel != null) {
                    securityHoleVO.setLevel(securityLevel);
                    securityHoleVO.setLevelDesc(SecurityLevel.getDesc(securityLevel));
                }
                SecurityStatus securityStatus = SecurityStatus.getEnumByValue(securityIssue.getStatus());
                if (securityStatus != null) {
                    securityHoleVO.setStatus(securityStatus);
                    securityHoleVO.setStatusDesc(SecurityStatus.getDesc(securityStatus));
                }
                securityHoles.add(securityHoleVO);
            }
        }
        return SecurityTestResultResp.buildSelf(securityHoles);
    }

    public SimpleTestReportDetailVO findTmSimpleTestReportDetailQuery(FindTmSimpleTestReportDetailQuery query) {
        SimpleTestReportDetailVO vo = new SimpleTestReportDetailVO();
        vo.setPlanCode(query.getPlanCode());
        TmTestReportEntityDO tmTestReportEntity;
        if (StringUtils.isNotBlank(query.getReportCode())) {
            tmTestReportEntity = tmTestReportRepository.getTmReportByPrimaryKey(query.getReportCode());
        } else {
            tmTestReportEntity = tmTestReportRepository.getReportByPlanCodeAndReportType(query.getPlanCode(), query.getReportType());
        }
        if (tmTestReportEntity != null) {
            vo = tmTestReportDomainConverter.convertSimpleVO(tmTestReportEntity);
            vo.setAttachments(attachmentRepository.findListByBusinessCode(tmTestReportEntity.getReportCode()));
        }
        this.setSimpleTestReportBasicInfo(vo);
        if (tmStateMachineExtendService.canReportEdit(vo.getProductCode(), query.getTransactor())) {
            vo.setButtonVOS(ReportButtonEnum.EDIT_REPORT);
            vo.setFieldVOS(Arrays.asList(ReportEditFieldEnum.planPresentationDay,
                    ReportEditFieldEnum.planApprovalExitDay, ReportEditFieldEnum.actualPresentationDate,
                    ReportEditFieldEnum.actualPresentationDay, ReportEditFieldEnum.actualApprovalExitDate,
                    ReportEditFieldEnum.actualApprovalExitDay, ReportEditFieldEnum.actualOnlineDate,
                    ReportEditFieldEnum.delay, ReportEditFieldEnum.asPlanedOnline, ReportEditFieldEnum.attachments,
                    ReportEditFieldEnum.summary));
        }
        vo.setReportType(query.getReportType());
        vo.setReportUserId(query.getTransactor().getUserId());
        vo.setReportUserName(query.getTransactor().getUserName());
        vo.setZuiFlag(containsZUI(vo.getProductCode(), vo.getVersionCode()));
        VersionVO versionVO = projectRpcService.findVersionQuery(vo.getVersionCode());
        vo.setNeedUiTest(versionVO.getNeedUiTest());
        vo.setTestStrategy(versionVO.getTestStrategy());
        vo.setPublishRecordList(buildDelayRecord(vo.getVersionCode()));
        return vo;
    }

    private void setSimpleTestReportBasicInfo(BaseReportInfoVO baseReportInfoVO) {
        TmTestPlanEntityDO testPlanEntity = tmTestPlanRepository.selectTmTestPlanByCode(baseReportInfoVO.getPlanCode());
        if (null == testPlanEntity) {
            return;
        }
        StringBuilder reportName = new StringBuilder();
        if (StringUtils.isNotBlank(testPlanEntity.getVersionName())) {
            reportName.append(testPlanEntity.getVersionName());
        }
        reportName.append(ReportType.SIMPLE_PROCESS.getValue());
        baseReportInfoVO.setReportName(reportName.toString());
        baseReportInfoVO.setProductCode(testPlanEntity.getProductCode());
        baseReportInfoVO.setProductName(testPlanEntity.getProductName());
        baseReportInfoVO.setDeptName(testPlanEntity.getDeptName());
        baseReportInfoVO.setDeptId(testPlanEntity.getDeptId());
        baseReportInfoVO.setPlanCode(testPlanEntity.getCode());
        baseReportInfoVO.setPlanName(testPlanEntity.getPlanName());
        baseReportInfoVO.setProductSource(testPlanEntity.getProductSource());
        baseReportInfoVO.setDeveloperCount(testPlanEntity.getDeveloperNum());
        baseReportInfoVO.setTesterCount(testPlanEntity.getTesterNum());
        baseReportInfoVO.setVersionName(testPlanEntity.getVersionName());
        baseReportInfoVO.setVersionCode(testPlanEntity.getVersionCode());
        baseReportInfoVO.setProductOwnerId(testPlanEntity.getProductDirectorId());
        baseReportInfoVO.setProductOwnerName(testPlanEntity.getProductDirectorName());
        baseReportInfoVO.setPlanOnlineDate(testPlanEntity.getPublishDate());
        baseReportInfoVO.setPlanPresentationDate(testPlanEntity.getAccessDate());
        baseReportInfoVO.setPlanApprovalExitDate(testPlanEntity.getPermitDate());
        baseReportInfoVO.setStartDate(testPlanEntity.getStartDate());
        baseReportInfoVO.setDeveloperCount(testPlanEntity.getDeveloperNum());
        baseReportInfoVO.setTesterCount(testPlanEntity.getTesterNum());
        baseReportInfoVO.setPresentationDay(enumConvert(testPlanEntity.getPermitDatePartition()));
        baseReportInfoVO.setPlanApprovalExitDay(enumConvert(testPlanEntity.getAccessDatePartition()));
        //实时查询版本数据
        this.buildVersionInfo(baseReportInfoVO);
        if (StringUtils.isEmpty(baseReportInfoVO.getReportCode())) {
            return;
        }
        List<EmailMemberVO> emailMemberVOS = emailRepository.queryEmailMembers(baseReportInfoVO.getReportCode());
        if (CollectionUtils.isEmpty(emailMemberVOS)) {
            return;
        }
        List<EmailMemberVO> receiveUsers = emailMemberVOS.stream()
                .filter(t -> NoticeUserTypeEnum.RECIPIENT.name().equals(t.getUserTypeCode()))
                .collect(Collectors.toList());
        List<EmailMemberVO> ccUsers = emailMemberVOS.stream()
                .filter(t -> NoticeUserTypeEnum.CC.name().equals(t.getUserTypeCode()))
                .collect(Collectors.toList());
        baseReportInfoVO.setReceiveUsers(receiveUsers);
        baseReportInfoVO.setCcUsers(ccUsers);
    }

    private String enumConvert(TestPlanDatePartitionEnum enu) {
        if (null == enu) {
            return "09:00:00";
        }
        return TestPlanDatePartitionEnum.PM.equals(enu) ? "18:00:00" : "09:00:00";
    }

    public List<AllReportDateResp> reList(PageReportMqQuery query) {
        log.info("reList----------------" + JsonUtil.toJSON(query));
        java.text.DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        query.setPage(1);
        query.setSize(100000);
        query.setStatus(TestPlanStatusEnum.NORMAL);
        PageReportVO pageReportVO = handlePageReportMqQuery(query);

        TestPlanListQuery queryTestPlan = new TestPlanListQuery();
        queryTestPlan.setVersionCode(query.getVersionCode());
        queryTestPlan.setCreateTimeEnd(query.getGmtCreateEnd());
        queryTestPlan.setCreateTimeStart(query.getGmtCreateStart());
        queryTestPlan.setProductCodes(query.getProductCodes());
        List<String> types = new ArrayList<>();
        types.add(TestPlanTypeEnum.TEST_PLAN.name());
        queryTestPlan.setTypes(types);
        queryTestPlan.setPage(1);
        queryTestPlan.setSize(100000);
        TestPlanListDto testPlanListDto = handleTestPlanListQuery(queryTestPlan);
        List<AllReportDateResp> res = new ArrayList<>();
        if (!pageReportVO.getList().isEmpty() && pageReportVO.getList().size() > 0) {
            for (ReportVO vo : pageReportVO.getList()) {
                log.info("进入ReportVO----------------" + JsonUtil.toJSON(vo) + "---------------" + vo.getCode());
                AllReportDateResp resp = new AllReportDateResp();
                resp.setName(vo.getName());
                resp.setVersionCode(vo.getVersionCode());
                resp.setCreateTime(format.format(vo.getGmtCreate()));
                resp.setCreateUser(vo.getCreator());
                resp.setActualTestStartDate(Objects.isNull(vo.getActualPresentationDate()) ? null : format.format(vo.getActualPresentationDate()));
                resp.setDelayForTest(getDelay(vo.getActualPresentationDate(), Objects.isNull(vo.getPresentationDate()) ? null : vo.getPresentationDate()));
                if (vo.getReportType().name().equals(ReportType.TEST_ACCESS.name())) {
                    FindReportCaseStatisticVOSQuery detailQuery = new FindReportCaseStatisticVOSQuery();
                    detailQuery.setCode(vo.getCode());
                    AccessReportDetailVO accessReportDetailVO = handleFindReportCaseStatisticVOSQuery(detailQuery);
                    //准入查询用例总数
                    if (!accessReportDetailVO.getCaseStatisticVOS().isEmpty() && accessReportDetailVO.getCaseStatisticVOS().size() > 0) {
                        resp.setTestCaseCount(accessReportDetailVO.getCaseStatisticVOS().get(0).getCaseCount());
                        resp.setSmokeTestCase(accessReportDetailVO.getCaseStatisticVOS().get(0).getPlanSmokeCaseCount());
                        resp.setSmokeTestCasePassed(accessReportDetailVO.getCaseStatisticVOS().get(0).getSmokeAccessCount());
                    } else {
                        CaseExecuteResultQuery caseExecuteResultQuery = new CaseExecuteResultQuery();
                        caseExecuteResultQuery.setCode(vo.getCode());
                        caseExecuteResultQuery.setPlanCode(vo.getPlanCode());
                        caseExecuteResultQuery.setReportType(TestReportTypeEnum.TEST_ACCESS);
                        // 准入报告：冒烟阶段
                        List<TestPlanStageEnum> testStageList = new ArrayList<>();
                        testStageList.add(TestPlanStageEnum.SMOKE_TEST);
                        caseExecuteResultQuery.setTestStageList(testStageList);
                        CaseExecuteResultVO caseExecuteResultVO = queryCaseExecuteResult(caseExecuteResultQuery);
                        if (CollectionUtil.isNotEmpty(caseExecuteResultVO)) {
                            if (CollectionUtil.isNotEmpty(caseExecuteResultVO.getTotalNumVO())) {
                                resp.setTestCaseCount(Math.toIntExact(caseExecuteResultVO.getTotalNumVO().getSumNum()));
                            }
                            if (CollectionUtil.isNotEmpty(caseExecuteResultVO.getPlanNumVO())) {
                                resp.setSmokeTestCase(Math.toIntExact(caseExecuteResultVO.getPlanNumVO().getSumNum()));
                            }
                            if (CollectionUtil.isNotEmpty(caseExecuteResultVO.getPassNumVO())) {
                                resp.setSmokeTestCasePassed(Math.toIntExact(caseExecuteResultVO.getPassNumVO().getSumNum()));
                            }
                        }
                    }
                } else {
                    resp.setSmokeTestCase(vo.getPlanSmokeCase());
                    resp.setSmokeTestCasePassed(vo.getFirstPermitSmoke());
                }
                resp.setPlanTestHalf(Objects.isNull(vo.getPresentationDate()) ? null : (ifAfternoonByDate(vo.getPresentationDate()) ? 0 : 1));
                resp.setActualTestHalf(Objects.isNull(vo.getActualPresentationDate()) ? null : (ifAfternoonByDate(vo.getActualPresentationDate()) ? 0 : 1));
                resp.setActualTestEndDate(Objects.isNull(vo.getActualApprovalExitDate()) ? null : format.format(vo.getActualApprovalExitDate()));
                resp.setActualVersionEndDate(Objects.isNull(vo.getActualPublishDate()) ? null : format.format(vo.getActualPublishDate()));
                if (vo.getReportType().name().equals(ReportType.CHECED_TEST.name()) || vo.getReportType().name().equals(ReportType.SIMPLE_PROCESS.name())) {
                    resp.setPrdLeader(getPrdLeader(vo.getVersionCode()));
                }
                resp.setTestResult(Objects.isNull(vo.getTestResult()) ? null : TestResultEunm.getValueByName(vo.getTestResult()));
                if (null != vo.getDelay() && vo.getDelay() == 1) {
                    resp.setDelayFlag(vo.getDelay());
                } else {
                    resp.setDelayFlag(0);
                }

                resp.setSourceType(Objects.isNull(vo.getCheckType()) ? "UNKNOWN" : CheckType.getValueByName(vo.getCheckType()));
                resp.setCheckResultDate(Objects.isNull(vo.getUpdateTestResultDate()) ? null : format.format(vo.getUpdateTestResultDate()));
                if (null != vo.getAsPlanedOnline() && vo.getAsPlanedOnline() == 1) {
                    resp.setPlanFlag(vo.getAsPlanedOnline());
                } else {
                    resp.setPlanFlag(0);
                }
                resp.setActualAccessHalf(Objects.isNull(vo.getActualApprovalExitDate()) ? null : (ifAfternoonByDate(vo.getActualApprovalExitDate()) ? 0 : 1));
                resp.setPlanAccessHalf(Objects.isNull(vo.getApprovalExitDate()) ? null : (ifAfternoonByDate(vo.getApprovalExitDate()) ? 0 : 1));
                resp.setPlanTestStartDate(Objects.isNull(vo.getPresentationDate()) ? null : format.format(vo.getPresentationDate()));
                resp.setDeveloperNum(vo.getDeveloperCount());
                resp.setTesterNum(vo.getTesterCount());
                resp.setType(vo.getReportType().getType());
                //计划准出时间
                resp.setPlanTestEndDate(Objects.isNull(vo.getApprovalExitDate()) ? null : format.format(vo.getApprovalExitDate()));
                //计划上线时间
                resp.setPlanTestVersionEndDate(Objects.isNull(vo.getPublishDate()) ? null : format.format(vo.getPublishDate()));
                //准出延期天数
                resp.setAccessDelayForTest(
                        getDelay(vo.getActualApprovalExitDate(),
                                Objects.isNull(vo.getApprovalExitDate()) ? null : vo.getApprovalExitDate()));

                res.add(resp);
            }
        }
        if (!testPlanListDto.getList().isEmpty() && testPlanListDto.getList().size() > 0) {
            for (TestPlanVO vo : testPlanListDto.getList()) {
                AllReportDateResp resp = new AllReportDateResp();
                resp.setVersionCode(vo.getVersionCode());
                resp.setName(vo.getPlanName());
                resp.setPrdLeader(vo.getProductDirectorName());
                resp.setPlanTestStartDate(null == vo.getPresentationDate() ? null : format.format(vo.getPresentationDate()));
                resp.setPlanTestEndDate(null == vo.getApprovalExitDate() ? null : format.format(vo.getApprovalExitDate()));
                //查询计划详情
                TestPlanQuery testPlanQuery = new TestPlanQuery();
                testPlanQuery.setCode(vo.getCode());
                TestPlanDto dto = handleTestPlanQuery(testPlanQuery);
                if (null != dto) {
                    resp.setDeveloperNum(dto.getDeveloperNo());
                    resp.setTesterNum(dto.getTesterNo());
                    resp.setPlanTestHalf(Objects.isNull(dto.getPresentationDate()) ? null : (ifAfternoonByDate(dto.getPresentationDate()) ? 0 : 1));
                    resp.setActualAccessHalf(Objects.isNull(dto.getApprovalExitDate()) ? null : (ifAfternoonByDate(dto.getApprovalExitDate()) ? 0 : 1));
                } else {
                    FindTmTestPlanQuery findTmTestPlanQuery = new FindTmTestPlanQuery(vo.getCode());
                    findTmTestPlanQuery.setCode(vo.getCode());
                    findTmTestPlanQuery.setTransactor(query.getTransactor());
                    log.info("查询新版本计划信息" + findTmTestPlanQuery);
                    TmTestPlanVO tmTestPlanVO = tmTestPlanQueryDomainService.testPlanDetail(findTmTestPlanQuery);
                    if (null != tmTestPlanVO.getDeveloperNum()) {
                        resp.setDeveloperNum(Math.toIntExact(tmTestPlanVO.getDeveloperNum()));
                    }
                    if (null != tmTestPlanVO.getTesterNum()) {
                        resp.setTesterNum(tmTestPlanVO.getTesterNum());
                    }
                    resp.setPlanTestHalf(Objects.isNull(tmTestPlanVO.getAccessDate()) ? null : (ifAfternoonByDate(tmTestPlanVO.getAccessDate()) ? 0 : 1));
                    resp.setActualAccessHalf(Objects.isNull(tmTestPlanVO.getPermitDate()) ? null : (ifAfternoonByDate(tmTestPlanVO.getPermitDate()) ? 0 : 1));
                }
                resp.setCreateUser(vo.getCreator());
                resp.setCreateTime(format.format(vo.getGmtCreate()));
                if ("TEST_PLAN".equals(vo.getType())) {
                    resp.setType(1);
                } else {
                    resp.setType(7);
                }
                res.add(resp);
            }
        }
        return res;
    }

    private TestPlanDto handleTestPlanQuery(TestPlanQuery query) {
        TestPlanDto dto = oldTestReportRepository.selectTestPlanMainByCode(query.getCode());
        // 详情查询，计划准入准出和上线时间使用版本 中的时间
        if (null != dto) {
            List<TestFunctionPointEntityDO> doList = testFunctionPointRepository.selectByBusinessCode(dto.getTestPlanCode());
            dto.setPointList(tmTestReportDomainConverter.convertToVo(doList));
            buildSendUser(dto);
            setTestPlanEditFiled(query, dto);
        }
        return dto;
    }

    public void buildSendUser(TestPlanDto infoVO) {
        String testPlanCode = infoVO.getTestPlanCode();
        if (!testPlanCode.startsWith("TP")) {
            testPlanCode = infoVO.getCode();
        }
        List<QcNoticeResultEntityDO> entityDOList = qcNoticeResultRepository.selectByPlanCode(testPlanCode);
        List<SendUserInfoVO> sendUserInfoVOS = tmTestReportDomainConverter.convert(entityDOList);
        if (CollectionUtil.isNotEmpty(entityDOList)) {
            infoVO.setSendTime(entityDOList.get(0).getSendTime());
            List<SendUserInfoVO> cc = sendUserInfoVOS.stream()
                    .filter(n -> NoticeUserTypeEnum.CC.name().equals(n.getUserType()))
                    .collect(Collectors.toList());
            List<SendUserInfoVO> recipient = sendUserInfoVOS.stream()
                    .filter(n -> NoticeUserTypeEnum.RECIPIENT.name().equals(n.getUserType()))
                    .collect(Collectors.toList());
            infoVO.setCcUsers(cc);
            infoVO.setReceiveUsers(recipient);
        }
    }

    private void setTestPlanEditFiled(TestPlanQuery query, TestPlanDto dto) {
        List<String> test = Arrays.asList(MemberTypeEnum.TESTER.name(), MemberTypeEnum.TESTER_OWNER.name(), MemberTypeEnum.TESTER_M.name());
        List<ProductMemberVO> productMembers = productRpcService.getProductMember(dto.getProductCode(), test);
        if (CollectionUtil.isNotEmpty(productMembers)) {
            Boolean contain = productMembers.stream().map(ProductMemberVO::getUserId).collect(Collectors.toList()).contains(query.getUserId());
            if (contain) {
                List<TestPlanEditEnum> testPlanEditList = new ArrayList<>();
                for (TestPlanEditEnum testPlanEditEnum : TestPlanEditEnum.values()) {
                    testPlanEditList.add(testPlanEditEnum);
                }
                if (TestPlanStatusEnum.NORMAL.name().equals(dto.getStatus())) {
                    testPlanEditList.remove(TestPlanEditEnum.productCode);
                }
                dto.setButtonVOS(PlanButtonEnum.EDIT_PLAN);
                dto.setFieldVOS(testPlanEditList);
            }
        }
    }


    public AccessReportDetailVO handleFindReportCaseStatisticVOSQuery(FindReportCaseStatisticVOSQuery query) {
        AccessReportDetailVO vo = new AccessReportDetailVO();
        vo.setReportCode(query.getCode());
        vo.setCaseStatisticVOS(getCaseStatisticVOS(query.getCode()));
        return vo;
    }

    private List<CaseStatisticsVO> getCaseStatisticVOS(String code) {
        return caseStatisticsRepository.selectByReportCode(code);
    }

    /**
     * 查询新旧报告
     *
     * @param query
     * @return
     */
    public PageReportVO handlePageReportMqQuery(PageReportMqQuery query) {
        Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        PageReportQuery pageReportQuery = tmTestReportDomainConverter.convert(query);
        List<ReportVO> reportVOList = oldTestReportRepository.pageQuery(pageReportQuery);
        reportVOList.addAll(iTmTestReportRepository.pageQuery(query));
        PageReportVO pageReportVO = new PageReportVO();
        pageReportVO.setList(reportVOList);
        pageReportVO.setTotal(page.getTotal());
        return pageReportVO;
    }

    /**
     * 查询新旧计划
     *
     * @param query
     * @return
     */
    private TestPlanListDto handleTestPlanListQuery(TestPlanListQuery query) {
        Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        TestPlanListDto result = new TestPlanListDto();
        List<TestPlanEntityDO> testPlanEntityList = tmTestPlanRepository.selectTestPlanList(query);
        query.setStatusList(StringUtils.isBlank(query.getStatus()) ? null : CollectionUtil.arraysToList(query.getStatus()));
        PlanListQuery planListQuery = tmTestPlanDomainConverter.convertQuery(query);
        planListQuery.setType(CollectionUtil.isNotEmpty(query.getTypes()) ? query.getTypes() : null);
        List<TmTestPlanVO> tmTestPlanEntityList = tmTestPlanRepository.selectTmTestPlanList(planListQuery);
        List<TestPlanVO> testPlanNewList = convertTestPlan(tmTestPlanEntityList);
        List<TestPlanVO> testPlanVOS = tmTestPlanDomainConverter.convert(testPlanEntityList);
        testPlanVOS.addAll(testPlanNewList);
        if (CollectionUtil.isEmpty(testPlanVOS)) {
            result.setList(new ArrayList<>());
            result.setTotal(0L);
        }
        List<String> versionCodes = testPlanEntityList.stream().map(TestPlanEntityDO::getVersionCode).collect(Collectors.toList());
        SimpleListVersionVO simpleListVersionVO = queryVersionList(versionCodes);
        if (simpleListVersionVO != null && CollectionUtil.isNotEmpty(simpleListVersionVO.getSimpleVersionVOList())) {
            Map<String, SimpleVersionVO> versionEntityMap = simpleListVersionVO
                    .getSimpleVersionVOList()
                    .stream()
                    .collect(Collectors.toMap(SimpleVersionVO::getCode, Function.identity(), (key1, key2) -> key2));
            for (TestPlanVO r : testPlanVOS) {
                Optional.ofNullable(versionEntityMap.get(r.getVersionCode()))
                        .ifPresent(v -> {
                            SimpleVersionVO simpleVersionVO = versionEntityMap.get(r.getVersionCode());
                            r.setVersionName(simpleVersionVO.getName());
                            r.setPresentationDate(simpleVersionVO.getPresentationDate());
                            r.setApprovalExitDate(simpleVersionVO.getApprovalExitDate());
                            r.setPublishDate(simpleVersionVO.getPublishDate());
                            r.setStartDate(simpleVersionVO.getStartDate());
                        });
            }
        }
        result.setList(testPlanVOS);
        result.setTotal(page.getTotal());
        return result;
    }

    private List<TestPlanVO> convertTestPlan(List<TmTestPlanVO> tmTestPlanEntityList) {
        if (tmTestPlanEntityList == null) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<TestPlanVO> list = new ArrayList<TestPlanVO>(tmTestPlanEntityList.size());
        for (TmTestPlanVO tmTestPlanEntity : tmTestPlanEntityList) {
            if (tmTestPlanEntity == null) {
                return null;
            }
            TestPlanVO testPlanVO = new TestPlanVO();
            testPlanVO.setCode(tmTestPlanEntity.getCode());
            testPlanVO.setPlanName(tmTestPlanEntity.getPlanName());
            testPlanVO.setProductCode(tmTestPlanEntity.getProductCode());
            testPlanVO.setProductName(tmTestPlanEntity.getProductName());
            if (tmTestPlanEntity.getType() != null) {
                testPlanVO.setType(tmTestPlanEntity.getType().name());
            }
            testPlanVO.setTestDirectorId(tmTestPlanEntity.getTestDirectorId());
            testPlanVO.setTestDirectorName(tmTestPlanEntity.getTestDirectorName());
            testPlanVO.setCreatorId(tmTestPlanEntity.getCreatorId());
            testPlanVO.setCreator(tmTestPlanEntity.getCreator());
            testPlanVO.setGmtCreate(tmTestPlanEntity.getGmtCreate());
            testPlanVO.setModifierId(tmTestPlanEntity.getModifierId());
            testPlanVO.setModifier(tmTestPlanEntity.getModifier());
            testPlanVO.setGmtModified(tmTestPlanEntity.getGmtModified());
            testPlanVO.setEditNo(tmTestPlanEntity.getEditNo());
            if (tmTestPlanEntity.getStatus() != null) {
                testPlanVO.setStatus(tmTestPlanEntity.getStatus().name());
            }
            testPlanVO.setVersionName(tmTestPlanEntity.getVersionName());
            testPlanVO.setVersionCode(tmTestPlanEntity.getVersionCode());
            testPlanVO.setDeptId(tmTestPlanEntity.getDeptId());
            testPlanVO.setDeptName(tmTestPlanEntity.getDeptName());
            testPlanVO.setProductDirectorId(tmTestPlanEntity.getProductDirectorId());
            testPlanVO.setProductDirectorName(tmTestPlanEntity.getProductDirectorName());
            testPlanVO.setStartDate(tmTestPlanEntity.getStartDate());
            testPlanVO.setStartDay(null == tmTestPlanEntity.getStartDate() ? null : simpleDateFormat.format(tmTestPlanEntity.getStartDate()));
            testPlanVO.setPublishDate(tmTestPlanEntity.getPublishDate());
            testPlanVO.setPublishDay(null == tmTestPlanEntity.getPublishDate() ? null : simpleDateFormat.format(tmTestPlanEntity.getPublishDate()));
            testPlanVO.setPresentationDate(tmTestPlanEntity.getAccessDate());
            testPlanVO.setPresentationDay(TestPlanDatePartitionEnum.AM == tmTestPlanEntity.getAccessDatePartition() ?
                    String.valueOf(tmTestPlanEntity.getAccessDate()) : null);
            testPlanVO.setApprovalExitDate(tmTestPlanEntity.getPermitDate());
            testPlanVO.setApprovalExitDay(TestPlanDatePartitionEnum.AM == tmTestPlanEntity.getPermitDatePartition() ?
                    String.valueOf(tmTestPlanEntity.getPermitDate()) : null);
            list.add(testPlanVO);
        }
        return list;
    }

    public SimpleListVersionVO queryVersionList(List<String> codes) {
        List<String> codesNew = codes.stream().distinct().collect(Collectors.toList());
        SimpleListVersionVO vo = new SimpleListVersionVO();
        if (CollectionUtil.isEmpty(codesNew)) {
            return vo;
        }
        try {
            SimpleVersionListQuery query = new SimpleVersionListQuery();
            query.setCode(codesNew);
            vo = iProjectRpcService.simpleVersionListQuery(query);
        } catch (Exception e) {
            log.info("query version info is error codes {} , e {}", codes, e);
        }
        return vo;
    }

    /**
     * da1 实际时间   da2 计划时间    实际与计划时间差
     *
     * @param da1 2022-04-13 18:00:00     2022-04-14 09:00:00
     *            实际                    计划
     * @param da2
     * @return
     */
    public double getDelay(Date da1, Date da2) {
        if (null == da1 || null == da2) {
            return 0;
        }
        java.text.DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date da3 = new Date();
        Date da4 = new Date();
        try {
            da3 = format.parse(format.format(da1));
            da4 = format.parse(format.format(da2));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        int days = 0;
        Double delay = 0.0;
        if (null != da1 && null != da2) {
            log.info("进入if---------------+" + da1 + "--------+" + da2);
            days = Math.abs((int) ((da3.getTime() - da4.getTime()) / (1000 * 3600 * 24)));
            if (da1.getTime() < da2.getTime()) {
                days = -days;
            }
            if (!ifAfternoonByDate(da1) && ifAfternoonByDate(da2)) {
                delay = days + 0.5;
            } else if (ifAfternoonByDate(da1) && !ifAfternoonByDate(da2)) {
                delay = days - 0.5;
            } else {
                delay = (double) days;
            }
            log.info("最后时间------+" + delay);
        }
        return delay;
    }

    public boolean ifAfternoonByDate(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH");
        String format = simpleDateFormat.format(date);
        if (Integer.parseInt(format) < 12) {
            return true;
        } else {
            return false;
        }
    }

    public String getPrdLeader(String versionCode) {
        VersionVO vo = iProjectRpcService.findVersionQuery(versionCode);
        if (Objects.nonNull(vo)) {
            SimpleQueryVO productDetailVO = productRpcService.getProductVO(vo.getProductCode());
            if (Objects.nonNull(vo)) {
                return productDetailVO.getProductUserName();
            }
        }
        return null;
    }

    public List<TestReportResultVO> query(ReportTestResultQuery query) {
        List<TestReportResultVO> testReportResultVOList = new LinkedList<>();

        if (CollectionUtil.isEmpty(query.getVersionCodeList())) {
            return testReportResultVOList;
        }
        List<ReportType> types = new LinkedList<>();
        types.add(ReportType.TEST_PERMIT);
        types.add(ReportType.SIMPLE_PROCESS);
        types.add(ReportType.CHECED_TEST);
        List<TmTestReportEntityDO> entities = tmTestReportRepository.findSentEmailList(query.getVersionCodeList(), types);
        query.getVersionCodeList().forEach(t -> {
            List<TmTestReportEntityDO> versionEntities = entities.stream().filter(e -> t.equals(e.getVersionCode())).collect(Collectors.toList());
            TestReportResultVO vo = getTestReportResultVO(t, versionEntities);
            if (!"PASS".equals(vo.getResult())) {
                testReportResultVOList.add(vo);
            }
        });
        return testReportResultVOList;
    }

    private TestReportResultVO getTestReportResultVO(String versionCode, List<TmTestReportEntityDO> entities) {
        TestReportResultVO vo = new TestReportResultVO();
        SimpleVersionQuery simpleVersionQuery = new SimpleVersionQuery();
        simpleVersionQuery.setCode(versionCode);
        try {
            SimpleVersionVO versionVO = projectRpcService.simpleVersionQuery(simpleVersionQuery);
            vo.setVersionCode(versionCode);
            vo.setVersionName(versionVO.getName());
        } catch (Exception e) {
            log.error("TestReportResultVO.getTestReportResultVO版本不存在:{}", e.getMessage());
            vo.setResult("PASS");
            return vo;
        }
        if (CollectionUtil.isEmpty(entities)) {
            vo.setResult("NO_SEND");
            vo.setReason("未发送相关报告");
            return vo;
        }
        List<TmTestResultEnum> passEnums = new LinkedList<>();
        passEnums.add(TmTestResultEnum.PASS);
        passEnums.add(TmTestResultEnum.ONCE_SUCCESS);
        passEnums.add(TmTestResultEnum.FILTER_SMOKE_SUCCESS);
        passEnums.add(TmTestResultEnum.MORE_SUCCESS);
        boolean passed = entities.stream().anyMatch(t -> passEnums.contains(t.getTestResult()));
        if (passed) {
            vo.setResult("PASS");
            return vo;
        }
        vo.setResult("NO_PASS");
        vo.setReason("测试报告结果未通过");
        return vo;
    }

    /**
     * 是否包含ZUI应用
     *
     * @param productCode 产品code
     * @param versionCode 版本code
     * @return {@link Boolean}
     */
    public Boolean containsZUI(String productCode, String versionCode) {
        //当前版本关联应用列表
        List<String> appIdList = iPipelineRpcService.findVersionAppIdQuery(versionCode);
        log.info("findVersionAppIdQuery_resp-> {}", JsonUtil.toJSON(appIdList));
        if (CollectionUtils.isEmpty(appIdList)) {
            return Boolean.FALSE;
        }

        //当前产品zui应用列表
        List<String> zuiList = getZUIAppListByProductCode(productCode);
        log.info("getZUIAppListByProductCode_resp-> {}", JsonUtil.toJSON(zuiList));
        if (CollectionUtils.isEmpty(zuiList)) {
            return Boolean.FALSE;
        }

        //数据是否有重合部分
        Set<String> resultSet = appIdList.stream().filter(item -> (zuiList.contains(item))).collect(Collectors.toSet());
        log.info("filter_zui_set-> {}", JsonUtil.toJSON(resultSet));
        return CollectionUtils.isNotEmpty(resultSet);
    }

    /**
     * 查询门户接口，筛选当前产品ZUI应用列表
     *
     * @param productCode 产品code
     * @return 应用id列表
     */
    public List<String> getZUIAppListByProductCode(String productCode) {
        List<String> appList = new ArrayList<>();
        if (StringUtils.isBlank(productCode)) {
            return appList;
        }

        if (redisService.hasKey("ZUI_APP_PRODUCT_CODE_" + productCode)) {
            String appValue = redisService.getKey("ZUI_APP_PRODUCT_CODE_" + productCode);
            appList = StringUtils.isNotBlank(appValue) ?
                    Arrays.asList(appValue.split(","))
                    : new ArrayList<>();
            return appList;
        }

        try {
            appList = new ArrayList<>(izuiRpcService.getZUIAppListByProductCode(productCode));
        } catch (Exception e) {
            throw new ServiceException("获取ZUI应用失败，请稍后重试！");
        }
        redisService.setKey("ZUI_APP_PRODUCT_CODE_" + productCode,
                CollectionUtils.isEmpty(appList) ? "" : String.join(",", appList),
                24,
                TimeUnit.HOURS);
        return appList;
    }

    /**
     * 发送线上冒烟报告之前，校验准出报告测试结果.
     *
     * @param planCode 测试计划code
     * @return {@link QueryPermitResultVO}
     */
    public QueryPermitResultVO queryPermitResult(String planCode) {
        if (StringUtils.isBlank(planCode)) {
            return QueryPermitResultVO.init();
        }

        TmTestReportEntityDO entityDO = tmTestReportRepository.getReportByPlanCodeAndReportType(planCode, ReportType.TEST_PERMIT);
        if (null == entityDO || !entityDO.getStatus().equals(TestPlanStatusEnum.NORMAL)) {
            return QueryPermitResultVO.init();
        }

        return QueryPermitResultVO.builder()
                .testResult(entityDO.getTestResult())
                .sentFlag(Boolean.TRUE)
                .build();
    }

    public List<TestReportDateByVersionCodeVO> ListReportDateByVersionCodes(ListReportDateByVersionCodesQuery query) {
        List<TmTestReportEntityDO> reportList = tmTestReportRepository.listByVersionCodesAndTypes(query.getCodeList(),
                Arrays.asList(TestReportTypeEnum.TEST_ACCESS,
                        TestReportTypeEnum.TEST_PERMIT,
                        TestReportTypeEnum.ONLINE_SMOKE,
                        TestReportTypeEnum.SIMPLE_PROCESS));
        if (CollectionUtil.isEmpty(reportList)) {
            return new ArrayList<>();
        }
        Map<String, List<TmTestReportEntityDO>> versionMap = reportList.stream().collect(Collectors.groupingBy(TmTestReportEntityDO::getVersionCode));
        return versionMap.values().stream().map(this::buildReportDate).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private TestReportDateByVersionCodeVO buildReportDate(List<TmTestReportEntityDO> entities) {
        if (CollectionUtil.isEmpty(entities)) {
            return null;
        }
        TestReportDateByVersionCodeVO vo = new TestReportDateByVersionCodeVO();
        entities.stream().filter(item->(StringUtils.isNotBlank(item.getVersionCode()))).findFirst().ifPresent(item-> vo.setCode(item.getVersionCode()));

        Map<ReportType, List<TmTestReportEntityDO>> typeMap = entities.stream()
                .filter(item->(Objects.nonNull(item.getReportType())))
                .collect(Collectors.groupingBy(TmTestReportEntityDO::getReportType));

        //简易流程测试报告
        List<TmTestReportEntityDO> simpleProcessList = typeMap.get(ReportType.SIMPLE_PROCESS);
        if (CollectionUtils.isNotEmpty(simpleProcessList)) {
            TmTestReportEntityDO simpleProcess = simpleProcessList.get(0);
            vo.setActualPresentationDate(simpleProcess.getActualPresentationDate());
            vo.setActualApprovalExitDate(simpleProcess.getActualApprovalExitDate());
            vo.setActualOnlineDate(simpleProcess.getActualOnlineDate());
            return vo;
        }

        //标准测试流程报告
        Optional.ofNullable(typeMap.get(ReportType.TEST_ACCESS))
                .filter(list -> !list.isEmpty())
                .ifPresent(list -> vo.setActualPresentationDate(list.get(0).getActualPresentationDate()));
        Optional.ofNullable(typeMap.get(ReportType.TEST_PERMIT))
                .filter(list -> !list.isEmpty())
                .ifPresent(list -> vo.setActualApprovalExitDate(list.get(0).getActualApprovalExitDate()));
        Optional.ofNullable(typeMap.get(ReportType.ONLINE_SMOKE))
                .filter(list -> !list.isEmpty())
                .ifPresent(list -> vo.setActualOnlineDate(list.get(0).getActualOnlineDate()));
        return vo;
    }
}
