package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.AddTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.RemoveTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagRemovedEvent;
import com.zto.devops.qc.domain.converter.TestcaseDomainConverter;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.repository.TagRepository;
import com.zto.devops.qc.domain.gateway.repository.TestcaseTagRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TestcaseTagCommendDomainService extends BaseDomainService {

    @Autowired
    private TestcaseTagRepository testcaseTagRepository;

    @Autowired
    private TagRepository tagRepository;

    @Autowired
    private TestcaseDomainConverter converter;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    public void removeTestcaseTagCommand(RemoveTestcaseTagCommand command) {
        log.info("RemoveTestcaseTagCommand >>> {}", command.getAggregateId());
        TagVO tagVO = tagRepository.findTagByCode(command.getAggregateId());
        TestcaseTagRemovedEvent event = converter.converter(command);
        event.setAggregateId(command.getAggregateId());
        event.setBusinessCode(tagVO.getBusinessCode());
        event.setTagName(tagVO.getTagName());
        testcaseTagRepository.removeTestcaseTag(event);
        apply(event);
    }

    public void addTestcaseTagCommand(AddTestcaseTagCommand command) {
        log.info("AddTestcaseTagCommand >>> {}", command.getAggregateId());
        TestcaseTagAddedEvent event = converter.converter(command);
        event.setAggregateId(command.getAggregateId());
        this.addTestcaseTag(event);
        apply(event);
    }

    public void addTestcaseTag(TestcaseTagAddedEvent event) {

        int count1 = testcaseTagRepository.selectCountByExample(event.getBusinessCode(), event.getTagName());
        if (count1 > 0) {
            return;
        }
        int count2 = testcaseTagRepository.selectCountByExample(event.getBusinessCode(),event.getDomain());
        int productTagMax = qcConfigBasicService.getProductTagMax();
        int testcaseTagMax = qcConfigBasicService.getTestcaseTagMax();
        if (DomainEnum.PRODUCT.equals(event.getDomain()) && count2 >= productTagMax) {
            throw new ServiceException(String.format("每个产品最多可新增%s个自定义标签！", productTagMax));
        }
        if (DomainEnum.TESTCASE.equals(event.getDomain()) && count2 >= testcaseTagMax) {
            throw new ServiceException(String.format("每个用例最多可关联%s个标签！", testcaseTagMax));
        }
        testcaseTagRepository.addTestcaseTag(event);
    }
}
