package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.dto.AutomaticPreExecutionEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticSchedulerEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListAutomaticTaskCaseQuery;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.AutomaticSchedulerDetailVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.PageSchedulerVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.ProductSchedulerVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.SchedulerCaseVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.*;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.*;

import java.util.Date;
import java.util.List;

public interface AutomaticSchedulerRepository {

    void add(AutomaticSchedulerAddedEvent event);

    void edit(AutomaticSchedulerEditedEvent event);

    void delete(AutomaticSchedulerDeletedEvent event);

    void addCases(AddSchedulerCasesEvent event);

    void removeCases(RemoveSchedulerCasesEvent event);

    void updateOssTag(String productCode, String executeSpaceCode,String ossTag);

    List<TestcaseVO> execute(AutomaticSchedulerExecutionEvent event);

    void updatePreExecution(AutomaticPreExecutionUpdateEvent updateEvent);

    AutomaticSchedulerDetailVO query(SchedulerDetailQuery query);

    List<SchedulerCaseVO> query(ListSchedulerCaseQuery query);

    List<SchedulerCaseVO> query(SchedulerModuleListQuery query);

    List<ProductSchedulerVO> query(ProductSchedulerQuery query);

    List<String> query(SchedulerCaseCodeListQuery query);

    PageSchedulerVO query(PageSchedulerQuery query);

    List<TestcaseVO> query(ListAutomaticTaskCaseQuery query);

    void updateByPrimaryKeySelective(AutomaticSchedulerEntityDO entity);

    List<AutomaticPreExecutionEntityDO>  getCodeListFromPreExecute(Date currentTime);

    AutomaticSchedulerEntityDO selectByPrimaryKey(String code);

    List<String> getCaseCodesBySchedulerCode(String schedulerCode);

    List<TestcaseVO> getTestcaseByCaseCodes(List<String> caseCodes);

    List<String> selectSchedulerByCaseCodes(List<String> caseCodes);

    void deleteSchedulerCaseByCodes(List<String> caseCodes);

    void batchInsert(List<SchedulerCaseVO> newCaseList);
}
