package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.dto.IssueEntityDO;
import com.zto.devops.qc.client.model.issue.command.AddVersionIssueCommand;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.query.ExpIssueQuery;
import com.zto.devops.qc.client.model.issue.query.IssueQuery;
import com.zto.devops.qc.client.model.parameter.IssueQueryParameter;
import com.zto.devops.qc.client.model.issue.query.PageIssueQuery;
import com.zto.devops.qc.client.model.parameter.IssueQueryParameter;
import com.zto.devops.qc.client.model.rpc.project.RelatedItemVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface IssueVOConverter {

    IssueVOConverter INSTANCE = Mappers.getMapper(IssueVOConverter.class);

    @Mapping(target = "tagName", source = "creator")
    @Mapping(target = "updateUserId", source = "modifierId")
    @Mapping(target = "updateUserName", source = "modifier")
    @Mapping(target = "fixVersionCode",expression = "java(com.zto.devops.qc.client.enums.constants.DefaultValueEnum.VERSION_VALUE.isDefaultValue(entity.getFixVersionCode())?null:entity.getFixVersionCode())")
    //@Mapping(target = "fixVersionName",expression = "entity.fixVersionName().equals(\"\")?null:entity.fixVersionName()")
    @Mapping(target = "fixVersionName",expression = "java((com.zto.devops.framework.common.util.StringUtil.isNotBlank(entity.getFixVersionName()))?null: entity.getFixVersionName())")
    IssueVO convert(IssueEntityDO entity);

    List<IssueVO> convert(List<IssueEntityDO> entity);


//    IssueBaseVO convertIssueBaseVO(IssueEntity entity);
//
//    List<IssueBaseVO> convertIssueBaseVOList(List<IssueEntity> entities);
//
//    List<IssueVO> convertSimple(List<IssueEntity> entity);

    IssueQueryParameter convert(PageIssueQuery query);

//    IssueQueryParameter convert(PageLaneIssueQuery query);
//
    IssueQueryParameter convert(IssueQuery query);
//
//    List<IssueLegacyVO> convertIssueLegacyVOList(List<IssueEntity> entity);
//
//    IssueQueryParameter convert(PageIssueThingQuery query);
//
//    @Mapping(target = "testUserName", source = "tester.userName")
//    @Mapping(target = "developUserName", source = "developer.userName")
//    @Mapping(target = "sprintName", source = "sprint.name")
//    @Mapping(target = "fixVersionName", source = "fixVersion.name" )
//    @Mapping(target = "findVersionName", source = "findVersion.name" )
//    IssueVO covert(IssueEditedEvent event);

    List<AddVersionIssueCommand> convertVersion(List<RelatedItemVO> vos);

    PageIssueQuery convertQuery(ExpIssueQuery query);

}
