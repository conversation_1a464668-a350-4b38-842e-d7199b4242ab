package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.issue.command.AddAttachmentCommand;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface AttachmentDomainConverter {

    AttachmentDomainConverter INSTANCE = Mappers.getMapper(AttachmentDomainConverter.class);

    AttachmentVO convert(AddAttachmentCommand command);

}
