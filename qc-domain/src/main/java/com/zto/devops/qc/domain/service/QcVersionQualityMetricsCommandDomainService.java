package com.zto.devops.qc.domain.service;

import com.google.common.util.concurrent.AtomicDouble;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.issue.IssueFindStage;
import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueRootCause;
import com.zto.devops.qc.client.enums.testmanager.quality.VersionQualityMetricTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.report.TestReportTypeEnum;
import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.issue.entity.HolidayVO;
import com.zto.devops.qc.client.model.issue.query.FindHolidayQuery;
import com.zto.devops.qc.client.model.rpc.project.VersionVO;
import com.zto.devops.qc.client.model.testmanager.report.command.ComputeMetricsCommand;
import com.zto.devops.qc.client.model.testmanager.report.event.TestReportSendEvent;
import com.zto.devops.qc.domain.gateway.repository.IIssueRepository;
import com.zto.devops.qc.domain.gateway.repository.ITmTestPlanRepository;
import com.zto.devops.qc.domain.gateway.repository.ITmTestReportRepository;
import com.zto.devops.qc.domain.gateway.repository.QcVersionQualityMetricsRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class QcVersionQualityMetricsCommandDomainService extends BaseDomainService {

    @Autowired
    private QcVersionQualityMetricsRepository qcVersionQualityMetricsRepository;
    @Autowired
    private IIssueRepository issueRepository;
    @Autowired
    private IProjectRpcService projectRpcService;
    @Autowired
    private ITmTestPlanRepository testPlanRepository;
    @Autowired
    private ITmTestReportRepository testReportRepository;

    @Transactional
    public void computeIssueMetrics(TestReportSendEvent testReportSendEvent) {
        if (Objects.isNull(testReportSendEvent)
                || StringUtil.isBlank(testReportSendEvent.getVersionCode())
                || StringUtil.isBlank(testReportSendEvent.getPlanCode())) {
            log.error("computeIssueMetrics_param_error:{}", testReportSendEvent.getReportCode());
            return;
        }
        //测试计划
        TmTestPlanEntityDO testPlan = testPlanRepository.getTestPlanByCode(testReportSendEvent.getPlanCode());
        if (Objects.isNull(testPlan)) {
            log.error("computeIssueMetrics_testPlan_is_null:{}", testReportSendEvent.getReportCode());
            return;
        }
        //版本
        VersionVO version = projectRpcService.findVersionQuery(testReportSendEvent.getVersionCode());
        if (Objects.isNull(version)) {
            log.error("computeIssueMetrics_version_is_null:{}", testReportSendEvent.getReportCode());
            return;
        }
        //缺陷总数
        List<IssueEntityDO> issues = issueRepository.selectByVersionCode(version.getCode());
        if (CollectionUtil.isEmpty(issues)) {
            log.info("computeIssueMetrics_issue_is_empty:{}", version.getCode());
            return;
        }
        List<QcVersionQualityMetricsEntityDO> entityDOS = new ArrayList<>();
        //人天缺陷数
        double defectsPerPersonDay = getDefectsPerPersonDay(issues, version.getStartDate(), testReportSendEvent.getActualApprovalExitDate(),
                testPlan.getDeveloperNum());
        entityDOS.add(buildEntity(version, VersionQualityMetricTypeEnum.DEFECTS_PER_PERSON_DAY, String.valueOf(defectsPerPersonDay), testReportSendEvent.getTransactor()));
        //冒烟阶段缺陷数
        List<IssueEntityDO> smokeIssues = issues.stream().filter(issue -> IssueFindStage.ACCESS_SMOKING_STAGE.equals(issue.getFindStage())).collect(Collectors.toList());
        long accessSmokingIssueCount = CollectionUtil.isEmpty(smokeIssues) ? 0 : smokeIssues.size();
        entityDOS.add(buildEntity(version, VersionQualityMetricTypeEnum.SMOKE_ISSUE_COUNT, String.valueOf(accessSmokingIssueCount), testReportSendEvent.getTransactor()));
        //冒烟缺陷开发人员名单
        if (accessSmokingIssueCount > 0) {
            String developers = smokeIssues.stream().map(IssueEntityDO::getDevelopUserName).filter(StringUtil::isNotBlank).distinct().collect(Collectors.joining(","));
            entityDOS.add(buildEntity(version, VersionQualityMetricTypeEnum.SMOKE_ISSUE_DEVELOPER_NAMES, developers, testReportSendEvent.getTransactor()));
        }
        batchDelete(testReportSendEvent.getVersionCode(), testReportSendEvent.getTransactor());
        batchInsert(entityDOS);
    }

    /**
     * 获取人天缺陷数
     * 【有效缺陷总数加权-环境因素缺陷数加权】 / 【（准出时间 - 开发开始时间）* 开发人数】
     */
    private double getDefectsPerPersonDay(List<IssueEntityDO> issues,
                                          Date startDate,
                                          Date actualApprovalExitDate,
                                          Integer developerCount) {
        if (CollectionUtil.isEmpty(issues) || Objects.isNull(startDate)
                || Objects.isNull(actualApprovalExitDate)
                || Objects.isNull(developerCount) || developerCount < 1) {
            return 0;
        }
        List<String> issueCodes = issues.stream()
                .map(IssueEntityDO::getCode)
                .filter(StringUtil::isNotBlank)
                .distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(issueCodes)) {
            return 0;
        }
        //缺陷加权
        double issueWeight = getIssueWeight(issues);
        long days = calculateDaysExcludeHoliday(startDate, actualApprovalExitDate);
        double defectsPerPersonDay = days < 1 ? 0 : issueWeight / days / developerCount;
        return Double.parseDouble(String.format("%.2f", defectsPerPersonDay));
    }

    private double getIssueWeight(List<IssueEntityDO> issues) {
        AtomicDouble invalidWeight = new AtomicDouble();
        AtomicDouble envWeight = new AtomicDouble();
        issues.forEach(issue -> {
            if (IssueRootCause.ENV_ISSUE_BUG.equals(issue.getRootCause())) {
                calculateWeight(issue.getPriority(), envWeight);
            } else if (issue.getIsValid()) {
                calculateWeight(issue.getPriority(), invalidWeight);
            }
        });
        double issueWeight = invalidWeight.get() - envWeight.get();
        return issueWeight < 0 ? 0 : issueWeight;
    }

    /**
     * 计算间隔天数，排除节假日
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link java.lang.Long}
     */
    private long calculateDaysExcludeHoliday(Date startDate, Date endDate) {
        //计算间隔天数，包含起止日期
        long totalDays = ChronoUnit.DAYS.between(startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()) + 1;
        if (totalDays < 1) {
            return totalDays;
        }
        //查询节假日
        FindHolidayQuery findHolidayQuery = new FindHolidayQuery();
        findHolidayQuery.setStartDate(startDate);
        findHolidayQuery.setEndDate(endDate);
        findHolidayQuery.setType(Arrays.asList(1, 2, 3));
        List<HolidayVO> holidayVOS = projectRpcService.findHoliday(findHolidayQuery);
        return CollectionUtil.isNotEmpty(holidayVOS) ? totalDays - holidayVOS.size() : totalDays;
    }

    /**
     * 根据等级加权
     *
     * @param priority 等级
     * @param result   权重
     */
    private void calculateWeight(IssuePriority priority, AtomicDouble result) {
        switch (priority) {
            case URGENCY:
                result.addAndGet(6);
                break;
            case HIGH:
                result.addAndGet(3);
                break;
            case MIDDLE:
                result.addAndGet(1);
                break;
            case LOW:
                result.addAndGet(0.5);
                break;
        }
    }

    private void batchInsert(List<QcVersionQualityMetricsEntityDO> entityDOS) {
        entityDOS = entityDOS.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(entityDOS)) {
            return;
        }
        entityDOS.forEach(item -> qcVersionQualityMetricsRepository.insertSelective(item));
    }

    private QcVersionQualityMetricsEntityDO buildEntity(VersionVO version,
                                                        VersionQualityMetricTypeEnum type,
                                                        String value,
                                                        User user) {
        QcVersionQualityMetricsEntityDO entityDO = new QcVersionQualityMetricsEntityDO();
        entityDO.setVersionCode(version.getCode());
        entityDO.setVersionName(version.getName());
        entityDO.setProductCode(version.getProductCode());
        entityDO.setProductName(version.getProductName());
        entityDO.setMetricType(type);
        entityDO.setMetricValue(value);
        entityDO.preCreate(user);
        return entityDO;
    }

    /**
     * 根据版本code，批量删除度量数据
     *
     * @param versionCode 版本code
     */
    private void batchDelete(String versionCode, User user) {
        List<VersionQualityMetricTypeEnum> types = Arrays.asList(VersionQualityMetricTypeEnum.DEFECTS_PER_PERSON_DAY,
                VersionQualityMetricTypeEnum.SMOKE_ISSUE_COUNT,
                VersionQualityMetricTypeEnum.SMOKE_ISSUE_DEVELOPER_NAMES);
        qcVersionQualityMetricsRepository.batchDeleteByVersionCodeAndTypes(versionCode, types, user);
    }

    public void computeMetrics(ComputeMetricsCommand command) {
        if (CollectionUtil.isEmpty(command.getVersionCodes())) {
            return;
        }
        //仅计算标准测试+简易测试
        List<TmTestReportEntityDO> reportEntities = testReportRepository.listByVersionCodesAndTypes(command.getVersionCodes(),
                Arrays.asList(TestReportTypeEnum.TEST_PERMIT, TestReportTypeEnum.SIMPLE_PROCESS));
        if (CollectionUtil.isEmpty(reportEntities)) {
            return;
        }
        reportEntities.forEach(item -> {
            TestReportSendEvent event = new TestReportSendEvent();
            event.setAggregateId(item.getReportCode());
            event.setReportCode(item.getReportCode());
            event.setVersionCode(item.getVersionCode());
            event.setPlanCode(item.getPlanCode());
            event.setActualApprovalExitDate(item.getActualApprovalExitDate());
            event.setTransactor(command.getTransactor());
            computeIssueMetrics(event);
        });
    }
}
