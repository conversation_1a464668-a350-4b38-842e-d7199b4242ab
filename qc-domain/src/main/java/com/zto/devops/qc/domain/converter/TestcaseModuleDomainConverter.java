package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.testmanager.cases.command.AddTestcaseCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.DragAutomaticRecordCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.MoveAutomaticRecordCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.MoveModuleCommand;
import com.zto.devops.qc.client.model.testmanager.cases.event.AddTestcaseEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticRecordDraggedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.MoveModuleEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface TestcaseModuleDomainConverter {
    TestcaseModuleDomainConverter INSTANCE = Mappers.getMapper(TestcaseModuleDomainConverter.class);

    @Mapping(target = "code", source = "aggregateId")
    AddTestcaseEvent convertor(AddTestcaseCommand command);

    @Mapping(target = "code", source = "aggregateId")
    AutomaticRecordDraggedEvent convertor(DragAutomaticRecordCommand command);

    MoveModuleEvent converter(MoveAutomaticRecordCommand command);

    MoveModuleEvent converter(MoveModuleCommand command);

}
