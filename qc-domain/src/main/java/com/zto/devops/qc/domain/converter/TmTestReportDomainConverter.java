package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.dto.QcNoticeResultEntityDO;
import com.zto.devops.qc.client.model.dto.ReviewInfoEntityDO;
import com.zto.devops.qc.client.model.dto.TestFunctionPointEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestReportEntityDO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.report.query.PageReportQuery;
import com.zto.devops.qc.client.model.rpc.user.UserSelectVO;
import com.zto.devops.qc.client.model.testPlan.entity.TestFunctionPointVO;
import com.zto.devops.qc.client.model.testmanager.coverage.event.CoverageReasonEditEvent;
import com.zto.devops.qc.client.model.testmanager.report.command.*;
import com.zto.devops.qc.client.model.testmanager.report.entity.*;
import com.zto.devops.qc.client.model.testmanager.report.event.*;
import com.zto.devops.qc.client.model.testmanager.report.query.PageReportMqQuery;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TmTestReportDomainConverter {

    TmAccessReportDetailVO convertAccess(TmTestReportEntityDO report);

    @Mapping(target = "actualPublishDate", source = "actualOnlineDate")
    TmSmokeReportDetailVO convertSmoke(TmTestReportEntityDO report);

    TmPermitReportDetailVO convertPermit(TmTestReportEntityDO report);

    @Mapping(target = "reportUserId", source = "modifierId")
    @Mapping(target = "reportUserName", source = "modifier")
    @Mapping(target = "actualPublishDate", source = "actualOnlineDate")
    ExternalTestReportDetailVO convertExternalVO(TmTestReportEntityDO report);

    TmAccessReportAddEvent converter(AddTmAccessReportCommand command);

    TmAccessReportEditEvent converter(EditTmAccessReportCommand command);

    @Mapping(target = "actualOnlineDate", source = "actualPublishDate")
    TmOnlineSmokeReportAddEvent converter(AddTmOnlineSmokeReportCommand command);

    @Mapping(target = "actualOnlineDate", source = "actualPublishDate")
    TmOnlineSmokeReportEditEvent converter(EditTmOnlineSmokeReportCommand command);

    ReviewReportAddedEvent convert(AddAndSendReviewReportCommand command);

    TestReportSendEvent convertTestReportSendEvent(EditAndSendReviewReportCommand command);

    TestReportSendEvent convertTestReportSendEvent(AddAndSendReviewReportCommand command);

    ReviewReportEditedEvent convert(EditAndSendReviewReportCommand command);

    TmPermitReportAddEvent converter(AddTmPermitReportCommand command);

    TmPermitReportEditedEvent converter(EditTmPermitReportCommand command);

    SimpleReportAddedEvent convert(AddAndSendSimpleTestReportCommand command);

    CoverageReasonEditEvent convertAddSimpleReasonEvent(AddAndSendSimpleTestReportCommand command);

    TestReportSendEvent convertTestReportSendEvent(AddAndSendSimpleTestReportCommand command);

    SimpleTestReportEditEvent convert(EditAndSendSimpleTestReportCommand command);

    CoverageReasonEditEvent convertEditSimpleReasonEvent(EditAndSendSimpleTestReportCommand command);

    TestReportSendEvent convertTestReportSendEvent(EditAndSendSimpleTestReportCommand command);

    @Mapping(target = "reportUserId", source = "modifierId")
    @Mapping(target = "reportUserName", source = "modifier")
    ReviewReportDetailVO convertReviewVO(TmTestReportEntityDO report);

    @Mapping(target = "reviewTypeDesc", expression = "java(com.zto.devops.qc.client.enums.testmanager.report.ReviewType.getDesc(entityDO.getReviewType()))")
    ReviewInfoUserHandleVO convertReviewInfoHandle(ReviewInfoEntityDO entityDO);

    List<ReviewUserVO> convertUser(List<UserSelectVO> userSelectVOList);

    @Mapping(target = "reportUserId", source = "modifierId")
    @Mapping(target = "reportUserName", source = "modifier")
    MobileTestReportDetailVO convertMobileVO(TmTestReportEntityDO entityDO);

    ReviewReportAddedEvent converter(AddReviewReportCommand command);

    ReviewReportEditedEvent convert(EditReviewReportCommand command);

    @Mapping(target = "actualOnlineDate", source = "actualPublishDate")
    ExternalReportAddEvent convert(AddExternalTestReportCommand command);

    @Mapping(target = "actualOnlineDate", source = "actualPublishDate")
    ExternalReportEditEvent convert(EditExternalTestReportCommand command);

    MobileTestReportAddedEvent convert(AddMobileTestReportCommand command);

    MobileTestReportEditEvent convert(EditMobileTestReportCommand command);

    SimpleTestReportDetailVO convertSimpleVO(TmTestReportEntityDO entityDO);

    SimpleReportAddedEvent convert(AddSimpleTestReportCommand command);

    SimpleTestReportEditEvent convert(EditSimpleTestReportCommand command);

    TmAccessReportAddEvent convert(AddAndSendTmAccessTestReportCommand command);

    TestReportSendEvent convertEmailSendEvent(AddAndSendTmAccessTestReportCommand command);

    TmAccessReportEditEvent convert(EditAndSendTmAccessTestReportCommand command);

    TestReportSendEvent convertEmailSendEvent(EditAndSendTmAccessTestReportCommand command);

    @Mapping(target = "actualOnlineDate", source = "actualPublishDate")
    ExternalReportAddEvent convert(AddAndSendExternalTestReportCommand command);

    @Mapping(target = "actualOnlineDate", source = "actualPublishDate")
    TestReportSendEvent convertTestReportSendEvent(AddAndSendExternalTestReportCommand command);

    @Mapping(target = "actualOnlineDate", source = "actualPublishDate")
    ExternalReportEditEvent convert(EditAndSendExternalTestReportCommand command);

    @Mapping(target = "actualOnlineDate", source = "actualPublishDate")
    TestReportSendEvent convertTestReportSendEvent(EditAndSendExternalTestReportCommand command);

    MobileTestReportAddedEvent convert(AddAndSendMobileTestReportCommand command);

    TestReportSendEvent convertTestReportSendEvent(AddAndSendMobileTestReportCommand command);

    MobileTestReportEditEvent convert(EditAndSendMobileTestReportCommand command);

    TestReportSendEvent convertTestReportSendEvent(EditAndSendMobileTestReportCommand command);

    @Mapping(target = "actualOnlineDate",source = "actualPublishDate")
    TmOnlineSmokeReportAddEvent convert(AddAndSendTmOnlineSmokeTestReportCommand command);

    @Mapping(target = "actualOnlineDate",source = "actualPublishDate")
    TestReportSendEvent convertEmailSendEvent(AddAndSendTmOnlineSmokeTestReportCommand command);

    @Mapping(target = "actualOnlineDate",source = "actualPublishDate")
    TmOnlineSmokeReportEditEvent convert(EditAndSendTmOnlineSmokeTestReportCommand command);

    @Mapping(target = "actualOnlineDate",source = "actualPublishDate")
    TestReportSendEvent convertEmailSendEvent(EditAndSendTmOnlineSmokeTestReportCommand command);

    TmPermitReportAddEvent convert(AddAndSendTmPermitTestReportCommand command);

    CoverageReasonEditEvent convertAddPermitReasonEvent(AddAndSendTmPermitTestReportCommand command);

    TestReportSendEvent convertEmailSendEvent(AddAndSendTmPermitTestReportCommand command);

    TmPermitReportEditedEvent convert(EditAndSendTmPermitTestReportCommand command);

    CoverageReasonEditEvent convertEditPermitReasonEvent(EditAndSendTmPermitTestReportCommand command);

    TestReportSendEvent convertEmailSendEvent(EditAndSendTmPermitTestReportCommand command);

    PageReportQuery convert(PageReportMqQuery query);

    List<TestFunctionPointVO> convertToVo(List<TestFunctionPointEntityDO> doList);

    List<SendUserInfoVO> convert(List<QcNoticeResultEntityDO> entityDOList);
}
