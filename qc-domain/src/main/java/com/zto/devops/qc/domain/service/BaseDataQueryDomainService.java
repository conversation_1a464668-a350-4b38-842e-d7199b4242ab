package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.rpc.categoryCodeEnum;
import com.zto.devops.qc.client.model.rpc.huiyan.StoreBrandVO;
import com.zto.devops.qc.client.model.rpc.huiyan.StoreNameVO;
import com.zto.devops.qc.client.model.rpc.outlet.ItemTypeVO;
import com.zto.devops.qc.client.model.rpc.pipeline.UserRespVO;
import com.zto.devops.qc.client.model.rpc.waybill.LabelNameVO;
import com.zto.devops.qc.client.service.testmanager.basedata.model.ClassListQueryReq;
import com.zto.devops.qc.client.service.testmanager.basedata.model.ItemTypeReq;
import com.zto.devops.qc.client.service.testmanager.basedata.model.StoreBrandQueryReq;
import com.zto.devops.qc.client.service.testmanager.basedata.model.TagListQueryReq;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.repository.BaseDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class BaseDataQueryDomainService extends BaseDomainService {

    @Autowired
    private BaseDataRepository baseDataRepository;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    public JSONArray queryUserInfo(String keyWord) {
        try {
            List<UserRespVO> list = baseDataRepository.queryUserAutoSearch(keyWord);
            if(CollectionUtil.isEmpty(list)) {
                return new JSONArray();
            }
            return configUserInfo(list);
        } catch (Exception e) {
            throw new ServiceException("获取不到人员信息！");
        }
    }

    private JSONArray configUserInfo(List<UserRespVO> list) {
        JSONArray resultArray = new JSONArray();
        if(CollectionUtil.isEmpty(list)) {
            return resultArray;
        }
        String personnel = qcConfigBasicService.getSceneInputParameterPersonnel();
        if("all".equals(personnel)) {
            return JSON.parseArray(JSON.toJSONString(list));
        }
        String[] paramArray = personnel.split(",");
        list.forEach(userRespVO -> {
            JSONObject userObj = JSON.parseObject(JSON.toJSONString(userRespVO));
            JSONObject configJson = new JSONObject();
            for(String key : paramArray) {
                configJson.put(key, userObj.get(key));
            }
            resultArray.add(configJson);
        });
        return resultArray;
    }

    public List<ItemTypeVO> itemType(ItemTypeReq req) {
        try{
            List<ItemTypeVO> list = baseDataRepository.queryDictionaries(categoryCodeEnum.goods_type.name());
            if(StringUtils.isNotBlank(req.getKeyWord())) {
                return list.stream().filter(vo -> vo.getName().contains(req.getKeyWord())).collect(Collectors.toList());
            }
            return list;
        } catch (Exception e) {
            throw new ServiceException("请求资料中心获取物品类型失败！");
        }
    }

    public List<LabelNameVO> queryTagList(TagListQueryReq req) {
        try {
            List<LabelNameVO> list = baseDataRepository.listAllTags();
            if(StringUtils.isNotBlank(req.getKeyWord())) {
                return list.stream().filter(vo -> vo.getLabel().contains(req.getKeyWord())).collect(Collectors.toList());
            }
            return list;
        } catch (Exception e) {
            throw new ServiceException("请求运单中心获取标签失败！");
        }
    }

    public List<ItemTypeVO> queryClassList(ClassListQueryReq req) {
        try {
            List<ItemTypeVO> list = baseDataRepository.queryDictionaries(categoryCodeEnum.transport_type.name());
            if(StringUtils.isNotBlank(req.getKeyWord())) {
                return list.stream().filter(vo -> vo.getName().contains(req.getKeyWord())).collect(Collectors.toList());
            }
            return list;
        } catch (Exception e) {
            throw new ServiceException("请求资料中心获取班次失败！");
        }
    }

    public List<StoreBrandVO> queryStoreBrand(StoreBrandQueryReq req) {
        try{
            List<StoreBrandVO> list = baseDataRepository.queryStoreBrand();
            if(StringUtils.isNotBlank(req.getKeyWord())) {
                return list.stream().filter(vo -> vo.getCompanyName().contains(req.getKeyWord())).distinct().collect(Collectors.toList());
            }
            return list.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            throw new ServiceException("请求慧眼管理端获取门店品牌失败！");
        }
    }

    public List<StoreNameVO> queryStoreName(ClassListQueryReq req) {
        try{
            return baseDataRepository.queryStoreName(req.getKeyWord(), req.getCompanyCode());
        } catch (Exception e) {
            throw new ServiceException("请求慧眼管理端获取门店名称失败！");
        }
    }

}
