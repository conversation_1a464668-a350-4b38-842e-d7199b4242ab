package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.command.ExecuteCallbackCommand;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTask;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskExecutedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskTerminatedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.ExecuteCallbackEvent;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.AutomaticSchedulerExecutionCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticPreExecutionUpdateEvent;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.AutomaticSchedulerExecutionEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface AutomaticTaskEventConverter {

    @Mapping(target = "code", source = "aggregateId")
    ExecuteCallbackEvent converter(ExecuteCallbackCommand command);

    void convert(AutomaticTask domain, @MappingTarget AutomaticTaskTerminatedEvent event);

    @Mapping(target = "tag", source = "executeTag")
    AutomaticTaskExecutedEvent convert(AutomaticTaskEntityDO entityDO);

    @Mapping(target = "schedulerCode", source = "aggregateId")
    AutomaticSchedulerExecutionEvent converter(AutomaticSchedulerExecutionCommand command);

    @Mapping(target = "schedulerCode", source = "aggregateId")
    AutomaticPreExecutionUpdateEvent convert(AutomaticSchedulerExecutionCommand command);
}
