package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.enums.impexp.ExtEnum;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.*;
import com.zto.devops.framework.common.fsm.Event;
import com.zto.devops.framework.common.fsm.State;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.HtmlCommentHandlerUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.constants.DefaultValueEnum;
import com.zto.devops.qc.client.enums.constants.HandlerTypeEnum;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.qc.client.enums.rpc.*;
import com.zto.devops.qc.client.model.common.TransitionNodeAddEvent;
import com.zto.devops.qc.client.model.common.handler.AbstractCurrentHandlerChangedEvent;
import com.zto.devops.qc.client.model.issue.command.*;
import com.zto.devops.qc.client.model.issue.entity.*;
import com.zto.devops.qc.client.model.issue.event.*;
import com.zto.devops.qc.client.model.issue.query.FindIssueByCodeQuery;
import com.zto.devops.qc.client.model.rpc.pipeline.FindVersionByNamespaceVO;
import com.zto.devops.qc.client.model.rpc.pipeline.query.FindVersionByNamespaceQuery;
import com.zto.devops.qc.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.qc.client.model.rpc.product.SimpleQueryVO;
import com.zto.devops.qc.client.model.rpc.project.*;
import com.zto.devops.qc.client.model.rpc.user.ProfileAddedSnapshotEvent;
import com.zto.devops.qc.client.service.issue.model.SlowSqlToIssueResp;
import com.zto.devops.qc.client.service.issue.model.VersionPlannedReq;
import com.zto.devops.qc.client.service.testmanager.apitest.model.QueryAppIdTagsReq;
import com.zto.devops.qc.domain.converter.IssueAddedEventConverter;
import com.zto.devops.qc.domain.converter.IssueEditedEventConverter;
import com.zto.devops.qc.domain.converter.IssueVOConverter;
import com.zto.devops.qc.domain.converter.RelevantUserConverter;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.message.QcRobotMessageService;
import com.zto.devops.qc.domain.gateway.mq.IssueMqSender;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.repository.*;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.domain.gateway.zcat.ZCatService;
import com.zto.devops.qc.domain.gateway.zim.ZimService;
import com.zto.devops.qc.domain.model.Issue;
import com.zto.devops.qc.domain.statemachine.IssueContext;
import com.zto.devops.qc.domain.statemachine.IssueStateMachineUtil;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
public class IssueCommandDomainService extends BaseDomainService {

    private static final String SLOW_SQL_ISSUE_DESC_FILE_NAME = "方舟SQL治理缺陷描述.txt";
    private static final String SLOW_SQL_ISSUE_DESC = "内容过长，请查看文档";

    @Autowired
    private IProjectRpcService iProjectRpcService;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    @Autowired
    private ZimService zimService;

    @Autowired
    private IProjectRpcService projectRpcService;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private TagRepository tagRepository;

    @Autowired
    private IIssueRepository issueRepository;

    @Autowired
    private AttachmentRepository attachmentRepository;

    @Autowired
    private IRelevantUserRepository iRelevantUserRepository;

    @Autowired
    private TransitionNodeRepository transitionNodeRepository;

    @Autowired
    private ITestcaseRelationRepository iTestcaseRelationRepository;

    @Autowired
    private IssueAddedEventConverter issueAddedEventConverter;

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private RelevantUserConverter relevantUserConverter;

    @Autowired
    private IssueEditedEventConverter issueEditedEventConverter;

    @Autowired
    private IssueMqSender issueMqSender;

    @Autowired
    private IssueTransitionNodeMqSender issueTransitionNodeMqSender;

    @Autowired
    private IssueMatterMqSender issueMatterMqSender;

    @Autowired
    private StatisticsIssueService statisticsIssueService;

    @Autowired
    private IssueVOConverter issueVOConverter;

    @Autowired
    private QcRobotMessageService qcRobotMessageService;

    @Autowired
    private ZCatService zCatService;

    @Autowired
    private IPipelineRpcService iPipelineRpcService;

    @Autowired
    private ZtoOssService ossService;

    private final static Integer ATTACHMENT_COUNT = 100;

    private final static Integer TAG_COUNT = 20;

    public void deliveryIssue(DeliveryValidatedCommand command) {
        Issue issue = issueRepository.loadFormDb(command.getAggregateId());

        IssueContext context = getIssueContext(command.getTransactor().getUserId(), command.getPermissions(), issue);
        List<Event> events = IssueStateMachineUtil.getInstance().readyEvent(issue.getStatus().toState(), context
        );
        if (!events.contains(IssueEvent.DELIVER.toEvent())) {
            throw new ServiceException("当前用户没有权限操作！");
        }

        State state = getState(issue.getStatus().toState(), IssueEvent.DELIVER.toEvent(),
                context);
        IssueDeliveryValidatedEvent event = new IssueDeliveryValidatedEvent();
        event.setAggregateId(command.getAggregateId());
        event.setCode(command.getAggregateId());
        event.setTransactor(command.getTransactor());
        event.setStatus(IssueStatus.valueOf(state.getCode()));
        event.setDeliverTime(new Date());
        event.setOccurred(new Date());
        event.setContent(command.getContent());
        event.setFixVersion(command.getFixVersion());
        event.setCurStatus(issue.getStatus());
        event.setNextStatus(IssueStatus.valueOf(state.getCode()));
        event.setTransitionNodeCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        event.setHandler(issue.getTester());
        event.setDeleteHandler(relevantUserConverter.convertCurrentHandlerVO(issue.getCurrentHandler()));
        event.setLastHandler(CurrentHandlerVO.buildSet(CurrentHandlerVO.builder()
                .businessCode(command.getAggregateId()).code(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE)).action(event.getStatus().name())
                .domain(DomainEnum.ISSUE.name()).type(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name()).handleType(HandlerTypeEnum.USER.name())
                .userId(String.valueOf(issue.getTester().getUserId())).userName(issue.getTester().getUserName())
                .build()));
        issue.setBaseAttribute(command, event);
        issueRepository.deliveryIssue(event);
        handleCurrentChangeEvent(event);
        handleMyOperatedEvent(event.getBusinessCode(), event.getOperator(), event.domain(), event.actionCode(), RelevantUserTypeEnum.HANDLED_USER);
        handleTransitionNodeAddEvent(event);
        issueMqSender.handleIssueDeliveryValidatedEvent(event);
        apply(event);
        qcRobotMessageService.sendDeliveryIssueMessageEvent(event, issue);
    }

    public String addIssue(AddIssueCommand command) {
        List<RequirementStatus> statuses = new ArrayList<>(6);
        statuses.add(RequirementStatus.ADOPTING);
        statuses.add(RequirementStatus.SCHEDULING);
        statuses.add(RequirementStatus.DEVELOPING);
        statuses.add(RequirementStatus.TESTING);
        statuses.add(RequirementStatus.FINISH);
        statuses.add(RequirementStatus.CHECKING);
        if (Objects.nonNull(command.getRequirement()) && StringUtil.isNotBlank(command.getRequirement().getCode())) {
            RequirementVO requirementVO = projectRpcService.requirementWithCodeQuery(command.getRequirement().getCode());
            //ISS220124008038 只有测试中，验收中，已完成的需求才可以关联创建缺陷
            if (null != requirementVO) {
                command.setRequirementLevel(requirementVO.getLevel() == 2 ? RequirementLevel.REQUIREMENT_CHILD : RequirementLevel.REQUIREMENT);
            }
            if (null == requirementVO || !statuses.contains(requirementVO.getStatus())) {
                throw new ServiceException("只有采纳、排期、开发、测试、验收中及已完成的需求才可以关联创建缺陷");
            }
        }
        String description = command.getDescription();
        if (StringUtil.isNotBlank(description)) {
            //缺陷描述正则匹配
            description = HtmlCommentHandlerUtil.deleteCommentInLine(description);
            command.setDescription(description);
        }
        log.info("------------------------>AggregateId:{}", command.getAggregateId());
        checkParms(command);
        if (command.getRequirement() != null && StringUtil.isNotBlank(command.getRequirement().getCode())) {
            Version fixVersion = getFixVersionByRequirementCode(command.getRequirement().getCode());
            if (fixVersion != null) {
                command.setFixVersion(fixVersion);
            }
        }
        if (command.getFindVersion() != null && StringUtil.isNotBlank(command.getFindVersion().getCode())) {
            Version fixVersion = getFixVersionByFindVersionCode(command.getFindVersion().getCode());
            if (fixVersion != null) {
                command.setFixVersion(fixVersion);
            }
        }
        IssueAddedEvent event = issueAddedEventConverter.convert(command);
        event.setStatus(IssueStatus.WAIT_FIX);
        event.setFindTime(new Date());
        event.setUpdateTime(new Date());
        event.setOccurred(new Date());
        event.setAggregateId(command.getAggregateId());
        if (Objects.nonNull(command.getFinder())) {
            event.setFinder(command.getFinder());
        }

        CurrentHandlerVO currentHandlerVO = CurrentHandlerVO.builder()
                .businessCode(command.getAggregateId()).code(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE)).action(event.getStatus().name())
                .domain(DomainEnum.ISSUE.name()).type(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name())
                .userId(String.valueOf(command.getHandler().getUserId())).userName(command.getHandler().getUserName())
                .build();
        event.setLastHandler(CurrentHandlerVO.buildSet(currentHandlerVO));
        issueRepository.addIssue(event);
        handleCurrentChangeEvent(event);
        handleMyCreatedEvent(event.getBusinessCode(), event.getOperator(), event.domain(), event.actionCode(), RelevantUserTypeEnum.CREATOR);
        issueMqSender.handleIssueAddedEvent(event);
        issueMatterMqSender.handleIssueAddedEvent(event);
        apply(event);

        if (CollectionUtil.isNotEmpty(command.getAttachments())) {
            AttachmentAddedEvent attachmentAddedEvent = new AttachmentAddedEvent();
            attachmentAddedEvent.setBusinessCode(command.getAggregateId());
            attachmentAddedEvent.setAttachments(command.getAttachments());
            attachmentAddedEvent.setTransactor(command.getTransactor());
            attachmentAddedEvent.setOccurred(new Date());
            attachmentAddedEvent.setAggregateId(command.getAggregateId());

            log.info("--------------AttachmentAddedEvent.businessCode:{} ", command.getAggregateId());
            attachmentRepository.handle(attachmentAddedEvent);
            apply(attachmentAddedEvent);
        }

        if (CollectionUtil.isNotEmpty(command.getTags())) {
            TagAddedEvent tagAddedEvent = new TagAddedEvent();
            tagAddedEvent.setBusinessCode(command.getAggregateId());
            tagAddedEvent.setTags(command.getTags());
            tagAddedEvent.setTransactor(command.getTransactor());
            tagAddedEvent.setOccurred(new Date());
            tagAddedEvent.setAggregateId(command.getAggregateId());

            log.info("TagAddedEvent------------------------>tagAddedEvent:{} ", JsonUtil.toJSON(tagAddedEvent));
            tagRepository.addTag(tagAddedEvent);
            issueMqSender.handleTagAddedEvent(tagAddedEvent);
            apply(tagAddedEvent);
        }
        if (CollectionUtil.isNotEmpty(command.getRelevantUserVOS())) {
            RelevantUserAddedEvent userAddedEvent = new RelevantUserAddedEvent();
            userAddedEvent.setBusinessCode(command.getAggregateId());
            userAddedEvent.setRelevantUserVOS(command.getRelevantUserVOS());
            userAddedEvent.setTransactor(command.getTransactor());
            userAddedEvent.setAggregateId(command.getAggregateId());

            log.info("RelevantUserAddedEvent------------------------>relevantUserAddedEvent:{} ", JsonUtil.toJSON(userAddedEvent));
            iRelevantUserRepository.ccIssue(userAddedEvent);
            apply(userAddedEvent);
        }
        if (CollectionUtil.isNotEmpty(command.getTestcaseCodes())) {
            TestcaseRelationAddEvent testcaseRelationAddEvent = new TestcaseRelationAddEvent();
            testcaseRelationAddEvent.setBusinessCode(command.getAggregateId());
            testcaseRelationAddEvent.setTestcaseCodes(command.getTestcaseCodes());
            testcaseRelationAddEvent.setTransactor(command.getTransactor());
            testcaseRelationAddEvent.setVersionCode(command.getFindVersion().getCode());
            testcaseRelationAddEvent.setAggregateId(command.getAggregateId());

            log.info("TestcaseRelationAddEvent------------------------>testcaseRelationAddEvent:{} ", JsonUtil.toJSON(testcaseRelationAddEvent));
            iTestcaseRelationRepository.addTestCase(testcaseRelationAddEvent);
            apply(testcaseRelationAddEvent);
        }

        // 日志信息要剥离
        TransitionNodeAddedEvent transitionNodeAddedEvent = new TransitionNodeAddedEvent();
        transitionNodeAddedEvent.setBusinessCode(command.getAggregateId());
        transitionNodeAddedEvent.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        transitionNodeAddedEvent.setNextStatus(event.getStatus());
        transitionNodeAddedEvent.setDomain(DomainEnum.ISSUE);
        transitionNodeAddedEvent.setAggregateId(command.getAggregateId());

        log.info("TransitionNodeAddedEvent------------------------>transitionNodeAddedEvent:{} ", JsonUtil.toJSON(transitionNodeAddedEvent));
        handleTransitionNodeAddedEvent(transitionNodeAddedEvent);
        apply(transitionNodeAddedEvent);

        //这里只发送消息
        ProfileAddedSnapshotEvent profileEvent = new ProfileAddedSnapshotEvent();
        profileEvent.setKey(ProfileKindEnum.BUG_FIELD_ADD_SNAPSHOT.name());
        profileEvent.setTransactor(event.getTransactor());
        try {
            IssueAddVO vo = new IssueAddVO();
            vo.setProductCode(event.getProduct() == null ? "" : event.getProduct().getCode());
            vo.setProductName(event.getProduct() == null ? "" : event.getProduct().getName());
            vo.setFindVersionCode(event.getFindVersion() == null ? "" : event.getFindVersion().getCode());
            vo.setFindVersionName(event.getFindVersion() == null ? "" : event.getFindVersion().getName());
            vo.setDevelopUserId(event.getDeveloper() == null ? null : event.getDeveloper().getUserId());
            vo.setDevelopUserName(event.getDeveloper() == null ? "" : event.getDeveloper().getUserName());
            profileEvent.setValue(new ObjectMapper().writeValueAsString(vo));
            profileEvent.setAggregateId(command.getAggregateId());
            apply(profileEvent);
        } catch (JsonProcessingException e) {
            log.error("AddIssueCommand.ProfileAddedSnapshotEvent:" + e.getMessage());
        }
        qcRobotMessageService.sendIssueMessageEvent(event);
        return command.getAggregateId();
    }

    public void handleAddVersionIssueCommand(AddVersionIssueCommand command) {
        //没有附件，日志，标签
        String description = command.getDescription();
        if (StringUtil.isNotBlank(description)) {
            description = HtmlCommentHandlerUtil.deleteCommentInLine(description);
            command.setDescription(description);
        }
        IssueAddedEvent event = issueAddedEventConverter.convert(command);
        event.setStatus(IssueStatus.WAIT_FIX);
        event.setFindTime(new Date());
        event.setUpdateTime(new Date());
        event.setOccurred(new Date());

        CurrentHandlerVO currentHandlerVO = CurrentHandlerVO.builder()
                .businessCode(command.getAggregateId()).code(generateId(AggregateType.SNOWFLAKE)).action(event.getStatus().name())
                .domain(DomainEnum.ISSUE.name()).type(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name())
                .userId(String.valueOf(command.getHandler().getUserId())).userName(command.getHandler().getUserName())
                .build();
        event.setLastHandler(CurrentHandlerVO.buildSet(currentHandlerVO));
        issueRepository.addIssue(event);
        handleCurrentChangeEvent(event);
        handleMyCreatedEvent(event.getBusinessCode(), event.getOperator(), event.domain(), event.actionCode(), RelevantUserTypeEnum.CREATOR);
        issueMqSender.handleIssueAddedEvent(event);
        issueMatterMqSender.handleIssueAddedEvent(event);
        apply(event);

        TransitionNodeAddedEvent transitionNodeAddedEvent = new TransitionNodeAddedEvent();
        transitionNodeAddedEvent.setAggregateId(command.getAggregateId());
        transitionNodeAddedEvent.setBusinessCode(command.getAggregateId());
        transitionNodeAddedEvent.setCode(generateId(AggregateType.SNOWFLAKE));
        // 写固定值 @see AddIssueCommandConverter.convertAfter
        transitionNodeAddedEvent.setNextStatus(IssueStatus.DELAY_FIX);
        transitionNodeAddedEvent.setDomain(DomainEnum.ISSUE);
        handleTransitionNodeAddedEvent(transitionNodeAddedEvent);
        apply(transitionNodeAddedEvent);
    }

    private void checkParms(AddIssueCommand command) {
        if (CollectionUtil.isNotEmpty(command.getAttachments()) && command.getAttachments().size() > ATTACHMENT_COUNT) {
            throw new ServiceException("附件最多可上传100个");
        }
        if (CollectionUtil.isNotEmpty(command.getTags()) && command.getTags().size() > TAG_COUNT) {
            throw new ServiceException("最多可新增20个标签");
        }
        if (command.getTags() != null) {
            Map<String, Long> mapGroup = command.getTags().stream().collect(Collectors.groupingBy(tag -> tag.getTagName(), Collectors.counting()));
            Stream<String> stringStream = mapGroup.entrySet().stream().filter(entry -> entry.getValue() > 1).map(entry -> entry.getKey());
            if (stringStream.count() > 0) {
                throw new ServiceException("标签不能重复添加");
            }
        }
    }

    public void handleSprintInIssueEditedEvent(SprintInIssueEditedEvent event) {
        issueRepository.updateBySprintInIssueEditedEvent(event);
        issueMqSender.handleSprintInIssueEditedEvent(event);
        apply(event);
    }

    public void handleLinkSprintIssueCommand(List<LinkSprintIssueCommand> commands) {
        commands.forEach(command -> {
            SprintIssueLinkedEvent event = new SprintIssueLinkedEvent();
            event.setSprintCode(command.getSprintCode());
            event.setCode(command.getAggregateId());
            event.setSprintName(command.getSprintName());
            event.setTransactor(command.getTransactor());
            event.setOccurred(command.getOccurred());
            issueRepository.updateBySprintIssueLinkedEvent(event);
            event.setAggregateId(command.getAggregateId());
            apply(event);
        });
    }

    public void issueRelatedRequirementCommand(IssueRelatedRequirementCommand command) {
        log.info("issueRelatedRequirementCommand >>> {}", JSON.toJSONString(command));
        IssueRelatedRequirementEvent event = new IssueRelatedRequirementEvent();
        event.setAggregateId(command.getAggregateId());
        event.setIssueCode(command.getAggregateId());
        event.setRequirementCode(command.getRequirementCode());
        event.setRequirementLevel(command.getRequirementLevel());
        event.setRequirementName(command.getRequirementName());
        event.setOccurred(new Date());
        event.setTransactor(command.getTransactor());
        if (StringUtil.isNotBlank(command.getRequirementCode())) {
            RequirementVO requirementVO = projectRpcService.requirementWithCodeQuery(command.getRequirementCode());
            if (null != requirementVO) {
                event.setRequirementLevel(requirementVO.getLevel() == 2 ? RequirementLevel.REQUIREMENT_CHILD : RequirementLevel.REQUIREMENT);
                if (!(RequirementStatus.FINISH.equals(requirementVO.getStatus()) || RequirementStatus.HAS_CLOSE.equals(requirementVO.getStatus()))) {
                    Version fixVersion = new Version();
                    fixVersion.setCode(StringUtil.isNotBlank(requirementVO.getVersionCode()) ? requirementVO.getVersionCode() : DefaultValueEnum.VERSION_VALUE.getValue());
                    fixVersion.setName(requirementVO.getVersionName());
                    event.setFixVersion(fixVersion);
                }
            }
        }
        issueRepository.updateIssue(event);
        issueMqSender.handleIssueRelatedRequirementEvent(event);
        apply(event);
    }

    private Version getFixVersionByFindVersionCode(String findVersionCode) {
        Version fixVersion = null;
        VersionInfoVO versionInfoVO = projectRpcService.findVersionBaseInfoQuery(findVersionCode);
        if (versionInfoVO != null && !(VersionStatus.ACCEPTING.name().equals(versionInfoVO.getStatus()) || VersionStatus.ACCEPTED.name().equals(versionInfoVO.getStatus()) || VersionStatus.CLOSED.name().equals(versionInfoVO.getStatus()))) {
            fixVersion = new Version();
            fixVersion.setCode(versionInfoVO.getCode());
            fixVersion.setName(versionInfoVO.getName());
        }
        return fixVersion;
    }

    private Version getFixVersionByRequirementCode(String requirementCode) {
        Version fixVersion = null;
        RequirementVO requirementVO = projectRpcService.requirementWithCodeQuery(requirementCode);
        if (requirementVO != null && !(RequirementStatus.FINISH.equals(requirementVO.getStatus()) || RequirementStatus.HAS_CLOSE.equals(requirementVO.getStatus()))) {
            fixVersion = new Version();
            fixVersion.setCode(StringUtil.isNotBlank(requirementVO.getVersionCode()) ? requirementVO.getVersionCode() : DefaultValueEnum.VERSION_VALUE.getValue());
            fixVersion.setName(requirementVO.getVersionName());
        }
        return fixVersion;
    }

    public void delayFixIssue(DelayFixIssueCommand command) {
        Issue issue = issueRepository.loadFormDb(command.getAggregateId());
        IssueContext context = getIssueContext(command.getTransactor().getUserId(), command.getPermissions(), issue);
        List<Event> events = IssueStateMachineUtil.getInstance().readyEvent(issue.getStatus().toState(), context);
        if (!events.contains(IssueEvent.DELAY.toEvent())) {
            throw new ServiceException("当前用户没有权限操作！");
        }
        State state = getState(issue.getStatus().toState(), IssueEvent.DELAY.toEvent(), context);

        IssueDelayFixedEvent event = new IssueDelayFixedEvent();
        event.setAggregateId(command.getAggregateId());
        event.setCode(command.getAggregateId());
        event.setTransactor(command.getTransactor());
        event.setReason(command.getReason());
        event.setStatus(IssueStatus.valueOf(state.getCode()));
        event.setDelayFixTime(new Date());
        event.setContent(command.getContent());
        event.setOccurred(new Date());
        event.setCurStatus(issue.getStatus());
        event.setNextStatus(IssueStatus.valueOf(state.getCode()));
        event.setTransitionNodeCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        event.setFixVersion(command.getFixVersion());
        issue.setBaseAttribute(command, event);
        //延期修复时，当前处理人不变动，这里不修改当前处理人数据。
        log.info("issueRelatedRequirementCommand >>> {}", JSON.toJSONString(command));

        issueRepository.updateIssue(event);
        handleMyOperatedEvent(event.getBusinessCode(), event.getOperator(), event.domain(), event.actionCode(), RelevantUserTypeEnum.HANDLED_USER);
        handleTransitionNodeAddEvent(event);
        issueMqSender.handleIssueDelayFixedEvent(event);
        apply(event);

    }

    public void backToRepair(BackToRepairCommand command) {
        Issue issue = issueRepository.loadFormDb(command.getAggregateId());
        IssueContext context = getIssueContext(command.getTransactor().getUserId(), command.getPermissions(), issue);
        List<Event> events = IssueStateMachineUtil.getInstance().readyEvent(issue.getStatus().toState(), context);
        if (!events.contains(IssueEvent.RETURN.toEvent())) {
            throw new ServiceException("当前用户没有权限操作！");
        }
        State state = getState(issue.getStatus().toState(), IssueEvent.RETURN.toEvent(), context);
        IssueBackToRepairedEvent event = new IssueBackToRepairedEvent();
        event.setTransactor(command.getTransactor());
        event.setReason(command.getReason());
        event.setAggregateId(command.getAggregateId());
        event.setCode(command.getAggregateId());
        event.setStatus(IssueStatus.valueOf(state.getCode()));
        event.setContent(command.getContent());
        event.setHandler(issue.getDeveloper());
        event.setCurStatus(issue.getStatus());
        event.setNextStatus(IssueStatus.valueOf(state.getCode()));
        event.setTransitionNodeCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        event.setDeleteHandler(relevantUserConverter.convertCurrentHandlerVO(issue.getCurrentHandler()));
        /*event.setLastHandler(CurrentHandlerVO
                .buildSet(CurrentHandlerVO.CurrentHandlerVOBuilder.create().withBusinessCode(command.getAggregateId())
                        .withCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE)).withDomain(this.currentDomain().name())
                        .withType(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name()).withUserId(this.developer.getUserId())
                        .withUserName(this.developer.getUserName()).build()));*/

        event.setLastHandler(CurrentHandlerVO.buildSet(CurrentHandlerVO.builder()
                .businessCode(command.getAggregateId()).code(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE)).action(event.getStatus().name())
                .domain(DomainEnum.ISSUE.name()).type(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name()).handleType(HandlerTypeEnum.USER.name())
                .userId(String.valueOf(issue.getDeveloper().getUserId())).userName(issue.getDeveloper().getUserName())
                .build()));

        issue.setBaseAttribute(command, event);
        issueRepository.updateIssue(event);
        handleCurrentChangeEvent(event);
        handleMyOperatedEvent(event.getBusinessCode(), event.getOperator(), event.domain(), event.actionCode(), RelevantUserTypeEnum.HANDLED_USER);
        handleTransitionNodeAddEvent(event);
        issueMqSender.handleIssueBackToRepairedEvent(event);
        apply(event);
    }

    public void circulation(CirculationIssueCommand command) {
        Issue issue = issueRepository.loadFormDb(command.getAggregateId());
        IssueContext context = getIssueContext(command.getTransactor().getUserId(), command.getPermissions(), issue);
        List<Event> events = IssueStateMachineUtil.getInstance().readyEvent(issue.getStatus().toState(), context);
        IssueCirculationedEvent event = checkPermissions(command, events, issue);
        State state = getState(issue.getStatus().toState(), IssueEvent.TRANSFER.toEvent(), context);
        event.setTransactor(command.getTransactor());
        event.setAggregateId(command.getAggregateId());
        event.setCode(command.getAggregateId());
        event.setStatus(IssueStatus.valueOf(state.getCode()));
        event.setOccurred(new Date());
        event.setContent(command.getContent());
        event.setCurStatus(issue.getStatus());
        event.setNextStatus(IssueStatus.valueOf(state.getCode()));
        event.setTransitionNodeCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        if (issue.getDeveloper() != null) {
            event.setOriginDevelopName(issue.getDeveloper().getUserName());
        }
        if (issue.getTester() != null) {
            event.setOriginTestName(issue.getTester().getUserName());
        }
        if (command.getDevelopId() != null && StringUtil.isNotBlank(command.getDevelopName())) {
            event.setDevelopId(command.getDevelopId());
            event.setDevelopName(command.getDevelopName());
        }
        if (command.getTestId() != null && StringUtil.isNotBlank(command.getTestName())) {
            event.setTestId(command.getTestId());
            event.setTestName(command.getTestName());
        }
        User handler = getHandler(issue.getStatus(), command);
        if (handler != null) {
            event.setHandler(handler);
            event.setDeleteHandler(relevantUserConverter.convertCurrentHandlerVO(issue.getCurrentHandler()));
            /*event.setLastHandler(CurrentHandlerVO
                    .buildSet(CurrentHandlerVO.CurrentHandlerVOBuilder.create().withBusinessCode(command.getAggregateId())
                            .withCode(AggregateIdUtil.generateId(AggregateType.SNOWFLAKE)).withDomain(this.currentDomain().name())
                            .withType(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name()).withUserId(handler.getUserId())
                            .withUserName(handler.getUserName()).build()));*/
            event.setLastHandler(CurrentHandlerVO.buildSet(CurrentHandlerVO.builder()
                    .businessCode(command.getAggregateId()).code(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE)).action(event.getStatus().name())
                    .domain(DomainEnum.ISSUE.name()).type(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name()).handleType(HandlerTypeEnum.USER.name())
                    .userId(String.valueOf(handler.getUserId())).userName(handler.getUserName())
                    .build()));
        }
        issue.setBaseAttribute(command, event);
        issueRepository.updateIssue(event);
        handleCurrentChangeEvent(event);
        handleMyOperatedEvent(event.getBusinessCode(), event.getOperator(), event.domain(), event.actionCode(), RelevantUserTypeEnum.HANDLED_USER);
        handleTransitionNodeAddEvent(event);
        issueMqSender.handleIssueCirculationedEvent(event);
        apply(event);
    }

    public void confirmClose(ConfirmCloseCommand command) {
        Issue issue = issueRepository.loadFormDb(command.getAggregateId());

        List<TransitionNodeVO> nodeList = transitionNodeRepository.findIssueTransitionNodeByIssueCode(command.getAggregateId());

        IssueContext context = getIssueContext(command.getTransactor().getUserId(), command.getPermissions(), issue);
        List<Event> events = IssueStateMachineUtil.getInstance().readyEvent(issue.getStatus().toState(), context);
        if (!events.contains(IssueEvent.CONFIRM_CLOSE.toEvent())) {
            throw new ServiceException("当前用户没有权限操作！");
        }
        State state = getState(issue.getStatus().toState(), IssueEvent.CONFIRM_CLOSE.toEvent(), context);

        IssueConfirmClosedEvent event = new IssueConfirmClosedEvent();
        event.setTransactor(command.getTransactor());
        event.setAggregateId(command.getAggregateId());
        event.setCode(command.getAggregateId());
        event.setStatus(IssueStatus.valueOf(state.getCode()));
        event.setCloseTime(new Date());
        event.setContent(command.getContent());
        event.setOccurred(new Date());
        event.setCurStatus(issue.getStatus());
        event.setNextStatus(IssueStatus.valueOf(state.getCode()));
        event.setTransitionNodeCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        // 关闭缺陷，当前处理人清空，
        event.setDeleteHandler(relevantUserConverter.convertCurrentHandlerVO(issue.getCurrentHandler()));
        isValid(event, nodeList);
        issue.setBaseAttribute(command, event);
        issueRepository.updateIssue(event);
        handleCurrentChangeEvent(event);
        handleMyOperatedEvent(event.getBusinessCode(), event.getOperator(), event.domain(), event.actionCode(), RelevantUserTypeEnum.HANDLED_USER);
        handleTransitionNodeAddEvent(event);
        issueMqSender.handleIssueConfirmClosedEvent(event);
        apply(event);
    }

    private void isValid(IssueConfirmClosedEvent event, List<TransitionNodeVO> nodeList) {
        if (CollectionUtil.isEmpty(nodeList)) {
            event.setIsValid(Boolean.TRUE);
            return;
        }
        List<TransitionNodeVO> statusList = nodeList.stream()
                .filter(item -> !Arrays.asList(IssueStatus.WAIT_FIX, IssueStatus.REJECTED, IssueStatus.CLOSED, IssueStatus.FIXING)
                        .contains(item.getNextStatus()))
                .collect(Collectors.toList());
        event.setIsValid(CollectionUtil.isNotEmpty(statusList));
    }

    private IssueContext getIssueContext(Long userId, List<String> permissions, Issue issue) {
        List<ProductRoleEnum> roles = new ArrayList<>();
        if (issue.getProduct() != null) {
            roles = productRpcService.getProductRole(issue.getProduct().getCode(), userId);
        }
        return IssueContext.convert(issue, userId, permissions, roles);
    }

    private State getState(State states, Event event, IssueContext context) {
        try {
            return IssueStateMachineUtil.getInstance().fireEvent(states, event, context);
        } catch (Exception e) {
            throw new ServiceException("请刷新数据");
        }
    }

    private IssueCirculationedEvent checkPermissions(CirculationIssueCommand command, List<Event> events, Issue issue) {
        if (!events.contains(IssueEvent.TRANSFER.toEvent())) {
            throw new ServiceException("当前用户没有权限操作！");
        }
        IssueCirculationedEvent event = new IssueCirculationedEvent();
        if (Objects.equals(command.getTransactor().getUserId(), issue.getDeveloper().getUserId()) && (command.getDevelopId() == null)) {
            throw new ServiceException("当前用户不能流转开发！");
        }
        if (Objects.equals(command.getTransactor().getUserId(), issue.getTester().getUserId()) && (command.getTestId() == null)) {
            throw new ServiceException("当前用户不能流转测试！");
        }
        Boolean isDeveloper = Objects.equals(command.getTransactor().getUserId(), issue.getDeveloper().getUserId());
        Boolean isTester = Objects.equals(command.getTransactor().getUserId(), issue.getTester().getUserId());
        Boolean flag = (command.getTestId() == null || (command.getDevelopId() == null));
        if (isDeveloper && isTester && flag) {
            throw new ServiceException("当前用户不能流转开发和测试！");
        }
        return event;
    }

    private User getHandler(IssueStatus status, CirculationIssueCommand command) {
        if ((status.equals(IssueStatus.WAIT_FIX) || status.equals(IssueStatus.FIXING) || status.equals(IssueStatus.DELAY_FIX)) && command.getDevelopId() != null) {
            return new User(command.getDevelopId(), command.getDevelopName());
        }
        if ((status.equals(IssueStatus.TESTING) || status.equals(IssueStatus.CLOSED) || status.equals(IssueStatus.REJECTED)) && command.getTestId() != null) {
            return new User(command.getTestId(), command.getTestName());
        }
        return null;
    }

    public void editIssue(EditIssueCommand command) {
        this.setRequirementLevel(command);
        String description = command.getDescription();
        if (StringUtil.isNotBlank(description)) {
            description = HtmlCommentHandlerUtil.deleteCommentInLine(description);
            command.setDescription(description);
        }
        Issue issue = issueRepository.loadFormDb(command.getAggregateId());
        if (command.getRequirement() != null
                && StringUtil.isNotBlank(command.getRequirement().getCode())
                && !command.getRequirement().getCode().equals(DefaultValueEnum.REQUIREMENT_VALUE.getValue())
                && !command.getRequirement().equals(issue.getRequirement())) {
            Version fixVersion = getFixVersionByRequirementCode(command.getRequirement().getCode());
            if (fixVersion != null) {
                command.setFixVersion(fixVersion);
            }
        }
        if (command.getFindVersion() != null
                && StringUtil.isNotBlank(command.getFindVersion().getCode())
                && !command.getFindVersion().getCode().equals(DefaultValueEnum.VERSION_VALUE.getValue())
                && !command.getFindVersion().equals(issue.getFindVersion())) {
            Version fixVersion = getFixVersionByFindVersionCode(command.getFindVersion().getCode());
            if (fixVersion != null) {
                command.setFixVersion(fixVersion);
            }
        }
        IssueEditedEvent event = issueEditedEventConverter.convert(command);

        // 部分状态下，修改开发人员，会更新当前处理人（修复中、待修复、延期修复）
        if ((issue.getStatus().equals(IssueStatus.WAIT_FIX) || issue.getStatus().equals(IssueStatus.DELAY_FIX) || issue.getStatus().equals(IssueStatus.FIXING))
                && command.getDeveloper() != null
                && command.getDeveloper().getUserId() != null
                && !command.getDeveloper().getUserId().equals(issue.getDeveloper().getUserId())) {
            event.setHandler(command.getDeveloper());
            event.setDeleteHandler(relevantUserConverter.convertCurrentHandlerVO(issue.getCurrentHandler()));
            event.setLastHandler(CurrentHandlerVO.buildSet(CurrentHandlerVO.builder()
                    .businessCode(command.getAggregateId()).code(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE)).action(issue.getStatus().name())
                    .domain(DomainEnum.ISSUE.name()).type(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name()).handleType(HandlerTypeEnum.USER.name())
                    .userId(String.valueOf(command.getDeveloper().getUserId())).userName(command.getDeveloper().getUserName())
                    .build()));
        }
        // 部分状态下，修改测试人员，会更新当前处理人（验证中、已关闭、已拒绝）
        if ((issue.getStatus().equals(IssueStatus.CLOSED) || issue.getStatus().equals(IssueStatus.TESTING) || issue.getStatus().equals(IssueStatus.REJECTED)) &&
                command.getTester() != null && command.getTester().getUserId() != null && !command.getTester().getUserId().equals(issue.getTester().getUserId())) {
            event.setDeleteHandler(relevantUserConverter.convertCurrentHandlerVO(issue.getCurrentHandler()));
            event.setLastHandler(CurrentHandlerVO.buildSet(CurrentHandlerVO.builder()
                    .businessCode(command.getAggregateId()).code(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE)).action(issue.getStatus().name())
                    .domain(DomainEnum.ISSUE.name()).type(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name()).handleType(HandlerTypeEnum.USER.name())
                    .userId(String.valueOf(command.getTester().getUserId())).userName(command.getTester().getUserName())
                    .build()));
            event.setHandler(command.getTester());
        }
        if (command.getFixVersion() != null && StringUtil.isNotBlank(command.getFixVersion().getCode()) && !command.getFixVersion().getCode().equals(issue.getFixVersion())) {
            VersionVO version = projectRpcService.findVersionQuery(command.getFixVersion().getCode());
            if (version != null && version.getIsConfirm()) {
                event.setVersionConfirm(Optional.ofNullable(version.getIsConfirm()).map(t -> t ? "after" : "before").orElse("before"));
            }
        }
        event.setOccurred(new Date());
        event.setUpdateTime(new Date());
        log.info("EditIssueCommand------------------------>AggregateId: {} ", command.getAggregateId());
        Version findVersionOld = issue.getFindVersion();
        Version findVersionNew = event.getFindVersion();
        FindIssueByCodeQuery query = new FindIssueByCodeQuery();
        query.setCode(issue.getCode());
        setChangeLogs(event, issue);
        issueRepository.editIssue(event);
        handleMyOperatedEvent(event.getBusinessCode(), event.getOperator(), event.domain(), event.actionCode(), RelevantUserTypeEnum.HANDLED_USER);
        handleCurrentChangeEvent(event);
        issueMqSender.handleIssueEditedEvent(event);
        issueMatterMqSender.handleIssueEditedEvent(event);
        apply(event);
        if (findVersionOld != null) {
            String versionCodeOld = findVersionOld.getCode();
            boolean flg = StringUtil.isNotBlank(versionCodeOld) &&
                    (findVersionNew == null || StringUtil.isBlank(findVersionNew.getCode()) || !versionCodeOld.equals(findVersionNew.getCode()));
            if (flg) {
                IssueEditedVersionChangedEvent issueEditedVersionChangedEvent = new IssueEditedVersionChangedEvent();
                issueEditedVersionChangedEvent.setCode(event.getCode());
                issueEditedVersionChangedEvent.setFindVersion(findVersionOld);
                issueEditedVersionChangedEvent.setTransactor(event.getTransactor());
            }
        }
    }

    private void setRequirementLevel(EditIssueCommand command) {
        if (null != command.getRequirement()) {
            String requirementCode = command.getRequirement().getCode();
            if (StringUtil.isNotBlank(requirementCode)) {
                RequirementVO requirementVO = projectRpcService.requirementWithCodeQuery(command.getRequirement().getCode());
                if (null != requirementVO) {
                    command.setRequirementLevel(requirementVO.getLevel() == 2 ? RequirementLevel.REQUIREMENT_CHILD : RequirementLevel.REQUIREMENT);
                }
            }
        }
    }

    private void setChangeLogs(IssueEditedEvent event, Issue issue) {
        HashMap<String, Diff> res = new HashMap<>();
        try {
            if (!Objects.equals(issue.getTitle(), event.getTitle()) && StringUtil.isNotEmpty(event.getTitle())) {
                res.put("缺陷标题", getDiff(issue.getTitle(), event.getTitle()));
            }
            if (!Objects.equals(issue.getFindVersion(), event.getFindVersion()) && event.getFindVersion() != null && StringUtil.isNotEmpty(event.getFindVersion().getCode())) {
                res.put("发现版本", getDiff(issue.getFindVersion() != null ? issue.getFindVersion().getName() : "", event.getFindVersion() != null ? event.getFindVersion().getName() : ""));
            }
            if (!Objects.equals(issue.getDeveloper(), event.getDeveloper()) && event.getDeveloper() != null && event.getDeveloper().getUserId() != null) {
                res.put("开发人员", getDiff(issue.getDeveloper() != null ? issue.getDeveloper().getUserName() : "", event.getDeveloper() != null ? event.getDeveloper().getUserName() : ""));
            }
            if (!Objects.equals(issue.getTester(), event.getTester()) && event.getTester() != null && event.getTester().getUserId() != null) {
                res.put("测试人员", getDiff(issue.getTester() != null ? issue.getTester().getUserName() : "", event.getTester() != null ? event.getTester().getUserName() : ""));
            }
            if (!Objects.equals(issue.getPriority(), event.getPriority()) && event.getPriority() != null) {
                res.put("优先级", getDiff(issue.getPriority() != null ? issue.getPriority().getValue() : "", event.getPriority() != null ? event.getPriority().getValue() : ""));
            }
            if (!Objects.equals(issue.getApplicationType(), event.getApplicationType()) && event.getApplicationType() != null) {
                res.put("应用类型", getDiff(issue.getApplicationType() != null ? issue.getApplicationType().getValue() : "", event.getApplicationType() != null ? event.getApplicationType().getValue() : ""));
            }
            if (!Objects.equals(issue.getRootCause(), event.getRootCause()) && event.getRootCause() != null) {
                res.put("BUG根源", getDiff(issue.getRootCause() != null ? issue.getRootCause().getValue() : "", event.getRootCause() != null ? event.getRootCause().getValue() : ""));
            }
            if (!Objects.equals(issue.getType(), event.getType()) && event.getType() != null) {
                res.put("BUG类别", getDiff(issue.getType() != null ? issue.getType().getValue() : "", event.getType() != null ? event.getType().getValue() : ""));
            }
            if (!Objects.equals(issue.getTestMethod(), event.getTestMethod()) && event.getTestMethod() != null) {
                res.put("测试方法", getDiff(issue.getTestMethod() != null ? issue.getTestMethod().getValue() : "", event.getTestMethod() != null ? event.getTestMethod().getValue() : ""));
            }
            if (!Objects.equals(issue.getRepetitionRate(), event.getRepetitionRate()) && event.getRepetitionRate() != null) {
                res.put("测试方法", getDiff(issue.getRepetitionRate() != null ? issue.getRepetitionRate().getValue() : "", event.getRepetitionRate() != null ? event.getRepetitionRate().getValue() : ""));
            }
            if (!Objects.equals(issue.getFindStage(), event.getFindStage()) && event.getFindStage() != null) {
                res.put("发现阶段", getDiff(issue.getFindStage() != null ? issue.getFindStage().getValue() : "", event.getFindStage() != null ? event.getFindStage().getValue() : ""));
            }
            if (!Objects.equals(issue.getFindEnv(), event.getFindEnv()) && event.getFindEnv() != null) {
                res.put("发现环境", getDiff(issue.getFindEnv() != null ? issue.getFindEnv().getValue() : "", event.getFindEnv() != null ? event.getFindEnv().getValue() : ""));
            }
            String originFixVersionName = issue.getFixVersion() != null ? issue.getFixVersion().getName() : null;
            String newFixVersionName = event.getFixVersion() != null ? event.getFixVersion().getName() : null;
            if (newFixVersionName == null) {
                newFixVersionName = "";
            }
            if (originFixVersionName == null || "null".equals(originFixVersionName)) {
                originFixVersionName = "";
            }
            if (!Objects.equals(originFixVersionName, newFixVersionName) && StringUtil.isNotEmpty(newFixVersionName)) {
                res.put("修复版本", getDiff(originFixVersionName, newFixVersionName));
            }
            if (!Objects.equals(issue.getRequirement(), event.getRequirement()) && event.getRequirement() != null && StringUtil.isNotEmpty(event.getRequirement().getName())) {
                res.put("关联需求", getDiff(issue.getRequirement() != null ? issue.getRequirement().getName() : "", event.getRequirement() != null ? event.getRequirement().getName() : ""));
            }
            Sprint sprint = issue.getSprint();
            if (sprint == null) {
                sprint = new Sprint();
                sprint.setCode(DefaultValueEnum.SPRINT_VALUE.getValue());
                sprint.setName("");
            }
            if (!Objects.equals(sprint, event.getSprint()) && event.getSprint() != null && StringUtil.isNotEmpty(event.getSprint().getName())) {
                res.put("关联迭代", getDiff(sprint.getName(), event.getSprint() != null ? event.getSprint().getName() : ""));
            }
            if (!Objects.equals(issue.getDescription(), event.getDescription()) && StringUtil.isNotEmpty(event.getDescription())) {
                res.put("缺陷描述", null);
            }
        } catch (Exception e) {
            log.error("记录日志失败 {}", e.getMessage());
        }
        log.info("记录日志： {}", String.join(",", res.keySet()));
        event.setChangesLog(res);
    }

    private Diff getDiff(String old, String newValue) {
        Diff diff = new Diff();
        diff.setOriginal(old);
        diff.setValue(newValue);
        return diff;
    }


    public void refusedIssue(RefusedIssueCommand command) {
        Issue issue = issueRepository.loadFormDb(command.getAggregateId());
        IssueStatus status = issue.getStatus();
        IssueContext context = getIssueContext(command.getTransactor().getUserId(), command.getPermissions(), issue);
        List<Event> events = IssueStateMachineUtil.getInstance().readyEvent(status.toState(),
                context);
        if (!events.contains(IssueEvent.REJECT.toEvent())) {
            throw new ServiceException("当前用户没有权限操作！");
        }
        State state = getState(status.toState(), IssueEvent.REJECT.toEvent(),
                context);

        IssueRefusedEvent event = new IssueRefusedEvent();
        event.setAggregateId(command.getAggregateId());
        event.setCode(command.getAggregateId());
        event.setTransactor(command.getTransactor());
        event.setStatus(IssueStatus.valueOf(state.getCode()));
        event.setRejectTime(new Date());
        event.setOccurred(new Date());
        event.setContent(command.getContent());
        event.setReason(command.getReason());
        event.setHandler(issue.getTester());
        event.setCurStatus(status);
        event.setNextStatus(IssueStatus.valueOf(state.getCode()));
        event.setTransitionNodeCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        event.setDeleteHandler(relevantUserConverter.convertCurrentHandlerVO(issue.getCurrentHandler()));
        event.setLastHandler(CurrentHandlerVO.buildSet(CurrentHandlerVO.builder()
                .businessCode(command.getAggregateId()).code(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE)).action(event.getStatus().name())
                .domain(DomainEnum.ISSUE.name()).type(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name()).handleType(HandlerTypeEnum.USER.name())
                .userId(String.valueOf(issue.getTester().getUserId())).userName(issue.getTester().getUserName())
                .build()));
        event.setTransactor(command.getTransactor());
        issueRepository.updateIssue(event);
        handleCurrentChangeEvent(event);
        handleMyOperatedEvent(event.getBusinessCode(), event.getOperator(), event.domain(), event.actionCode(), RelevantUserTypeEnum.HANDLED_USER);
        handleTransitionNodeAddEvent(event);
        issueMqSender.handleIssueRefusedEvent(event);
        apply(event);
    }

    public void removeIssue(RemoveIssueCommand command) {
        Issue issue = issueRepository.loadFormDb(command.getAggregateId());
        IssueStatus status = issue.getStatus();


        IssueContext context = getIssueContext(command.getTransactor().getUserId(), command.getPermissions(), issue);
        //更新 issue
        List<Event> events = IssueStateMachineUtil.getInstance().readyEvent(status.toState(),
                context);
        if (!events.contains(IssueEvent.REMOVE.toEvent())) {
            throw new ServiceException("当前用户没有权限操作！");
        }
        State state = getState(status.toState(), IssueEvent.REMOVE.toEvent(),
                context);

        IssueRemovedEvent event = new IssueRemovedEvent();
        event.setAggregateId(command.getAggregateId());
        event.setCode(command.getAggregateId());
        event.setTransactor(command.getTransactor());
        event.setStatus(IssueStatus.valueOf(state.getCode()));
        event.setContent(command.getContent());
        event.setOccurred(new Date());
        event.setTransactor(command.getTransactor());
        event.setCurStatus(status);
        event.setNextStatus(IssueStatus.valueOf(state.getCode()));
        event.setTransitionNodeCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        issueRepository.removeIssue(event);
        handleMyDeletedEvent(event.getBusinessCode(), event.getOperator(), event.domain());
        issueMqSender.handleIssueRemovedEvent(event);
        issueMatterMqSender.handleIssueRemovedEvent(event);
        apply(event);
    }

    public void issueMsgJob() {
        List<CountUserIssueNumVO> fixIssueMsgList = fixIssueMsg();
        List<CountUserIssueNumVO> testIssueMsgList = testIssueMsg();
        log.info("缺陷定时发送消息.参数：fixIssueMsgList {}  testIssueMsgList {}", JSON.toJSONString(fixIssueMsgList), JSON.toJSONString(testIssueMsgList));
        String resultMsgFix = zimService.sendMessage(fixIssueMsgList, "764-9a5-20211022");
        String resultMsgTest = zimService.sendMessage(testIssueMsgList, "3f4-143-20211022");
        log.info("resultMsgFix_发送成功: {} ", resultMsgFix);
        log.info("resultMsgTest_发送成功: {} ", resultMsgTest);
    }

    /**
     * 版本规划缺陷
     *
     * @param req
     */
    public void handleVersionPlanedEvent(VersionPlannedReq req) {
        if (req == null) {
            return;
        }
        String versionConfirm = req.getConfirm() ? "after" : "before";
        if (CollectionUtil.isNotEmpty(req.getRemovedIssues())) {
            issueRepository.updateIssueFixVersionFiled(req.getRemovedIssues(), DefaultValueEnum.VERSION_VALUE.getValue(), " ", versionConfirm);
            issueMqSender.handleIssueVersionEditedEvent(req.getAddedIssues(), DefaultValueEnum.VERSION_VALUE.getValue(), "", versionConfirm);
            issueMatterMqSender.handleIssueVersionEditedEvent(req.getAddedIssues(), DefaultValueEnum.VERSION_VALUE.getValue(), "", versionConfirm);
        }
        if (CollectionUtil.isNotEmpty(req.getAddedIssues())) {
            issueRepository.updateIssueFixVersionFiled(req.getAddedIssues(), req.getVersionCode(), req.getVersionName(), versionConfirm);
            issueMqSender.handleIssueVersionEditedEvent(req.getAddedIssues(), req.getVersionCode(), req.getVersionName(), versionConfirm);
            issueMatterMqSender.handleIssueVersionEditedEvent(req.getAddedIssues(), req.getVersionCode(), req.getVersionName(), versionConfirm);
        }
        if (StringUtil.isNotBlank(req.getVersionCode())) {
            statisticsIssueService.statisticsVersionIssue(req.getVersionCode());
        }
    }

    /**
     * 缺陷-创建人
     *
     * @param businessCode
     * @param operator
     * @param domainEnum
     * @param actionCode
     * @param userTypeEnum
     */
    public void handleMyCreatedEvent(String businessCode, User operator, DomainEnum domainEnum, String actionCode, RelevantUserTypeEnum userTypeEnum) {
        iRelevantUserRepository.saveByEvent(businessCode, operator, domainEnum, actionCode, userTypeEnum);
    }

    /**
     * 缺陷-删除人
     *
     * @param businessCode
     * @param operator
     * @param domainEnum
     */
    public void handleMyDeletedEvent(String businessCode, User operator, DomainEnum domainEnum) {
        iRelevantUserRepository.updateDelete(businessCode, operator, domainEnum);
    }

    /**
     * 缺陷-处理过的人
     *
     * @param businessCode
     * @param operator
     * @param domainEnum
     * @param actionCode
     * @param userTypeEnum
     */
    public void handleMyOperatedEvent(String businessCode, User operator, DomainEnum domainEnum, String actionCode, RelevantUserTypeEnum userTypeEnum) {
        iRelevantUserRepository.saveByEvent(businessCode, operator, domainEnum, actionCode, userTypeEnum);
    }

    /**
     * 当前处理人变更
     *
     * @param event
     */
    public void handleCurrentChangeEvent(AbstractCurrentHandlerChangedEvent event) {
        log.info("消费AbstractCurrentHandlerChangedEvent事件");
        iRelevantUserRepository.updateByEvent(event);
    }

    /**
     * 缺陷-节点
     *
     * @param event
     */
    public void handleTransitionNodeAddEvent(TransitionNodeAddEvent event) {
        transitionNodeRepository.handleTransitionNodeAddEvent(event);
        issueTransitionNodeMqSender.handleTransitionNodeAddEvent(event);
    }

    public void handleTransitionNodeAddedEvent(TransitionNodeAddedEvent event) {
        transitionNodeRepository.handleTransitionNodeAddedEvent(event);
        issueTransitionNodeMqSender.handleTransitionNodeAddedEvent(event);
    }


    public List<CountUserIssueNumVO> fixIssueMsg() {
        Boolean isHoliday = iProjectRpcService.query(new Date());
        if (!isHoliday) {
            return issueRepository.queryFixIssueGroupByDevelopUserId(Arrays.asList(IssueStatus.WAIT_FIX, IssueStatus.FIXING));
        }
        return new ArrayList<>();
    }

    public List<CountUserIssueNumVO> testIssueMsg() {
        Boolean isHoliday = iProjectRpcService.query(new Date());
        if (!isHoliday) {
            return issueRepository.queryTestIssueGroupByTestUserId(Arrays.asList(IssueStatus.TESTING, IssueStatus.REJECTED));
        }
        return new ArrayList<>();
    }

    public void updateIssueHandleUserId(List<DevThingUpdateVO> vos) {
        issueRepository.updateIssueHandleUserId(vos);
    }


    public void confirmCloseAndSelect(ConfirmCloseCommand command) {
        Issue issue = issueRepository.loadFormDb(command.getAggregateId());
        List<String> status = Arrays.asList(IssueStatus.CLOSED.name(), IssueStatus.END.name(), IssueStatus.REMOVED.name());
        if (status.contains(issue.getStatus().name())) {
            throw new ServiceException("该缺陷状态为最终状态，不能操作");
        }
        IssueConfirmClosedEvent event = new IssueConfirmClosedEvent();
        event.setTransactor(command.getTransactor());
        event.setAggregateId(command.getAggregateId());
        event.setCode(command.getAggregateId());
        event.setStatus(IssueStatus.CLOSED);
        event.setCloseTime(new Date());
        event.setContent(command.getContent());
        event.setOccurred(new Date());
        event.setCurStatus(issue.getStatus());
        event.setTransitionNodeCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        // 关闭缺陷，当前处理人清空，
        event.setDeleteHandler(relevantUserConverter.convertCurrentHandlerVO(issue.getCurrentHandler()));
        issue.setBaseAttribute(command, event);
        issueRepository.updateIssue(event);
        handleCurrentChangeEvent(event);
        handleMyOperatedEvent(event.getBusinessCode(), event.getOperator(), event.domain(), event.actionCode(), RelevantUserTypeEnum.HANDLED_USER);
        handleTransitionNodeAddEvent(event);
        issueMqSender.handleIssueConfirmClosedEvent(event);
        apply(event);
    }

    public SlowSqlToIssueResp addIssueForSlowSql(AddIssueCommand command) {
        //开发人员
        List<String> members = Stream.of(MemberTypeEnum.DEVELOPER_OWNER).map(Enum::name).collect(Collectors.toList());
        List<ProductMemberVO> memberVOS = productRpcService.getProductMember(command.getProduct().getCode(), members);
        if (CollectionUtil.isEmpty(memberVOS) || Objects.isNull(memberVOS.get(0))) {
            throw new ServiceException("该产品下没有开发负责人，请先添加开发人员");
        }
        command.setDeveloper(new User(memberVOS.get(0).getUserId(), memberVOS.get(0).getUserName()));

        //发现版本
        Version version = getVersionCodeByLocationAndTime(command.getLocation(), command.getStartAt(), command.getProductCode());
        if (Objects.nonNull(version)) {
            command.setFindVersion(version);
        }
        User transactor = new User(command.getTestUserId(), command.getTestUserName());
        command.setHandler(transactor);
        command.setTransactor(transactor);
        command.setPriority(IssuePriority.MIDDLE);
        command.setRootCause(IssueRootCause.OTHER);

        if (command.getUploadDescFlag()) {
            String uploadKey = ossService.uploadFile(command.getDescription(), ExtEnum.TXT, SLOW_SQL_ISSUE_DESC_FILE_NAME);
            command.setDescription(SLOW_SQL_ISSUE_DESC);
            AttachmentVO attachmentVO = new AttachmentVO();
            attachmentVO.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
            attachmentVO.setBusinessCode(command.getAggregateId());
            attachmentVO.setDomain(DomainEnum.ISSUE);
            attachmentVO.setType(AttachmentTypeEnum.FILE);
            attachmentVO.setDocumentType(AttachmentDocumentTypeEnum.BUSINESS);
            attachmentVO.setRemoteFileId(uploadKey);
            attachmentVO.setName(SLOW_SQL_ISSUE_DESC_FILE_NAME);
            command.setAttachments(Collections.singletonList(attachmentVO));
        }
        String issueCode = addIssue(command);
        return SlowSqlToIssueResp.builder().issueCode(issueCode).build();
    }

    private Version getVersionCodeByLocationAndTime(String location, Long occurrenceAt, String productCode) {
        //查tag
        QueryAppIdTagsReq req = new QueryAppIdTagsReq(location, occurrenceAt);
        Map<String, Map<String, String>> resp = zCatService.queryAppIdTags(req);
        if (MapUtils.isEmpty(resp)) {
            log.error("zcat查询tag失败,location:{},occurrenceAt:{}", location, occurrenceAt);
            return null;
        }

        Set<String> appIds = resp.keySet();
        if (CollectionUtil.isEmpty(appIds)) {
            log.error("zcat查询appId为空,location:{},occurrenceAt:{},resp:{}", location, occurrenceAt, JSON.toJSON(resp));
            return null;
        }
        for (String appId : appIds) {
            //查版本code
            FindVersionByNamespaceQuery query = new FindVersionByNamespaceQuery();
            query.setProductCode(productCode);
            query.setName(resp.get(appId).get("namespaceTag"));
            FindVersionByNamespaceVO versionResp = iPipelineRpcService.queryVersionByNamespace(query);
            if (Objects.nonNull(versionResp)) {
                return new Version(versionResp.getVersionCode(), versionResp.getVersionName());
            }
        }
        log.error("zcat查询版本为空,productCode:{},resp:{}", productCode, JSON.toJSON(resp));
        return null;
    }

    public void planVersionIssue(VersionBaseEvent event) {
        if(event == null){
            return;
        }

        List<RelatedItemVO> relatedList = event.getRelatedList();
        List<RelatedItemVO> oldRelatedList = event.getOldRelatedList();
        Boolean isConfirm = event.getIsConfirm();
        String code = event.getCode();
        String name = event.getName();
        String productCode = event.getProductCode();

        if (CollectionUtil.isNotEmpty(oldRelatedList)) {
            List<String> oldIssueCodes = oldRelatedList.stream()
                    .filter(e -> e.getDomain().equals(DomainEnum.ISSUE.name()))
                    .map(RelatedItemVO::getCode)
                    .collect(Collectors.toList());
            VersionPlannedReq req = new VersionPlannedReq();
            req.setRemovedIssues(oldIssueCodes);
            req.setConfirm(isConfirm);
            handleVersionPlanedEvent(req);
        }
        if (CollectionUtil.isNotEmpty(relatedList)) {
            List<String> issueCodes = relatedList.stream()
                    .filter(e -> e.getDomain().equals(DomainEnum.ISSUE.name()) && StringUtil.isNotBlank(e.getCode()))
                    .map(RelatedItemVO::getCode)
                    .collect(Collectors.toList());
            VersionPlannedReq req = new VersionPlannedReq();
            req.setVersionCode(code);
            req.setVersionName(name);
            req.setConfirm(isConfirm);
            req.setAddedIssues(issueCodes);
            handleVersionPlanedEvent(req);
            List<RelatedItemVO> newIssue = relatedList.stream()
                    .filter(e -> e.getDomain().equals(DomainEnum.ISSUE.name()) && StringUtil.isBlank(e.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(newIssue)) {
                addNewIssue(event.getTransactor(), newIssue, code, name, productCode);
            }
        }
    }

    private void addNewIssue(User transactor, List<RelatedItemVO> vos, String versionCode, String versionName, String productCode) {
        List<AddVersionIssueCommand> commandList = issueVOConverter.convertVersion(vos);
        for (AddVersionIssueCommand command : commandList) {
            command.setAggregateId(aggregateIdGenerateService.generateId("ISSUE"));
            User developUser = new User(transactor.getUserId(), transactor.getUserName());
            Version version = new Version();
            version.setCode(versionCode);
            version.setName(versionName);
            Sprint sprint = new Sprint();
            sprint.setCode(DefaultValueEnum.SPRINT_VALUE.getValue());
            Product product = new Product();
            product.setCode(productCode);
            SimpleQueryVO productVO = productRpcService.getProductVO(productCode);
            String productName = productVO.getProductName();
            if (StringUtil.isNotBlank(productName)) {
                product.setName(productName);
            }
            command.setDeveloper(developUser);
            command.setHandler(developUser);
            command.setFinder(transactor);
            command.setFindVersion(version);
            command.setFixVersion(version);
            command.setTester(transactor);
            command.setProduct(product);
            command.setTransactor(transactor);
            command.setSprint(sprint);
            command.setRequirement(new Requirement());
            // 由版本 创建 新增的缺陷，都是版本确认前的数据
            command.setVersionConfirm("before");
            if (StringUtil.isEmpty(command.getDescription())) {
                command.setDescription(command.getTitle());
            }
            handleAddVersionIssueCommand(command);
        }
    }
}
