package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.enums.rpc.MemberTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseFlagEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.model.dto.AutomaticSourceLogEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.*;
import com.zto.devops.qc.domain.converter.AutomaticSourceRecordDomainConverter;
import com.zto.devops.qc.domain.converter.TestcaseDomainConverter;
import com.zto.devops.qc.domain.gateway.repository.AutomaticSourceRecordRepository;
import com.zto.devops.qc.domain.gateway.repository.TestcaseRepository;
import com.zto.titans.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class AutomaticSourceRecordQueryDomainService {

    @Autowired
    private AutomaticSourceRecordRepository automaticSourceRecordRepository;

    @Autowired
    private AutomaticSourceRecordDomainConverter automaticSourceRecordDomainConverter;

    @Autowired
    private TestcaseRepository testcaseRepository;

    @Autowired
    private TestcaseDomainConverter testcaseDomainConverter;

    @Autowired
    private TestcaseQueryDomainService testcaseQueryDomainService;

    public List<AutomaticRecordVO> automaticRecordByNameQuery(AutomaticRecordByNameQuery query) {
        log.info("automaticRecordByNameQuery >>> {}", JSON.toJSONString(query));
        return automaticSourceRecordRepository.listAutomaticRecordVO(query);
    }

    public List<AutomaticRecordLogVO> automaticRecordLogQuery(AutomaticRecordLogQuery query) {
        log.info("automaticRecordLogQuery >>> {}", JSON.toJSONString(query));
        return automaticSourceRecordRepository.listAutomaticRecordLogVO(query);
    }

    public AutomaticRecordVO automaticRecordQuery(AutomaticRecordQuery query) {
        log.info("automaticRecordQuery >>> {}", JSON.toJSONString(query));
        //查询自动化用例登记源
        AutomaticSourceRecordEntityDO entityDO = automaticSourceRecordRepository.find(query.getCode());
        if (null == entityDO) {
            return null;
        }
        AutomaticRecordVO vo = automaticSourceRecordDomainConverter.convert(entityDO);
        if (StringUtil.isNotBlank(entityDO.getTestcaseCode())) {
            //登记库挂载的用例模块
            TestcaseEntityDO testcaseEntityDO = testcaseRepository.loadFormDb(entityDO.getTestcaseCode());
            vo.setTestcaseVO(testcaseDomainConverter.convertVO(testcaseEntityDO));
        }
        List<Button> buttonList = new ArrayList<>();
        if (null != query.getTransactor()
                && (Arrays.asList(entityDO.getCreatorId(), entityDO.getPersonLiableId())
                .contains(query.getTransactor().getUserId())
                || query.getTransactor().getPermissions().contains(MemberTypeEnum.SUPPER.name()))) {
            buttonList.add(new Button("修改责任人", "editPersonLiable", 1));
        }
        vo.setButtons(buttonList);
        return vo;
    }

    public AutomaticRecordLogVO automaticRecordLogByCodeQuery(AutomaticRecordLogByCodeQuery query) {
        return automaticSourceRecordRepository.findAutomaticRecordLogVO(query.getCode());
    }

    public AutomaticAndTestcaseNoVO countAutomaticAndTestcase(AutomaticAndTestcaseNoQuery query) {
        AutomaticAndTestcaseNoVO automaticAndTestcaseNoVO = new AutomaticAndTestcaseNoVO();
        if (TestcaseTypeEnum.SOURCERECORD.equals(query.getType())) {
            AutomaticSourceRecordEntityDO automaticSourceRecord = automaticSourceRecordRepository.find(query.getCode());
            automaticAndTestcaseNoVO.setFullName(automaticSourceRecord.getName());
            List<TestcaseEntityDO> testcaseList = testcaseRepository.queryCaseListByAutomaticCodes(
                    Collections.singletonList(automaticSourceRecord.getCode()));
            automaticAndTestcaseNoVO.setTestcaseNo(testcaseList.size());
            return automaticAndTestcaseNoVO;
        }
        TestcaseEntityDO testcase = testcaseRepository.loadFormDb(query.getCode());
        if (StringUtil.isBlank(testcase.getPath())) {
            automaticAndTestcaseNoVO.setFullName(testcase.getName());
        } else {
            List<String> nameList = testcaseQueryDomainService.findParentFullNameQuery(testcase.getPath());
            nameList.add(testcase.getName());
            String fullName = String.join("/", nameList);
            automaticAndTestcaseNoVO.setFullName(fullName);
        }
        List<TestcaseEntityDO> children = testcaseRepository.queryChildCaseAndModuleByPath(testcase.getCode(), testcase.getPath());
        List<String> testcaseCodeList = children.stream()
                .filter(i -> TestcaseAttributeEnum.MODULE.equals(i.getAttribute()))
                .map(TestcaseEntityDO::getCode)
                .collect(Collectors.toList());
        automaticAndTestcaseNoVO.setTestcaseModuleNo(testcaseCodeList.size());
        testcaseCodeList.add(testcase.getCode());
        List<AutomaticSourceRecordEntityDO> automaticSourceRecordList =
                automaticSourceRecordRepository.queryAutomaticSourceByTestcaseCode(testcaseCodeList);
        automaticAndTestcaseNoVO.setAutomaticNo(automaticSourceRecordList.size());
        if (!automaticSourceRecordList.isEmpty()) {
            List<String> list = automaticSourceRecordList.stream()
                    .map(AutomaticSourceRecordEntityDO::getCode)
                    .collect(Collectors.toList());
            List<TestcaseEntityDO> testcaseList = testcaseRepository.queryCaseListByAutomaticCodes(list);
            automaticAndTestcaseNoVO.setTestcaseNo(testcaseList.size());
        }
        return automaticAndTestcaseNoVO;

    }

    public AutomaticSourceLogTestcaseListVO automaticSourceLogTestcaseQuery(AutomaticSourceLogTestcaseQuery query) {
        AutomaticSourceLogEntityDO automaticSourceLog = automaticSourceRecordRepository.findAutomaticSourceByLogCode(query.getCode());
        if (null == automaticSourceLog) {
            return null;
        }
        TestcaseQuery testcaseQuery = new TestcaseQuery();
        testcaseQuery.setProductCode(automaticSourceLog.getProductCode());
        testcaseQuery.setType(TestcaseTypeEnum.AUTO);
        testcaseQuery.setAutomaticSourceCode(automaticSourceLog.getAutomaticSourceCode());
        List<ListTestcaseVO> moduleList = testcaseRepository.selectTestCaseModule(testcaseQuery);
        AutomaticSourceLogTestcaseListVO resVo = new AutomaticSourceLogTestcaseListVO();

        List<AutomaticSourceLogTestcaseVO> insertVoList = automaticSourceRecordRepository.selectListAndPlanNameLog(query.getCode(), TestcaseFlagEnum.insert.name());
        long insertNo = insertVoList.stream().filter(item -> TestcaseAttributeEnum.TESTCASE.name().equals(item.getAttribute())).count();
        resVo.setInsertNo(insertNo);
        if (insertNo > 0) {
            List<AutomaticSourceLogTestcaseVO> insertList = composeTreeModule(insertVoList, moduleList);
            resVo.setInsertList(insertList);
        }
        List<AutomaticSourceLogTestcaseVO> updateVoList = automaticSourceRecordRepository.selectListAndPlanNameLog(query.getCode(), TestcaseFlagEnum.update.name());
        long updateNo = updateVoList.stream().filter(item -> TestcaseAttributeEnum.TESTCASE.name().equals(item.getAttribute())).count();
        resVo.setUpdateNo(updateNo);
        if (updateNo > 0) {
            List<AutomaticSourceLogTestcaseVO> updateList = composeTreeModule(updateVoList, moduleList);
            resVo.setUpdateList(updateList);
        }
        List<AutomaticSourceLogTestcaseVO> deleteVoList = automaticSourceRecordRepository.selectListAndPlanNameLog(query.getCode(), TestcaseFlagEnum.delete.name());
        long deleteNo = deleteVoList.stream().filter(item -> TestcaseAttributeEnum.TESTCASE.name().equals(item.getAttribute())).count();
        resVo.setDeleteNo(deleteNo);
        if (deleteNo > 0) {
            List<AutomaticSourceLogTestcaseVO> deleteList = composeTreeModule(deleteVoList, moduleList);
            resVo.setDeleteList(deleteList);
        }
        return resVo;
    }

    public List<AutomaticRecordVO> analysisAutomaticRecordAbortQuery(AnalysisAutomaticRecordAbortQuery query) {
        return automaticSourceRecordRepository.analysisAutomaticRecordAbortQuery(query);
    }

    public List<AutomaticSourceRecordEntityDO> selectAutoAnalysisByAddress(String gitHttpUrl) {
        if (StringUtil.isBlank(gitHttpUrl)) {
            return new ArrayList<>();
        }
        return automaticSourceRecordRepository.selectAutoAnalysisByAddress(gitHttpUrl);
    }

    private List<AutomaticSourceLogTestcaseVO> composeTreeModule(List<AutomaticSourceLogTestcaseVO> operateList, List<ListTestcaseVO> moduleList) {
        Map<String, Long> countMap = operateList.stream()
                .filter(item -> TestcaseAttributeEnum.TESTCASE.name().equals(item.getAttribute()))
                .flatMap(item -> Stream.of(item.getPath().split("\\.")))
                .collect(Collectors.groupingBy(s -> s, Collectors.counting()));
        List<AutomaticSourceLogTestcaseVO> list = moduleList.stream()
                .filter(item -> countMap.containsKey(item.getCode()))
                .map(item -> automaticSourceRecordDomainConverter.convert(item))
                .collect(Collectors.toList());
        List<String> codes = list.stream().map(AutomaticSourceLogTestcaseVO::getTestcaseCode).collect(Collectors.toList());
        operateList.stream().filter(item -> !codes.contains(item.getTestcaseCode())).forEach(list::add);
        return list.stream().peek(item -> {
                    list.stream()
                            .filter(o -> item.getTestcaseCode().equals(o.getParentCode()))
                            .forEach(o -> {
                                if (null == item.getSonList()) {
                                    item.setSonList(new ArrayList<>());
                                }
                                item.getSonList().add(o);
                            });
                    item.setCount(countMap.getOrDefault(item.getTestcaseCode(), 0L).intValue());
                })
                .filter(item -> StringUtil.isBlank(item.getParentCode()))
                .collect(Collectors.toList());
    }
}
