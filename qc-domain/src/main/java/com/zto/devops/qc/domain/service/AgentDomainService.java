package com.zto.devops.qc.domain.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.agent.AgentCommandEnum;
import com.zto.devops.qc.client.enums.agent.ChaosExceptionTypeEnum;
import com.zto.devops.qc.client.enums.agent.ChaosRuleTypeEnum;
import com.zto.devops.qc.client.model.dto.QcAgentRuleConfigEntityDO;
import com.zto.devops.qc.client.model.rpc.pipeline.query.VersionReleaseBranchCommitQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoveragePublishVO;
import com.zto.devops.qc.client.service.agent.model.*;
import com.zto.devops.qc.domain.gateway.http.HttpService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.CoverageRepository;
import com.zto.devops.qc.domain.gateway.repository.QcAgentRuleConfigRepository;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class AgentDomainService {

    @Autowired
    private RedisService redisService;
    @Autowired
    private IPipelineRpcService pipelineRpcService;
    @Autowired
    private QcAgentRuleConfigRepository qcAgentRuleConfigRepository;
    @Autowired
    private HttpService httpService;
    @Autowired
    private CoverageRepository coverageRepository;
    @Autowired
    private AgentQueryDomainService agentQueryDomainService;
    private static final String AGENT_PREFIX = "AGENT_IP";
    private static final String AGENT_ANALYZE_ALERT = "非try{}代码块、注释代码，不支持添加规则";

    public PageRuleConfigResp pageQueryAgentRuleConfig(PageRuleConfigReq req) {
        Page<Object> page = PageHelper.startPage(req.getPage(), req.getSize());
        List<QcAgentRuleConfigEntityDO> ruleConfigEntityDOS = qcAgentRuleConfigRepository.queryAgentRuleConfigPage(req);
        List<RuleConfigVO> ruleConfigVOS = new ArrayList<>();
        if (ruleConfigEntityDOS != null) {
            for (QcAgentRuleConfigEntityDO configEntityDO : ruleConfigEntityDOS) {
                RuleConfigVO ruleConfigVO = ruleConfigEntityDO2RuleConfigResp(configEntityDO);
                ruleConfigVOS.add(ruleConfigVO);
            }
        }
        return PageRuleConfigResp.buildSelf(ruleConfigVOS, page.getTotal());
    }

    public Map<String, Object> pullConfig(String appId, String dynEnv, String agentIp) {
        Map<String, Object> conf = new HashMap<>();
        if (StringUtils.isNotEmpty(dynEnv) && dynEnv.endsWith("dev")) {
            return conf;
        }
        RuleConfigReq ruleConfigReq = new RuleConfigReq();
        ruleConfigReq.setAppid(appId);
        String versionCode = pipelineRpcService.getVersionSimpleVO(appId, dynEnv);
        ruleConfigReq.setVersionCode(versionCode);
        List<QcAgentRuleConfigEntityDO> configEntityDOList = qcAgentRuleConfigRepository.queryAgentRuleConfig(ruleConfigReq);
        conf.put("command", AgentCommandEnum.CONFIG.name());
        conf.put("versionCode", versionCode);
        conf.putAll(convertToMap(configEntityDOList));
        if (StringUtil.isNotBlank(agentIp)) {
            String key = AGENT_PREFIX + "_" + appId + "_" + versionCode;
            redisService.opsForZSetAddFirst(key, agentIp);
            log.info("agent report success. appId : {}, env : {}, ip : {}", appId, dynEnv, agentIp);
        }
        return conf;
    }

    public RuleConfigVO queryAgentRuleConfig(Long id) {
        QcAgentRuleConfigEntityDO configEntityDO = qcAgentRuleConfigRepository.queryAgentRuleConfig(id);
        if (configEntityDO == null) {
            return null;
        }
        return ruleConfigEntityDO2RuleConfigResp(configEntityDO);
    }

    public JSONArray queryAnalyze(ExceptionInfoReq req) {
        if (null != req.getCommitId()) {
//            CoverageRecordEntityDO recordEntityDO = coverageRepository.queryCoverageRecordById(req.getId());
            CoveragePublishVO entity = new CoveragePublishVO();
            entity.setAppId(req.getAppId());
            entity.setVersionCode(req.getVersionCode());
            CoveragePublishVO coveragePublishVO = coverageRepository.getLatestPublishRecordByEntity(entity);
            if (null != coveragePublishVO) {
                if (!req.getCommitId().equals(coveragePublishVO.getCommitId())) {
                    throw new ServiceException("当前覆盖率报告非最新release分支代码，请重新构建、部署并生成新的覆盖率报告后再添加规则");
                }
            }
            VersionReleaseBranchCommitQuery query = new VersionReleaseBranchCommitQuery();
            query.setAppId(req.getAppId());
            query.setVersionCode(req.getVersionCode());
            String gitCommit = pipelineRpcService.queryVersionReleaseBranchCommit(query);
            if(StringUtil.isNotBlank(gitCommit) && !gitCommit.equals(req.getCommitId())){
                throw new ServiceException("当前覆盖率报告非最新release分支代码，请重新构建、部署并生成新的覆盖率报告后再添加规则");
            }
        }
        Map<String, Object> analyzeCommand = new HashMap<>();
        analyzeCommand.put("command", AgentCommandEnum.ANALYZE.name());
        analyzeCommand.put("appid", req.getAppId());
        analyzeCommand.put("versionCode", req.getVersionCode());
        analyzeCommand.put("position", String.format("%s:%s", req.getClassPath(), req.getLineNum()));
        String body = "";
        String key = AGENT_PREFIX + "_" + req.getAppId() + "_" + req.getVersionCode();
        for (String ip : getAgentIpList(key)) {
            String url = String.format("http://%s:29090/command", ip);
            try {
                String resp = httpService.doPost(url, JSON.toJSONString(analyzeCommand));
                if (StringUtils.isEmpty(resp) || JSON.parseObject(resp).isEmpty()) {
                    continue;
                }
                if (JSON.parseObject(resp).containsKey("data")) {
                    return JSON.parseObject(resp).getJSONArray("data");
                } else {
                    body = "ALERT";
                    break;
                }
            } catch (ServiceException e) {
                redisService.opsForZSetRemove(key, ip);
                log.error("请求查询异常信息列表失败. url : {}", url, e);
            } catch (Exception e) {
                redisService.opsForZSetRemove(key, ip);
                log.error("请求查询异常信息列表失败. url : {}", url, e);
            }
        }
        if (StringUtils.isEmpty(body)) {
            throw new ServiceException("agent未挂载，请重新构建、部署release分支并生成新的覆盖率报告后，再进行代码行注入");
        }
        if (body.equals("ALERT")) {
            throw new ServiceException(AGENT_ANALYZE_ALERT);
        }
        return new JSONArray();
    }

    public void batchRemoveByVersionCodes(List<String> versionCodes) {
        if (CollectionUtil.isEmpty(versionCodes)) {
            return;
        }
        for (String versionCode : versionCodes) {
            qcAgentRuleConfigRepository.batchRemoveByVersionCode(versionCode);
        }
    }

    private RuleConfigVO ruleConfigEntityDO2RuleConfigResp(QcAgentRuleConfigEntityDO configEntityDO) {
        RuleConfigVO ruleConfigVO = new RuleConfigVO();
        ruleConfigVO.setId(configEntityDO.getId());
        ruleConfigVO.setAppid(configEntityDO.getAppid());
        ruleConfigVO.setVersionCode(configEntityDO.getVersionCode());
        ruleConfigVO.setCreator(configEntityDO.getCreator());
        ruleConfigVO.setGmtCreate(configEntityDO.getGmtCreate());
        ruleConfigVO.setModifier(configEntityDO.getModifier());
        ruleConfigVO.setGmtModified(configEntityDO.getGmtModified());
        ruleConfigVO.setStatus(configEntityDO.getStatus());
        ChaosExceptionTypeEnum exceptionTypeEnum = ChaosExceptionTypeEnum.valueOf(configEntityDO.getChaosExceptionType());
        ruleConfigVO.setExceptionType(configEntityDO.getChaosExceptionType());
        ruleConfigVO.setExceptionTypeName(exceptionTypeEnum.getValue());

        ChaosRuleTypeEnum ruleTypeEnum = ChaosRuleTypeEnum.valueOf(configEntityDO.getRuleType());
        ruleConfigVO.setRuleTypeName(ruleTypeEnum.getValue());
        ruleConfigVO.setRuleType(configEntityDO.getRuleType());
        ruleConfigVO.setInjection(configEntityDO.getInjection());
        ruleConfigVO.setInjectionRuleName(configEntityDO.getInjectionRuleName());
        handlerRuleBody(configEntityDO, ruleConfigVO);
        ruleConfigVO.setButtons(setButtons(configEntityDO));
        return ruleConfigVO;
    }

    private List<Button> setButtons(QcAgentRuleConfigEntityDO configEntityDO) {
        List<Button> result = new ArrayList<>();
        if (null == configEntityDO) {
            return Collections.emptyList();
        }
        if (configEntityDO.getRuleType().equals(ChaosRuleTypeEnum.ServiceClass.name())) {
            if (configEntityDO.getStatus().equals(0)) {
                result.add(new Button("删除", "delete", 1));
                result.add(new Button("启用", "on", 2));
            } else if (configEntityDO.getStatus().equals(1)) {
                result.add(new Button("删除", "delete", 1));
                result.add(new Button("停用", "off", 2));
            } else if (configEntityDO.getStatus().equals(2)) {
                result.add(new Button("删除", "delete", 1));
            } else {
                log.warn("status is invalid. id : {}", configEntityDO.getId());
            }
        } else {
            if (configEntityDO.getStatus().equals(0)) {
                result.add(new Button("编辑", "edit", 1));
                result.add(new Button("删除", "delete", 2));
                result.add(new Button("启用", "on", 3));
            } else if (configEntityDO.getStatus().equals(1)) {
                result.add(new Button("编辑", "edit", 1));
                result.add(new Button("删除", "delete", 2));
                result.add(new Button("停用", "off", 3));
            } else if (configEntityDO.getStatus().equals(2)) {
                result.add(new Button("删除", "delete", 1));
            } else {
                log.warn("status is invalid. id : {}", configEntityDO.getId());
            }
        }
        result.add(new Button("复制", "copy", 4));
        return result;
    }

    private void handlerRuleBody(QcAgentRuleConfigEntityDO configEntityDO, RuleConfigVO ruleConfigVO) {
        Map<String, List<Map<String, String>>> chaosExceptionTypeMap = agentQueryDomainService.getChaosExceptionTypeList();
        Map<String, Object> ruleBody = configEntityDO.getRuleBody();
        String exceptedResult = "";
        if (configEntityDO.getRuleType().equals(ChaosRuleTypeEnum.HttpClient.name())) {
            List<Map<String, String>> httpChaosExceptionTypes = chaosExceptionTypeMap.get(ChaosRuleTypeEnum.HttpClient.name());
            if (configEntityDO.getChaosExceptionType().equals(ChaosExceptionTypeEnum.ServerDown.name())) {
                exceptedResult = httpChaosExceptionTypes.stream()
                        .filter(map -> map.get("name").equals(ChaosExceptionTypeEnum.ServerDown.name()))
                        .map(map -> map.get("exception")).findFirst().orElse(null);
            } else if (configEntityDO.getChaosExceptionType().equals(ChaosExceptionTypeEnum.TimeoutException.name())) {
                exceptedResult = httpChaosExceptionTypes.stream()
                        .filter(map -> map.get("name").equals(ChaosExceptionTypeEnum.TimeoutException.name()))
                        .map(map -> map.get("exception")).findFirst().orElse(null);
            }
        } else if (configEntityDO.getRuleType().equals(ChaosRuleTypeEnum.DubboClient.name())) {
            List<Map<String, String>> dubboChaosExceptionTypes = chaosExceptionTypeMap.get(ChaosRuleTypeEnum.DubboClient.name());
            if (configEntityDO.getChaosExceptionType().equals(ChaosExceptionTypeEnum.DegradeException.name())) {
                exceptedResult = dubboChaosExceptionTypes.stream()
                        .filter(map -> map.get("name").equals(ChaosExceptionTypeEnum.DegradeException.name()))
                        .map(map -> map.get("exception")).findFirst().orElse(null);
            } else if (configEntityDO.getChaosExceptionType().equals(ChaosExceptionTypeEnum.FlowException.name())) {
                exceptedResult = dubboChaosExceptionTypes.stream()
                        .filter(map -> map.get("name").equals(ChaosExceptionTypeEnum.FlowException.name()))
                        .map(map -> map.get("exception")).findFirst().orElse(null);
            } else if (configEntityDO.getChaosExceptionType().equals(ChaosExceptionTypeEnum.ServerDown.name())) {
                exceptedResult = dubboChaosExceptionTypes.stream()
                        .filter(map -> map.get("name").equals(ChaosExceptionTypeEnum.ServerDown.name()))
                        .map(map -> map.get("exception")).findFirst().orElse(null);
            } else if (configEntityDO.getChaosExceptionType().equals(ChaosExceptionTypeEnum.TimeoutException.name())) {
                exceptedResult = dubboChaosExceptionTypes.stream()
                        .filter(map -> map.get("name").equals(ChaosExceptionTypeEnum.TimeoutException.name()))
                        .map(map -> map.get("exception")).findFirst().orElse(null);
            }
        }
        if (MapUtils.isNotEmpty(ruleBody)) {
            ruleConfigVO.setRuleDetail(JSON.toJSONString(ruleBody));
            if (ruleBody.containsKey("exceptionClassName")) {
                exceptedResult = String.format("%s(%s)", ruleBody.get("exceptionClassName"), ruleBody.get("constructionMethodParamValue"));
            }
            if (ruleBody.containsKey("mockResponseBody")) {
                exceptedResult = JSON.toJSONString(ruleBody.get("mockResponseBody"));
            }
            if (ruleBody.containsKey("mockTag")) {
                ruleConfigVO.setMockTag(String.valueOf(ruleBody.get("mockTag")));
            }
            if (ruleBody.containsKey("mockTagName")) {
                ruleConfigVO.setMockTagName(String.valueOf(ruleBody.get("mockTagName")));
            }
            if (ruleBody.containsKey("versionName")) {
                ruleConfigVO.setVersionName(String.valueOf(ruleBody.get("versionName")));
            }
        }
        ruleConfigVO.setExpectedResult(exceptedResult);
    }

    private static Map<String, Object> convertToMap(List<QcAgentRuleConfigEntityDO> ruleConfigs) {
        Map<String, List<Map<String, String>>> serviceClassCustomExceptionConfig = new HashMap<>();
        Map<String, List<Map<String, Object>>> middleWareConfig = new HashMap<>();
        for (QcAgentRuleConfigEntityDO config : ruleConfigs) {
            ChaosRuleTypeEnum ruleTypeEnum = ChaosRuleTypeEnum.valueOf(config.getRuleType());
            if (ruleTypeEnum == ChaosRuleTypeEnum.ServiceClass) {
                serviceClassConfigHandler(config, serviceClassCustomExceptionConfig);
                continue;
            }
            if (ruleTypeEnum == ChaosRuleTypeEnum.DubboClient) {
                dubboClientConfigHandler(config, middleWareConfig);
                continue;
            }
            if (ruleTypeEnum == ChaosRuleTypeEnum.HttpClient) {
                httpClientConfigHandler(config, middleWareConfig);
            }
        }

        Map<String, Object> resultMap = new HashMap<>();

        try {
            resultMap.put("serverClassCustomException", serviceClassCustomExceptionConfig);
            resultMap.put("middleware", middleWareConfig);
        } catch (Exception e) {
            log.error("", e);
        }

        return resultMap;
    }

    private Set<String> getAgentIpList(String key) {
        if (redisService.hasKey(key)) {
            return redisService.opsForZSetRang(key, 0, -1);
        }
        return Collections.EMPTY_SET;
    }

    private static void serviceClassConfigHandler(QcAgentRuleConfigEntityDO config, Map<String, List<Map<String, String>>> serviceClassCustomExceptionConfig) {
        String className = config.getHeuristicClassName();
        Map<String, String> detailMap = new HashMap<>();
        detailMap.put("id", String.valueOf(config.getId()));
        detailMap.put("type", config.getRuleType());
        detailMap.put("method", config.getMethodName());
        detailMap.put("exceptionClassName", String.valueOf(config.getRuleBody().get("exceptionClassName")));
        detailMap.put("lineNumber", String.valueOf(config.getRuleBody().get("lineNumber")));
        detailMap.put("constructionMethodParamName", String.valueOf(config.getRuleBody().get("constructionMethodParamName")));
        detailMap.put("constructionMethodParamValue", String.valueOf(config.getRuleBody().get("constructionMethodParamValue")));

        if (!serviceClassCustomExceptionConfig.containsKey(className)) {
            serviceClassCustomExceptionConfig.put(className, new ArrayList<>());
        }
        serviceClassCustomExceptionConfig.get(className).add(detailMap);
    }

    private static void dubboClientConfigHandler(QcAgentRuleConfigEntityDO config, Map<String, List<Map<String, Object>>> middleWareConfig) {
        String className = config.getHeuristicClassName();
        if (StringUtils.isNotBlank(config.getMethodName())) {
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("id", String.valueOf(config.getId()));
            if (MapUtils.isNotEmpty(config.getRuleBody())) {
                detailMap.put("mockTag", Objects.isNull(config.getRuleBody().get("mockTag")) ? null : config.getRuleBody().get("mockTag"));
                detailMap.put("mockRule", Objects.isNull(config.getRuleBody().get("mockRule")) ? null : config.getRuleBody().get("mockRule"));
                detailMap.put("mockResponseBody", Objects.isNull(config.getRuleBody().get("mockResponseBody")) ? null : config.getRuleBody().get("mockResponseBody"));
            }
            String key = String.format("%s.%s.%s#%s", config.getRuleType(), config.getChaosExceptionType(), className.trim(), config.getMethodName().trim());
            if (!middleWareConfig.containsKey(key)) {
                middleWareConfig.put(key, new ArrayList<>());
            }
            middleWareConfig.get(key).add(detailMap);
        }
    }

    private static void httpClientConfigHandler(QcAgentRuleConfigEntityDO config, Map<String, List<Map<String, Object>>> middleWareConfig) {
        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("id", String.valueOf(config.getId()));
        if (MapUtils.isNotEmpty(config.getRuleBody())) {
            detailMap.put("mockTag", Objects.isNull(config.getRuleBody().get("mockTag")) ? null : config.getRuleBody().get("mockTag"));
            detailMap.put("mockRule", Objects.isNull(config.getRuleBody().get("mockRule")) ? null : config.getRuleBody().get("mockRule"));
            detailMap.put("mockResponseBody", Objects.isNull(config.getRuleBody().get("mockResponseBody")) ? null : config.getRuleBody().get("mockResponseBody"));
        }
        String key = String.format("%s.%s.%s", config.getRuleType(), config.getChaosExceptionType(), config.getHeuristicClassName().trim());
        if (!middleWareConfig.containsKey(key)) {
            middleWareConfig.put(key, new ArrayList<>());
        }
        middleWareConfig.get(key).add(detailMap);
    }

}
