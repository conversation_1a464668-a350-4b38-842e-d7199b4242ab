package com.zto.devops.qc.domain.service;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.rpc.VersionStatus;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordErrorMsgEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.devops.qc.client.model.dto.InterfaceCoverageEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestPlanEntityDO;
import com.zto.devops.qc.client.model.rpc.pipeline.ApplicationVO;
import com.zto.devops.qc.client.model.rpc.project.VersionInfoVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.*;
import com.zto.devops.qc.client.model.testmanager.coverage.query.*;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmTestReportVO;
import com.zto.devops.qc.client.service.coverage.model.req.CoverageRecordReq;
import com.zto.devops.qc.client.service.coverage.model.req.CoverageReportClassInfoReq;
import com.zto.devops.qc.client.service.coverage.model.req.OssBasicReq;
import com.zto.devops.qc.client.service.coverage.model.req.QueryReasonListReq;
import com.zto.devops.qc.client.service.coverage.model.resp.CoverageRecordResp;
import com.zto.devops.qc.client.service.coverage.model.resp.CoverageReportClassInfoResp;
import com.zto.devops.qc.client.service.coverage.model.resp.QueryReasonListResp;
import com.zto.devops.qc.domain.converter.CoverageDomainConverter;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.CoverageRepository;
import com.zto.devops.qc.domain.gateway.repository.ITmTestPlanRepository;
import com.zto.devops.qc.domain.gateway.repository.InterfaceCoverageRepository;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/9/22 10:55
 */
@Service
@Slf4j
public class CoverageQueryDomainService extends BaseDomainService {

    @Autowired
    private CoverageRepository coverageRepository;
    @Autowired
    private ITmTestPlanRepository tmTestPlanRepository;
    @Autowired
    private QcConfigBasicService qcConfigBasicService;
    @Autowired
    private ZtoOssService ztoOssService;
    @Autowired
    private IProjectRpcService projectRpcService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IPipelineRpcService pipelineRpcService;
    @Autowired
    private CoverageDomainConverter converter;
    @Autowired
    private InterfaceCoverageRepository interfaceCoverageRepository;

    public List<CoverageVersionRateVO> getVersionRecordRateList(List<String> versionCodes) {
        if (CollectionUtils.isEmpty(versionCodes)) {
            return Collections.emptyList();
        }

        List<CoverageVersionRateVO> coverageVersionRateVOS = new ArrayList<>();
        List<List<String>> partition = Lists.partition(versionCodes, 200);
        for (List<String> partitionVersionCodes : partition) {
            List<CoverageVersionRateVO> versionRecordRateList = coverageRepository.getVersionRecordRateList(partitionVersionCodes);
            Map<String, CoverageVersionRateVO> map = versionRecordRateList.stream()
                .collect(Collectors.toMap(CoverageVersionRateVO::getVersionCode, item -> item, (o1, o2) -> o1));
            for (String versionCode : partitionVersionCodes) {
                coverageVersionRateVOS.add(populateRate(map.get(versionCode)));
            }
        }

        return coverageVersionRateVOS;
    }

    public CoverageVersionRateVO getCoverageVersionRate(CoverageVersionRateQuery query) {
        CoverageVersionRateVO rateVO =
                coverageRepository.getVersionRecordRate(query.getVersionCode());
        return populateRate(rateVO);
    }

    private CoverageVersionRateVO populateRate(CoverageVersionRateVO rateVO) {
        if (rateVO == null) {
            CoverageVersionRateVO result = new CoverageVersionRateVO();
            result.setBranchVersionRate(0);
            result.setMasterVersionRate(0);
            return result;
        }
        rateVO.setBranchVersionRate(0);
        rateVO.setMasterVersionRate(0);
        if (null != rateVO.getBranchCodeCoverNum() && null != rateVO.getBranchCodeSum()) {
            rateVO.setBranchVersionRate(rateVO.getBranchCodeSum() == 0 ? 0 :
                (int) Math.floor((float) rateVO.getBranchCodeCoverNum() * 100 / rateVO.getBranchCodeSum()));
        }
        if (null != rateVO.getMasterCodeCoverNum() && null != rateVO.getMasterCodeSum()) {
            rateVO.setMasterVersionRate(rateVO.getMasterCodeSum() == 0 ? 0 :
                (int) Math.floor((float) rateVO.getMasterCodeCoverNum() * 100 / rateVO.getMasterCodeSum()));
        }
        return rateVO;
    }

    public PageCoverageRecordVO getCoverageReportListPage(CoverageRecordPageQuery query) {
        Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        List<CoverageRecordVO> coverageRecordVOList =
                coverageRepository.getCoverageRecordList(query);
        String testPlanCode = buildCoverageTestPlan(query);
        if (StringUtils.isNotEmpty(testPlanCode)) {
            coverageRecordVOList.forEach(x -> x.setTestPlanCode(testPlanCode));
        }
        PageCoverageRecordVO pageCoverageRecordVO = new PageCoverageRecordVO();
        if (CollectionUtil.isEmpty(coverageRecordVOList)) {
            pageCoverageRecordVO.setList(new ArrayList<>());
            pageCoverageRecordVO.setTotal(0L);
            return pageCoverageRecordVO;
        }
        Map<String, BigDecimal> appIdInterfaceCoverageRateMap = calculateInterfaceCoverage(query.getVersionCode());
        Map<String, ApplicationVO> appIdApplicationVOMap = new HashMap<>();
        List<String> appIds = coverageRecordVOList.stream().map(r -> r.getAppId()).collect(Collectors.toList());
        List<ApplicationVO> applicationVOS = pipelineRpcService.listApplicationByAppIds(appIds);
        if (CollectionUtil.isNotEmpty(applicationVOS)) {
            appIdApplicationVOMap = applicationVOS.stream().collect(Collectors.toMap(ApplicationVO::getAppId, Function.identity(), (key1, key2) -> key2));
        }
        for (CoverageRecordVO coverageRecordVO : coverageRecordVOList) {
            ApplicationVO applicationVO = appIdApplicationVOMap.get(coverageRecordVO.getAppId());
            if (applicationVO != null) {
                coverageRecordVO.setApplicationCode(applicationVO.getCode());
            }
            if (appIdInterfaceCoverageRateMap.containsKey(coverageRecordVO.getAppId() + "_" + coverageRecordVO.getBranchCommitId())) {
                coverageRecordVO.setInterfaceCoverageRate(appIdInterfaceCoverageRateMap.get(coverageRecordVO.getAppId() + "_" + coverageRecordVO.getBranchCommitId()));
            }
        }
        pageCoverageRecordVO.setList(coverageRecordVOList);
        pageCoverageRecordVO.setTotal(page.getTotal());
        return pageCoverageRecordVO;
    }

    /**
     * 计算接口覆盖率
     *
     * @param versionCode
     * @return
     */
    private Map<String, BigDecimal> calculateInterfaceCoverage(String versionCode) {
        List<InterfaceCoverageEntityDO> interfaceCoverageEntityDOList = interfaceCoverageRepository.queryInterfaceCoverageList(versionCode);
        if (CollectionUtil.isEmpty(interfaceCoverageEntityDOList)) {
            return new HashMap<>();
        }
        return calculateInterfaceCoverage(interfaceCoverageEntityDOList);
    }

    public static Map<String, BigDecimal> calculateInterfaceCoverage(List<InterfaceCoverageEntityDO> coverageList) {
        Map<String, Integer> numeratorMap = new HashMap<>();
        Map<String, Integer> denominatorMap = new HashMap<>();
        for (InterfaceCoverageEntityDO entity : coverageList) {
            String key = entity.getAppId() + "_" + entity.getCommitId();
            denominatorMap.put(key, denominatorMap.getOrDefault(key, 0) + 1);
            if (entity.getIsCovered() == 1) {
                numeratorMap.put(key, numeratorMap.getOrDefault(key, 0) + 1);
            }
        }
        Map<String, BigDecimal> coverageMap = new HashMap<>();
        for (String key : denominatorMap.keySet()) {
            int numerator = numeratorMap.getOrDefault(key, 0);
            int denominator = denominatorMap.get(key);
            BigDecimal coverage = (denominator == 0) ? BigDecimal.ZERO : BigDecimal.valueOf(numerator).divide(BigDecimal.valueOf(denominator), 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
            coverageMap.put(key, coverage);
        }
        return coverageMap;
    }

    public PageCoverageTaskVO getPageCoverageTask(CoverageTaskQuery query) {
        Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        List<CoverageTaskVO> list = coverageRepository.selectTaskIdFromCoverage(query);
        PageCoverageTaskVO pageCoverageTaskVO = new PageCoverageTaskVO();
        if (CollectionUtil.isEmpty(list)) {
            pageCoverageTaskVO.setList(new ArrayList<>());
            pageCoverageTaskVO.setTotal(0L);
            return pageCoverageTaskVO;
        }
        List<String> taskList = list.stream().map(CoverageTaskVO::getTaskId)
                .collect(Collectors.toList());
        query.setTaskIdList(taskList);
        List<CoverageTaskVO> coverageList = coverageRepository.selectCoverageByTaskId(query);
        coverageList.forEach(this::buildCoverageList);
        list.forEach(t -> buildCoverageTaskList(t, coverageList));
        pageCoverageTaskVO.setList(list);
        pageCoverageTaskVO.setTotal(page.getTotal());
        return pageCoverageTaskVO;
    }

    public CoverageResultVO getCoverageResult(CoverageResultQuery query) {
        if (null == query || StringUtil.isBlank(query.getVersionCode())) {
            return CoverageResultVO.init();
        }
        //待筛选不达标应用集合
        List<CoverageAppInfoVO> voList =
                coverageRepository.selectListByVersionCode(query.getVersionCode(), query.getAppIdList());
        if (CollectionUtil.isEmpty(voList)) {
            return CoverageResultVO.init();
        }
        //不达标应用集合
        List<CoverageAppInfoVO> filterList = CoverageAppInfoVO.filterFailedList(query.getVersionCode(), voList);
        //查询上次不达标原因
        TmTestReportVO reportEntity = coverageRepository.getTmTestReport(query);
        //原因填充
        fillReason(filterList, reportEntity);
        return CoverageResultVO.buildSelf(filterList, qcConfigBasicService.getCoverageConfig().getCodeCoverageReason());
    }

    public CoverageResultVO getCoverageResultList(CoverageResultListQuery query) {
        if (null == query || CollectionUtil.isEmpty(query.getVersionCodeList())) {
            return CoverageResultVO.init();
        }
        //待筛选不达标应用集合
        List<CoverageAppInfoVO> voList =
                coverageRepository.selectListByVersionCodeList(query.getVersionCodeList(), query.getAppIdList());
        if (CollectionUtil.isEmpty(voList)) {
            return CoverageResultVO.init();
        }
        //不达标应用集合
        List<CoverageAppInfoVO> filterList = this.filterFailedByVersionCodeList(voList);

        //达标应用集合 清空之前填写的不达标原因
        List<CoverageAppInfoVO> filterSuccessList = CoverageAppInfoVO.filterSuccessByVersionCodeList(voList);
        filterSuccessList.forEach(t -> {
            User user = query.getTransactor();
            coverageRepository.resetCoverageReason(t.getVersionCode(), t.getAppId(), user);
        });
        return CoverageResultVO.buildSelf(filterList, qcConfigBasicService.getCoverageConfig().getCodeCoverageReason());
    }

    public CoverageRecordResp getLatestCoverageReport(CoverageRecordReq req) {
        return coverageRepository.getLatestCoverageReport(req);
    }

    public CoverageReportClassInfoResp getCoverageReportClassInfo(CoverageReportClassInfoReq req) {
        String key = req.getAppId() + ":" + req.getVersionCode() + ":" + req.getCommitId() + ":" + req.getPackages() + "." + req.getClassName();
        if (redisService.hasKey(key)) {
            return JSONObject.parseObject(redisService.getKey(key), CoverageReportClassInfoResp.class);
        }
        return null;
    }

    private List<CoverageAppInfoVO> filterFailedByVersionCodeList(List<CoverageAppInfoVO> voList) {
        List<CoverageAppInfoVO> resultList = new ArrayList<>();
        Map<String, List<CoverageAppInfoVO>> voMap = voList.stream().collect(Collectors.groupingBy(CoverageAppInfoVO::getVersionCode));
        for (Map.Entry<String, List<CoverageAppInfoVO>> versionEntry : voMap.entrySet()) {
            resultList.addAll(this.filterFailedList(versionEntry.getKey(), versionEntry.getValue()));
        }
        return resultList;
    }

    private List<CoverageAppInfoVO> filterFailedList(String versionCode, List<CoverageAppInfoVO> voList) {
        List<CoverageAppInfoVO> resultList = new ArrayList<>();
        Map<String, List<CoverageAppInfoVO>> voMap = voList.stream().collect(Collectors.groupingBy(CoverageAppInfoVO::getAppId));

        for (Map.Entry<String, List<CoverageAppInfoVO>> entry : voMap.entrySet()) {
            List<CoverageAppInfoVO> values = entry.getValue();
            boolean flag = false;
            boolean masterFlag = false;
            boolean branchFlag = false;
            String reason = "";
            if (CollectionUtil.isEmpty(values)) {
                continue;
            }
            String versionName = values.get(0).getVersionName();
            for (CoverageAppInfoVO vo : values) {
                reason = StringUtils.isNotBlank(vo.getReason()) ? vo.getReason() : "";
                // 1.有一个达标不弹框
                // 2.两个都没达标但都是代码没有实现类不弹框
                if (vo.getStatus().equals(RecordStatusEnum.SUCCEED.name())
                        && (vo.getRecordRate().compareTo(vo.getStandardRate()) >= 0)) {
                    flag = true;
                }
                if ("BRANCH".equals(vo.getRecordType())
                        && RecordStatusEnum.NEEDLESS.name().equals(vo.getStatus())
                        && RecordErrorMsgEnum.NO_IMPLEMENTATION_CLASS.getValue().equals(vo.getRecordErrorMsg())) {
                    branchFlag = true;
                }
            }
            List<BigDecimal> rateList = values.stream()
                    .filter(item -> RecordTypeEnum.BRANCH.name().equals(item.getRecordType()))
                    .map(CoverageAppInfoVO::getRecordRate)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (!flag && !branchFlag) {
                CoverageAppInfoVO appInfoVO = CoverageAppInfoVO.builder()
                        .versionCode(versionCode)
                        .versionName(versionName)
                        .appId(entry.getKey())
                        .reason(reason)
                        .recordRate(CollectionUtil.isNotEmpty(rateList) ? rateList.get(0) : BigDecimal.ZERO)
                        .build();
                appInfoVO.addAppInfoVOReason();
                resultList.add(appInfoVO);
            }
        }
        return resultList;
    }

    public String getReportUrl(OssBasicReq req) {
        String url = generateStandardUrl(req);
        if (StringUtil.isEmpty(url)) {
            log.error("获取覆盖率报告地址url为空:{}", JsonUtil.toJSON(req));
            throw new ServiceException("获取覆盖率报告地址异常,请联系管理员");
        }
        return url;
    }

    private String generateStandardUrl(OssBasicReq req) {
        Map<String, String> data = getData(req.getFileName());
        String[] requiredKeys = {"versionCode", "branch", "appId", "commitId", "date"};
        for (String key : requiredKeys) {
            if (!data.containsKey(key)) {
                throw new ServiceException("缺失必要数据字段: " + key);
            }
        }

        String reportHost = getReportHost();
        return String.format(
                qcConfigBasicService.getCoverageConfig().getReportUrl(),
                reportHost,
                req.getProductCode(),
                data.get("versionCode"),
                data.get("branch"),
                data.get("appId"),
                data.get("commitId"),
                data.get("date")
        );
    }

    public QueryReasonListResp queryReasonList(QueryReasonListReq req) {
        log.info("查询不达标原因下拉框， QueryReasonListResp{}", JSON.toJSONString(req));
        QueryReasonListResp resp = new QueryReasonListResp();
        String remarkString = coverageRepository.selectCoverageRemark(req.getVersionCode(), req.getAppId());
        if (StringUtils.isNotBlank(remarkString)) {
            List<String> reasonList = new ArrayList<>();
            String customReason = StringUtils.EMPTY;
            String regular = "(?<=[1-9]\\.\\s|[1-9][0-9]\\.\\s)([\\d\\D]*?)(?=\\$)";
            Pattern pattern = Pattern.compile(regular);
            Matcher m = pattern.matcher(remarkString);
            while (m.find()) {
                String content = m.group(1);
                if (StringUtils.isNotBlank(content)) {
                    if (content.contains("自定义：")) {
                        customReason = content.substring(4);
                    } else {
                        reasonList.add(content);
                    }
                }
            }
            // 系统自动回填的原因，没有序号
            if (CollectionUtils.isEmpty(reasonList) && StringUtils.isBlank(customReason)) {
                reasonList.add(remarkString);
            }
            resp.setReasonList(reasonList);
            resp.setCustomReason(customReason);
        }
        String allReasons = qcConfigBasicService.getCoverageConfig().getCodeCoverageReason();
        if (StringUtils.isNotBlank(allReasons)) {
            List<String> allReasonList = Arrays.asList(allReasons.split(";"));
            resp.setAllReasonList(allReasonList);
        }
        return resp;
    }

    /**
     * 回归通过之后不显示不达标原因编辑按钮
     */
    public boolean isButtonDisplay(String versionCode) {
        if (StringUtils.isBlank(versionCode)) {
            return false;
        }
        VersionInfoVO versionInfoVO = this.queryVersionVOByCode(versionCode);
        List<VersionStatus> statusList = Arrays.asList(VersionStatus.REGRESSED, VersionStatus.WAIT_AUDIT,
                VersionStatus.WAIT_RELEASE, VersionStatus.RELEASING, VersionStatus.ACCEPTING, VersionStatus.ACCEPTED,
                VersionStatus.CLOSED, VersionStatus.RELEASE_FAILED, VersionStatus.UNKNOWN);
        if (null != versionInfoVO && StringUtils.isNotBlank(versionInfoVO.getStatus())) {
            VersionStatus versionStatus = VersionStatus.getByName(versionInfoVO.getStatus());
            if (!statusList.contains(versionStatus)) {
                return true;
            }
        }
        return false;
    }

    public VersionInfoVO queryVersionVOByCode(String versionCode) {
        if (StringUtils.isBlank(versionCode)) {
            return null;
        }
        try {
            return projectRpcService.findVersionBaseInfoQuery(versionCode);
        } catch (Exception e) {
            log.error("操作失败！调用项目域获取版本状态异常");
            return null;
        }
    }

    private String buildCoverageTestPlan(CoverageRecordPageQuery query) {
        List<TmTestPlanVO> planList = coverageRepository.getTestPlan(query);
        return planList.stream().findFirst().orElse(new TmTestPlanVO()).getCode();
    }

    private List<CoverageTaskVO> replaceListTaskId(List<CoverageTaskVO> filterList) {
        filterList.forEach(x -> x.setTaskId(String.valueOf(x.getId())));
        return filterList;
    }

    private void buildCoverageList(CoverageTaskVO vo) {
        vo.setAppNums(1);
        vo.setRecordStatusDesc(vo.getRecordStatus().getValue());
        vo.setRecordTypeDesc(vo.getRecordType().getValue());
        vo.setDiffTypeDesc(vo.getDiffType().getValue());
    }

    private void buildCoverageTaskList(CoverageTaskVO vo, List<CoverageTaskVO> coverageList) {
        List<CoverageTaskVO> filterList = coverageList.stream()
                .filter(x -> vo.getTaskId().equals(x.getTaskId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(filterList)) {
            return;
        }
        calculateStandardRate(vo, filterList);
    }

    /**
     * 覆盖率、应用数、生成时间、更新时间
     */
    private void calculateStandardRate(CoverageTaskVO vo, List<CoverageTaskVO> filterList) {
        int codeCoverNum = 0;
        int codeSum = 0;
        int appNums = 0;
        for (CoverageTaskVO coverage : filterList) {
            codeCoverNum = codeCoverNum + coverage.getCodeCoverNum();
            codeSum = codeSum + coverage.getCodeSum();
            appNums = appNums + coverage.getAppNums();
        }
        vo.setAppNums(appNums);
        vo.setRecordRate(new BigDecimal(0));
        if (codeSum != 0) {
            vo.setRecordRate(BigDecimal.valueOf(Math.floor((float) codeCoverNum * 100 / codeSum)));
        }
        vo.setDiffTypeDesc(vo.getDiffType().getValue());
        vo.setRecordTypeDesc(vo.getRecordType().getValue());
        vo.setRecordStatus(getRecordStatusByList(filterList));
        vo.setRecordStatusDesc(vo.getRecordStatus().getValue());
        vo.setRecordCreate(filterList.stream()
                .sorted(Comparator.comparing(CoverageTaskVO::getRecordCreate))
                .collect(Collectors.toList()).get(0).getRecordCreate());
        vo.setRecordModified(filterList.stream()
                .sorted(Comparator.comparing(CoverageTaskVO::getRecordModified).reversed())
                .collect(Collectors.toList()).get(0).getRecordModified());
        List<String> envList = filterList.stream()
                .map(CoverageTaskVO::getEnvName)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        vo.setEnvName(CollectionUtil.isEmpty(envList) ? "" : envList.get(0));
        vo.setChildren(replaceListTaskId(filterList));
    }

    private RecordStatusEnum getRecordStatusByList(List<CoverageTaskVO> filterList) {
        Map<RecordStatusEnum, Integer> map = new HashMap<>();
        map.put(RecordStatusEnum.INITIAL, 0);
        map.put(RecordStatusEnum.RUNNING, 0);
        map.put(RecordStatusEnum.NEEDLESS, 0);
        map.put(RecordStatusEnum.SUCCEED, 0);
        map.put(RecordStatusEnum.FAIL, 0);
        for (CoverageTaskVO vo : filterList) {
            if (map.containsKey(vo.getRecordStatus())) {
                map.put(vo.getRecordStatus(), map.get(vo.getRecordStatus()) + 1);
            }
        }
        // 只要有一个生成中 或者 待生成+任意状态 = 生成中
        if (map.get(RecordStatusEnum.RUNNING) > 0
                || (map.get(RecordStatusEnum.INITIAL) > 0
                && map.get(RecordStatusEnum.INITIAL) < filterList.size())) {
            return RecordStatusEnum.RUNNING;
        }
        // 全部是待生成 = 待生成
        if (map.get(RecordStatusEnum.INITIAL) == filterList.size()) {
            return RecordStatusEnum.INITIAL;
        }
        // 全部是无需生成 = 无需生成
        if (map.get(RecordStatusEnum.NEEDLESS) == filterList.size()) {
            return RecordStatusEnum.NEEDLESS;
        }
        // 全部都是成功 或者 全部都是成功+无需生成 = 生成成功
        if (map.get(RecordStatusEnum.SUCCEED) > 0
                && (map.get(RecordStatusEnum.SUCCEED)
                + map.get(RecordStatusEnum.NEEDLESS) == filterList.size())) {
            return RecordStatusEnum.SUCCEED;
        }
        // 只要有一个失败 = 生成失败
        if (map.get(RecordStatusEnum.FAIL) > 0) {
            return RecordStatusEnum.FAIL;
        }
        return null;
    }

    /**
     * 填充代码覆盖率不达标原因
     *
     * @param voList       不达标应用
     * @param reportEntity 上次不达标原因
     */
    private void fillReason(List<CoverageAppInfoVO> voList, TmTestReportVO reportEntity) {

        if (CollectionUtil.isEmpty(voList) || null == reportEntity || StringUtil.isBlank(reportEntity.getCodeCoverReason())) {
            return;
        }
        List<CoverageAppInfoVO> reasonList = null;
        try {
            reasonList = JSON.parseArray(reportEntity.getCodeCoverReason(),
                    CoverageAppInfoVO.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (CollectionUtil.isEmpty(reasonList)) {
            return;
        }
        //appId相同，复用上次不达标原因
        for (CoverageAppInfoVO vo : voList) {
            for (CoverageAppInfoVO reason : reasonList) {
                if (vo.getAppId().equals(reason.getAppId())) {
                    vo.setReason(reason.getReason());
                    break;
                }
            }
        }
    }

    public boolean checkCompleted(List<String> versionCodeList) {
        if (CollectionUtil.isEmpty(versionCodeList)) {
            return Boolean.FALSE;
        }
        return tmTestPlanRepository.countNotCheckListByVersionCodes(versionCodeList) < 1;
    }

    public String selectTestPlanByVersion(String versionCode) {
        TmTestPlanEntityDO testPlanByVersion = tmTestPlanRepository.getTestPlanByVersion(versionCode);
        if (null != testPlanByVersion) {
            return testPlanByVersion.getCode();
        }
        return StringUtils.EMPTY;
    }

    public Map<String, String> getData(String fileName) {
        Map map = new HashMap();
        if (StringUtils.isEmpty(fileName)) {
            return map;
        }
        String[] parts = fileName.split("/");
        map.put("branch", parts[1]);
        map.put("commitId", parts[2]);
        map.put("date", parts[3]);

        String[] part0 = parts[0].replace("-" + parts[1] + "-", "/").split("/");
        map.put("versionCode", part0[0]);
        map.put("appId", part0[1]);
        return map;
    }

    public static String getReportHost() {
        String env = System.getProperty("env");

        if ("pro".equalsIgnoreCase(env)) {
            return "devops.dev.ztosys.com";
        } else {
            String tag = "base";
            if (StringUtils.isNotEmpty(System.getProperty("titans.dubbo.tag"))) {
                tag = System.getProperty("titans.dubbo.tag");
            }
            return tag + "-luban-frontend-dme.test.ztosys.com";
        }
    }

}
