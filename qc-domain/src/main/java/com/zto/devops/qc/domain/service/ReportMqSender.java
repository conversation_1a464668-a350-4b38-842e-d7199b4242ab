package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.domain.gateway.mq.IProducerService;
import com.zto.devops.qc.client.enums.constants.BasicTagEnum;
import com.zto.devops.qc.client.model.issue.entity.*;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testmanager.plan.query.FindTmTestPlanQuery;
import com.zto.devops.qc.client.model.testmanager.report.event.TestReportSendEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ReportMqSender {

    @Autowired
    private IProducerService producerService;

    @Autowired
    private TmTestReportQueryDomainService tmTestReportQueryDomainService;

    @Async
    public void handleTestReportSendEvent(TestReportSendEvent reportSendEvent) {
        log.info("同步发送的相关报告信息:{}", reportSendEvent);
        switch (reportSendEvent.getReportType()) {
            case TEST_ACCESS:
                setAccessReportDateResp(reportSendEvent);
                break;
            case TEST_PERMIT:
                setPermitReportDateResp(reportSendEvent);
                break;
            case SPECIAL_MOBILE:
                break;
            case ONLINE_SMOKE:
                setOnlineSmokeReportDateResp(reportSendEvent);
                break;
            case CHECED_TEST:
                setChecedReportDateResp(reportSendEvent);
                break;
            case SIMPLE_PROCESS:
                setSimpleProcessReportDateResp(reportSendEvent);
                break;
            case CASE_REVIEW:
                break;
            default:
                break;
        }
    }

    private boolean ifAfternoonByDate(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH");
        String format = simpleDateFormat.format(date);
        return Integer.parseInt(format) < 12;
    }

    private double getDelay(Date da1, Date da2) {
        if (null == da1 || null == da2) {
            return 0;
        }
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date da3 = new Date();
        Date da4 = new Date();
        try {
            da3 = format.parse(format.format(da1));
            da4 = format.parse(format.format(da2));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        double delay;
        log.info("进入if---------------+" + da1 + "--------+" + da2);
        int days = Math.abs((int) ((da3.getTime() - da4.getTime()) / (1000 * 3600 * 24)));
        if (da1.getTime() < da2.getTime()) {
            days = -days;
        }
        if (!ifAfternoonByDate(da1) && ifAfternoonByDate(da2)) {
            delay = days + 0.5;
        } else if (ifAfternoonByDate(da1) && !ifAfternoonByDate(da2)) {
            delay = days - 0.5;
        } else {
            delay = days;
        }
        log.info("最后时间------+" + delay);
        return delay;
    }

    private boolean ifAfternoonByTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(date.getTime());
        calendar.set(Calendar.HOUR_OF_DAY, 12);
        return date.before(calendar.getTime());
    }

    private void setAccessReportDateResp(TestReportSendEvent event) {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        AccessReportDateResp resp = new AccessReportDateResp();
        resp.setName(event.getReportName());
        resp.setVersionCode(event.getVersionCode());
        resp.setSmokeTestCase(event.getPlanCaseNum());
        resp.setSmokeTestCasePassed(event.getPermitNum());

        FindTmTestPlanQuery query = new FindTmTestPlanQuery(event.getPlanCode());
        query.setCode(event.getPlanCode());
        query.setTransactor(event.getTransactor());
        log.info("计划名：{}，实际时间：{}，计划时间：{}", event.getPlanName(), event.getActualPresentationDate(), event.getPlanPresentationDate());
        resp.setDelayForTest(getDelay(event.getActualPresentationDate(), event.getPlanPresentationDate()));
        resp.setTestCaseCount(event.getTestCaseNum());
        resp.setCreateTime(Objects.isNull(event.getOccurred()) ? null : format.format(event.getOccurred()));
        //实际提测时间actualPresentationDate
        resp.setActualTestStartDate(Objects.isNull(event.getActualPresentationDate()) ? null : format.format(event.getActualPresentationDate()));
        resp.setPlanTestHalf(ifAfternoonByDate(event.getPlanPresentationDate()) ? 0 : 1);
        resp.setActualTestHalf(Objects.isNull(event.getActualPresentationDate()) ? null : (ifAfternoonByDate(event.getActualPresentationDate()) ? 0 : 1));
        resp.setCreateUser(event.getTransactor().getUserName());
        resp.setType(2);

        if (CollectionUtils.isNotEmpty(event.getCcUsers())) {
            List<String> ccUsersEmail = event.getCcUsers().stream().map(SendUserInfoVO::getEmail).collect(Collectors.toList());
            resp.setCcUsersEmail(ccUsersEmail);
        }

        if (CollectionUtils.isNotEmpty(event.getReceiveUsers())) {
            List<String> recipientsEmail = event.getReceiveUsers().stream().map(SendUserInfoVO::getEmail).collect(Collectors.toList());
            resp.setRecipientsEmail(recipientsEmail);
        }
        log.info("AccessReportDateResp >>> {}", resp);
        producerService.sendBasic(BasicTagEnum.ACCESS_REPORT.getTag(), event.getOperation().getActionEnum(), event.getReportCode(), resp);
    }

    private void setPermitReportDateResp(TestReportSendEvent event) {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        PermitReportDateResp resp = new PermitReportDateResp();
        resp.setName(event.getReportName());
        resp.setVersionCode(event.getVersionCode());
        resp.setTestResult((Objects.isNull(event.getTestResult()) ? null : event.getTestResult().getValue()));
        resp.setCreateTime(Objects.isNull(event.getOccurred()) ? null : format.format(event.getOccurred()));
        resp.setActualTestEndDate(Objects.isNull(event.getActualApprovalExitDate()) ? null : format.format(event.getActualApprovalExitDate()));
        resp.setActualAccessHalf(Objects.isNull(event.getActualApprovalExitDate()) ? null : (ifAfternoonByDate(event.getActualApprovalExitDate()) ? 0 : 1));
        resp.setCreateUser(event.getTransactor().getUserName());
        resp.setType(3);

        if (CollectionUtils.isNotEmpty(event.getCcUsers())) {
            List<String> ccUsersEmail = event.getCcUsers().stream().map(SendUserInfoVO::getEmail).collect(Collectors.toList());
            resp.setCcUsersEmail(ccUsersEmail);
        }

        if (CollectionUtils.isNotEmpty(event.getReceiveUsers())) {
            List<String> recipientsEmail = event.getReceiveUsers().stream().map(SendUserInfoVO::getEmail).collect(Collectors.toList());
            resp.setRecipientsEmail(recipientsEmail);
        }
        log.info("PermitReportDateResp >>> {}", resp);
        producerService.sendBasic(BasicTagEnum.PERMIT_REPORT.getTag(), event.getOperation().getActionEnum(), event.getReportCode(), resp);
    }

    private void setOnlineSmokeReportDateResp(TestReportSendEvent event) {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        OnlineSmokeReportDateResp resp = new OnlineSmokeReportDateResp();
        resp.setVersionCode(event.getVersionCode());
        resp.setName(event.getReportName());
        resp.setCheckResultDate(Objects.isNull(event.getUpdateTestResultDate()) ? null : format.format(event.getUpdateTestResultDate()));
        resp.setActualVersionEndDate(Objects.isNull(event.getActualOnlineDate()) ? null : format.format(event.getActualOnlineDate()));
        resp.setTestResult((Objects.isNull(event.getTestResult()) ? null : event.getTestResult().getValue()));
        resp.setCreateUser(event.getTransactor().getUserName());
        resp.setCreateTime(Objects.isNull(event.getOccurred()) ? null : format.format(event.getOccurred()));
        if (null != event.getDelay() && event.getDelay() == 1) {
            resp.setDelayFlag(event.getDelay());
        } else {
            resp.setDelayFlag(0);
        }
        if (null != event.getAsPlanedOnline() && event.getAsPlanedOnline() == 1) {
            resp.setPlanFlag(event.getAsPlanedOnline());
        } else {
            resp.setPlanFlag(0);
        }
        resp.setType(4);

        if (CollectionUtils.isNotEmpty(event.getCcUsers())) {
            List<String> ccUsersEmail = event.getCcUsers().stream().map(SendUserInfoVO::getEmail).collect(Collectors.toList());
            resp.setCcUsersEmail(ccUsersEmail);
        }

        if (CollectionUtils.isNotEmpty(event.getReceiveUsers())) {
            List<String> recipientsEmail = event.getReceiveUsers().stream().map(SendUserInfoVO::getEmail).collect(Collectors.toList());
            resp.setRecipientsEmail(recipientsEmail);
        }
        log.info("OnlineSmokeReportDateResp >>> {}", resp);
        producerService.sendBasic(BasicTagEnum.ONLINE_SMOKE_REPORT.getTag(), event.getOperation().getActionEnum(), event.getReportCode(), resp);
    }

    private void setChecedReportDateResp(TestReportSendEvent event) {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        CheckedReportDateResp resp = new CheckedReportDateResp();
        resp.setName(event.getReportName());
        resp.setVersionCode(event.getVersionCode());
        resp.setActualTestStartDate(Objects.isNull(event.getCheckStartDate()) ? null : format.format(event.getCheckStartDate()));
        resp.setActualTestEndDate(Objects.isNull(event.getCheckEndDate()) ? null : format.format(event.getCheckEndDate()));
        resp.setActualVersionEndDate(Objects.isNull(event.getActualOnlineDate()) ? null : format.format(event.getActualOnlineDate()));
        resp.setPrdLeader(tmTestReportQueryDomainService.getPrdLeader(event.getVersionCode()));
        resp.setTestResult((Objects.isNull(event.getTestResult()) ? null : event.getTestResult().getValue()));
        if (null != event.getDelay() && event.getDelay() == 1) {
            resp.setDelayFlag(event.getDelay());
        } else {
            resp.setDelayFlag(0);
        }
        resp.setSourceType(Objects.isNull(event.getCheckType()) ? "UNKNOWN" : event.getCheckType().getValue());
        resp.setCreateUser(event.getTransactor().getUserName());
        resp.setCreateTime(format.format(new Date()));
        resp.setType(6);

        if (CollectionUtils.isNotEmpty(event.getCcUsers())) {
            List<String> ccUsersEmail = event.getCcUsers().stream().map(SendUserInfoVO::getEmail).collect(Collectors.toList());
            resp.setCcUsersEmail(ccUsersEmail);
        }

        if (CollectionUtils.isNotEmpty(event.getReceiveUsers())) {
            List<String> recipientsEmail = event.getReceiveUsers().stream().map(SendUserInfoVO::getEmail).collect(Collectors.toList());
            resp.setRecipientsEmail(recipientsEmail);
        }
        log.info("CheckedReportDateResp >>> {}", resp);
        producerService.sendBasic(BasicTagEnum.EXTERNAL_TEST_REPORT.getTag(), event.getOperation().getActionEnum(), event.getReportCode(), resp);
    }

    private void setSimpleProcessReportDateResp(TestReportSendEvent event) {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleProcessReportDateResp resp = new SimpleProcessReportDateResp();
        resp.setName(event.getReportName());
        resp.setVersionCode(event.getVersionCode());
        resp.setActualTestStartDate(Objects.isNull(event.getActualPresentationDate()) ? null : format.format(event.getActualPresentationDate()));
        resp.setActualTestEndDate(Objects.isNull(event.getActualApprovalExitDate()) ? null : format.format(event.getActualApprovalExitDate()));
        resp.setActualVersionEndDate(Objects.isNull(event.getActualOnlineDate()) ? null : format.format(event.getActualOnlineDate()));
        resp.setPrdLeader(tmTestReportQueryDomainService.getPrdLeader(event.getVersionCode()));
        resp.setTestResult((Objects.isNull(event.getTestResult()) ? null : event.getTestResult().getValue()));
        if (null != event.getDelay() && event.getDelay() == 1) {
            resp.setDelayFlag(event.getDelay());
        } else {
            resp.setDelayFlag(0);
        }
        if (null != event.getAsPlanedOnline() && event.getAsPlanedOnline() == 1) {
            resp.setPlanFlag(event.getAsPlanedOnline());
        } else {
            resp.setPlanFlag(0);
        }
        resp.setPlanTestStartDate(Objects.isNull(event.getPlanPresentationDate()) ? null : format.format(event.getPlanPresentationDate()));
        resp.setPlanTestHalf(Objects.isNull(event.getPlanPresentationDate()) ? null : (ifAfternoonByTime(event.getPlanPresentationDate()) ? 0 : 1));
        resp.setActualTestHalf(Objects.isNull(event.getActualPresentationDate()) ? null : (ifAfternoonByTime(event.getActualPresentationDate()) ? 0 : 1));
        resp.setPlanAccessHalf(Objects.isNull(event.getPlanApprovalExitDate()) ? null : (ifAfternoonByTime(event.getPlanApprovalExitDate()) ? 0 : 1));
        resp.setActualAccessHalf(Objects.isNull(event.getActualApprovalExitDate()) ? null : (ifAfternoonByTime(event.getActualApprovalExitDate()) ? 0 : 1));
        resp.setSmokeTestCase(event.getPlanCaseNum());
        resp.setSmokeTestCasePassed(event.getPermitNum());
        resp.setDeveloperNum(event.getDeveloperCount());
        resp.setTesterNum(event.getTesterCount());
        resp.setCreateUser(event.getTransactor().getUserName());
        resp.setCreateTime(Objects.isNull(event.getOccurred()) ? format.format(new Date()) : format.format(event.getOccurred()));
        resp.setType(5);

        if (CollectionUtils.isNotEmpty(event.getCcUsers())) {
            List<String> ccUsersEmail = event.getCcUsers().stream().map(SendUserInfoVO::getEmail).collect(Collectors.toList());
            resp.setCcUsersEmail(ccUsersEmail);
        }

        if (CollectionUtils.isNotEmpty(event.getReceiveUsers())) {
            List<String> recipientsEmail = event.getReceiveUsers().stream().map(SendUserInfoVO::getEmail).collect(Collectors.toList());
            resp.setRecipientsEmail(recipientsEmail);
        }
        log.info("SimpleProcessReportDateResp >>> {}", resp);
        producerService.sendBasic(BasicTagEnum.SIMPLE_REPORT.getTag(), event.getOperation().getActionEnum(), event.getReportCode(), resp);
    }

}
