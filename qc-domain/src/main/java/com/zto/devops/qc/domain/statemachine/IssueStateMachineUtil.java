package com.zto.devops.qc.domain.statemachine;

import com.alibaba.cola.statemachine.StateMachine;
import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.framework.client.simple.Field;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.fsm.Event;
import com.zto.devops.framework.common.fsm.State;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.issue.IssueEditFieldEnum;
import com.zto.devops.qc.client.enums.issue.IssueEvent;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.rpc.ProductRoleEnum;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 缺陷状态机工具类
 *
 * <AUTHOR>
 */
@Component
public class IssueStateMachineUtil implements ApplicationContextAware {

    private static ApplicationContext context = null;

    private static StateMachine<State, Event, IssueContext> INSTANCE;

    @Autowired
    private IProductRpcService productRpcService;

    /**
     *获取所有权限按钮
     */
    public void buildButtons(List<IssueVO> issueVos, User user) {
        if (user == null) {
            return;
        }
       issueVos.forEach(vo->vo.setButtons(getAllButtons(vo, user)));
    }

    /**
     * 设置可编辑字段
     * @param issueVOS
     * @param user
     */
    public void buildFields(List<IssueVO> issueVOS, User user, boolean isSuperUser) {
        if (user == null || CollectionUtil.isEmpty(issueVOS)) {
            return;
        }
        //TODO::临时取个产品编码
        List<ProductRoleEnum> productRoles = productRpcService.getProductRoleForIssue(issueVOS.get(0).getProductCode(), user.getUserId());
        issueVOS.forEach(vo -> {
            Set<Field> fields = new HashSet<>();
            State state = IssueStateMachineUtil.getInstance().getState(vo.getStatus().toState());
            Object findEditableFields;
            if (Objects.equals(user.getUserId(), vo.getFindUserId()) || isSuperUser) {
                findEditableFields = state.getMetaMap().get(IssueStateMachine.FIND_EDITABLE_FIELDS);
                ((Collection<? extends IssueEditFieldEnum>) findEditableFields).forEach(e -> fields.add(new Field(e.getValue(), e.getFieldName())));
            }
            if (Objects.equals(user.getUserId(), vo.getDevelopUserId()) || IssueStateMachine.isDeveloper(productRoles) || isSuperUser) {
                findEditableFields = state.getMetaMap().get(IssueStateMachine.DEVELOP_EDITABLE_FIELDS);
                ((Collection<? extends IssueEditFieldEnum>) findEditableFields).forEach(e -> fields.add(new Field(e.getValue(), e.getFieldName())));
                vo.setTransitToDevelop(true);
            }
            if (Objects.equals(user.getUserId(), vo.getTestUserId()) || IssueStateMachine.isTester(productRoles) || isSuperUser) {
                findEditableFields = state.getMetaMap().get(IssueStateMachine.TEST_EDITABLE_FIELDS);
                ((Collection<? extends IssueEditFieldEnum>) findEditableFields).forEach(e -> fields.add(new Field(e.getValue(), e.getFieldName())));
                vo.setTransitToTest(true);
            }
            if (user.getPermissions() != null && user.getPermissions().contains(IssueStateMachine.GLOBAL_PERM_CAN_MODIFY_ISSUE) || isSuperUser) {
                findEditableFields = state.getMetaMap().get(IssueStateMachine.ADMIN_EDIT_FIELDS);
                ((Collection<? extends IssueEditFieldEnum>) findEditableFields).forEach(e -> fields.add(new Field(e.getValue(), e.getFieldName())));
                vo.setTransitToTest(true);
            }
            vo.setFields(new ArrayList<>(fields));
            vo.setEditableFieldNames(fields.stream().map(Field::getCode).collect(Collectors.toSet()));
        });
    }

    // 获取指定按钮权限
    public void buildFilteredButtons(List<IssueVO> issueVOS, User user) {
        if (user == null) {
            return;
        }
        if (CollectionUtil.isEmpty(issueVOS)) {
            return;
        }
        issueVOS.forEach(vo -> vo.setButtons(getAllButtons(vo, user).stream().filter(b -> "FIX".equals(b.getCode()) || "DELAY".equals(b.getCode()) || "DELIVER".equals(b.getCode())
                || "TEST_PASS_CLOSE".equals(b.getCode()) || "RETURN".equals(b.getCode()) || "CONFIRM_CLOSE".equals(b.getCode())).collect(Collectors.toList())));
    }

    private List<Button> getAllButtons(IssueVO vo, User user) {
        if (user == null) {
            return Collections.emptyList();
        }
        List<Event> events = getInstance()
                .readyEvent(IssueStatus.getByName(vo.getStatus().name()).toState(), IssueContext.convert(vo, user.getUserId(), new ArrayList<>(user.getPermissions())));
        events.removeIf(event -> (event.getCode().equals(IssueEvent.START.toEvent().getCode()) && event.getName().equals(IssueEvent.START.toEvent().getName())
                || event.getCode().equals(IssueEvent.END.toEvent().getCode()) && event.getName().equals(IssueEvent.END.toEvent().getName())));
        if (CollectionUtil.isEmpty(events)) {
            return Collections.emptyList();
        }
        return events.stream().map(e -> {
            Button btn = new Button();
            btn.setCode(e.getCode());
            btn.setName(e.getName());
            return btn;
        }).collect(Collectors.toList());
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    public static StateMachine<State, Event, IssueContext> getInstance() {
        if (INSTANCE == null) {
            INSTANCE = (StateMachine<State, Event, IssueContext>) context.getBean("stateMachine");
        }
        return INSTANCE;
    }
}
