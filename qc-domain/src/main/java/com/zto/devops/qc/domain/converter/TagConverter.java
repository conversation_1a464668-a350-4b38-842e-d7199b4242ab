package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.issue.command.AddTagCommand;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.DeleteTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.RemoveTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagDeleteEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagRemovedEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface TagConverter {

    TagVO convert(AddTagCommand command);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseTagDeleteEvent converter(DeleteTestcaseTagCommand command);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseTagRemovedEvent converter(RemoveTestcaseTagCommand command);
}
