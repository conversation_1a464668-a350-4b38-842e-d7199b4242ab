package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.constants.RelevantUserHandleRoleEnum;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneTaskStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.model.dto.RelevantUserEntityDO;
import com.zto.devops.qc.client.model.dto.TmDebugRecordEntityDO;
import com.zto.devops.qc.client.model.dto.TmSceneDebugRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.SaveUserVariableCommand;
import com.zto.devops.qc.client.service.testmanager.apitest.model.SceneTaskResultResp;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.repository.IRelevantUserRepository;
import com.zto.devops.qc.domain.gateway.repository.TmDebugRecordRepository;
import com.zto.devops.qc.domain.gateway.repository.TmSceneDebugRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;


@Slf4j
@Component
public class SceneRecordQueryService {

    @Autowired
    private TmDebugRecordRepository tmDebugRecordRepository;

    @Autowired
    private TmSceneDebugRecordRepository tmSceneDebugRecordRepository;

    @Autowired
    private ZtoOssService ztoOssService;

    @Autowired
    private IRelevantUserRepository iRelevantUserRepository;

    public SceneTaskResultResp querySceneTaskResult(String taskId) {
        TmDebugRecordEntityDO record = tmDebugRecordRepository.queryDebugRecordByTaskId(taskId);
        log.info("查询造数结果, TmDebugRecordEntityDO:{}", JSON.toJSONString(record));
        if(null == record || null == record.getStatus() || AutomaticStatusEnum.IN_PROGRESS.equals(record.getStatus())) {
            return new SceneTaskResultResp(SceneTaskStatusEnum.WAIT);
        }
        if(!AutomaticStatusEnum.SUCCESS.equals(record.getStatus())) {
            return new SceneTaskResultResp(SceneTaskStatusEnum.FAILED);
        }
        TmSceneDebugRecordEntityDO debugRecord = tmSceneDebugRecordRepository.selectOneByTaskId(taskId);
        log.info("查询造数结果, TmSceneDebugRecordEntityDO:{}", JSON.toJSONString(debugRecord));
        if(null == debugRecord) {
            return new SceneTaskResultResp(SceneTaskStatusEnum.FAILED);
        }
        if(StringUtils.isBlank(debugRecord.getLogOssPath())) {
            return new SceneTaskResultResp(SceneTaskStatusEnum.WAIT);
        }
        String content = "";
        try{
            content = ztoOssService.getObjectTextWithRange(debugRecord.getBucketName(), debugRecord.getLogOssPath());
        } catch (Exception e) {
            log.error("查询造数结果, 获取oss结果文件失败！{}", e.getMessage());
            return new SceneTaskResultResp(SceneTaskStatusEnum.EXCEPTION, e.getMessage());
        }
        // 成功返回output
        return new SceneTaskResultResp(SceneTaskStatusEnum.SUCCESS, content);
    }

    public void saveUserVariable(SaveUserVariableCommand command, User operator) {
        if(null == operator) {
            throw new ServiceException("当前操作人不能为空!");
        }
        RelevantUserEntityDO existDO = iRelevantUserRepository.findRelevantUser(RelevantUserTypeEnum.CREATOR,
                command.getProductCode(), String.valueOf(operator.getUserId()));
        if(null != existDO) {
            iRelevantUserRepository.updateAccessTagByCode(existDO.getCode(), command.getVariableCode());
            return;
        }
        RelevantUserEntityDO entityDO = new RelevantUserEntityDO();
        entityDO.setCode(command.getAggregateId());
        entityDO.setBusinessCode(command.getProductCode());
        entityDO.setDomain(DomainEnum.SCENE);
        entityDO.setType(RelevantUserTypeEnum.CREATOR);
        entityDO.setUserId(String.valueOf(operator.getUserId()));
        entityDO.setUserName(operator.getUserName());
        entityDO.setHandleType(RelevantUserHandleRoleEnum.USER.name());
        entityDO.setAction(DomainEnum.SCENE.name());
        entityDO.setAccessTag(command.getVariableCode());
        entityDO.setEnable(true);
        entityDO.setCreator(operator.getUserName());
        entityDO.setCreatorId(operator.getUserId());
        entityDO.setGmtCreate(new Date());
        iRelevantUserRepository.save(entityDO);
    }

    public String queryUserVariable(String productCode, Long userId) {
        if(null == userId) {
            throw new ServiceException("当前操作人不能为空!");
        }
        RelevantUserEntityDO existDO = iRelevantUserRepository.findRelevantUser(
                RelevantUserTypeEnum.CREATOR, productCode, String.valueOf(userId));
        if(null != existDO) {
            return existDO.getAccessTag();
        }
        return StringUtils.EMPTY;
    }

}
