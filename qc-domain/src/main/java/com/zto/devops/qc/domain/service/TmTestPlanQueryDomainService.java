package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.zto.devops.framework.client.dto.Page;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.rpc.*;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.*;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.issue.entity.HolidayVO;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.query.FindHolidayQuery;
import com.zto.devops.qc.client.model.report.entity.PlanCaseButtonVO;
import com.zto.devops.qc.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.qc.client.model.rpc.product.query.ListProductMemberByPIdQuery;
import com.zto.devops.qc.client.model.rpc.project.*;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.FindSortedPlanCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageTestcaseQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.TestPlanToSendMailQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.*;
import com.zto.devops.qc.client.model.testmanager.plan.query.*;
import com.zto.devops.qc.client.service.plan.model.VerifyTestPassConditionResp;
import com.zto.devops.qc.domain.converter.TmTestPlanDomainConverter;
import com.zto.devops.qc.domain.gateway.repository.*;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class TmTestPlanQueryDomainService extends BaseDomainService {

    private static final String NO_GROUP_PLAN_CASE_MODULE = "no_group_plan_case_module";
    private static final String ALL_PLAN_CASE_MODULE = "all_plan_case_module";

    @Autowired
    private IProjectRpcService iProjectRpcService;
    @Autowired
    private IProductRpcService iProductRpcService;

    @Autowired
    private TestcaseRepository testcaseRepository;
    @Autowired
    private IEmailRepository emailRepository;
    @Autowired
    private ITmTestReportRepository tmTestReportRepository;
    @Autowired
    private IEmailRepository iEmailRepository;
    @Autowired
    private ITmTestPlanRepository tmTestPlanRepository;
    @Autowired
    private TmTestPlanRangeRepository tmTestPlanRangeRepository;
    @Autowired
    private TestFunctionPointRepository testFunctionPointRepository;
    @Autowired
    private ITmTestPlanCaseRepository tmTestPlanCaseRepository;
    @Autowired
    private TmTestPlanIssueRepository planIssueRepository;
    @Autowired
    private ITestCaseSortRepository testCaseSortRepository;

    @Autowired
    private TmTestPlanDomainConverter tmTestPlanDomainConverter;

    public TmTestPlanVO testPlanDetail(FindTmTestPlanQuery query) {
        TmTestPlanVO vo;
        if (StringUtil.isEmpty(query.getCode())) {
            throw new ServiceException("参数异常");
        }
        if (query.getTransactor() == null) {
            throw new ServiceException("查询参数缺少当前登录人信息");
        }
        TmTestPlanEntityDO entityDO = tmTestPlanRepository.getTestPlanByCode(query.getCode());
        if (entityDO == null) {
            throw new ServiceException("测试计划不存在:" + query.getCode());
        }
        vo = tmTestPlanDomainConverter.convertDO2VO(entityDO);
        vo.setPlanDirectorNameReal(entityDO.getPlanDirectorName());
        if (vo.getPlanDirectorId() == null) {
            vo.setPlanDirectorId(query.getTransactor().getUserId());
            vo.setPlanDirectorName(query.getTransactor().getUserName());
        }
        buildVersionInfo(vo);
        buildProductDirector(vo);
        buildTestPlanRange(vo);
        buildFunctionPoint(vo);
        buildFields(vo);
        buildRelationPlan(vo);
        buildPlanReport(vo);
        buildCaseReviewButtonVOS(vo);
        if (!TestPlanNewTypeEnum.TEST_PLAN.equals(vo.getType())) {
            return vo;
        }
        if (TestPlanStrategyEnum.ALL_TEST.equals(vo.getTestStrategy()) || TestPlanStrategyEnum.ALLOW_EXIT_TEST.equals(vo.getTestStrategy())) {
            int diff = daysDiff(vo.getAccessDate(), vo.getPermitDate(), 0);
            vo.setIsSimpleTest(diff <= 2);
        }
        return vo;
    }

    private void buildVersionInfo(TmTestPlanVO vo) {
        try {
            SimpleVersionListQuery listQuery = new SimpleVersionListQuery();
            listQuery.setCode(Collections.singletonList(vo.getVersionCode()));
            SimpleListVersionVO simpleListVersionVO = iProjectRpcService.simpleVersionListQuery(listQuery);
            List<SimpleVersionVO> simpleVersionVOList = simpleListVersionVO.getSimpleVersionVOList();
            SimpleVersionVO versionVO = simpleVersionVOList.get(0);
            vo.setProductCode(versionVO.getProductCode());
            vo.setProductName(versionVO.getProductName());
            vo.setDeptId(versionVO.getDeptId());
            vo.setDeptName(versionVO.getDeptName());
            vo.setVersionName(versionVO.getName());
            vo.setVersionCode(versionVO.getCode());
            vo.setAccessDate(versionVO.getPresentationDate());
            vo.setPermitDate(versionVO.getApprovalExitDate());
            vo.setPublishDate(versionVO.getPublishDate());
            vo.setStartDate(versionVO.getStartDate());
            if (TestPlanNewTypeEnum.TEST_PLAN.equals(vo.getType())) {
                vo.setTestStrategy(TestPlanStrategyEnum.getEnumByName(versionVO.getTestStrategy().name()));
            }
        } catch (Exception e) {
            throw new ServiceException("执行项目域SimpleVersionQuery异常:" + e.getMessage());
        }
    }

    private void buildProductDirector(TmTestPlanVO vo) {
        List<ProductMemberVO> list = iProductRpcService.findProductMemberByIdQuery(vo.getProductCode(),
                Collections.singletonList(MemberTypeEnum.PRODUCTER_OWNER.name()));
        if (CollectionUtil.isEmpty(list)) {
            log.warn("版本产品负责人不存在:" + vo.getProductCode());
            return;
        }
        vo.setProductDirectorId(list.get(0).getUserId());
        vo.setProductDirectorName(list.get(0).getUserName());
    }

    private void buildTestPlanRange(TmTestPlanVO vo) {
        List<TmTestPlanRangeEntityDO> rangeEntityDOList = tmTestPlanRangeRepository.selectByPlanCode(vo.getCode(), Boolean.TRUE);
        rangeEntityDOList.forEach(t -> {
            if (TestPlanRangeTypeEnum.MOBILE_TEST.equals(t.getTestRange())) {
                vo.setMobileSpecialTest(t.getTestRangeStatus());
            }
            if (TestPlanRangeTypeEnum.SAFETY_SCANNING.equals(t.getTestRange())) {
                vo.setSecurityScan(t.getTestRangeStatus());
            }
            if (TestPlanRangeTypeEnum.STATIC_ANALYSIS.equals(t.getTestRange())) {
                vo.setStaticAnalysis(t.getTestRangeStatus());
                vo.setStaticAnalysisDirectorId(t.getExecutorId());
                vo.setStaticAnalysisDirectorName(t.getExecutor());
                vo.setStaticAnalysisTime(t.getTestTime());
            }
            if (TestPlanRangeTypeEnum.PERFORMANCE_TEST.equals(t.getTestRange())) {
                vo.setPerformanceTest(t.getTestRangeStatus());
                vo.setPerformanceTestDirectorId(t.getExecutorId());
                vo.setPerformanceTestDirectorName(t.getExecutor());
                vo.setPerformanceTestTime(t.getTestTime());
            }
            if (TestPlanRangeTypeEnum.EXPLORATORY_TEST.equals(t.getTestRange())) {
                vo.setExploratoryTest(t.getTestRangeStatus());
                vo.setExploratoryTestDirectorId(t.getExecutorId());
                vo.setExploratoryTestDirectorName(t.getExecutor());
                vo.setExploratoryTestTime(t.getTestTime());
            }
            if (TestPlanRangeTypeEnum.PERMISSIONS_TEST.equals(t.getTestRange())) {
                vo.setPermissionsTest(t.getTestRangeStatus());
                vo.setPriority(t.getPriority());
                vo.setLastTestDate(t.getTestTime());
                vo.setTestInformation(t.getTestInformation());
                vo.setPermissionsTestInformation(t.getPermissionsTestInformation());
            }
        });
    }

    private void buildFunctionPoint(TmTestPlanVO vo) {
        List<TestFunctionPointEntityDO> entityDOList = testFunctionPointRepository.selectByBusinessCode(vo.getCode());
        vo.setPointList(tmTestPlanDomainConverter.convertPointVOList(entityDOList));
    }

    private void buildFields(TmTestPlanVO vo) {
        List<TmTestPlanEditEnum> fieldVOS = new LinkedList<>();
        for (TmTestPlanEditEnum testPlanEditEnum : TmTestPlanEditEnum.values()) {
            fieldVOS.add(testPlanEditEnum);
        }
        vo.setFieldVOS(fieldVOS);
    }

    private void buildRelationPlan(TmTestPlanVO vo) {
        if (StringUtil.isEmpty(vo.getRelationPlanCode())) {
            return;
        }
        TmTestPlanEntityDO entityDO = tmTestPlanRepository.getTestPlanByCode(vo.getRelationPlanCode());
        if (entityDO == null) {
            return;
        }
        vo.setRelationPlanName(entityDO.getPlanName());
    }

    private void buildPlanReport(TmTestPlanVO vo) {
        List<TmTestPlanReportVO> reportList = new LinkedList<>();
        List<TmTestReportEntityDO> entityDOList = tmTestReportRepository.selectByPlanCode(vo.getCode());
        entityDOList.forEach(t -> {
            reportList.add(new TmTestPlanReportVO(t.getReportCode(), t.getReportName()));
        });
        vo.setReportList(reportList);
    }

    private void buildCaseReviewButtonVOS(TmTestPlanVO vo) {
        if (TestPlanStrategyEnum.SIMPLE_TEST.equals(vo.getTestStrategy())
                || TestPlanStrategyEnum.EXPLORE_TEST.equals(vo.getTestStrategy())
                || TestPlanStrategyEnum.NULL_TEST.equals(vo.getTestStrategy())) {
            return;
        }
        List<Button> buttonVOS = new ArrayList<>();
        List<TmEmailEntityDO> entityDOList = iEmailRepository.selectEnableByRelatePlanCodeAndEmailType(vo.getCode(), EmailTypeEnum.CASE_REVIEW);
        if (entityDOList == null || entityDOList.size() == 0) {
            buttonVOS.add(new Button("发送用例评审报告", "sendCaseReviewReport", TestPlanNewStatusEnum.TERMINATED.equals(vo.getStatus()) ? 0 : 1));
            vo.setCaseReviewButtonVOS(buttonVOS);
            return;
        }
        buttonVOS.add(new Button("查看用例评审报告", "queryCaseReviewReport"));
        vo.setCaseReviewButtonVOS(buttonVOS);
    }

    public void dealPlanOperate(String productCode, Long userId, List<TmTestPlanVO> list) {

        log.info("Start processing plan permission information ~~~ productCode{} ~~~ userId{}", productCode, userId);

        if (StringUtils.isEmpty(productCode) || Objects.isNull(userId) || CollectionUtils.isEmpty(list)) {
            return;
        }
        ListProductMemberByPIdQuery productQuery = new ListProductMemberByPIdQuery();
        productQuery.setProductCode(productCode);
        Boolean productPermission = iProductRpcService.checkProductPermission(userId, productQuery);

        // 产品的测试负责人、产品的测试人员、超级管理员
        List<String> memberTypes = Arrays.asList(
                MemberTypeEnum.TESTER_OWNER.name(),
                MemberTypeEnum.TESTER_M.name(),
                MemberTypeEnum.TESTER.name(),
                MemberTypeEnum.SUPPER.name());
        productQuery.setMemberTypes(memberTypes);
        Boolean OperatePermission = iProductRpcService.checkProductPermission(userId, productQuery);
        try {
            list.parallelStream().forEach(
                    t -> getOperation(t, productPermission, OperatePermission, userId)
            );
        } catch (Exception e) {
            log.error("getOperation is error{}  userId {}", list, userId);
        }
    }

    private void getOperation(TmTestPlanVO tmTestPlanVO, Boolean productPermission, Boolean OperatePermission, Long userId) {
        LinkedHashSet<PlanEventFieldEnum> planEventFieldLists = new LinkedHashSet<>();
        boolean isSendMail = sendMailRecord(tmTestPlanVO.getCode(), tmTestPlanVO.getType().name());
        if (tmTestPlanVO.getStatus().name().equals(TestPlanNewStatusEnum.NOT_STARTED.name()) ||
                tmTestPlanVO.getStatus().name().equals(TestPlanNewStatusEnum.IN_PROGRESS.name())) {

            if (OperatePermission || tmTestPlanVO.getPlanDirectorId().equals(userId)) {
                planEventFieldLists.add(PlanEventFieldEnum.PlanEditedEvent);
                planEventFieldLists.add(PlanEventFieldEnum.PlanStopEvent);
                if (isSendMail) {
                    planEventFieldLists.add(PlanEventFieldEnum.ViewMailEvent);
                } else {
                    planEventFieldLists.add(PlanEventFieldEnum.SendMailEvent);
                }
            }
            //产品中的全部人员
            if (productPermission) {
                if (isSendMail) {
                    planEventFieldLists.add(PlanEventFieldEnum.ViewMailEvent);
                }
            }
        } else if (tmTestPlanVO.getStatus().name().equals(TestPlanNewStatusEnum.COMPLETED.name())) {
            //已完成只能查看
            if (productPermission && isSendMail) {
                planEventFieldLists.add(PlanEventFieldEnum.ViewMailEvent);
            }
        } else if (tmTestPlanVO.getStatus().name().equals(TestPlanNewStatusEnum.TERMINATED.name())) {
            if (OperatePermission) {
                planEventFieldLists.add(PlanEventFieldEnum.PlanRestartEvent);
            }
            if (productPermission && isSendMail) {
                planEventFieldLists.add(PlanEventFieldEnum.ViewMailEvent);
            }
        }
        log.info("Finished processing the  plan permission information ~~~ planEventFieldLists{}", planEventFieldLists);
        tmTestPlanVO.setButtonVOS(tmTestPlanDomainConverter.convertButtonVOS(planEventFieldLists));

    }

    //是否发送邮件
    private Boolean sendMailRecord(String code, String type) {
        log.info("Query whether to send mail ~~~ code{} type{}", code, type);
        TestPlanToSendMailQuery query = new TestPlanToSendMailQuery();
        query.setPlanCode(code);
        query.setPlanType(type);
        return emailRepository.testPlanToSendMail(query);
    }

    public TestPlanTipVO findTmTestPlanTipQuery(FindTmTestPlanTipQuery query) {
        TestPlanTipVO tipVO = new TestPlanTipVO();
        if (StringUtil.isEmpty(query.getPlanCode())) {
            return tipVO;
        }
        TmTestPlanEntityDO plan = tmTestPlanRepository.getTestPlanByCode(query.getPlanCode());

        // 安全测试计划
        if (TestPlanNewTypeEnum.SAFETY_TEST.equals(plan.getType())) {
            tipVO.setTip("安全测试计划已与安全平台完成对接，");
            tipVO.setPlanTipTitle(getTipTitle(PlanTipEnum.SAFE_TEST));
            tipVO.setPlanTipEnum(PlanTipEnum.SAFE_TEST);
            return tipVO;
        }
        // 移动专项
        if (TestPlanNewTypeEnum.MOBILE_SPECIAL.equals(plan.getType())) {
            tipVO.setTip("当前计划是移动专项测试计划，");
            tipVO.setPlanTipTitle(getTipTitle(PlanTipEnum.MOBILE_TEST));
            tipVO.setPlanTipEnum(PlanTipEnum.MOBILE_TEST);
            return tipVO;
        }
        SimpleVersionQuery versionQuery = new SimpleVersionQuery();
        versionQuery.setCode(plan.getVersionCode());
        SimpleVersionVO versionVO = iProjectRpcService.simpleVersionQuery(versionQuery);
        if (null == versionVO) {
            return tipVO;
        }
        // 研发自测
        if (TestStrategyEnum.EXPLORE_TEST.equals(versionVO.getTestStrategy())) {
            tipVO.setTip("当前计划测试策略是【研发自测】");
            tipVO.setPlanTipTitle(getTipTitle(PlanTipEnum.SELF_TEST));
            tipVO.setPlanTipEnum(PlanTipEnum.SELF_TEST);
            return tipVO;
        }
        // 验收测试
        if (TestStrategyEnum.ACCEPTANCE_TEST.equals(versionVO.getTestStrategy())) {
            tipVO.setTip("当前计划测试策略是【验收测试】");
            tipVO.setPlanTipTitle(getTipTitle(PlanTipEnum.ACCEPTED_TEST));
            tipVO.setPlanTipEnum(PlanTipEnum.ACCEPTED_TEST);
            return tipVO;
        }
        // 全流程测试、自定义测试
        if (TestStrategyEnum.ALLOW_EXIT_TEST.equals(versionVO.getTestStrategy()) || TestStrategyEnum.ALL_TEST.equals(versionVO.getTestStrategy())) {
            return buildFullAndSelfPlanTip(versionVO, versionVO.getTestStrategy());
        }
        // 标准测试
        if (TestStrategyEnum.STANDARD_TEST.equals(versionVO.getTestStrategy())) {
            tipVO.setTip("当前计划测试策略是【标准测试】");
            tipVO.setPlanTipTitle(getTipTitle(PlanTipEnum.STANDARD_TEST));
            tipVO.setPlanTipEnum(PlanTipEnum.STANDARD_TEST);
            return tipVO;
        }
        // 简易测试
        if (TestStrategyEnum.SIMPLE_TEST.equals(versionVO.getTestStrategy())) {
            tipVO.setTip("当前计划测试策略是【简易测试】");
            tipVO.setPlanTipTitle(getTipTitle(PlanTipEnum.NEW_SIMPLE_TEST));
            tipVO.setPlanTipEnum(PlanTipEnum.NEW_SIMPLE_TEST);
            return tipVO;
        }
        return tipVO;
    }

    private String getTipTitle(PlanTipEnum tipEnum) {
        if (tipEnum == null) {
            return "";
        }
        return "查看" + tipEnum.getValue() + "流程图";
    }

    private TestPlanTipVO buildFullAndSelfPlanTip(SimpleVersionVO versionVO,
                                                  TestStrategyEnum testStrategy) {
        TestPlanTipVO tipVO = new TestPlanTipVO();
        if (null == versionVO.getApprovalExitDate() || null == versionVO.getPresentationDate()) {
            return tipVO;
        }
        int days = daysDiff(versionVO.getPresentationDate(), versionVO.getApprovalExitDate(), 0);
        if (days <= 2) {
            tipVO.setTip(TestStrategyEnum.ALL_TEST.equals(testStrategy)
                    ? "当前计划测试策略是【全流程测试】，且测试周期≤2天，将按【简易测试】流程执行，"
                    : "当前计划测试策略是【自定义测试】，且测试周期≤2天，将按【简易测试】流程执行，");
            tipVO.setPlanTipEnum(PlanTipEnum.SIMPLE_TEST);
            tipVO.setPlanTipTitle(getTipTitle(tipVO.getPlanTipEnum()));
            return tipVO;
        }
        if (TestStrategyEnum.ALL_TEST.equals(testStrategy)) {
            tipVO.setTip("当前计划测试策略是【全流程测试】，且测试周期＞2天，将按【全流程测试】流程执行，");
            tipVO.setPlanTipEnum(PlanTipEnum.FULL_TEST);
            tipVO.setPlanTipTitle(getTipTitle(tipVO.getPlanTipEnum()));
            return tipVO;
        }
        if (TestStrategyEnum.ALLOW_EXIT_TEST.equals(testStrategy)) {
            tipVO.setTip("当前计划测试策略是【自定义测试】，且测试周期＞2天，将按【自定义测试】流程执行");
            tipVO.setPlanTipEnum(PlanTipEnum.CUSTOM_TEST);
            tipVO.setPlanTipTitle(getTipTitle(tipVO.getPlanTipEnum()));
            return tipVO;
        }
        return tipVO;
    }

    private int daysDiff(Date smallDate, Date bigDate, int type) {
        if (smallDate == null || bigDate == null) {
            return 0;
        }
        FindHolidayQuery holidayQuery = new FindHolidayQuery();
        holidayQuery.setStartDate(smallDate);
        holidayQuery.setEndDate(bigDate);
        holidayQuery.setType(Collections.singletonList(type));
        // 获取工作日；
        List<HolidayVO> workDaysList = iProjectRpcService.findHoliday(holidayQuery);
        List<HolidayVO> hasWorkDays = getHolidayVOs(smallDate, bigDate, workDaysList);
        return hasWorkDays.size();
    }

    private List<HolidayVO> getHolidayVOs(Date start, Date end, List<HolidayVO> holidayVOList) {
        if (start == null || end == null || CollectionUtil.isEmpty(holidayVOList)) {
            return Collections.emptyList();
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String startDateStr = formatter.format(start);
        String endDateStr = formatter.format(end);
        Date startDate = null;
        Date endDate = null;
        try {
            startDate = formatter.parse(startDateStr);
            endDate = formatter.parse(endDateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Date finalEndDate = endDate;
        Date finalStartDate = startDate;
        return holidayVOList.stream().filter(h -> h.getHoliday().getTime() <= finalEndDate.getTime() && h.getHoliday().getTime() >= finalStartDate.getTime()).collect(Collectors.toList());
    }

    public RelatedPlanReportVO relatedTest(RelatedPlanReportQuery query) {
        RelatedPlanReportVO relatedVO = new RelatedPlanReportVO();
        if (StringUtil.isNotEmpty(query.getPlanCode())) {
            TmTestPlanEntityDO planEntityDO = tmTestPlanRepository.getTestPlanByCode(query.getPlanCode());
            if (null != planEntityDO) {
                query.setVersionCode(planEntityDO.getVersionCode());
            }
        }
        if (StringUtil.isNotEmpty(query.getReportCode())) {
            TmTestReportEntityDO reportEntityDO = tmTestReportRepository.loadFromDb(query.getReportCode());
            if (null != reportEntityDO) {
                query.setVersionCode(reportEntityDO.getVersionCode());
            }
        }
        if (StringUtil.isBlank(query.getVersionCode())) {
            String businessCode = StringUtil.isBlank(query.getPlanCode())
                    ? (StringUtil.isBlank(query.getReportCode()) ? "" : query.getReportCode())
                    : query.getPlanCode();
            if (StringUtil.isNotEmpty(businessCode)) {
                List<TmEmailEntityDO> emailEntityDOList = emailRepository.selectByBusinessCode(businessCode);
                if (CollectionUtil.isNotEmpty(emailEntityDOList)) {
                    query.setVersionCode(emailEntityDOList.get(0).getVersionCode());
                }
            }
        }
        List<RelatedPlanVO> testPlan = getRelatedPlanList(query);
        if (CollectionUtil.isNotEmpty(testPlan)) {
            testPlan.forEach(plan -> plan.setPlanTypeDesc(EmailTypeEnum.getValueByName(plan.getPlanType())));
        }
        relatedVO.setTestPlan(testPlan);
        List<RelatedReportVO> testReport = getRelatedReportList(query);
        if (CollectionUtil.isNotEmpty(testReport)) {
            testReport.forEach(report -> report.setReportTypeDesc(EmailTypeEnum.getValueByName(report.getReportType())));
        }
        relatedVO.setTestReport(testReport);
        return relatedVO;
    }

    private List<RelatedPlanVO> getRelatedPlanList(RelatedPlanReportQuery query) {
        List<TmEmailEntityDO> emailEntityDOList = emailRepository.getRelatedPlanList(query.getVersionCode(), query.getPlanType());
        if (CollectionUtil.isNotEmpty(emailEntityDOList)) {
            return tmTestPlanDomainConverter.convertPlanVOList(emailEntityDOList);
        }
        return new ArrayList<>();
    }

    private List<RelatedReportVO> getRelatedReportList(RelatedPlanReportQuery query) {
        List<TmEmailEntityDO> emailEntityDOList = emailRepository.getRelatedReportList(query.getVersionCode(), query.getReportType());
        if (CollectionUtil.isNotEmpty(emailEntityDOList)) {
            return tmTestPlanDomainConverter.convertReportVOList(emailEntityDOList);
        }
        return new ArrayList<>();
    }

    public PageCaseIssueVO getPlanIssue(PagePlanIssueQuery query) {
        TmPlanIssueEntityDO issueEntityDO = tmTestPlanDomainConverter.convert(query);
        TmTestPlanEntityDO planEntityDO = tmTestPlanRepository.getTestPlanByCode(query.getPlanCode());
        issueEntityDO.setVersionCode(planEntityDO.getVersionCode());
        com.github.pagehelper.Page<Object> page = PageHelper.startPage(query.getPage(),
                query.getSize());
        List<TmPlanCaseIssueVO> issueVOList = planIssueRepository.selectPlanIssue(issueEntityDO);
        setExternalBatchNameHandle(issueVOList);
        PageCaseIssueVO caseIssueVO = new PageCaseIssueVO();
        caseIssueVO.setList(issueVOList);
        caseIssueVO.setTotal(page.getTotal());
        return caseIssueVO;
    }


    private void setExternalBatchNameHandle(List<TmPlanCaseIssueVO> issueVOList) {
        Set<String> versionSet = new HashSet<String>();
        List<String> findversionCodes = issueVOList.stream()
                .filter(vo -> StringUtils.isNotBlank(vo.getFindVersionCode()) && !("NotAssociated").equals(vo.getFindVersionCode()))
                .map(IssueVO::getFindVersionCode)
                .distinct()
                .collect(Collectors.toList());

        List<String> fixversionCodes = issueVOList.stream()
                .filter(vo -> StringUtils.isNotBlank(vo.getFixVersionCode()) && !("NotAssociated").equals(vo.getFixVersionCode()))
                .map(IssueVO::getFixVersionCode)
                .distinct()
                .collect(Collectors.toList());
        versionSet.addAll(findversionCodes);
        versionSet.addAll(fixversionCodes);
        Map<String, String> versionInfoMap = buildVersion(versionSet);
        issueVOList.stream().forEach(issueVO -> {
            if (StringUtils.isNotBlank(issueVO.getFixVersionCode()) && !issueVO.getFixVersionCode().equals("NotAssociated")) {
                issueVO.setFixVersionName(versionInfoMap.get(issueVO.getFixVersionCode()));
            }
            if (StringUtils.isNotBlank(issueVO.getFindVersionCode()) && !issueVO.getFindVersionCode().equals("NotAssociated")) {
                issueVO.setFindVersionName(versionInfoMap.get(issueVO.getFindVersionCode()));
            }
        });
    }

    private Map<String, String> buildVersion(Set<String> versionSet) {
        Map<String, String> result = new HashMap<>();
        if (CollectionUtil.isEmpty(versionSet)) {
            return result;
        }
        if (CollectionUtil.isEmpty(versionSet)) {
            return result;
        }
        SimpleVersionListQuery versionQuery = new SimpleVersionListQuery();
        versionQuery.setCode(new ArrayList<>(versionSet));
        SimpleListVersionVO listVersionVO = iProjectRpcService.simpleVersionListQuery(versionQuery);
        if (listVersionVO == null) {
            return result;
        }
        List<SimpleVersionVO> simpleVersionVOList = listVersionVO.getSimpleVersionVOList();
        result = simpleVersionVOList.stream().collect(Collectors.toMap(SimpleVersionVO::getCode, SimpleVersionVO::getName));
        return result;
    }

    public TestPlanCaseResultVO planCaseResultCount(PlanCaseResultCountQuery query) {
        //测试计划阶段状态
        TmTestPlanEntityDO planEntityDO = tmTestPlanRepository.selectEnableByPrimaryKey(query.getPlanCode());
        if (null != planEntityDO && MapUtils.isNotEmpty(planEntityDO.getStageStatus())
                && TestPlanStageStatusEnum.COMPLETED.name().equals(planEntityDO.getStageStatus().get(query.getTestStage()))) {
            query.setQueryDelete(Boolean.TRUE);
        }
        List<TestPlanCaseStatusEnum> statusList = tmTestPlanCaseRepository.findStatusList(query);
        Map<TestPlanCaseStatusEnum, Long> groupBy = statusList.parallelStream()
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()));
        //按状态分组
        TestPlanCaseResultVO resultVO = new TestPlanCaseResultVO();
        resultVO.setTotalCount(statusList.size());
        resultVO.setPassedCount(groupBy.getOrDefault(TestPlanCaseStatusEnum.PASSED, 0L).intValue()
                + groupBy.getOrDefault(TestPlanCaseStatusEnum.SUCCESS, 0L).intValue());
        resultVO.setFailedCount(groupBy.getOrDefault(TestPlanCaseStatusEnum.FAILED, 0L).intValue()
                + groupBy.getOrDefault(TestPlanCaseStatusEnum.UNKNOWN, 0L).intValue());
        resultVO.setBlockCount(groupBy.getOrDefault(TestPlanCaseStatusEnum.BLOCK, 0L).intValue()
                + groupBy.getOrDefault(TestPlanCaseStatusEnum.BROKEN, 0L).intValue());
        resultVO.setSkipCount(groupBy.getOrDefault(TestPlanCaseStatusEnum.SKIP, 0L).intValue()
                + groupBy.getOrDefault(TestPlanCaseStatusEnum.SKIPPED, 0L).intValue());
        resultVO.setInitialCount(groupBy.getOrDefault(TestPlanCaseStatusEnum.INITIAL, 0L).intValue()
                + groupBy.getOrDefault(TestPlanCaseStatusEnum.NOT_STARTED, 0L).intValue()
                + groupBy.getOrDefault(TestPlanCaseStatusEnum.SUBMITTED, 0L).intValue()
                + groupBy.getOrDefault(TestPlanCaseStatusEnum.IN_PROGRESS, 0L).intValue());
        resultVO.setDoneCount(resultVO.getPassedCount() + resultVO.getBlockCount() + resultVO.getFailedCount() + resultVO.getSkipCount());
        if (CollectionUtil.isNotEmpty(statusList)) {
            int passRate =
                    Math.round((resultVO.getPassedCount().floatValue() / resultVO.getTotalCount()) * 100);
            resultVO.setPassRate(passRate + "%");
        } else {
            resultVO.setPassRate("0%");
        }
        return resultVO;
    }

    public PageTmTestPlanVO pageTestPlanList(PlanListQuery query) {
        com.github.pagehelper.Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        List<TmTestPlanVO> planEntityList = tmTestPlanRepository.pageTestPlanList(query);
        buildVersionListInfo(planEntityList);
        return PageTmTestPlanVO.buildSelf(planEntityList, page.getTotal());
    }

    private void buildVersionListInfo(List<TmTestPlanVO> tmTestPlanVOS) {
        if (CollectionUtil.isEmpty(tmTestPlanVOS)) {
            return;
        }
        SimpleVersionListQuery simpleVersionListQuery = new SimpleVersionListQuery();
        simpleVersionListQuery.setCode(tmTestPlanVOS.stream().map(TmTestPlanVO::getVersionCode).distinct().collect(Collectors.toList()));
        SimpleListVersionVO simpleListVersionVO = iProjectRpcService.simpleVersionListQuery(simpleVersionListQuery);
        if (Objects.isNull(simpleListVersionVO) || CollectionUtils.isEmpty(simpleListVersionVO.getSimpleVersionVOList())) {
            return;
        }
        Map<String, SimpleVersionVO> versionEntityMap = simpleListVersionVO.getSimpleVersionVOList()
                .parallelStream().collect(Collectors.toMap(SimpleVersionVO::getCode, Function.identity(), (key1, key2) -> key2));
        tmTestPlanVOS.parallelStream()
                .forEach(vo -> Optional.ofNullable(versionEntityMap.get(vo.getVersionCode())).ifPresent(so -> simpleVersionVOCoverToTmTestPlanVO(so, vo)));
    }

    public void simpleVersionVOCoverToTmTestPlanVO(SimpleVersionVO so, TmTestPlanVO vo) {
        vo.setProductCode(so.getProductCode());
        vo.setProductName(so.getProductName());
        vo.setDeptId(so.getDeptId());
        vo.setDeptName(so.getDeptName());
        vo.setVersionName(so.getName());
        vo.setVersionCode(so.getCode());
        if (TestPlanNewTypeEnum.TEST_PLAN.equals(vo.getType())) {
            vo.setTestStrategy(TestPlanStrategyEnum.getEnumByName(!Objects.isNull(so.getTestStrategy()) ? so.getTestStrategy().name() : "NULL_TEST"));
        }
        vo.setAccessDate(so.getPresentationDate());
        vo.setPermitDate(so.getApprovalExitDate());
        vo.setPublishDate(so.getPublishDate());
        vo.setStartDate(so.getStartDate());
    }

    public Page pageListPlanCase(PageListPlanCaseQuery query) {
        if (query.getPage() <= 0) {
            query.setPage(1);
        }
        if (query.getSize() <= 0) {
            query.setSize(Integer.MAX_VALUE);
        }
        boolean groupByVersion = Boolean.FALSE;
        if (ALL_PLAN_CASE_MODULE.equals(query.getParentCode())) {
            query.setParentCode(null);
            if (CollectionUtil.isNotEmpty(query.getSetCoreList()) && query.getSetCoreList().contains(Boolean.FALSE)) {
                groupByVersion = Boolean.TRUE;
            }
        }
        if (StringUtils.isNotBlank(query.getParentCode()) && query.getParentCode().startsWith(NO_GROUP_PLAN_CASE_MODULE)) {
            if (query.getParentCode().contains("VER")) {
                query.setVersionCode(query.getParentCode().replace(NO_GROUP_PLAN_CASE_MODULE + "___", ""));
            }
            query.setParentCode(StringUtils.EMPTY);
        }
        if (StringUtils.isNotBlank(query.getParentCode())
                && (query.getParentCode().startsWith("VER") || query.getParentCode().equals("NONE_VERSION"))) {
            query.setVersionCode(query.getParentCode());
            query.setParentCode(null);
        }

        Boolean moduleSetCore = null;
        boolean isAuto = false;
        if (CollectionUtils.isNotEmpty(query.getCaseTypeList())) {
            if (query.getCaseTypeList().contains(TestcaseTypeEnum.MANUAL)) {
                if (CollectionUtils.isEmpty(query.getSetCoreList())) {
                    query.setSetCoreList(Collections.singletonList(Boolean.FALSE));
                }
                moduleSetCore = query.getSetCoreList().contains(Boolean.TRUE);
            }
            if (query.getCaseTypeList().contains(TestcaseTypeEnum.AUTO)) {
                isAuto = Boolean.TRUE;
            }
        }

        List<AutomaticNodeTypeEnum> moduleNodeTypeList = null;
        if (CollectionUtils.isNotEmpty(query.getNodeTypeList())) {
            moduleNodeTypeList = query.getNodeTypeList().stream()
                    .filter(n -> TestcaseAttributeEnum.MODULE.equals(n.getAttribute()))
                    .collect(Collectors.toList());
            if (query.getNodeTypeList().contains(AutomaticNodeTypeEnum.OTHER)) {
                moduleNodeTypeList.addAll(Arrays.asList(AutomaticNodeTypeEnum.TestPlan, AutomaticNodeTypeEnum.ThreadGroup));
            }
            query.setNodeTypeList(query.getNodeTypeList().stream()
                    .filter(n -> TestcaseAttributeEnum.TESTCASE.equals(n.getAttribute()))
                    .collect(Collectors.toList()));
        }

        if (CollectionUtils.isNotEmpty(query.getStatusList()) && query.getStatusList().contains(TestPlanCaseStatusEnum.PASSED)) {
            query.getStatusList().add(TestPlanCaseStatusEnum.SUCCESS);
        }

        if (StringUtils.isNotEmpty(query.getSearch())) {
            query.setSearch(query.getSearch().replace("\\", "\\\\")
                    .replace("%", "\\%")
                    .replace("_", "\\_"));
        }

        Page<TestPlanCaseVO> page = new Page<>();

        ListPlanCaseQuery listPlanCaseQuery = tmTestPlanDomainConverter.convert(query);
        List<TestPlanCaseVO> planCaseList = tmTestPlanCaseRepository.getTestPlanCaseList(listPlanCaseQuery);
        List<TestPlanCaseVO> deletedCaseList = tmTestPlanCaseRepository.queryDeletedPlanCase(listPlanCaseQuery);

        if (CollectionUtil.isEmpty(planCaseList) && CollectionUtil.isEmpty(deletedCaseList)) {
            return page;
        }

        if (CollectionUtils.isNotEmpty(moduleNodeTypeList)) {
            PageTestcaseQuery pageTestcaseQuery = new PageTestcaseQuery();
            pageTestcaseQuery.setProductCode(query.getProductCode());
            pageTestcaseQuery.setNodeTypeList(moduleNodeTypeList);
            List<String> moduleCodeList = testcaseRepository.selectModuleCodeList(pageTestcaseQuery);
            planCaseList.removeIf(item -> moduleCodeList.stream().noneMatch(s -> item.getCasePath().contains(s)));
        }

        int moduleControllerCount = 0;
        if (isAuto && StringUtils.isNotEmpty(query.getParentCode())) {
            TestcaseVO pEntity = testcaseRepository.findCaseOrModuleByCodeQuery(query.getParentCode());
            if (null != pEntity && StringUtils.isNotEmpty(pEntity.getPath())) {
                List<String> codeList = new ArrayList<>(Arrays.asList(pEntity.getPath().split("\\.")));
                codeList.add(query.getParentCode());
                moduleControllerCount = testcaseRepository.countByNodeTypeAndCodeList(AutomaticNodeTypeEnum.ModuleController, codeList);
            }
        }
        boolean enableChecked = moduleControllerCount == 0;

        //手工用例，按版本分组
        if (!isAuto && groupByVersion && CollectionUtil.isNotEmpty(planCaseList)) {
            return buildVersionModule(query, planCaseList, moduleSetCore, enableChecked);
        }

        List<TestPlanCaseVO> list = new ArrayList<>();

        if (query.getPage() == 1) {
            if (!isAuto && null == query.getParentCode()) {
                int noGroupNum = (int) planCaseList.parallelStream()
                        .filter(item -> StringUtils.EMPTY.equals(item.getParentCode()))
                        .count();
                if (noGroupNum > 0) {
                    TestPlanCaseVO noGroup = new TestPlanCaseVO();
                    noGroup.setCaseName("未分组用例");
                    noGroup.setCaseCode(NO_GROUP_PLAN_CASE_MODULE + "___" + query.getVersionCode());
                    noGroup.setCaseAttribute(TestcaseAttributeEnum.MODULE);
                    noGroup.setEnableChecked(true);
                    noGroup.setChildrenNum(noGroupNum);
                    list.add(noGroup);
                }
            }
            ListPlanCaseModuleQuery listPlanCaseModuleQuery = new ListPlanCaseModuleQuery();
            listPlanCaseModuleQuery.setProductCode(query.getProductCode());
            listPlanCaseModuleQuery.setCaseTypeList(query.getCaseTypeList());
            listPlanCaseModuleQuery.setSetCore(moduleSetCore);
            listPlanCaseModuleQuery.setIsAuto(isAuto);
            List<TestPlanCaseVO> planModuleList = tmTestPlanCaseRepository.selectPlanCaseModuleList(listPlanCaseModuleQuery);
            if (isAuto) {
                List<TestPlanCaseVO> automaticModuleList = tmTestPlanCaseRepository.selectPlanCaseAutomaticModuleList(listPlanCaseModuleQuery);
                planModuleList.addAll(automaticModuleList);
            }
            planModuleList.removeIf(item -> !item.getParentCode().equals(StringUtils.defaultString(query.getParentCode())));

            List<TestPlanCaseVO> finalPlanCaseList = planCaseList;
            planModuleList.forEach(item -> {
                item.setEnableChecked(enableChecked);
                int childrenNum = (int) finalPlanCaseList.parallelStream()
                        .filter(child -> child.getCasePath().contains(item.getCaseCode())
                                || StringUtils.defaultString(child.getTestcaseModulePath()).contains(item.getCaseCode()))
                        .count();
                item.setChildrenNum(childrenNum);
            });
            planModuleList.removeIf(item -> item.getChildrenNum() == 0);
            list.addAll(planModuleList);
        }

        if (null != query.getParentCode()) {
            planCaseList = planCaseList.parallelStream()
                    .filter(item -> {
                        if (item.getParentCode().equals(query.getParentCode())) {
                            return true;
                        }
                        if (StringUtils.isEmpty(item.getParentCode()) &&
                                item.getTestcaseModulePath().endsWith(query.getParentCode())) {
                            return true;
                        }
                        return false;
                    })
                    .collect(Collectors.toList());
            List<List<TestPlanCaseVO>> partition = Lists.partition(planCaseList, query.getSize());
            if (query.getPage() <= partition.size()) {
                List<TestPlanCaseVO> subList = partition.get(query.getPage() - 1);
                subList.forEach(item -> item.setEnableChecked(enableChecked));
                list.addAll(subList);
            }
        }

        page.setList(list);
        page.setTotal(planCaseList.size());
        page.setSize(query.getSize());
        page.setPage(query.getPage());

        if (null == query.getParentCode()) {
            page.getList().addAll(deletedCaseList);
            page.setTotal(page.getTotal() + deletedCaseList.size());
        }

        return page;
    }

    /**
     * 手工用例-按版本分组
     *
     * @param query
     * @param planCaseList
     * @return
     */
    private Page<TestPlanCaseVO> buildVersionModule(PageListPlanCaseQuery query,
                                                    List<TestPlanCaseVO> planCaseList,
                                                    Boolean moduleSetCore,
                                                    boolean enableChecked) {

        Page<TestPlanCaseVO> page = new Page<>();
        List<TestPlanCaseVO> list = new ArrayList<>();
        Map<String, String> versionInfoMap = getVersionNameList(planCaseList.stream().map(TestPlanCaseVO::getVersionCode).collect(Collectors.toSet()));

        Map<String, List<TestPlanCaseVO>> planCaseMap = planCaseList.stream().collect(Collectors.groupingBy(TestPlanCaseVO::getVersionCode));
        if (MapUtils.isEmpty(planCaseMap)) {
            page.setList(list);
            page.setTotal(planCaseList.size());
            page.setSize(query.getSize());
            page.setPage(query.getPage());
            return page;
        }

        planCaseMap.forEach((versionCode, caseList) -> {
            List<TestPlanCaseVO> versionList = new ArrayList<>();
            if (query.getPage() == 1) {
                if (null == query.getParentCode()) {
                    int noGroupNum = (int) caseList.parallelStream()
                            .filter(item -> StringUtils.EMPTY.equals(item.getParentCode()))
                            .count();
                    if (noGroupNum > 0) {
                        TestPlanCaseVO noGroup = new TestPlanCaseVO();
                        noGroup.setCaseName("未分组用例");
                        noGroup.setCaseCode(NO_GROUP_PLAN_CASE_MODULE + "___" + versionCode);
                        noGroup.setCaseAttribute(TestcaseAttributeEnum.MODULE);
                        noGroup.setEnableChecked(true);
                        noGroup.setChildrenNum(noGroupNum);
                        versionList.add(noGroup);
                    }
                }
                ListPlanCaseModuleQuery listPlanCaseModuleQuery = new ListPlanCaseModuleQuery();
                listPlanCaseModuleQuery.setProductCode(query.getProductCode());
                listPlanCaseModuleQuery.setCaseTypeList(query.getCaseTypeList());
                listPlanCaseModuleQuery.setSetCore(moduleSetCore);
                listPlanCaseModuleQuery.setIsAuto(Boolean.FALSE);
                List<TestPlanCaseVO> planModuleList = tmTestPlanCaseRepository.selectPlanCaseModuleList(listPlanCaseModuleQuery);
                planModuleList.removeIf(item -> !item.getParentCode().equals(StringUtils.defaultString(query.getParentCode())));

                List<TestPlanCaseVO> finalPlanCaseList = caseList;
                planModuleList.forEach(item -> {
                    item.setEnableChecked(enableChecked);
                    int childrenNum = (int) finalPlanCaseList.parallelStream()
                            .filter(child -> child.getCasePath().contains(item.getCaseCode())
                                    || StringUtils.defaultString(child.getTestcaseModulePath()).contains(item.getCaseCode()))
                            .count();
                    item.setChildrenNum(childrenNum);
                });
                planModuleList.removeIf(item -> item.getChildrenNum() == 0);
                versionList.addAll(planModuleList);
            }

            if (null != query.getParentCode()) {
                caseList = caseList.parallelStream()
                        .filter(item -> item.getParentCode().equals(query.getParentCode()))
                        .collect(Collectors.toList());
                List<List<TestPlanCaseVO>> partition = Lists.partition(caseList, query.getSize());
                if (query.getPage() <= partition.size()) {
                    List<TestPlanCaseVO> subList = partition.get(query.getPage() - 1);
                    subList.forEach(item -> item.setEnableChecked(enableChecked));
                    versionList.addAll(subList);
                }
            }
            if (CollectionUtil.isNotEmpty(versionList)) {
                TestPlanCaseVO versionGroup = new TestPlanCaseVO();
                String moduleName = versionCode.equals("NONE_VERSION")
                        ? "未关联版本用例"
                        : (MapUtils.isEmpty(versionInfoMap) ? versionCode : versionInfoMap.get(versionCode));
                versionGroup.setCaseName(moduleName);
                versionGroup.setCaseCode(versionCode);
                versionGroup.setCaseAttribute(TestcaseAttributeEnum.MODULE);
                versionGroup.setEnableChecked(true);
                versionGroup.setChildrenNum(versionList.stream().mapToInt(TestPlanCaseVO::getChildrenNum).sum());
                list.add(versionGroup);
            }
        });
        page.setList(list);
        page.setTotal(planCaseList.size());
        page.setSize(query.getSize());
        page.setPage(query.getPage());
        return page;
    }

    public List<String> listTestPlanCaseCode(ListPlanCaseCodeQuery query) {
        if (StringUtils.isNotBlank(query.getParentCode()) && query.getParentCode().startsWith(NO_GROUP_PLAN_CASE_MODULE)) {
            if (query.getParentCode().contains("VER")) {
                query.setVersionCode(query.getParentCode().replace(NO_GROUP_PLAN_CASE_MODULE + "___", ""));
            }
            query.setParentCode(StringUtils.EMPTY);
        }
        if (ALL_PLAN_CASE_MODULE.equals(query.getParentCode())) {
            query.setParentCode(null);
        }
        if (StringUtils.isNotBlank(query.getParentCode())
                && (query.getParentCode().startsWith("VER") || query.getParentCode().equals("NONE_VERSION"))) {
            query.setVersionCode(query.getParentCode());
            query.setParentCode(null);
        }
        if (CollectionUtils.isNotEmpty(query.getCaseTypeList())
                && query.getCaseTypeList().contains(TestcaseTypeEnum.MANUAL)
                && CollectionUtils.isEmpty(query.getSetCoreList())) {
            query.setSetCoreList(Collections.singletonList(Boolean.FALSE));
        }
        List<AutomaticNodeTypeEnum> moduleNodeTypeList = null;
        if (CollectionUtils.isNotEmpty(query.getNodeTypeList())) {
            moduleNodeTypeList = query.getNodeTypeList().stream()
                    .filter(n -> TestcaseAttributeEnum.MODULE.equals(n.getAttribute()))
                    .collect(Collectors.toList());
            if (query.getNodeTypeList().contains(AutomaticNodeTypeEnum.OTHER)) {
                moduleNodeTypeList.addAll(Arrays.asList(AutomaticNodeTypeEnum.TestPlan, AutomaticNodeTypeEnum.ThreadGroup));
            }
            query.setNodeTypeList(query.getNodeTypeList().stream()
                    .filter(n -> TestcaseAttributeEnum.TESTCASE.equals(n.getAttribute()))
                    .collect(Collectors.toList()));
        }
        List<SimpleTestcaseVO> planCaseList = tmTestPlanCaseRepository.selectTestcaseCodeList(query);
        if (CollectionUtils.isNotEmpty(moduleNodeTypeList)) {
            PageTestcaseQuery pageTestcaseQuery = new PageTestcaseQuery();
            pageTestcaseQuery.setProductCode(query.getProductCode());
            pageTestcaseQuery.setNodeTypeList(moduleNodeTypeList);
            List<String> moduleCodeList = testcaseRepository.selectModuleCodeList(pageTestcaseQuery);
            planCaseList.removeIf(item -> moduleCodeList.stream().noneMatch(s -> StringUtils.defaultString(item.getPath()).contains(s)));
        }
        List<String> list = planCaseList.parallelStream().map(SimpleTestcaseVO::getCode).collect(Collectors.toList());
        if (null == query.getParentCode() && (CollectionUtil.contains(query.getSetCoreList(), Boolean.TRUE)
                || CollectionUtil.contains(query.getCaseTypeList(), TestcaseTypeEnum.AUTO))) {
            ListPlanCaseQuery listPlanCaseQuery = tmTestPlanDomainConverter.convert(query);
            List<TestPlanCaseVO> deletedCaseList = tmTestPlanCaseRepository.queryDeletedPlanCase(listPlanCaseQuery);
            deletedCaseList.forEach(vo -> list.add(vo.getCaseCode()));
        }
        return list;
    }

    public PagePlanPhaseVO listPlanPhase(ListPlanPhaseQuery query) {
        com.github.pagehelper.Page<TmTestPlanEntityDO> page = PageHelper.startPage(query.getPage(), query.getSize());
        List<TmTestPlanEntityDO> entityDOList = tmTestPlanRepository.selectByProductCodeAndStatusList(query,
                Arrays.asList(TestPlanNewStatusEnum.TERMINATED, TestPlanNewStatusEnum.COMPLETED));
        List<PlanPhaseVO> phaseVOList = new ArrayList<>();
        PagePlanPhaseVO pagePlanPhaseVO = new PagePlanPhaseVO();
        if (CollectionUtils.isEmpty(entityDOList)) {
            pagePlanPhaseVO.setList(phaseVOList);
            pagePlanPhaseVO.setTotal(page.getTotal());
            return pagePlanPhaseVO;
        }
        for (TmTestPlanEntityDO t : entityDOList) {
            //安全测试计划关联不了移动与安全测试计划
            if (query.getCaseType().equals(TestcaseTypeEnum.AUTO)
                    && (t.getType().equals(TestPlanNewTypeEnum.SAFETY_TEST)
                    || t.getType().equals(TestPlanNewTypeEnum.MOBILE_SPECIAL))) {
                continue;
            }
            PlanPhaseVO planPhaseVO = new PlanPhaseVO();
            planPhaseVO.setPlanCode(t.getCode());
            planPhaseVO.setPlanName(t.getPlanName());
            planPhaseVO.setType(t.getType());
            planPhaseVO.setStatus(t.getStatus());
            planPhaseVO.setTestStrategy(t.getTestStrategy());
            if (t.getStageStatus() != null
                    && t.getType().name().equals(TestPlanNewTypeEnum.TEST_PLAN.name())
                    && (t.getTestStrategy().equals(TestPlanStrategyEnum.ALLOW_EXIT_TEST)
                    || t.getTestStrategy().equals(TestPlanStrategyEnum.ALL_TEST)
                    || t.getTestStrategy().equals(TestPlanStrategyEnum.STANDARD_TEST))) {
                TestPlanStageVO[] testPlanStageVOList = new TestPlanStageVO[3];
                Set<Map.Entry<String, Object>> en = t.getStageStatus().entrySet();
                for (Map.Entry<String, Object> entry : en) {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    if (key.equals(TestPlanStageEnum.SMOKE_TEST.name())) {
                        testPlanStageVOList[0] = new TestPlanStageVO();
                        testPlanStageVOList[0].setTestStage(TestPlanStageEnum.valueOf(key));
                        testPlanStageVOList[0].setTestStageStatus(TestPlanStageStatusEnum.valueOf((String) value));
                    }
                    if (key.equals(TestPlanStageEnum.FUNCTIONAL_TEST.name())) {
                        testPlanStageVOList[1] = new TestPlanStageVO();
                        testPlanStageVOList[1].setTestStage(TestPlanStageEnum.valueOf(key));
                        testPlanStageVOList[1].setTestStageStatus(TestPlanStageStatusEnum.valueOf((String) value));
                    }
                    if (key.equals(TestPlanStageEnum.ONLINE_SMOKE_TEST.name())) {
                        testPlanStageVOList[2] = new TestPlanStageVO();
                        testPlanStageVOList[2].setTestStage(TestPlanStageEnum.valueOf(key));
                        testPlanStageVOList[2].setTestStageStatus(TestPlanStageStatusEnum.valueOf((String) value));
                    }
                }
                planPhaseVO.setTestPlanStageVOList(Arrays.asList(testPlanStageVOList));
            }
            phaseVOList.add(planPhaseVO);
        }
        pagePlanPhaseVO.setList(phaseVOList);
        pagePlanPhaseVO.setTotal(page.getTotal());
        return pagePlanPhaseVO;
    }

    public FindSortedPlanCaseVO findSortedPlanCase(FindSortedPlanCaseQuery query) {
        FindSortedPlanCaseVO vo = new FindSortedPlanCaseVO();
        TestcaseVO testcaseVO = testcaseRepository.findCaseOrModuleByCodeQuery(query.getCaseCode());
        query.setSetCore(testcaseVO.getSetCore());
        List<PlanCaseVO> planCaseList = tmTestPlanCaseRepository.selectSimplePlanCase(query);
        if (CollectionUtil.isEmpty(planCaseList)) {
            return vo;
        }
        List<String> codeList = planCaseList.stream()
                .flatMap(planCase -> Arrays.stream(planCase.getPath().split("\\.")))
                .distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(codeList)) {
            return vo;
        }
        List<PlanCaseVO> moduleList = testCaseSortRepository.selectSortedPlanCaseByCode(codeList);
        PlanCaseVO noGroup = new PlanCaseVO();
        noGroup.setCaseCode(StringUtils.EMPTY);
        noGroup.setParentCode(StringUtils.EMPTY);
        moduleList.add(0, noGroup);

        findSortedPlanCase(vo, query.getCaseCode(), moduleList, planCaseList, StringUtils.EMPTY, new AtomicReference<>(null));
        return vo;
    }

    private void findSortedPlanCase(FindSortedPlanCaseVO vo, String code, List<PlanCaseVO> moduleList,
                                    List<PlanCaseVO> planCaseList, String parentCode, AtomicReference<String> temp) {
        moduleList.stream()
                .filter(module -> parentCode.equals(module.getParentCode()))
                .forEach(module -> {
                    if (StringUtils.isEmpty(vo.getNext()) && StringUtils.isNotEmpty(module.getCaseCode())) {
                        findSortedPlanCase(vo, code, moduleList, planCaseList, module.getCaseCode(), temp);
                    }
                    planCaseList.stream()
                            .filter(planCase -> module.getCaseCode().equals(planCase.getParentCode()))
                            .forEach(planCase -> {
                                if (code.equals(planCase.getCaseCode())) {
                                    vo.setPrevious(temp.get());
                                }
                                if (code.equals(temp.get())) {
                                    vo.setNext(planCase.getCaseCode());
                                }
                                temp.set(planCase.getCaseCode());
                            });
                });
    }

    public PageTestPlanVO getPagePlan(PageTestPlanQuery query) {
        TmTestPlanEntityDO entityDO = tmTestPlanDomainConverter.convert(query);
        com.github.pagehelper.Page<TmTestPlanEntityDO> page = PageHelper.startPage(query.getPage(), query.getSize());
        List<TmTestPlanEntityDO> entityDOList = tmTestPlanRepository.selectPlanOrSafePlan(entityDO);
        List<PageTestPlanBaseVO> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(entityDOList)) {
            entityDOList.forEach(i -> {
                PageTestPlanBaseVO baseVO = new PageTestPlanBaseVO();
                baseVO.setPlanCode(i.getCode());
                baseVO.setPlanName(i.getPlanName());
                list.add(baseVO);
            });
        }
        PageTestPlanVO testPlanVO = new PageTestPlanVO();
        testPlanVO.setList(list);
        testPlanVO.setTotal(page.getTotal());
        return testPlanVO;
    }

    public List<TmTestPlanVO> getVersionPlan(VersionPlanQuery query) {
        List<TmTestPlanEntityDO> tmTestPlanEntityLists = tmTestPlanRepository.selectVersionPlanList(query);
        List<TmTestPlanVO> tmTestPlanVOS = tmTestPlanDomainConverter.convertLists(tmTestPlanEntityLists);
        return tmTestPlanVOS;
    }

    public List<TestPlanCaseVO> listPlanCase(ListPlanCaseQuery query) {
        //一键展开
        boolean groupByVersion = Boolean.FALSE;
        if (ALL_PLAN_CASE_MODULE.equals(query.getParentCode())) {
            query.setParentCode(null);
            if (CollectionUtil.isNotEmpty(query.getSetCoreList()) && query.getSetCoreList().contains(Boolean.FALSE)) {
                groupByVersion = Boolean.TRUE;
            }
        }

        //未分组用例，区分版本
        if (StringUtils.isNotEmpty(query.getParentCode()) && query.getParentCode().startsWith(NO_GROUP_PLAN_CASE_MODULE)) {
            if (query.getParentCode().contains("VER")) {
                query.setVersionCode(query.getParentCode().replace(NO_GROUP_PLAN_CASE_MODULE + "___", ""));
            }
            query.setParentCode(StringUtils.EMPTY);
        }

        //按版本查询全部用例
        if (StringUtils.isNotBlank(query.getParentCode()) && query.getParentCode().contains("VER")) {
            query.setVersionCode(query.getParentCode());
            query.setParentCode(null);
        }

        Boolean moduleSetCore = null;
        if (CollectionUtils.isNotEmpty(query.getCaseTypeList())) {
            if (query.getCaseTypeList().contains(TestcaseTypeEnum.MANUAL)) {
                if (CollectionUtils.isEmpty(query.getSetCoreList())) {
                    query.setSetCoreList(Collections.singletonList(Boolean.FALSE));
                }
                moduleSetCore = query.getSetCoreList().contains(Boolean.TRUE);
            }
            if (query.getCaseTypeList().contains(TestcaseTypeEnum.AUTO)) {
                query.setIsAuto(Boolean.TRUE);
            }
        }

        List<AutomaticNodeTypeEnum> moduleNodeTypeList = null;
        if (CollectionUtils.isNotEmpty(query.getNodeTypeList())) {
            moduleNodeTypeList = query.getNodeTypeList().stream()
                    .filter(n -> TestcaseAttributeEnum.MODULE.equals(n.getAttribute()))
                    .collect(Collectors.toList());
            if (query.getNodeTypeList().contains(AutomaticNodeTypeEnum.OTHER)) {
                moduleNodeTypeList.addAll(Arrays.asList(AutomaticNodeTypeEnum.TestPlan, AutomaticNodeTypeEnum.ThreadGroup));
            }
            query.setNodeTypeList(query.getNodeTypeList().stream()
                    .filter(n -> TestcaseAttributeEnum.TESTCASE.equals(n.getAttribute()))
                    .collect(Collectors.toList()));
        }

        if (CollectionUtils.isNotEmpty(query.getStatusList()) && query.getStatusList().contains(TestPlanCaseStatusEnum.PASSED)) {
            query.getStatusList().add(TestPlanCaseStatusEnum.SUCCESS);
        }

        if (StringUtils.isNotEmpty(query.getSearch())) {
            query.setSearch(query.getSearch().replace("\\", "\\\\")
                    .replace("%", "\\%")
                    .replace("_", "\\_"));
        }

        List<TestPlanCaseVO> planCaseList = tmTestPlanCaseRepository.getTestPlanCaseList(query);
        if (CollectionUtil.isEmpty(planCaseList)) {
            return Collections.emptyList();
        }

        if (StringUtils.EMPTY.equals(query.getParentCode())) {
            planCaseList.forEach(item -> item.setEnableChecked(true));
            return planCaseList;
        }

        if (CollectionUtils.isNotEmpty(moduleNodeTypeList)) {
            PageTestcaseQuery pageTestcaseQuery = new PageTestcaseQuery();
            pageTestcaseQuery.setProductCode(query.getProductCode());
            pageTestcaseQuery.setNodeTypeList(moduleNodeTypeList);
            List<String> moduleCodeList = testcaseRepository.selectModuleCodeList(pageTestcaseQuery);
            planCaseList.removeIf(item -> moduleCodeList.stream().noneMatch(s -> item.getCasePath().contains(s)));
        }

        //手工用例，按版本分组
        if ((null == query.getIsAuto() || !query.getIsAuto()) && groupByVersion && CollectionUtil.isNotEmpty(planCaseList)) {
            return buildVersionModuleWithChildren(query, planCaseList, moduleSetCore);
        }

        ListPlanCaseModuleQuery listPlanCaseModuleQuery = new ListPlanCaseModuleQuery();
        listPlanCaseModuleQuery.setProductCode(query.getProductCode());
        listPlanCaseModuleQuery.setCaseTypeList(query.getCaseTypeList());
        listPlanCaseModuleQuery.setSetCore(moduleSetCore);
        listPlanCaseModuleQuery.setIsAuto(query.getIsAuto());
        List<TestPlanCaseVO> planModuleList = tmTestPlanCaseRepository.selectPlanCaseModuleList(listPlanCaseModuleQuery);
        if (Boolean.TRUE.equals(query.getIsAuto())) {
            planModuleList.addAll(tmTestPlanCaseRepository.selectPlanCaseAutomaticModuleList(listPlanCaseModuleQuery));
        }

        List<String> moduleControllerCodeList = planModuleList.parallelStream()
                .filter(item -> AutomaticNodeTypeEnum.ModuleController.name().equals(item.getNodeType()))
                .map(TestPlanCaseVO::getCaseCode)
                .collect(Collectors.toList());

        planModuleList.removeIf(item -> planCaseList.stream()
                .noneMatch(s -> s.getCasePath().contains(item.getCaseCode())
                        || StringUtils.defaultString(s.getTestcaseModulePath()).contains(item.getCaseCode())));

        planModuleList.addAll(planCaseList.parallelStream()
                .filter(item -> StringUtils.isNotEmpty(item.getParentCode()))
                .collect(Collectors.toList()));

        planModuleList.forEach(item -> {
            boolean enableChecked = Arrays.stream(StringUtils.defaultString(item.getCasePath()).split("\\."))
                    .noneMatch(moduleControllerCodeList::contains);
            item.setEnableChecked(enableChecked);
            List<TestPlanCaseVO> children = planModuleList.parallelStream()
                    .filter(child -> child.getParentCode().equals(item.getCaseCode()))
                    .collect(Collectors.toList());
            item.setChildren(children);
            int childrenNum = (int) planCaseList.parallelStream()
                    .filter(child -> child.getCasePath().contains(item.getCaseCode())
                            || StringUtils.defaultString(child.getTestcaseModulePath()).contains(item.getCaseCode()))
                    .count();
            item.setChildrenNum(childrenNum);
        });

        List<TestPlanCaseVO> tree = planModuleList.parallelStream()
                .filter(item -> StringUtils.defaultString(query.getParentCode()).equals(item.getParentCode()))
                .collect(Collectors.toList());

        if (null == query.getParentCode()) {
            List<TestPlanCaseVO> noGroupCaseList = planCaseList.parallelStream()
                    .filter(item -> StringUtil.isEmpty(item.getParentCode()))
                    .collect(Collectors.toList());
            if (noGroupCaseList.size() > 0) {
                TestPlanCaseVO noGroup = new TestPlanCaseVO();
                noGroup.setCaseName("未分组用例");
                noGroup.setCaseCode(NO_GROUP_PLAN_CASE_MODULE + "___" + query.getVersionCode());
                noGroup.setCaseAttribute(TestcaseAttributeEnum.MODULE);
                noGroup.setChildrenNum(noGroupCaseList.size());
                noGroup.setChildren(noGroupCaseList);
                noGroup.setEnableChecked(true);
                tree.add(0, noGroup);
            }
        }

        return tree;
    }

    /**
     * 手工用例-按版本分组
     *
     * @param query
     * @param planCaseList
     * @return
     */
    private List<TestPlanCaseVO> buildVersionModuleWithChildren(ListPlanCaseQuery query,
                                                                List<TestPlanCaseVO> planCaseList,
                                                                Boolean moduleSetCore) {
        List<TestPlanCaseVO> resultList = new ArrayList<>();
        ListPlanCaseModuleQuery listPlanCaseModuleQuery = new ListPlanCaseModuleQuery();
        listPlanCaseModuleQuery.setProductCode(query.getProductCode());
        listPlanCaseModuleQuery.setCaseTypeList(query.getCaseTypeList());
        listPlanCaseModuleQuery.setSetCore(moduleSetCore);
        listPlanCaseModuleQuery.setIsAuto(query.getIsAuto());
        List<TestPlanCaseVO> planModuleList = tmTestPlanCaseRepository.selectPlanCaseModuleList(listPlanCaseModuleQuery);

        List<String> moduleControllerCodeList = planModuleList.parallelStream()
                .filter(item -> AutomaticNodeTypeEnum.ModuleController.name().equals(item.getNodeType()))
                .map(TestPlanCaseVO::getCaseCode)
                .collect(Collectors.toList());
        //按版本分组
        Map<String, List<TestPlanCaseVO>> versionCaseMap = planCaseList.stream().collect(Collectors.groupingBy(TestPlanCaseVO::getVersionCode));
        if (MapUtils.isEmpty(versionCaseMap)) {
            return new ArrayList<>();
        }
        Map<String, List<TestPlanCaseVO>> versionModuleMap = planModuleList.stream().collect(Collectors.groupingBy(TestPlanCaseVO::getVersionCode));
        Map<String, String> versionInfoMap = getVersionNameList(versionCaseMap.keySet());
        versionCaseMap.forEach((caseVersionCode, caseList) -> {
            versionModuleMap.forEach((moduleVersionCode, moduleList) -> {
                if (caseVersionCode.equals(moduleVersionCode)) {
                    moduleList.removeIf(item -> caseList.stream()
                            .noneMatch(s -> s.getCasePath().contains(item.getCaseCode())
                                    || StringUtils.defaultString(s.getTestcaseModulePath()).contains(item.getCaseCode())));
                    moduleList.addAll(caseList.parallelStream()
                            .filter(item -> StringUtils.isNotEmpty(item.getParentCode()))
                            .collect(Collectors.toList()));
                    moduleList.forEach(item -> {
                        boolean enableChecked = Arrays.stream(StringUtils.defaultString(item.getCasePath()).split("\\."))
                                .noneMatch(moduleControllerCodeList::contains);
                        item.setEnableChecked(enableChecked);
                        List<TestPlanCaseVO> children = moduleList.parallelStream()
                                .filter(child -> child.getParentCode().equals(item.getCaseCode()))
                                .collect(Collectors.toList());
                        item.setChildren(children);
                        int childrenNum = (int) caseList.parallelStream()
                                .filter(child -> child.getCasePath().contains(item.getCaseCode())
                                        || StringUtils.defaultString(child.getTestcaseModulePath()).contains(item.getCaseCode()))
                                .count();
                        item.setChildrenNum(childrenNum);
                    });

                    List<TestPlanCaseVO> tree = moduleList.parallelStream()
                            .filter(item -> StringUtils.defaultString(query.getParentCode()).equals(item.getParentCode()))
                            .collect(Collectors.toList());

                    if (null == query.getParentCode()) {
                        List<TestPlanCaseVO> noGroupCaseList = caseList.parallelStream()
                                .filter(item -> StringUtil.isEmpty(item.getParentCode()))
                                .collect(Collectors.toList());
                        if (noGroupCaseList.size() > 0) {
                            TestPlanCaseVO noGroup = new TestPlanCaseVO();
                            noGroup.setCaseName("未分组用例");
                            noGroup.setCaseCode(NO_GROUP_PLAN_CASE_MODULE + "___" + caseVersionCode);
                            noGroup.setCaseAttribute(TestcaseAttributeEnum.MODULE);
                            noGroup.setChildrenNum(noGroupCaseList.size());
                            noGroup.setChildren(noGroupCaseList);
                            noGroup.setEnableChecked(true);
                            tree.add(0, noGroup);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(tree)) {
                        TestPlanCaseVO result = new TestPlanCaseVO();
                        String moduleName = caseVersionCode.equals("NONE_VERSION")
                                ? "未关联版本用例"
                                : (MapUtils.isEmpty(versionInfoMap) ? caseVersionCode : versionInfoMap.get(caseVersionCode));
                        result.setCaseName(moduleName);
                        result.setCaseCode(caseVersionCode);
                        result.setCaseAttribute(TestcaseAttributeEnum.MODULE);
                        result.setChildrenNum(tree.stream().mapToInt(TestPlanCaseVO::getChildrenNum).sum());
                        result.setChildren(tree);
                        result.setEnableChecked(true);
                        resultList.add(result);
                    }
                }
            });
        });
        return resultList;
    }


    public List<PlanCaseButtonVO> dealPlanStageOperate(String planCode, Long ssoUserId, TestPlanStageEnum testStage) {
        List<PlanCaseButtonVO> buttonVOS = new LinkedList<>();
        TmTestPlanEntityDO vo = getTmTestPlanEntity(planCode);
        if (vo == null) {
            throw new ServiceException("计划不存在:" + planCode);
        }
        Map<String, Object> stageStatus = vo.getStageStatus();
        if (stageStatus == null) {
            stageStatus = new HashMap<>();
            stageStatus.put("SMOKE_TEST", "INITIAL");
        }
        if (stageStatus.get("SMOKE_TEST") == null) {
            stageStatus.put("SMOKE_TEST", "INITIAL");
        }
        boolean isPlanStop = TestPlanNewStatusEnum.TERMINATED.equals(vo.getStatus());
        SimpleVersionVO versionVO = getSimpleVersionVO(vo.getVersionCode());
        if (versionVO.getPresentationDate() == null || versionVO.getApprovalExitDate() == null || versionVO.getTestStrategy() == null) {
            throw new ServiceException("版本计划准入或准出时间或版本测试策略不能为空:" + versionVO.getTestStrategy());
        }
        //普通测试计划 自定义 全流程
        if (TestPlanNewTypeEnum.TEST_PLAN.equals(vo.getType())) {
            if (TestStrategyEnum.ALL_TEST.equals(versionVO.getTestStrategy())
                    || TestStrategyEnum.ALLOW_EXIT_TEST.equals(versionVO.getTestStrategy())
                    || TestStrategyEnum.STANDARD_TEST.equals(versionVO.getTestStrategy())) {
                ListProductMemberByPIdQuery productQuery = new ListProductMemberByPIdQuery();
                // 产品的测试负责人、产品的测试人员、超级管理员
                List<String> memberTypes = Arrays.asList(
                        MemberTypeEnum.TESTER_OWNER.name(),
                        MemberTypeEnum.TESTER_M.name(),
                        MemberTypeEnum.TESTER.name(),
                        MemberTypeEnum.SUPPER.name());
                productQuery.setProductCode(vo.getProductCode());
                productQuery.setMemberTypes(memberTypes);
                Boolean Permission = iProductRpcService.checkProductPermission(ssoUserId, productQuery);
                boolean planer = false;
                if (Objects.nonNull(vo.getPlanDirectorId())) {
                    planer = vo.getPlanDirectorId().equals(ssoUserId);
                }
                if (Permission || planer) {

                    if (testStage.equals(TestPlanStageEnum.SMOKE_TEST) && (TestPlanStageStatusEnum.INITIAL.name().equals(stageStatus.get("SMOKE_TEST")) || TestPlanStageStatusEnum.IN_PROGRESS.name().equals(stageStatus.get("SMOKE_TEST")))) {
                        buttonVOS.add(new PlanCaseButtonVO("移出计划", "deletePlan", "", 1));
                        buttonVOS.add(new PlanCaseButtonVO("设置功能测试用例", "setFunctionTestCase", "", 1));
                        buttonVOS.add(new PlanCaseButtonVO("设置线上冒烟用例", "setOnlineSmokeTestCase", "", 1));
                    } else if (testStage.equals(TestPlanStageEnum.SMOKE_TEST) && TestPlanStageStatusEnum.COMPLETED.name().equals(stageStatus.get("SMOKE_TEST"))) {
                        buttonVOS.add(new PlanCaseButtonVO("设置功能测试用例", "setFunctionTestCase", "", 1));
                        buttonVOS.add(new PlanCaseButtonVO("设置线上冒烟用例", "setOnlineSmokeTestCase", "", 1));
                    } else if (testStage.equals(TestPlanStageEnum.FUNCTIONAL_TEST) && (TestPlanStageStatusEnum.INITIAL.name().equals(stageStatus.get("FUNCTIONAL_TEST")) || TestPlanStageStatusEnum.IN_PROGRESS.name().equals(stageStatus.get("FUNCTIONAL_TEST")))) {
                        buttonVOS.add(new PlanCaseButtonVO("移出计划", "deletePlan", "", 1));
                        if (!TestPlanStageStatusEnum.COMPLETED.name().equals(stageStatus.get("SMOKE_TEST"))) {
                            buttonVOS.add(new PlanCaseButtonVO("设置冒烟用例", "setSmokeTestCase", "", 1));
                        }
                        buttonVOS.add(new PlanCaseButtonVO("设置线上冒烟用例", "setOnlineSmokeTestCase", "", 1));
                    } else if (testStage.equals(TestPlanStageEnum.FUNCTIONAL_TEST) && TestPlanStageStatusEnum.COMPLETED.name().equals(stageStatus.get("FUNCTIONAL_TEST"))) {
                        buttonVOS.add(new PlanCaseButtonVO("设置线上冒烟用例", "setOnlineSmokeTestCase", "", 1));
                    } else if (testStage.equals(TestPlanStageEnum.ONLINE_SMOKE_TEST) && TestPlanStageStatusEnum.INITIAL.name().equals(stageStatus.get("ONLINE_SMOKE_TEST"))) {
                        buttonVOS.add(new PlanCaseButtonVO("移出计划", "deletePlan", "", 1));
                        if (!TestPlanStageStatusEnum.COMPLETED.name().equals(stageStatus.get("SMOKE_TEST"))) {
                            buttonVOS.add(new PlanCaseButtonVO("设置冒烟用例", "setSmokeTestCase", "", 1));
                        }
                        if (!TestPlanStageStatusEnum.COMPLETED.name().equals(stageStatus.get("FUNCTIONAL_TEST"))) {
                            buttonVOS.add(new PlanCaseButtonVO("设置功能测试用例", "setFunctionTestCase", "", 1));
                        }
                    } else if (testStage.equals(TestPlanStageEnum.ONLINE_SMOKE_TEST) && TestPlanStageStatusEnum.IN_PROGRESS.name().equals(stageStatus.get("ONLINE_SMOKE_TEST"))) {
                        buttonVOS.add(new PlanCaseButtonVO("移出计划", "deletePlan", "", 1));
                    }
                }
                int diff = daysDiff(versionVO.getPresentationDate(), versionVO.getApprovalExitDate(), 0);
                if (diff <= 2 && !TestStrategyEnum.STANDARD_TEST.equals(versionVO.getTestStrategy())) {
                    return getSimplePlanButtons(stageStatus, isPlanStop, vo.getCode(), buttonVOS);
                }
                return getMoreThan2PlanButtons(stageStatus, isPlanStop, vo.getCode(), buttonVOS, versionVO.getCode());
            }
            if (TestStrategyEnum.EXPLORE_TEST.equals(versionVO.getTestStrategy())) {
                buttonVOS.add(new PlanCaseButtonVO("移出计划", "deletePlan", "", 1));
                if (TestPlanNewStatusEnum.NOT_STARTED.equals(vo.getStatus())) {
                    buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "", getPlanBtnIndex(isPlanStop)));
                    buttonVOS.add(new PlanCaseButtonVO("开始测试", "startTest", "", getPlanBtnIndex(isPlanStop)));
                }
                if (TestPlanNewStatusEnum.IN_PROGRESS.equals(vo.getStatus())) {
                    buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "", getPlanBtnIndex(isPlanStop)));
                    VersionVO versionInfo = iProjectRpcService.findVersionInfoQuery(versionVO.getCode());
                    int sendReportFlag = getPlanBtnIndex(isPlanStop);
                    if (Objects.isNull(versionInfo)) {
                        throw new ServiceException("查询版本信息异常，请联系管理员!");
                    }
                    List<String> hiddenReasonList = new ArrayList<>();
                    if (0 == sendReportFlag) {
                        hiddenReasonList.add("测试计划已终止");
                    }
                    if (PublishStrategyEnum.AUTO.equals(versionInfo.getPublishStrategy())) {
                        if (!Arrays.asList(FlowStatusEnum.ACCEPTING.name(), FlowStatusEnum.DELAY_ACCEPTING.name())
                                .contains(versionInfo.getStatus())) {
                            hiddenReasonList.add("版本未处于【验收中/延迟验收】状态");
                            sendReportFlag = 0;
                        }
                    }
                    buttonVOS.add(new PlanCaseButtonVO("完成测试", "finishTest", "", sendReportFlag, hiddenReasonList));
                }
            }
            if (TestStrategyEnum.ACCEPTANCE_TEST.equals(versionVO.getTestStrategy())) {
                if (TestPlanNewStatusEnum.NOT_STARTED.equals(vo.getStatus())) {
                    buttonVOS.add(new PlanCaseButtonVO("移出计划", "deletePlan", "", 1));
                    buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "", getPlanBtnIndex(isPlanStop)));
                    buttonVOS.add(new PlanCaseButtonVO("开始测试", "startTest", "", getPlanBtnIndex(isPlanStop)));
                    buttonVOS.add(new PlanCaseButtonVO("发送验收测试报告", "sendAcceptanceReport", "", 0));
                }
                if (TestPlanNewStatusEnum.IN_PROGRESS.equals(vo.getStatus())) {
                    buttonVOS.add(new PlanCaseButtonVO("移出计划", "deletePlan", "", 1));
                    buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "", getPlanBtnIndex(isPlanStop)));
                    buttonVOS.add(new PlanCaseButtonVO("发送验收测试报告", "sendAcceptanceReport", "", getPlanBtnIndex(isPlanStop)));
                }
                if (TestPlanNewStatusEnum.COMPLETED.equals(vo.getStatus())) {
                    if (isSendEmail(planCode, EmailTypeEnum.CHECED_TEST)) {
                        buttonVOS.add(new PlanCaseButtonVO("查看验收测试报告", "queryAcceptanceReport", "", 1));
                    } else {
                        buttonVOS.add(new PlanCaseButtonVO("发送验收测试报告", "sendAcceptanceReport", "", getPlanBtnIndex(isPlanStop)));
                    }
                }
            }
            if (TestStrategyEnum.SIMPLE_TEST.equals(versionVO.getTestStrategy())) {
                if (TestPlanNewStatusEnum.NOT_STARTED.equals(vo.getStatus())) {
                    buttonVOS.add(new PlanCaseButtonVO("移出计划", "deletePlan", "", 1));
                    buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "", getPlanBtnIndex(isPlanStop)));
                    buttonVOS.add(new PlanCaseButtonVO("开始测试", "startTest", "", getPlanBtnIndex(isPlanStop)));
                    buttonVOS.add(new PlanCaseButtonVO("发送简易流程报告", "sendSimpleReport", "", 0));
                }
                if (TestPlanNewStatusEnum.IN_PROGRESS.equals(vo.getStatus())) {
                    buttonVOS.add(new PlanCaseButtonVO("移出计划", "deletePlan", "", 1));
                    buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "", getPlanBtnIndex(isPlanStop)));
                    if (isSendEmail(planCode, EmailTypeEnum.SIMPLE_PROCESS)) {
                        buttonVOS.add(new PlanCaseButtonVO("查看简易流程报告", "querySimpleReport", "", 1));
                    } else {
                        int sendReportFlag = getPlanBtnIndex(isPlanStop);
                        VersionVO versionInfo = iProjectRpcService.findVersionInfoQuery(versionVO.getCode());
                        if (Objects.isNull(versionInfo)) {
                            throw new ServiceException("查询版本信息异常，请联系管理员!");
                        }
                        List<String> hiddenReasonList = new ArrayList<>();
                        if (0 == sendReportFlag) {
                            hiddenReasonList.add("测试计划已终止");
                        }
                        if (PublishStrategyEnum.AUTO.equals(versionInfo.getPublishStrategy())) {
                            if (!Arrays.asList(FlowStatusEnum.ACCEPTING.name(), FlowStatusEnum.DELAY_ACCEPTING.name())
                                    .contains(versionInfo.getStatus())) {
                                hiddenReasonList.add("版本未处于【验收中/延迟验收】状态");
                                sendReportFlag = 0;
                            }
                        }
                        buttonVOS.add(new PlanCaseButtonVO("发送简易流程报告", "sendSimpleReport", "", sendReportFlag, hiddenReasonList));
                    }
                }
                if (TestPlanNewStatusEnum.COMPLETED.equals(vo.getStatus())) {
                    if (isSendEmail(planCode, EmailTypeEnum.SIMPLE_PROCESS)) {
                        buttonVOS.add(new PlanCaseButtonVO("查看简易流程报告", "querySimpleReport", "", 1));
                    } else {
                        buttonVOS.add(new PlanCaseButtonVO("发送简易流程报告", "sendSimpleReport", "", getPlanBtnIndex(isPlanStop)));
                    }
                }
            }
        }
        if (TestPlanNewTypeEnum.SAFETY_TEST.equals(vo.getType())) {
            buttonVOS.add(new PlanCaseButtonVO("移出计划", "deletePlan", "", 1));
            if (TestPlanNewStatusEnum.NOT_STARTED.equals(vo.getStatus())) {
                buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "", 1));
                buttonVOS.add(new PlanCaseButtonVO("开始测试", "startTest", "", 1));
            }
            if (TestPlanNewStatusEnum.IN_PROGRESS.equals(vo.getStatus())) {
                buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "", 1));
                buttonVOS.add(new PlanCaseButtonVO("完成测试", "finishTest", "", 1));
            }
        }
        if (TestPlanNewTypeEnum.MOBILE_SPECIAL.equals(vo.getType())) {
            if (TestPlanNewStatusEnum.NOT_STARTED.equals(vo.getStatus())) {
                buttonVOS.add(new PlanCaseButtonVO("移出计划", "deletePlan", "", 1));
                buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "", 1));
                buttonVOS.add(new PlanCaseButtonVO("开始测试", "startTest", "", 1));
                buttonVOS.add(new PlanCaseButtonVO("发送移动专项测试报告", "sendMobileReport", "", 0));
            }
            if (TestPlanNewStatusEnum.IN_PROGRESS.equals(vo.getStatus())) {
                buttonVOS.add(new PlanCaseButtonVO("移出计划", "deletePlan", "", 1));
                buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "", 1));
                buttonVOS.add(new PlanCaseButtonVO("发送移动专项测试报告", "sendMobileReport", "", getPlanBtnIndex(isPlanStop)));
            }
            if (TestPlanNewStatusEnum.COMPLETED.equals(vo.getStatus())) {
                buttonVOS.add(new PlanCaseButtonVO("查看移动专项测试报告", "queryMobileReport", "", 1));
            }
        }
        return buttonVOS;
    }

    private TmTestPlanEntityDO getTmTestPlanEntity(String planCode) {
        return tmTestPlanRepository.selectTmTestPlanByCode(planCode);
    }

    private SimpleVersionVO getSimpleVersionVO(String versionCode) {
        SimpleVersionQuery simpleVersionQuery = new SimpleVersionQuery();
        simpleVersionQuery.setCode(versionCode);
        try {
            SimpleVersionVO versionVO = iProjectRpcService.simpleVersionQuery(simpleVersionQuery);
            return versionVO;
        } catch (Exception e) {
            throw new ServiceException("执行项目域SimpleVersionQuery异常:" + versionCode + e.getMessage());
        }
    }

    private List<PlanCaseButtonVO> getSimplePlanButtons(Map<String, Object> stageStatus,
                                                        boolean isStop, String planCode,
                                                        List<PlanCaseButtonVO> buttonVOS) {
        if (TestPlanStageStatusEnum.INITIAL.name().equals(stageStatus.get("SMOKE_TEST"))) {
            buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "SMOKE_TEST", getPlanBtnIndex(isStop)));
            buttonVOS.add(new PlanCaseButtonVO("开始测试", "startTest", "SMOKE_TEST", getPlanBtnIndex(isStop)));
        }
        if (TestPlanStageStatusEnum.IN_PROGRESS.name().equals(stageStatus.get("SMOKE_TEST"))) {
            buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "SMOKE_TEST", getPlanBtnIndex(isStop)));
            buttonVOS.add(new PlanCaseButtonVO("完成测试", "finishTest", "SMOKE_TEST", getPlanBtnIndex(isStop)));
        }

        if (TestPlanStageStatusEnum.INITIAL.name().equals(stageStatus.get("FUNCTIONAL_TEST"))) {
            buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "FUNCTIONAL_TEST", getPlanBtnIndex(isStop)));
        }
        if (TestPlanStageStatusEnum.IN_PROGRESS.name().equals(stageStatus.get("FUNCTIONAL_TEST"))) {
            buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "FUNCTIONAL_TEST", getPlanBtnIndex(isStop)));
            buttonVOS.add(new PlanCaseButtonVO("完成测试", "finishTest", "FUNCTIONAL_TEST", getPlanBtnIndex(isStop)));
        }

        if (TestPlanStageStatusEnum.INITIAL.name().equals(stageStatus.get("ONLINE_SMOKE_TEST"))) {
            buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "ONLINE_SMOKE_TEST", getPlanBtnIndex(isStop)));
            buttonVOS.add(new PlanCaseButtonVO("发送简易流程报告", "sendSimpleReport", "ONLINE_SMOKE_TEST", 0));
        }
        if (TestPlanStageStatusEnum.IN_PROGRESS.name().equals(stageStatus.get("ONLINE_SMOKE_TEST"))) {
            buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "ONLINE_SMOKE_TEST", 1));
            buttonVOS.add(new PlanCaseButtonVO("发送简易流程报告", "sendSimpleReport", "ONLINE_SMOKE_TEST", getPlanBtnIndex(isStop)));
        }
        if (TestPlanStageStatusEnum.COMPLETED.name().equals(stageStatus.get("ONLINE_SMOKE_TEST"))) {
            if (isSendEmail(planCode, EmailTypeEnum.SIMPLE_PROCESS)) {
                buttonVOS.add(new PlanCaseButtonVO("查看简易流程报告", "querySimpleReport", "ONLINE_SMOKE_TEST", 1));
            } else {
                buttonVOS.add(new PlanCaseButtonVO("发送简易流程报告", "sendSimpleReport", "ONLINE_SMOKE_TEST", getPlanBtnIndex(isStop)));
            }
        }
        return buttonVOS;
    }

    private List<PlanCaseButtonVO> getMoreThan2PlanButtons(Map<String, Object> stageStatus,
                                                           boolean isStop, String planCode,
                                                           List<PlanCaseButtonVO> buttonVOS,
                                                           String versionCode) {
        VersionVO versionInfo = iProjectRpcService.findVersionInfoQuery(versionCode);
        if (Objects.isNull(versionInfo)) {
            throw new ServiceException("查询版本信息异常，请联系管理员!");
        }
        boolean checkFlag = StringUtils.isNotBlank(versionInfo.getTestStrategy()) && versionInfo.getTestStrategy().equals(TestStrategyEnum.STANDARD_TEST.name());
        if (TestPlanStageStatusEnum.INITIAL.name().equals(stageStatus.get("SMOKE_TEST"))) {
            int sendReportFlag = getPlanBtnIndex(isStop);
            List<String> hiddenReasonList = new ArrayList<>();
            if (0 == sendReportFlag) {
                hiddenReasonList.add("测试计划已终止");
                if (checkFlag && PublishStrategyEnum.AUTO.equals(versionInfo.getPublishStrategy())) {
                    if (!versionInfo.getStatus().equals(FlowStatusEnum.SMOKING.name())) {
                        hiddenReasonList.add("版本未处于【冒烟中】状态");
                    }
                    List<TmTestPlanCaseEntityDO> caseEntityDOList = tmTestPlanCaseRepository.selectTestPlanCaseByPlanCodeAndTestStage(planCode, Collections.singletonList(TestPlanStageEnum.SMOKE_TEST));
                    if (CollectionUtil.isEmpty(caseEntityDOList)) {
                        hiddenReasonList.add("未关联冒烟用例");
                        hiddenReasonList.add("冒烟用例必须有执行结果(不包含未测）");
                    } else {
                        long countNum = caseEntityDOList.stream().filter(item -> (Objects.isNull(item.getStatus())
                                || TestPlanCaseStatusEnum.NOT_STARTED.equals(item.getStatus())
                                || TestPlanCaseStatusEnum.INITIAL.equals(item.getStatus()))).count();
                        if (countNum > 0) {
                            hiddenReasonList.add("冒烟用例必须有执行结果(不包含未测）");
                        }
                    }
                }

            }
            buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "SMOKE_TEST", getPlanBtnIndex(isStop)));
            buttonVOS.add(new PlanCaseButtonVO("开始测试", "startTest", "SMOKE_TEST", getPlanBtnIndex(isStop)));
            buttonVOS.add(new PlanCaseButtonVO("发送准入报告", "sendAccessReport", "SMOKE_TEST", 0, hiddenReasonList));
        }
        if (TestPlanStageStatusEnum.IN_PROGRESS.name().equals(stageStatus.get("SMOKE_TEST"))) {
            buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "SMOKE_TEST", getPlanBtnIndex(isStop)));
            if (isSendEmail(planCode, EmailTypeEnum.TEST_ACCESS)) {
                buttonVOS.add(new PlanCaseButtonVO("查看准入报告", "queryAccessReport", "SMOKE_TEST", 1));
            } else {
                List<String> hiddenReasonList = new ArrayList<>();
                int sendReportFlag = getPlanBtnIndex(isStop);
                if (0 == sendReportFlag) {
                    hiddenReasonList.add("测试计划已终止");
                }
                if (checkFlag && PublishStrategyEnum.AUTO.equals(versionInfo.getPublishStrategy())) {
                    if (!versionInfo.getStatus().equals(FlowStatusEnum.SMOKING.name())) {
                        hiddenReasonList.add("版本未处于【冒烟中】状态");
                        sendReportFlag = 0;
                    }
                    List<TmTestPlanCaseEntityDO> caseEntityDOList = tmTestPlanCaseRepository.selectTestPlanCaseByPlanCodeAndTestStage(planCode, Collections.singletonList(TestPlanStageEnum.SMOKE_TEST));
                    if (CollectionUtil.isEmpty(caseEntityDOList)) {
                        hiddenReasonList.add("未关联冒烟用例");
                        hiddenReasonList.add("冒烟用例必须有执行结果(不包含未测）");
                        sendReportFlag = 0;
                    } else {
                        long countNum = caseEntityDOList.stream().filter(item -> (Objects.isNull(item.getStatus())
                                || TestPlanCaseStatusEnum.NOT_STARTED.equals(item.getStatus())
                                || TestPlanCaseStatusEnum.INITIAL.equals(item.getStatus()))).count();
                        if (countNum > 0) {
                            hiddenReasonList.add("冒烟用例必须有执行结果(不包含未测）");
                            sendReportFlag = 0;
                        }
                    }
                }
                buttonVOS.add(new PlanCaseButtonVO("发送准入报告", "sendAccessReport", "SMOKE_TEST", sendReportFlag, hiddenReasonList));
            }
        }
        if (TestPlanStageStatusEnum.COMPLETED.name().equals(stageStatus.get("SMOKE_TEST"))) {
            if (isSendEmail(planCode, EmailTypeEnum.TEST_ACCESS)) {
                buttonVOS.add(new PlanCaseButtonVO("查看准入报告", "queryAccessReport", "SMOKE_TEST", 1));
            } else {
                buttonVOS.add(new PlanCaseButtonVO("发送准入报告", "sendAccessReport", "SMOKE_TEST", getPlanBtnIndex(isStop)));
            }
        }
        if (TestPlanStageStatusEnum.INITIAL.name().equals(stageStatus.get("FUNCTIONAL_TEST"))) {
            buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "FUNCTIONAL_TEST", getPlanBtnIndex(isStop)));
            if (isSendEmail(planCode, EmailTypeEnum.TEST_PERMIT)) {
                buttonVOS.add(new PlanCaseButtonVO("查看准出报告", "queryPermitReport", "FUNCTIONAL_TEST", 1));
            } else {
                List<String> hiddenReasonList = new ArrayList<>();
                int sendReportFlag = getPlanBtnIndex(isStop);
                if (0 == sendReportFlag) {
                    hiddenReasonList.add("测试计划已终止");
                }
                if (checkFlag && PublishStrategyEnum.AUTO.equals(versionInfo.getPublishStrategy())) {
                    if (!versionInfo.getStatus().equals(FlowStatusEnum.REGRESSING.name())) {
                        hiddenReasonList.add("版本未处于【回归中】状态");
                        sendReportFlag = 0;
                    }
                    List<TmTestPlanCaseEntityDO> caseEntityDOList = tmTestPlanCaseRepository.selectTestPlanCaseByPlanCodeAndTestStage(planCode, Collections.singletonList(TestPlanStageEnum.FUNCTIONAL_TEST));
                    if (CollectionUtil.isEmpty(caseEntityDOList)) {
                        hiddenReasonList.add("未关联功能测试用例");
                        hiddenReasonList.add("功能测试用例必须有执行结果(不包含未测）");
                        sendReportFlag = 0;
                    } else {
                        long countNum = caseEntityDOList.stream().filter(item -> (Objects.isNull(item.getStatus())
                                || TestPlanCaseStatusEnum.NOT_STARTED.equals(item.getStatus())
                                || TestPlanCaseStatusEnum.INITIAL.equals(item.getStatus()))).count();
                        if (countNum > 0) {
                            hiddenReasonList.add("功能测试用例必须有执行结果(不包含未测）");
                            sendReportFlag = 0;
                        }
                    }
                }
                buttonVOS.add(new PlanCaseButtonVO("发送准出报告", "sendPermitReport", "FUNCTIONAL_TEST", sendReportFlag, hiddenReasonList));
            }
        }
        if (TestPlanStageStatusEnum.IN_PROGRESS.name().equals(stageStatus.get("FUNCTIONAL_TEST"))) {
            buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "FUNCTIONAL_TEST", getPlanBtnIndex(isStop)));
            if (isSendEmail(planCode, EmailTypeEnum.TEST_PERMIT)) {
                buttonVOS.add(new PlanCaseButtonVO("查看准出报告", "queryPermitReport", "FUNCTIONAL_TEST", 1));
            } else {
                List<String> hiddenReasonList = new ArrayList<>();
                int sendReportFlag = getPlanBtnIndex(isStop);
                if (0 == sendReportFlag) {
                    hiddenReasonList.add("测试计划已终止");
                }
                if (checkFlag && PublishStrategyEnum.AUTO.equals(versionInfo.getPublishStrategy())) {
                    if (!versionInfo.getStatus().equals(FlowStatusEnum.REGRESSING.name())) {
                        hiddenReasonList.add("版本未处于【回归中】状态");
                        sendReportFlag = 0;
                    }
                    List<TmTestPlanCaseEntityDO> caseEntityDOList = tmTestPlanCaseRepository.selectTestPlanCaseByPlanCodeAndTestStage(planCode, Collections.singletonList(TestPlanStageEnum.FUNCTIONAL_TEST));
                    if (CollectionUtil.isEmpty(caseEntityDOList)) {
                        hiddenReasonList.add("未关联功能测试用例");
                        hiddenReasonList.add("功能测试用例必须有执行结果(不包含未测）");
                        sendReportFlag = 0;
                    } else {
                        long countNum = caseEntityDOList.stream().filter(item -> (Objects.isNull(item.getStatus())
                                || TestPlanCaseStatusEnum.NOT_STARTED.equals(item.getStatus())
                                || TestPlanCaseStatusEnum.INITIAL.equals(item.getStatus()))).count();
                        if (countNum > 0) {
                            hiddenReasonList.add("功能测试用例必须有执行结果(不包含未测）");
                            sendReportFlag = 0;
                        }
                    }
                }
                buttonVOS.add(new PlanCaseButtonVO("发送准出报告", "sendPermitReport", "FUNCTIONAL_TEST", sendReportFlag, hiddenReasonList));
            }
        }
        if (TestPlanStageStatusEnum.COMPLETED.name().equals(stageStatus.get("FUNCTIONAL_TEST"))) {
            if (isSendEmail(planCode, EmailTypeEnum.TEST_PERMIT)) {
                buttonVOS.add(new PlanCaseButtonVO("查看准出报告", "queryPermitReport", "FUNCTIONAL_TEST", 1));
            } else {
                buttonVOS.add(new PlanCaseButtonVO("发送准出报告", "sendPermitReport", "FUNCTIONAL_TEST", getPlanBtnIndex(isStop)));
            }
        }
        if (TestPlanStageStatusEnum.INITIAL.name().equals(stageStatus.get("ONLINE_SMOKE_TEST"))) {
            buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "ONLINE_SMOKE_TEST", getPlanBtnIndex(isStop)));
            if (isSendEmail(planCode, EmailTypeEnum.ONLINE_SMOKE)) {
                buttonVOS.add(new PlanCaseButtonVO("查看线上冒烟报告", "queryOnlineSmokeReport", "ONLINE_SMOKE_TEST", 1));
            } else {
                List<String> hiddenReasonList = new ArrayList<>();
                int sendReportFlag = getPlanBtnIndex(isStop);
                if (0 == sendReportFlag) {
                    hiddenReasonList.add("测试计划已终止");
                }
                if (checkFlag && PublishStrategyEnum.AUTO.equals(versionInfo.getPublishStrategy())) {
                    if (!Arrays.asList(FlowStatusEnum.ACCEPTING.name(), FlowStatusEnum.DELAY_ACCEPTING.name())
                            .contains(versionInfo.getStatus())) {
                        hiddenReasonList.add("版本未处于【验收中/延迟验收】状态");
                        sendReportFlag = 0;
                    }
                    List<TmTestPlanCaseEntityDO> caseEntityDOList = tmTestPlanCaseRepository.selectTestPlanCaseByPlanCodeAndTestStage(planCode, Collections.singletonList(TestPlanStageEnum.ONLINE_SMOKE_TEST));
                    if (CollectionUtil.isEmpty(caseEntityDOList)) {
                        hiddenReasonList.add("未关联线上冒烟用例");
                        hiddenReasonList.add("线上冒烟用例必须有执行结果(不包含未测）");
                        sendReportFlag = 0;
                    } else {
                        long countNum = caseEntityDOList.stream().filter(item -> (Objects.isNull(item.getStatus())
                                || TestPlanCaseStatusEnum.NOT_STARTED.equals(item.getStatus())
                                || TestPlanCaseStatusEnum.INITIAL.equals(item.getStatus()))).count();
                        if (countNum > 0) {
                            hiddenReasonList.add("线上冒烟用例必须有执行结果(不包含未测）");
                            sendReportFlag = 0;
                        }
                    }
                }
                buttonVOS.add(new PlanCaseButtonVO("发送线上冒烟报告", "sendOnlineSmokeReport", "ONLINE_SMOKE_TEST", sendReportFlag, hiddenReasonList));
            }
        }
        if (TestPlanStageStatusEnum.IN_PROGRESS.name().equals(stageStatus.get("ONLINE_SMOKE_TEST"))) {
            buttonVOS.add(new PlanCaseButtonVO("关联用例", "addPlanCase", "ONLINE_SMOKE_TEST", getPlanBtnIndex(isStop)));
            if (isSendEmail(planCode, EmailTypeEnum.ONLINE_SMOKE)) {
                buttonVOS.add(new PlanCaseButtonVO("查看线上冒烟报告", "queryOnlineSmokeReport", "ONLINE_SMOKE_TEST", 1));
            } else {
                List<String> hiddenReasonList = new ArrayList<>();
                int sendReportFlag = getPlanBtnIndex(isStop);
                if (0 == sendReportFlag) {
                    hiddenReasonList.add("测试计划已终止");
                }
                if (checkFlag && PublishStrategyEnum.AUTO.equals(versionInfo.getPublishStrategy())) {
                    if (!Arrays.asList(FlowStatusEnum.ACCEPTING.name(), FlowStatusEnum.DELAY_ACCEPTING.name())
                            .contains(versionInfo.getStatus())) {
                        hiddenReasonList.add("版本未处于【验收中/延迟验收】状态");
                        sendReportFlag = 0;
                    }
                    List<TmTestPlanCaseEntityDO> caseEntityDOList = tmTestPlanCaseRepository.selectTestPlanCaseByPlanCodeAndTestStage(planCode, Collections.singletonList(TestPlanStageEnum.ONLINE_SMOKE_TEST));
                    if (CollectionUtil.isEmpty(caseEntityDOList)) {
                        hiddenReasonList.add("未关联线上冒烟用例");
                        hiddenReasonList.add("线上冒烟用例必须有执行结果(不包含未测）");
                        sendReportFlag = 0;
                    } else {
                        long countNum = caseEntityDOList.stream().filter(item -> (Objects.isNull(item.getStatus())
                                || TestPlanCaseStatusEnum.NOT_STARTED.equals(item.getStatus())
                                || TestPlanCaseStatusEnum.INITIAL.equals(item.getStatus()))).count();
                        if (countNum > 0) {
                            hiddenReasonList.add("线上冒烟用例必须有执行结果(不包含未测）");
                            sendReportFlag = 0;
                        }
                    }
                }
                buttonVOS.add(new PlanCaseButtonVO("发送线上冒烟报告", "sendOnlineSmokeReport", "ONLINE_SMOKE_TEST", sendReportFlag, hiddenReasonList));
            }
        }
        if (TestPlanStageStatusEnum.COMPLETED.name().equals(stageStatus.get("ONLINE_SMOKE_TEST"))) {
            if (isSendEmail(planCode, EmailTypeEnum.ONLINE_SMOKE)) {
                buttonVOS.add(new PlanCaseButtonVO("查看线上冒烟报告", "queryOnlineSmokeReport", "ONLINE_SMOKE_TEST", 1));
            } else {
                buttonVOS.add(new PlanCaseButtonVO("发送线上冒烟报告", "sendOnlineSmokeReport", "ONLINE_SMOKE_TEST", getPlanBtnIndex(isStop)));
            }
        }
        return buttonVOS;
    }

    private Integer getPlanBtnIndex(boolean isPlanStop) {
        return isPlanStop ? 0 : 1;
    }

    private boolean isSendEmail(String relatePlanCode, EmailTypeEnum emailType) {
        List<TmEmailEntityDO> entityList = emailRepository.selectEnableByRelatePlanCodeAndEmailType(relatePlanCode, emailType);
        return CollectionUtil.isNotEmpty(entityList);
    }

    public List<TestPlanCaseVO> listPlanCaseModule(ListPlanCaseModuleQuery query) {
        if (CollectionUtils.isNotEmpty(query.getCaseTypeList())) {
            if (query.getCaseTypeList().contains(TestcaseTypeEnum.MANUAL)) {
                query.setSetCore(Boolean.TRUE.equals(query.getSetCore()));
            }
            if (query.getCaseTypeList().contains(TestcaseTypeEnum.AUTO)) {
                query.setSetCore(null);
                query.setIsAuto(Boolean.TRUE);
            }
        }

        List<TestPlanCaseVO> planCaseVOS = tmTestPlanCaseRepository.selectAllPlanCasePath(query);
        if (CollectionUtils.isEmpty(planCaseVOS)) {
            TestPlanCaseVO vo = new TestPlanCaseVO();
            vo.setCaseName("全部用例");
            vo.setChildrenNum(0);
            vo.setCaseCode(ALL_PLAN_CASE_MODULE);
            vo.setChildren(Collections.emptyList());
            return Collections.singletonList(vo);
        }

        List<TestPlanCaseVO> testPlanCaseModuleVOS = tmTestPlanCaseRepository.selectPlanCaseModuleList(query);
        if (Boolean.TRUE.equals(query.getIsAuto())) {
            testPlanCaseModuleVOS.addAll(tmTestPlanCaseRepository.selectPlanCaseAutomaticModuleList(query));
        }
        testPlanCaseModuleVOS.removeIf(t -> planCaseVOS.stream()
                .noneMatch(c -> c.getCasePath().contains(t.getCaseCode())
                        || StringUtils.defaultString(c.getTestcaseModulePath()).contains(t.getCaseCode())));

        testPlanCaseModuleVOS.forEach(t -> {
            List<TestPlanCaseVO> children = testPlanCaseModuleVOS.parallelStream()
                    .filter(child -> child.getParentCode().equals(t.getCaseCode()))
                    .collect(Collectors.toList());
            t.setChildren(children);
            long childrenNum = planCaseVOS.parallelStream()
                    .filter(c -> c.getCasePath().contains(t.getCaseCode())
                            || StringUtils.defaultString(c.getTestcaseModulePath()).contains(t.getCaseCode())
                    ).count();
            t.setChildrenNum((int) childrenNum);
        });

        List<TestPlanCaseVO> treeList = testPlanCaseModuleVOS.parallelStream()
                .filter(t -> StringUtils.EMPTY.equals(t.getParentCode()))
                .collect(Collectors.toList());

        //版本用例，按版本维度展开
        if (query.getCaseTypeList().contains(TestcaseTypeEnum.MANUAL) && !query.getSetCore()) {
            return buildVersionTree(planCaseVOS, treeList, query.getCaseTypeList());
        }

        int topChildrenNum = (int) planCaseVOS.parallelStream()
                .filter(t -> StringUtils.isEmpty(t.getCasePath()) && StringUtils.isEmpty(t.getTestcaseModulePath()))
                .count();

        if (topChildrenNum > 0 && query.getCaseTypeList().contains(TestcaseTypeEnum.MANUAL)) {
            TestPlanCaseVO topCaseGroup = new TestPlanCaseVO();
            topCaseGroup.setCaseName("未分组用例");
            topCaseGroup.setChildrenNum(topChildrenNum);
            topCaseGroup.setCaseCode(NO_GROUP_PLAN_CASE_MODULE);
            topCaseGroup.setCaseAttribute(TestcaseAttributeEnum.MODULE);
            treeList.add(0, topCaseGroup);
        }

        TestPlanCaseVO vo = new TestPlanCaseVO();
        vo.setCaseName("全部用例");
        vo.setChildrenNum(planCaseVOS.size());
        vo.setCaseCode(ALL_PLAN_CASE_MODULE);
        vo.setChildren(treeList);
        return Collections.singletonList(vo);
    }

    public Map<String, String> getVersionNameList(Set<String> versionCodeList) {
        Map<String, String> versionInfoMap = new HashMap<>();
        if (CollectionUtil.isEmpty(versionCodeList)) {
            return versionInfoMap;
        }
        try {
            SimpleVersionListQuery simpleVersionListQuery = new SimpleVersionListQuery();
            simpleVersionListQuery.setCode(new ArrayList<>(versionCodeList));
            SimpleListVersionVO versionInfoList = iProjectRpcService.simpleVersionListQuery(simpleVersionListQuery);
            if (null != versionInfoList && CollectionUtil.isNotEmpty(versionInfoList.getSimpleVersionVOList())) {
                versionInfoMap = versionInfoList.getSimpleVersionVOList().stream().collect(Collectors.toMap(SimpleVersionVO::getCode, SimpleVersionVO::getName));
            }
        } catch (Exception e) {
            log.error("error->{}, getVersionNameList->{}", e, versionCodeList);
        }
        return versionInfoMap;
    }

    /**
     * 手工用例，版本维度展开
     *
     * @param planCaseVOS
     * @param treeList
     * @param caseTypeList
     * @return
     */
    private List<TestPlanCaseVO> buildVersionTree(List<TestPlanCaseVO> planCaseVOS,
                                                  List<TestPlanCaseVO> treeList,
                                                  List<TestcaseTypeEnum> caseTypeList) {
        List<TestPlanCaseVO> versionModuleList = new ArrayList<>();
        Map<String, List<TestPlanCaseVO>> caseMap = planCaseVOS.stream().collect(Collectors.groupingBy(TestPlanCaseVO::getVersionCode));
        if (MapUtils.isEmpty(caseMap)) {
            return new ArrayList<>();
        }
        Map<String, String> versionInfoMap = getVersionNameList(planCaseVOS.stream().map(TestPlanCaseVO::getVersionCode).collect(Collectors.toSet()));
        Map<String, List<TestPlanCaseVO>> moduleMap = treeList.stream().collect(Collectors.groupingBy(TestPlanCaseVO::getVersionCode));
        Map<String, List<TestPlanCaseVO>> finalModuleMap = MapUtils.isEmpty(moduleMap) ? new HashMap<>() : moduleMap;
        caseMap.forEach((caseVersion, caseList) -> {
            int topChildrenNum = (int) caseList.parallelStream()
                    .filter(t -> StringUtils.isEmpty(t.getCasePath()) && StringUtils.isEmpty(t.getTestcaseModulePath()))
                    .count();
            if (topChildrenNum > 0 && caseTypeList.contains(TestcaseTypeEnum.MANUAL)) {
                TestPlanCaseVO topCaseGroup = new TestPlanCaseVO();
                topCaseGroup.setCaseName("未分组用例");
                topCaseGroup.setChildrenNum(topChildrenNum);
                topCaseGroup.setCaseCode(NO_GROUP_PLAN_CASE_MODULE + "___" + caseVersion);
                topCaseGroup.setCaseAttribute(TestcaseAttributeEnum.MODULE);
                if (finalModuleMap.keySet().contains(caseVersion)) {
                    finalModuleMap.get(caseVersion).add(0, topCaseGroup);
                } else {
                    finalModuleMap.put(caseVersion, Collections.singletonList(topCaseGroup));
                }
            }
            finalModuleMap.forEach((moduleVersion, moduleList) -> {
                if (moduleVersion.equals(caseVersion)) {
                    TestPlanCaseVO vo = new TestPlanCaseVO();
                    String moduleName = moduleVersion.equals("NONE_VERSION")
                            ? "未关联版本用例"
                            : (MapUtils.isEmpty(versionInfoMap) ? moduleVersion : versionInfoMap.get(moduleVersion));
                    vo.setCaseName(moduleName);
                    vo.setChildrenNum(caseList.size());
                    vo.setCaseCode(moduleVersion);
                    vo.setChildren(moduleList);
                    versionModuleList.add(vo);
                }
            });
        });
        TestPlanCaseVO vo = new TestPlanCaseVO();
        vo.setCaseName("全部用例");
        vo.setChildrenNum(planCaseVOS.size());
        vo.setCaseCode(ALL_PLAN_CASE_MODULE);
        vo.setChildren(versionModuleList);
        return Collections.singletonList(vo);
    }

    public Boolean checkTestPlanEmail(TestPlanToSendMailQuery query) {
        return emailRepository.testPlanToSendMail(query);
    }

    public Boolean findPlanByProductQuery(FindPlanByProductQuery query) {
        List<TmTestPlanEntityDO> tmTestPlanEntities = tmTestPlanRepository.selectTestPlanEntityListByQuery(query);
        return CollectionUtil.isNotEmpty(tmTestPlanEntities);
    }

    public List<TmTestPlanVO> associatedTestPlanListQuery(AssociatedTestPlanListQuery query) {
        // 所有需要创建移动专项测试且未创建移动专项测试计划的普通测试计划的计划code
        List<String> codeList = tmTestPlanRangeRepository.selectAssociatedTestPlanCode();
        if (CollectionUtil.isEmpty(codeList)) {
            return Collections.emptyList();
        }
        List<TmTestPlanEntityDO> tmTestPlanEntities = tmTestPlanRepository.selectTestPlanEntityListByQuery(query, codeList);
        return tmTestPlanDomainConverter.convertLists(tmTestPlanEntities);
    }

    public List<ButtonVO> currentPersonPermissionInformationQuery(CurrentPersonPermissionInformationQuery query) {

        List<PlanEventFieldEnum> planEventFieldLists = new ArrayList<>();
        if (StringUtil.isEmpty(query.getProductCode()) || null == query.getTransactor().getUserId()) {
            return Collections.emptyList();
        }
        ListProductMemberByPIdQuery productQuery = new ListProductMemberByPIdQuery();
        List<String> memberTypes = Arrays.asList(
                MemberTypeEnum.TESTER_OWNER.name(),
                MemberTypeEnum.TESTER_M.name(),
                MemberTypeEnum.TESTER.name(),
                MemberTypeEnum.SUPPER.name());
        productQuery.setProductCode(query.getProductCode());
        productQuery.setMemberTypes(memberTypes);

        if (iProductRpcService.checkProductPermission(query.getTransactor().getUserId(), productQuery)) {
            planEventFieldLists.add(PlanEventFieldEnum.PlanCreatedEvent);
        }
        return tmTestPlanDomainConverter.convertButtonVOList(planEventFieldLists.stream().distinct().collect(Collectors.toList()));
    }

    public SimpleTmTestPlanVO simpleTmTestPlanQuery(SimpleTmTestPlanQuery query) {
        SimpleTmTestPlanVO vo;
        if (StringUtil.isEmpty(query.getCode())) {
            throw new ServiceException("参数异常");
        }
        TmTestPlanEntityDO entity = tmTestPlanRepository.selectTmTestPlanByCode(query.getCode());
        if (entity == null) {
            throw new ServiceException("测试计划不存在:" + query.getCode());
        }
        vo = tmTestPlanDomainConverter.convertSimpleVO(entity);
        buildSimpleTestPlanRange(vo);
        return vo;
    }

    private void buildSimpleTestPlanRange(SimpleTmTestPlanVO vo) {

        List<TmTestPlanRangeEntityDO> rangeEntities = tmTestPlanRangeRepository.selectBySimpleTmTestPlanVO(vo);
        rangeEntities.forEach(t -> {
            if (TestPlanRangeTypeEnum.MOBILE_TEST.equals(t.getTestRange())) {
                vo.setMobileSpecialTest(t.getTestRangeStatus());
            }
            if (TestPlanRangeTypeEnum.SAFETY_SCANNING.equals(t.getTestRange())) {
                vo.setSecurityScan(t.getTestRangeStatus());
            }
            if (TestPlanRangeTypeEnum.STATIC_ANALYSIS.equals(t.getTestRange())) {
                vo.setStaticAnalysis(t.getTestRangeStatus());
                vo.setStaticAnalysisDirectorId(t.getExecutorId());
                vo.setStaticAnalysisDirectorName(t.getExecutor());
                vo.setStaticAnalysisTime(t.getTestTime());
            }
            if (TestPlanRangeTypeEnum.PERFORMANCE_TEST.equals(t.getTestRange())) {
                vo.setPerformanceTest(t.getTestRangeStatus());
                vo.setPerformanceTestDirectorId(t.getExecutorId());
                vo.setPerformanceTestDirectorName(t.getExecutor());
                vo.setPerformanceTestTime(t.getTestTime());
            }
            if (TestPlanRangeTypeEnum.EXPLORATORY_TEST.equals(t.getTestRange())) {
                vo.setExploratoryTest(t.getTestRangeStatus());
                vo.setExploratoryTestDirectorId(t.getExecutorId());
                vo.setExploratoryTestDirectorName(t.getExecutor());
                vo.setExploratoryTestTime(t.getTestTime());
            }
            if (TestPlanRangeTypeEnum.PERMISSIONS_TEST.equals(t.getTestRange())) {
                vo.setPermissionsTest(t.getTestRangeStatus());
                vo.setPriority(t.getPriority());
                vo.setLastTestDate(t.getTestTime());
                vo.setTestInformation(t.getTestInformation());
                vo.setPermissionsTestInformation(t.getPermissionsTestInformation());
            }
        });
    }

    public boolean testPlanStageStart(String versionCode, String stage) {
        TmTestPlanEntityDO testPlan = tmTestPlanRepository.getTestPlanByVersion(versionCode);
        if (testPlan == null) {
            return false;
        }
        if (CollectionUtil.isNotEmpty(testPlan.getStageStatus())) {
            return !TestPlanStageStatusEnum.INITIAL.name().equals(testPlan.getStageStatus().get(stage));
        }
        return TestPlanNewStatusEnum.IN_PROGRESS.equals(testPlan.getStatus())
                || TestPlanNewStatusEnum.COMPLETED.equals(testPlan.getStatus());
    }

    public boolean testPlanStart(String versionCode) {
        TmTestPlanEntityDO testPlan = tmTestPlanRepository.getTestPlanByVersion(versionCode);
        if (testPlan == null) {
            return false;
        }
        return TestPlanNewStatusEnum.IN_PROGRESS.equals(testPlan.getStatus())
                || TestPlanNewStatusEnum.COMPLETED.equals(testPlan.getStatus());
    }

    public VerifyTestPassConditionResp verifyTestPassCondition(VerifyTestPassConditionQuery query) {
        log.info("verifyTestPassCondition_query->{}", JSONObject.toJSONString(query));
        if (!Arrays.asList(FlowEventEnum.SMOKE_REPORT, FlowEventEnum.REGRESS_REPORT, FlowEventEnum.ACCEPT_REPORT).contains(query.getEvent())) {
            return VerifyTestPassConditionResp.buildPassed();
        }
        VersionInfoVO versionInfoVO = iProjectRpcService.findVersionBaseInfoQuery(query.getVersionCode());
        if (Objects.isNull(versionInfoVO)) {
            throw new ServiceException("版本不存在，请联系管理员！");
        }
        if (!versionInfoVO.getIsConfirm()) {
            throw new ServiceException("请先确认版本！");
        }
        //非标准/简易跳过
        if (VersionTypeEnum.URGENT_TYPE.name().equals(versionInfoVO.getType())
                || !Arrays.asList(TestStrategyEnum.STANDARD_TEST.name(), TestStrategyEnum.SIMPLE_TEST.name()).contains(versionInfoVO.getTestStrategy())) {
            return VerifyTestPassConditionResp.buildPassed();
        }

        TmTestPlanEntityDO planEntityDO = tmTestPlanRepository.getTestPlanByVersion(versionInfoVO.getCode());
        if (Objects.isNull(planEntityDO)) {
            throw new ServiceException("测试计划查询失败，请先确认版本！");
        }
        if (TestPlanNewStatusEnum.TERMINATED.equals(planEntityDO.getStatus())) {
            throw new ServiceException("测试计划已终止，请先重启测试计划！");
        }
        ReportType reportType = ReportType.getByFlowEventAndTestStrategy(query.getEvent(), versionInfoVO.getTestStrategy());
        if (Objects.isNull(reportType)) {
            throw new ServiceException("当前操作获取测试报告类型失败，请联系管理员！");
        }
        List<TmEmailEntityDO> doList = emailRepository.selectEnableByRelatePlanCodeAndEmailType(planEntityDO.getCode(), EmailTypeEnum.buildSelf(reportType));

        //标准
        if (TestStrategyEnum.STANDARD_TEST.name().equals(versionInfoVO.getTestStrategy())) {
            Map<String, Object> stageMap = planEntityDO.getStageStatus();
            if (CollectionUtil.isEmpty(stageMap)) {
                throw new ServiceException("测试计划阶段信息为空，请联系管理员！");
            }
            TestPlanStageEnum planStage = TestPlanStageEnum.getEnumByFlowEvent(query.getEvent(), versionInfoVO.getTestStrategy());
            if (Objects.isNull(planStage)) {
                throw new ServiceException("当前操作获取测试计划阶段失败，请联系管理员！");
            }
            List<TmEmailEntityDO> safetyPlanEmailList = emailRepository.selectEnableByRelatePlanCodeAndEmailType(planEntityDO.getCode(), EmailTypeEnum.SAFETY_TEST);
            Boolean caseNotStartedFlag = tmTestPlanCaseRepository.verifyCaseNotStartedByPlanCodeAndTestStage(planEntityDO.getCode(), Collections.singletonList(planStage));
            // （用例完成 && 报告完成 && 阶段不是未开始） || 阶段完成
            boolean testCompletedFlag = (!caseNotStartedFlag
                    && CollectionUtil.isNotEmpty(doList)
                    && !stageMap.get(planStage.name()).equals(TestPlanStageStatusEnum.INITIAL.name()))
                    || stageMap.get(planStage.name()).equals(TestPlanStageStatusEnum.COMPLETED.name());
            String safetyPlanCode = Strings.EMPTY;
            if (FlowEventEnum.REGRESS_REPORT.equals(query.getEvent())) {
                //回归结果加安全测试计划校验
                testCompletedFlag = testCompletedFlag && CollectionUtil.isNotEmpty(safetyPlanEmailList);
                TmTestPlanEntityDO safePlanEntity = tmTestPlanRepository.getSafePlanCode(planEntityDO.getCode());
                safetyPlanCode = Objects.nonNull(safePlanEntity) ? safePlanEntity.getCode() : Strings.EMPTY;
            }
            return VerifyTestPassConditionResp.buildSelf(testCompletedFlag,
                    CollectionUtil.isNotEmpty(doList),
                    CollectionUtil.isNotEmpty(safetyPlanEmailList),
                    !caseNotStartedFlag,
                    planEntityDO.getCode(),
                    safetyPlanCode,
                    reportType);
        }

        //简易
        if (TestStrategyEnum.SIMPLE_TEST.name().equals(versionInfoVO.getTestStrategy())) {
            if (!query.getEvent().equals(FlowEventEnum.ACCEPT_REPORT)) {
                return VerifyTestPassConditionResp.buildPassed();
            }
            return VerifyTestPassConditionResp.buildSelf(Boolean.TRUE,
                    CollectionUtil.isNotEmpty(doList),
                    Boolean.TRUE,
                    Boolean.TRUE,
                    planEntityDO.getCode(),
                    Strings.EMPTY,
                    reportType);
        }
        return VerifyTestPassConditionResp.buildPassed();
    }
}
