package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.dto.StatisticsVersionIssueEntityDO;

import java.util.Date;
import java.util.List;

public interface StatisticsVersionIssueRepository {

    List<StatisticsVersionIssueEntityDO> selectByActualPublishDateAndProductCode(Date startDate,
                                                                                 Date endDate,
                                                                                 List<String> productCode);

}
