package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.issue.entity.IssueMatterDataResp;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.event.IssueAddedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueEditedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueRemovedEvent;
import org.mapstruct.*;

@Mapper(
        componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface IssueMatterMatterMqConverter {

    IssueMatterDataResp convert(IssueAddedEvent event);

    @Mapping(target = "findVersionCode", source = "findVersion.code")
    @Mapping(target = "findVersionName", source = "findVersion.name")
    @Mapping(target = "fixVersionCode", source = "fixVersion.code")
    @Mapping(target = "fixVersionName", source = "fixVersion.name")
    IssueMatterDataResp convert(IssueEditedEvent event);

    IssueMatterDataResp convert(IssueRemovedEvent event);

    IssueMatterDataResp convert(IssueVO issueVO);

    @Mapping(target = "findVersionCode", source = "findVersion.code")
    @Mapping(target = "findVersionName", source = "findVersion.name")
    @Mapping(target = "fixVersionCode", source = "fixVersion.code")
    @Mapping(target = "fixVersionName", source = "fixVersion.name")
    void converter(IssueEditedEvent event, @MappingTarget IssueMatterDataResp resp);
}
