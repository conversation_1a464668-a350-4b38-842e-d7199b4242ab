package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.dto.ApiTestCaseEntityDO;
import com.zto.devops.qc.client.model.dto.ApiTestEntityDO;
import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.*;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.*;
import com.zto.devops.qc.client.model.testmanager.apitest.event.*;
import com.zto.devops.qc.client.model.testmanager.apitest.query.*;
import com.zto.devops.qc.client.service.testmanager.apitest.model.*;
import com.zto.devops.qc.client.service.testmanager.cases.model.ApiTestCaseExecuteDetailReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface ApiTestDomainConverter {


    @Mapping(target = "variableCode", source = "aggregateId")
    AddApiTestVariableEvent convert(AddApiTestVariableCommand command);

    EditApiTestVariableEvent convert(EditApiTestVariableCommand command);

    UpdateApiTestVariableStatusEvent convert(UpdateApiTestVariableStatusCommand command);

    @Mapping(target = "variableCode", source = "aggregateId")
    DeleteApiTestVariableEvent convert(DeleteApiTestVariableCommand command);

    PageApiCaseQuery convert(PageApiCaseReq req);

    @Mapping(target = "occurred", source = "gmtModified")
    @Mapping(target = "transactor.userId", source = "modifierId")
    @Mapping(target = "transactor.userName", source = "modifier")
    SceneInfoPublishedEvent convert(SceneInfoEntityDO entityDO);

    @Mapping(target = "sceneBackData", ignore = true)
    SceneInfoResp convert2SceneInfoResp(SceneInfoEntityDO entityDO);

    SceneInfoEntityDO copyConvert(SceneInfoEntityDO entityDO);

    @Mapping(target = "occurred", source = "gmtModified")
    @Mapping(target = "transactor.userId", source = "modifierId")
    @Mapping(target = "transactor.userName", source = "modifier")
    ChangeSceneStatusEvent convertToChangeStatusEvent(SceneInfoEntityDO entityDO);

    SceneModuleQueryResp converter(SceneModuleQueryVO vo);

    List<SceneModuleQueryResp> converterList(List<SceneModuleQueryVO> vo);

    AddSceneModuleEvent convert(AddSceneModuleCommand command);

    AddPreDataModuleEvent convert(AddPreDataModuleCommand command);

    @Mapping(target = "gmtCreate", source = "occurred")
    @Mapping(target = "gmtModified", source = "occurred")
    @Mapping(target = "creator", source = "transactor.userName")
    @Mapping(target = "creatorId", source = "transactor.userId")
    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "sceneIndexCode", source = "aggregateId")
    @Mapping(target = "sceneType", expression = "java(null!=event.getSceneType() ? com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.getValueByName(event.getSceneType()) : com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.SCENE.getCode())")
    SceneIndexVO convert(AddSceneModuleEvent event);

    @Mapping(target = "gmtCreate", source = "occurred")
    @Mapping(target = "gmtModified", source = "occurred")
    @Mapping(target = "creator", source = "transactor.userName")
    @Mapping(target = "creatorId", source = "transactor.userId")
    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "sceneIndexCode", source = "aggregateId")
    @Mapping(target = "sceneType", expression = "java(null!=event.getSceneType() ? com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.getValueByName(event.getSceneType()) : com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.SCENE.getCode())")
    SceneIndexVO convert(AddPreDataModuleEvent event);

    EditSceneModuleEvent convert(EditSceneModuleCommand command);

    EditPreDataModuleEvent convert(EditPreDataModuleCommand command);

    @Mapping(target = "gmtModified", source = "occurred")
    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "sceneIndexCode", source = "code")
    @Mapping(target = "sceneType", expression = "java(null!=event.getSceneType() ? com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.getValueByName(event.getSceneType()) : com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.SCENE.getCode())")
    SceneIndexVO convert(EditSceneModuleEvent event);

    @Mapping(target = "gmtModified", source = "occurred")
    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "sceneIndexCode", source = "code")
    @Mapping(target = "sceneType", expression = "java(null!=event.getSceneType() ? com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.getValueByName(event.getSceneType()) : com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.SCENE.getCode())")
    SceneIndexVO convert(EditPreDataModuleEvent event);

    MoveSceneModuleEvent convert(MoveSceneModuleCommand command);

    MovePreDataModuleEvent convert(MovePreDataModuleCommand command);

    BatchMoveSceneEvent convert(BatchMoveSceneCommand command);

    @Mapping(target = "gmtModified", source = "occurred")
    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "sceneIndexCode", source = "code")
    @Mapping(target = "sceneType", expression = "java(com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.getValueByName(event.getSceneType()))")
    SceneIndexVO convert(MoveSceneModuleEvent event);

    @Mapping(target = "gmtModified", source = "occurred")
    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "sceneIndexCode", source = "code")
    @Mapping(target = "sceneType", expression = "java(null!=event.getSceneType() ? com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.getValueByName(event.getSceneType()) : com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum.SCENE.getCode())")
    SceneIndexVO convert(MovePreDataModuleEvent event);

    @Mapping(target = "code", source = "aggregateId")
    MoveSceneModuleCommand convert(EditSceneInfoCommand command);

    @Mapping(target = "code", source = "aggregateId")
    MovePreDataModuleCommand convert(EditPreDataInfoCommand command);

    @Mapping(target = "aggregateId", source = "code")
    @Mapping(target = "sceneName", source = "sceneIndexName")
    EditSceneInfoCommand convertEditSceneInfo(EditSceneModuleCommand command);

    @Mapping(target = "aggregateId", source = "code")
    @Mapping(target = "sceneName", source = "sceneIndexName")
    EditPreDataInfoCommand convertEditPreDataInfo(EditPreDataModuleCommand command);

    List<BaseApiTestVariableResp> converter(List<ApiTestVariableVO> vo);

    List<QueryPreDataVariableVO> convertVOList(List<ApiTestVariableVO> vo);

    PageApiInfoResp convert(ApiSampleCaseVO vo);

    @Mapping(target = "sceneCode", source = "aggregateId")
    PreDataSharedEvent convert(SharePreDataCommand command);

    List<BaseApiTestVariableReq> convertList(List<BaseApiTestVariableResp> list);

    DbAuthorizeEvent convert(DbAuthorizeCommand command);

    ApiTestRevokeEvent convert(ApiTestRevokeCommand command);

    @Mapping(target = "caseCode", source = "aggregateId")
    AddApiTestCaseEvent convert(AddApiTestCaseCommand command);

    @Mapping(target = "aggregateId", source = "caseCode")
    EditApiTestCaseEvent convert(EditApiTestCaseCommand command);

    @Mapping(target = "aggregateId", source = "caseCode")
    PublishApiTestCaseEvent convert(PublishApiTestCaseCommand command);

    AddApiTestCaseEvent convert(ApiTestCaseEntityDO apiTestCaseDO);

    EditApiTestCaseEvent convertToEditApiTestCaseEvent(ApiTestCaseEntityDO apiTestCaseDO);

    ApiCaseDetailResp convertApiCaseDetailResp(ApiTestCaseEntityDO entityDO);

    PageApiTestCaseQuery convert(PageApiTestCaseReq req);

    ApiTestCaseExecuteDetailQuery convert(ApiTestCaseExecuteDetailReq req);

    PageApiTestCaseChildrenQuery convert(PageApiTestCaseChildrenReq req);

    PageApiExceptionCaseQuery convert(QueryApiCaseExceptionReq req);

    @Mapping(target = "apiCode", source = "mainApiCode")
    ApiSampleCaseVO convertApiSampleCaseVO(ApiTestEntityDO entity);

    List<ApiSampleCaseVO> convertApiSampleCaseVO(List<ApiTestEntityDO> entityList);
}
