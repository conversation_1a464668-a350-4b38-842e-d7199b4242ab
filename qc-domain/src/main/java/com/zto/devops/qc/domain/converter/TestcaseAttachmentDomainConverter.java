package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.testmanager.cases.command.AddTestcaseAttachmentCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.RemoveTestcaseAttachmentCommand;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseAttachmentAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseAttachmentRemovedEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface TestcaseAttachmentDomainConverter {


    @Mapping(target = "code", source = "aggregateId")
    TestcaseAttachmentAddedEvent converter(AddTestcaseAttachmentCommand command);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseAttachmentRemovedEvent converter(RemoveTestcaseAttachmentCommand command);

}
