package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.model.issue.command.AddTagCommand;
import com.zto.devops.qc.client.model.issue.command.RemoveTagCommand;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.issue.event.TagAddedEvent;
import com.zto.devops.qc.client.model.issue.event.TagRemovedSimpleEvent;
import com.zto.devops.qc.client.model.testmanager.cases.command.DeleteTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagDeleteEvent;
import com.zto.devops.qc.domain.converter.TagConverter;
import com.zto.devops.qc.domain.gateway.mq.IssueMqSender;
import com.zto.devops.qc.domain.gateway.repository.TagRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class TagCommandDomainService extends BaseDomainService {

    private static final Integer TAG_COUNT = 20;

    @Autowired
    private TagConverter tagConverter;

    @Autowired
    private TagRepository tagRepository;

    @Autowired
    private IssueMqSender issueMqSender;

    public void addTagCommand(AddTagCommand command) {
        log.info("addTagCommand >>> {}", JSON.toJSONString(command));
        TagVO tagVO = tagConverter.convert(command);
        List<TagVO> tagVOList = tagRepository.listTagsByBusinessCodeAndDomain(command.getBusinessCode(), command.getDomain());
        checkAndAddTag(tagVO, tagVOList);
        TagAddedEvent event = createTagAddedEvent(command, tagVO);
        tagRepository.addTag(event);
        if (DomainEnum.ISSUE.equals(command.getDomain())) {
            issueMqSender.handleTagAddedEvent(event);
        }
        apply(event);
    }

    public void removeTagCommand(RemoveTagCommand command) {
        log.info("removeTagCommand >>> {}", JSON.toJSONString(command));
        TagRemovedSimpleEvent event = new TagRemovedSimpleEvent();
        event.setAggregateId(command.getAggregateId());
        event.setCode(command.getCode());
        event.setOccurred(new Date());
        event.setTransactor(command.getTransactor());
        TagVO tagVO = tagRepository.findTagByCode(command.getCode());
        if (tagVO != null) {
            event.setTagName(tagVO.getTagName());
        }
        tagRepository.removeTag(event);
        if (tagVO != null && !tagVO.getDomain().equals(DomainEnum.SCENE)){
            issueMqSender.handleTagRemovedSimpleEvent(event);
        }
        apply(event);
    }

    public void deleteTagCommand(DeleteTestcaseTagCommand command) {
        log.info("DeleteTestcaseTagCommand >>> {}", command.getAggregateId());
        TestcaseTagDeleteEvent event = tagConverter.converter(command);
        TagVO tagVO = tagRepository.findTagByCode(command.getAggregateId());
        event.setTagName(tagVO.getTagName());
        event.setBusinessCode(command.getAggregateId());
        List<String> codeList = tagRepository.findCodesByNameAndProCode(event.getTagName(),
                event.getBusinessCode());
        codeList.add(event.getCode());
        tagRepository.deleteTestcaseTag(codeList);
        apply(event);
    }

    private void checkAndAddTag(TagVO tagVO, List<TagVO> tagVOList) {
        if (CollectionUtils.isNotEmpty(tagVOList)) {
            if (tagVOList.size() >= TAG_COUNT) {
                throw new ServiceException("最多可新增20个标签");
            }
            tagVOList.add(tagVO);
            Map<String, Long> mapGroup = tagVOList.stream()
                    .collect(Collectors.groupingBy(TagVO::getTagName, Collectors.counting()));
            Stream<String> stringStream = mapGroup.entrySet().stream()
                    .filter(entry -> entry.getValue() > 1)
                    .map(Map.Entry::getKey);
            if (stringStream.findAny().isPresent()) {
                throw new ServiceException("标签不能重复添加");
            }
        }
    }

    private TagAddedEvent createTagAddedEvent(AddTagCommand command, TagVO tagVO) {
        TagAddedEvent tagAddedEvent = new TagAddedEvent();
        tagAddedEvent.setAggregateId(command.getAggregateId());
        tagAddedEvent.setBusinessCode(command.getBusinessCode());
        tagAddedEvent.setTags(Collections.singletonList(tagVO));
        tagAddedEvent.setTransactor(command.getTransactor());
        tagAddedEvent.setOccurred(new Date());
        return tagAddedEvent;
    }


}
