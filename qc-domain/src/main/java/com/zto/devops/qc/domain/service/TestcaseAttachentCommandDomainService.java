package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.model.testmanager.cases.command.RemoveTestcaseAttachmentCommand;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseAttachmentRemovedEvent;
import com.zto.devops.qc.client.model.testmanager.plan.event.PlanCaseOperatedLogEvent;
import com.zto.devops.qc.domain.converter.TestcaseAttachmentDomainConverter;
import com.zto.devops.qc.domain.gateway.repository.TestcaseAttachmentRepository;
import com.zto.devops.qc.domain.model.TestcaseAttachment;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TestcaseAttachentCommandDomainService extends BaseDomainService {

    @Autowired
    private TestcaseAttachmentRepository testcaseAttachmentRepository;

    @Autowired
    private TestcaseAttachmentDomainConverter testcaseAttachmentConverter;


    public void removeTestcaseAttachmentCommand(RemoveTestcaseAttachmentCommand command) {
        log.info("RemoveTestcaseAttachmentCommand >>> {}", command.getAggregateId());
        TestcaseAttachment testcaseAttachment = testcaseAttachmentRepository.loadFormDb(command.getAggregateId());
        if (null == testcaseAttachment) {
            log.info("附件不存在。code： {}", command.getAggregateId());
            return;
        }
        TestcaseAttachmentRemovedEvent event = testcaseAttachmentConverter.converter(command);
        event.setAggregateId(command.getAggregateId());
        event.setBusinessCode(testcaseAttachment.getBusinessCode());
        event.setName(testcaseAttachment.getName());
        testcaseAttachmentRepository.removeTestcaseAttachment(event);
        apply(event);
        if (StringUtils.isNotBlank(command.getOperateCaseCode())) {
            PlanCaseOperatedLogEvent logEvent = new PlanCaseOperatedLogEvent();
            logEvent.setAggregateId(command.getOperateCaseCode());
            logEvent.setTransactor(command.getTransactor());
            logEvent.setOccurred(command.getOccurred());
            logEvent.setCode(command.getOperateCaseCode());
            logEvent.setAction("删除了文档");
            logEvent.setMakeString(testcaseAttachment.getName());
            apply(logEvent);
        }
    }
}
