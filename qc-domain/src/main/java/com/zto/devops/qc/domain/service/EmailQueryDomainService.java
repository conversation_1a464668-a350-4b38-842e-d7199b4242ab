package com.zto.devops.qc.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.email.EmailSourceEnum;
import com.zto.devops.qc.client.enums.testmanager.email.TmEmailDataSourceEnum;
import com.zto.devops.qc.client.enums.testmanager.report.EmailButtonEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.enums.rpc.ProductRoleEnum;
import com.zto.devops.qc.client.model.testmanager.email.entity.DetailEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.PageEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.VersionEmailVO;
import com.zto.devops.qc.client.model.testmanager.email.query.DetailEmailQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.PageEmailQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.TestPlanToSendMailQuery;
import com.zto.devops.qc.client.model.testmanager.email.query.VersionEmailQuery;
import com.zto.devops.qc.domain.gateway.repository.AttachmentRepository;
import com.zto.devops.qc.domain.gateway.repository.IEmailRepository;
import com.zto.devops.qc.domain.gateway.repository.ITmTestPlanRepository;
import com.zto.devops.qc.domain.gateway.repository.QcNoticeResultRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EmailQueryDomainService {

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private IEmailRepository emailRepository;
    @Autowired
    private AttachmentRepository attachmentRepository;
    @Autowired
    private QcNoticeResultRepository qcNoticeResultRepository;
    @Autowired
    private ITmTestPlanRepository tmTestPlanRepository;


    public PageEmailVO pageEmail(PageEmailQuery query) {
        return emailRepository.pageEmail(query);
    }

    public DetailEmailVO detailEmail(DetailEmailQuery query) {
        //邮件基础信息
        DetailEmailVO result = emailRepository.findEmailDetail(query);
        List<AttachmentVO> attachmentVOList = getAttachment(result.getBusinessCode());
        if (CollectionUtil.isNotEmpty(attachmentVOList)) {
            result.setAttachments(attachmentVOList);
        }
        //组装收件人抄送人信息
        result = qcNoticeResultRepository.findSendUserInfo(result);
        //组装可操作按钮
        if (StringUtils.isNotBlank(result.getProductCode()) && null != query.getTransactor()
                && result.getDataSource() != TmEmailDataSourceEnum.TRANSFER) {
            result.setButtonVOS(getEmailButtonList(result, query.getTransactor()));
        }
        return result;
    }

    public List<AttachmentVO> getAttachment(String code) {
        return attachmentRepository.findListByBusinessCode(code);
    }

    /**
     * 根据当前登录人id，获取邮件可操作按钮
     *
     * @param emailVO 邮件vo
     * @param user    当前登录人id
     * @return {@link EmailButtonEnum}
     */
    private EmailButtonEnum getEmailButtonList(DetailEmailVO emailVO, User user) {
        //测试计划已终止状态不支持更新
        if (EmailSourceEnum.TEST_PLAN.equals(emailVO.getEmailSource())) {
            Boolean sendFlag = tmTestPlanRepository.checkSendEmailPlan(emailVO.getBusinessCode());
            if (!sendFlag) {
                return null;
            }
        }
        //当前登录人是否有权限
        Boolean contain = checkPermission(emailVO.getProductCode(), user);
        if (contain) {
            return EmailButtonEnum.EDIT_REPORT_OR_PLAN;
        }
        return null;
    }

    /**
     * 校验是否具备发邮件权限
     * @param productCode 产品code
     * @param user 当前操作人
     * @return
     */
    private boolean checkPermission(String productCode, User user) {
        List<ProductRoleEnum> productRoles = productRpcService.getProductRole(productCode, user.getUserId());
        if (CollectionUtil.isNotEmpty(productRoles) &&
                (productRoles.contains(ProductRoleEnum.TESTER) || productRoles.contains(ProductRoleEnum.TESTER_OWNER))) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public List<VersionEmailVO> getVersionEmail(VersionEmailQuery query) {
        return emailRepository.getVersionEmail(query);
    }

}
