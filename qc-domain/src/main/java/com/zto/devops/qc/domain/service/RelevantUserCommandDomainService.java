package com.zto.devops.qc.domain.service;

import cn.hutool.json.JSONUtil;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import com.zto.devops.qc.client.model.issue.command.AddRelevantUserCommand;
import com.zto.devops.qc.client.model.issue.command.RemoveRelevantUserCommand;
import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import com.zto.devops.qc.client.model.issue.event.RelevantUserAddedEvent;
import com.zto.devops.qc.client.model.issue.event.RelevantUserRemovedSimpleEvent;
import com.zto.devops.qc.domain.converter.RelevantUserVOConverter;
import com.zto.devops.qc.domain.gateway.repository.IRelevantUserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Service
@Slf4j
public class RelevantUserCommandDomainService extends BaseDomainService {

    private final static RelevantUserVOConverter RELEVANT_USERVO_CONVERTER = RelevantUserVOConverter.INSTANCE;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;
    @Autowired
    private IRelevantUserRepository iRelevantUserRepository;

    public void ccIssueCommand(AddRelevantUserCommand command){
        Set<RelevantUserVO> relevantUserVOSet = new HashSet<>();
        if (CollectionUtil.isNotEmpty(command.getUserList())) {
            for (User user : command.getUserList()) {
                RelevantUserVO relevantUserVO = RELEVANT_USERVO_CONVERTER.convert(command);
                if (RelevantUserTypeEnum.CC.equals(command.getType())) {
                    relevantUserVO.setCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                }
                relevantUserVO.setUserId(user.getUserId());
                relevantUserVO.setUserName(user.getUserName());
                relevantUserVOSet.add(relevantUserVO);
            }
        }
        RelevantUserAddedEvent relevantUserAddedEvent = new RelevantUserAddedEvent();
        relevantUserAddedEvent.setAggregateId(command.getAggregateId());
        relevantUserAddedEvent.setBusinessCode(command.getAggregateId());
        relevantUserAddedEvent.setRelevantUserVOS(relevantUserVOSet);
        relevantUserAddedEvent.setTransactor(command.getTransactor());
        relevantUserAddedEvent.setOccurred(new Date());
        log.info("----------------AddRelevantUserCommand------------" + JSONUtil.toJsonStr(relevantUserAddedEvent));
        iRelevantUserRepository.ccIssue(relevantUserAddedEvent);
        apply(relevantUserAddedEvent);

    }

    public void unCCIssueCommand(RemoveRelevantUserCommand command){
        RelevantUserRemovedSimpleEvent event = new RelevantUserRemovedSimpleEvent();
        event.setCode(command.getCode());
        event.setAggregateId(command.getCode());
        iRelevantUserRepository.unCCIssueCommand(event);
        apply(event);
    }
}
