package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.enums.ActionEnum;
import com.zto.devops.framework.domain.gateway.mq.IProducerService;
import com.zto.devops.qc.client.enums.constants.BasicTagEnum;
import com.zto.devops.qc.client.model.common.TransitionNodeAddEvent;
import com.zto.devops.qc.client.model.issue.event.TransitionNodeAddedEvent;
import com.zto.devops.qc.client.service.issue.model.TransitionNodeResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
@Async
public class IssueTransitionNodeMqSender {

    @Autowired
    private IProducerService producerService;

    public void handleTransitionNodeAddedEvent(TransitionNodeAddedEvent event) {
        log.info("handleTransitionNodeAddedEvent >>> {}", JSON.toJSONString(event));
        TransitionNodeResp resp = new TransitionNodeResp();
        resp.setCode(event.getCode());
        resp.setContent(event.getContent());
        if (event.getReason() != null) {
            resp.setReason(event.getReason().name());
        }
        if (event.getDomain() != null) {
            resp.setDomain(event.getDomain().name());
        }
        resp.setBusinessCode(event.getBusinessCode());
        if (event.getCurStatus() != null) {
            resp.setCurStatus(event.getCurStatus().getCode());
        }
        if (event.getNextStatus() != null) {
            resp.setNextStatus(event.getNextStatus().getCode());
        }
        resp.preCreate(event);
        producerService.sendBasic(BasicTagEnum.ISSUE_TRANSITION_NODE.getTag(), ActionEnum.I, event.getCode(), JSON.toJSONString(resp));
    }

    public void handleTransitionNodeAddEvent(TransitionNodeAddEvent event) {
        log.info("handleTransitionNodeAddEvent >>> {}", JSON.toJSONString(event));
        TransitionNodeResp resp = new TransitionNodeResp();
        resp.setCode(event.getTransitionNodeCode());
        resp.setContent(event.getContent());
        if(event.getReason() != null){
            resp.setReason(event.getReason().name());
        }
        if(event.domain() != null){
            resp.setDomain(event.domain().name());
        }
        resp.setBusinessCode(event.getBusinessCode());
        if(event.getCurStatus() != null){
            resp.setCurStatus(event.getCurStatus().getCode());
        }
        if(event.getNextStatus() != null){
            resp.setNextStatus(event.getNextStatus().getCode());
        }
        resp.setEnable(Boolean.TRUE);
        resp.setGmtCreate(new Date());
        if (null != event.getOperator()) {
            resp.setCreator(event.getOperator().getUserName());
            resp.setCreatorId(event.getOperator().getUserId());
            resp.setModifier(event.getOperator().getUserName());
            resp.setModifierId(event.getOperator().getUserId());
        }
        resp.setGmtModified(new Date());
        producerService.sendBasic(BasicTagEnum.ISSUE_TRANSITION_NODE.getTag(), ActionEnum.I, resp.getCode(), JSON.toJSONString(resp));
    }
}
