package com.zto.devops.qc.domain.service;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.zto.devops.framework.client.entity.UserInfo;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.LockSeal;
import com.zto.devops.framework.client.simple.ReactiveId;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.gateway.util.LockService;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.constants.LockStoreEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.*;
import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.DebugTaskRespTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.LinkMapTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.rpc.pipeline.NamespaceResp;
import com.zto.devops.qc.client.model.rpc.product.PipelineProductVO;
import com.zto.devops.qc.client.model.rpc.product.SimpleQueryVO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.*;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.*;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskResp;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.Scene;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component.DubboRequestComponent;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component.HttpParameter;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component.HttpRequestComponent;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes.Node;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.metadata.GlobalEnvVO;
import com.zto.devops.qc.client.model.testmanager.apitest.event.*;
import com.zto.devops.qc.client.model.testmanager.cases.command.*;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordLogVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.AddTestcaseEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskExecutedEvent;
import com.zto.devops.qc.client.model.websocket.WebSocketMessage;
import com.zto.devops.qc.client.service.testmanager.apitest.model.*;
import com.zto.devops.qc.domain.converter.ApiTestDomainConverter;
import com.zto.devops.qc.domain.converter.AutomaticSourceRecordDomainConverter;
import com.zto.devops.qc.domain.converter.AutomaticTaskDomainConverter;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.debug.IQcDebugService;
import com.zto.devops.qc.domain.gateway.metadata.MetaDataService;
import com.zto.devops.qc.domain.gateway.noticing.ReactiveEmitterService;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.*;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.util.JmxParseUtil;
import com.zto.devops.qc.domain.gateway.util.JmxUtil;
import com.zto.devops.qc.domain.gateway.zbase.ZbaseService;
import com.zto.devops.qc.domain.gateway.zms.ZMSTemplateService;
import com.zto.devops.qc.domain.util.DocInvokeDubboRequestBuilder;
import com.zto.devops.qc.domain.util.DocInvokeHttpRequestBuilder;
import com.zto.devops.qc.domain.util.GenerateApiCaseUtil;
import com.zto.titans.common.util.BeanUtil;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.dom4j.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.net.URL;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApiTestCommandDomainService extends BaseDomainService {

    @Autowired
    private ApiTestRepository apiTestRepository;

    @Autowired
    private ApiTestDomainConverter convert;

    @Autowired
    private AuthCookieDomainService authCookieDomainService;

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private IPipelineRpcService pipelineRpcService;

    @Autowired
    private MetaDataService metaDataService;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private AutomaticTaskDomainConverter automaticTaskDomainConverter;

    @Autowired
    private IAutomaticTaskRepository automaticTaskRepository;

    @Autowired
    private TestcaseRepository testcaseRepository;

    @Autowired
    private ApiTestDocDataService apiTestDocDataService;

    @Autowired
    private IProductRpcService iProductRpcService;

    @Autowired
    private ZtoOssService ztoOssService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private LinkMapRepository linkMapRepository;

    @Autowired
    private LinkMapDomainService linkMapDomainService;

    @Autowired
    private AutomaticSourceRecordRepository automaticSourceRecordRepository;

    @Autowired
    private AutomaticSourceRecordCommandDomainService automaticSourceRecordCommandDomainService;

    @Autowired
    private AutomaticSourceRecordDomainConverter automaticSourceRecordDomainConverter;

    @Autowired
    private AutomaticTaskCommandDomainService automaticTaskCommandDomainService;

    @Autowired
    private TestcaseCommandDomainService testcaseCommandDomainService;

    @Autowired
    private ApiTestQueryDomainService apiTestQueryDomainService;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    @Autowired
    private JmxUtil jmxUtil;

    @Autowired
    private LockService lockService;

    @Autowired
    private TagRepository tagRepository;

    @Autowired
    private ZbaseService zbaseService;

    @Autowired
    private TmSceneDebugRecordRepository tmSceneDebugRecordRepository;

    @Autowired
    private ApiGlobalConfigurationRepository apiGlobalConfigurationRepository;

    @Autowired
    private IQcDebugService qcDebugService;

    @Autowired
    private TmSceneCollectionRepository tmSceneCollectionRepository;

    @Autowired
    private JmxParseUtil jmxParseUtil;

    @Autowired
    private ReactiveEmitterService reactiveEmitter;

    @Autowired
    private ZMSTemplateService zmsTemplate;

    private static final String CASE_EXCEL_PATH = "apitest/";

    private static final String DEVOPS_WS_MESSAGE_MQ = "devops_ws_message_MQ";

    private static final int TAG_COUNT = 20;

    private final Executor SYNC_API_TEST_EXECUTOR = new ThreadPoolExecutor(
            50,
            200,
            30L,
            TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            ThreadFactoryBuilder.create().setNamePrefix("Sync-ApiTest-").build());

    private final Executor AUTO_SOURCE_EXECUTOR = new ThreadPoolExecutor(
            10,
            50,
            10L,
            TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            ThreadFactoryBuilder.create().setNamePrefix("EXECUTOR-Automatic-").build());

    private final Executor COPY_MODULE_EXECUTOR = new ThreadPoolExecutor(
            10,
            50,
            10L,
            TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            ThreadFactoryBuilder.create().setNamePrefix("EXECUTOR-CopyModule-").build());

    private final Executor ADD_SUB_API_CASE_EXECUTOR = new ThreadPoolExecutor(
            10,
            50,
            10L,
            TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            ThreadFactoryBuilder.create().setNamePrefix("Add-Sub-ApiCase-").build());

    private final Executor BATCH_PUBLISH_API_CASE_EXECUTOR = new ThreadPoolExecutor(
            10,
            50,
            10L,
            TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            ThreadFactoryBuilder.create().setNamePrefix("EXECUTOR-PublishApiCase-").build());

    private final Executor MODIFY_OSS_JMETER_FILE__EXECUTOR = new ThreadPoolExecutor(
            10,
            50,
            5L,
            TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            ThreadFactoryBuilder.create().setNamePrefix("EXECUTOR-ModifyOssJmeterFile-").build());

    public void addApiTestVariable(AddApiTestVariableCommand command) {
        log.info("addApiTestVariable >>> {}", JsonUtil.toJSON(command));
        AddApiTestVariableEvent event = convert.convert(command);
        SimpleQueryVO productVO = productRpcService.getProductVO(command.getProductCode());
        if(null != productVO) {
            event.setProductName(productVO.getProductName());
        }
        ApiTestVariableVO apiTestVariableVO = apiTestRepository.getUniqueApiTestVariable(event.getVariableCode(), event.getProductCode(), event.getLinkCode(), event.getVariableKey());
        if (null != apiTestVariableVO) {
            throw new ServiceException("变量名称已存在。");
        }
        String encodedString = Base64.getEncoder().encodeToString(event.getVariableValue().getBytes());
        event.setVariableValue(encodedString);
        event.setLoginValidTime(event.getLoginValidTime() != null ? event.getLoginValidTime() : 1800);
        apiTestRepository.addApiTestVariable(event);
        apply(event);
    }

    public void editApiTestVariable(EditApiTestVariableCommand command) {
        log.info("editApiTestVariable >>> {}", JsonUtil.toJSON(command));
        EditApiTestVariableEvent event = convert.convert(command);
        ApiTestVariableVO apiTestVariableVO = apiTestRepository.getApiTestVariableByVariableCode(event.getVariableCode());
        if (null == apiTestVariableVO) {
            throw new ServiceException("变量不存在。");
        }
        ApiTestVariableVO uniqueApiTestVariable = apiTestRepository.getUniqueApiTestVariable(event.getVariableCode(), event.getProductCode(), event.getLinkCode(), event.getVariableKey());
        if (null != uniqueApiTestVariable) {
            throw new ServiceException("变量名称已存在。");
        }
        String encodedString = Base64.getEncoder().encodeToString(event.getVariableValue().getBytes());
        event.setVariableValue(encodedString);
        event.setLoginValidTime(event.getLoginValidTime() != null ? event.getLoginValidTime() : 1800);
        apiTestRepository.editApiTestVariable(event);
        event.setAggregateId(command.getAggregateId());
        apply(event);
    }

    public void updateApiTestVariableStatus(UpdateApiTestVariableStatusCommand command) {
        log.info("updateApiTestVariableEnable >>> {}", JsonUtil.toJSON(command));
        UpdateApiTestVariableStatusEvent event = convert.convert(command);
        ApiTestVariableVO apiTestVariableVO = apiTestRepository.getApiTestVariableByVariableCode(event.getVariableCode());
        if (null == apiTestVariableVO) {
            throw new ServiceException("变量不存在。");
        }
        apiTestRepository.updateApiTestVariableStatus(event);

        ApiCaseEntityDO entityDO = new ApiCaseEntityDO();
        entityDO.setUserVariableCode(event.getVariableCode());
        entityDO.preUpdate(event);
        apiTestRepository.updateApiCaseUserByVariableCode(entityDO);
        event.setAggregateId(command.getAggregateId());
        apply(event);
    }

    public void deleteApiTestVariable(DeleteApiTestVariableCommand command) {
        log.info("deleteApiTestVariable >>> {}", JsonUtil.toJSON(command));
        DeleteApiTestVariableEvent event = convert.convert(command);
        ApiTestVariableVO apiTestVariableVO = apiTestRepository.getApiTestVariableByVariableCode(event.getVariableCode());
        if (null == apiTestVariableVO) {
            throw new ServiceException("变量不存在。");
        }
        apiTestRepository.deleteApiTestVariable(event);
        ApiCaseEntityDO entityDO = new ApiCaseEntityDO();
        entityDO.setUserVariableCode(event.getVariableCode());
        entityDO.preUpdate(event);
        apiTestRepository.updateApiCaseUserByVariableCode(entityDO);
        event.setAggregateId(command.getAggregateId());
        apply(event);
    }

    public Map<String, String> getApiTestVariableResult(GetApiTestVariableResultReq req) {
        log.info("getApiTestVariableResult >>> {}", JsonUtil.toJSON(req));
        ApiTestVariableVO apiTestVariableVO = apiTestRepository.getUniqueApiTestVariable("", req.getProductCode(), req.getLinkCode(), req.getVariableKey());
        if (null == apiTestVariableVO) {
            throw new ServiceException("变量信息不存在");
        }
        log.info("开始获取cookie：{}", JsonUtil.toJSON(apiTestVariableVO));
        Map<String, String> cookieMap = authCookieDomainService.getDataToLogin(apiTestVariableVO, req.getNameSpace(), req.getProductCode());
        return cookieMap;
    }

    public void syncApiMetaData(SyncApiTestDocReq req) {
        SYNC_API_TEST_EXECUTOR.execute(() -> {
            log.info("syncApiMetaData >>> {}", req);
            List<String> productCodeList = new ArrayList<>();
            if (StringUtils.isNotEmpty(req.getProductCode())) {
                productCodeList.add(req.getProductCode());
            } else {
                List<PipelineProductVO> productList = productRpcService.getAllOnlineProduct();
                if (CollectionUtils.isEmpty(productList)) {
                    log.warn("获取一站式线上产品失败！");
                    return;
                }
                productList.stream().map(PipelineProductVO::getCode).forEach(productCodeList::add);
            }
            List<ApiTypeEnum> typeList = new ArrayList<>();
            if (null != req.getType()) {
                typeList.add(req.getType());
            } else {
                typeList.add(ApiTypeEnum.HTTP);
                typeList.add(ApiTypeEnum.DUBBO);
            }
            productCodeList.forEach(productCode -> apiTestDocDataService.syncApiMetaDataByProduct(productCode, typeList));
        });
    }

    public void executeApiTest(ExecuteApiTestCommand command) {
        log.info("ExecuteApiTestCommand >>> {}", JSON.toJSONString(command));
        if (CollectionUtils.isEmpty(command.getApiCodes())) {
            throw new ServiceException("勾选的api接口不存在或接口状态不支持执行！");
        }

        //接口列表
        List<ApiTestEntityDO> apiDOList = apiTestRepository.queryApiDetailByMainCodeListAndEnable(
                command.getApiCodes(), Arrays.asList(ApiTestEnableEnum.ONLINE, ApiTestEnableEnum.UNKNOWN));
        if (CollectionUtils.isEmpty(apiDOList)) {
            log.error("勾选的api接口不存在或接口状态不支持执行！apiCodes：{}", JSON.toJSONString(command.getApiCodes()));
            throw new ServiceException("勾选的api接口不存在或接口状态不支持执行！");
        }

        //用例列表
        List<ApiTestCaseEntityDO> caseDOList = apiTestRepository.queryApiCaseDataByApiCode(command.getApiCodes());
        if (CollectionUtils.isEmpty(caseDOList)) {
            log.error("勾选的api接口下不存在用例！apiCodes：{}", JSON.toJSONString(command.getApiCodes()));
            throw new ServiceException("勾选的api接口下不存在用例！");
        }
        command.setCaseDOList(caseDOList);

        //执行
        SYNC_API_TEST_EXECUTOR.execute(() -> this.execute(command));
    }

    public void executeApiCase(ExecuteApiCaseCommand command) {
        log.info("ExecuteApiCaseCommand >>> {}", JSON.toJSONString(command));
        if (CollectionUtils.isEmpty(command.getCaseCodes())) {
            throw new ServiceException("用例不存在或用例无api接口！");
        }

        List<ApiTestCaseEntityDO> caseDOList = apiTestRepository.queryApiCaseDataByParentCode(command.getCaseCodes());
        if (CollectionUtils.isEmpty(caseDOList)) {
            log.error("用例不存在或用例无api接口，caseCodes：{}", command.getCaseCodes());
            throw new ServiceException("用例不存在或用例无api接口！");
        }
        command.setCaseDOList(caseDOList);

        SYNC_API_TEST_EXECUTOR.execute(() -> this.execute(command));
    }

    private void execute(ExecuteApiBaseCommand command) {
        String taskId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        log.info("执行单接口任务：{}", taskId);
        command.getCaseDOList().stream()
                .collect(Collectors.groupingBy(ApiTestCaseEntityDO::getApiCode))
                .forEach((apiCode, apiCaseList) -> apiCaseList.stream()
                        .collect(Collectors.groupingBy(ApiTestCaseEntityDO::getParentCaseCode))
                        .forEach((parentCaseCode, caseDataList) -> {
                            String code = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
                            ExecuteAutomaticTaskCommand taskCommand = new ExecuteAutomaticTaskCommand(code);
                            taskCommand.setTaskId(taskId);
                            taskCommand.setAutomaticSourceCode(parentCaseCode);
                            List<String> caseCodeList = caseDataList.stream().map(ApiTestCaseEntityDO::getCaseCode).collect(Collectors.toList());
                            taskCommand.setTestcaseCodeList(caseCodeList);
                            taskCommand.setTrigMode(command.getTrigMode());
                            taskCommand.setEnv(command.getEnv());
                            taskCommand.setTransactor(command.getTransactor());

                            AutomaticTaskExecutedEvent event = automaticTaskDomainConverter.converter(taskCommand);
                            event.setProductCode(command.getProductCode());
                            // set执行环境
                            setAutoEnv(command, event);
                            // 获取产品名称
                            setProductName(event);
                            // 任务task表插数据
                            addExecuteApiTestTask(command, event);
                            // 记录表插数据
                            addExecuteApiTestRecord(event);
                            // 更新父用例状态
                            updateParentCaseExecuteResultByTaskCode(event.getAggregateId());
                            // 提交任务
                            submitApiTestTask(parentCaseCode, event.getEnv(), command.getEnvName(), caseDataList, event.getCode());
                        }));
    }

    public void submitApiTestTask(
            String parentCaseCode,
            String ztoenv,
            String envName,
            List<ApiTestCaseEntityDO> caseDataList,
            String taskCode) {
        ApiDebugReq apiDebugReq = new ApiDebugReq();
        apiDebugReq.setCaseCode(parentCaseCode);
        apiDebugReq.setZtoenv(ztoenv);
        apiDebugReq.setEnvName(envName);
        try {
            boolean result = linkMapDomainService.executeApi(apiDebugReq, taskCode, caseDataList);
            if (result) {
                AutomaticTaskEntityDO entityDO = new AutomaticTaskEntityDO();
                entityDO.setCode(taskCode);
                entityDO.setStatus(AutomaticStatusEnum.IN_PROGRESS);
                automaticTaskRepository.updateByAutomaticTaskEntityDO(entityDO);
                automaticTaskRepository.updateResultByAutomaticTaskCode(taskCode, TestPlanCaseStatusEnum.IN_PROGRESS);
            }
        } catch (Exception e) {
            log.error("单接口用例执行失败！{} {}", JSON.toJSONString(apiDebugReq), taskCode, e);
            // 终止任务
            abortExecuteApiTestTask(taskCode);
        }
    }

    private void abortExecuteApiTestTask(String taskCode) {
        AutomaticTaskEntityDO entityDO = new AutomaticTaskEntityDO();
        entityDO.setCode(taskCode);
        entityDO.setStatus(AutomaticStatusEnum.TERMINATION);
        entityDO.setFinishTime(new Date());
        entityDO.setModifierId(0L);
        entityDO.setModifier("cookieFailAbort");
        entityDO.setGmtModified(new Date());
        automaticTaskRepository.updateByAutomaticTaskEntityDO(entityDO);
        automaticTaskRepository.updateResultByAutomaticTaskCode(taskCode, TestPlanCaseStatusEnum.TERMINATION);
    }

    private void setAutoEnv(ExecuteApiBaseCommand command, AutomaticTaskExecutedEvent event) {
        NamespaceResp baseName = null;
        try {
            baseName = pipelineRpcService.findBaseNamespace(event.getProductCode());
        } catch (Exception e) {
            log.info("查询发布域异常{}", e.toString());
        }
        NamespaceResp finalBaseName = baseName;
        if (command.getAutoEnv() && !command.getIsDeploy()) {
            if (finalBaseName == null) {
                throw new ServiceException("未找到动态多环境base空间");
            }
            event.setEnv(finalBaseName.getTag());//用于执行
            command.setEnvName(finalBaseName.getName());//用于回显
        }
    }

    private void setProductName(AutomaticTaskExecutedEvent event) {
        SimpleQueryVO product = iProductRpcService.getProductVO(event.getProductCode());
        if (null != product) {
            event.setProductName(product.getProductName());
        }
    }

    public void addExecuteApiTestTask(ExecuteApiBaseCommand command, AutomaticTaskExecutedEvent event) {
        AutomaticTaskEntityDO taskEntityDO = automaticTaskDomainConverter.converter(event);
        taskEntityDO.setProductCode(event.getProductCode());
        taskEntityDO.setType(AutomaticRecordTypeEnum.JMETER);
        taskEntityDO.setStatus(AutomaticStatusEnum.NOT_STARTED);
        taskEntityDO.setExecuteMode(AutomaticExecuteModeEnum.API);
        taskEntityDO.setStartTime(new Date());
        taskEntityDO.preCreate(event);
        taskEntityDO.setEnv(command.getEnvName());
        taskEntityDO.setExecuteTag(event.getEnv());
        // tm_automatic_task添加记录
        automaticTaskRepository.insertSelective(taskEntityDO);
    }

    private void addExecuteApiTestRecord(AutomaticTaskExecutedEvent event) {
        TestcaseExecuteRecordEntityDO executeRecordEntityDO = new TestcaseExecuteRecordEntityDO();
        executeRecordEntityDO.setResult(TestPlanCaseStatusEnum.NOT_STARTED);
        executeRecordEntityDO.setAutomaticTaskCode(event.getAggregateId());
        executeRecordEntityDO.setStartTime(new Date());
        executeRecordEntityDO.preCreate(event);
        List<List<String>> partition = Lists.partition(event.getTestcaseCodeList(), 200);
        for (List<String> list : partition) {
            testcaseRepository.insertBatchExecuteRecord(executeRecordEntityDO, list);
        }
    }

    public Boolean executeCallBack(ExecuteApiCallBackCommand command) {
        TestcaseExecuteRecordEntityDO entityDO = new TestcaseExecuteRecordEntityDO();
        entityDO.setTestcaseCode(command.getCaseCode());
        entityDO.setAutomaticTaskCode(command.getTaskCode());
        entityDO.setResult(command.getResult());
        entityDO.setResultFile(command.getPath());
        entityDO.setFinishTime(new Date());
        automaticTaskRepository.updateTestcaseStatus(entityDO, command.getCaseCode(), command.getTaskCode());

        //更新父用例结果
        updateParentCaseExecuteResult(command.getCaseCode(), command.getTaskCode());

        // 查询当前是否已经终止, 若终止，返回false
        AutomaticTaskEntityDO taskDO = automaticTaskRepository.getAutomaticTaskByCode(command.getTaskCode());
        if (null != taskDO) {
            return !AutomaticStatusEnum.TERMINATION.equals(taskDO.getStatus());
        }
        return true;
    }

    /**
     * 更新父用例结果
     *
     * @param caseCode 用例code
     * @param taskCode 任务code
     */
    private void updateParentCaseExecuteResult(String caseCode, String taskCode) {
        //用例
        List<ApiTestCaseEntityDO> doList = apiTestRepository.getApiCaseByCodes(Collections.singletonList(caseCode));
        if (CollectionUtils.isEmpty(doList)) {
            return;
        }

        //当前任务执行结果
        String parentCode = doList.get(0).getParentCaseCode();
        String executeResult = apiTestRepository.queryExecuteResultByParentCodeAndTaskCode(parentCode, taskCode);

        //更新父用例状态
        apiTestRepository.updateApiCaseExecuteResult(parentCode, taskCode, TestPlanCaseStatusEnum.getApiCaseExecuteResult(executeResult));
    }

    /**
     * 更新父用例执行结果
     *
     * @param taskCode 任务code
     */
    public void updateParentCaseExecuteResultByTaskCode(String taskCode) {
        List<StatApiCaseExecuteResultVO> voList = apiTestRepository.statApiCaseExecuteResult(taskCode);
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        voList.forEach(vo -> {
            apiTestRepository.updateApiCaseExecuteResult(vo.getParentCode(), taskCode,
                    TestPlanCaseStatusEnum.getApiCaseExecuteResult(vo.getExecuteResultMerge()));
        });
    }

    public void updateApiCaseUserCommand(UpdateApiCaseUserCommand command) {
        log.info("updateApiCaseUserCommand >>> {}", JSON.toJSONString(command));
        ApiCaseEntityDO apiCaseEntityDO = apiTestRepository.queryApiCaseByCode(command.getAggregateId());
        if (null == apiCaseEntityDO || ApiCaseEnableEnum.DELETED.equals(apiCaseEntityDO.getEnable())) {
            log.warn("接口用例不存在或已删除 {}", command.getAggregateId());
            return;
        }
        apiCaseEntityDO.setUserVariableCode(command.getUserVariableCode());
        apiCaseEntityDO.preUpdate(command);
        apiTestRepository.updateApiCaseSelective(apiCaseEntityDO);
        if (ApiCaseSourceTypeEnum.SYSTEM.equals(apiCaseEntityDO.getSourceType())) {
            apiTestRepository.updateApiCaseUserByParent(command);
        }
    }

    /**
     * 出入参变化或者造数状态变化 ==>> 变更场景下的造数节点标识
     *
     * @param preDataCode
     * @param tag
     * @param preDataMarkEnum
     */
    private void syncSceneByPreData(String preDataCode, SceneTagEnum tag, PreDataMarkEnum preDataMarkEnum) {
        List<SceneInfoEntityDO> sceneList = apiTestRepository.querySceneByProductCodeAndSceneType(null, UseCaseFactoryTypeEnum.SCENE.getCode());
        if (CollectionUtils.isEmpty(sceneList)) {
            return;
        }
        for (SceneInfoEntityDO scene : sceneList) {
            SceneInfoEntityDO edit = apiTestRepository.queryLatestSceneInfo(scene.getSceneCode(), SceneInfoStatusEnum.edit);
            boolean preDataUpdated = checkPreDataNodeUpdatedMark(edit, preDataCode, preDataMarkEnum);
            SceneInfoEntityDO publish = apiTestRepository.queryLatestSceneInfo(scene.getSceneCode(), SceneInfoStatusEnum.publish);
            preDataUpdated = checkPreDataNodeUpdatedMark(publish, preDataCode, preDataMarkEnum) || preDataUpdated;
            if ("UPDATE".equals(preDataMarkEnum.name()) && preDataUpdated) {
                tagRepository.saveSceneTag(scene.getSceneCode(), tag);
            }
        }
    }

    /**
     * 删除所有场景下的造数节点标识
     *
     * @param preDataCode
     * @param preDataMarkEnum
     */
    private void syncDeleteSceneByPreData(String preDataCode, PreDataMarkEnum preDataMarkEnum) {
        List<SceneInfoEntityDO> sceneList = apiTestRepository.querySceneByProductCodeAndSceneType(null, UseCaseFactoryTypeEnum.SCENE.getCode());
        if (CollectionUtils.isEmpty(sceneList)) {
            return;
        }
        for (SceneInfoEntityDO scene : sceneList) {
            SceneInfoEntityDO edit = apiTestRepository.queryLatestSceneInfo(scene.getSceneCode(), SceneInfoStatusEnum.edit);
            deletePreDataNodeUpdatedMark(edit, preDataCode, preDataMarkEnum);
            SceneInfoEntityDO publish = apiTestRepository.queryLatestSceneInfo(scene.getSceneCode(), SceneInfoStatusEnum.publish);
            deletePreDataNodeUpdatedMark(publish, preDataCode, preDataMarkEnum);
        }
    }

    /**
     * 单个场景下的造数据节点变更标识
     *
     * @param sceneInfo
     * @param preDataCode
     * @param preDataMarkEnum
     * @return
     */
    private boolean checkPreDataNodeUpdatedMark(SceneInfoEntityDO sceneInfo, String preDataCode, PreDataMarkEnum preDataMarkEnum) {
        JSONObject jsonObject = Optional.ofNullable(sceneInfo).map(SceneInfoEntityDO::getSceneBackData).map(JSONObject::parseObject).orElse(null);
        if (null == jsonObject) {
            return false;
        }
        JSONArray updateTypeJson = jsonObject.getJSONArray("updateTypeList");

        Set<String> allPreDataNodes = new HashSet<>();
        jsonObject.getJSONObject("nodes").keySet().forEach(key -> {
            JSONObject node = jsonObject.getJSONObject("nodes").getJSONObject(key);
            if (node == null) {
                return;
            }
            String type = node.getJSONObject("sampler").getString("type");
            String sceneCode = node.getJSONObject("sampler").getString("dataCenterSceneCode");
            if (LinkMapTypeEnum.DataCenter_REQUEST_COMPONENT.name().equals(type) && preDataCode.equals(sceneCode)) {
                allPreDataNodes.add(key);
            }
        });
        List<Map<String, String>> updateTypeList = getNewUpdateType(updateTypeJson, allPreDataNodes, preDataMarkEnum);
        boolean apiUpdated = !updateTypeList.isEmpty();
        if (apiUpdated) {
            jsonObject.put("updateTypeList", updateTypeList);
            SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
            entityDO.setId(sceneInfo.getId());
            entityDO.setSceneBackData(jsonObject.toJSONString());
            entityDO.setGmtModified(sceneInfo.getGmtModified());
            apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
        }
        return apiUpdated;
    }

    private List<Map<String, String>> getNewUpdateType(JSONArray updateTypeJson, Set<String> preDataNodeCodes, PreDataMarkEnum preDataMarkEnum) {
        List<Map<String, String>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(preDataNodeCodes)) {
            return Collections.emptyList();
        }
        if (null == updateTypeJson || updateTypeJson.size() == 0) {
            for (String node : preDataNodeCodes) {
                Map<String, String> newMap = new HashMap<>();
                newMap.put("node", node);
                newMap.put("enable", preDataMarkEnum.name());
                result.add(newMap);
            }
            return result;
        }
        List<Map<String, String>> listMap = (List<Map<String, String>>) JSONArray.parse(updateTypeJson.toString());
        Iterator<String> it_node = preDataNodeCodes.iterator();
        while (it_node.hasNext()) {
            String node = it_node.next();
            Iterator<Map<String, String>> it_map = listMap.iterator();
            while (it_map.hasNext()) {
                Map<String, String> oldMap = it_map.next();
                Map<String, String> newMap = new HashMap<>();
                if (oldMap.get("node").equals(node)) {
                    String enable = oldMap.get("enable");
                    Integer oldType = PreDataMarkEnum.getValueByName(enable);
                    Integer newType = PreDataMarkEnum.getValueByName(preDataMarkEnum.name());
                    if (null == oldType || null == newType) {
                        continue;
                    }
                    if (oldType > newType) {
                        newMap.put("node", node);
                        newMap.put("enable", preDataMarkEnum.name());
                        result.add(newMap);
                    } else {
                        result.add(oldMap);
                    }
                    it_map.remove();
                    it_node.remove();
                }
            }
        }

        if (CollectionUtil.isNotEmpty(listMap)) {
            result.addAll(listMap);
        }
        if (CollectionUtil.isNotEmpty(preDataNodeCodes)) {
            for (String node : preDataNodeCodes) {
                Map<String, String> newMap = new HashMap<>();
                newMap.put("node", node);
                newMap.put("enable", preDataMarkEnum.name());
                result.add(newMap);
            }
        }
        return result;
    }

    /**
     * 删除当场景下的造数节点标识 (编辑/发布)
     *
     * @param sceneInfo
     * @param preDataCode
     * @param preDataMarkEnum
     * @return
     */
    private void deletePreDataNodeUpdatedMark(SceneInfoEntityDO sceneInfo, String preDataCode, PreDataMarkEnum preDataMarkEnum) {
        JSONObject jsonObject = Optional.ofNullable(sceneInfo).map(SceneInfoEntityDO::getSceneBackData).map(JSONObject::parseObject).orElse(null);
        if (null == jsonObject) {
            return;
        }
        JSONArray updateTypeJson = jsonObject.getJSONArray("updateTypeList");
        if (null == updateTypeJson || updateTypeJson.size() == 0) {
            return;
        }
        jsonObject.getJSONObject("nodes").keySet().forEach(key -> {
            JSONObject node = jsonObject.getJSONObject("nodes").getJSONObject(key);
            if (node == null) {
                return;
            }
            String type = node.getJSONObject("sampler").getString("type");
            String sceneCode = node.getJSONObject("sampler").getString("dataCenterSceneCode");
            if (LinkMapTypeEnum.DataCenter_REQUEST_COMPONENT.name().equals(type) && preDataCode.equals(sceneCode)) {
                deleteNewUpdateType(updateTypeJson, key, preDataMarkEnum);
            }
        });
        jsonObject.put("updateTypeList", updateTypeJson);
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setId(sceneInfo.getId());
        entityDO.setSceneBackData(jsonObject.toJSONString());
        entityDO.setGmtModified(sceneInfo.getGmtModified());
        apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
    }

    /**
     * 删除标识json
     *
     * @param updateTypeJson
     * @param preDataNodeCode
     * @param preDataMarkEnum
     * @return
     */
    private void deleteNewUpdateType(JSONArray updateTypeJson, String preDataNodeCode, PreDataMarkEnum preDataMarkEnum) {
        List<Map<String, String>> listMap = (List<Map<String, String>>) JSONArray.parse(updateTypeJson.toString());
        for (Map<String, String> oldMap : listMap) {
            if (null == oldMap) {
                continue;
            }
            if (oldMap.get("enable").equals(preDataMarkEnum.name())) {
                if (StringUtils.isEmpty(preDataNodeCode) || oldMap.get("node").equals(preDataNodeCode)) {
                    //空或者节点相同移除
                    updateTypeJson.remove(oldMap);
                }
            }
        }
    }

    public void addSceneBasicInfoCommand(EditSceneInfoCommand command) {
        log.info("addSceneBasicInfoCommand >>> {}", JSON.toJSONString(command));
        checkSceneNameMethod(command.getSceneType(), command.getSceneName(), command.getProductCode(), null, command.getParentCode());
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setSceneCode(command.getAggregateId());
        entityDO.setProductCode(command.getProductCode());
        entityDO.setSceneName(command.getSceneName());
        entityDO.setSceneInfoDesc(command.getSceneInfoDesc());
        entityDO.setStatus(SceneInfoStatusEnum.edit);
        entityDO.setSceneVersion(1);
        entityDO.setSceneOssPath(UUID.randomUUID().toString(true));
        entityDO.setSceneOssFile("/V1.json");
        entityDO.setSceneFrontData(command.getSceneFrontData());
        entityDO.setSceneBackData(command.getSceneBackData());
        entityDO.setEnable(SceneInfoEnableEnum.UNPUBLISHED);
        entityDO.preCreate(command);
        if (StringUtils.isNotBlank(command.getSceneType()) && command.getSceneType().equals(UseCaseFactoryTypeEnum.CREATE.name())) {
            entityDO.setSceneType(UseCaseFactoryTypeEnum.CREATE.getCode());
        } else {
            entityDO.setSceneType(UseCaseFactoryTypeEnum.SCENE.getCode());
        }
        apiTestRepository.insertSceneInfoSelective(entityDO);
        // 场景插入索引表
        insertSceneIndex(command);
    }

    private void checkSceneNameMethod(String sceneType, String sceneName, String productCode, String sceneCode, String parentCode) {
        sceneType = StringUtils.isBlank(sceneType) ? UseCaseFactoryTypeEnum.SCENE.name() : sceneType;
        String key = sceneType + "_" + productCode + "_" + parentCode + "_" + sceneName;
        log.info("checkSceneNameMethod_redis_key_{}_value_in :{}", key, redisService.getKey(key));
        if (redisService.hasKey(key)) {
            if (StringUtils.isBlank(sceneCode) || !sceneCode.equals(redisService.getKey(key))) {
                throw new ServiceException(sceneType.equals(UseCaseFactoryTypeEnum.CREATE.name()) ? "造数名称已存在！" : "场景名称已存在！");
            }
        } else {
            redisService.setKey(key, (StringUtils.isBlank(sceneCode) ? key : sceneCode), 5L, TimeUnit.SECONDS);
            log.info("checkSceneNameMethod_redis_key_{}_value_set :{}", key, redisService.getKey(key));
        }
        if (StringUtils.isNotBlank(sceneType) && sceneType.equals(UseCaseFactoryTypeEnum.CREATE.name())) {
            Boolean checkNameExist =
                    apiTestRepository.checkSceneName(sceneName, productCode, sceneCode, UseCaseFactoryTypeEnum.CREATE.getCode());
            if (checkNameExist) {
                throw new ServiceException("造数名称已存在！");
            }
        } else {
            Boolean checkNameExist =
                    apiTestRepository.checkSceneName(sceneName, productCode, sceneCode, UseCaseFactoryTypeEnum.SCENE.getCode());
            if (checkNameExist) {
                throw new ServiceException("场景名称已存在！");
            }
        }
    }

    private void insertSceneIndex(EditSceneInfoCommand command) {
        SceneIndexVO indexVO = new SceneIndexVO();
        indexVO.setSceneIndexCode(command.getAggregateId());
        indexVO.setSceneIndexName(command.getSceneName());
        indexVO.setSceneIndexType(SceneIndexTypeEnum.SCENE);
        indexVO.setProductCode(command.getProductCode());
        indexVO.setParentCode(command.getParentCode());
        indexVO.setEnable(true);
        indexVO.setCreatorId(command.getTransactor().getUserId());
        indexVO.setCreator(command.getTransactor().getUserName());
        indexVO.setGmtCreate(new Date());
        indexVO.setModifierId(command.getTransactor().getUserId());
        indexVO.setModifier(command.getTransactor().getUserName());
        indexVO.setGmtModified(new Date());
        if (StringUtils.isNotBlank(command.getSceneType()) && command.getSceneType().equals(UseCaseFactoryTypeEnum.CREATE.name())) {
            indexVO.setSceneType(UseCaseFactoryTypeEnum.CREATE.getCode());
        } else {
            indexVO.setSceneType(UseCaseFactoryTypeEnum.SCENE.getCode());
        }
        apiTestRepository.insertSceneIndexSelective(indexVO);
    }

    public void editSceneBasicInfoCommand(EditSceneInfoCommand command) {
        log.info("editSceneBasicInfoCommand >>> {}", JSON.toJSONString(command));
        checkSceneNameMethod(command.getSceneType(), command.getSceneName(), command.getProductCode(), command.getAggregateId(), command.getParentCode());
        // 修改场景图信息
        updateSceneBasicInfo(command);
        // 修改登记库名字
        updateAutoSourceName(command);
        // 修改分组
        SceneIndexVO moveVo = apiTestRepository.querySceneIndexByCode(command.getAggregateId());
        if (!moveVo.getParentCode().equals(command.getParentCode())) {
            moveSceneModule(convert.convert(command));
        }
    }

    public void editPreDataBasicInfoCommand(EditPreDataInfoCommand command) {
        log.info("editSceneBasicInfoCommand >>> {}", JSON.toJSONString(command));
        Boolean checkNameExist = apiTestRepository.checkSceneName(command.getSceneName(), command.getProductCode(), command.getAggregateId(), UseCaseFactoryTypeEnum.CREATE.getCode());
        if (checkNameExist) {
            throw new ServiceException("造数名称已存在！");
        }
        // 修改造数信息
        updateSceneBasicInfo(command);
        // 修改分组
        SceneIndexVO moveVo = apiTestRepository.querySceneIndexByCode(command.getAggregateId());
        if (!moveVo.getParentCode().equals(command.getParentCode())) {
            movePreDataModule(convert.convert(command));
        }
    }

    private void updateAutoSourceName(EditSceneInfoCommand command) {
        // 修改登记库名字 - 草稿
        updateAutoSourceByStatus(command, SceneInfoStatusEnum.edit);
        // 修改登记库名字 - 发布
        updateAutoSourceByStatus(command, SceneInfoStatusEnum.publish);
    }

    private void updateAutoSourceByStatus(EditSceneInfoCommand command, SceneInfoStatusEnum statusEnum) {
        SceneInfoEntityDO sceneInfo = apiTestRepository.queryLatestSceneInfo(command.getAggregateId(), statusEnum);
        if (null != sceneInfo && StringUtils.isNotBlank(sceneInfo.getAutomaticSourceCode())) {
            String name = command.getSceneName() + "-" + statusEnum.getDesc();
            AutomaticSourceRecordEntityDO entityDO = new AutomaticSourceRecordEntityDO();
            entityDO.setCode(sceneInfo.getAutomaticSourceCode());
            entityDO.setName(name);
            entityDO.preUpdate(command);
            automaticSourceRecordRepository.update(entityDO);
        }
    }

    private void updateSceneBasicInfo(EditSceneInfoCommand command) {
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setSceneCode(command.getAggregateId());
        entityDO.setSceneName(command.getSceneName());
        if (StringUtils.isEmpty(command.getSceneInfoDesc())) {
            entityDO.setSceneInfoDesc("");
        } else {
            entityDO.setSceneInfoDesc(command.getSceneInfoDesc());
        }
        entityDO.preUpdate(command);
        apiTestRepository.updateSceneInfoByCode(entityDO);
    }

    public EditSceneBasicInfoResp editSceneInfoCommand(EditSceneInfoCommand command) {
        log.info("editSceneInfoCommand >>> {}", JSON.toJSONString(command));
        SceneInfoEntityDO editSceneInfo =
                apiTestRepository.queryLatestSceneInfo(command.getAggregateId(), SceneInfoStatusEnum.edit);
        if (StringUtils.isNotEmpty(editSceneInfo.getStepRecord())) {
            JSONObject stepRecord = JSONObject.parseObject(editSceneInfo.getStepRecord());
            if (AutomaticStatusEnum.IN_PROGRESS.name().equals(stepRecord.getString("result"))) {
                throw new ServiceException("场景图正在生成中，请稍后再试！");
            }
        }
        rewriteUploadNodeList(command, editSceneInfo.getSceneBackData(), UseCaseFactoryTypeEnum.SCENE);
        refreshStepRecord(editSceneInfo, command.getSceneBackData());
        setModifyTag(command, editSceneInfo.getEnable(), SceneTagEnum.SCENE_MODIFY);
        // 判断是否修改串、并行，并修改oss脚本
        updateRunSetting(command, editSceneInfo);
        editSceneInfo.setSceneFrontData(command.getSceneFrontData());
        editSceneInfo.setSceneBackData(command.getSceneBackData());
        editSceneInfo.preUpdate(command);
        apiTestRepository.updateSceneInfoByPrimaryKey(editSceneInfo);
        saveDbConfig(command.getDbIds(), editSceneInfo.getProductCode(), editSceneInfo.getUser(), UseCaseFactoryTypeEnum.SCENE, "");
        EditSceneBasicInfoResp resp = new EditSceneBasicInfoResp();
        resp.setSceneCode(editSceneInfo.getSceneCode());
        resp.setSceneVersion(editSceneInfo.getSceneVersion().toString());
        return resp;
    }

    public EditPreDataBasicInfoResp editPreDataInfoCommand(EditPreDataCommand command) {
        log.info("editPreDataInfoCommand: {}", JSON.toJSONString(command));
        SceneInfoEntityDO editSceneInfo = apiTestRepository.queryLatestSceneInfo(command.getAggregateId(), SceneInfoStatusEnum.edit);
        if (StringUtils.isNotEmpty(editSceneInfo.getStepRecord())) {
            JSONObject stepRecord = JSONObject.parseObject(editSceneInfo.getStepRecord());
            if (AutomaticStatusEnum.IN_PROGRESS.name().equals(stepRecord.getString("result"))) {
                throw new ServiceException("造数正在生成中，请稍后再试！");
            }
        }
        rewriteUploadNodeList(command, editSceneInfo.getSceneBackData(), UseCaseFactoryTypeEnum.CREATE);
        refreshStepRecord(editSceneInfo, command.getSceneBackData());
        setModifyTag(command, editSceneInfo.getEnable(), SceneTagEnum.PRE_DATA_MODIFY);
        editSceneInfo.setSceneFrontData(command.getSceneFrontData());
        editSceneInfo.setSceneBackData(command.getSceneBackData());
        editSceneInfo.preUpdate(command);
        apiTestRepository.updateSceneInfoByPrimaryKey(editSceneInfo);
        saveDbConfig(command.getDbIds(), editSceneInfo.getProductCode(), editSceneInfo.getUser(), UseCaseFactoryTypeEnum.CREATE, "");
        return EditPreDataBasicInfoResp.buildSelf(editSceneInfo.getSceneCode(), String.valueOf(editSceneInfo.getSceneVersion()));
    }

    private void rewriteUploadNodeList(EditSceneInfoCommand command, String sceneBackData, UseCaseFactoryTypeEnum useCaseFactoryTypeEnum) {
        if (StringUtils.isEmpty(sceneBackData)) {
            return;
        }
        JSONArray updateTypeList = Optional.ofNullable(JSON.parseObject(sceneBackData))
                .map(json -> json.getJSONArray("updateTypeList"))
                .orElse(null);
        if (CollectionUtils.isNotEmpty(updateTypeList)) {
            JSONObject data = JSONObject.parseObject(command.getSceneBackData());
            data.put("updateTypeList", updateTypeList);
            command.setSceneBackData(data.toJSONString());
        }

        JSONArray updateNodeList = Optional.ofNullable(JSON.parseObject(sceneBackData))
                .map(json -> json.getJSONArray("updateNodeList"))
                .orElse(null);
        if (CollectionUtils.isNotEmpty(updateNodeList)) {
            JSONObject data = JSONObject.parseObject(command.getSceneBackData());
            data.put("updateNodeList", updateNodeList);
            command.setSceneBackData(data.toJSONString());
        }
    }

    private void refreshStepRecord(SceneInfoEntityDO sceneInfo, String sceneBackData) {
        if (!checkSceneInfoChanged(sceneInfo.getSceneBackData(), sceneBackData)) {
            sceneInfo.setStepRecord("");
        }
    }

    private void setModifyTag(EditSceneInfoCommand command, SceneInfoEnableEnum enable, SceneTagEnum tag) {
        if (!SceneInfoEnableEnum.PUBLISHED.equals(enable)) {
            return;
        }
        SYNC_API_TEST_EXECUTOR.execute(() -> {
            SceneInfoEntityDO publishSceneInfo =
                    apiTestRepository.queryLatestSceneInfo(command.getAggregateId(), SceneInfoStatusEnum.publish);
            if (checkSceneInfoChanged(publishSceneInfo.getSceneBackData(), command.getSceneBackData())) {
                tagRepository.removeSceneTag(publishSceneInfo.getSceneCode(), Collections.singletonList(tag));
            } else {
                tagRepository.saveSceneTag(publishSceneInfo.getSceneCode(), tag);
            }
        });
    }

    private void setModifySceneTag(EditSceneInfoCommand command, SceneInfoEnableEnum enable, SceneTagEnum tag) {
        if (!SceneInfoEnableEnum.PUBLISHED.equals(enable)) {
            return;
        }
        SYNC_API_TEST_EXECUTOR.execute(() -> {
            SceneInfoEntityDO publishSceneInfo =
                    apiTestRepository.queryLatestSceneInfo(command.getAggregateId(), SceneInfoStatusEnum.publish);
            if (checkSceneInfoChanged(publishSceneInfo.getSceneBackData(), command.getSceneBackData())) {
                return;
            }
            syncSceneByPreData(command.getAggregateId(), tag, PreDataMarkEnum.UPDATE);
            syncApiCaseByPreData(command.getAggregateId());
        });
    }

    private void setModifySceneTag(String sceneCode, SceneTagEnum tag) {
        SceneInfoEntityDO publishSceneInfo =
                apiTestRepository.queryLatestSceneInfo(sceneCode, SceneInfoStatusEnum.publish);
        if (null == publishSceneInfo) {
            return;
        }
        SYNC_API_TEST_EXECUTOR.execute(() -> {
            tagRepository.saveSceneTag(publishSceneInfo.getSceneCode(), tag);
        });
    }

    private boolean checkSceneInfoChanged(String oldData, String newData) {
        try {
            if (StringUtils.isEmpty(oldData)) {
                return false;
            }
            Map<String, Object> oldMap = new TreeMap<>(JSONObject.parseObject(oldData));
            Map<String, Object> newMap = new TreeMap<>(JSONObject.parseObject(newData));
            return newMap.equals(oldMap);
        } catch (Exception e) {
            log.error("checkSceneInfoChanged error", e);
            return false;
        }
    }

    public EditSceneBasicInfoResp publishSceneInfoCommand(EditSceneInfoCommand command) {
        log.info("publishSceneInfoCommand >>> {}", JSON.toJSONString(command));
        LockSeal lockSeal = lockService.acquireLock(LockStoreEnum.SCENE_GENERATE.getValue(),
                Collections.singletonList(command.getAggregateId()),
                30000L);
        if (null == lockSeal) {
            throw new ServiceException("场景图正在生成中，请稍后再试！");
        }
        SceneInfoEntityDO editSceneInfo =
                apiTestRepository.queryLatestSceneInfo(command.getAggregateId(), SceneInfoStatusEnum.edit);
        JSONObject stepRecord = new JSONObject();
        stepRecord.put("result", AutomaticStatusEnum.IN_PROGRESS.name());
        editSceneInfo.setStepRecord(stepRecord.toJSONString());
        editSceneInfo.preUpdate(command);
        apiTestRepository.updateSceneInfoByPrimaryKey(editSceneInfo);
        SYNC_API_TEST_EXECUTOR.execute(() -> {
            try {
                boolean result = executePublish(editSceneInfo);
                setPublishTag(result, editSceneInfo.getSceneCode(), UseCaseFactoryTypeEnum.SCENE);
            } finally {
                lockService.releaseLock(lockSeal);
            }
        });
        EditSceneBasicInfoResp resp = new EditSceneBasicInfoResp();
        resp.setSceneCode(editSceneInfo.getSceneCode());
        resp.setSceneVersion(editSceneInfo.getSceneVersion().toString());
        return resp;
    }

    private void setPublishTag(boolean result, String sceneCode, UseCaseFactoryTypeEnum type) {
        if (result) {
            List<SceneTagEnum> removeTags = Arrays.asList(
                    SceneTagEnum.API_UPDATE,
                    SceneTagEnum.SCENE_MODIFY,
                    SceneTagEnum.GENERATE_ERROR,
                    SceneTagEnum.PRE_DATA_MODIFY,
                    SceneTagEnum.PRE_DATA_UPDATE,
                    SceneTagEnum.PUBLISH_ERROR);
            tagRepository.removeSceneTag(sceneCode, removeTags);
        } else {
            if (UseCaseFactoryTypeEnum.SCENE.equals(type)) {
                tagRepository.saveSceneTag(sceneCode, SceneTagEnum.GENERATE_ERROR);
            } else {
                tagRepository.saveSceneTag(sceneCode, SceneTagEnum.PUBLISH_ERROR);
            }
        }
    }

    public EditSceneBasicInfoResp publishDataCenter(EditSceneInfoCommand command) {
        log.info("publishDataCenterCommand >>> {}", JSON.toJSONString(command));
        LockSeal lockSeal = lockService.acquireLock(LockStoreEnum.SCENE_GENERATE.getValue(),
                Collections.singletonList(command.getAggregateId()),
                30000L);
        if (null == lockSeal) {
            throw new ServiceException("造数正在生成中，请稍后再试！");
        }
        SceneInfoEntityDO editSceneInfo =
                apiTestRepository.queryLatestSceneInfo(command.getAggregateId(), SceneInfoStatusEnum.edit);
        SceneInfoEntityDO publishSceneInfo =
                apiTestRepository.queryLatestSceneInfo(command.getAggregateId(), SceneInfoStatusEnum.publish);
        JSONObject stepRecord = new JSONObject();
        stepRecord.put("result", AutomaticStatusEnum.IN_PROGRESS.name());
        editSceneInfo.setStepRecord(stepRecord.toJSONString());
        editSceneInfo.preUpdate(command);
        apiTestRepository.updateSceneInfoByPrimaryKey(editSceneInfo);
        SYNC_API_TEST_EXECUTOR.execute(() -> {
            boolean flag = false;
            try {
                if (null != publishSceneInfo) {
                    //对比上一次发布与当前即将发布的出参与入参
                    JSONArray publishInputParameter = getParameter(publishSceneInfo, "inputParameter");
                    JSONArray editInputParameter = getParameter(editSceneInfo, "inputParameter");
                    JSONArray publishOutputParameter = getParameter(publishSceneInfo, "outputParameter");
                    JSONArray editOutputParameter = getParameter(editSceneInfo, "outputParameter");
                    if (!comparePreDataParameter(Objects.requireNonNull(publishInputParameter), Objects.requireNonNull(editInputParameter))
                            || !comparePreDataParameter(Objects.requireNonNull(publishOutputParameter), Objects.requireNonNull(editOutputParameter))) {
                        flag = true;
                    }
                }
                boolean result = executePublish(editSceneInfo);
                setPublishTag(result, editSceneInfo.getSceneCode(), UseCaseFactoryTypeEnum.CREATE);
                if (result && flag) {
                    setModifySceneTag(command, editSceneInfo.getEnable(), SceneTagEnum.PRE_DATA_UPDATE);
                }
            } finally {
                lockService.releaseLock(lockSeal);
            }
        });
        EditSceneBasicInfoResp resp = new EditSceneBasicInfoResp();
        resp.setSceneCode(editSceneInfo.getSceneCode());
        resp.setSceneVersion(editSceneInfo.getSceneVersion().toString());
        return resp;
    }

    private JSONArray getParameter(SceneInfoEntityDO sceneInfo, String paraType) {
        JSONObject jsonObject = Optional.ofNullable(sceneInfo).map(SceneInfoEntityDO::getSceneBackData).map(JSONObject::parseObject).orElse(null);
        return jsonObject != null ? jsonObject.getJSONArray(paraType) : null;
    }

    private boolean comparePreDataParameter(JSONArray publishArrays, JSONArray editArrays) {
        if (publishArrays.size() != editArrays.size()) {
            return false;
        }
        if (publishArrays.size() == 0 || editArrays.size() == 0) {
            return true;
        }
        List oldList = new ArrayList<>(publishArrays.toJavaList(JSONObject.class));
        List newList = new ArrayList<>(editArrays.toJavaList(JSONObject.class));
        oldList.sort(Comparator.comparing(Object::toString));
        newList.sort(Comparator.comparing(Object::toString));
        for (int i = 0; i < oldList.size(); i++) {
            if (!oldList.get(i).toString().equals(newList.get(i).toString())) {
                return false;
            }
        }
        return true;
    }

    private boolean uploadSceneFile(SceneInfoEntityDO sceneInfo, boolean afterPublish) {
        log.info("上传场景图json文件 >>> {}_{}", sceneInfo.getSceneCode(), sceneInfo.getSceneVersion());
        try {
            return ztoOssService.createObject("autojmx",
                    sceneInfo.getSceneOssPath() + sceneInfo.getSceneOssFile(),
                    sceneInfo.getSceneBackData());
        } catch (Exception e) {
            log.error("场景图json文件上传失败！", e);
            JSONObject stepRecord = JSON.parseObject(sceneInfo.getStepRecord());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            if (afterPublish) {
                stepRecord.put("message", "场景图发布成功，同步草稿json文件上传失败！");
            } else {
                stepRecord.put("message", "场景图json文件上传失败！");
            }
            SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
            entityDO.setId(sceneInfo.getId());
            entityDO.setStepRecord(stepRecord.toJSONString());
            apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
            return false;
        }
    }

    private boolean executePublish(SceneInfoEntityDO sceneInfo) {
        log.info("executePublish >>> [{}_{}]", sceneInfo.getSceneCode(), sceneInfo.getSceneVersion());
        if (!uploadSceneFile(sceneInfo, false)) {
            return false;
        }
        if (!executeGenerateLink(sceneInfo, false)) {
            return false;
        }
        String editAutomaticSourceCode = null;
        String publishAutomaticSourceCode = null;
        if (UseCaseFactoryTypeEnum.SCENE.getCode() == sceneInfo.getSceneType()) {
            editAutomaticSourceCode = sceneInfo.getAutomaticSourceCode();
            SceneInfoEntityDO publishSceneInfo =
                    apiTestRepository.queryLatestSceneInfo(sceneInfo.getSceneCode(), SceneInfoStatusEnum.publish);
            if (null != publishSceneInfo) {
                publishAutomaticSourceCode = publishSceneInfo.getAutomaticSourceCode();
            }
        }
        if (!executeProcess(sceneInfo, SceneInfoStatusEnum.publish, publishAutomaticSourceCode, false)) {
            return false;
        }
        return editToPublish(sceneInfo, editAutomaticSourceCode);
    }

    private boolean executeGenerateLink(SceneInfoEntityDO sceneInfo, boolean afterPublish) {
        log.info("链路解析 >>> {}_{}", sceneInfo.getSceneCode(), sceneInfo.getSceneVersion());
        JSONObject stepRecord = JSON.parseObject(sceneInfo.getStepRecord());
        try {
            int update = linkMapDomainService.generateLink(sceneInfo.getSceneCode(), sceneInfo.getSceneVersion());
            log.info("链路解析update={}", update);
            stepRecord.put(SceneInfoStepEnum.LinkMap.name(), AutomaticStatusEnum.SUCCESS.name());
        } catch (ServiceException e) {
            log.error("链路解析异常！", e);
            stepRecord.put(SceneInfoStepEnum.LinkMap.name(), AutomaticStatusEnum.FAIL.name());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            if (afterPublish) {
                stepRecord.put("message", "场景图发布成功，同步草稿链路解析异常！" + e.getMsg());
            } else {
                stepRecord.put("message", "链路解析异常！" + e.getMsg());
            }
        } catch (Exception e) {
            log.error("链路解析异常！", e);
            stepRecord.put(SceneInfoStepEnum.LinkMap.name(), AutomaticStatusEnum.FAIL.name());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            if (afterPublish) {
                stepRecord.put("message", "场景图发布成功，同步草稿链路解析异常！" + e.getMessage());
            } else {
                stepRecord.put("message", "链路解析异常！" + e.getMessage());
            }
        }
        sceneInfo.setStepRecord(subStepRecordString(stepRecord));
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setId(sceneInfo.getId());
        entityDO.setStepRecord(sceneInfo.getStepRecord());
        apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
        return AutomaticStatusEnum.SUCCESS.name().equals(stepRecord.getString(SceneInfoStepEnum.LinkMap.name()));
    }

    private boolean executeGenerateNodeCase(SceneInfoEntityDO sceneInfo, boolean afterPublish) {
        log.info("生成用例 >>> {}_{}", sceneInfo.getSceneCode(), sceneInfo.getSceneVersion());
        JSONObject stepRecord = JSON.parseObject(sceneInfo.getStepRecord());
        try {
            Scene scene = JSON.parseObject(sceneInfo.getSceneBackData(), Scene.class);
            if (null == scene.getNodes()) {
                throw new ServiceException("场景图缺少节点数据！");
            }
            List<ApiCaseEntityDO> nodeCaseList = new ArrayList<>();
            scene.getNodes().keySet().forEach(key -> {
                Node node = JSON.parseObject(scene.getNodes().getString(key), Node.class);
                ApiTestDataVO reqData = GenerateApiCaseUtil.transNodeToCase(node);
                ApiCaseEntityDO nodeCase = new ApiCaseEntityDO();
                nodeCase.setNodeCode(node.getCode());
                nodeCase.setSceneCode(sceneInfo.getSceneCode());
                nodeCase.setUserVariableCode(node.getUser());
                nodeCase.setReqData(JSON.toJSONString(reqData));
                nodeCaseList.add(nodeCase);
            });
            String ossPath = sceneInfo.getSceneOssPath() + "/edit/nodeCase.json";
            boolean result = ztoOssService.createObject("autojmx", ossPath, JSON.toJSONString(nodeCaseList));
            log.info("生成节点用例文件 {}：{}", ossPath, result);
            stepRecord.put(SceneInfoStepEnum.NodeCase.name(), AutomaticStatusEnum.SUCCESS.name());
        } catch (ServiceException e) {
            log.error("生成用例异常！", e);
            stepRecord.put(SceneInfoStepEnum.NodeCase.name(), AutomaticStatusEnum.FAIL.name());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            if (afterPublish) {
                stepRecord.put("message", "场景图发布成功，同步草稿生成用例异常！" + e.getMsg());
            } else {
                stepRecord.put("message", "生成用例异常！" + e.getMsg());
            }
        } catch (Exception e) {
            log.error("生成用例异常！", e);
            stepRecord.put(SceneInfoStepEnum.NodeCase.name(), AutomaticStatusEnum.FAIL.name());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            if (afterPublish) {
                stepRecord.put("message", "场景图发布成功，同步草稿生成用例异常！" + e.getMessage());
            } else {
                stepRecord.put("message", "生成用例异常！" + e.getMessage());
            }
        }
        sceneInfo.setStepRecord(subStepRecordString(stepRecord));
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setId(sceneInfo.getId());
        entityDO.setStepRecord(sceneInfo.getStepRecord());
        apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
        return AutomaticStatusEnum.SUCCESS.name().equals(stepRecord.getString(SceneInfoStepEnum.NodeCase.name()));
    }

    private boolean executeGenerateScript(SceneInfoEntityDO sceneInfo, String jmxFileName, boolean afterPublish) {
        log.info("生成脚本 >>> {}_{}", sceneInfo.getSceneCode(), sceneInfo.getSceneVersion());
        JSONObject stepRecord = JSON.parseObject(sceneInfo.getStepRecord());
        try {
            if (UseCaseFactoryTypeEnum.CREATE.getCode() == sceneInfo.getSceneType()) {
                JSONObject scene = JSON.parseObject(sceneInfo.getSceneBackData());
                JSONObject nodes = new JSONObject();
                for (Map.Entry<String, Object> entry : scene.getJSONObject("nodes").entrySet()) {
                    JSONObject node = (JSONObject) entry.getValue();
                    if (Boolean.FALSE.equals(node.getBoolean("gatewaySource"))) {
                        nodes.put(entry.getKey(), entry.getValue());
                    }
                }
                if (!nodes.isEmpty()) {
                    linkMapDomainService.setNonGatewayApiEnvUrl(nodes, sceneInfo.getProductCode(), null);
                    scene.getJSONObject("nodes").putAll(nodes);
                    SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
                    entityDO.setId(sceneInfo.getId());
                    entityDO.setSceneBackData(scene.toJSONString());
                    apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
                }
            }
            this.createJmeterScript(sceneInfo, jmxFileName);
            stepRecord.put(SceneInfoStepEnum.Script.name(), AutomaticStatusEnum.SUCCESS.name());
        } catch (ServiceException e) {
            log.error("生成脚本异常！", e);
            stepRecord.put(SceneInfoStepEnum.Script.name(), AutomaticStatusEnum.FAIL.name());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            if (afterPublish) {
                stepRecord.put("message", "场景图发布成功，同步草稿生成脚本异常！" + e.getMsg());
            } else {
                stepRecord.put("message", "生成脚本异常！" + e.getMsg());
            }
        } catch (Exception e) {
            log.error("生成脚本异常！", e);
            stepRecord.put(SceneInfoStepEnum.Script.name(), AutomaticStatusEnum.FAIL.name());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            if (afterPublish) {
                stepRecord.put("message", "场景图发布成功，同步草稿生成脚本异常！" + e.getMessage());
            } else {
                stepRecord.put("message", "生成脚本异常！" + e.getMessage());
            }
        }
        sceneInfo.setStepRecord(subStepRecordString(stepRecord));
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setId(sceneInfo.getId());
        entityDO.setStepRecord(sceneInfo.getStepRecord());
        apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
        return AutomaticStatusEnum.SUCCESS.name().equals(stepRecord.getString(SceneInfoStepEnum.Script.name()));
    }

    private void createJmeterScript(SceneInfoEntityDO sceneInfo, String jmxFileName) {
        JSONObject runningSet = new JSONObject();
        if(null != sceneInfo.getSceneBackData()) {
            JSONObject sceneBackData = JSON.parseObject(sceneInfo.getSceneBackData());
            if(null != sceneBackData) {
                runningSet = sceneBackData.getJSONObject("runSettingForm");
            }
        }
        jmxUtil.createJmeterScript(
                sceneInfo.getSceneCode(),
                sceneInfo.getSceneVersion(),
                sceneInfo.getSceneName(),
                sceneInfo.getSceneOssPath() + "/edit",
                jmxFileName,
                sceneInfo.getProductCode(),
                sceneInfo.getSceneType(),
                runningSet);
    }

    private boolean executeGenerateDataFile(SceneInfoEntityDO sceneInfo, boolean afterPublish) {
        log.info("生成数据文件 >>> {}_{}", sceneInfo.getSceneCode(), sceneInfo.getSceneVersion());
        JSONObject stepRecord = JSON.parseObject(sceneInfo.getStepRecord());
        try {
            linkMapDomainService.generateSceneLinkDataFile(
                    sceneInfo.getSceneCode(), sceneInfo.getSceneVersion(), sceneInfo.getSceneOssPath(), sceneInfo.getProductCode());
            if (sceneInfo.getSceneType() == 2) {
                linkMapDomainService.generateDataCenterVariableFile(sceneInfo.getProductCode(), sceneInfo.getSceneCode(), sceneInfo.getSceneOssPath());
            }
            stepRecord.put(SceneInfoStepEnum.DataFile.name(), AutomaticStatusEnum.SUCCESS.name());
        } catch (ServiceException e) {
            log.error("生成数据文件异常！", e);
            stepRecord.put(SceneInfoStepEnum.DataFile.name(), AutomaticStatusEnum.FAIL.name());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            if (afterPublish) {
                stepRecord.put("message", "场景图发布成功，同步草稿生成数据文件异常！" + e.getMsg());
            } else {
                stepRecord.put("message", "生成数据文件异常！" + e.getMsg());
            }
        } catch (Exception e) {
            log.error("生成数据文件异常！", e);
            stepRecord.put(SceneInfoStepEnum.DataFile.name(), AutomaticStatusEnum.FAIL.name());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            if (afterPublish) {
                stepRecord.put("message", "场景图发布成功，同步草稿生成数据文件异常！" + e.getMessage());
            } else {
                stepRecord.put("message", "生成数据文件异常！" + e.getMessage());
            }
        }
        sceneInfo.setStepRecord(subStepRecordString(stepRecord));
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setId(sceneInfo.getId());
        entityDO.setStepRecord(sceneInfo.getStepRecord());
        apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
        return AutomaticStatusEnum.SUCCESS.name().equals(stepRecord.getString(SceneInfoStepEnum.DataFile.name()));
    }

    private boolean executeGenerateAutomaticSourceRecord(
            SceneInfoEntityDO sceneInfo, SceneInfoStatusEnum status, String automaticSourceCode, boolean afterPublish) {
        log.info("同步登记库 >>> {}_{}_{}", sceneInfo.getSceneCode(), sceneInfo.getSceneVersion(), status);
        JSONObject stepRecord = JSON.parseObject(sceneInfo.getStepRecord());
        try {
            String ossPath = sceneInfo.getSceneOssPath() + "/edit/";
            User transactor = new User(sceneInfo.getModifierId(), sceneInfo.getModifier());
            if (StringUtils.isBlank(automaticSourceCode)) {
                // 未生成过登记库，新建上级分组
                AddTestcaseEvent event = createNewAutomaticModule(sceneInfo, status, transactor);
                // 新增登记库
                AddAutomaticRecordCommand addCommand = createNewAutomaticSource(sceneInfo, status, event, ossPath, transactor);
                // 新增解析记录-进行中
                String automaticRecordLog = addAutomaticRecordLog(addCommand);
                // 新增后解析登记库
                parseAutomaticSource(addCommand, automaticRecordLog);
                automaticSourceCode = addCommand.getAggregateId();
                // 更新解析记录-成功
                updateAutomaticRecordLogStatus(automaticRecordLog);
            } else {
                // 查询登记库
                AutomaticSourceRecordEntityDO entityDO = automaticSourceRecordRepository.find(automaticSourceCode);
                if (null == entityDO) {
                    throw new ServiceException("该场景未找到登记库！");
                }
                if (!entityDO.getEnable()) {
                    log.warn("登记库已删除，重新更新状态：{}", automaticSourceCode);
                    AutomaticSourceRecordEntityDO automaticSourceRecord = new AutomaticSourceRecordEntityDO();
                    automaticSourceRecord.setCode(automaticSourceCode);
                    automaticSourceRecord.setEnable(true);
                    automaticSourceRecordRepository.updateByPrimaryKeySelective(automaticSourceRecord);
                }
                EditAutomaticRecordCommand editCommand = buildEditAutomaticCommand(entityDO, ossPath);
                editCommand.setTransactor(transactor);
                AddAutomaticRecordLogCommand addCommand =
                        automaticSourceRecordDomainConverter.convertCommand(editCommand);
                // 新增解析记录-进行中
                String automaticRecordLog = addAutomaticRecordLog(addCommand);
                // 重新解析登记库
                parseAutomaticSourceAgain(editCommand, automaticRecordLog);
                // 确认登记库
                submitAnalysisAutomatic(automaticRecordLog, entityDO.getCode(), transactor, ossPath);
            }
            stepRecord.put(SceneInfoStepEnum.AutomaticSourceRecord.name(), AutomaticStatusEnum.SUCCESS.name());
            sceneInfo.setAutomaticSourceCode(automaticSourceCode);
        } catch (ServiceException e) {
            log.error("同步登记库异常！", e);
            stepRecord.put(SceneInfoStepEnum.AutomaticSourceRecord.name(), AutomaticStatusEnum.FAIL.name());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            if (afterPublish) {
                stepRecord.put("message", "场景图发布成功，同步草稿同步登记库异常！" + e.getMsg());
            } else {
                stepRecord.put("message", "同步登记库异常！" + e.getMsg());
            }
        } catch (Exception e) {
            log.error("同步登记库异常！", e);
            stepRecord.put(SceneInfoStepEnum.AutomaticSourceRecord.name(), AutomaticStatusEnum.FAIL.name());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            if (afterPublish) {
                stepRecord.put("message", "场景图发布成功，同步草稿同步登记库异常！" + e.getMessage());
            } else {
                stepRecord.put("message", "同步登记库异常！" + e.getMessage());
            }
        }
        sceneInfo.setStepRecord(subStepRecordString(stepRecord));
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setId(sceneInfo.getId());
        entityDO.setStepRecord(sceneInfo.getStepRecord());
        apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
        return AutomaticStatusEnum.SUCCESS.name().equals(stepRecord.getString(SceneInfoStepEnum.AutomaticSourceRecord.name()));
    }

    private String subStepRecordString(JSONObject stepRecord) {
        if (stepRecord.toJSONString().length() < 1025) {
            return stepRecord.toJSONString();
        }
        int length = stepRecord.getString("message").length() - (stepRecord.toJSONString().length() - 1024) - 3;
        String message = StrUtil.maxLength(stepRecord.getString("message"), length);
        stepRecord.put("message", message);
        return stepRecord.toJSONString();
    }

    private boolean editToPublish(SceneInfoEntityDO sceneInfo, String automaticSourceCode) {
        log.info("发布同步 >>> {}_{}", sceneInfo.getSceneCode(), sceneInfo.getSceneVersion());
        JSONObject stepRecord = JSON.parseObject(sceneInfo.getStepRecord());
        try {
            List<String> editList = ztoOssService.getListObjectKey("autojmx",
                    sceneInfo.getSceneOssPath() + "/edit/");
            if (CollectionUtils.isEmpty(editList)) {
                throw new ServiceException("未找到脚本文件！[" + sceneInfo.getSceneOssPath() + "/edit/]");
            }
            List<String> publishList =
                    ztoOssService.getListObjectKey("autojmx", sceneInfo.getSceneOssPath() + "/publish/");
            if (CollectionUtils.isNotEmpty(publishList)) {
                publishList.forEach(path -> ztoOssService.cleanObject("autojmx", path));
            }
            editList.forEach(path -> {
                String copyPath = path.replace("/edit/", "/publish/");
                ztoOssService.copyObject("autojmx", path, copyPath);
            });
            if (StringUtils.isNotEmpty(sceneInfo.getAutomaticSourceCode())) {
                AutomaticSourceRecordEntityDO automaticSourceRecord = new AutomaticSourceRecordEntityDO();
                automaticSourceRecord.setCode(sceneInfo.getAutomaticSourceCode());
                automaticSourceRecord.setAddress(sceneInfo.getSceneOssPath() + "/publish/");
                automaticSourceRecordRepository.updateByPrimaryKeySelective(automaticSourceRecord);
            }
            JSONObject data = JSONObject.parseObject(sceneInfo.getSceneBackData());
            data.remove("updateNodeList");
            if (UseCaseFactoryTypeEnum.SCENE.getCode() == sceneInfo.getSceneType()) {
                JSONArray updateTypeJson = data.getJSONArray("updateTypeList");
                if (null != updateTypeJson && updateTypeJson.size() > 0) {
                    deleteNewUpdateType(updateTypeJson, null, PreDataMarkEnum.UPDATE);
                    data.put("updateTypeList", updateTypeJson);
                }
            }
            stepRecord.put(SceneInfoStepEnum.Publish.name(), AutomaticStatusEnum.SUCCESS.name());
            stepRecord.put("result", AutomaticStatusEnum.SUCCESS.name());
            sceneInfo.setStatus(SceneInfoStatusEnum.publish);
            sceneInfo.setEnable(SceneInfoEnableEnum.PUBLISHED);
            sceneInfo.setSceneBackData(data.toString());
            return true;
        } catch (ServiceException e) {
            log.error("发布异常！", e);
            stepRecord.put(SceneInfoStepEnum.Publish.name(), AutomaticStatusEnum.FAIL.name());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            stepRecord.put("message", "发布异常！" + e.getMsg());
            sceneInfo.setAutomaticSourceCode(automaticSourceCode);
            return false;
        } catch (Exception e) {
            log.error("发布异常！", e);
            stepRecord.put(SceneInfoStepEnum.Publish.name(), AutomaticStatusEnum.FAIL.name());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            stepRecord.put("message", "发布异常！" + e.getMessage());
            sceneInfo.setAutomaticSourceCode(automaticSourceCode);
            return false;
        } finally {
            SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
            entityDO.setId(sceneInfo.getId());
            entityDO.setStepRecord(subStepRecordString(stepRecord));
            entityDO.setStatus(sceneInfo.getStatus());
            entityDO.setEnable(sceneInfo.getEnable());
            entityDO.setAutomaticSourceCode(sceneInfo.getAutomaticSourceCode());
            entityDO.setSceneBackData(sceneInfo.getSceneBackData());
            apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
            if (AutomaticStatusEnum.SUCCESS.name().equals(stepRecord.getString("result"))) {
                apply(convert.convert(sceneInfo));
                editCopyAfterPublish(sceneInfo, automaticSourceCode);
                saveSceneApiRelation(sceneInfo);
            }
        }
    }

    private void editCopyAfterPublish(SceneInfoEntityDO sceneInfo, String automaticSourceCode) {
        try {
            SceneInfoEntityDO editScene = convert.copyConvert(sceneInfo);
            editScene.setId(null);
            if (UseCaseFactoryTypeEnum.SCENE.getCode() == editScene.getSceneType()) {
                JSONObject stepRecord = new JSONObject();
                stepRecord.put("result", AutomaticStatusEnum.IN_PROGRESS);
                editScene.setStepRecord(stepRecord.toJSONString());
            } else {
                editScene.setStepRecord("");
            }
            editScene.setSceneVersion(sceneInfo.getSceneVersion() + 1);
            editScene.setSceneOssFile("/V" + editScene.getSceneVersion() + ".json");
            editScene.setStatus(SceneInfoStatusEnum.edit);
            editScene.setAutomaticSourceCode(automaticSourceCode);
            editScene.setSceneBackDataMd5("reset");
            editScene.setSceneType(sceneInfo.getSceneType());
            apiTestRepository.insertSceneInfoSelective(editScene);
            if (UseCaseFactoryTypeEnum.CREATE.getCode() == editScene.getSceneType()) {
                return;
            }
            if (!uploadSceneFile(editScene, true)) {
                return;
            }
            if (!executeGenerateLink(editScene, true)) {
                return;
            }
            if (executeProcess(editScene, SceneInfoStatusEnum.edit, automaticSourceCode, true)) {
                JSONObject stepRecord = JSON.parseObject(editScene.getStepRecord());
                stepRecord.put("result", AutomaticStatusEnum.SUCCESS.name());
                SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
                entityDO.setId(editScene.getId());
                entityDO.setStepRecord(stepRecord.toJSONString());
                entityDO.setAutomaticSourceCode(editScene.getAutomaticSourceCode());
                apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
            }
        } catch (Exception e) {
            log.error("场景图发布后同步草稿异常！", e);
        }
    }

    private void saveSceneApiRelation(SceneInfoEntityDO sceneInfo) {
        try {
            if (UseCaseFactoryTypeEnum.CREATE.getCode() == sceneInfo.getSceneType()) {
                return;
            }
            Map<String, SceneApiRelationEntityDO> relationMap = new HashMap<>();
            Scene scene = JSON.parseObject(sceneInfo.getSceneBackData(), Scene.class);
            scene.getNodes().keySet().forEach(key -> {
                Node node = scene.getNodes().getObject(key, Node.class);
                SceneApiRelationEntityDO relation = new SceneApiRelationEntityDO();
                relation.setSceneCode(sceneInfo.getSceneCode());
                relation.setProductCode(sceneInfo.getProductCode());
                relation.preCreate(new User(sceneInfo.getModifierId(), sceneInfo.getModifier()));
                if (StringUtils.isNotEmpty(node.getApiCode())) {
                    ApiTestEntityDO apiTest = apiTestRepository.queryLatestApiTestByMainCode(node.getApiCode());
                    if (null != apiTest) {
                        relation.setAppId(apiTest.getAppId());
                        relation.setApiType(apiTest.getApiType());
                        relation.setApiAddress(apiTest.getApiAddress());
                        relation.setRelatedApiName(apiTest.getApiName());
                        relation.setMainApiCode(apiTest.getMainApiCode());
                        relationMap.put(relation.getMainApiCode(), relation);
                        return;
                    }
                }
                LinkMapTypeEnum type = LinkMapTypeEnum.valueOf(node.getSampler().getString("type"));
                if (LinkMapTypeEnum.DUBBO_REQUEST_COMPONENT.equals(type)
                        && StringUtils.isNotEmpty(node.getSampler().getString("interfaceName"))
                        && StringUtils.isNotEmpty(node.getSampler().getString("methodName"))) {
                    relation.setApiType(ApiTypeEnum.DUBBO);
                    relation.setApiAddress(node.getSampler().getString("interfaceName")
                            + "#" + node.getSampler().getString("methodName"));
                    relationMap.put(relation.getApiAddress(), relation);
                }
                if ((LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_FORM_TYPE.equals(type)
                        || LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE.equals(type))
                        && StringUtils.isNotEmpty(node.getRequestUrl())) {
                    relation.setApiType(ApiTypeEnum.HTTP);
                    relation.setApiAddress(node.getRequestUrl());
                    relationMap.put(relation.getApiAddress(), relation);
                }
            });
            removeSceneApiRelation(sceneInfo.getSceneCode(), relationMap);
            relationMap.values().forEach(relation -> {
                apiTestRepository.insertSceneApiRelation(relation);
                if (StringUtils.isNotEmpty(relation.getMainApiCode())) {
                    apiTestRepository.updateApiTestRelatedSceneFlagByMainApiCode(relation.getMainApiCode(), 1);
                }
            });
        } catch (Exception e) {
            log.error("场景图发布后记录关联接口异常！", e);
        }
    }

    private void removeSceneApiRelation(String sceneCode, Map<String, SceneApiRelationEntityDO> relationMap) {
        List<String> oldApiCodeList = apiTestRepository.queryRelatedApiCodeBySceneCode(sceneCode);
        apiTestRepository.deleteSceneApiRelationBySceneCode(sceneCode);
        if (null != relationMap) {
            oldApiCodeList.removeIf(relationMap::containsKey);
        }
        oldApiCodeList.forEach(apiCode -> {
            int count = apiTestRepository.countSceneApiRelationByMainApiCode(apiCode);
            if (count == 0) {
                apiTestRepository.updateApiTestRelatedSceneFlagByMainApiCode(apiCode, 0);
            }
        });
    }

    public void changeLinkStatusCommand(ChangeLinkStatusCommand command) {
        log.info("changeLinkStatusCommand >>> {}", JSON.toJSONString(command));
        if (StringUtils.isEmpty(command.getLinkMapCode())) {
            SceneInfoEntityDO publishSceneInfo =
                    apiTestRepository.queryLatestSceneInfo(command.getAggregateId(), SceneInfoStatusEnum.publish);
            if (null == publishSceneInfo) {
                return;
            }
            SceneInfoEntityDO sceneInfo = new SceneInfoEntityDO();
            sceneInfo.setSceneCode(publishSceneInfo.getSceneCode());
            sceneInfo.setEnable(command.getEnable() ? SceneInfoEnableEnum.PUBLISHED : SceneInfoEnableEnum.DISABLED);
            sceneInfo.preUpdate(command);
            if (UseCaseFactoryTypeEnum.CREATE.equals(command.getSceneType()) && !command.getEnable()) {
                //已停用无法分享
                sceneInfo.setShareStatus(false);
            }
            apiTestRepository.updateSceneInfoByCode(sceneInfo);
            apply(convert.convertToChangeStatusEvent(sceneInfo));
            // 停用场景需要停用登记库
            if (StringUtils.isNotEmpty(publishSceneInfo.getAutomaticSourceCode())
                    && !UseCaseFactoryTypeEnum.CREATE.equals(command.getSceneType())) {
                SYNC_API_TEST_EXECUTOR.execute(() -> {
                    log.info("场景图停用/启用更新登记库 >>> {}", publishSceneInfo.getAutomaticSourceCode());
                    TestcaseStatusEnum status =
                            command.getEnable() ? TestcaseStatusEnum.NORMAL : TestcaseStatusEnum.DISABLE;
                    testcaseRepository.updateStatusFromSceneInfo(publishSceneInfo.getAutomaticSourceCode(), status);
                });
            }
        } else {
            SceneInfoEntityDO sceneInfo = apiTestRepository.querySceneInfoByCodeAndVersion(
                    command.getAggregateId(), command.getSceneVersion());
            if (null == sceneInfo || SceneInfoStatusEnum.publish.equals(sceneInfo.getStatus())) {
                return;
            }
            linkMapRepository.updateSceneLinkInfoEnable(command);
            if (StringUtils.isNotEmpty(sceneInfo.getAutomaticSourceCode())
                    && !UseCaseFactoryTypeEnum.CREATE.equals(command.getSceneType())) {
                SYNC_API_TEST_EXECUTOR.execute(() -> {
                    log.info("链路停用/启用更新登记库 >>> {}", sceneInfo.getAutomaticSourceCode());
                    TestcaseStatusEnum status = command.getEnable() ? TestcaseStatusEnum.NORMAL : TestcaseStatusEnum.DISABLE;
                    testcaseRepository.updateStatusFromLinkMap(sceneInfo.getAutomaticSourceCode(), command.getLinkMapCode(), status);
                });
            }
        }
        if (!command.getEnable()) {
            SYNC_API_TEST_EXECUTOR.execute(() -> {
                syncSceneByPreData(command.getAggregateId(), null, PreDataMarkEnum.DISABLED);
            });
        } else {
            SYNC_API_TEST_EXECUTOR.execute(() -> {
                syncDeleteSceneByPreData(command.getAggregateId(), PreDataMarkEnum.DISABLED);
            });
        }
    }

    public void generateAutomaticSource(AutomaticSourceGenerateCommand command) {
        log.info("点击进入登记库，AutomaticSourceGenerateCommand:{}", JSON.toJSONString(command));
        SceneInfoEntityDO sceneDO = apiTestRepository.queryLatestSceneInfo(command.getSceneCode(), command.getStatus());
        if (null == sceneDO) {
            throw new ServiceException("场景图不存在！");
        }
        if (StringUtils.isNotBlank(sceneDO.getStepRecord())) {
            JSONObject stepRecord = JSON.parseObject(sceneDO.getStepRecord());
            if (null == stepRecord) {
                throw new ServiceException("初始化过程数据异常！");
            }
            if (AutomaticStatusEnum.IN_PROGRESS.name().equals(stepRecord.getString("result"))) {
                throw new ServiceException("登记库生成中，请耐心等候！");
            }
            if (AutomaticStatusEnum.SUCCESS.name().equals(stepRecord.getString("AutomaticSourceRecord"))) {
                return;
            }
        }
        LockSeal lockSeal = lockService.acquireLock(LockStoreEnum.SCENE_GENERATE.getValue(),
                Collections.singletonList(command.getSceneCode()),
                30000L);
        if (null == lockSeal) {
            throw new ServiceException("场景图正在生成中，请稍后再试！");
        }
        JSONObject stepRecord = new JSONObject();
        stepRecord.put("result", AutomaticStatusEnum.IN_PROGRESS.name());
        sceneDO.setStepRecord(stepRecord.toJSONString());
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setId(sceneDO.getId());
        entityDO.setStepRecord(sceneDO.getStepRecord());
        entityDO.preUpdate(command);
        apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
        AUTO_SOURCE_EXECUTOR.execute(() -> {
            try {
                executeAutomaticSource(sceneDO);
            } finally {
                lockService.releaseLock(lockSeal);
            }
        });
    }

    /**
     * 生成链路*
     */
    public JSONObject generateLink(String sceneCode, Integer sceneVersion) {
        SceneInfoEntityDO sceneDO = apiTestRepository.querySceneInfoByCodeAndVersion(sceneCode, sceneVersion);
        JSONObject stepRecord = StringUtils.isEmpty(sceneDO.getStepRecord()) ? new JSONObject() : JSON.parseObject(sceneDO.getStepRecord());
        try {
            int isUpdate = linkMapDomainService.generateLink(sceneCode, sceneVersion);
            if (isUpdate == 1) {
                if (stepRecord.containsKey(SceneInfoStepEnum.LinkMap.name())) {
                    return stepRecord;
                }
            }
            stepRecord.put(SceneInfoStepEnum.LinkMap.name(), AutomaticStatusEnum.SUCCESS.name());
            stepRecord.put("result", AutomaticStatusEnum.SUCCESS.name());
        } catch (Exception ex) {
            stepRecord.put(SceneInfoStepEnum.LinkMap.name(), AutomaticStatusEnum.FAIL.name());
            stepRecord.put("result", AutomaticStatusEnum.FAIL.name());
            stepRecord.put("message", ex.getMessage());
        }
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setId(sceneDO.getId());
        entityDO.setStepRecord(subStepRecordString(stepRecord));
        apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
        return stepRecord;
    }

    /**
     * 生成登记库
     */
    private void executeAutomaticSource(SceneInfoEntityDO sceneDO) {
        if (!uploadSceneFile(sceneDO, false)) {
            tagRepository.saveSceneTag(sceneDO.getSceneCode(), SceneTagEnum.GENERATE_ERROR);
            return;
        }
        if (!executeGenerateLink(sceneDO, false)) {
            tagRepository.saveSceneTag(sceneDO.getSceneCode(), SceneTagEnum.GENERATE_ERROR);
            return;
        }
        boolean result = executeProcess(sceneDO, sceneDO.getStatus(), sceneDO.getAutomaticSourceCode(), false);
        if (result) {
            JSONObject stepRecord = JSON.parseObject(sceneDO.getStepRecord());
            stepRecord.put("result", AutomaticStatusEnum.SUCCESS.name());
            SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
            entityDO.setId(sceneDO.getId());
            entityDO.setStepRecord(stepRecord.toJSONString());
            entityDO.setAutomaticSourceCode(sceneDO.getAutomaticSourceCode());
            apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
            tagRepository.removeSceneTag(sceneDO.getSceneCode(), Collections.singletonList(SceneTagEnum.GENERATE_ERROR));
        } else {
            tagRepository.saveSceneTag(sceneDO.getSceneCode(), SceneTagEnum.GENERATE_ERROR);
        }
    }

    private boolean executeProcess(SceneInfoEntityDO sceneInfo, SceneInfoStatusEnum status, String automaticSourceCode, boolean afterPublish) {
        if (!executeGenerateNodeCase(sceneInfo, afterPublish)) {
            return false;
        }
        if (!executeGenerateDataFile(sceneInfo, afterPublish)) {
            return false;
        }
        String jmxFileName = "scene.jmx";
        if (UseCaseFactoryTypeEnum.SCENE.getCode() == sceneInfo.getSceneType()
                && StringUtils.isNotBlank(automaticSourceCode)) {
            jmxFileName = "scene_temp.jmx";
        }
        if (!executeGenerateScript(sceneInfo, jmxFileName, afterPublish)) {
            return false;
        }
        if (UseCaseFactoryTypeEnum.CREATE.getCode() == sceneInfo.getSceneType()) {
            return true;
        }
        if (!executeGenerateAutomaticSourceRecord(sceneInfo, status, automaticSourceCode, afterPublish)) {
            return false;
        }
        return true;
    }

    /**
     * 更新解析记录
     */
    private void updateAutomaticRecordLogStatus(String automaticRecordLog) {
        AutomaticSourceLogEntityDO logEntityDO = new AutomaticSourceLogEntityDO();
        logEntityDO.setCode(automaticRecordLog);
        logEntityDO.setStatus(AutomaticStatusEnum.NALYSISSUCCESSCONFIRMED.name());
        automaticSourceRecordRepository.update(logEntityDO);
    }

    /**
     * 确认登记库
     */
    private void submitAnalysisAutomatic(String recordLog, String automaticSourceCode, User transactor, String ossPath) {
        automaticSourceRecordRepository.editAutomaticLogStatus(recordLog);
        SubmitAnalysisAutomaticCommand submitCommand = new SubmitAnalysisAutomaticCommand(automaticSourceCode);
        submitCommand.setAutomaticSourceLogCode(recordLog);
        submitCommand.setCode(automaticSourceCode);
        submitCommand.setTransactor(transactor);
        submitCommand.setAddress(ossPath);
        automaticSourceRecordCommandDomainService.submitAnalysisAutomatic(submitCommand, transactor);
    }

    private EditAutomaticRecordCommand buildEditAutomaticCommand(AutomaticSourceRecordEntityDO entityDO, String ossPath) {
        EditAutomaticRecordCommand editCommand = new EditAutomaticRecordCommand(entityDO.getCode());
        editCommand.setProductCode(entityDO.getProductCode());
        editCommand.setName(entityDO.getName());
        editCommand.setAddress(ossPath);
        editCommand.setType(AutomaticRecordTypeEnum.JMETER);
        editCommand.setFileName("scene_temp.jmx");
        editCommand.setBucketName("autojmx");
        editCommand.setTestcaseCode(entityDO.getTestcaseCode());
        editCommand.setStatus(AutomaticStatusEnum.IN_PROGRESS);
        return editCommand;
    }

    private String addAutomaticRecordLog(AddAutomaticRecordCommand addCommand) {
        String logCode = aggregateIdGenerateService.generateId(AggregateType.TEST_CASE);
        AddAutomaticRecordLogCommand logCommand = new AddAutomaticRecordLogCommand(addCommand.getAggregateId());
        logCommand.setProductCode(addCommand.getProductCode());
        logCommand.setFileName(addCommand.getFileName());
        logCommand.setName(addCommand.getName());
        logCommand.setAutomaticSourceCode(logCode);
        logCommand.setStatus(AutomaticStatusEnum.IN_PROGRESS.name());
        logCommand.setAddress(addCommand.getAddress());
        logCommand.setBucketName(addCommand.getBucketName());
        logCommand.setTransactor(addCommand.getTransactor());
        automaticSourceRecordCommandDomainService.addAutomaticRecordLogCommand(logCommand);
        return logCode;
    }

    private void parseAutomaticSource(AddAutomaticRecordCommand command, String automaticRecordLog) {
        automaticSourceRecordCommandDomainService.sendCase(command);
        AutomaticRecordLogVO logVO = automaticSourceRecordRepository.findAutomaticRecordLogVO(automaticRecordLog);
        if (AutomaticStatusEnum.FAIL.name().equals(logVO.getStatus())) {
            throw new ServiceException("解析登记库异常！" + logVO.getFailInformation());
        }
    }

    private void parseAutomaticSourceAgain(EditAutomaticRecordCommand editCommand, String automaticRecordLog) {
        automaticSourceRecordCommandDomainService.sendEditCase(editCommand, editCommand.getFileName(), automaticRecordLog);
        AutomaticRecordLogVO logVO = automaticSourceRecordRepository.findAutomaticRecordLogVO(automaticRecordLog);
        if (AutomaticStatusEnum.FAIL.name().equals(logVO.getStatus())) {
            throw new ServiceException("解析登记库异常！" + logVO.getFailInformation());
        }
    }

    private AddAutomaticRecordCommand createNewAutomaticSource(
            SceneInfoEntityDO sceneDO,
            SceneInfoStatusEnum status,
            AddTestcaseEvent event, String ossPath, User transactor) {
        String aggregateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddAutomaticRecordCommand addCommand = new AddAutomaticRecordCommand(aggregateId);
        addCommand.setProductCode(sceneDO.getProductCode());
        addCommand.setName(sceneDO.getSceneName() + "-" + status.getDesc());
        addCommand.setAddress(ossPath);
        addCommand.setType(AutomaticRecordTypeEnum.JMETER);
        addCommand.setFileName("scene.jmx");
        addCommand.setBucketName("autojmx");
        addCommand.setTestcaseCode(event.getCode());
        addCommand.setPath(event.getPath());
        addCommand.setTransactor(transactor);
        automaticSourceRecordCommandDomainService.addAutomaticRecordCommand(addCommand);
        return addCommand;
    }

    /**
     * 新建登记库上级分组
     */
    private AddTestcaseEvent createNewAutomaticModule(SceneInfoEntityDO sceneDO, SceneInfoStatusEnum status, User transactor) {
        AddTestcaseEvent event;
        final String topName = "用例工厂";
        final String moduleName = SceneInfoStatusEnum.publish.equals(status) ? "发布-场景" : "草稿";
        // 是否已有用例工厂分组
        TestcaseEntityDO firstModule = getModuleByNameAndParent("", topName, sceneDO.getProductCode());
        if (null == firstModule) {
            // 新增分组-用例工厂
            AddTestcaseEvent addFirstModuleEvent =
                    this.addNewAutoModule(topName, sceneDO.getProductCode(), transactor, "", 1, "");
            event = this.addNewAutoModule(
                    moduleName, sceneDO.getProductCode(), transactor, addFirstModuleEvent.getCode(), 2, "");
        } else {
            // 分组-用例工厂存在，判断分组-草稿/发布是否存在
            TestcaseEntityDO secondLevelModule =
                    this.getModuleByNameAndParent(firstModule.getCode(), moduleName, sceneDO.getProductCode());
            if (null != secondLevelModule) {
                event = new AddTestcaseEvent();
                event.setCode(secondLevelModule.getCode());
                event.setPath(secondLevelModule.getPath());
            } else {
                event = this.addNewAutoModule(
                        moduleName, sceneDO.getProductCode(), transactor, firstModule.getCode(), 2, "");
            }
        }
        if (SceneInfoStatusEnum.publish.equals(status)) {
            SceneIndexVO sceneIndex = apiTestRepository.querySceneIndexByCode(sceneDO.getSceneCode());
            if (!"ALL".equals(sceneIndex.getParentCode())) {
                SceneIndexVO parentIndex = apiTestRepository.querySceneIndexByCode(sceneIndex.getParentCode());
                TestcaseEntityDO testcase = testcaseRepository.loadFormDb(parentIndex.getTestcaseCode());
                event = new AddTestcaseEvent();
                event.setCode(testcase.getCode());
                event.setPath(testcase.getPath());
            }
        }
        return event;
    }

    private TestcaseEntityDO getModuleByNameAndParent(String parentCode, String name, String productCode) {
        TestcaseEntityDO entityDO = new TestcaseEntityDO();
        entityDO.setParentCode(parentCode);
        entityDO.setName(name);
        entityDO.setProductCode(productCode);
        entityDO.setVersionCode("NONE_VERSION");
        entityDO.setType(TestcaseTypeEnum.AUTO);
        entityDO.setAttribute(TestcaseAttributeEnum.MODULE);
        return testcaseRepository.selectModuleInfoByName(entityDO);
    }

    private AddTestcaseEvent addNewAutoModule(String name, String productCode, User transactor,
                                              String parentCode, Integer layer, String interfaceName) {
        AddTestcaseEvent event = new AddTestcaseEvent();
        event.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.TEST_CASE));
        event.setCode(event.getAggregateId());
        event.setProductCode(productCode);
        event.setName(name);
        event.setType(TestcaseTypeEnum.AUTO);
        event.setAttribute(TestcaseAttributeEnum.MODULE);
        event.setLayer(layer);
        event.setParentCode(parentCode);
        event.setTransactor(transactor);
        event.setInterfaceName(interfaceName);
        testcaseRepository.addTestcase(event);
        apply(event);
        return event;
    }

    /**
     * 上传变量文件
     *
     * @param productCode
     * @param automaticOssPath
     * @param sceneBackData
     * @param env
     */
    public void uploadVariableFileByProductCode(String productCode, String automaticOssPath, String sceneBackData, String env, String sceneCode) {
        String fileName = automaticOssPath + "variable/variable.json";
        try {
            Scene scene = JSON.parseObject(sceneBackData, Scene.class);
            List<Node> nodeList = scene.getNodes().keySet().stream()
                    .map(key -> JSON.parseObject(scene.getNodes().getString(key), Node.class))
                    .collect(Collectors.toList());
            String envUrl = null;
            if (nodeList.stream().anyMatch(node -> "${ztoEnvHost}".equals(node.getSampler().getString("serverNameOrIp")))) {
                List<GlobalEnvVO> globalEnvList = metaDataService.getDocGlobalEnv(productCode);
                if (CollectionUtils.isNotEmpty(globalEnvList)) {
                    envUrl = globalEnvList.stream()
                            .filter(globalEnv -> globalEnv.getEnvName().equals(env))
                            .map(GlobalEnvVO::getEnvUrl)
                            .findAny().orElse(null);
                }
            }
            String finalEnvUrl = envUrl;
            Map<String, String> sceneVariableMap = new HashMap<>();
            Map<String, String> userMap = new HashMap<>();
            linkMapDomainService.getUser(userMap, nodeList, false, envUrl, productCode);
            linkMapDomainService.setUserCookies(userMap, sceneVariableMap, productCode);
            nodeList.stream()
                    .filter(node -> StringUtils.isNotEmpty(node.getUser()))
                    .forEach(node -> {
                        try {
                            String namespace;
                            if ("${ztoEnvHost}".equals(node.getSampler().getString("serverNameOrIp"))) {
                                namespace = linkMapDomainService.getNameSpace(finalEnvUrl);
                                sceneVariableMap.put("ztoEnvNamespace", namespace);
                            }
                        } catch (Exception e) {
                            log.error("put(ztoEnvNamespace)失败！envUrl = {}", finalEnvUrl, e);
                        }
                    });
            if (StringUtils.isNotEmpty(envUrl)) {
                try {
                    URL url = new URL(envUrl);
                    sceneVariableMap.put("ztoEnvProtocol", url.getProtocol());
                    sceneVariableMap.put("ztoEnvHost", url.getHost());
                    if (url.getPath().endsWith("/")) {
                        sceneVariableMap.put("ztoEnvPath", StringUtils.substringBeforeLast(url.getPath(), "/"));
                    } else {
                        sceneVariableMap.put("ztoEnvPath", url.getPath());
                    }
                    sceneVariableMap.put("ztoEnvPort", String.valueOf(url.getPort()));
                } catch (Exception e) {
                    log.error("url解析失败！{}", envUrl);
                }
            }
            linkMapDomainService.addVariableMap(productCode, sceneCode, sceneVariableMap);
            if (MapUtils.isNotEmpty(sceneVariableMap)) {
                boolean result = ztoOssService.createObject("autojmx", fileName, JSON.toJSONString(sceneVariableMap));
                log.info("{}_uploadVariableFile_result->{}, fileName-> {}", productCode, result, fileName);
            }
        } catch (Exception e) {
            log.error("{}_uploadVariableFile_exception, fileName-> {}", productCode, fileName, e);
        }
    }

    public String copySceneInfoReq(CopySceneInfoCommand command) {
        SceneInfoEntityDO sceneDO = apiTestRepository.queryLatestSceneInfo(
                command.getSceneCode(), SceneInfoStatusEnum.publish);
        if (null == sceneDO) {
            sceneDO = apiTestRepository.queryLatestSceneInfo(command.getSceneCode(), SceneInfoStatusEnum.edit);
            if (null == sceneDO) {
                throw new ServiceException("当前场景或者造数不存在");
            }
        }
        // 新建场景
        String newSceneCode = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        EditSceneInfoCommand editCommand = new EditSceneInfoCommand(newSceneCode);
        editCommand.setSceneName(command.getSceneName());
        editCommand.setSceneInfoDesc(command.getSceneInfoDesc());
        editCommand.setProductCode(sceneDO.getProductCode());
        editCommand.setTransactor(command.getTransactor());
        editCommand.setSceneFrontData(sceneDO.getSceneFrontData());
        editCommand.setSceneBackData(sceneDO.getSceneBackData());
        editCommand.setParentCode(command.getParentCode());
        editCommand.setSceneType(null != command.getSceneType() ? command.getSceneType().name() : null);
        this.addSceneBasicInfoCommand(editCommand);
        return newSceneCode;
    }

    public void abortDebugTask(AbortDebugTaskReq req) {
        if (StringUtils.isEmpty(req.getTaskId())) {
            return;
        }
        log.info("开始终止调试任务。req : {}", req.getTaskId());
        qcDebugService.abort(req.getTaskId());
        try {
            linkMapDomainService.updateDebugRecord(req.getTaskId(), AutomaticStatusEnum.TERMINATION);
        } catch (Exception e) {
            log.error("", e);
        }
    }

    public void debugCallBack(DebugCallBackReq req) {
        log.info("调试回调callBack, DebugCallBackReq:{}", JSON.toJSONString(req));
        DebugTaskResp<JSONObject> resp = new DebugTaskResp<>();
        resp.setTaskId(req.getTaskId());
        if (UseCaseFactoryTypeEnum.CREATE.equals(req.getSceneType())) {
            if (StringUtils.isNotBlank(req.getNodeCode())) {
                SceneInfoEntityDO scene = apiTestRepository.queryLatestSceneInfo(req.getNodeCode(), null);
                // 造数调试，发出参结果给前端
                if (null != scene) {
                    sendWebSocketMessage(DebugTaskRespTypeEnum.OUTPUT, req);
                    return;
                }
            }
            // 引用造数调试，发节点返回结果给前端
            sendWebSocketMessage(DebugTaskRespTypeEnum.NODE, req);
            return;
        }
        sendDocResponseEvent(req.getTaskId(), req.getResponseBody());
        resp.setType(DebugTaskRespTypeEnum.NODE);
        JSONObject resultJson = new JSONObject();
        resultJson.put("nodeCode", req.getNodeCode());
        resultJson.put("requestMessage", req.getRequestMessage());
        resultJson.put("responseHeader", req.getResponseHeader());
        resultJson.put("responseBody", req.getResponseBody());
        resultJson.put("assertContent", req.getAssertContent());
        resultJson.put("nodeStatus", req.getNodeStatus());
        resultJson.put("index", null == req.getIndex() ? null : Integer.valueOf(req.getIndex()));
        resp.setData(resultJson);
        log.info("回调结果实时同步前端, DebugTaskResp:{}", JSON.toJSONString(resp));
//        WebSocketMessage message = new WebSocketMessage();
//        message.setKey(req.getTaskId());
//        message.setData(resp);
//        zmsTemplate.send(DEVOPS_WS_MESSAGE_MQ, message);
        linkMapDomainService.sendDebugCallbackMessage(req.getTaskId(), resp);
        saveDebugNodeInfo(req.getTaskId(), req.getNodeCode(), resultJson);
    }

    private void sendDocResponseEvent(String taskId, String responseBody) {
        log.info("文档运行结束 -> taskId: {}, responseBody：{}", taskId, responseBody);
        ReactiveId reactiveId = new ReactiveId("USER", taskId, "DOC_RESPONSE_EVENT");
        reactiveEmitter.emit(reactiveId, responseBody);
    }

    private void sendWebSocketMessage(DebugTaskRespTypeEnum type, DebugCallBackReq req) {
        DebugTaskResp<JSONObject> resp = new DebugTaskResp<>();
        resp.setTaskId(req.getTaskId());
        resp.setType(type);
        JSONObject resultJson = new JSONObject();
        if (DebugTaskRespTypeEnum.OUTPUT.equals(type)) {
            resultJson.put("output", req.getOutputParameter());
            sendSaveDebugInfoEvent(req.getTaskId(), req.getOutputParameter());
        } else {
            resultJson.put("output", req.getOutputParameter());
            resultJson.put("input", req.getInputParameter());
            resultJson.put("nodeCode", req.getNodeCode());
            resultJson.put("nodeStatus", req.getNodeStatus());
        }
        resp.setData(resultJson);
        log.info("回调结果实时同步前端, DebugTaskResp:{}", JSON.toJSONString(resp));
//        WebSocketMessage message = new WebSocketMessage();
//        message.setKey(resp.getTaskId());
//        message.setData(resp);
//        zmsTemplate.send(DEVOPS_WS_MESSAGE_MQ, message);
        linkMapDomainService.sendDebugCallbackMessage(resp.getTaskId(), resp);
        saveDebugNodeInfo(req.getTaskId(), req.getNodeCode(), resultJson);
    }

    public void debugCallBackEnd(DebugCallBackEndReq req) {
        log.info("调试结束回调callBack, DebugCallBackEndReq:{}", JSON.toJSONString(req));
        DebugTaskResp<String> resp = new DebugTaskResp<>();
        resp.setTaskId(req.getTaskId());
        resp.setType(req.getType());
        if (DebugTaskRespTypeEnum.Exception.equals(req.getType())) {
            resp.setData(req.getExceptionReason());
        }
        log.info("回调结果实时同步前端, DebugTaskResp:{}", JSON.toJSONString(resp));
        DebugRequestTypeEnum requestTypeEnum = DebugRequestTypeEnum.valueOf(req.getRequestType());
        if (requestTypeEnum == DebugRequestTypeEnum.APIDEBUG || requestTypeEnum == DebugRequestTypeEnum.SCENEDEBUG) {
            linkMapDomainService.debugCallBackEnd(req.getTaskId(), resp, 0L, false);
        }
        if (DebugRequestTypeEnum.APITEST.equals(requestTypeEnum)) {
            automaticTaskCommandDomainService.apiTestCallback(req.getTaskId(), req.getType());
        }
    }

    @Async
    private void saveDbConfig(List<Integer> dbIds, String productCode, User user, UseCaseFactoryTypeEnum sceneType, String businessCode) {
        log.info("saveDbConfig >>> [{}]", JSON.toJSONString(dbIds));
        if (CollectionUtils.isEmpty(dbIds)) {
            return;
        }
        Set<Integer> dbIdSet = new HashSet<>(dbIds);
        for (Integer id : dbIdSet) {
            String dbIdKey = productCode + id + VariableTypeEnum.JDBC_CONFIG.name();
            if (redisService.hasKey(dbIdKey)) {
                log.info("变量已缓存。productCode : {}, dBId : {}", productCode, id);
                continue;
            } else {
                redisService.setKey(dbIdKey, String.valueOf(id), 500L, TimeUnit.MILLISECONDS);
            }
            ApiTestVariableVO apiTestVariableVO = apiTestRepository.getUniqueDbVariable(productCode, String.valueOf(id), sceneType.getCode(), VariableTypeEnum.JDBC_CONFIG, businessCode);
            if (null != apiTestVariableVO) {
                log.info("变量名称已存在。productCode : {}, dBId : {}", productCode, id);
                continue;
            }
            JSONObject physicalDbInfo = zbaseService.queryPhysicalDbInfo(String.valueOf(id));
            if (Objects.isNull(physicalDbInfo)) {
                log.error("获取数据库物理信息异常.{}", id);
                throw new ServiceException("获取数据库物理信息为空。请确认数据库是否正确");
            }
            JSONObject dbAccountInfo = zbaseService.queryDbAccountInfo(physicalDbInfo);
            if (Objects.isNull(dbAccountInfo) || !"SYS000".equals(dbAccountInfo.getString("statusCode"))) {
                log.error("获取数据库账户信息异常.{}, {}", physicalDbInfo, dbAccountInfo);
                throw new ServiceException("获取数据库账户信息异常。请确认数据库是否正确");
            }
            log.info("dbAccountInfo >>> {}", dbAccountInfo.toJSONString());
            SubVariableTypeEnum subVariableTypeEnum;
            if (StringUtils.isEmpty(dbAccountInfo.getString("dbType"))) {
                subVariableTypeEnum = SubVariableTypeEnum.MYSQL;
            } else {
                subVariableTypeEnum = dbAccountInfo.getString("dbType").toLowerCase().trim().equals("mysql") ? SubVariableTypeEnum.MYSQL : SubVariableTypeEnum.ORACLE;
            }
            String generateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
            AddApiTestVariableCommand command = new AddApiTestVariableCommand(generateId);
            command.setProductCode(productCode);
            command.setType(VariableTypeEnum.JDBC_CONFIG);
            command.setVariableName(String.valueOf(id));
            command.setVariableKey(String.valueOf(id));
            command.setVariableValue(String.valueOf(id));
            command.setSceneType(sceneType.getCode());
            command.setTransactor(user);
            command.setSubVariableType(subVariableTypeEnum);
            command.setLinkCode(businessCode);
            AddApiTestVariableEvent event = convert.convert(command);
            apiTestRepository.addApiTestVariable(event);
        }
    }

    public void deleteSceneInfo(DeleteSceneInfoCommand command) {
        log.info("deleteSceneInfo >>> {}", command.getAggregateId());
        SceneInfoEntityDO publishSceneInfo =
                apiTestRepository.queryLatestSceneInfo(command.getAggregateId(), SceneInfoStatusEnum.publish);
        SceneInfoEntityDO sceneInfo = new SceneInfoEntityDO();
        sceneInfo.setSceneCode(command.getAggregateId());
        sceneInfo.setEnable(SceneInfoEnableEnum.DELETED);
        sceneInfo.preUpdate(command);
        apiTestRepository.updateSceneInfoByCode(sceneInfo);
        SceneIndexVO sceneIndex = new SceneIndexVO();
        sceneIndex.setSceneIndexCode(command.getAggregateId());
        sceneIndex.setEnable(false);
        sceneIndex.preUpdate(command);
        apiTestRepository.updateSceneIndexByCode(sceneIndex);
        SYNC_API_TEST_EXECUTOR.execute(() -> {
            log.info("删除{}关联标签和登记库 >>> {}", command.getSceneType(), command.getAggregateId());
            tagRepository.removeSceneTag(command.getAggregateId(), Arrays.asList(SceneTagEnum.values()));
            // 删除场景需要删除自动化用例库
            if (!UseCaseFactoryTypeEnum.CREATE.equals(command.getSceneType())) {
                List<SceneInfoEntityDO> list = apiTestRepository.querySceneInfoByCode(command.getAggregateId());
                if (CollectionUtils.isEmpty(list)) {
                    return;
                }
                list.stream()
                        .map(SceneInfoEntityDO::getAutomaticSourceCode)
                        .filter(StringUtils::isNotEmpty)
                        .distinct().forEach(code -> {
                            DeleteAutomaticRecordCommand deleteAutomaticRecordCommand = new DeleteAutomaticRecordCommand(code);
                            deleteAutomaticRecordCommand.setCode(code);
                            deleteAutomaticRecordCommand.setTransactor(command.getTransactor());
                            automaticSourceRecordCommandDomainService.deleteAutomaticRecord(deleteAutomaticRecordCommand);
                        });
                removeSceneApiRelation(command.getAggregateId(), null);
            }
        });

        if (null != publishSceneInfo && SceneInfoEnableEnum.PUBLISHED.equals(publishSceneInfo.getEnable()) && UseCaseFactoryTypeEnum.CREATE.equals(command.getSceneType())) {
            SYNC_API_TEST_EXECUTOR.execute(() -> {
                syncSceneByPreData(command.getAggregateId(), null, PreDataMarkEnum.DELETE);
            });
        }
    }

    public EditSceneAuthenticationResp editSceneAuthenticationReq(EditSceneAuthenticationCommand command) {
        EditSceneAuthenticationResp resp = new EditSceneAuthenticationResp();

        if (redisService.hasKey(command.getAggregateId() + "_Auth")) {
            String value = redisService.getKey(command.getAggregateId() + "_Auth");
            JSONObject object = JSON.parseObject(value);
            if (!command.getTransactor().getUserId().equals(object.getLong("userId"))) {
                log.info("当前场景[{}][{}]正在编辑，同一时间同一场景只能单个用户编辑，请稍后再试", command.getAggregateId(), object.getString("userName"));
                resp.setIsEdit(false);
                resp.setCurrentUser(object.getString("userName"));
                return resp;
            }
        }
        log.info("鉴权通过，当前场景[{}]授权[{}]编辑", command.getAggregateId(), command.getTransactor().getUserName());
        String token = DigestUtil.md5Hex(String.valueOf(System.currentTimeMillis()));
        resp.setIsEdit(true);
        resp.setCurrentUser(command.getTransactor().getUserName());
        resp.setToken(token);
        redisService.setKey(token, command.getTransactor().getUserId().toString(), 4, TimeUnit.HOURS);
        return resp;
    }

    public void sceneInitial(SceneInitialReq req) {
        String aggregateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        log.info("场景图迁移初始化，开始执行！" + aggregateId);
        // 删除分组下所有场景
        apiTestRepository.deleteAllModuleScene(req.getProductCode());
        // 查出所有场景
        List<SceneInfoEntityDO> list = apiTestRepository.querySceneByProductCodeAndSceneType(req.getProductCode(), null);
        if (CollectionUtils.isNotEmpty(list)) {
            // 重新插入所有场景
            apiTestRepository.insertAllModuleScene(buildSceneIndexList(list, req.getProductCode()));
            log.info("场景图迁移初始化，执行结束！" + aggregateId);
        }
    }

    /**
     * 组装场景索引List
     */
    private List<SceneIndexVO> buildSceneIndexList(List<SceneInfoEntityDO> list, String productCode) {
        List<SceneIndexVO> indexList = new ArrayList<>();
        list.stream().forEach(scene -> {
            SceneIndexVO indexVO = new SceneIndexVO();
            indexVO.setSceneIndexCode(scene.getSceneCode());
            indexVO.setSceneIndexName(scene.getSceneName());
            indexVO.setProductCode(scene.getProductCode());
            indexVO.setParentCode("ALL");
            indexVO.setSceneIndexType(SceneIndexTypeEnum.SCENE);
            indexVO.setEnable(true);
            indexVO.setCreatorId(0L);
            indexVO.setCreator("system");
            indexVO.setModifierId(0L);
            indexVO.setModifier("system");
            indexVO.setGmtCreate(new Date());
            indexVO.setGmtModified(new Date());
            indexList.add(indexVO);
        });
        return indexList;
    }

    public void addSceneModule(AddSceneModuleCommand command) {
        log.info("addSceneModule >>> {}", JsonUtil.toJSON(command));
        AddSceneModuleEvent event = convert.convert(command);
        SceneIndexVO sceneIndexVO;
        if (StringUtils.isNotEmpty(command.getSceneType()) && command.getSceneType().equals(UseCaseFactoryTypeEnum.CREATE.name())) {
            sceneIndexVO = apiTestRepository.queryUniqueSceneModule(event.getProductCode(), event.getParentCode(), event.getSceneIndexName(), SceneIndexTypeEnum.MODULE, UseCaseFactoryTypeEnum.CREATE);
        } else {
            sceneIndexVO = apiTestRepository.queryUniqueSceneModule(event.getProductCode(), event.getParentCode(), event.getSceneIndexName(), SceneIndexTypeEnum.MODULE, UseCaseFactoryTypeEnum.SCENE);
        }
        if (null != sceneIndexVO) {
            throw new ServiceException("分组名称已存在");
        }
        apiTestRepository.insertSceneIndexSelective(convert.convert(event));
        //自动化用例库新增分组
        String parentCode = Strings.EMPTY;
        if (!command.getParentCode().equals(TestcaseGroupTypeEnum.ALL.name())) {
            SceneIndexVO parentIndexVO = apiTestRepository.querySceneIndexByCode(command.getParentCode());
            parentCode = null == parentIndexVO ? Strings.EMPTY : parentIndexVO.getTestcaseCode();
        }
        String testcaseModuleCode = testcaseCommandDomainService.addModuleBySceneIndex(command, parentCode);
        if (StringUtils.isNotBlank(testcaseModuleCode)) {
            SceneIndexVO toUpdate = new SceneIndexVO();
            toUpdate.setSceneIndexCode(command.getAggregateId());
            toUpdate.setTestcaseCode(testcaseModuleCode);
            toUpdate.preUpdate(command.getTransactor());
            apiTestRepository.updateSceneIndexByCode(toUpdate);
        }
        apply(event);
    }

    public void addPreDataModule(AddPreDataModuleCommand command) {
        log.info("addPreDataModule >>> {}", JsonUtil.toJSON(command));
        AddPreDataModuleEvent event = convert.convert(command);
        SceneIndexVO sceneIndexVO = apiTestRepository.queryUniqueSceneModule(event.getProductCode(), event.getParentCode(), event.getSceneIndexName(), SceneIndexTypeEnum.MODULE,
                UseCaseFactoryTypeEnum.CREATE);
        if (null != sceneIndexVO) {
            throw new ServiceException("分组名称已存在");
        }
        apiTestRepository.insertSceneIndexSelective(convert.convert(event));
        apply(event);
    }

    public void editSceneModule(EditSceneModuleCommand command) {
        log.info("editSceneModule >>> {}", JsonUtil.toJSON(command));
        EditSceneModuleEvent event = convert.convert(command);
        SceneIndexVO vo = apiTestRepository.querySceneIndexByCode(event.getCode());
        if (null == vo) {
            throw new ServiceException("分组或场景不存在");
        }
        if (vo.getSceneIndexName().equals(event.getSceneIndexName())) {
            throw new ServiceException(vo.getSceneIndexType().equals(SceneIndexTypeEnum.MODULE) ? "分组无变化，无需更新" : "场景无变化，无需更新");
        }
        SceneIndexVO sceneIndexVO = apiTestRepository.queryUniqueSceneModule(vo.getProductCode(), vo.getParentCode(), event.getSceneIndexName(), vo.getSceneIndexType(), UseCaseFactoryTypeEnum.SCENE);
        if (null != sceneIndexVO) {
            throw new ServiceException(vo.getSceneIndexType().equals(SceneIndexTypeEnum.MODULE) ? "分组已存在" : "场景已存在");
        }
        apiTestRepository.updateByExampleSelective(convert.convert(event));
        //更新自动化用例库分组名称
        if (vo.getSceneIndexType().equals(SceneIndexTypeEnum.MODULE)) {
            testcaseRepository.updateNameBySceneIndex(vo.getTestcaseCode(), event.getSceneIndexName(), command.getTransactor());
        }
        if (vo.getSceneIndexType().equals(SceneIndexTypeEnum.SCENE)) {
            EditSceneInfoCommand editSceneInfoCommand = convert.convertEditSceneInfo(command);
            // 修改场景图信息
            updateSceneBasicInfo(editSceneInfoCommand);
            // 修改登记库名字
            updateAutoSourceName(editSceneInfoCommand);
        }
        event.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        apply(event);
    }

    public void editPreDataModule(EditPreDataModuleCommand command) {
        log.info("editPreDataModule >>> {}", JsonUtil.toJSON(command));
        EditPreDataModuleEvent event = convert.convert(command);
        SceneIndexVO vo = apiTestRepository.querySceneIndexByCode(event.getCode());
        if (null == vo) {
            throw new ServiceException("造数不存在");
        }
        if (vo.getSceneIndexName().equals(event.getSceneIndexName())) {
            throw new ServiceException(vo.getSceneIndexType().equals(SceneIndexTypeEnum.MODULE) ? "分组无变化，无需更新" : "造数无变化，无需更新");
        }
        SceneIndexVO sceneIndexVO = apiTestRepository.queryUniqueSceneModule(vo.getProductCode(), vo.getParentCode(), event.getSceneIndexName(), vo.getSceneIndexType(), UseCaseFactoryTypeEnum.CREATE);
        if (null != sceneIndexVO) {
            throw new ServiceException(vo.getSceneIndexType().equals(SceneIndexTypeEnum.MODULE) ? "分组已存在" : "造数已存在");
        }
        apiTestRepository.updateByExampleSelective(convert.convert(event));
        if (vo.getSceneIndexType().equals(SceneIndexTypeEnum.SCENE)) {
            EditPreDataInfoCommand editPreDataInfoCommand = convert.convertEditPreDataInfo(command);
            // 修改造数图信息
            updateSceneBasicInfo(editPreDataInfoCommand);
        }
        event.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        apply(event);
    }

    public void moveSceneModule(MoveSceneModuleCommand command) {
        log.info("moveSceneModule >>> {}", JsonUtil.toJSON(command));
        MoveSceneModuleEvent event = convert.convert(command);
        if (!event.getParentCode().equals("ALL")) {
            SceneIndexVO targetVo = apiTestRepository.querySceneIndexByCode(event.getParentCode());
            if (null == targetVo || !targetVo.getEnable()) {
                throw new ServiceException("目标分组不存在");
            }
            if (targetVo.getSceneIndexType().equals(SceneIndexTypeEnum.SCENE)) {
                throw new ServiceException("目标为场景，不支持移动");
            }
            event.setParentTestcaseCode(targetVo.getTestcaseCode());
        } else {
            TestcaseEntityDO firstModule = getModuleByNameAndParent("", "用例工厂", command.getProductCode());
            if (null != firstModule && StringUtils.isNotBlank(firstModule.getCode())) {
                TestcaseEntityDO secondModule = getModuleByNameAndParent(firstModule.getCode(), "发布-场景", command.getProductCode());
                if (null != secondModule && StringUtils.isNotBlank(secondModule.getCode())) {
                    event.setParentTestcaseCode(secondModule.getCode());
                }
            }
        }
        SceneIndexVO moveVo = apiTestRepository.querySceneIndexByCode(event.getCode());
        if (null == moveVo || !moveVo.getEnable()) {
            throw new ServiceException("移动分组不存在");
        }
        if (moveVo.getParentCode().equals(event.getParentCode())) {
            throw new ServiceException("目标分组相同，无需移动");
        }
        if (moveVo.getSceneIndexCode().equals(event.getParentCode())) {
            throw new ServiceException("移动分组与目标分组相同，无法移动");
        }
        event.setSceneIndexType(moveVo.getSceneIndexType());
        event.setTestcaseCode(moveVo.getTestcaseCode());
        SceneIndexVO sceneIndexVO = apiTestRepository.queryUniqueSceneModule(event.getProductCode(), event.getParentCode(), moveVo.getSceneIndexName(), moveVo.getSceneIndexType(),
                UseCaseFactoryTypeEnum.SCENE);
        if (null != sceneIndexVO) {
            throw new ServiceException(moveVo.getSceneIndexType().equals(SceneIndexTypeEnum.SCENE) ? "场景名称已存在" : "分组名称已存在");
        }
        apiTestRepository.updateByExampleSelective(convert.convert(event));
        event.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        apply(event);
        //同步更新自动化用例库分组
        SYNC_API_TEST_EXECUTOR.execute(() -> testcaseCommandDomainService.handleMoveSceneModuleEvent(event));
    }

    public void movePreDataModule(MovePreDataModuleCommand command) {
        log.info("moveSceneModule >>> {}", JsonUtil.toJSON(command));
        MovePreDataModuleEvent event = convert.convert(command);
        if (!event.getParentCode().equals("ALL")) {
            SceneIndexVO targetVo = apiTestRepository.querySceneIndexByCode(event.getParentCode());
            if (null == targetVo || !targetVo.getEnable()) {
                throw new ServiceException("目标分组不存在");
            }
            if (targetVo.getSceneIndexType().equals(SceneIndexTypeEnum.SCENE)) {
                throw new ServiceException("目标为造数，不支持移动");
            }
        }
        SceneIndexVO moveVo = apiTestRepository.querySceneIndexByCode(event.getCode());
        if (null == moveVo || !moveVo.getEnable()) {
            throw new ServiceException("移动分组不存在");
        }
        if (moveVo.getParentCode().equals(event.getParentCode())) {
            throw new ServiceException("目标分组相同，无需移动");
        }
        if (moveVo.getSceneIndexCode().equals(event.getParentCode())) {
            throw new ServiceException("移动分组与目标分组相同，无法移动");
        }
        event.setSceneIndexType(moveVo.getSceneIndexType());
        SceneIndexVO sceneIndexVO = apiTestRepository.queryUniqueSceneModule(event.getProductCode(), event.getParentCode(), moveVo.getSceneIndexName(), moveVo.getSceneIndexType(),
                UseCaseFactoryTypeEnum.CREATE);
        if (null != sceneIndexVO) {
            throw new ServiceException(moveVo.getSceneIndexType().equals(SceneIndexTypeEnum.SCENE) ? "造数名称已存在" : "分组名称已存在");
        }
        apiTestRepository.updateByExampleSelective(convert.convert(event));
        event.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        apply(event);
    }

    public void deleteSceneModule(DeleteSceneModuleCommand command) {
        log.info("deleteSceneModule >>> {}", command.getAggregateId());
        SceneIndexVO sceneIndex = apiTestRepository.querySceneIndexByCode(command.getAggregateId());
        if (null == sceneIndex || !sceneIndex.getEnable()
                || !SceneIndexTypeEnum.MODULE.equals(sceneIndex.getSceneIndexType())) {
            return;
        }
        sceneIndex.setEnable(false);
        sceneIndex.preUpdate(command);
        apiTestRepository.updateSceneIndexByCode(sceneIndex);
        // 删除场景分组需要删除登记库分组
        if (!UseCaseFactoryTypeEnum.CREATE.equals(command.getSceneType())) {
            testcaseRepository.deleteModule(sceneIndex.getTestcaseCode(), command.getTransactor());
        }
        SYNC_API_TEST_EXECUTOR.execute(() -> {
            log.info("删除场景图分组关联子分组和场景图 >>> {}", command.getAggregateId());
            deleteSceneIndexByParentCode(command.getAggregateId(), command.getTransactor(), command.getSceneType());
        });
    }

    private void deleteSceneIndexByParentCode(String sceneIndexCode, User user, UseCaseFactoryTypeEnum type) {
        List<SceneIndexVO> list = apiTestRepository.querySceneIndexByParentCode(sceneIndexCode);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(sceneIndex -> {
            if (SceneIndexTypeEnum.MODULE.equals(sceneIndex.getSceneIndexType())) {
                sceneIndex.setEnable(false);
                sceneIndex.preUpdate(user);
                apiTestRepository.updateSceneIndexByCode(sceneIndex);
                deleteSceneIndexByParentCode(sceneIndex.getSceneIndexCode(), user, type);
            }
            if (SceneIndexTypeEnum.SCENE.equals(sceneIndex.getSceneIndexType())) {
                DeleteSceneInfoCommand command = new DeleteSceneInfoCommand(sceneIndex.getSceneIndexCode());
                command.setSceneType(type);
                command.setTransactor(user);
                deleteSceneInfo(command);
            }
        });
    }

    public void batchDeleteScene(BatchDeleteSceneCommand command) {
        log.info("batchDeleteScene >>> {}", JsonUtil.toJSON(command));
        if (CollectionUtils.isEmpty(command.getSceneCodeList())) {
            return;
        }
        for (String sceneCode : command.getSceneCodeList()) {
            // 删除造数需要判断是否超管和创建者
            if (UseCaseFactoryTypeEnum.CREATE.equals(command.getSceneType())) {
                SceneInfoEntityDO sceneDO = apiTestRepository.queryLatestSceneInfo(sceneCode, SceneInfoStatusEnum.edit);
                if (null == sceneDO) {
                    continue;
                }
                if (!apiTestQueryDomainService.creatorAndSuperManagerPermission(
                        command.getTransactor().getUserId(),
                        sceneDO.getCreatorId(), new ArrayList<>(command.getTransactor().getPermissions()))) {
                    continue;
                }
            }
            DeleteSceneInfoCommand deleteSceneInfoCommand = new DeleteSceneInfoCommand(sceneCode);
            deleteSceneInfoCommand.setTransactor(command.getTransactor());
            deleteSceneInfoCommand.setSceneType(command.getSceneType());
            deleteSceneInfo(deleteSceneInfoCommand);
        }
    }

    public void batchMoveScene(BatchMoveSceneCommand command) {
        log.info("batchMoveScene >>> {}", JsonUtil.toJSON(command));
        if (CollectionUtils.isEmpty(command.getSceneCodeList())) {
            return;
        }
        String targetTestcaseCode;
        if (!command.getParentCode().equals("ALL")) {
            SceneIndexVO targetVo = apiTestRepository.querySceneIndexByCode(command.getParentCode());
            if (null == targetVo || !targetVo.getEnable()) {
                throw new ServiceException("目标分组不存在");
            }
            if (targetVo.getSceneIndexType().equals(SceneIndexTypeEnum.SCENE)) {
                throw new ServiceException("目标为场景，不支持移动");
            }
            targetTestcaseCode = targetVo.getTestcaseCode();
        } else {
            targetTestcaseCode = StringUtils.EMPTY;
        }
        apiTestRepository.batchMoveScene(convert.convert(command));
        // 场景移动需要移动登记库分组
        if (!UseCaseFactoryTypeEnum.CREATE.equals(command.getSceneType())) {
            SYNC_API_TEST_EXECUTOR.execute(() -> command.getSceneCodeList().forEach(code -> {
                MoveSceneModuleEvent event = new MoveSceneModuleEvent();
                event.setCode(code);
                event.setParentTestcaseCode(targetTestcaseCode);
                event.setSceneIndexType(SceneIndexTypeEnum.SCENE);
                event.setTransactor(command.getTransactor());
                testcaseCommandDomainService.handleMoveSceneModuleEvent(event);
            }));
        }
    }

    public void batchExecute(UserInfo currentUser, SceneBatchExecuteReq req) {
        List<SceneInfoEntityDO> sceneList = apiTestRepository.queryPublishSceneAutoSource(req.getSceneCodeList());
        if (CollectionUtils.isEmpty(sceneList)) {
            return;
        }
        List<String> autoSourceCodes = sceneList.stream()
                .map(SceneInfoEntityDO::getAutomaticSourceCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(autoSourceCodes)) {
            // 查出登记库下所有用例
            List<TestcaseEntityDO> list = testcaseRepository.selectTestCaseByAutoSourceCodes(autoSourceCodes);
            if (CollectionUtils.isNotEmpty(list)) {
                List<String> caseCodes = list.stream().map(TestcaseEntityDO::getCode).collect(Collectors.toList());
                req.setTestcaseCodeList(caseCodes);
                automaticTaskCommandDomainService.executeAutoTask(currentUser, req);
            }
        }
    }

    public void batchAddVariable(BatchAddVariableCommand command) {
        log.info("batchAddVariable >>> {}", JsonUtil.toJSON(command));
        if (isDuplicate(command.getApiTestVariableList())) {
            throw new ServiceException("自定义变量不能重名");
        }
        if (isKeyNull(command.getApiTestVariableList())) {
            throw new ServiceException("自定义变量key不能为空");
        }
        if (isDuplicate(command.getApiTestHeaderList())) {
            throw new ServiceException("请求参数不能重名");
        }
        if (isKeyNull(command.getApiTestHeaderList())) {
            throw new ServiceException("请求参数key不能为空");
        }
        if (command.getSceneType().equals(UseCaseFactoryTypeEnum.CREATE) && CollectionUtil.isNotEmpty(command.getApiTestVariableList())) {
            List<String> reqVariableList = command.getApiTestVariableList().stream().map(BaseApiTestVariableReq::getVariableKey)
                    .collect(Collectors.toList());
            this.checkDataCenterVariableName(reqVariableList, command.getProductCode(), command.getLinkCode(), command.getType(), SubVariableTypeEnum.CUSTOM,
                    Arrays.asList(VariableUsageTypeEnum.INPUT, VariableUsageTypeEnum.OUTPUT));
        }
        Boolean customModifyFlag = this.addVariable(command, SubVariableTypeEnum.CUSTOM);
        Boolean headerModifyFlag = this.addVariable(command, SubVariableTypeEnum.HEADER);
        if (customModifyFlag || headerModifyFlag) {
            this.setModifySceneTag(command.getLinkCode(), SceneTagEnum.PRE_DATA_MODIFY);
        }
    }

    private void checkDataCenterVariableName(List<String> reqVariableList, String productCode, String linkCode, VariableTypeEnum type, SubVariableTypeEnum subVariableType,
                                             List<VariableUsageTypeEnum> variableUsageTypes) {
        List<ApiTestVariableVO> dbVariableVos = apiTestRepository.querySceneVariable(productCode, linkCode, type, subVariableType, variableUsageTypes);
        if (CollectionUtil.isEmpty(dbVariableVos)) {
            return;
        }
        List<String> dbVariableList = dbVariableVos.stream().map(ApiTestVariableVO::getVariableKey)
                .collect(Collectors.toList());
        Set<String> duplicateList = new HashSet<>();
        reqVariableList.stream().filter(dbVariableList::contains).forEach(duplicateList::add);
        if (CollectionUtil.isNotEmpty(duplicateList)) {
            throw new ServiceException("新增自定义或出入参变量" + JSON.toJSONString(duplicateList) + "已存在，请重命名");
        }
    }

    private Boolean addVariable(BatchAddVariableCommand command, SubVariableTypeEnum subVariableType) {
        List<ApiTestVariableVO> addList = new ArrayList<>();
        List<ApiTestVariableVO> updateList = new ArrayList<>();
        List<ApiTestVariableVO> deleteList = new ArrayList<>();
        // 数据库中的变量list
        List<ApiTestVariableVO> lastVariables = apiTestRepository.querySceneVariable(
                command.getProductCode(), command.getLinkCode(), command.getType(), subVariableType, Arrays.asList(VariableUsageTypeEnum.UNKNOWN));
        // 新变量list
        List<BaseApiTestVariableReq> newVariables = new ArrayList<>();
        if (SubVariableTypeEnum.CUSTOM.equals(subVariableType)) {
            newVariables = command.getApiTestVariableList();
        } else if (SubVariableTypeEnum.HEADER.equals(subVariableType)) {
            newVariables = command.getApiTestHeaderList();
        }
        if (CollectionUtils.isEmpty(lastVariables)) {
            for (BaseApiTestVariableReq req : newVariables) {
                addList.add(this.buildApiTestVariableVO(command, subVariableType,
                        req.getVariableKey(), req.getVariableValue(), req.getVariableDesc()));
            }
        } else {
            Map<String, ApiTestVariableVO> lastVariablesMap = new HashMap<>();
            for (ApiTestVariableVO variableVO : lastVariables) {
                lastVariablesMap.put(variableVO.getVariableKey(), variableVO);
            }
            if (MapUtils.isNotEmpty(lastVariablesMap)) {
                for (BaseApiTestVariableReq newVariable : newVariables) {
                    // 修改list
                    if (lastVariablesMap.containsKey(newVariable.getVariableKey())) {
                        ApiTestVariableVO lastVariable = lastVariablesMap.get(newVariable.getVariableKey());
                        if (!lastVariable.getVariableValue().equals(newVariable.getVariableValue()) ||
                                !lastVariable.getVariableDesc().equals(newVariable.getVariableDesc())) {
                            lastVariable.setVariableValue(newVariable.getVariableValue());
                            lastVariable.setVariableDesc(newVariable.getVariableDesc());
                            lastVariable.setModifier(command.getTransactor().getUserName());
                            lastVariable.setModifierId(command.getTransactor().getUserId());
                            lastVariable.setGmtModified(new Date());
                            lastVariable.setVariableStatus(true);
                            lastVariable.setSceneType(command.getSceneType().getCode());
                            updateList.add(lastVariable);
                        }
                        lastVariablesMap.remove(newVariable.getVariableKey());
                    } else {
                        // 新增list
                        addList.add(this.buildApiTestVariableVO(command, subVariableType, newVariable.getVariableKey(),
                                newVariable.getVariableValue(), newVariable.getVariableDesc()));
                    }
                }
            }
            if (MapUtils.isNotEmpty(lastVariablesMap)) {
                // 删除list
                lastVariablesMap.forEach((key, vo) -> {
                    vo.setEnable(false);
                    deleteList.add(vo);
                });
            }
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            apiTestRepository.batchInsertVariable(addList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            this.deleteVariableRedisKey(command, updateList);
            apiTestRepository.batchUpdateVariable(updateList);
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            this.deleteVariableRedisKey(command, deleteList);
            apiTestRepository.batchUpdateVariable(deleteList);
        }
        List<ApiTestVariableVO> changeList = new ArrayList<>(addList);
        changeList.addAll(updateList);
        for (ApiTestVariableVO variable : changeList) {
            if (StringUtils.isEmpty(variable.getVariableValue()) || !variable.getVariableValue().startsWith("${__dataExclusiveLock")) {
                continue;
            }
            linkMapDomainService.initCacheVariable(command.getProductCode(), variable.getVariableKey(), variable.getVariableValue());
        }
        boolean isNeedSaveTags = false;
        if (CollectionUtils.isNotEmpty(addList)
                || CollectionUtils.isNotEmpty(updateList) || CollectionUtils.isNotEmpty(deleteList)) {
            isNeedSaveTags = true;
        }
        return isNeedSaveTags;
    }

    private void deleteVariableRedisKey(BatchAddVariableCommand command, List<ApiTestVariableVO> list) {
        for (ApiTestVariableVO variable : list) {
            if (StringUtils.isEmpty(variable.getVariableValue()) || !variable.getVariableValue().startsWith("${__dataExclusiveLock")) {
                continue;
            }
            try {
                redisService.delete(command.getProductCode() + "_" + variable.getVariableKey());
                String newVariableValue = variable.getVariableValue().substring(22, variable.getVariableValue().length() - 2);
                JSONArray variableArr = JSONObject.parseArray(newVariableValue);
                for (int i = 0; i < variableArr.size(); i++) {
                    redisService.delete(command.getProductCode() + "_" + variable.getVariableKey() + "_" + i);
                }
            } catch (Exception e) {
                log.error("解析变量值异常。", e);
                throw new ServiceException("解析变量值异常");
            }
        }
    }

    private ApiTestVariableVO buildApiTestVariableVO(BatchAddVariableCommand command, SubVariableTypeEnum typeEnum,
                                                     String variableKey, String variableValue, String variableDesc) {
        ApiTestVariableVO entityVO = new ApiTestVariableVO();
        entityVO.setVariableCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        entityVO.setProductCode(command.getProductCode());
        entityVO.setProductName(command.getProductName());
        entityVO.setLinkCode(command.getLinkCode());
        entityVO.setType(command.getType());
        entityVO.setVariableName(variableKey);
        entityVO.setVariableKey(variableKey);
        entityVO.setVariableValue(variableValue);
        entityVO.setRequiredStatus(Boolean.FALSE);
        entityVO.setUsageType(VariableUsageTypeEnum.UNKNOWN);
        entityVO.setSceneType(command.getSceneType().getCode());
        entityVO.preCreate(command.getTransactor());
        entityVO.setVariableDesc(StringUtils.isNotEmpty(variableDesc) ? variableDesc : "");
        entityVO.setSubVariableType(typeEnum);
        entityVO.setEnable(true);
        entityVO.setLoginValidTime(0);
        entityVO.setVariableStatus(true);
        return entityVO;
    }

    public static boolean isDuplicate(List<BaseApiTestVariableReq> list) {
        List<String> stringList = list.stream().map(BaseApiTestVariableReq::getVariableKey)
                .collect(Collectors.toList());
        long count = stringList.stream().distinct().count();
        if (stringList.size() == count) {
            return false;
        } else {
            return true;
        }
    }

    public static boolean isKeyNull(List<BaseApiTestVariableReq> list) {
        List<BaseApiTestVariableReq> nullKeyList = list.stream()
                .filter(t -> StringUtils.isBlank(t.getVariableKey())).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(nullKeyList);
    }

    public void updateSceneStepRecord(UpdateSceneStepRecordCommand command) {
        log.info("updateSceneStepRecord >>> {}", JsonUtil.toJSON(command));
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setId(command.getSceneId());
        if (apiTestRepository.querySceneInfoByPrimaryKey(entityDO) == null) {
            log.warn("场景主键不存在。id : {}", command.getSceneId());
            return;
        }
        entityDO.setStepRecord(command.getStepRecord());
        apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
        log.info("更新场景阶段记录完成. id : {}", command.getSceneId());
    }

    /**
     * 判断当前造数相关操作是否有权限
     */
    public boolean hasPermission(User user, String sceneCode, String productCode) {
        if (StringUtils.isBlank(productCode)) {
            throw new ServiceException("产品code不能为空！");
        }
        SceneInfoEntityDO sceneDO = apiTestRepository.queryLatestSceneInfo(sceneCode, SceneInfoStatusEnum.edit);
        if (null == sceneDO) {
            throw new ServiceException("当前造数不存在！");
        }
        boolean isSuperAndCreator = apiTestQueryDomainService.creatorAndSuperManagerPermission(user.getUserId(), sceneDO.getCreatorId(), new ArrayList<>(user.getPermissions()));
        boolean isProductTestMember = apiTestQueryDomainService.productTestMembersPermission(user.getUserId(), productCode);
        return isSuperAndCreator || isProductTestMember;
    }

    public void batchAddPreDataVariable(BatchAddPreDataVariableCommand command) {
        log.info("batchAddPreDataVariable >>> {}", JsonUtil.toJSON(command));
        apiTestRepository.batchDeleteVariable(command.getProductCode(), command.getLinkCode(), command.getType(), Arrays.asList(VariableUsageTypeEnum.INPUT,
                VariableUsageTypeEnum.OUTPUT));
        if (CollectionUtils.isEmpty(command.getInputParameter()) && CollectionUtils.isEmpty(command.getOutputParameter())) {
            return;
        }
        if (command.checkDuplicateKey()) {
            throw new ServiceException("自定义变量不能重名");
        }
        List<String> reqVariableList = command.getParameterList().stream().map(BasePreDataVariableReq::getVariableKey).collect(Collectors.toList());
        this.checkDataCenterVariableName(reqVariableList, command.getProductCode(), command.getLinkCode(), command.getType(), SubVariableTypeEnum.CUSTOM, Arrays.asList(VariableUsageTypeEnum.UNKNOWN));
        apiTestRepository.batchAddPreDataVariable(command);
    }

    /**
     * 校验造数变量字段长度
     *
     * @param command {@link BatchAddPreDataVariableCommand}
     */
    public void validParam(BatchAddPreDataVariableCommand command) {
        if (CollectionUtils.isNotEmpty(command.getInputParameter())) {
            command.getInputParameter().forEach(this::doValid);
        }
        if (CollectionUtils.isNotEmpty(command.getOutputParameter())) {
            command.getOutputParameter().forEach(this::doValid);
        }
    }

    private void doValid(BasePreDataVariableReq item) {
        if (StringUtils.isBlank(item.getVariableKey())) {
            throw new ServiceException("变量名称不能为空！");
        }
        if (StringUtils.isBlank(item.getVariableName())) {
            throw new ServiceException("显示名称不能为空！");
        }
        if (item.getVariableKey().length() > 60) {
            throw new ServiceException("变量名称最多60个字符");
        }
        if (item.getVariableName().length() > 60) {
            throw new ServiceException("显示名称最多60个字符");
        }
        if (StringUtils.isNotBlank(item.getVariableValue()) && item.getVariableValue().length() > 60) {
            throw new ServiceException("变量值最多60个字符");
        }
    }

    public void sharePreData(SharePreDataCommand command) {
        log.info("SharePreDataCommand >>> {}", JSON.toJSONString(command));
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setSceneCode(command.getAggregateId());
        entityDO.setShareStatus(command.getShareStatus());
        entityDO.preUpdate(command);
        apiTestRepository.updateSceneInfoByCode(entityDO);
        if (!command.getShareStatus()) {
            SYNC_API_TEST_EXECUTOR.execute(() -> {
                syncSceneByPreData(command.getAggregateId(), null, PreDataMarkEnum.CANCEL_SHARE);
            });
        } else {
            SYNC_API_TEST_EXECUTOR.execute(() -> {
                syncDeleteSceneByPreData(command.getAggregateId(), PreDataMarkEnum.CANCEL_SHARE);
            });
        }
        apply(convert.convert(command));
    }

    @Async
    public void batchSharePreData(BatchSharePreDataCommand batchCommand) {
        log.info("BatchSharePreDataCommand >>> {}", JSON.toJSONString(batchCommand));
        if (CollectionUtils.isEmpty(batchCommand.getSceneCodeList())) {
            return;
        }
        batchCommand.getSceneCodeList().forEach(sceneCode -> {
            SharePreDataCommand command = new SharePreDataCommand(sceneCode);
            command.setShareStatus(batchCommand.getShareStatus());
            command.setTransactor(batchCommand.getTransactor());
            this.sharePreData(command);
        });
    }

    public void copyModule(SceneModuleCopyCommand command) {
        log.info("SceneModuleCopyCommand >>> {}", JSON.toJSONString(command));
        SceneIndexVO indexVO = apiTestRepository.querySceneIndexByCode(command.getModuleCode());
        if (null == indexVO || !SceneIndexTypeEnum.MODULE.equals(indexVO.getSceneIndexType())) {
            throw new ServiceException("复制失败：复制的该分组不存在！");
        }
        String newModuleName = "【复制" + System.currentTimeMillis() + "】" + indexVO.getSceneIndexName();
        if (newModuleName.length() > 120) {
            throw new ServiceException("复制失败：复制的该分组名字过长！");
        }
        SceneModuleQueryReq queryReq = new SceneModuleQueryReq();
        queryReq.setProductCode(command.getProductCode());
        queryReq.setSceneType(command.getSceneType().name());
        List<SceneIndexVO> list = apiTestRepository.queryAllSceneModule(queryReq);
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, List<SceneIndexVO>> map = list.stream().collect(Collectors.groupingBy(SceneIndexVO::getParentCode));
            String aggregateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
            List<SceneIndexVO> copyList = new ArrayList<>();
            if (MapUtils.isNotEmpty(map)) {
                copyList.addAll(getChildrenList(map, command.getModuleCode(), aggregateId, command.getTransactor()));
            }
            indexVO.setSceneIndexCode(aggregateId);
            indexVO.setSceneIndexName(newModuleName);
            indexVO.preCreate(command.getTransactor());
            indexVO.preUpdate(command.getTransactor());
            copyList.add(indexVO);
            COPY_MODULE_EXECUTOR.execute(() -> {
                batchCopyModule(command, copyList);
            });
        }
    }

    private void batchCopyModule(SceneModuleCopyCommand command, List<SceneIndexVO> copyList) {
        // add分组
        List<SceneIndexVO> moduleList = copyList.stream().filter(
                vo -> SceneIndexTypeEnum.MODULE.equals(vo.getSceneIndexType())).collect(Collectors.toList());
        this.batchAddSceneModule(moduleList, command.getProductCode(), command.getTransactor());

        // add场景
        List<SceneIndexVO> sceneList = copyList.stream().filter(
                vo -> SceneIndexTypeEnum.SCENE.equals(vo.getSceneIndexType())).collect(Collectors.toList());
        this.batchCopyScene(sceneList, command.getProductCode(), command.getProductName(), command.getTransactor());
    }

    private void batchCopyScene(List<SceneIndexVO> list, String productCode,
                                String productName, User user) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (SceneIndexVO vo : list) {
            String oldSceneCode = vo.getSceneIndexCode();
            // copy场景
            CopySceneInfoCommand copySceneInfoCommand = new CopySceneInfoCommand(vo.getSceneIndexCode());
            copySceneInfoCommand.setParentCode(vo.getParentCode());
            copySceneInfoCommand.setSceneCode(vo.getSceneIndexCode());
            copySceneInfoCommand.setSceneName(vo.getSceneIndexName());
            copySceneInfoCommand.setSceneInfoDesc(vo.getSceneInfoDesc());
            copySceneInfoCommand.setSceneType(UseCaseFactoryTypeEnum.SCENE);
            if (null != vo.getSceneType() && vo.getSceneType() == 2) {
                copySceneInfoCommand.setSceneType(UseCaseFactoryTypeEnum.CREATE);
            }
            copySceneInfoCommand.setTransactor(user);
            try {
                vo.setSceneIndexCode(this.copySceneInfoReq(copySceneInfoCommand));
            } catch (Exception e) {
                log.error("复制分组-复制场景异常：" + e.getMessage());
                // 复制场景异常，不去复制变量了
                continue;
            }
            // copy变量
            QuerySceneVariableReq req = new QuerySceneVariableReq();
            req.setLinkCode(oldSceneCode);
            req.setProductCode(productCode);
            req.setSubVariableType(8);
            req.setType(VariableTypeEnum.VARIABLE);
            List<BaseApiTestVariableResp> variableList = apiTestQueryDomainService.querySceneVariable(req);
            req.setSubVariableType(11);
            List<BaseApiTestVariableResp> headerList = apiTestQueryDomainService.querySceneVariable(req);
            BatchAddVariableCommand batchAddVariableCommand = new BatchAddVariableCommand(vo.getSceneIndexCode());
            batchAddVariableCommand.setProductCode(productCode);
            batchAddVariableCommand.setProductName(productName);
            batchAddVariableCommand.setApiTestVariableList(convert.convertList(variableList));
            batchAddVariableCommand.setApiTestHeaderList(convert.convertList(headerList));
            batchAddVariableCommand.setType(VariableTypeEnum.VARIABLE);
            batchAddVariableCommand.setLinkCode(vo.getSceneIndexCode());
            batchAddVariableCommand.setTransactor(user);
            batchAddVariableCommand.setSceneType(UseCaseFactoryTypeEnum.SCENE);
            if (null != vo.getSceneType() && vo.getSceneType() == 2) {
                batchAddVariableCommand.setSceneType(UseCaseFactoryTypeEnum.CREATE);
            }
            try {
                this.batchAddVariable(batchAddVariableCommand);
            } catch (Exception e) {
                log.error("复制分组-添加变量异常：" + e.getMessage());
            }
        }
    }

    private void batchAddSceneModule(List<SceneIndexVO> list, String productCode, User user) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (SceneIndexVO vo : list) {
            AddSceneModuleCommand addSceneModuleCommand = new AddSceneModuleCommand(vo.getSceneIndexCode());
            addSceneModuleCommand.setParentCode(vo.getParentCode());
            addSceneModuleCommand.setProductCode(productCode);
            addSceneModuleCommand.setSceneIndexName(vo.getSceneIndexName());
            addSceneModuleCommand.setSceneType("SCENE");
            if (null != vo.getSceneType() && vo.getSceneType() == 2) {
                addSceneModuleCommand.setSceneType("CREATE");
            }
            addSceneModuleCommand.setTransactor(user);
            try {
                this.addSceneModule(addSceneModuleCommand);
            } catch (Exception e) {
                log.error("复制分组-添加分组异常：" + e.getMessage());
            }
        }
    }

    private List<SceneIndexVO> getChildrenList(Map<String, List<SceneIndexVO>> map,
                                               String moduleCode, String parentCode, User user) {
        List<SceneIndexVO> copyList = new ArrayList<>();
        List<SceneIndexVO> sceneModule = map.get(moduleCode);
        if (CollectionUtils.isNotEmpty(sceneModule)) {
            for (SceneIndexVO vo : sceneModule) {
                String newName = "【复制" + System.currentTimeMillis() + "】" + vo.getSceneIndexName();
                if (newName.length() > 120) {
                    if (SceneIndexTypeEnum.MODULE.equals(vo.getSceneIndexType())) {
                        throw new ServiceException("复制失败：分组" + vo.getSceneIndexName() + "名字过长！");
                    }
                    if (SceneIndexTypeEnum.SCENE.equals(vo.getSceneIndexType())) {
                        throw new ServiceException("复制失败：场景" + vo.getSceneIndexName() + "名字过长！");
                    }
                }
                vo.setSceneIndexName(newName);
                vo.preCreate(user);
                vo.preUpdate(user);
                vo.setParentCode(parentCode);
                if (SceneIndexTypeEnum.MODULE.equals(vo.getSceneIndexType())) {
                    String aggregateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
                    copyList.addAll(getChildrenList(map, vo.getSceneIndexCode(), aggregateId, user));
                    vo.setSceneIndexCode(aggregateId);
                }
                copyList.add(vo);
            }
        }
        return copyList;
    }

    public void dbAuthorize(DbAuthorizeCommand command) {
        log.info("添加数据库授权，DbAuthorizeCommand：{}", JSON.toJSONString(command));
        DbAuthorizeEvent event = convert.convert(command);
        List<SceneDatabaseAuthorizeEntityDO> list = new ArrayList<>();
        event.getAuthorizeProductList().forEach(product -> {
            if (command.getProductCode().equals(product.getCode())) {
                throw new ServiceException("授权产品中不能包含当前产品！");
            }
            event.getAuthorizeDbList().forEach(vo -> {
                SceneDatabaseAuthorizeEntityDO entityDO = new SceneDatabaseAuthorizeEntityDO();
                entityDO.setAuthorizeCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                entityDO.setProductCode(command.getProductCode());
                entityDO.setAuthorizeProductCode(product.getCode());
                entityDO.setAuthorizeProductName(product.getName());
                entityDO.setDbId(vo.getId());
                entityDO.setDbName(vo.getDbName());
                entityDO.setPhysicDbName(vo.getPhysicDbName());
                entityDO.setEnable(true);
                entityDO.setCreator(command.getTransactor().getUserName());
                entityDO.setCreatorId(command.getTransactor().getUserId());
                entityDO.setModifier(command.getTransactor().getUserName());
                entityDO.setModifierId(command.getTransactor().getUserId());
                list.add(entityDO);
            });
        });
        apiTestRepository.addSceneDbAuthorize(list);
        apply(event);
    }

    public void dbRevoke(ApiTestRevokeCommand command) {
        log.info("取消数据库授权，ApiTestRevokeCommand：{}", JSON.toJSONString(command));
        ApiTestRevokeEvent event = convert.convert(command);
        event.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        apiTestRepository.dbRevoke(event.getProductCode(), event.getAuthorizeProductCode());
        apply(event);
    }

    /**
     * 保存调试日志
     *
     * @param command
     */
    @Async
    public void saveDebugRecord(SaveDebugInfoCommand command) {
        try {
            //保存日志文件
            String bucket = "autojmx";
            String filePath = "debug/" + command.getProductCode() + "-" + command.getSceneCode() + "/debug.json";
            List<String> logList = redisService.getListRange("debugLogAll-" + command.getTaskId(), 0, -1);
            log.info("saveDebugRecord_rangeList:{}", JSONObject.toJSONString(logList));
            DebugInfoVO vo = DebugInfoVO.buildNodeInfo(command, logList, buildLinkBaseInfo(command));
            if (Objects.isNull(vo)) {
                log.info("saveDebugRecord_DebugInfoVO_is_null_sceneCode:{}", command.getSceneCode());
                return;
            }
            boolean uploadFlag = ztoOssService.createObject(bucket, filePath, JSONObject.toJSONString(vo));
            if (!uploadFlag) {
                log.info("saveDebugRecord_upload_fail_sceneCode:{}", command.getSceneCode());
                return;
            }

            //更新数据库记录
            command.setBucketName(bucket);
            command.setLogOssPath(filePath);
            if (StringUtils.isBlank(command.getRecordCode())) {
                command.setRecordCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                tmSceneDebugRecordRepository.insertSelective(command);
            } else {
                tmSceneDebugRecordRepository.updateSelective(command);
            }

            //清缓存
            redisService.delete(command.getProductCode() + "-" + command.getSceneCode() + "-log");
            redisService.delete(command.getProductCode() + "-" + command.getSceneCode() + "-link");
            redisService.delete("debugLogAll-" + command.getTaskId());
        } catch (Exception e) {
            log.error("saveDebugRecord_error:{}", e.getMessage());
        }
    }

    public void saveApiGlobalConfiguration(AddApiGlobalConfigurationCommand command) {
        List<ApiGlobalConfigurationEntityDO> apiGlobalConfigurationEntityDOList = new ArrayList<>();
        List<ApiGlobalConfigurationVO> apiGlobalConfigurationVOList = command.getApiGlobalConfigurationVOList();

        if (CollectionUtils.isEmpty(apiGlobalConfigurationVOList)) {
            throw new ServiceException("类型、应用范围、断言需必填");
        }
        for (ApiGlobalConfigurationVO apiGlobalConfigurationVO : apiGlobalConfigurationVOList) {
            List<ApiConfigVO> apiConfigVOList = apiGlobalConfigurationVO.getApiConfigVOList();
            if (CollectionUtils.isEmpty(apiConfigVOList)) {
                continue;
            }
            for (ApiConfigVO apiConfigVO : apiConfigVOList) {
                ApiGlobalConfigurationEntityDO apiGlobalConfigurationEntityDO = new ApiGlobalConfigurationEntityDO();
                apiGlobalConfigurationEntityDO.setApiConfigCode(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
                apiGlobalConfigurationEntityDO.setProductCode(command.getProductCode());
                apiGlobalConfigurationEntityDO.setApiConfigType(apiGlobalConfigurationVO.getApiConfigType());
                apiGlobalConfigurationEntityDO.setEnable(apiGlobalConfigurationVO.getEnable());
                apiGlobalConfigurationEntityDO.setApiConfigScope(apiConfigVO.getApiConfigScope());
                apiGlobalConfigurationEntityDO.setApiConfigValue(apiConfigVO.getApiConfigValue());
                apiGlobalConfigurationEntityDO.setApiConfigAssert(apiConfigVO.getAsserts());
                apiGlobalConfigurationEntityDO.setCreatorId(command.getTransactor().getUserId());
                apiGlobalConfigurationEntityDO.setGmtCreate(new Date());
                apiGlobalConfigurationEntityDO.setCreator(command.getTransactor().getUserName());
                apiGlobalConfigurationEntityDOList.add(apiGlobalConfigurationEntityDO);
            }
        }
        apiGlobalConfigurationRepository.deleteApiGlobalConfigurationByProductCode(command.getProductCode(), command.getTransactor());
        if (CollectionUtil.isNotEmpty(apiGlobalConfigurationEntityDOList)) {
            apiGlobalConfigurationRepository.batchInsertApiGlobalConfiguration(apiGlobalConfigurationEntityDOList);
        }
    }

    public void favoritesPreData(FavoritesPreDataCommand command) {
        log.info("FavoritesPreDataCommand >>> {}", JSON.toJSONString(command));
        List<SceneCollectionEntityDO> sharePreDataSceneAndModuleByProduct = getSharePreDataSceneAndModuleByProduct(command);
        if (CollectionUtil.isEmpty(sharePreDataSceneAndModuleByProduct)) {
            return;
        }
        List<SceneCollectionEntityDO> collect = sharePreDataSceneAndModuleByProduct.stream().distinct().collect(Collectors.toList());
        if (command.getFavoritesStatus()) {
            tmSceneCollectionRepository.saveFavoritesPreData(collect);
        } else {
            if (command.getType().equals("MODULE") && command.getProductCode().equals(command.getAggregateId())) {
                tmSceneCollectionRepository.deleteFavoritesPreDataByProductCode(command.getProductCode(), command.getTransactor());
            } else {
                tmSceneCollectionRepository.deleteFavoritesPreData(collect, command.getTransactor());
            }
        }
    }

    private List<SceneCollectionEntityDO> getSharePreDataSceneAndModuleByProduct(FavoritesPreDataCommand command) {
        List<SceneCollectionEntityDO> result = new ArrayList<>();
        SharedSceneModuleQueryReq sharedSceneModuleQueryReq = new SharedSceneModuleQueryReq();
        sharedSceneModuleQueryReq.setProductCode(command.getProductCode());
        sharedSceneModuleQueryReq.setShareProductFlag(false);
        List<SceneIndexVO> preDataList = apiTestRepository.querySharedPreData(sharedSceneModuleQueryReq);
        List<SceneIndexVO> moduleList = apiTestRepository.querySharedPreDataModuleByProduct(Collections.singletonList(command.getProductCode()));
        if (CollectionUtil.isEmpty(preDataList)) {
            return Collections.emptyList();
        }
        List<SceneIndexVO> preDataModuleList = new ArrayList<>();
        if (command.getType().equals("SCENE")) {
            List<SceneIndexVO> collect = preDataList.stream().distinct()
                    .filter(code -> code.getSceneIndexCode().equals(command.getAggregateId()))
                    .collect(Collectors.toList());
            this.traverseUpToRootModule(moduleList, collect, preDataModuleList);
            if (CollectionUtil.isEmpty(collect)) {
                return Collections.emptyList();
            }
            if (command.getFavoritesStatus()) {
                collect.forEach(x -> result.add(convertSceneCollectionEntityDO(x.getSceneIndexCode(), command.getProductCode(), 2, command.getTransactor())));
                if (CollectionUtil.isNotEmpty(preDataModuleList)) {
                    preDataModuleList.forEach(x -> result.add(convertSceneCollectionEntityDO(x.getSceneIndexCode(), x.getProductCode(), 1, command.getTransactor())));
                }
                return result;
            } else {
                collect.forEach(x -> result.add(convertSceneCollectionEntityDO(x.getSceneIndexCode(), command.getProductCode(), 2, command.getTransactor())));
                return result;
            }
        }
        if (command.getType().equals("MODULE")) {
            if (command.getProductCode().equals(command.getAggregateId())) {
                //点击产品单独保存产品数据
                this.traverseUpToRootModule(moduleList, preDataList, preDataModuleList);
                preDataList.forEach(x -> result.add(convertSceneCollectionEntityDO(x.getSceneIndexCode(), x.getProductCode(), 2, command.getTransactor())));
                if (CollectionUtil.isNotEmpty(preDataModuleList)) {
                    preDataModuleList.forEach(x -> result.add(convertSceneCollectionEntityDO(x.getSceneIndexCode(), x.getProductCode(), 1, command.getTransactor())));
                }
                return result;
            } else {
                List<SceneIndexVO> moduleCodes = moduleList.stream().filter(vo -> vo.getSceneIndexCode().equals(command.getAggregateId())).distinct().collect(Collectors.toList());
                traverseDownToLeafModules(moduleList, moduleCodes.get(0), preDataModuleList);
                List<SceneIndexVO> newPreDataList = new ArrayList<>();
                List<SceneIndexVO> newPreDataModuleList = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(preDataModuleList)) {
                    Map<String, SceneIndexVO> moduleMap = new HashMap<>();
                    for (SceneIndexVO preDataModuleVO : preDataModuleList) {
                        moduleMap.put(preDataModuleVO.getSceneIndexCode(), preDataModuleVO);
                    }
                    for (SceneIndexVO preDataVO : preDataList) {
                        String parentCode = preDataVO.getParentCode();
                        if (moduleMap.containsKey(parentCode)) {
                            newPreDataList.add(preDataVO);
                        }
                    }
                }
                if (command.getFavoritesStatus()) {
                    this.traverseUpToRootModule(moduleList, newPreDataList, newPreDataModuleList);
                } else {
                    newPreDataModuleList = preDataModuleList;
                }
                newPreDataList.forEach(x -> result.add(convertSceneCollectionEntityDO(x.getSceneIndexCode(), x.getProductCode(), 2, command.getTransactor())));
                if (CollectionUtil.isNotEmpty(newPreDataModuleList)) {
                    newPreDataModuleList.forEach(x -> result.add(convertSceneCollectionEntityDO(x.getSceneIndexCode(), x.getProductCode(), 1, command.getTransactor())));
                }
                return result;
            }
        }
        return Collections.emptyList();
    }

    private static SceneCollectionEntityDO convertSceneCollectionEntityDO(String sceneIndexCode, String productCode, Integer type, User user) {
        SceneCollectionEntityDO sceneCollectionEntityDO = new SceneCollectionEntityDO();
        sceneCollectionEntityDO.setSceneIndexCode(sceneIndexCode);
        sceneCollectionEntityDO.setProductCode(productCode);
        sceneCollectionEntityDO.setFavoriteType(type);
        sceneCollectionEntityDO.setCreatorId(user.getUserId());
        sceneCollectionEntityDO.setCreator(user.getUserName());
        sceneCollectionEntityDO.setGmtCreate(new Date());
        sceneCollectionEntityDO.setGmtModified(new Date());
        sceneCollectionEntityDO.setModifier(user.getUserName());
        sceneCollectionEntityDO.setModifierId(user.getUserId());
        return sceneCollectionEntityDO;
    }

    /**
     * 向下递归查询所有模块与造数
     *
     * @param moduleList
     * @param module
     * @param preDataModuleList
     */
    private void traverseDownToLeafModules(List<SceneIndexVO> moduleList, SceneIndexVO module, List<SceneIndexVO> preDataModuleList) {
        preDataModuleList.add(module);
        if (CollectionUtil.isEmpty(moduleList)) {
            return;
        }
        for (SceneIndexVO sceneIndexVO : moduleList) {
            if (sceneIndexVO.getParentCode().equals(module.getSceneIndexCode())) {
                traverseDownToLeafModules(moduleList, sceneIndexVO, preDataModuleList);
            }
        }
    }

    /**
     * 向上递归查询所有模块
     *
     * @param moduleList
     * @param children
     * @param preDataModuleList
     */
    private void traverseUpToRootModule(List<SceneIndexVO> moduleList, List<SceneIndexVO> children, List<SceneIndexVO> preDataModuleList) {
        List<SceneIndexVO> parentList = moduleList.stream().filter(module -> children.stream().anyMatch(child -> module.getSceneIndexCode().equals(child.getParentCode()))).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(parentList)) {
            return;
        }
        preDataModuleList.addAll(parentList);
        this.traverseUpToRootModule(moduleList, parentList, preDataModuleList);
    }

    /**
     * 每次回调，保存节点结果
     *
     * @param taskId
     * @param nodeCode
     * @param nodeInfo
     */
    private void saveDebugNodeInfo(String taskId, String nodeCode, JSONObject nodeInfo) {
        log.info("saveDebugNodeInfo_in_taskid:{},nodeCode:{},nodeinfo:{}", taskId, nodeCode, JSONObject.toJSONString(nodeInfo));
        if (StringUtils.isBlank(taskId) || StringUtils.isBlank(nodeCode) || Objects.isNull(nodeInfo)) {
            log.info("saveDebugNodeInfo_data_is_incomplete");
            return;
        }
        //当前任务没有结束，数据暂存
        TmSceneDebugRecordEntityDO entityDO = tmSceneDebugRecordRepository.selectOneByTaskId(taskId);
        if (null == entityDO || StringUtils.isBlank(entityDO.getLogOssPath())) {
            redisService.addToListOnRightWithExpiration("debugNode-" + taskId, JSON.toJSONString(nodeInfo), 7200L);
            log.info("saveDebugNodeInfo_data_is_loading_taskId:{},nodeCode:{}", taskId, nodeCode);
            return;
        }

        //更新文件
        String content = "";
        try {
            content = ztoOssService.getObjectTextWithRange(entityDO.getBucketName(), entityDO.getLogOssPath());
        } catch (Exception e) {
            log.error("saveDebugNodeInfo_getData_error:{}", e.getMessage());
        }
        if (StringUtils.isBlank(content)) {
            log.error("saveDebugNodeInfo_is_blank_recordCode :{}", entityDO.getRecordCode());
            return;
        }
        DebugInfoVO debugInfoVO = JSONObject.parseObject(content, DebugInfoVO.class);
        List<DebugNodeInfoVO> linkBaseInfo = debugInfoVO.getLinkBaseInfo();
        if (CollectionUtils.isEmpty(linkBaseInfo)) {
            return;
        }
        for (DebugNodeInfoVO node : linkBaseInfo) {
            if (node.getLinkComponentCode().equals(nodeInfo.getOrDefault("nodeCode", ""))) {
                node.setStatus(String.valueOf(nodeInfo.get("nodeStatus")));
                node.setRequestMessage(String.valueOf(nodeInfo.get("requestMessage")));
                node.setResponseHeader(String.valueOf(nodeInfo.get("responseHeader")));
                node.setResponseBody(String.valueOf(nodeInfo.get("responseBody")));
                node.setAssertContent(String.valueOf(nodeInfo.get("assertContent")));
                node.setOutput(String.valueOf(nodeInfo.get("output")));
                break;
            }
        }
        SaveDebugInfoCommand command = new SaveDebugInfoCommand(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        BeanUtil.copyPropertiesNew(entityDO, command);
        command.setLinkBaseInfo(linkBaseInfo);
        command.setStatus(debugInfoVO.getStatus());
        command.setDebugLog(debugInfoVO.getDebugLog());
        saveDebugRecord(command);
    }

    /**
     * 更新链路中的节点结果
     *
     * @param command
     * @return
     */
    private List<DebugNodeInfoVO> buildLinkBaseInfo(SaveDebugInfoCommand command) {
        List<DebugNodeInfoVO> linkBaseInfo = command.getLinkBaseInfo();
        if (CollectionUtils.isEmpty(linkBaseInfo)) {
            return linkBaseInfo;
        }
        List<String> cacheNodeList = redisService.getListRange("debugNode-" + command.getTaskId(), 0, -1);
        redisService.delete("debugNode-" + command.getTaskId());
        log.info("buildLinkBaseInfo_cacheNodeList:{}", JSON.toJSONString(cacheNodeList));
        if (CollectionUtils.isEmpty(cacheNodeList)) {
            return linkBaseInfo;
        }
        cacheNodeList.forEach(str -> {
            DebugNodeInfoVO vo = JsonUtil.parse(str, DebugNodeInfoVO.class);
            linkBaseInfo.forEach(link -> {
                if (vo.getNodeCode().equals(link.getLinkComponentCode())) {
                    link.setStatus(vo.getNodeStatus());
                    link.setRequestMessage(vo.getRequestMessage());
                    link.setResponseHeader(vo.getResponseHeader());
                    link.setResponseBody(vo.getResponseBody());
                    link.setAssertContent(vo.getAssertContent());
                    link.setOutput(vo.getOutput());
                }
            });
        });
        return linkBaseInfo;
    }

    public void addApiTestCase(AddApiTestCaseCommand command) {
        log.info("addApiTestCase >>> {}", JsonUtil.toJSON(command));
        AddApiTestCaseEvent event = convert.convert(command);
        if (CollectionUtil.isNotEmpty(apiTestRepository.queryUniqueApiTestCase(event.getApiCode(), event.getCaseCode(), event.getCaseName()))) {
            throw new ServiceException("用例名称已存在");
        }
        //补充接口信息
        ApiTestEntityDO apiTest = apiTestRepository.queryLatestApiTestByMainCode(command.getApiCode());
        if (Objects.isNull(apiTest)) {
            throw new ServiceException("关联接口不存在");
        }
        event.setRelatedApiName(apiTest.getApiName());
        event.setRelatedApiAddress(apiTest.getApiAddress());
        event.setDocVersion(apiTest.getDocVersion());
        event.setApiType(apiTest.getApiType());
        event.setEnable(ApiCaseEnableEnum.DRAFT);
        apiTestRepository.addApiTestCase(event);
        doGenerateApiCaseNormal(event, command.getTransactor());
        doGenerateApiCaseException(event.getCaseCode(), null, command.getTransactor(), true);
        if (CollectionUtil.isNotEmpty(event.getDbIds())) {
            SYNC_API_TEST_EXECUTOR.execute(() -> {
                saveDbConfig(event.getDbIds(), event.getProductCode(), event.getTransactor(), UseCaseFactoryTypeEnum.API, event.getCaseCode());
            });
        }
        apply(event);
    }

    public void editApiTestCase(EditApiTestCaseCommand command) {
        log.info("editApiTestCase >>> {}", JsonUtil.toJSON(command));
        EditApiTestCaseEvent event = convert.convert(command);
        TmApiTestCaseVO tmApiTestCaseVO = apiTestRepository.getApiTestCase(event.getCaseCode(), ApiCaseStatusEnum.edit);
        if (null == tmApiTestCaseVO) {
            throw new ServiceException("接口测试用例不存在");
        }
        if (CollectionUtil.isNotEmpty(apiTestRepository.queryUniqueApiTestCase(event.getApiCode(), event.getCaseCode(), event.getCaseName()))) {
            throw new ServiceException("接口测试用例名称已存在");
        }
        //补充接口信息
        ApiTestEntityDO apiTest = apiTestRepository.queryLatestApiTestByMainCode(command.getApiCode());
        if (Objects.isNull(apiTest)) {
            throw new ServiceException("关联接口不存在");
        }
        event.setRelatedApiName(apiTest.getApiName());
        event.setRelatedApiAddress(apiTest.getApiAddress());
        event.setDocVersion(apiTest.getDocVersion());
        event.setApiType(apiTest.getApiType());
        apiTestRepository.editApiTestCase(event);
        this.syncModifyNormalApiCase(event);
        setApiCaseModifyTag(event, SceneTagEnum.API_CASE_MODIFY);
        deletePreDataTag(tmApiTestCaseVO.getCaseCode(), event.getTransactor());
        if (CollectionUtil.isNotEmpty(event.getDbIds())) {
            SYNC_API_TEST_EXECUTOR.execute(() -> {
                saveDbConfig(event.getDbIds(), event.getProductCode(), event.getTransactor(), UseCaseFactoryTypeEnum.API, event.getCaseCode());
            });
        }
        apply(event);
    }

    /**
     * 用例引用的造数全部刷新以后，删除用例【造数变更】标签
     *
     * @param caseCode 父用例code
     * @param user     当前操作人
     */
    private void deletePreDataTag(String caseCode, User user) {
        ApiTestCaseEntityDO draftCase = apiTestRepository.selectApiTestCaseByCodeAndStatus(caseCode, ApiCaseStatusEnum.edit);
        if (Objects.isNull(draftCase)
                || (!Arrays.asList(ApiCaseTypeEnum.API_CASE.getCode(), ApiCaseTypeEnum.SYSTEM_CASE.getCode()).contains(draftCase.getCaseType()))
                || StringUtils.isBlank(draftCase.getCaseReqData())) {
            return;
        }
        JSONObject caseReqData = JSONObject.parseObject(draftCase.getCaseReqData());
        if (Objects.isNull(caseReqData)) {
            return;
        }
        JSONArray dataCenterArr = caseReqData.getJSONArray("dataCenter");
        if (CollectionUtils.isEmpty(dataCenterArr)) {
            apiTestRepository.deleteApiTestCaseTag(caseCode, Collections.singletonList(SceneTagEnum.PRE_DATA_UPDATE), user);
            return;
        }
        boolean removeFlag = true;
        for (int i = 0; i < dataCenterArr.size(); i++) {
            JSONObject jsonObject = dataCenterArr.getJSONObject(i);
            if (jsonObject.getOrDefault("preDataUpdated", false).equals(true)) {
                removeFlag = false;
                break;
            }
        }
        if (removeFlag) {
            apiTestRepository.deleteApiTestCaseTag(caseCode, Collections.singletonList(SceneTagEnum.PRE_DATA_UPDATE), user);
        }
    }

    public void publishApiTestCase(PublishApiTestCaseCommand command) {
        log.info("publishApiTestCase >>> {}", JsonUtil.toJSON(command));
        PublishApiTestCaseEvent event = convert.convert(command);
        LockSeal lockSeal = lockService.acquireLock(LockStoreEnum.PUBLISH_API_CASE.getValue(), Collections.singletonList(command.getCaseCode()), 600000L);
        if (Objects.isNull(lockSeal)) {
            log.error("publishApiTestCase_is_publishing_code: {}", command.getCaseCode());
            throw new ServiceException("当前用例正在发布中，请稍后重试！");
        }
        Boolean publishFlag = false;
        String mainApiCode = null;
        try {
            ApiTestCaseEntityDO editApiTestCaseDO = apiTestRepository.queryApiCaseByCodeAndStatus(event.getCaseCode(), ApiCaseStatusEnum.edit);
            if (null == editApiTestCaseDO) {
                throw new ServiceException("接口测试用例不存在");
            }
            ApiTestEntityDO apiTestEntityDO = apiTestRepository.queryLatestApiTestByMainCode(editApiTestCaseDO.getApiCode());
            if (null == apiTestEntityDO) {
                throw new ServiceException("接口不存在");
            }
            //取消reqData造数变更标识
            setPreDataTag(editApiTestCaseDO, "");
            mainApiCode = apiTestEntityDO.getMainApiCode();
            ApiTestCaseEntityDO publishApiTestCaseDO = apiTestRepository.queryApiCaseByCodeAndStatus(event.getCaseCode(), ApiCaseStatusEnum.publish);
            String parentCode;
            String apiAddress = this.getApiAddress(apiTestEntityDO);
            if (null != publishApiTestCaseDO) {
                parentCode = executeGenerateApiTestAutomaticSourceRecord(editApiTestCaseDO, ApiCaseStatusEnum.publish,
                        publishApiTestCaseDO.getAutomaticSourceCode(), event.getTransactor(), apiAddress, apiTestEntityDO.getApiName());
                EditApiTestCaseEvent editApiTestCaseEvent = convert.convertToEditApiTestCaseEvent(editApiTestCaseDO);
                editApiTestCaseEvent.setPublished(Boolean.TRUE);
                editApiTestCaseEvent.setCaseCode(publishApiTestCaseDO.getCaseCode());
                editApiTestCaseEvent.setEnable(ApiCaseEnableEnum.ENABLED);
                editApiTestCaseEvent.setStatus(publishApiTestCaseDO.getStatus());
                editApiTestCaseEvent.setTransactor(event.getTransactor());
                editApiTestCaseEvent.setOccurred(event.getOccurred());
                apiTestRepository.editApiTestCase(editApiTestCaseEvent);
            } else {
                parentCode = executeGenerateApiTestAutomaticSourceRecord(editApiTestCaseDO, ApiCaseStatusEnum.publish,
                        null, event.getTransactor(), apiAddress, apiTestEntityDO.getApiName());
                AddApiTestCaseEvent addApiTestCaseEvent = convert.convert(editApiTestCaseDO);
                addApiTestCaseEvent.setPublished(Boolean.TRUE);
                addApiTestCaseEvent.setEnable(ApiCaseEnableEnum.ENABLED);
                addApiTestCaseEvent.setStatus(ApiCaseStatusEnum.publish);
                addApiTestCaseEvent.setTransactor(event.getTransactor());
                addApiTestCaseEvent.setOccurred(event.getOccurred());
                apiTestRepository.addApiTestCase(addApiTestCaseEvent);
            }
            // 更新草稿enable
            apiTestRepository.editDraftApiTestCaseStatus(editApiTestCaseDO.getCaseCode(), ApiCaseEnableEnum.ENABLED, event.getTransactor());
            addSubApiTestCases(editApiTestCaseDO, editApiTestCaseDO.getCaseCode(), event.getTransactor(), apiTestEntityDO.getApiType(), apiTestEntityDO.getApiName(), parentCode);
            apply(event);
            publishFlag = true;
        } catch (Exception e) {
            log.error("", e);
            publishFlag = false;
        } finally {
            lockService.releaseLock(lockSeal);
            setApiCasePublishTag(publishFlag, event.getCaseCode(), mainApiCode, event.getTransactor());
        }
    }

    @Async
    public void batchPublishApiTestCase(BatchPublishApiTestCaseCommand command) {
        log.info("batchPublishApiTestCase_command->{}", JSON.toJSONString(command));
        if (command.getPublishCaseFlag()) {
            BATCH_PUBLISH_API_CASE_EXECUTOR.execute(() -> command.getCaseCodeList().forEach(code -> {
                PublishApiTestCaseCommand publishApiTestCaseCommand = new PublishApiTestCaseCommand(code);
                publishApiTestCaseCommand.setCaseCode(code);
                publishApiTestCaseCommand.setProductCode(command.getProductCode());
                publishApiTestCaseCommand.setTransactor(command.getTransactor());
                publishApiTestCase(publishApiTestCaseCommand);
            }));
        } else {
            BATCH_PUBLISH_API_CASE_EXECUTOR.execute(() -> command.getApiCodeList().forEach(apiCode -> {
                List<String> caseCodeList = apiTestRepository.queryApiCaseCodeByApiCode(apiCode);
                if (CollectionUtil.isNotEmpty(caseCodeList)) {
                    caseCodeList.forEach(caseCode -> {
                        PublishApiTestCaseCommand publishApiTestCaseCommand = new PublishApiTestCaseCommand(caseCode);
                        publishApiTestCaseCommand.setCaseCode(caseCode);
                        publishApiTestCaseCommand.setProductCode(command.getProductCode());
                        publishApiTestCaseCommand.setTransactor(command.getTransactor());
                        publishApiTestCase(publishApiTestCaseCommand);
                    });
                }
            }));
        }
    }

    private String getApiAddress(ApiTestEntityDO apiTestEntityDO) {
        if (null == apiTestEntityDO.getApiType()) {
            throw new ServiceException("接口类型为空");
        }
        if (StringUtils.isEmpty(apiTestEntityDO.getApiAddress())) {
            throw new ServiceException("接口地址为空");
        }
        return apiTestEntityDO.getApiType().equals(ApiTypeEnum.DUBBO) ? GenerateApiCaseUtil.getSubStr(apiTestEntityDO.getApiAddress(), 1, ".") : apiTestEntityDO.getApiAddress();
    }

    /**
     * 新增接口用例子用例
     *
     * @param editApiTestCaseDO
     * @param parentCaseCode
     * @param transactor
     */
    private void addSubApiTestCases(ApiTestCaseEntityDO editApiTestCaseDO, String parentCaseCode, User transactor, ApiTypeEnum apiType, String apiName, String parentCode) {
        ADD_SUB_API_CASE_EXECUTOR.execute(() -> {
            apiTestRepository.deleteApiCaseByParentCaseCode(parentCaseCode, ApiCaseStatusEnum.publish, transactor);
            List<ApiTestCaseEntityDO> editApiTestCaseList = apiTestRepository.queryExceptionCaseByParentCaseCode(parentCaseCode,
                    ApiCaseStatusEnum.edit, false, Arrays.asList(ApiCaseTypeEnum.EXCEPTION_CASE.getCode(), ApiCaseTypeEnum.NORMAL_CASE.getCode()));
            editApiTestCaseList.stream().map(item -> {
                if (StringUtils.isNotEmpty(item.getCaseReqData())) {
                    JSONObject data = JSON.parseObject(item.getCaseReqData());
                    data.remove("label");
                    item.setCaseReqData(data.toJSONString());
                }
                // 删除草稿子用例标签
                ApiTestCaseEntityDO edit = new ApiTestCaseEntityDO();
                edit.setId(item.getId());
                edit.setTagValue("");
                edit.setCaseReqData(item.getCaseReqData());
                edit.preUpdate(transactor);
                apiTestRepository.updateApiTestCaseSelective(edit);

                item.setCaseCode(aggregateIdGenerateService.generateId(AggregateType.TEST_CASE));
                item.setStatus(ApiCaseStatusEnum.publish);
                item.setParentCaseCode(parentCaseCode);
                item.setLatestTaskId("");
                item.setCreator(transactor.getUserName());
                item.setCreatorId(transactor.getUserId());
                item.setGmtCreate(new Date());
                item.setModifier(transactor.getUserName());
                item.setModifierId(transactor.getUserId());
                item.setGmtModified(new Date());
                item.setAutomaticSourceCode(editApiTestCaseDO.getAutomaticSourceCode());
                return item;
            }).collect(Collectors.toList());
            apiTestRepository.batchInsertApiTestCase(editApiTestCaseList);
            syncApiTestCaseToTestCase(editApiTestCaseDO, editApiTestCaseList, transactor, editApiTestCaseDO.getPath(), apiType, apiName, parentCode);
        });
    }

    /**
     * 同步更新正常子用例
     *
     * @param event
     */
    private void syncModifyNormalApiCase(EditApiTestCaseEvent event) {
        ApiTestCaseEntityDO normalApiTestCaseEntity = apiTestRepository.queryNormalCaseByParentCode(event.getCaseCode());
        if (null == normalApiTestCaseEntity) {
            return;
        }
        event.setCaseName(normalApiTestCaseEntity.getCaseName());
        event.setCaseCode(normalApiTestCaseEntity.getCaseCode());
        event.setCaseType(normalApiTestCaseEntity.getCaseType());
        apiTestRepository.editApiTestCase(event);
        apiTestRepository.addApiTestCaseTag(normalApiTestCaseEntity.getCaseCode(), SceneTagEnum.API_CASE_MODIFY, event.getTransactor());
    }

    private void setApiCaseModifyTag(EditApiTestCaseEvent event, SceneTagEnum tag) {
        TmApiTestCaseVO tmApiTestCaseVO = apiTestRepository.getApiTestCase(event.getCaseCode(), ApiCaseStatusEnum.publish);
        if (null == tmApiTestCaseVO) {
            return;
        }
        if (!ApiCaseStatusEnum.publish.equals(tmApiTestCaseVO.getStatus())) {
            return;
        }

        //如果用例名称有变更，直接更新发布态用例名称，不打标记
        if (!tmApiTestCaseVO.getCaseName().equals(event.getCaseName())) {
            ApiTestCaseEntityDO entityDO = new ApiTestCaseEntityDO();
            entityDO.setCaseCode(tmApiTestCaseVO.getCaseCode());
            entityDO.setStatus(tmApiTestCaseVO.getStatus());
            entityDO.setCaseName(event.getCaseName());
            entityDO.preUpdate(event);
            apiTestRepository.updateApiTestCaseSelectiveByCaseCodeAndStatus(entityDO);
        }

        SYNC_API_TEST_EXECUTOR.execute(() -> {
            if (checkSceneInfoChanged(tmApiTestCaseVO.getCaseReqData(), event.getCaseReqData())) {
                tagRepository.removeSceneTag(tmApiTestCaseVO.getCaseCode(), Collections.singletonList(tag));
                apiTestRepository.deleteApiTestCaseTag(tmApiTestCaseVO.getCaseCode(), Collections.singletonList(tag), event.getTransactor());
            } else {
                apiTestRepository.addApiTestCaseTag(tmApiTestCaseVO.getCaseCode(), tag, event.getTransactor());
            }
        });
    }

    private void setApiCasePublishTag(boolean result, String caseCode, String apiCode, User transactor) {
        if (result) {
            List<SceneTagEnum> removeTags = Arrays.asList(
                    SceneTagEnum.API_CASE_MODIFY,
                    SceneTagEnum.PUBLISH_ERROR,
                    SceneTagEnum.API_UPDATE,
                    SceneTagEnum.PRE_DATA_UPDATE);
            apiTestRepository.deleteApiTestCaseTag(caseCode, removeTags, transactor);
            this.removeApiTestTagByApiCase(apiCode);
        } else {
            apiTestRepository.addApiTestCaseTag(caseCode, SceneTagEnum.PUBLISH_ERROR, transactor);
        }
    }

    private void removeApiTestTagByApiCase(String mainApiCode) {
        List<String> tagList = apiTestRepository.queryApiCaseTagByApiCode(mainApiCode);
        if (CollectionUtil.isNotEmpty(tagList) && tagList.stream()
                .filter(StringUtils::isNotEmpty)
                .anyMatch(tags -> tags.contains(SceneTagEnum.API_UPDATE.name()))) {
            return;
        }
        apiTestRepository.updateApiTestTagByMainApiCode(mainApiCode, "");
    }

    public void updateApiCaseException(UpdateApiCaseExceptionCommand command) {
        log.info("updateApiCaseException >>> {}", JSON.toJSONString(command));
        String caseName =
                String.format("%s_%s_%s", command.getKey(), command.getApiConfigType().getDesc(), command.getValue());
        if (caseName.length() > 100) {
            caseName = StrUtil.maxLength(caseName, 97);
        }
        ApiTestCaseEntityDO apiCase = new ApiTestCaseEntityDO();
        apiCase.setCaseCode(command.getAggregateId());
        apiCase.setCaseName(caseName);
        apiCase.setCaseReqData(JSON.toJSONString(command));
        apiCase.setGenerateRules(command.getApiConfigType());
        apiCase.setGenerateFieldSource(command.getKey());
        apiCase.preUpdate(command);
        apiTestRepository.updateExceptionCaseByCode(apiCase);
        SYNC_API_TEST_EXECUTOR.execute(() -> {
            apiTestRepository.addApiTestCaseTag(command.getAggregateId(), SceneTagEnum.API_CASE_MODIFY, command.getTransactor());
            ApiTestCaseEntityDO child = apiTestRepository.queryApiCaseByCodeAndStatus(command.getAggregateId(), ApiCaseStatusEnum.edit);
            if (null == child) {
                return;
            }
            ApiTestCaseEntityDO publish = apiTestRepository.queryApiCaseByCodeAndStatus(child.getParentCaseCode(), ApiCaseStatusEnum.publish);
            if (null != publish) {
                apiTestRepository.addApiTestCaseTag(publish.getCaseCode(), SceneTagEnum.API_CASE_MODIFY, command.getTransactor());
            }
        });
    }

    public void batchDeleteApiCaseException(List<String> caseCodeList, User transactor) {
        log.info("batchDeleteApiCaseException >>> {}", caseCodeList);
        apiTestRepository.batchDeleteApiCaseExceptionByCodeList(caseCodeList, transactor);
        SYNC_API_TEST_EXECUTOR.execute(() -> {
            List<ApiTestCaseEntityDO> list = apiTestRepository.queryExceptionCaseByCodeList(caseCodeList);
            if (CollectionUtil.isNotEmpty(list)) {
                ApiTestCaseEntityDO publish = apiTestRepository.queryApiCaseByCodeAndStatus(list.get(0).getParentCaseCode(), ApiCaseStatusEnum.publish);
                if (null != publish) {
                    apiTestRepository.addApiTestCaseTag(publish.getCaseCode(), SceneTagEnum.API_CASE_MODIFY, transactor);
                }
            }
        });
    }

    public void generateApiCaseException(GenerateApiCaseExceptionCommand command) {
        log.info("generateApiCaseException >>> {}", JSON.toJSONString(command));
        if (CollectionUtils.isEmpty(command.getApiFieldConfigList())) {
            return;
        }
        String key = String.format("process::%s", command.getParentCaseCode());
        if (redisService.hasKey(key) && StringUtils.isNotEmpty(redisService.getKey(key))) {
            throw new ServiceException("异常数据正在生成中，请稍后再试！");
        }
        doGenerateApiCaseException(command.getParentCaseCode(), command.getApiFieldConfigList(), command.getTransactor(), false);
    }

    public void doGenerateApiCaseException(
            String caseCode,
            List<ApiFieldConfigVO> apiFieldConfigList,
            User transactor,
            boolean initialize) {
        SYNC_API_TEST_EXECUTOR.execute(() -> {
            log.info("异步生成异常数据断言: {}", caseCode);
            String key = String.format("process::%s", caseCode);
            LockSeal lockSeal = null;
            try {
                lockSeal = lockService.acquireLock(
                        LockStoreEnum.EXCEPTION_CASE.getValue(), Collections.singletonList(key), 30000L);
                if (null == lockSeal) {
                    log.warn("异常数据正在生成中！[{}]", key);
                    return;
                }
                redisService.setKey(key, "1", 5L, TimeUnit.MINUTES);
                ApiTestCaseEntityDO apiCase = apiTestRepository.queryApiCaseByCodeAndStatus(caseCode, ApiCaseStatusEnum.edit);
                if (null == apiCase || StringUtils.isBlank(apiCase.getApiCode())) {
                    return;
                }
                ApiTestEntityDO apiTest = apiTestRepository.queryLatestApiTestByMainCode(apiCase.getApiCode());
                if (null == apiTest) {
                    return;
                }
                List<ApiGlobalConfigurationEntityDO> configList = apiTestQueryDomainService.getApiConfig(
                        apiTest.getProductCode(), apiTest.getAppId(), apiTest.getMainApiCode());
                List<ApiFieldConfigVO> finalApiFieldConfigList = apiFieldConfigList;
                if (initialize) {
                    try {
                        if (CollectionUtil.isEmpty(configList)) {
                            return;
                        }
                        List<ApiConfigTypeEnum> apiConfigTypeList = configList.stream()
                                .map(config -> ApiConfigTypeEnum.codeOf(config.getApiConfigType()))
                                .collect(Collectors.toList());
                        List<String> keys = GenerateApiCaseUtil.parseApiFieldKey(apiTest.getApiType(), apiTest.getApiData());
                        if (CollectionUtil.isEmpty(keys)) {
                            return;
                        }
                        finalApiFieldConfigList = keys.stream()
                                .map(fieldKey -> {
                                    ApiFieldConfigVO vo = new ApiFieldConfigVO();
                                    vo.setKey(fieldKey);
                                    vo.setApiConfigTypeList(apiConfigTypeList);
                                    return vo;
                                }).collect(Collectors.toList());
                    } finally {
                        ApiTestCaseEntityDO entityDO = new ApiTestCaseEntityDO();
                        entityDO.setId(apiCase.getId());
                        entityDO.setInitStatus(ApiCaseInItStatusEnum.SUCCESS.getCode());
                        apiTestRepository.updateApiTestCaseSelective(entityDO);
                    }
                }
                ApiCaseGenerateParameter parameter = new ApiCaseGenerateParameter();
                parameter.setApiType(apiTest.getApiType());
                parameter.setApiData(apiTest.getApiData());
                parameter.setApiFieldConfigList(finalApiFieldConfigList);
                parameter.setApiGlobalConfigList(configList);
                parameter.setInitialize(initialize);
                parameter.setApiGatewayRule(qcConfigBasicService.getApiGatewayRule());
                List<ApiCaseExceptionVO> caseList = GenerateApiCaseUtil.generateApiCaseException(parameter);
                if (CollectionUtils.isEmpty(caseList)) {
                    return;
                }
                caseList.forEach(exceptionCase -> insertApiCaseException(exceptionCase, apiCase, transactor));
                ApiTestCaseEntityDO publish = apiTestRepository.queryApiCaseByCodeAndStatus(caseCode, ApiCaseStatusEnum.publish);
                if (null != publish) {
                    apiTestRepository.addApiTestCaseTag(caseCode, SceneTagEnum.API_CASE_MODIFY, transactor);
                }
            } finally {
                lockService.releaseLock(lockSeal);
                redisService.delete(key);
            }
        });
    }

    /**
     * 生成正常接口用例
     *
     * @param event
     * @param transactor
     */
    public void doGenerateApiCaseNormal(AddApiTestCaseEvent event, User transactor) {
        ApiTestCaseEntityDO normalCaseEntityDO = new ApiTestCaseEntityDO();
        normalCaseEntityDO.setCaseCode(aggregateIdGenerateService.generateId(AggregateType.TEST_CASE));
        normalCaseEntityDO.setCaseName(event.getCaseName() + "_正常");
        normalCaseEntityDO.setProductCode(event.getProductCode());
        normalCaseEntityDO.setApiCode(event.getApiCode());
        normalCaseEntityDO.setRelatedApiName(event.getRelatedApiName());
        normalCaseEntityDO.setRelatedApiAddress(event.getRelatedApiAddress());
        normalCaseEntityDO.setDocVersion(event.getDocVersion());
        normalCaseEntityDO.setApiType(event.getApiType());
        normalCaseEntityDO.setStatus(event.getStatus());
        normalCaseEntityDO.setCaseType(ApiCaseTypeEnum.NORMAL_CASE.getCode());
        normalCaseEntityDO.setCaseReqData(event.getCaseReqData());
        normalCaseEntityDO.setParentCaseCode(event.getCaseCode());
        normalCaseEntityDO.setEnable(ApiCaseEnableEnum.ENABLED);
        normalCaseEntityDO.preCreate(transactor);
        normalCaseEntityDO.setLatestTaskId("");
        apiTestRepository.insertApiTestCaseSelective(normalCaseEntityDO);
    }

    public CheckBatchOperationVO verifyBatchOperation(VerifyBatchOperationCommand command) {
        log.info("verifyBatchOperation_command ->{}", JSON.toJSON(command));
        if (StringUtils.isBlank(command.getProductCode()) || CollectionUtil.isEmpty(command.getApiCaseCodeList())) {
            throw new ServiceException("校验条件不足，无法判断");
        }
        List<ApiTestCaseEnableVO> voList = new ArrayList<>();
        if (ApiCaseBatchOperationEnum.DELETED.equals(command.getOperation())) {
            voList = apiTestRepository.queryApiCaseEnable(command.getProductCode(), command.getApiCaseCodeList(), "");
        }
        if (ApiCaseBatchOperationEnum.EXECUTE.equals(command.getOperation())) {
            voList = apiTestRepository.queryApiCaseEnable(command.getProductCode(), command.getApiCaseCodeList(), ApiCaseStatusEnum.publish.name());
        }
        return CheckBatchOperationVO.buildSelf(voList, command);
    }

    public void changeApiCaseStatus(ChangeApiCaseStatusCommand command) {
        log.info("changeApiCaseStatus_command->{}", JSON.toJSONString(command));
        if (ApiCaseStatusEnum.edit.equals(command.getStatus()) &&
                !ApiCaseBatchOperationEnum.DELETED.equals(command.getOperation())) {
            throw new ServiceException("未发布的用例，不支持【启用】或【禁用】！");
        }
        ApiTestCaseEntityDO entityDO = apiTestRepository.selectApiTestCaseByCodeAndStatus(command.getApiCaseCode(), command.getStatus());
        if (Objects.isNull(entityDO) || ApiCaseEnableEnum.DELETED.equals(entityDO.getEnable())) {
            throw new ServiceException("用例不存在！");
        }
        if (command.getOperation().mapping().equals(entityDO.getEnable())) {
            throw new ServiceException("用例已" + command.getOperation().getDesc() + "！");
        }
        apiTestRepository.updateApiTestCaseEnable(command);
        if (ApiCaseBatchOperationEnum.DELETED.equals(command.getOperation())) {
            this.removeApiTestTagByApiCase(entityDO.getApiCode());
        }
        if (StringUtils.isNotEmpty(entityDO.getAutomaticSourceCode())) {
            this.updateSyncTestcaseStatus(Arrays.asList(entityDO.getAutomaticSourceCode()), command.getOperation(), command.getTransactor());
        }
        this.updateTestPlanCases(Arrays.asList(command.getApiCaseCode()), command.getOperation(), command.getTransactor());
    }

    /**
     * 同步更新testcase状态
     *
     * @param automaticSourceCodes
     * @param operation
     * @param transactor
     */
    private void updateSyncTestcaseStatus(List<String> automaticSourceCodes, ApiCaseBatchOperationEnum operation, User transactor) {
        if (CollectionUtil.isEmpty(automaticSourceCodes)) {
            return;
        }
        if (operation == ApiCaseBatchOperationEnum.DELETED) {
            testcaseRepository.updateTestcaseEnableByAutomaticSourceCode(automaticSourceCodes, Boolean.FALSE, transactor, TestcaseStatusEnum.DISABLE);
            AutomaticSourceRecordEntityDO automaticSourceRecordEntityDO = new AutomaticSourceRecordEntityDO();
            automaticSourceRecordEntityDO.setEnable(false);
            automaticSourceRecordRepository.updateByCodeList(automaticSourceRecordEntityDO, automaticSourceCodes);
        }
    }

    private void updateTestPlanCases(List<String> parentCaseCodes, ApiCaseBatchOperationEnum operation, User transactor) {
        if (!ApiCaseBatchOperationEnum.DELETED.equals(operation)) {
            return;
        }
        List<String> caseCodes = Optional.ofNullable(apiTestRepository.querySubApiCasesByParentCaseCodes(parentCaseCodes, ApiCaseStatusEnum.publish))
                .orElse(Collections.emptyList()).stream()
                .map(ApiTestCaseEntityDO::getCaseCode).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(caseCodes)) {
            return;
        }
        testcaseCommandDomainService.batchChangeCaseStatus(caseCodes, TestcaseStatusEnum.DISABLE, TestcaseAbandonReasonEnum.NULL, transactor);
    }

    @Async
    public void batchChangeApiCaseStatus(BatchChangeApiCaseStatusCommand command) {
        log.info("batchChangeApiCaseStatus_command->{}", JSON.toJSONString(command));
        List<String> apiCaseCodeList = command.getApiCaseCodeList();
        if (CollectionUtils.isEmpty(apiCaseCodeList)) {
            return;
        }
        List<List<String>> partCodes = Lists.partition(apiCaseCodeList, 200);
        partCodes.forEach(codes -> {
            command.setApiCaseCodeList(codes);
            if (command.getOperation().equals(ApiCaseBatchOperationEnum.DELETED)) {
                apiTestRepository.batchDeleteApiTestCase(command);
            }
        });
        if (ApiCaseBatchOperationEnum.DELETED.equals(command.getOperation())) {
            List<String> apiCodeList = apiTestRepository.queryApiCodeByCaseCode(apiCaseCodeList);
            apiCodeList.forEach(this::removeApiTestTagByApiCase);
        }
        List<String> automaticSourceCodeList = apiTestRepository.selectAutomaticSourceCodeList(apiCaseCodeList, ApiCaseStatusEnum.publish);
        if (CollectionUtil.isEmpty(automaticSourceCodeList)) {
            return;
        }
        this.updateSyncTestcaseStatus(automaticSourceCodeList, command.getOperation(), command.getTransactor());
        this.updateTestPlanCases(apiCaseCodeList, command.getOperation(), command.getTransactor());
    }

    @Async
    public void refreshSceneApiRelation(String productCode) {
        List<String> list = apiTestRepository.querySceneApiRelationAllSceneCode(productCode);
        list.forEach(sceneCode -> {
            SceneInfoEntityDO scene = apiTestRepository.queryLatestSceneInfo(sceneCode, SceneInfoStatusEnum.publish);
            if (null == scene) {
                return;
            }
            saveSceneApiRelation(scene);
        });
    }

    /**
     * 生成接口自动化用例登记库
     *
     * @param apiTestCaseEntityDO
     * @param status
     * @param automaticSourceCode
     * @param transactor
     * @param apiName
     */
    private String executeGenerateApiTestAutomaticSourceRecord(
            ApiTestCaseEntityDO apiTestCaseEntityDO, ApiCaseStatusEnum status, String automaticSourceCode, User transactor, String apiAddress, String apiName) {
        log.info("同步登记库 >>> {}_{}_{}", apiTestCaseEntityDO.getApiCode(), apiTestCaseEntityDO.getCaseCode(), status);
        String parentCode = "";
        try {
            // 未生成过登记库，新建上级分组
            AddTestcaseEvent event = createApiTestAutomaticModule(apiTestCaseEntityDO, transactor, apiAddress, apiName);
            // 新增登记库
            AddAutomaticRecordCommand addAutomaticRecordCommand = createApiTestAutomaticSource(apiTestCaseEntityDO, status, event, transactor);
            apiTestCaseEntityDO.setAutomaticSourceCode(addAutomaticRecordCommand.getAggregateId());
            apiTestCaseEntityDO.setPath(addAutomaticRecordCommand.getPath());
            parentCode = addAutomaticRecordCommand.getTestcaseCode();
            if (StringUtils.isNotBlank(automaticSourceCode)) {
                // 查询登记库
                AutomaticSourceRecordEntityDO entityDO = automaticSourceRecordRepository.find(automaticSourceCode);
                if (null == entityDO) {
                    throw new ServiceException("该接口未找到登记库！");
                }
                if (entityDO.getEnable()) {
                    AutomaticSourceRecordEntityDO automaticSourceRecord = new AutomaticSourceRecordEntityDO();
                    automaticSourceRecord.setCode(automaticSourceCode);
                    automaticSourceRecord.setEnable(false);
                    automaticSourceRecord.setModifier(transactor.getUserName());
                    automaticSourceRecord.setModifierId(transactor.getUserId());
                    automaticSourceRecord.setGmtModified(new Date());
                    automaticSourceRecordRepository.updateByPrimaryKeySelective(automaticSourceRecord);
                    testcaseRepository.updateTestcaseEnableByAutomaticSourceCode(Arrays.asList(automaticSourceCode), Boolean.FALSE, transactor, TestcaseStatusEnum.DISABLE);
                }
            }
        } catch (Exception e) {
            log.error("同步登记库异常！", e);
            throw new ServiceException("同步登记库异常。" + e.getMessage());
        }
        return parentCode;
    }

    /**
     * 创建接口自动化分组
     *
     * @param apiTestCaseEntityDO
     * @param transactor
     * @return
     */
    private AddTestcaseEvent createApiTestAutomaticModule(ApiTestCaseEntityDO apiTestCaseEntityDO, User transactor, String apiAddress, String interfaceName) {
        AddTestcaseEvent event;
        // 是否已有用例工厂分组
        TestcaseEntityDO firstLevelModule = getModuleByNameAndParent("", "用例工厂", apiTestCaseEntityDO.getProductCode());
        if (null == firstLevelModule) {
            // 新增分组-用例工厂
            AddTestcaseEvent addFirstModuleEvent =
                    this.addNewAutoModule("用例工厂", apiTestCaseEntityDO.getProductCode(), transactor, "", 1, "");
            AddTestcaseEvent addSecondModuleEvent = this.addNewAutoModule(
                    "发布-接口", apiTestCaseEntityDO.getProductCode(), transactor, addFirstModuleEvent.getCode(), 2, "");
            event = this.addNewAutoModule(
                    apiAddress, apiTestCaseEntityDO.getProductCode(), transactor, addSecondModuleEvent.getCode(), 3, interfaceName);
        } else {
            // 判断分组-发布-接口是否存在
            TestcaseEntityDO secondLevelModule =
                    this.getModuleByNameAndParent(firstLevelModule.getCode(), "发布-接口", apiTestCaseEntityDO.getProductCode());
            if (null == secondLevelModule) {
                AddTestcaseEvent addSecondModuleEvent = this.addNewAutoModule(
                        "发布-接口", apiTestCaseEntityDO.getProductCode(), transactor, firstLevelModule.getCode(), 2, "");
                event = this.addNewAutoModule(
                        apiAddress, apiTestCaseEntityDO.getProductCode(), transactor, addSecondModuleEvent.getCode(), 3, interfaceName);
            } else {
                // 判断接口名是否存在
                TestcaseEntityDO thirdLevelModule =
                        this.getModuleByNameAndParent(secondLevelModule.getCode(), apiAddress, apiTestCaseEntityDO.getProductCode());
                if (null == thirdLevelModule) {
                    event = this.addNewAutoModule(
                            apiAddress, apiTestCaseEntityDO.getProductCode(), transactor, secondLevelModule.getCode(), 3, interfaceName);
                } else {
                    event = new AddTestcaseEvent();
                    event.setCode(thirdLevelModule.getCode());
                    event.setPath(thirdLevelModule.getPath());
                }
            }
        }
        return event;
    }

    /**
     * 创建接口测试用例登记库
     *
     * @param apiTestCaseEntityDO
     * @param status
     * @param event
     * @param transactor
     * @return
     */
    private AddAutomaticRecordCommand createApiTestAutomaticSource(
            ApiTestCaseEntityDO apiTestCaseEntityDO,
            ApiCaseStatusEnum status,
            AddTestcaseEvent event, User transactor) {

        String aggregateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddAutomaticRecordCommand addCommand = new AddAutomaticRecordCommand(aggregateId);
        addCommand.setProductCode(apiTestCaseEntityDO.getProductCode());
        addCommand.setName(apiTestCaseEntityDO.getCaseName() + "-" + status.getDesc());
        addCommand.setAddress("");
        addCommand.setType(AutomaticRecordTypeEnum.API_TEST);
        addCommand.setTestcaseCode(event.getCode());
        addCommand.setPath(event.getPath());
        addCommand.setTransactor(transactor);
        automaticSourceRecordCommandDomainService.addAutomaticRecordCommand(addCommand);
        return addCommand;
    }

    /**
     * 同步接口用例到testcase表
     *
     * @param apiTestCaseList
     * @param user
     * @param path
     */
    private void syncApiTestCaseToTestCase(
            ApiTestCaseEntityDO editApiTestCaseDO,
            List<ApiTestCaseEntityDO> apiTestCaseList,
            User user, String path,
            ApiTypeEnum apiType,
            String apiName,
            String parentCode) {
        try {
            if (null == editApiTestCaseDO) {
                return;
            }
            String testcaseModulePath = (StringUtils.isEmpty(path) ? "" : path + ".")
                    + (StringUtils.isEmpty(parentCode) ? "" : parentCode + ".")
                    + editApiTestCaseDO.getAutomaticSourceCode();
            List<TestcaseEntityDO> list = new ArrayList<>();
            for (ApiTestCaseEntityDO entityDO : apiTestCaseList) {
                this.addSubTestCaseList(
                        entityDO,
                        user,
                        editApiTestCaseDO.getAutomaticSourceCode(),
                        editApiTestCaseDO.getProductCode(),
                        list,
                        testcaseModulePath,
                        apiType,
                        apiName);
            }
            testcaseRepository.batchInsertCase(list);
        } catch (Exception e) {
            log.error("", e);
            throw new ServiceException("同步测试用例异常");
        }
    }

    private void addSubTestCaseList(
            ApiTestCaseEntityDO apiTestCaseEntityDO,
            User transactor,
            String automaticSourceCode,
            String productCode,
            List<TestcaseEntityDO> list,
            String testcaseModulePath,
            ApiTypeEnum apiType,
            String apiName) {
        if (StringUtils.isNotEmpty(apiTestCaseEntityDO.getCaseName()) && apiTestCaseEntityDO.getCaseName().length() > 256) {
            throw new ServiceException("名称长度不超过256字符！");
        }
        TestcaseEntityDO testcase = new TestcaseEntityDO();
        testcase.setCode(apiTestCaseEntityDO.getCaseCode());
        testcase.setAttribute(TestcaseAttributeEnum.TESTCASE);
        testcase.setType(TestcaseTypeEnum.AUTO);
        testcase.setName(apiTestCaseEntityDO.getCaseName());
        testcase.setParentCode("");
        testcase.preCreate(transactor);
        if (null != transactor) {
            testcase.setDutyUser(transactor.getUserName());
            testcase.setDutyUserId(transactor.getUserId());
        }
        testcase.setAutomaticSourceCode(automaticSourceCode);
        testcase.setProductCode(productCode);
        testcase.setPriority(TestcasePriorityEnum.MIDDLE);
        testcase.setStatus(TestcaseStatusEnum.NORMAL);
        if (ApiTypeEnum.HTTP.equals(apiType)) {
            testcase.setNodeType(AutomaticNodeTypeEnum.HTTPSamplerProxy);
        } else if (ApiTypeEnum.DUBBO.equals(apiType)) {
            testcase.setNodeType(AutomaticNodeTypeEnum.DubboSample);
        } else {
            testcase.setNodeType(AutomaticNodeTypeEnum.OTHER);
        }
        testcase.setSort(list.size());
        testcase.setNodeTypePath(apiTestCaseEntityDO.getCaseName());
        testcase.setPath("");
        testcase.setTestcaseModulePath(testcaseModulePath);
        testcase.setInterfaceName(apiName);
        list.add(testcase);
    }

    @Async
    public void refreshSceneApiCode(String productCode) {
        List<SceneInfoEntityDO> list = apiTestRepository.querySceneByProductCodeAndSceneType(productCode, null);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(scene -> {
            log.info("刷新场景图接口code:{}", scene.getSceneCode());
            try {
                SceneInfoEntityDO edit = apiTestRepository.queryLatestSceneInfo(scene.getSceneCode(), SceneInfoStatusEnum.edit);
                updateSceneNodeApiCode(edit);
                SceneInfoEntityDO publish = apiTestRepository.queryLatestSceneInfo(scene.getSceneCode(), SceneInfoStatusEnum.publish);
                updateSceneNodeApiCode(publish);
            } catch (Exception e) {
                log.error("刷新场景图接口code异常", e);
            }
        });
    }

    private void updateSceneNodeApiCode(SceneInfoEntityDO scene) {
        if (null == scene || StringUtils.isEmpty(scene.getSceneBackData())) {
            return;
        }
        JSONObject data = JSON.parseObject(scene.getSceneBackData());
        JSONObject nodes = data.getJSONObject("nodes");
        if (null == nodes || nodes.isEmpty()) {
            return;
        }
        nodes.keySet().forEach(key -> {
            JSONObject node = nodes.getJSONObject(key);
            if (StringUtils.isNotEmpty(node.getString("apiCode"))) {
                ApiTestEntityDO apiTest = apiTestRepository.queryApiDetailByCode(node.getString("apiCode"));
                if (null != apiTest && StringUtils.isNotEmpty(apiTest.getMainApiCode())) {
                    node.put("apiCode", apiTest.getMainApiCode());
                    node.put("docId", apiTest.getDocId());
                    return;
                }
            }
            String productCode = null;
            if (UseCaseFactoryTypeEnum.SCENE.getCode() == scene.getSceneType()) {
                productCode = scene.getProductCode();
            }
            if (null != node.getLong("docId") && node.getLong("docId") > 0) {
                List<ApiTestEntityDO> docList = apiTestRepository.getApiTestByDocId(node.getLong("docId"), productCode);
                if (CollectionUtils.isNotEmpty(docList)) {
                    node.put("apiCode", docList.get(0).getMainApiCode());
                    node.put("docId", docList.get(0).getDocId());
                    return;
                }
            }
            PageApiInfoReq req = new PageApiInfoReq();
            req.setProductCode(productCode);
            req.setPage(1);
            req.setSize(1);
            JSONObject sampler = node.getJSONObject("sampler");
            LinkMapTypeEnum type = LinkMapTypeEnum.valueOf(sampler.getString("type"));
            if (LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE.equals(type)
                    || LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_FORM_TYPE.equals(type)) {
                if (StringUtils.isEmpty(node.getString("requestUrl"))) {
                    return;
                }
                req.setApiType(ApiTypeEnum.HTTP);
                try {
                    URL url = new URL(node.getString("requestUrl"));
                    req.setNameOrAddress(url.getPath());
                } catch (Exception e) {
                    req.setNameOrAddress(node.getString("requestUrl"));
                }
            }
            if (LinkMapTypeEnum.DUBBO_REQUEST_COMPONENT.equals(type)) {
                if (StringUtils.isEmpty(sampler.getString("interfaceName"))
                        || StringUtils.isEmpty(sampler.getString("methodName"))) {
                    return;
                }
                req.setApiType(ApiTypeEnum.DUBBO);
                req.setNameOrAddress(sampler.getString("interfaceName") + "#" + sampler.getString("methodName"));
            }
            if (StringUtils.isNotEmpty(req.getNameOrAddress())) {
                PageInfo<ApiSampleCaseVO> pageInfo = apiTestRepository.queryPageApiTest(req);
                if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
                    node.put("apiCode", pageInfo.getList().get(0).getApiCode());
                    node.put("docId", pageInfo.getList().get(0).getDocId());
                    return;
                }
            }
            if (StringUtils.isNotEmpty(node.getString("apiCode"))) {
                node.put("apiCode", "");
            }
        });
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setId(scene.getId());
        entityDO.setSceneBackData(data.toJSONString());
        entityDO.setGmtModified(scene.getGmtModified());
        apiTestRepository.updateSceneInfoByPrimaryKey(entityDO);
    }

    private void syncApiCaseByPreData(String preDataCode) {
        List<String> caseList = apiTestRepository.queryApiCaseCodeByApiCode(null);
        if (CollectionUtils.isEmpty(caseList)) {
            return;
        }
        caseList.forEach(caseCode -> {
            ApiTestCaseEntityDO editCase = apiTestRepository.queryApiCaseByCodeAndStatus(caseCode, ApiCaseStatusEnum.edit);
            boolean preDataUpdated = checkApiCasePreDataUpdated(editCase, preDataCode);
            if (!preDataUpdated) {
                ApiTestCaseEntityDO publishCase = apiTestRepository.queryApiCaseByCodeAndStatus(caseCode, ApiCaseStatusEnum.publish);
                preDataUpdated = checkApiCasePreDataUpdated(publishCase, preDataCode);
            }
            if (preDataUpdated) {
                apiTestRepository.addApiTestCaseTag(caseCode, SceneTagEnum.PRE_DATA_UPDATE, new User(0L, "系统"));
                setPreDataTag(editCase, preDataCode);
            }
        });
    }

    private boolean checkApiCasePreDataUpdated(ApiTestCaseEntityDO apiCase, String preDataCode) {
        JSONArray dataCenter = Optional.ofNullable(apiCase)
                .map(ApiTestCaseEntityDO::getCaseReqData)
                .map(JSONObject::parseObject)
                .map(json -> json.getJSONArray("dataCenter"))
                .orElse(null);
        if (CollectionUtils.isEmpty(dataCenter)) {
            return false;
        }
        for (int i = 0; i < dataCenter.size(); i++) {
            JSONObject jsonObject = dataCenter.getJSONObject(i);
            if (preDataCode.equals(jsonObject.getString("dataCenterSceneCode"))) {
                return true;
            }
        }
        return false;
    }

    private void setPreDataTag(ApiTestCaseEntityDO draftCase, String preDataCode) {
        log.info("setPreDataTag_draftCase:{},preDataCode:{}", draftCase.getCaseCode() + "_" + draftCase.getCaseType(), preDataCode);
        if (!Arrays.asList(ApiCaseTypeEnum.API_CASE.getCode(), ApiCaseTypeEnum.SYSTEM_CASE.getCode()).contains(draftCase.getCaseType())) {
            return;
        }
        JSONObject caseReqData = JSONObject.parseObject(draftCase.getCaseReqData());
        JSONArray dataCenterArr = caseReqData.getJSONArray("dataCenter");
        if (CollectionUtils.isEmpty(dataCenterArr)) {
            return;
        }
        boolean updateFlag = false;
        for (int i = 0; i < dataCenterArr.size(); i++) {
            JSONObject jsonObject = dataCenterArr.getJSONObject(i);
            //用例发布，所有标识都刷掉
            if (StringUtils.isBlank(preDataCode) && jsonObject.getOrDefault("preDataUpdated", true).equals(true)) {
                jsonObject.put("preDataUpdated", false);
                dataCenterArr.set(i, jsonObject);
                updateFlag = true;
            } else if (preDataCode.equals(jsonObject.getString("dataCenterSceneCode"))) {
                //造数发布，刷新标识
                jsonObject.put("preDataUpdated", true);
                dataCenterArr.set(i, jsonObject);
                updateFlag = true;
            }
        }
        if (updateFlag) {
            caseReqData.put("dataCenter", dataCenterArr);
            ApiTestCaseEntityDO toUpdate = new ApiTestCaseEntityDO();
            toUpdate.setCaseReqData(caseReqData.toJSONString());
            toUpdate.setId(draftCase.getId());
            apiTestRepository.updateApiTestCaseSelective(toUpdate);
            draftCase.setCaseReqData(caseReqData.toJSONString());
        }
    }

    public void batchGenerateApiCase(BatchGenerateApiCaseCommand command) {
        SYNC_API_TEST_EXECUTOR.execute(() -> {
            log.info("batchGenerateApiCase >>> {}", JSON.toJSONString(command));
            if (CollectionUtils.isNotEmpty(command.getApiCodes())) {
                command.getApiCodes().forEach(apiCode -> {
                    try {
                        ApiTestEntityDO apiTest = apiTestRepository.queryLatestApiTestByMainCode(apiCode);
                        if (null == apiTest) {
                            return;
                        }
                        int count = apiTestRepository.countSystemCaseByApiCode(apiCode, ApiCaseStatusEnum.edit);
                        if (count == 0) {
                            addApiSystemCase(apiTest, command.getTransactor());
                        }
                        List<ApiGlobalConfigurationEntityDO> configList = apiTestQueryDomainService.getApiConfig(
                                apiTest.getProductCode(), apiTest.getAppId(), apiTest.getMainApiCode());
                        List<String> keys = GenerateApiCaseUtil.parseApiFieldKey(apiTest.getApiType(), apiTest.getApiData());
                        List<String> caseCodeList = apiTestRepository.queryApiCaseCodeByApiCode(apiCode);
                        caseCodeList.forEach(caseCode -> {
                            ApiTestCaseEntityDO apiCase = apiTestRepository.queryApiCaseByCodeAndStatus(caseCode, ApiCaseStatusEnum.edit);
                            doRefreshApiCaseException(apiCase, apiTest, command.getTransactor(), configList, keys);
                        });
                    } catch (Exception e) {
                        log.error("接口[{}]一键生成异常数据失败！", apiCode, e);
                    }
                });
                return;
            }
            if (CollectionUtils.isNotEmpty(command.getCaseCodes())) {
                Map<String, ApiTestEntityDO> apiMap = new HashMap<>();
                Map<String, List<ApiGlobalConfigurationEntityDO>> configMap = new HashMap<>();
                Map<String, List<String>> keyMap = new HashMap<>();
                command.getCaseCodes().forEach(caseCode -> {
                    try {
                        ApiTestCaseEntityDO apiCase = apiTestRepository.queryApiCaseByCodeAndStatus(caseCode, ApiCaseStatusEnum.edit);
                        if (null == apiCase || StringUtils.isBlank(apiCase.getApiCode())) {
                            return;
                        }
                        ApiTestEntityDO apiTest = apiMap.get(apiCase.getApiCode());
                        if (null == apiTest) {
                            apiTest = apiTestRepository.queryLatestApiTestByMainCode(apiCase.getApiCode());
                            if (null == apiTest) {
                                return;
                            }
                            apiMap.put(apiTest.getApiCode(), apiTest);
                        }
                        if (null == configMap.get(apiTest.getApiCode())) {
                            List<ApiGlobalConfigurationEntityDO> configList = apiTestQueryDomainService.getApiConfig(
                                    apiTest.getProductCode(), apiTest.getAppId(), apiTest.getMainApiCode());
                            configMap.put(apiTest.getApiCode(), configList);
                        }
                        if (null == keyMap.get(apiTest.getApiCode())) {
                            List<String> keys = GenerateApiCaseUtil.parseApiFieldKey(apiTest.getApiType(), apiTest.getApiData());
                            keyMap.put(apiTest.getApiCode(), keys);
                        }
                        doRefreshApiCaseException(apiCase, apiTest, command.getTransactor(),
                                configMap.get(apiTest.getApiCode()), keyMap.get(apiTest.getApiCode()));
                    } catch (Exception e) {
                        log.error("用例[{}]一键生成异常数据失败！", caseCode, e);
                    }
                });
            }
        });
    }

    private void addApiSystemCase(ApiTestEntityDO apiTest, User transactor) {
        PageApiInfoResp resp = new PageApiInfoResp();
        ApiSampleCaseVO vo = new ApiSampleCaseVO();
        vo.setApiName(apiTest.getApiName());
        vo.setApiType(apiTest.getApiType());
        vo.setApiData(apiTest.getApiData());
        GenerateApiCaseUtil.convertPageApiInfoResp(vo, resp);

        JSONObject sampler = null;
        if (ApiTypeEnum.HTTP.equals(apiTest.getApiType())) {
            HttpRequestComponent component = new HttpRequestComponent();
            component.setProtocol(resp.getProtocol());
            component.setServerNameOrIp(resp.getHost());
            component.setPortNumber(resp.getPortNumber());
            component.setMethod(apiTest.getReqMethod().name());
            component.setPathUrl(resp.getPath());
            component.setBodyData(resp.getBody());
            if (StringUtils.isNotEmpty(resp.getBody())) {
                component.setType(LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_BODY_DATA_TYPE);
            } else {
                component.setType(LinkMapTypeEnum.HTTP_REQUEST_COMPONENT_FORM_TYPE);
            }
            if (CollectionUtils.isNotEmpty(resp.getHeaders())) {
                JSONArray httpRequestHeader = new JSONArray();
                resp.getHeaders().forEach(header -> {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", header.getKey());
                    jsonObject.put("value", header.getValue());
                    httpRequestHeader.add(jsonObject);
                });
                component.setHttpRequestHeader(httpRequestHeader);
            }
            if (CollectionUtils.isNotEmpty(resp.getPathParams())) {
                JSONArray pathParams = new JSONArray();
                resp.getPathParams().forEach(pathParam -> {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", pathParam.getKey());
                    jsonObject.put("value", pathParam.getValue());
                    pathParams.add(jsonObject);
                });
                component.setPathParams(pathParams);
            }
            if (CollectionUtils.isNotEmpty(resp.getParams())) {
                List<HttpParameter> parameters = resp.getParams().stream()
                        .map(param -> {
                            HttpParameter httpParameter = new HttpParameter();
                            httpParameter.setType(param.getKey());
                            httpParameter.setValue(param.getValue());
                            httpParameter.setType(param.getTypeValue());
                            httpParameter.setUrlEncode(false);
                            httpParameter.setIncludeEquals(false);
                            return httpParameter;
                        })
                        .collect(Collectors.toList());
                component.setParameters(parameters);
            }
            sampler = (JSONObject) JSON.toJSON(component);
        }
        if (ApiTypeEnum.DUBBO.equals(apiTest.getApiType())) {
            DubboRequestComponent component = new DubboRequestComponent();
            component.setInterfaceName(resp.getInterfaceName());
            component.setMethodName(resp.getMethodName());
            component.setGroup(StringUtils.defaultString(resp.getGroup()));
            component.setVersion(StringUtils.defaultString(resp.getVersion()));
            component.setType(LinkMapTypeEnum.DUBBO_REQUEST_COMPONENT);
            if (CollectionUtils.isNotEmpty(resp.getParams())) {
                JSONArray args = new JSONArray();
                resp.getParams().forEach(arg -> {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("paramType", arg.getKey());
                    jsonObject.put("paramValue", arg.getValue());
                    args.add(jsonObject);
                });
                component.setArgs(args);
            }
            sampler = (JSONObject) JSON.toJSON(component);
            sampler.put("method", "dubbo");
        }
        JSONObject caseReqData = new JSONObject();
        caseReqData.put("interfaceType", apiTest.getApiType().getValue());
        caseReqData.put("requestUrl", resp.getRequestUrl());
        caseReqData.put("gatewaySource", resp.getGatewaySource());
        caseReqData.put("apiName", apiTest.getApiName());
        caseReqData.put("apiCode", apiTest.getApiCode());
        caseReqData.put("docId", apiTest.getDocId());
        caseReqData.put("docName", resp.getDocName());
        caseReqData.put("docProductCode", apiTest.getDocProductCode());
        caseReqData.put("sampler", sampler);

        String caseCode = aggregateIdGenerateService.generateId(AggregateType.TEST_CASE);
        AddApiTestCaseCommand addApiTestCaseCommand = new AddApiTestCaseCommand(caseCode);
        addApiTestCaseCommand.setProductCode(apiTest.getProductCode());
        addApiTestCaseCommand.setCaseName(StrUtil.maxLength(apiTest.getApiName(), 97));
        addApiTestCaseCommand.setApiCode(apiTest.getMainApiCode());
        addApiTestCaseCommand.setStatus(ApiCaseStatusEnum.edit);
        addApiTestCaseCommand.setCaseType(ApiCaseTypeEnum.SYSTEM_CASE.getCode());
        addApiTestCaseCommand.setCaseReqData(caseReqData.toJSONString());
        addApiTestCaseCommand.setTransactor(transactor);
        addApiTestCase(addApiTestCaseCommand);
    }

    private void doRefreshApiCaseException(
            ApiTestCaseEntityDO apiCase,
            ApiTestEntityDO apiTest,
            User transactor,
            List<ApiGlobalConfigurationEntityDO> configList,
            List<String> keys) {
        log.info("刷新异常数据断言: {}", apiCase.getCaseCode());
        String key = String.format("process::%s", apiCase.getCaseCode());
        LockSeal lockSeal = null;
        try {
            lockSeal = lockService.acquireLock(
                    LockStoreEnum.EXCEPTION_CASE.getValue(), Collections.singletonList(key), 30000L);
            if (null == lockSeal) {
                log.warn("异常数据正在生成中！[{}]", key);
                return;
            }
            redisService.setKey(key, "1", 5L, TimeUnit.MINUTES);
            List<ApiTestCaseEntityDO> caseList = apiTestRepository.querySubApiCasesByParentCaseCodes(
                    Collections.singletonList(apiCase.getCaseCode()), ApiCaseStatusEnum.edit);
            List<ApiCaseExceptionVO> exceptionCaseList = caseList.stream()
                    .filter(item -> ApiCaseTypeEnum.EXCEPTION_CASE.getCode() == item.getCaseType())
                    .map(item -> {
                        ApiCaseExceptionVO vo = new ApiCaseExceptionVO();
                        if (StringUtils.isNotBlank(item.getCaseReqData())) {
                            vo = JSON.parseObject(item.getCaseReqData(), ApiCaseExceptionVO.class);
                        }
                        vo.setCaseCode(item.getCaseCode());
                        return vo;
                    }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(exceptionCaseList)) {
                ApiTestCaseEntityDO entityDO = new ApiTestCaseEntityDO();
                entityDO.setId(apiCase.getId());
                entityDO.setInitStatus(ApiCaseInItStatusEnum.SUCCESS.getCode());
                apiTestRepository.updateApiTestCaseSelective(entityDO);
                if (CollectionUtil.isEmpty(configList) || CollectionUtil.isEmpty(keys)) {
                    return;
                }
            }
            List<String> deleteList = exceptionCaseList.stream()
                    .filter(item -> !keys.contains(item.getKey()))
                    .map(ApiCaseExceptionVO::getCaseCode)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deleteList)) {
                apiTestRepository.batchDeleteApiCaseExceptionByCodeList(deleteList, transactor);
            }
            List<ApiConfigTypeEnum> apiConfigTypeList = configList.stream()
                    .map(config -> ApiConfigTypeEnum.codeOf(config.getApiConfigType()))
                    .collect(Collectors.toList());
            List<ApiFieldConfigVO> apiFieldConfigList = new ArrayList<>();
            List<ApiCaseExceptionVO> diffApiCaseList = new ArrayList<>();
            keys.forEach(fieldKey -> {
                List<ApiCaseExceptionVO> list = exceptionCaseList.stream()
                        .filter(item -> fieldKey.equals(item.getKey()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(list)) {
                    ApiFieldConfigVO vo = new ApiFieldConfigVO();
                    vo.setKey(fieldKey);
                    vo.setApiConfigTypeList(apiConfigTypeList);
                    apiFieldConfigList.add(vo);
                } else {
                    List<ApiConfigTypeEnum> newApiConfigTypeList = apiConfigTypeList.stream()
                            .filter(configType ->
                                    list.stream().noneMatch(item -> configType.equals(item.getApiConfigType())))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(newApiConfigTypeList)) {
                        ApiFieldConfigVO vo = new ApiFieldConfigVO();
                        vo.setKey(fieldKey);
                        vo.setApiConfigTypeList(newApiConfigTypeList);
                        apiFieldConfigList.add(vo);
                    }
                    diffApiCaseList.addAll(list);
                }
            });
            ApiCaseGenerateParameter parameter = new ApiCaseGenerateParameter();
            parameter.setApiType(apiTest.getApiType());
            parameter.setApiData(apiTest.getApiData());
            parameter.setApiFieldConfigList(apiFieldConfigList);
            parameter.setApiGlobalConfigList(configList);
            parameter.setInitialize(true);
            parameter.setApiGatewayRule(qcConfigBasicService.getApiGatewayRule());
            parameter.setDiffApiCaseList(diffApiCaseList);
            List<ApiCaseExceptionVO> insertList = GenerateApiCaseUtil.generateApiCaseException(parameter);
            if (CollectionUtils.isNotEmpty(insertList)) {
                insertList.forEach(exceptionCase -> {
                    exceptionCase.setLabel("insert");
                    insertApiCaseException(exceptionCase, apiCase, transactor);
                });
            }
            List<ApiCaseExceptionVO> updateList = GenerateApiCaseUtil.diffApiCaseException(parameter);
            if (CollectionUtils.isNotEmpty(updateList)) {
                updateList.forEach(exceptionCase -> {
                    exceptionCase.setLabel("update");
                    ApiTestCaseEntityDO caseEntityDO = new ApiTestCaseEntityDO();
                    caseEntityDO.setCaseCode(exceptionCase.getCaseCode());
                    caseEntityDO.setCaseReqData(JSON.toJSONString(exceptionCase));
                    caseEntityDO.preUpdate(transactor);
                    apiTestRepository.updateExceptionCaseByCode(caseEntityDO);
                });
            }
            if (CollectionUtils.isEmpty(insertList) && CollectionUtils.isEmpty(updateList)) {
                return;
            }
            ApiTestCaseEntityDO publish = apiTestRepository.queryApiCaseByCodeAndStatus(apiCase.getCaseCode(), ApiCaseStatusEnum.publish);
            if (null != publish) {
                apiTestRepository.addApiTestCaseTag(apiCase.getCaseCode(), SceneTagEnum.API_CASE_MODIFY, transactor);
            }
        } finally {
            lockService.releaseLock(lockSeal);
            redisService.delete(key);
            ApiTestCaseEntityDO entityDO = new ApiTestCaseEntityDO();
            entityDO.setId(apiCase.getId());
            entityDO.preUpdate(transactor);
            apiTestRepository.updateApiTestCaseSelective(entityDO);
        }
    }

    private void insertApiCaseException(ApiCaseExceptionVO exceptionCase, ApiTestCaseEntityDO apiCase, User transactor) {
        String caseName = String.format("%s_%s_%s",
                exceptionCase.getKey(), exceptionCase.getApiConfigType().getDesc(), exceptionCase.getValue());
        if (caseName.length() > 100) {
            caseName = StrUtil.maxLength(caseName, 97);
        }
        ApiTestCaseEntityDO caseEntityDO = new ApiTestCaseEntityDO();
        caseEntityDO.setCaseCode(aggregateIdGenerateService.generateId(AggregateType.TEST_CASE));
        caseEntityDO.setCaseName(caseName);
        caseEntityDO.setProductCode(apiCase.getProductCode());
        caseEntityDO.setApiCode(apiCase.getApiCode());
        caseEntityDO.setRelatedApiName(apiCase.getRelatedApiName());
        caseEntityDO.setRelatedApiAddress(apiCase.getRelatedApiAddress());
        caseEntityDO.setApiType(apiCase.getApiType());
        caseEntityDO.setDocVersion(apiCase.getDocVersion());
        caseEntityDO.setStatus(ApiCaseStatusEnum.edit);
        caseEntityDO.setCaseType(ApiCaseTypeEnum.EXCEPTION_CASE.getCode());
        caseEntityDO.setCaseReqData(JSON.toJSONString(exceptionCase));
        caseEntityDO.setGenerateRules(exceptionCase.getApiConfigType());
        caseEntityDO.setGenerateFieldSource(exceptionCase.getKey());
        caseEntityDO.setParentCaseCode(apiCase.getCaseCode());
        caseEntityDO.setEnable(ApiCaseEnableEnum.ENABLED);
        caseEntityDO.preCreate(transactor);
        caseEntityDO.setLatestTaskId("");
        apiTestRepository.insertApiTestCaseSelective(caseEntityDO);
    }

    @Async
    public void refreshApiCasePreDataUpdate() {
        log.info("refreshApiCasePreDataUpdate_begin");
        //所有包含造数用例code
        List<ApiTestCaseEntityDO> apiTestCaseEntityDOList = apiTestRepository.selectApiTestCaseContainPreData();
        if (CollectionUtil.isEmpty(apiTestCaseEntityDOList)) {
            return;
        }
        //刷新造数标识
        apiTestCaseEntityDOList.forEach(draftCase -> {
            if (StringUtils.isNotBlank(draftCase.getCaseReqData())) {
                JSONObject caseReqData = JSONObject.parseObject(draftCase.getCaseReqData());
                if (Objects.nonNull(caseReqData)) {
                    JSONArray dataCenterArr = caseReqData.getJSONArray("dataCenter");
                    if (CollectionUtils.isNotEmpty(dataCenterArr)) {
                        for (int i = 0; i < dataCenterArr.size(); i++) {
                            JSONObject jsonObject = dataCenterArr.getJSONObject(i);
                            jsonObject.put("preDataUpdated", false);
                            dataCenterArr.set(i, jsonObject);
                        }
                        caseReqData.put("dataCenter", dataCenterArr);
                        ApiTestCaseEntityDO toUpdate = new ApiTestCaseEntityDO();
                        toUpdate.setCaseReqData(caseReqData.toJSONString());
                        toUpdate.setId(draftCase.getId());
                        apiTestRepository.updateApiTestCaseSelective(toUpdate);
                    }
                }
            }
        });
        log.info("refreshApiCasePreDataUpdate_end");
    }

    @Async
    public void refreshApiTestRelateScene() {
        log.info("refreshApiTestRelateScene_begin");
        //所有删除的scene
        Set<String> sceneCodeSet = apiTestRepository.selectDisableSceneCode();
        if (CollectionUtil.isEmpty(sceneCodeSet)) {
            return;
        }
        //批量删除关联关系
        List<List<String>> partCodes = Lists.partition(new ArrayList<>(sceneCodeSet), 50);
        partCodes.forEach(codes -> apiTestRepository.batchDeleteSceneApiRelation(codes));
        log.info("refreshApiTestRelateScene_end");
    }

    public void uploadJmxFile(JmxFileUploadCommand command) {
        log.info("开始解析jmx文件，JmxFileUploadCommand:{}", JSON.toJSONString(command));
        try {
            Document document = jmxUtil.getJmeterDocument(
                    command.getBucketName(), command.getAddress(), command.getFileName());
            JmxParseSceneVO sceneVO = new JmxParseSceneVO();
            sceneVO.setProductCode(command.getProductCode());
            sceneVO.setSceneCode(command.getSceneCode());
            sceneVO.setUser(command.getTransactor());
            sceneVO.setType(command.getType());
            // 解析脚本
            jmxParseUtil.parseJmx(document, sceneVO);
            // 变量表插入数据库id
            Set<Integer> dbIdSet = sceneVO.getScene().getDbIds();
            if (CollectionUtil.isNotEmpty(dbIdSet)) {
                List<Integer> dbIds = new ArrayList<>(dbIdSet);
                if (CollectionUtil.isNotEmpty(dbIds)) {
                    saveDbConfig(dbIds, command.getProductCode(), command.getTransactor(),
                            command.getType(), command.getSceneCode());
                }
            }
            // 更新sceneBackData
            SceneInfoEntityDO editSceneInfo = apiTestRepository.queryLatestSceneInfo(command.getSceneCode(), SceneInfoStatusEnum.edit);
            editSceneInfo.setSceneBackData(JSON.toJSONString(sceneVO.getScene()));
            editSceneInfo.setSceneFrontData("");
            editSceneInfo.preUpdate(command);
            apiTestRepository.updateSceneInfoByPrimaryKey(editSceneInfo);
            // 更新全局变量、全局header
            this.addVariableAndHeader(command, sceneVO);
        } catch (Exception e) {
            throw new ServiceException("解析jmeter脚本失败！" + e.toString());
        }
    }

    private void addVariableAndHeader(JmxFileUploadCommand command, JmxParseSceneVO sceneVO) {
        List<BaseApiTestVariableReq> apiTestVariableList = new ArrayList<>();
        List<BaseApiTestVariableReq> apiTestHeaderList = new ArrayList<>();
        if (null != sceneVO.getGlobalVariable()) {
            JSONObject globalVariable = sceneVO.getGlobalVariable();
            Set<String> variableSet = globalVariable.keySet();
            for (String key : variableSet) {
                BaseApiTestVariableReq variableReq = new BaseApiTestVariableReq();
                variableReq.setVariableKey(key);
                variableReq.setVariableValue(globalVariable.get(key).toString());
                apiTestVariableList.add(variableReq);
            }
        }
        if (null != sceneVO.getGlobalHeader()) {
            JSONObject globalHeader = sceneVO.getGlobalHeader();
            Set<String> headerSet = globalHeader.keySet();
            for (String key : headerSet) {
                BaseApiTestVariableReq headerReq = new BaseApiTestVariableReq();
                headerReq.setVariableKey(key);
                headerReq.setVariableValue(globalHeader.get(key).toString());
                apiTestHeaderList.add(headerReq);
            }
        }
        if (CollectionUtil.isNotEmpty(apiTestVariableList) || CollectionUtil.isNotEmpty(apiTestHeaderList)) {
            BatchAddVariableCommand variableCommand = new BatchAddVariableCommand(command.getSceneCode());
            variableCommand.setProductCode(command.getProductCode());
            variableCommand.setProductName(command.getProductName());
            variableCommand.setType(VariableTypeEnum.VARIABLE);
            variableCommand.setLinkCode(command.getSceneCode());
            variableCommand.setSceneType(command.getType());
            variableCommand.setApiTestVariableList(apiTestVariableList);
            variableCommand.setApiTestHeaderList(apiTestHeaderList);
            variableCommand.setTransactor(command.getTransactor());
            try {
                this.batchAddVariable(variableCommand);
            } catch (Exception e) {
                log.error("导入脚本-设置全局变量异常：" + e.getMessage());
            }
        }
    }

    @Async
    public void refreshApiTestField(RefreshApiTestFieldReq req) {
        if ("tm_api_test.tag_value".equals(req.getSource())) {
            log.info("刷新tm_api_test字段tag_value");
            List<String> list = tagRepository.findBusinessCodeByTag(SceneTagEnum.API_UPDATE);
            for (String apiCode : list) {
                ApiTestEntityDO apiTest = apiTestRepository.queryApiDetailByCode(apiCode);
                if (null != apiTest && StringUtils.isNotEmpty(apiTest.getMainApiCode())) {
                    apiTestRepository.updateApiTestTagByMainApiCode(apiTest.getMainApiCode(), SceneTagEnum.API_UPDATE.name());
                    tagRepository.removeSceneTag(apiCode, Collections.singletonList(SceneTagEnum.API_UPDATE));
                }
            }
        }
        if ("tm_api_test_case.api_code".equals(req.getSource())) {
            log.info("刷新tm_api_test_case字段api_code");
            List<String> list = apiTestRepository.queryApiCaseAllApiCode(req.getProductCode());
            for (String apiCode : list) {
                ApiTestEntityDO apiTest = apiTestRepository.queryApiDetailByCode(apiCode);
                if (null != apiTest && StringUtils.isNotEmpty(apiTest.getMainApiCode())) {
                    apiTestRepository.updateApiCaseMainApiCode(apiCode, apiTest.getMainApiCode());
                }
            }
        }
        if ("tm_api_config.api_config_value".equals(req.getSource())) {
            log.info("刷新tm_api_config字段api_config_value");
            List<String> list = apiGlobalConfigurationRepository.queryApiConfigAllApiCode(req.getProductCode());
            for (String apiCode : list) {
                ApiTestEntityDO apiTest = apiTestRepository.queryApiDetailByCode(apiCode);
                if (null != apiTest && StringUtils.isNotEmpty(apiTest.getMainApiCode())) {
                    apiGlobalConfigurationRepository.updateApiConfigMainApiCode(apiCode, apiTest.getMainApiCode());
                }
            }
        }
    }

    @Async
    public void addSceneTag(AddSceneTagCommand command) {
        log.info("场景添加标签，AddSceneTagCommand：{}", JSON.toJSONString(command));
        if (CollectionUtils.isEmpty(command.getBusinessCodeList())) {
            return;
        }

        List<String> businessCodeList = command.getBusinessCodeList();
        List<String> tagNameList = command.getTagNameList();
        String tags = "";

        if (CollectionUtils.isNotEmpty(command.getTagNameList())) {
            tagNameList.stream()
                    .filter(tagName -> tagName.length() > 10)
                    .findFirst()
                    .ifPresent(tagName -> {
                        throw new ServiceException("当前标签长度超过最大限制");
                    });
            tags = tagNameList.stream()
                    .map(String::trim) // 移除前后空格
                    .distinct() // 去重
                    .collect(Collectors.joining(","));
        }

        List<SceneInfoEntityDO> sceneInfoEntityDOS = apiTestRepository.querySceneInfoTagList(command.getBusinessCodeList());
        if (CollectionUtils.isEmpty(sceneInfoEntityDOS)) {
            return;
        }
        insertSceneTag(businessCodeList.get(0), tags, command);
    }

    public static Map<String, String> convertToMap(List<SceneInfoEntityDO> sceneInfoEntityDOS) {
        return sceneInfoEntityDOS.stream()
                .collect(Collectors.toMap(
                        SceneInfoEntityDO::getSceneCode, // 键映射函数
                        SceneInfoEntityDO::getSceneTagData, // 值映射函数
                        (existingValue, newValue) -> {
                            throw new IllegalStateException("Duplicate sceneCode: " + existingValue);
                        }
                ));
    }


    private void insertSceneTag(String sceneCode, String tagNameList, AddSceneTagCommand command) {
        SceneInfoEntityDO entityDO = new SceneInfoEntityDO();
        entityDO.setSceneCode(sceneCode);
        entityDO.setSceneTagData(tagNameList);
        entityDO.preUpdate(command);
        apiTestRepository.updateSceneInfoByCode(entityDO);
    }

    private void updateRunSetting(EditSceneInfoCommand command, SceneInfoEntityDO sceneInfo) {
        // 判断是否修改串、并行
        if(checkRunSettingModify(command.getSceneBackData(), sceneInfo.getSceneBackData())) {
            log.info("场景图串并行运行配置修改，异步修改脚本->" + command.getAggregateId());
            MODIFY_OSS_JMETER_FILE__EXECUTOR.execute(() -> {
                modifyOssJmeterFile(command.getSceneBackData(), command.getAggregateId(), sceneInfo);
            });
        }
    }

    private void modifyOssJmeterFile(String sceneBackData, String sceneCode, SceneInfoEntityDO sceneInfo) {
        try{
            boolean existTag = ztoOssService.isExist("autojmx", sceneInfo.getSceneOssPath() + "/edit/scene.jmx");
            if(existTag) {
                log.info("异步修改场景图串并行 -> 开始执行" + sceneCode);
                Document document = jmxUtil.getJmeterDocument(
                        "autojmx", sceneInfo.getSceneOssPath() + "/edit/", "scene.jmx");
                if(null == document) {
                    log.info("异步修改场景图串并行 -> document文件为空!" + sceneCode);
                }
                JSONObject sceneObj = JSON.parseObject(sceneBackData);
                if(null != sceneObj) {
                    JSONObject newRunSetting = sceneObj.getJSONObject("runSettingForm");
                    String method = newRunSetting.getString("method");
                    String time = (null == newRunSetting.getString("time")) ? "" : newRunSetting.getString("time");
                    jmxUtil.modifyThreadGroupSerialize(document, method, time);
                    log.info("异步修改场景图串并行 -> 修改成功!" + sceneCode);
                    jmxUtil.modifyOssFile(document, sceneInfo.getSceneOssPath() + "/edit/scene.jmx");
                    log.info("异步修改场景图串并行 -> 上传成功!" + sceneCode);
                }
            }else{
                log.info("异步修改场景图串并行 -> 未找到oss文件!" + sceneCode);
            }
        } catch (Exception e) {
            log.error("异步修改场景图串并行 -> 失败!" + sceneCode + e.toString());
        }
    }

    private boolean checkRunSettingModify(String newSceneBackData, String lastSceneBackData) {
        JSONObject newSceneObj = JSON.parseObject(newSceneBackData);
        JSONObject lastSceneObj = JSON.parseObject(lastSceneBackData);
        if(null != newSceneObj && null != lastSceneObj) {
            JSONObject newRunSetting = new JSONObject();
            JSONObject lastRunSetting = new JSONObject();
            if(null != newSceneObj.getJSONObject("runSettingForm")) {
                newRunSetting = newSceneObj.getJSONObject("runSettingForm");
            }
            if(null != lastSceneObj.getJSONObject("runSettingForm")) {
                lastRunSetting = lastSceneObj.getJSONObject("runSettingForm");
            }
            return !JSON.toJSONString(newRunSetting).equals(JSON.toJSONString(lastRunSetting));
        }
        return false;
    }

    /**
     * 保存造数调试结果
     * @param taskId
     * @param outputParameter
     */
    private void sendSaveDebugInfoEvent(String taskId, String outputParameter) {
        SaveDebugInfoEvent event = new SaveDebugInfoEvent();
        event.setAggregateId(taskId);
        event.setTaskId(taskId);
        event.setOutput(outputParameter);
        apply(event);
    }

    @Transactional(rollbackFor = Exception.class)
    public void copyApiTestCase(CopyApiTestCaseCommand command) {
        //查原始用例详情
        ApiTestCaseEntityDO sourceDO = apiTestRepository.queryApiCaseByCodeAndStatus(command.getCaseCode(), ApiCaseStatusEnum.edit);
        if (Objects.isNull(sourceDO)) {
            throw new ServiceException("查询待复制用例数据失败，请联系管理员");
        }
        //校验用例名称
        if (!apiTestRepository.isUniqueCaseName(sourceDO.getApiCode(), command.getCaseName())) {
            throw new ServiceException(String.format("用例名称( %s )已存在,请修改以后复制", command.getCaseName()));
        }
        //保存副本
        ApiTestCaseEntityDO targetDO = convert(sourceDO, command.getCaseName(), command.getTransactor());
        apiTestRepository.insertApiTestCaseSelective(targetDO);

        //生成副本正常子用例
        AddApiTestCaseEvent event = new AddApiTestCaseEvent();
        BeanUtil.copyPropertiesNew(targetDO, event);
        doGenerateApiCaseNormal(event, command.getTransactor());

        //复制副本异常子用例
        List<ApiTestCaseEntityDO> exceptionCases = apiTestRepository.queryExceptionCaseByParentCaseCode(sourceDO.getCaseCode(),
                ApiCaseStatusEnum.edit, false, Collections.singletonList(ApiCaseTypeEnum.EXCEPTION_CASE.getCode()));
        if (CollectionUtil.isNotEmpty(exceptionCases)) {
            List<ApiTestCaseEntityDO> toSaveExceptionCases = exceptionCases.stream()
                    .map(item -> (convertExceptionCase(item, targetDO.getCaseCode(), command.getTransactor())))
                    .collect(Collectors.toList());
            apiTestRepository.batchInsertApiTestCase(toSaveExceptionCases);
        }
    }

    private ApiTestCaseEntityDO convertExceptionCase(ApiTestCaseEntityDO entityDO, String parentCode, User transactor) {
        entityDO.setId(null);
        entityDO.setCaseCode(aggregateIdGenerateService.generateId(AggregateType.TEST_CASE));
        entityDO.setParentCaseCode(parentCode);
        entityDO.preCreate(transactor);
        return entityDO;
    }

    private ApiTestCaseEntityDO convert(ApiTestCaseEntityDO sourceDO, String caseName, User transactor) {
        ApiTestCaseEntityDO targetDO = new ApiTestCaseEntityDO();
        cn.hutool.core.bean.BeanUtil.copyProperties(sourceDO, targetDO,
                "id",
                "latestTaskId",
                "latestExecuteTime",
                "latestExecuteResult");
        targetDO.setCaseCode(aggregateIdGenerateService.generateId(AggregateType.TEST_CASE));
        targetDO.setCaseName(caseName);
        targetDO.preCreate(transactor);
        targetDO.setLatestTaskId("");
        targetDO.setStatus(ApiCaseStatusEnum.edit);
        targetDO.setEnable(ApiCaseEnableEnum.DRAFT);
        return targetDO;
    }

    public AddApiTestCaseCommand buildAddApiTestCaseCommandByDocInvokeHistory(DocInvokeHistoryToApiTestCaseVO historyVO, String generateId) {
        log.info("【一键转case】历史记录 >>> {}", JsonUtil.toJSON(historyVO));
        List<ApiTestEntityDO> apiTestList = apiTestRepository.getApiTestByDocId(historyVO.getServiceDocId(), historyVO.getProductCode());
        if (CollectionUtil.isEmpty(apiTestList)){
            throw new ServiceException("数据尚未关联，请稍后重试!");
        }
        ApiTestEntityDO apiTestEntityDO = apiTestList.get(0);
        AddApiTestCaseCommand command = new AddApiTestCaseCommand(generateId);
        command.setStatus(ApiCaseStatusEnum.edit);
        command.setCaseType(ApiCaseTypeEnum.API_CASE.getCode());
        command.setProductCode(historyVO.getProductCode());
        command.setCaseName(historyVO.getCaseName());
        command.setApiCode(apiTestEntityDO.getMainApiCode());
        ApiSampleCaseVO apiSampleCaseVO = convert.convertApiSampleCaseVO(apiTestEntityDO);
        PageApiInfoResp resp = new PageApiInfoResp();
        GenerateApiCaseUtil.convertPageApiInfoResp(apiSampleCaseVO, resp);
        if (ApiTypeEnum.HTTP.equals(historyVO.getApiType())){
            command.setCaseReqData(DocInvokeHttpRequestBuilder.setHTTPSelect(apiTestEntityDO,resp,historyVO));
        }else {
            command.setCaseReqData(DocInvokeDubboRequestBuilder.setDubboSelect(apiTestEntityDO,resp,historyVO));
        }
        return command;
    }

}