package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.issue.entity.IssueMqResp;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.issue.event.IssueAddedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueEditedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueRelatedRequirementEvent;
import com.zto.devops.qc.client.model.issue.event.IssueRemovedEvent;
import com.zto.devops.qc.client.service.issue.model.IssueTagResp;
import org.mapstruct.*;

import java.util.Collection;
import java.util.List;

@Mapper(
        componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface IssueMqConverter {

    @Mapping(target = "findUserId", source = "finder.userId")
    @Mapping(target = "findUserName", source = "finder.userName")
    @Mapping(target = "handleUserId", source = "handler.userId")
    @Mapping(target = "handleUserName", source = "handler.userName")
    @Mapping(target = "developUserId", source = "developer.userId")
    @Mapping(target = "developUserName", source = "developer.userName")
    @Mapping(target = "testUserId", source = "tester.userId")
    @Mapping(target = "testUserName", source = "tester.userName")
    @Mapping(target = "updateUserId", source = "transactor.userId")
    @Mapping(target = "updateUserName", source = "transactor.userName")
    @Mapping(target = "productCode", source = "product.code")
    @Mapping(target = "productName", source = "product.name")
    @Mapping(target = "requirementCode", source = "requirement.code")
    @Mapping(target = "requirementName", source = "requirement.name")
    @Mapping(target = "findVersionCode", source = "findVersion.code")
    @Mapping(target = "findVersionName", source = "findVersion.name")
    @Mapping(target = "fixVersionCode", source = "fixVersion.code")
    @Mapping(target = "fixVersionName", source = "fixVersion.name")
    @Mapping(target = "sprintCode", source = "sprint.code")
    @Mapping(target = "sprintName", source = "sprint.name")
    IssueMqResp convert(IssueAddedEvent event);

    IssueMqResp convert(IssueRemovedEvent event);

    IssueMqResp convert(IssueVO issueVO);

    @Mapping(target = "handleUserId", source = "handler.userId")
    @Mapping(target = "handleUserName", source = "handler.userName")
    @Mapping(target = "developUserId", source = "developer.userId")
    @Mapping(target = "developUserName", source = "developer.userName")
    @Mapping(target = "testUserId", source = "tester.userId")
    @Mapping(target = "testUserName", source = "tester.userName")
    @Mapping(target = "updateUserId", source = "transactor.userId")
    @Mapping(target = "updateUserName", source = "transactor.userName")
    @Mapping(target = "requirementCode", source = "requirement.code")
    @Mapping(target = "requirementName", source = "requirement.name")
    @Mapping(target = "findVersionCode", source = "findVersion.code")
    @Mapping(target = "findVersionName", source = "findVersion.name")
    @Mapping(target = "fixVersionCode", source = "fixVersion.code")
    @Mapping(target = "fixVersionName", source = "fixVersion.name")
    @Mapping(target = "sprintCode", source = "sprint.code")
    @Mapping(target = "modifier", ignore = true)
    void convert(IssueEditedEvent event, @MappingTarget IssueMqResp resp);

    @Mapping(target = "code", source = "issueCode")
    IssueMqResp convert(IssueRelatedRequirementEvent event);

    List<IssueTagResp> convert(Collection<TagVO> vo);
}
