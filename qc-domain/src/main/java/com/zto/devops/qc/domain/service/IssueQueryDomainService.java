package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.zto.devops.framework.client.dto.Page;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.fsm.Event;
import com.zto.devops.framework.common.fsm.State;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.common.util.time.DateUtil;
import com.zto.devops.qc.client.enums.constants.DefaultValueEnum;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.qc.client.enums.rpc.ProductRoleEnum;
import com.zto.devops.qc.client.model.dto.IssueEntityDO;
import com.zto.devops.qc.client.model.dto.TransitionNodeEntityDO;
import com.zto.devops.qc.client.model.issue.entity.*;
import com.zto.devops.qc.client.model.issue.query.*;
import com.zto.devops.qc.client.model.parameter.IssueQueryParameter;
import com.zto.devops.qc.client.model.parameter.TaskBaseParameter;
import com.zto.devops.qc.client.model.parameter.TaskResultParameter;
import com.zto.devops.qc.client.model.relevantUser.query.ListMyTaskVO;
import com.zto.devops.qc.client.model.relevantUser.query.MyIssueQuery;
import com.zto.devops.qc.client.model.relevantUser.query.MyTaskVO;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.devops.qc.client.model.report.entity.IssueNumStatisticsVO;
import com.zto.devops.qc.client.model.rpc.project.*;
import com.zto.devops.qc.client.service.issue.model.ExportIssueReq;
import com.zto.devops.qc.domain.converter.IssueVOConverter;
import com.zto.devops.qc.domain.converter.RelevantUserConverter;
import com.zto.devops.qc.domain.gateway.apollo.IssueExportConfigService;
import com.zto.devops.qc.domain.gateway.repository.*;
import com.zto.devops.qc.domain.gateway.rpc.IProductGroupRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.domain.statemachine.IssueContext;
import com.zto.devops.qc.domain.statemachine.IssueStateMachine;
import com.zto.devops.qc.domain.statemachine.IssueStateMachineUtil;
import com.zto.devops.qc.domain.util.QcDateUtil;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IssueQueryDomainService {

    @Autowired
    private IIssueRepository issueRepository;

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private IssueStateMachineUtil issueStateMachine;

    @Autowired
    private IProjectRpcService projectRpcService;

    @Autowired
    private IssueVOConverter issueVOConverter;

    @Autowired
    private TagQueryDomainService tagQueryDomainService;

    @Autowired
    private AttachmentRepository attachmentRepository;

    @Autowired
    private TransitionNodeRepository transitionNodeRepository;

    private LaneIssueMap laneIssueMap = new LaneIssueMap(new LaneIssueMapCondition(LaneDomainEnum.LIST));

    @Autowired
    private ITestcaseRelationRepository testcaseRelationRepository;

    @Autowired
    private IRelevantUserRepository relevantUserRepository;

    @Autowired
    private RelevantUserConverter relevantUserConverter;

    @Autowired
    private IProductGroupRpcService productGroupRpcService;

    @Autowired
    private IssueExportConfigService issueExportService;

    /**
     * 超级管理员标识
     */
    private static final String SUPPER_USER_PERMISSION_KEY = "globalpermCanWriteProd";

    public Integer requirementQuery(RequirementQuery query) {
        return issueRepository.selectCountByRequirementCode(query.getRequirementCode());
    }

    public PageIssueVO pageIssueQuery(PageIssueQuery query) {
        PageIssueVO listIssueVO = new PageIssueVO();

        if (CollectionUtil.isEmpty(query.getRelatedProductList()) && query.getCreateTimeStart() == null && query.getCreateTimeEnd() == null) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.MONTH, -1);
            query.setCreateTimeEnd(new Date());
            query.setCreateTimeStart(cal.getTime());
        }
        Page<IssueVO> page = issueRepository.pageIssueQuery(query);
        setExternalBatchNameHandle(page.getList());
        listIssueVO.setList(page.getList());
        listIssueVO.setTotal(page.getTotal());
        return listIssueVO;
    }

    public List<RelatedMatterStatusCountVO> listRelatedIssueStatusCountQuery(ListRelatedIssueStatusCountQuery query) {
        List<RelatedMatterStatusCountVO> relatedMatterStatusCountVOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(query.getRequirementCodeList())) {
            relatedMatterStatusCountVOList = issueRepository.selectRelatedMatterStatusCount(query.getRequirementCodeList());
        }
        return relatedMatterStatusCountVOList;
    }

    public List<IssueVO> listIssueForVersionQuery(ListIssueForVersionQuery query) {
        List<IssueVO> issues = issueRepository.listIssueForVersionQuery(query);
        issues = issues.stream().distinct().collect(Collectors.toList());
        boolean isSuperUser = false;
        if (StringUtil.isNotEmpty(query.getProductCode()) && query.getTransactor() != null) {
            List<ProductRoleEnum> productRoles = productRpcService.getProductRole(query.getProductCode(), query.getTransactor().getUserId());
            isSuperUser = isSuperUser(productRoles, new ArrayList<String>(query.getTransactor().getPermissions()));
        }
        issueStateMachine.buildButtons(issues, query.getTransactor());
        issueStateMachine.buildFields(issues, query.getTransactor(), isSuperUser);
        return issues;
    }

    private boolean isSuperUser(List<ProductRoleEnum> productRoles, List<String> permissions) {
        if (CollectionUtil.isNotEmpty(permissions)
                && permissions.contains(SUPPER_USER_PERMISSION_KEY)) {
            return true;
        }
        return CollectionUtil.isNotEmpty(productRoles)
                && (productRoles.contains(ProductRoleEnum.DEVELOPER_OWNER) || productRoles.contains(ProductRoleEnum.ARCHITECT) || productRoles.contains(ProductRoleEnum.TESTER_OWNER));
    }

    public PageIssueVO pageIssueThingQuery(PageIssueThingQuery query) {
        PageIssueVO listIssueVO = new PageIssueVO();
        Page<String> page = issueRepository.pageIssueCodeThingQuery(query);
        if (Objects.isNull(page) || CollectionUtil.isEmpty(page.getList())) {
            listIssueVO.setList(CollectionUtil.newEmptyList());
            listIssueVO.setTotal(0L);
            return listIssueVO;
        }
//        IssueQueryParameter parameter = issueVOConverter.convert(query);
        IssueQueryParameter parameter = new IssueQueryParameter();
        listIssueVO.setTotal(page.getTotal());
        parameter.setIssueCodeList(page.getList());
        parameter.setOrderField(query.getOrderField());
        parameter.setOrderType(query.getOrderType());
        parameter.setCcUserIdList(query.getCcUserIdList());
        List<IssueVO> issueVOList = issueRepository.doSelectByDefinedQuery(parameter);
        List<TransitionNodeEntityDO> nodeList = transitionNodeRepository.selectContentByIssueList(issueVOList.stream().map(i->i.getCode()).collect(Collectors.toList()));
        Map<String,TransitionNodeEntityDO> result = nodeList.stream().collect(Collectors.toMap(TransitionNodeEntityDO::getBusinessCode, u -> u, (v1, v2) -> v2));
        for(IssueVO issueVO : issueVOList ){
            if(result.containsKey(issueVO.getCode())){
                issueVO.setContent(result.get(issueVO.getCode()).getContent());
            }
        }
        setExternalBatchNameHandle(issueVOList);
        if (query.isNeedEvent()) {
            buildButtons(issueVOList, query.getCurrentUserId(), query.getPermissions());
        }
        listIssueVO.setList(issueVOList);
        return listIssueVO;
    }

    private void setExternalBatchNameHandle(List<IssueVO> issueVOList) {
        List<Date> holidayList = getThisYearHoliday();
        //获取需求
        List<String> requirementCodeList = issueVOList.stream().filter(vo -> DefaultValueEnum.REQUIREMENT_VALUE.isNotNone(vo.getRequirementCode())).map(IssueVO::getRequirementCode).distinct().collect(Collectors.toList());
        Map<String, String> requirementInfoMap = buildRequirement(requirementCodeList);
        issueVOList.forEach(issueVO -> {
            if (StringUtil.isNotEmpty(issueVO.getRequirementCode()) && !issueVO.getRequirementCode().equals("NotAssociated")) {
                issueVO.setRequirementName(requirementInfoMap.get(issueVO.getRequirementCode()));
            }
        });
        //获取关联迭代code
        List<String> sprintCodeList = issueVOList.stream().filter(vo -> StringUtil.isNotEmpty(vo.getSprintCode()) && !("NotAssociated").equals(vo.getSprintCode())).map(IssueVO::getSprintCode).distinct().collect(Collectors.toList());
        Map<String, String> sprintMap = buildSprint(sprintCodeList);
        issueVOList.forEach(issueVO -> {
            if (StringUtil.isNotEmpty(issueVO.getSprintCode()) && !issueVO.getSprintCode().equals("NotAssociated")) {
                issueVO.setSprintName(sprintMap.get(issueVO.getSprintCode()));
            }
        });

        Set<String> versionSet = new HashSet<String>();
        List<String> findversionCodes = issueVOList.stream().filter(vo -> StringUtil.isNotEmpty(vo.getFindVersionCode()) && !("NotAssociated").equals(vo.getFindVersionCode())).map(IssueVO::getFindVersionCode).distinct().collect(Collectors.toList());
        List<String> fixversionCodes = issueVOList.stream().filter(vo -> StringUtil.isNotEmpty(vo.getFixVersionCode()) && !("NotAssociated").equals(vo.getFixVersionCode())).map(IssueVO::getFixVersionCode).distinct().collect(Collectors.toList());
        //发现版本编码
        versionSet.addAll(findversionCodes);
        //修复版本编码
        versionSet.addAll(fixversionCodes);
        Map<String, String> versionInfoMap = buildVersion(versionSet);
        issueVOList.forEach(issueVO -> {
            if (StringUtil.isNotEmpty(issueVO.getFixVersionCode()) && !issueVO.getFixVersionCode().equals("NotAssociated")) {
                issueVO.setFixVersionName(versionInfoMap.get(issueVO.getFixVersionCode()));
            }
            if (StringUtil.isNotEmpty(issueVO.getFindVersionCode()) && !issueVO.getFindVersionCode().equals("NotAssociated")) {
                issueVO.setFindVersionName(versionInfoMap.get(issueVO.getFindVersionCode()));
            }
        });
    }

    private void buildWarnIssueVO(List<IssueVO> issueVOS) {
        issueVOS.stream().forEach(myTaskVO -> {
            String warn = "";
            double divide = new BigDecimal(myTaskVO.getWarnDay()/24).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            double compareDay = IssuePriority.LOW.name().equals(myTaskVO.getPriority()) ? 1*24 : 0.5*24;
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.WAIT_FIX.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未处理";
                warn = "超时" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.FIXING.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未修复";
                warn = "超时" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.TESTING.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未验证";
                warn = "超时" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.REJECTED.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未处理";
                warn = "超时" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > 7*24) {
                // warn = "已逾期超过7天";
                warn = "超时" + divide + "天";
            }
            myTaskVO.setWarn(warn);
        });
    }

    private void buildWarnIssueVONew(List<IssueVO> issueVOS) {
        issueVOS.stream().forEach(myTaskVO -> {
            String warn = "";
            double divide = new BigDecimal(myTaskVO.getWarnDay()/24).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            double compareDay = IssuePriority.LOW.name().equals(myTaskVO.getPriority()) ? 1*24 : 0.5*24;
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.WAIT_FIX.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未处理";
                warn = "逾期" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.FIXING.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未修复";
                warn = "逾期" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.TESTING.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未验证";
                warn = "逾期" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.REJECTED.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未处理";
                warn = "逾期" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > 7*24) {
                // warn = "已逾期超过7天";
                warn = "逾期" + divide + "天";
            }
            myTaskVO.setWarn(warn);
        });
    }


    private Map<String, String> buildVersion(Set<String> versionSet) {
        Map<String, String> result = new HashMap<>();

        if (CollectionUtil.isEmpty(versionSet)) {
            return result;
        }

        if (CollectionUtil.isEmpty(versionSet)) {
            return result;
        }
        SimpleVersionListQuery versionQuery = new SimpleVersionListQuery();
        versionQuery.setCode(new ArrayList<>(versionSet));
        SimpleListVersionVO listVersionVO = projectRpcService.simpleVersionListQuery(versionQuery);
        if (listVersionVO == null) {
            return result;
        }
        List<SimpleVersionVO> simpleVersionVOList = listVersionVO.getSimpleVersionVOList();
        result = simpleVersionVOList.stream().collect(Collectors.toMap(SimpleVersionVO::getCode, SimpleVersionVO::getName, (entity1, entity2) -> entity1));
        return result;
    }

    private Map<String, String> buildSprint(List<String> sprintCodeList) {
        Map<String, String> result = new HashMap<>();

        if (CollectionUtil.isEmpty(sprintCodeList)) {
            return result;
        }
        SimpleListSprintQuery sprintQuery = new SimpleListSprintQuery();
        sprintQuery.setCode(sprintCodeList);
        SimpleListSprintVO listSprintVO = projectRpcService.simpleListSprintQuery(sprintQuery);
        if (listSprintVO == null) {
            return result;
        }
        List<SimpleSprintVO> simpleSprintVOList = listSprintVO.getSimpleSprintVOList();
        result = simpleSprintVOList.stream().collect(Collectors.toMap(SimpleSprintVO::getCode, SimpleSprintVO::getName));
        return result;

    }

    private Map<String, String> buildRequirement(List<String> requirementCodeList) {
        Map<String, String> result = new HashMap<>();
        if (CollectionUtil.isEmpty(requirementCodeList)) {
            return result;
        }
        SimpleListRequirementQuery requirementQuery = new SimpleListRequirementQuery();
        requirementQuery.setRequirementCode(requirementCodeList);
        SimpleListRequirementVO listRequirementVO = projectRpcService.simpleListRequirementQuery(requirementQuery);
        if ((listRequirementVO == null)) {
            return result;
        }
        List<SimpleRequirementVO> simpleRequirementVOList = listRequirementVO.getSimpleRequirementVOList();
        result = simpleRequirementVOList.stream().collect(Collectors.toMap(SimpleRequirementVO::getCode, SimpleRequirementVO::getTitle));

        return result;
    }

    private void buildButtons(List<IssueVO> issueVOList, Long currentUserId, List<String> permissions) {
        issueVOList.forEach(s -> {
            if (Objects.equals(currentUserId, s.getDevelopUserId())) {
                s.setTransitToDevelop(true);
            }
            if (Objects.equals(currentUserId, s.getTestUserId())) {
                s.setTransitToTest(true);
            }
            IssueContext context = IssueContext.convert(s, currentUserId, permissions);
            List<Event> events = IssueStateMachineUtil.getInstance().readyEvent(s.getStatus().toState(), context);
            events.removeIf(event -> (event.getCode().equals(IssueEvent.START.toEvent().getCode()) && event.getName().equals(IssueEvent.START.toEvent().getName())
                    || event.getCode().equals(IssueEvent.END.toEvent().getCode()) && event.getName().equals(IssueEvent.END.toEvent().getName())));
            if (CollectionUtil.isNotEmpty(events)) {
                List<com.zto.devops.qc.client.model.issue.entity.Event> collect = events.stream().map(e -> new com.zto.devops.qc.client.model.issue.entity.Event(e.getCode())).collect(Collectors.toList());
                List<com.zto.devops.qc.client.model.issue.entity.Event> operableEvents = sortOperableEvents(collect);
                s.setOperableEvents(operableEvents);
            }
        });
    }

    private List<com.zto.devops.qc.client.model.issue.entity.Event> sortOperableEvents(List<com.zto.devops.qc.client.model.issue.entity.Event> collect) {
        List<com.zto.devops.qc.client.model.issue.entity.Event> operableEvents = new LinkedList<>();
        collect.forEach(c -> {
            if (IssueEvent.FIX.getCode().equals(c.getCode())) {
                c.setSort(1);
                c.setName(IssueEvent.FIX.getName());
                operableEvents.add(c);
            }
            if (IssueEvent.REOPEN.getCode().equals(c.getCode())) {
                c.setSort(2);
                c.setName(IssueEvent.REOPEN.getName());
                operableEvents.add(c);
            }
            if (IssueEvent.TEST_PASS_CLOSE.getCode().equals(c.getCode())) {
                c.setSort(3);
                c.setName(IssueEvent.TEST_PASS_CLOSE.getName());
                operableEvents.add(c);
            }
            if (IssueEvent.CONFIRM_CLOSE.getCode().equals(c.getCode())) {
                c.setSort(4);
                c.setName(IssueEvent.CONFIRM_CLOSE.getName());
                operableEvents.add(c);
            }
            if (IssueEvent.RETURN.getCode().equals(c.getCode())) {
                c.setSort(5);
                c.setName(IssueEvent.RETURN.getName());
                operableEvents.add(c);
            }
            if (IssueEvent.DELIVER.getCode().equals(c.getCode())) {
                c.setSort(6);
                c.setName(IssueEvent.DELIVER.getName());
                operableEvents.add(c);
            }
            if (IssueEvent.DELAY.getCode().equals(c.getCode())) {
                c.setSort(7);
                c.setName(IssueEvent.DELAY.getName());
                operableEvents.add(c);
            }
            if (IssueEvent.TRANSFER.getCode().equals(c.getCode())) {
                c.setSort(8);
                c.setName(IssueEvent.TRANSFER.getName());
                operableEvents.add(c);
            }
            if (IssueEvent.REJECT.getCode().equals(c.getCode())) {
                c.setSort(9);
                c.setName(IssueEvent.REJECT.getName());
                operableEvents.add(c);
            }
            if (IssueEvent.REMOVE.getCode().equals(c.getCode())) {
                c.setSort(10);
                c.setName(IssueEvent.REMOVE.getName());
                operableEvents.add(c);
            }
        });
        operableEvents.sort(Comparator.comparing(com.zto.devops.qc.client.model.issue.entity.Event::getSort));
        return operableEvents;
    }

    public List<IssueBaseVO> listBySprintQuery(ListBySprintQuery query) {
        return issueRepository.listBySprintQuery(query);
    }

    public List<IssueVO> simpleIssueQuery(SimpleIssueQuery query) {
        if (CollectionUtil.isNotEmpty(query.getCode()) && query.getCode().size() > 1000) {
            throw new ServiceException("批次查询缺陷code超出最大数量1000限制");
        }
        List<IssueEntityDO> issueEntities = issueRepository.selectBySimpleIssueQuery(query);
        List<IssueVO> issueVOList = issueVOConverter.convert(issueEntities);
        setExternalBatchNameHandle(issueVOList);
        return issueVOList;
    }

    public IssueVO findIssueByCode(FindIssueByCodeQuery query) {
        IssueEntityDO issueDO = issueRepository.findIssueByCode(query.getCode());
        if (issueDO == null) {
            log.error("没有对应的缺陷数据 ，issueCode = {}", query.getCode());
            return null;
        }
        IssueVO issueVO = issueVOConverter.convert(issueDO);
        List<ProductRoleEnum> productRoles = productRpcService.getProductRoleForIssue(issueVO.getProductCode(), query.getCurrentUserId());
        //获取当前issue的tag信息
        List<TagVO> tagVOList = tagQueryDomainService.listTagsByBusinessCodeQuery(issueVO.getCode());
        Integer files = attachmentRepository.getAttachmentCount(issueDO.getCode());
        List<TransitionNodeVO> transitionNodeVOList = transitionNodeRepository.getTransitionNodeList(issueVO.getCode());

        setExternalNameHandle(issueVO);
        issueVO.setMorefixVersion(Boolean.FALSE);
        if (StringUtil.isNotBlank(issueVO.getFixVersionName()) && issueVO.getFixVersionName().contains(",")) {
            issueVO.setMorefixVersion(Boolean.TRUE);
        }

        String content = null;
        if (CollectionUtil.isNotEmpty(transitionNodeVOList) && transitionNodeVOList.size() > 0) {
            content = transitionNodeVOList.get(0).getContent();
            issueVO.setReason(transitionNodeVOList.get(0).getReason());
            issueVO.setContent(content);
        }

        List<CaseVO> caseNameList = testcaseRelationRepository.getCaseNameList(query.getCode());
        issueVO.setCaseCount(CollectionUtil.isNotEmpty(caseNameList) ? caseNameList.size() : 0);
        issueVO.setTags(tagVOList);
        issueVO.setFileCount(files);
        //获取当前人可编辑字段
        buildFiled(query, issueDO, issueVO, isSuperUser(productRoles, query.getPermissions()),productRoles);
        IssueContext issueContext = IssueContext.convert(issueVO, query.getCurrentUserId(), query.getPermissions());
        issueContext.setRoles(productRoles);

        List<Event> events = IssueStateMachineUtil.getInstance()
                .readyEvent(issueDO.getStatus().toState(), issueContext);
        //删除容器中非事件的数据
        events.removeIf(event -> (event.getCode().equals(IssueEvent.START.toEvent().getCode()) && event.getName().equals(IssueEvent.START.toEvent().getName())
                || event.getCode().equals(IssueEvent.END.toEvent().getCode()) && event.getName().equals(IssueEvent.END.toEvent().getName())));
        if (CollectionUtil.isNotEmpty(events)) {
            List<com.zto.devops.qc.client.model.issue.entity.Event> collect = events.stream().map(e -> new com.zto.devops.qc.client.model.issue.entity.Event(e.getCode())).collect(Collectors.toList());
            //可操作事件
            collect.forEach(t -> {
                t.setName(IssueEvent.getValueByCode(t.getCode()));
            });
            issueVO.setOperableEvents(collect);
        }
        List<IssueStatus> toBugStatusList = Arrays.asList(IssueStatus.WAIT_FIX,IssueStatus.FIXING,IssueStatus.DELAY_FIX,IssueStatus.REJECTED);
        if(toBugStatusList.contains(issueDO.getStatus())&&(productRoles.contains(ProductRoleEnum.TESTER)||productRoles.contains(ProductRoleEnum.MAJOR_TESTER_OWNER)||productRoles.contains(ProductRoleEnum.TESTER_OWNER)||
                isSupperUser(new HashSet<>(query.getPermissions())))){
            com.zto.devops.qc.client.model.issue.entity.Event event = new com.zto.devops.qc.client.model.issue.entity.Event("BugToRequirement");
            event.setName("缺陷转需求");
            if(null!=issueVO.getOperableEvents()&&CollectionUtil.isNotEmpty(issueVO.getOperableEvents())){
                issueVO.getOperableEvents().add(event);
            }else{
                issueVO.setOperableEvents(Arrays.asList(event));
            }
        }
        List<RelevantUserVO> relevantUserList = relevantUserRepository.findByBusinessCode(issueDO.getCode());
        Map<RelevantUserTypeEnum, List<RelevantUserVO>> relevantUser = relevantUserList.stream().collect(Collectors.groupingBy(RelevantUserVO::getType));
        if (CollectionUtil.isNotEmpty(relevantUser.get(RelevantUserTypeEnum.CC))) {
            Set<RelevantUserVO> ccList = relevantUserConverter.convert(relevantUser.get(RelevantUserTypeEnum.CC));
            ccList.stream().filter(i -> i.getCreatorId().equals(query.getCurrentUserId())).forEach(i -> i.setCanDelete(true));
            for (RelevantUserVO re : ccList) {
                if (re.getUserId().equals(query.getCurrentUserId())) {
                    if (CollectionUtil.isNotEmpty(issueVO.getEditableFieldNames())) {
                        issueVO.getEditableFieldNames().add(IssueEditFieldEnum.CCMANLIST.getFieldName());
                    } else {
                        Set<String> editableFieldNames = new HashSet<>();
                        editableFieldNames.add(IssueEditFieldEnum.CCMANLIST.getFieldName());
                        issueVO.setEditableFieldNames(editableFieldNames);
                    }
                }
            }
            issueVO.setCcManList(ccList);
        }
        buildWarn(issueVO);
        return issueVO;
    }

    public List<IssueVO> listUnClosedIssueByVersionCodesQuery(List<String> versionCodes) {
        log.info("listUnClosedIssueByVersionCodesQuery >>> {}", versionCodes);
        return issueRepository.listUnClosedIssueByVersionCodes(versionCodes);
    }

    public List<IssueVO> listUnClosedIssueByFixedVersionCodes(List<String> versionCodes) {
        log.info("listUnClosedIssueByFixedVersionCodes >>> {}", versionCodes);
        return issueRepository.listUnClosedIssueByFixedVersionCodes(versionCodes);
    }

    public List<IssueLegacyVO> issueLegacyListQuery(IssueLegacyListQuery query) {
        log.info("issueLegacyListQuery >>> {}", JSON.toJSONString(query));
        if (null == query || StringUtil.isBlank(query.getBusinessCode())) {
            return new ArrayList<>();
        }
        return issueRepository.listIssueLegacy(query);
    }

    public IssueNumStatisticsVO issueNumStatisticQuery(IssueNumStatisticQuery query) {
        log.info("issueNumStatisticQuery >>> {}", JSON.toJSONString(query));
        return issueRepository.getIssueNumStatistic(query);
    }

    public IssueNumStatisticsVO issueUiTestNumStatisticQuery(IssueNumStatisticQuery query) {
        log.info("issueNumStatisticQuery >>> {}", JSON.toJSONString(query));
        return issueRepository.getUiTestIssueNumStatistic(query);
    }

    private void setExternalNameHandle(IssueVO issueVO) {
        ArrayList<IssueVO> issueVOS = new ArrayList<>();
        issueVOS.add(issueVO);
        this.setExternalBatchNameHandle(issueVOS);
        issueVO = issueVOS.get(0);
    }

    private void buildFiled(FindIssueByCodeQuery query, IssueEntityDO issueDO, IssueVO issueVO, boolean isSuperUser, List<ProductRoleEnum> productRoles) {
        Set<String> editableFieldNames = new HashSet<>();
        State state = IssueStateMachineUtil.getInstance().getState(issueDO.getStatus().toState());
        Object findEditableFields;
        if (Objects.equals(query.getCurrentUserId(), issueDO.getFindUserId()) || isSuperUser) {
            findEditableFields = state.getMetaMap().get(IssueStateMachine.FIND_EDITABLE_FIELDS);
            ((Collection<? extends IssueEditFieldEnum>) findEditableFields).forEach(e -> editableFieldNames.add(e.getFieldName()));
        }
        if (Objects.equals(query.getCurrentUserId(), issueDO.getDevelopUserId()) || IssueStateMachine.isDeveloper(productRoles) || isSuperUser) {
            findEditableFields = state.getMetaMap().get(IssueStateMachine.DEVELOP_EDITABLE_FIELDS);
            ((Collection<? extends IssueEditFieldEnum>) findEditableFields).forEach(e -> editableFieldNames.add(e.getFieldName()));
            issueVO.setTransitToDevelop(true);
        }
        if (Objects.equals(query.getCurrentUserId(), issueDO.getTestUserId()) || IssueStateMachine.isTester(productRoles) || isSuperUser) {
            findEditableFields = state.getMetaMap().get(IssueStateMachine.TEST_EDITABLE_FIELDS);
            ((Collection<? extends IssueEditFieldEnum>) findEditableFields).forEach(e -> editableFieldNames.add(e.getFieldName()));
            issueVO.setTransitToTest(true);
        }
        if (query.getPermissions() != null && query.getPermissions().contains(IssueStateMachine.GLOBAL_PERM_CAN_MODIFY_ISSUE) || isSuperUser) {
            findEditableFields = state.getMetaMap().get(IssueStateMachine.ADMIN_EDIT_FIELDS);
            ((Collection<? extends IssueEditFieldEnum>) findEditableFields).forEach(e -> editableFieldNames.add(e.getFieldName()));
            issueVO.setTransitToTest(true);
        }
        issueVO.setEditableFieldNames(editableFieldNames);
    }

    private void buildWarn(IssueVO myTaskVO) {
        int warnDay = DateUtil.daysBetween(myTaskVO.getGmtCreate(), new Date());
        myTaskVO.setWarnDay(warnDay);
        String warn = "";
        if (myTaskVO.getWarnDay() > 1 && IssueStatus.WAIT_FIX.equals(myTaskVO.getStatus())) {
            warn = "超时" + myTaskVO.getWarnDay() + "天";
        }
        if (myTaskVO.getWarnDay() > 1 && IssueStatus.FIXING.equals(myTaskVO.getStatus())) {
            warn = "超时" + myTaskVO.getWarnDay() + "天";
        }
        if (myTaskVO.getWarnDay() > 1 && IssueStatus.TESTING.equals(myTaskVO.getStatus())) {
            warn = "超时" + myTaskVO.getWarnDay() + "天";
        }
        if (myTaskVO.getWarnDay() > 1 && IssueStatus.REJECTED.equals(myTaskVO.getStatus())) {
            warn = "超时" + myTaskVO.getWarnDay() + "天";
        }
        if (myTaskVO.getWarnDay() > 7) {
            warn = "超时" + myTaskVO.getWarnDay() + "天";
        }
        myTaskVO.setWarn(warn);
    }

    public List<IssueVO> issueQuery(IssueQuery query) {
        IssueQueryParameter parameter = issueVOConverter.convert(query);
        List<IssueVO> issueVOList = issueRepository.selectByDefinedQuery(parameter);
        if (query.getTransactor() != null) {
            issueStateMachine.buildFields(issueVOList, query.getTransactor(), false);
        }
        setExternalBatchNameHandle(issueVOList);
        return issueVOList;
    }

    public PageIssueVO pageIssue(PageIssueQuery query) {
        PageIssueVO listIssueVO = new PageIssueVO();
        if (StringUtil.isNotEmpty(query.getGroupId())) {
            String productCode = productGroupRpcService.getProductCodeByGroupId(query.getGroupId());
            if (StringUtil.isNotEmpty(productCode)) {
                query.setRelatedProductList(Collections.singletonList(productCode));
            }else{
                listIssueVO.setTotal(0L);
                return listIssueVO;
            }
        }
        resetCreateTime(query);
        IssueQueryParameter parameter = issueVOConverter.convert(query);
        listIssueVO = issueRepository.pageIssue(parameter, query.getPage(), query.getSize());
        List<IssueVO> issueVOList = listIssueVO.getList();
        setExternalBatchNameHandle(issueVOList);
        buildWarnIssueVONew(issueVOList);
        listIssueVO.setList(issueVOList);

        return listIssueVO;
    }

    public static void resetCreateTime(PageIssueQuery query) {
        if (CollectionUtil.isEmpty(query.getRelatedProductList()) && query.getCreateTimeStart() == null && query.getCreateTimeEnd() == null) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.MONTH, -1);
            query.setCreateTimeEnd(new Date());
            query.setCreateTimeStart(cal.getTime());
        }
    }

    public LaneIssueCtxVO listLaneQuery(PageLaneIssueQuery query) {
        resetCreateTime(query);
        IssueQueryParameter parameter = issueVOConverter.convert(query);
        log.info("LaneIssueQueryParameter" + JsonUtil.toJSON(parameter));
        List<LaneIssueMatterVO> list = getList(query, parameter);
        List<IssueStatus> allStatus = getAllStatus(parameter);
        LaneIssueCtxVO vo = new LaneIssueCtxVO(list, allStatus, new LaneIssueMapCondition(LaneDomainEnum.LIST));
        return vo;
    }

    private List<LaneIssueMatterVO> getList(PageLaneIssueQuery query, IssueQueryParameter parameter) {
        List<LaneIssueMatterVO> list = new ArrayList<>();
        List<IssueVO> issueVOList = new ArrayList<>();
        List<Date> holidayList = getThisYearHoliday();
        List<LaneStatusEnum> laneStatusList = CollectionUtil.isEmpty(query.getLane()) ? laneIssueMap.getAllLane() : query.getLane();
        laneStatusList.forEach(laneStatus -> {
            List<IssueStatus> statusList = laneIssueMap.getStatus(laneStatus).stream().map(s -> IssueStatus.getByName(s)).collect(Collectors.toList());
            List<IssueStatus> queryStatusList = CollectionUtil.isNotEmpty(query.getStatusList()) ? statusList.stream().filter(query.getStatusList()::contains).collect(Collectors.toList()) : statusList;
            if (CollectionUtil.isEmpty(queryStatusList)) {
                return;
            }
            parameter.setStatusList(queryStatusList);
            List<IssueVO> tempIssueVOList = issueRepository.findIssueVOList(query, parameter);
            if (CollectionUtil.isNotEmpty(tempIssueVOList)) {
                issueVOList.addAll(tempIssueVOList);
            }
        });
        if (CollectionUtil.isEmpty(issueVOList)) {
            return list;
        }
        issueStateMachine.buildButtons(issueVOList, query.getTransactor());
        issueStateMachine.buildFields(issueVOList, query.getTransactor(), isSupperUser(query.getTransactor().getPermissions()));
        issueVOList.forEach(t -> {
            list.add(convertLaneIssueMatterVO(t, holidayList));
        });
        return list;
    }

    private List<IssueStatus> getAllStatus(IssueQueryParameter parameter) {
        List<IssueStatus> list = new ArrayList<>();
        List<String> statusList = issueRepository.listStatusByDefinedQuery(parameter);
        statusList.stream().collect(Collectors.toList()).forEach(t -> {
            list.add(IssueStatus.getByName(t));
        });
        return list;
    }


    private List<Date> getThisYearHoliday() {
        List<Date> holidayList = new LinkedList<>();
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        int year = cal.get(Calendar.YEAR);
        String startYear = (year - 1) + "-12-01";
        String endYear = (year + 1) + "-01-31";
        if (CollectionUtil.isEmpty(holidayList)) {
            try {
                FindHolidayQuery findHolidayQuery = new FindHolidayQuery();
                findHolidayQuery.setStartDate(DateUtil.parseDate(startYear));
                findHolidayQuery.setEndDate(DateUtil.parseDate(endYear));
                findHolidayQuery.setType(Arrays.asList(1, 2, 3));
                List<HolidayVO> holidayVOS = projectRpcService.findHoliday(findHolidayQuery);
                if (CollectionUtil.isNotEmpty(holidayVOS)) {
                    holidayList = holidayVOS.stream().map(t -> t.getHoliday()).collect(Collectors.toList());
                }
            } catch (Exception e) {
                log.error("initHoliday error:" + e.getMessage());
            }
        }
        return holidayList;
    }

    public static boolean isSupperUser(Set<String> permissions) {
        try {
            if (CollectionUtil.isNotEmpty(permissions)) {
                return permissions.contains(SUPPER_USER_PERMISSION_KEY);
            }
        } catch (Exception e) {
            log.error("判定超管出错", e);
        }
        return false;
    }


    private LaneIssueMatterVO convertLaneIssueMatterVO(IssueVO i, List<Date> holidayList) {
        LaneIssueMatterVO vo = new LaneIssueMatterVO();
        vo.setTitle(i.getTitle());
        vo.setCode(i.getCode());
        vo.setHandler(i.getHandleUserName());
        vo.setHandlerId(i.getHandleUserId());
        vo.setPlanStartDate(i.getPlanStartDate());
        vo.setPlanEndDate(i.getPlanEndDate());
        vo.setStatus(i.getStatus());
        vo.setPriority(i.getPriority());
        vo.setPriorityDesc(i.getPriority() != null ? i.getPriority().getValue() : "");
        vo.setButtons(i.getButtons());
        vo.setFields(i.getFields());
        vo.setMainBtn(getMainBtn(i));
        vo.setSkipConditions(getSkipConditions(i));
        vo.setWarn(getWarn(i, holidayList));
        vo.setDuration(getDuration(i, holidayList));
        vo.setProductCode(i.getProductCode());
        vo.setReopen(i.getReopen());
        vo.setCreator(i.getCreator());
        vo.setCreatorId(i.getCreatorId());
        return vo;
    }

    private Button getMainBtn(IssueVO vo) {
        List<Button> planButtons = laneIssueMap.getMainBtn(vo.getStatus().name());
        List<Button> buttons = vo.getButtons();
        if (CollectionUtil.isEmpty(buttons) || CollectionUtil.isEmpty(planButtons)) {
            return null;
        }
        List<String> btnCodes = buttons.stream().map(Button::getCode).collect(Collectors.toList());
        List<Button> actualButtons = planButtons.stream().filter(t -> btnCodes.contains(t.getCode())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(actualButtons)) {
            return null;
        }
        return actualButtons.get(0);
    }

    private List<LaneSkipCondition> getSkipConditions(IssueVO vo) {
        List<LaneSkipCondition> conditions = new ArrayList<>();
        List<Button> buttons = vo.getButtons();
        if (CollectionUtil.isEmpty(buttons)) {
            return conditions;
        }
        List<String> btnCodes = buttons.stream().map(Button::getCode).collect(Collectors.toList());
        List<LaneSkipCondition> planList = laneIssueMap.getSkipCondition(vo.getStatus().name());

        conditions = planList.stream().filter(t -> btnCodes.contains(t.getAction().getCode())).collect(Collectors.toList());
        return conditions;
    }

    private String getWarn(IssueVO vo, List<Date> holidayList) {
        if (IssueStatus.CLOSED.equals(vo.getStatus()) || IssuePriority.LOW.equals(vo.getPriority())) {
            return null;
        }
        Long workDayTime = vo.getReopenTime() != null ? QcDateUtil.betweenAndExclude(vo.getReopenTime(), new Date(), holidayList) : QcDateUtil.betweenAndExclude(vo.getGmtCreate(), new Date(), holidayList);
        Long planFixTime = IssuePriority.MIDDLE.equals(vo.getPriority()) ? 8 * 3600l : 4 * 3600l;
        Long overTime = workDayTime - planFixTime;
        if (overTime <= 0) {
            return null;
        }
        double f1 = new BigDecimal(overTime.doubleValue() / 32400).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
        if (f1 >= 1) {
            return "逾期" + f1 + "天";
        } else {
            double f2 = new BigDecimal(overTime.doubleValue() / 3600).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            return "超出" + f2 + "H";
        }
    }

    public boolean isOverdue(IssueEntityDO issueEntityDO, List<Date> thisYearHoliday) {
        if (IssueStatus.CLOSED.equals(issueEntityDO.getStatus()) || IssuePriority.LOW.equals(issueEntityDO.getPriority())) {
            return false;
        }
        Long workDayTime = issueEntityDO.getReopenTime() != null ? QcDateUtil.betweenAndExclude(issueEntityDO.getReopenTime(), new Date(), thisYearHoliday) : QcDateUtil.betweenAndExclude(issueEntityDO.getGmtCreate(), new Date(), thisYearHoliday);
        Long planFixTime = IssuePriority.MIDDLE.equals(issueEntityDO.getPriority()) ? 8 * 3600l : 4 * 3600l;
        Long overTime = workDayTime - planFixTime;
        if (overTime <= 0) {
            return false;
        }
        return true;
    }

    private String getDuration(IssueVO vo, List<Date> holidayList) {
        Long workDayTime;
        if (IssueStatus.CLOSED.equals(vo.getStatus())) {
            workDayTime = QcDateUtil.betweenAndExclude(vo.getGmtCreate(), vo.getCloseTime(), holidayList);
        } else {
            workDayTime = QcDateUtil.betweenAndExclude(vo.getGmtCreate(), new Date(), holidayList);
        }
        int hours = Math.round(workDayTime / 3600);
        return hours + "H";
    }


    public ListMyTaskVO myIssueQuery(MyIssueQuery query) {
        ListMyTaskVO listMyTaskVO = new ListMyTaskVO();
        TaskBaseParameter taskBaseParameter = relevantUserConverter.convertTask(query);
        com.github.pagehelper.Page<TaskResultParameter> pageInfo = PageHelper.startPage(query.getPage(), query.getSize());
        if ("priority".equals(taskBaseParameter.getOrderField())) {
            taskBaseParameter.setOrderField("priorityIndex");
        }
        if ("gmt_create".equals(taskBaseParameter.getOrderField())) {
            taskBaseParameter.setOrderField("gmtCreate");
        }
        /*if (CollectionUtil.isEmpty(query.getStatus())) {
            List<String> statuses = getDefaultStatus(DomainEnum.ISSUE, query.getRelevantUserTypes(), TabEnum.WORK_BENCH);
            taskBaseParameter.setStatus(statuses);
        }*/

        List<TaskResultParameter> taskResultParameters = relevantUserRepository.queryMyIssue(taskBaseParameter);
        if (CollectionUtils.isEmpty(taskResultParameters)) {
            return listMyTaskVO;
        }
        List<MyTaskVO> myTaskVOS =
                taskResultParameters.stream().map(taskResultParameter -> relevantUserConverter.convertIssue(taskResultParameter)).collect(Collectors.toList());
        if (query.getRelevantUserTypes().contains(RelevantUserTypeEnum.CURRENT_HANDLE_USER)
                || query.getRelevantUserTypes().contains(RelevantUserTypeEnum.CREATOR)) {
            buildIssueButton(myTaskVOS, query.getTransactor());
        }
        buildWarn(myTaskVOS);
        listMyTaskVO.setTotal(pageInfo.getTotal());
        listMyTaskVO.setVos(myTaskVOS);
        return listMyTaskVO;
    }

    private void buildWarn(List<MyTaskVO> myTaskVOS) {
        myTaskVOS.stream().forEach(myTaskVO -> {
            String warn = "";
            double divide = new BigDecimal(myTaskVO.getWarnDay() / 24).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            double compareDay = IssuePriority.LOW.name().equals(myTaskVO.getPriority()) ? 1 * 24 : 0.5 * 24;
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.WAIT_FIX.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未处理";
                warn = "超时" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.FIXING.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未修复";
                warn = "超时" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.TESTING.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未验证";
                warn = "超时" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > compareDay && IssueStatus.REJECTED.name().equals(myTaskVO.getStatus())) {
                //warn = "超过" + myTaskVO.getWarnDay() + "天未处理";
                warn = "超时" + divide + "天";
            }
            if (myTaskVO.getWarnDay() > 7 * 24) {
                // warn = "已逾期超过7天";
                warn = "超时" + divide + "天";
            }
            myTaskVO.setWarnDay(divide);
            myTaskVO.setWarn(warn);
        });
    }

    private void buildIssueButton(List<MyTaskVO> myTaskVOS, User user) {
        for (MyTaskVO p : myTaskVOS) {
            List<Event> events = IssueStateMachineUtil.getInstance()
                    .readyEvent(IssueStatus.getByName(p.getStatus()).toState(), IssueContext.convert(p, user.getUserId()));
            events.removeIf(event -> (event.getCode().equals(IssueEvent.START.toEvent().getCode()) && event.getName().equals(IssueEvent.START.toEvent().getName())
                    || event.getCode().equals(IssueEvent.END.toEvent().getCode()) && event.getName().equals(IssueEvent.END.toEvent().getName())));
            if (CollectionUtil.isEmpty(events)) {
                continue;
            }
            List<Button> buttonVOS = events.stream().map(e -> {
                        Button buttonVO = new Button();
                        buttonVO.setCode(e.getCode());
                        buttonVO.setName(e.getName());
                        return buttonVO;
                    }).filter(b -> "FIX".equals(b.getCode()) || "DELAY".equals(b.getCode()) || "DELIVER".equals(b.getCode())
                            || "TEST_PASS_CLOSE".equals(b.getCode()) || "RETURN".equals(b.getCode()) || "CONFIRM_CLOSE".equals(b.getCode()))
                    .collect(Collectors.toList());
            p.setButtonVOs(buttonVOS);
        }
    }

    public IssueVO findIssueByIssueCode(SimpleSingleIssueQuery query) {
        IssueEntityDO entity = issueRepository.selectByPrimaryKey(query.getCode());
        if (entity == null) {
            log.error("没有对应的缺陷数据 ，issueCode = {}", query.getCode());
            throw new ServiceException("没有对应的缺陷数据 ，issueCode = {}" + query.getCode());
        }
        IssueVO issueVO = issueVOConverter.convert(entity);
        setExternalNameHandle(issueVO);
        return issueVO;
    }

    public List<TransitionNodeVO> findIssueTransitionNodeByIssueCode(ListTransitionNodesByBusinessCodeQuery query) {
        return transitionNodeRepository.findIssueTransitionNodeByIssueCode(query.getBusinessCode());
    }

    public  List<HandlerWaitFixIssueCountVO> handlerWaitFixIssueCountByProductCodeListQuery(List<String> statusList, List<String> productCodeList){
        Long start = System.currentTimeMillis();
        List<HandlerWaitFixIssueCountVO> result = new ArrayList<>();

        SimpleIssueQuery query = new SimpleIssueQuery();
        query.setStatus(statusList);
        query.setProductCode(productCodeList);
        List<IssueEntityDO> issueEntityList = issueRepository.selectBySimpleIssueQuery(query);
        Long end = System.currentTimeMillis() - start;
        log.info("handlerWaitFixIssueCountByProductCodeListQuery selectBySimpleIssueQuery 耗时:{}", end);
        if(CollectionUtil.isNotEmpty(issueEntityList)){
            Map<String, List<IssueEntityDO>> productCodeMap = issueEntityList.stream().collect(Collectors.groupingBy(IssueEntityDO::getProductCode));
            List<Date> thisYearHoliday = getThisYearHoliday();
            end = System.currentTimeMillis() - start;
            log.info("handlerWaitFixIssueCountByProductCodeListQuery getThisYearHoliday 耗时:{}", end);
            for(String key : productCodeMap.keySet()){
                List<IssueEntityDO> value = productCodeMap.get(key);
                Map<Long, HandlerWaitFixIssueCountVO> handlerMatterCountMap = new HashMap();
                for(IssueEntityDO issueEntityDO : value){
                    Long userId = issueEntityDO.getHandleUserId();
                    String userName = issueEntityDO.getHandleUserName();

                    HandlerWaitFixIssueCountVO handlerMatterCountVO = handlerMatterCountMap.get(userId);
                    if(handlerMatterCountVO == null){
                        handlerMatterCountVO =new HandlerWaitFixIssueCountVO();
                        handlerMatterCountVO.setProductCode(key);
                        handlerMatterCountVO.setUserId(userId);
                        handlerMatterCountVO.setUserName(userName);
                        handlerMatterCountVO.setCount(0);
                        handlerMatterCountVO.setHighPriorityCount(0);
                        handlerMatterCountVO.setOverdueNotFixCount(0);
                        handlerMatterCountVO.setDelayFixCount(0);
                    }
                    handlerMatterCountVO.setCount(handlerMatterCountVO.getCount() + 1);
                    List<IssuePriority> highPriority = Arrays.asList(IssuePriority.URGENCY, IssuePriority.HIGH);
                    if(highPriority.contains(issueEntityDO.getPriority())){
                        handlerMatterCountVO.setHighPriorityCount(handlerMatterCountVO.getHighPriorityCount() + 1);
                    }
                    if(isOverdue(issueEntityDO, thisYearHoliday)){
                        handlerMatterCountVO.setOverdueNotFixCount(handlerMatterCountVO.getOverdueNotFixCount() + 1);
                    }
                    if(IssueStatus.DELAY_FIX.equals(issueEntityDO.getStatus())){
                        handlerMatterCountVO.setDelayFixCount(handlerMatterCountVO.getDelayFixCount() + 1);
                    }
                    handlerMatterCountMap.put(userId, handlerMatterCountVO);
                }
                result.addAll(handlerMatterCountMap.values());
                handlerMatterCountMap.clear();
            }
        }
        end = System.currentTimeMillis() - start;
        log.info("handlerWaitFixIssueCountByProductCodeListQuery run 耗时:{}", end);

        return result;
    }

    public List<DevThingVO> listIssueByHandleUserId(Long handleUserId) {
        return issueRepository.listIssueByHandleUserId(handleUserId);
    }

    public IssueVO findIssueDetail(String issueCode) {
        IssueEntityDO entity = issueRepository.selectByPrimaryKey(issueCode);
        if (entity == null) {
            log.error("findIssueDetail_is_null_issueCode: {}", issueCode);
            throw new ServiceException("没有对应的缺陷数据 ，issueCode = {}" + issueCode);
        }
        return issueVOConverter.convert(entity);
    }

    public Map<String, Long> getIssueCountByFixedVersionCode(List<String> fixedVersionCodes) {
        if (CollectionUtil.isEmpty(fixedVersionCodes)) {
            return Collections.emptyMap();
        }
        Map<String, Long> issueCountByFixedVersionCode =
            issueRepository.getIssueCountByFixedVersionCode(fixedVersionCodes);
        for (String fixedVersionCode : fixedVersionCodes) {
            if (!issueCountByFixedVersionCode.containsKey(fixedVersionCode)) {
                issueCountByFixedVersionCode.put(fixedVersionCode, 0L);
            }
        }
        return issueCountByFixedVersionCode;
    }

    public List<IssueVO> exportIssueCount(ExpIssueQuery query) {
        PageIssueQuery issueQuery = issueVOConverter.convertQuery(query);
        PageIssueVO pageIssueVO = this.pageIssue(issueQuery);
        List<IssueVO> list = pageIssueVO.getList();
        if(CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        if(list.size() > issueExportService.getExportLimit()) {
            throw new ServiceException("导出数量不能超过" + issueExportService.getExportLimit() + "条");
        }
        return list;
    }
}
