package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.DebugTypeEnum;
import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.SceneDataCenterExecuteCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskBaseInfo;
import com.zto.devops.qc.client.service.testmanager.apitest.model.DebugOnNodeReq;
import com.zto.devops.qc.domain.gateway.repository.ApiTestRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class SceneRecordDomainService {

    @Autowired
    private LinkMapDomainService linkMapDomainService;

    @Autowired
    private ApiTestRepository apiTestRepository;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    public String executeSceneDataCenter(SceneDataCenterExecuteCommand command) {
        SceneInfoEntityDO scene = apiTestRepository.queryLatestSceneInfo(command.getSceneCode(), SceneInfoStatusEnum.publish);
        if(Objects.isNull(scene)) {
            throw new ServiceException("造数未发布!");
        }
        JSONObject sceneBackJson = JSONObject.parseObject(scene.getSceneBackData());
        if(Objects.isNull(sceneBackJson)) {
            throw new ServiceException("造数数据异常!");
        }
        JSONArray endNodeArray = sceneBackJson.getJSONArray("endNode");
        if(Objects.isNull(endNodeArray) || endNodeArray.isEmpty()) {
            throw new ServiceException("造数数据异常!");
        }
        return execute(command, endNodeArray.getString(0), sceneBackJson.getJSONArray("inputParameter"));
    }

    private String execute(SceneDataCenterExecuteCommand command, String nodeCode, JSONArray inputParameter) {
        DebugOnNodeReq req = new DebugOnNodeReq();
        req.setSceneCode(command.getSceneCode());
        if(CollectionUtils.isNotEmpty(inputParameter)) {
            req.setInputParameter(buildInputParameter(command.getInputParameter(), inputParameter));
        }
        req.setDebugType(DebugTypeEnum.FIRSTLY);
        req.setNodeCode(nodeCode);
        DebugTaskBaseInfo debugTaskBaseInfo = linkMapDomainService.debugOnNode(req, command.getTransactor());
        return debugTaskBaseInfo.getTaskId();
    }

    private JSONArray buildInputParameter(JSONObject newInputParameter, JSONArray oldInputParameter) {
        oldInputParameter.forEach(item -> {
            JSONObject parameter = (JSONObject) item;
            String key = parameter.getString("name");
            if(StringUtils.isNotBlank(key)) {
                parameter.put("value", newInputParameter.get(key));
            }
        });
        return oldInputParameter;
    }

}
