package com.zto.devops.qc.domain.service;

import com.zto.devops.qc.client.model.knowledgebase.query.KnowledgeBaseByProductCodeQuery;
import com.zto.devops.qc.client.model.knowledgebase.query.KnowledgeBaseVO;
import com.zto.devops.qc.domain.gateway.repository.KnowledgeBaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class KnowledgeBaseQueryDomainService {

    @Autowired
    private KnowledgeBaseRepository knowledgeBaseRepository;

    public KnowledgeBaseVO getKnowledgeBaseDetail(KnowledgeBaseByProductCodeQuery query) {
        return knowledgeBaseRepository.getKnowledgeBaseDetail(query);
    }

}
