package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.domain.gateway.util.LockService;
import com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestVariableVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.UserCookieMapVO;
import com.zto.devops.qc.client.service.testmanager.rpc.model.UserCookieQueryReq;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.ApiTestRepository;
import com.zto.devops.qc.domain.gateway.sso.SsoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AuthCookieDomainService {

    @Autowired
    private QcConfigBasicService config;
    @Autowired
    private RedisService redisService;
    @Autowired
    private LockService lockService;
    @Autowired
    private SsoService ssoService;

    @Autowired
    private ApiTestRepository apiTestRepository;

    private static String COOKIE = "cookie";
    private static String TOKEN = "token";
    private static String X_TOKEN = "X-Token";
    private static String X_OPENID = "X-OpenId";

    public Map<String, String> getDataToLogin(ApiTestVariableVO variable, String namespace, String productCode) {
        if (variable.getSubVariableType().equals(SubVariableTypeEnum.SSO)) {
            return getCookieGw(variable, namespace, productCode);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.ZBASE)) {
            return getCookie(variable, namespace, productCode);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.SECRISK)) {
            return getCookie(variable, namespace, productCode);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.KDGJ)) {
            return getCookieKDGJ(variable, namespace);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.ZZT)) {
            return getTokenAndOpenIdZZT(variable, namespace);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.YZT)) {
//            return getTokenYZT(variable, namespace);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.CLIENT)) {
            return getCustomerClientToken(variable, namespace);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.GH_THIRD)) {
            return getCookieGHThird(variable, namespace);
        } else if (variable.getSubVariableType().equals(SubVariableTypeEnum.KDCS)) {
            return ssoService.getKdcsLoginInfo(variable.getVariableCode(), variable.getVariableValue());
        }
        return null;
    }

    public Map<String, String> getCookie(ApiTestVariableVO apiTestVariableVO, String nameSpace, String productCode) {
        try {
            String key;
            if (productCode.equals("PRO2202168000")) {
                key = apiTestVariableVO.getVariableCode();
            } else {
                key = apiTestVariableVO.getVariableCode() + "_" + nameSpace;
            }
            Map<String, String> resultMap = new HashMap<>();
            byte[] decodedBytes = Base64.getDecoder().decode(apiTestVariableVO.getVariableValue());
            String value = new String(decodedBytes);
            String cookie;
            int i = 0;
            do {
                cookie = ssoService.getCookie(key, value, nameSpace);
                log.info("key[{}] : {}, new cookie : {}", i, key, cookie);
                if (cookie.contains("wyandyy=")) {
                    break;
                }
                i++;
            } while (i < 3);
            log.info("final key : {}, final cookie : {}", key, cookie);
            resultMap.put(key, cookie);
            return resultMap;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public Map<String, String> getCookieGw(ApiTestVariableVO apiTestVariableVO, String nameSpace, String productCode) {
        try {
            String key;
            if (productCode.equals("PRO2202168000")) {
                key = apiTestVariableVO.getVariableCode();
            } else {
                key = apiTestVariableVO.getVariableCode() + "_" + nameSpace;
            }
            Map<String, String> resultMap = new HashMap<>();
            byte[] decodedBytes = Base64.getDecoder().decode(apiTestVariableVO.getVariableValue());
            String value = new String(decodedBytes);
            String cookie;
            int i = 0;
            do {
                cookie = ssoService.getCookieGw(key, value, nameSpace);
                log.info("key[{}] : {}, new cookie : {}", i, key, cookie);
                if (cookie.contains("wyandyy=")) {
                    break;
                }
                i++;
            } while (i < 3);
            log.info("final key : {}, final cookie : {}", key, cookie);
            resultMap.put(key, cookie);
            return resultMap;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public Map<String, String> getCookieKDGJ(ApiTestVariableVO apiTestVariableVO, String nameSpace) {
        try {
            String key = apiTestVariableVO.getVariableCode() + "_" + nameSpace;
            Map<String, String> resultMap = new HashMap<>();
            byte[] decodedBytes = Base64.getDecoder().decode(apiTestVariableVO.getVariableValue());
            String value = new String(decodedBytes);
            String cookie = ssoService.getCookieKDGJ(apiTestVariableVO.getVariableKey(), value);
            resultMap.put(key, cookie);
            return resultMap;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public Map<String, String> getTokenAndOpenIdZZT(ApiTestVariableVO apiTestVariableVO, String nameSpace) {
        String key = apiTestVariableVO.getProductCode() + "-" + apiTestVariableVO.getLinkCode() + "-" + apiTestVariableVO.getVariableKey() + "-" + apiTestVariableVO.getVariableCode() + "-" + nameSpace;
        byte[] decodedBytes = Base64.getDecoder().decode(apiTestVariableVO.getVariableValue());
        String value = new String(decodedBytes);
        Map<String, String> resultMap = ssoService.getOpenIdZZT(key, value);
        if (MapUtils.isEmpty(resultMap)) {
            return null;
        }
        return resultMap;
    }

    public Map<String, String> getCustomerClientToken(ApiTestVariableVO apiTestVariableVO, String nameSpace) {
        Map<String, String> resultMap = new HashMap<>();
        byte[] decodedBytes = Base64.getDecoder().decode(apiTestVariableVO.getVariableValue());
        String value = new String(decodedBytes);
        String token = ssoService.getCustomerClientToken(apiTestVariableVO.getVariableKey(), value);
        resultMap.put(apiTestVariableVO.getVariableCode() + "_" + nameSpace, token);
        return resultMap;
    }

    public Map<String, String> getCookieGHThird(ApiTestVariableVO apiTestVariableVO, String namespace) {
        try {
            String key = apiTestVariableVO.getVariableCode() + "_" + namespace;
            Map<String, String> resultMap = new HashMap<>();
            byte[] decodedBytes = Base64.getDecoder().decode(apiTestVariableVO.getVariableValue());
            String value = new String(decodedBytes);
            String cookie;
            int i = 0;
            do {
                cookie = ssoService.getCookieGHThird(key, value);
                log.info("key[{}] : {}, new cookie : {}", i, key, cookie);
                if (cookie.contains("wyandyy=")) {
                    break;
                }
                i++;
            } while (i < 3);
            log.info("final key : {}, final cookie : {}", key, cookie);
            resultMap.put(key, cookie);
            return resultMap;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }


    public UserCookieMapVO getUserCookie(UserCookieQueryReq req) {
        ApiTestVariableVO variable = apiTestRepository.getApiTestVariableByVariableCode(req.getVariableCode());
        if (null == variable) {
            throw new ServiceException("变量不存在。");
        }
        String namespace = getNameSpace(req.getRequestUrl());
        Map<String, String> resultMap = new HashMap<>();
        switch(variable.getSubVariableType()) {
            case SSO:
                resultMap = getCookieGw(variable, namespace, "");
                return new UserCookieMapVO(resultMap.get(variable.getVariableCode() + "_" + namespace));
            case ZBASE:
            case SECRISK:
                resultMap = getCookie(variable, namespace, "");
                return new UserCookieMapVO(resultMap.get(variable.getVariableCode() + "_" + namespace));
            case KDGJ:
                resultMap = getCookieKDGJ(variable, namespace);
                return new UserCookieMapVO(resultMap.get(variable.getVariableCode() + "_" + namespace));
            case ZZT:
                resultMap = getTokenAndOpenIdZZT(variable, namespace);
                if(resultMap.containsKey(variable.getVariableCode() + "_" + namespace + "_" + X_TOKEN)) {
                    Map<String, String> userCookieMap = new HashMap<>();
                    userCookieMap.put(X_TOKEN, resultMap.get(variable.getVariableCode() + "_" + namespace + "_" + X_TOKEN));
                    userCookieMap.put(X_OPENID, resultMap.get(variable.getVariableCode() + "_" + namespace + "_" + X_OPENID));
                    return new UserCookieMapVO(userCookieMap);
                }
            case CLIENT:
                resultMap = getCustomerClientToken(variable, namespace);
                return new UserCookieMapVO(resultMap.get(variable.getVariableCode() + "_" + namespace));
            case GH_THIRD:
                resultMap = getCookieGHThird(variable, namespace);
                return new UserCookieMapVO(resultMap.get(variable.getVariableCode() + "_" + namespace));
            default:
                return new UserCookieMapVO(resultMap);
        }
    }

    public String getNameSpace(String url) {
        log.info("getNameSpace url : {}", url);
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        int startIndex = url.contains("//") ? url.indexOf("//") + 2 : 0;
        int endIndex = url.indexOf(".", startIndex);
        String nameSpace = null;
        if (endIndex != -1) {
            nameSpace = url.substring(startIndex, endIndex);
        } else {
            log.error("未找到合适的截取范围. url : {}", url);
        }
        return nameSpace;
    }
}
