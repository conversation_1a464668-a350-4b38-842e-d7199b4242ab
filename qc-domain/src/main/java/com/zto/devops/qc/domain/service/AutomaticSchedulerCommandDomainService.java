package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.model.testmanager.cases.command.ExecuteAutomaticTaskCommand;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.*;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.AutomaticSchedulerDetailVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.PageSchedulerVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.ProductSchedulerVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.SchedulerCaseVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.*;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.*;
import com.zto.devops.qc.domain.converter.AutomaticSchedulerDomainConvertor;
import com.zto.devops.qc.domain.gateway.repository.AutomaticSchedulerRepository;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AutomaticSchedulerCommandDomainService extends BaseDomainService {

    @Autowired
    private AutomaticSchedulerRepository automaticSchedulerRepository;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private AutomaticTaskCommandDomainService automaticTaskCommandDomainService;

    @Autowired
    private IPipelineRpcService iPipelineRpcService;

    private static final AutomaticSchedulerDomainConvertor AUTOMATIC_SCHEDULER_DOMAIN_CONVERTOR =
            Mappers.getMapper(AutomaticSchedulerDomainConvertor.class);

    public void addAutomaticScheduler(AddAutomaticSchedulerCommand command) {
        AutomaticSchedulerAddedEvent event = AUTOMATIC_SCHEDULER_DOMAIN_CONVERTOR.converter(command);
        event.setAggregateId(command.getAggregateId());
        automaticSchedulerRepository.add(event);
        apply(event);

    }

    public void addSchedulerCases(AddSchedulerCasesCommand command) {

        AddSchedulerCasesEvent event = AUTOMATIC_SCHEDULER_DOMAIN_CONVERTOR.converter(command);
        event.setAggregateId(command.getAggregateId());
        automaticSchedulerRepository.addCases(event);
        apply(event);

    }

    public void editAutomaticScheduler(EditAutomaticSchedulerCommand command) {
        AutomaticSchedulerEditedEvent event = AUTOMATIC_SCHEDULER_DOMAIN_CONVERTOR.converter(command);
        event.setAggregateId(command.getAggregateId());
        event.setProductCode(command.getProductCode());
        event.setExecuteSpaceCode(command.getExecuteSpaceCode());
        if (null == event.getSchedulerName()) {
            event.setSchedulerName(command.getSchedulerName());
        }
        if (null == event.getCrontab()) {
            event.setCrontab(command.getCrontab());
        }
        if (null == event.getSwitchFlag()) {
            event.setSwitchFlag(command.getSwitchFlag());
        }
        automaticSchedulerRepository.edit(event);
        apply(event);

    }

    public void updateOssTag(String productCode, String executeSpaceCode, String ossTag) {
        automaticSchedulerRepository.updateOssTag(productCode, executeSpaceCode, ossTag);

    }

    public void deleteAutomaticScheduler(DeleteAutomaticSchedulerCommand command) {
        AutomaticSchedulerDeletedEvent event = new AutomaticSchedulerDeletedEvent(command.getAggregateId());
        event.setAggregateId(command.getAggregateId());
        automaticSchedulerRepository.delete(event);
        apply(event);
    }

    public void removeSchedulerCases(RemoveSchedulerCasesCommand command) {
        RemoveSchedulerCasesEvent event = AUTOMATIC_SCHEDULER_DOMAIN_CONVERTOR.converter(command);
        event.setAggregateId(command.getAggregateId());
        automaticSchedulerRepository.removeCases(event);
        apply(event);

    }

    public AutomaticSchedulerDetailVO query(SchedulerDetailQuery query) {
        return automaticSchedulerRepository.query(query);
    }

    public List<SchedulerCaseVO> query(ListSchedulerCaseQuery query) {

        return automaticSchedulerRepository.query(query);

    }

    public List<SchedulerCaseVO> query(SchedulerModuleListQuery query) {

        return automaticSchedulerRepository.query(query);

    }

    public List<ProductSchedulerVO> query(ProductSchedulerQuery query) {
        return automaticSchedulerRepository.query(query);
    }

    public List<String> query(SchedulerCaseCodeListQuery query) {

        return automaticSchedulerRepository.query(query);

    }

    public PageSchedulerVO query(PageSchedulerQuery query) {

        return automaticSchedulerRepository.query(query);
    }

    public void automaticSchedulerExecution(AutomaticSchedulerExecutionCommand command) {
        AutomaticSchedulerExecutionEvent event = AUTOMATIC_SCHEDULER_DOMAIN_CONVERTOR.converter(command);
        event.setAggregateId(command.getAggregateId());
        List<TestcaseVO> testcaseVOList = automaticSchedulerRepository.execute(event);
        if (testcaseVOList == null) {
            return;
        }
        executeAutomaticScheduler(testcaseVOList, event);
        apply(event);
        AutomaticPreExecutionUpdateEvent updateEvent = AUTOMATIC_SCHEDULER_DOMAIN_CONVERTOR.convert(command);
        updateEvent.setAggregateId(command.getAggregateId());
        automaticSchedulerRepository.updatePreExecution(updateEvent);
        apply(updateEvent);

    }

    private void executeAutomaticScheduler(List<TestcaseVO> list, AutomaticSchedulerExecutionEvent event) {
        Map<String, List<TestcaseVO>> map = list.stream()
                .collect(Collectors.groupingBy(TestcaseVO::getAutomaticSourceCode));
        String taskId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        map.forEach((automaticSourceCode, testcaseList) -> {
            List<String> testcaseCodeList =
                    testcaseList.stream().map(TestcaseVO::getCode).collect(Collectors.toList());
            String code = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
            ExecuteAutomaticTaskCommand command = new ExecuteAutomaticTaskCommand(code);
            command.setTaskId(taskId);
            command.setAutomaticSourceCode(automaticSourceCode);
            command.setSchedulerCode(event.getSchedulerCode());
            command.setEnv(event.getExecuteEnv());
            command.setTag(event.getExecuteTag());
            command.setCoverageFlag(event.getCoverageFlag());
            command.setTransactor(event.getTransactor());
            command.setTestcaseCodeList(testcaseCodeList);
            command.setTrigMode(event.getTrigMode());
            automaticTaskCommandDomainService.executeAutomaticTask(command);
//            commandGateway.syncSend(command);
            log.info("=========发送自动化用例执行command" + JSON.toJSONString(command));
        });
    }

}
