package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.issue.command.EditIssueCommand;
import com.zto.devops.qc.client.model.issue.event.IssueEditedEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface IssueEditedEventConverter {

    IssueEditedEventConverter INSTANCE = Mappers.getMapper(IssueEditedEventConverter.class);

    @Mapping(target = "code", source = "aggregateId")
    @Mapping(target = "sprint.code",expression = "java((sprint!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(sprint.getCode())) ?sprint.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.SPRINT_VALUE.getValue())")
    @Mapping(target = "requirement.code",expression = "java((requirement!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(requirement.getCode())) ?requirement.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.REQUIREMENT_VALUE.getValue())")
    IssueEditedEvent convert(EditIssueCommand command);

}
