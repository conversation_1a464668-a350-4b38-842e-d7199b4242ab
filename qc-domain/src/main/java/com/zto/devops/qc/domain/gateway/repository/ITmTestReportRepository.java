package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.enums.testmanager.report.TestReportTypeEnum;
import com.zto.devops.qc.client.model.dto.ReviewInfoEntityDO;
import com.zto.devops.qc.client.model.dto.TestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TestReportEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestReportEntityDO;
import com.zto.devops.qc.client.model.report.entity.ReportVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewOpinionVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewRenewalVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmTestReportVO;
import com.zto.devops.qc.client.model.testmanager.report.event.*;
import com.zto.devops.qc.client.model.testmanager.report.query.PageReportMqQuery;

import java.util.List;

public interface ITmTestReportRepository {

    List<TmTestReportEntityDO> selectByPlanCode(String code);

    TmTestReportEntityDO getReportByReportCode(String reportCode);

    TmTestReportEntityDO getReportByPlanCodeAndReportType(String planCode, ReportType type);

    List<TmTestReportVO> getReportListByPlanCodeAndReportType(String planCode, ReportType type);

    List<TmModuleTestVO> findReportModuleTestVOS(String reportCode);

    List<TmModuleTestVO> findPlanModuleTestVOS(String planCode);

    TmTestReportEntityDO loadFromDb(String reportCode);

    TmTestReportEntityDO getTmReportByPrimaryKey(String reportCode);

    void saveTmAccessReport(TmAccessReportAddEvent event);

    void updateTmAccessReport(TmAccessReportEditEvent event);

    void saveTmOnlineSmokeReport(TmOnlineSmokeReportAddEvent event);

    void updateTmOnlineSmokeReport(TmOnlineSmokeReportEditEvent event);

    void updatePlanStageStatus(String planCode, TestPlanStageEnum stageEnum);

    void editSimpleTestReport(SimpleTestReportEditEvent event);

    void saveTmPermitReport(TmPermitReportAddEvent event);

    void updateTmPermitReport(TmPermitReportEditedEvent event);

    void addSimpleTestReport(SimpleReportAddedEvent event);

    ReviewInfoEntityDO getReviewInfoUserByCode(String reportCode);

    TmTestReportEntityDO getReportByProductCodeAndUser(String productCode, Long reportUserId);

    List<ReviewOpinionVO> getReviewOpinionVOS(String code);

    List<ReviewRenewalVO> getReviewRenewalVOS(String code);

    void saveTestReviewReport(ReviewReportAddedEvent event);

    void updateTestReviewReport(ReviewReportEditedEvent event);

    void saveExternalTestReport(ExternalReportAddEvent event);

    void updateExternalTestReport(ExternalReportEditEvent event);

    void addMobileTestReport(MobileTestReportAddedEvent event);

    void updateMobileTestReport(MobileTestReportEditEvent event);

    List<ReportVO> pageQuery(PageReportMqQuery query);

    List<TmTestReportEntityDO> findSentEmailList(List<String> versionCodeList, List<ReportType> typeList);

    List<TmTestReportEntityDO> listByVersionCodesAndTypes(List<String> versionCodes, List<TestReportTypeEnum> reportTypes);

    void updateVersionInfo(TmTestReportEntityDO reportEntity, String name, User transactor);
}
