package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.issue.command.AddCommentCommand;
import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface CommentVOConverter {

    CommentVOConverter INSTANCE = Mappers.getMapper(CommentVOConverter.class);

    CommentVO convert(AddCommentCommand command);

}
