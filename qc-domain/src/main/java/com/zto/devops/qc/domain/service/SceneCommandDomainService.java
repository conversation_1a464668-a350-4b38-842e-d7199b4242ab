package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.model.dto.SceneInfoEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.SceneBatchAddToTestPlanCommand;
import com.zto.devops.qc.domain.gateway.repository.SceneRepository;
import com.zto.devops.qc.domain.gateway.repository.TestcaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SceneCommandDomainService extends BaseDomainService {

    @Autowired
    private SceneRepository sceneRepository;
    @Autowired
    private TestcaseQueryDomainService testcaseQueryDomainService;
    @Autowired
    private TestcaseCommandDomainService testcaseCommandDomainService;
    @Autowired
    private TestcaseRepository testcaseRepository;

    public void sceneBatchAddToTestPlan(SceneBatchAddToTestPlanCommand command) {
        List<SceneInfoEntityDO> sceneInfoEntities = sceneRepository.queryPublishScenes(command.getSceneCodeList(), command.getProductCode());
        if (CollectionUtil.isEmpty(sceneInfoEntities)) {
            throw new ServiceException("没有可以加入测试计划的场景");
        }
        List<String> automaticSourceCodeList = sceneInfoEntities
                .stream().map(SceneInfoEntityDO::getAutomaticSourceCode)
                .collect(Collectors.toList());
        List<TestcaseEntityDO> testcaseEntities = testcaseRepository.queryCaseListByAutomaticCodes(automaticSourceCodeList);
        if (CollectionUtils.isEmpty(testcaseEntities)) {
            throw new ServiceException("没有可以加入测试计划的自动化用例");
        }
        List<String> caseCodes = testcaseEntities
                .stream().map(TestcaseEntityDO::getCode)
                .collect(Collectors.toList());
        testcaseCommandDomainService.doBatchJoinPlan(caseCodes, command.getTestPlanList(), command.getTransactor());
    }


}