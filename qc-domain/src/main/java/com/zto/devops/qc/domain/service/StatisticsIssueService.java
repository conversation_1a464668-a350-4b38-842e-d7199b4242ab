package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.qc.client.model.dto.IssueEntityDO;
import com.zto.devops.qc.client.model.dto.StatisticsVersionIssueEntityDO;
import com.zto.devops.qc.client.model.dto.TransitionNodeEntityDO;
import com.zto.devops.qc.client.model.issue.entity.HolidayVO;
import com.zto.devops.qc.client.model.issue.query.FindHolidayQuery;
import com.zto.devops.qc.client.model.rpc.pipeline.VersionFlowDateVO;
import com.zto.devops.qc.client.model.rpc.pipeline.query.FindVersionFlowDateQuery;
import com.zto.devops.qc.client.model.rpc.product.ProductVO;
import com.zto.devops.qc.client.model.rpc.product.query.FindProductByIdQuery;
import com.zto.devops.qc.client.model.rpc.project.VersionVO;
import com.zto.devops.qc.domain.gateway.repository.IIssueRepository;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.domain.util.QcDateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: chenguangyu
 * @Description:
 * @Date: 2021/12/29 10:33
 */
@Service
public class StatisticsIssueService extends BaseDomainService {

    @Autowired
    private IProjectRpcService projectRpcService;
    @Autowired
    private IPipelineRpcService pipelineRpcService;
    @Autowired
    private IProductRpcService productRpcService;
    @Autowired
    private IIssueRepository issueRepository;

    /**
     * 4小时
     */
    public static final Long EFFECTIVE_HIGH = 4 * 60 * 60L;
    /**
     * 8小时
     */
    public static final Long EFFECTIVE_MIDDLE = 8 * 60 * 60L;

    public void statisticsVersionIssue(String versionCode) {
        StatisticsVersionIssueEntityDO statisticsVersionIssue = new StatisticsVersionIssueEntityDO();
        //补充版本信息
        VersionVO versionVO = projectRpcService.findVersionInfoQuery(versionCode);
        if (versionVO == null || StringUtil.isBlank(versionVO.getStatus()) || !versionVO.getStatus().equals("ACCEPTED")) {
            //只统计已上线的版本
            return;
        }
        statisticsVersionIssue.setVersionCode(versionCode);
        statisticsVersionIssue.setName(versionVO.getName());
        statisticsVersionIssue.setStatus(versionVO.getStatus());
        statisticsVersionIssue.setType(versionVO.getType());
        statisticsVersionIssue.setProductCode(versionVO.getProductCode());
        statisticsVersionIssue.setVersionGmtCreate(versionVO.getGmtCreate());
        FindVersionFlowDateQuery findVersionActualDateQuery = new FindVersionFlowDateQuery(versionCode);
        VersionFlowDateVO versionActualDateVO = pipelineRpcService.findVersionFlowDateQuery(findVersionActualDateQuery);
        if (versionActualDateVO != null) {
            statisticsVersionIssue.setActualPublishDate(versionActualDateVO.getReleaseDate());
        }
        //补充产品、部门信息
        if (StringUtil.isNotBlank(versionVO.getProductCode())) {
            FindProductByIdQuery findProductByIdQuery = new FindProductByIdQuery();
            findProductByIdQuery.setCode(versionVO.getProductCode());
            ProductVO productVO = productRpcService.findProductByIdQuery(findProductByIdQuery);
            if (productVO != null) {
                statisticsVersionIssue.setDeptId(productVO.getDeptId());
//                statisticsVersionIssue.setDeptNo();
            }
        }

        //版本下缺陷查询(发现版本)
        List<IssueEntityDO> entityList = issueRepository.selectByVersionCode(versionCode);

        this.statisticsInfo(statisticsVersionIssue, entityList);

        StatisticsVersionIssueEntityDO entity = issueRepository.selectStatisticsVersionIssueEntity(versionCode);
        if (entity != null) {
            //已存在
            issueRepository.updateStatisticsVersionIssueEntity(statisticsVersionIssue);
        } else {
            issueRepository.insertStatisticsVersionIssueEntity(statisticsVersionIssue);
        }
    }

    private void statisticsInfo(StatisticsVersionIssueEntityDO statisticsVersionIssue, List<IssueEntityDO> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return;
        }
        List<String> issueCodes = entityList.stream().map(IssueEntityDO::getCode).collect(Collectors.toList());
        List<TransitionNodeEntityDO> transitionNodeAll = this.listIssueTransitionNode(issueCodes);
        //有效缺陷
        List<IssueEntityDO> effectiveList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(transitionNodeAll)) {
            TransitionNodeEntityDO firstNode = transitionNodeAll.get(0);
            TransitionNodeEntityDO lastNode = transitionNodeAll.get(transitionNodeAll.size() - 1);
            Date firstTime = lastNode.getGmtCreate();
            Date lastTime = firstNode.getGmtCreate();
            //查询节假日
            FindHolidayQuery findHolidayQuery = new FindHolidayQuery();
            findHolidayQuery.setStartDate(firstTime);
            findHolidayQuery.setEndDate(lastTime);
            findHolidayQuery.setType(Arrays.asList(1, 2, 3));
            List<HolidayVO> holidayVOS = projectRpcService.findHoliday(findHolidayQuery);
            Map<String, List<TransitionNodeEntityDO>> transitionNodeAllMap = transitionNodeAll.stream().collect(Collectors.groupingBy(TransitionNodeEntityDO::getBusinessCode));
            this.doStatisticsTime(statisticsVersionIssue, entityList, holidayVOS, transitionNodeAllMap);
            effectiveList = this.getEffective(entityList, transitionNodeAllMap);
        } else {
            effectiveList = entityList;
        }
        //紧急优先级缺陷数量
        Long urgencyNum = entityList.stream().filter(item -> item.getPriority() == IssuePriority.URGENCY).count();
        //高优先级缺陷数量
        Long hignNum = entityList.stream().filter(item -> item.getPriority() == IssuePriority.HIGH).count();
        //中优先级缺陷数量
        Long middleNum = entityList.stream().filter(item -> item.getPriority() == IssuePriority.MIDDLE).count();
        //低优先级缺陷数量
        Long lowNum = entityList.stream().filter(item -> item.getPriority() == IssuePriority.LOW).count();


        statisticsVersionIssue.setUrgencyNum(urgencyNum);
        statisticsVersionIssue.setHighNum(hignNum);
        statisticsVersionIssue.setMiddleNum(middleNum);
        statisticsVersionIssue.setLowNum(lowNum);

        Map<String, IssuePriority> issuePriorityMap = entityList.stream().collect(Collectors.toMap(IssueEntityDO::getCode, IssueEntityDO::getPriority));
        List<TransitionNodeEntityDO> transitionNodes = issueRepository.selectIssueReopenList(issueCodes);
        //紧急优先级缺陷reopen数量
        Long urgencyReopenNum = transitionNodes.stream().filter(item ->
                (issuePriorityMap.containsKey(item.getBusinessCode()) && issuePriorityMap.get(item.getBusinessCode()) == IssuePriority.URGENCY)).count();
        //高优先级缺陷reopen数量
        Long hignReopenNum = transitionNodes.stream().filter(item ->
                (issuePriorityMap.containsKey(item.getBusinessCode()) && issuePriorityMap.get(item.getBusinessCode()) == IssuePriority.HIGH)).count();
        //中优先级缺陷reopen数量
        Long middleReopenNum = transitionNodes.stream().filter(item ->
                (issuePriorityMap.containsKey(item.getBusinessCode()) && issuePriorityMap.get(item.getBusinessCode()) == IssuePriority.MIDDLE)).count();
        //低优先级缺陷reopen数量
        Long lowReopenNum = transitionNodes.stream().filter(item ->
                (issuePriorityMap.containsKey(item.getBusinessCode()) && issuePriorityMap.get(item.getBusinessCode()) == IssuePriority.LOW)).count();

        statisticsVersionIssue.setUrgencyReopenNum(urgencyReopenNum);
        statisticsVersionIssue.setHighReopenNum(hignReopenNum);
        statisticsVersionIssue.setMiddleReopenNum(middleReopenNum);
        statisticsVersionIssue.setLowReopenNum(lowReopenNum);


        //紧急优先级缺陷有效数量
        Long urgencyEffectiveNum = effectiveList.stream().filter(item -> item.getPriority() == IssuePriority.URGENCY).count();
        //高优先级缺陷有效数量
        Long hignEffectiveNum = effectiveList.stream().filter(item -> item.getPriority() == IssuePriority.HIGH).count();
        //中优先级缺陷有效数量
        Long middleEffectiveNum = effectiveList.stream().filter(item -> item.getPriority() == IssuePriority.MIDDLE).count();
        //低优先级缺陷有效数量
        Long lowEffectiveNum = effectiveList.stream().filter(item -> item.getPriority() == IssuePriority.LOW).count();

        //需求问题数量
        Long requirementIssueNum = effectiveList.stream().filter(item -> (item.getFindEnv() == IssueFindEnv.FAT_EVN && item.getRootCause() == IssueRootCause.REQUIREMENT_ISSUE_BUG)).count();

        statisticsVersionIssue.setUrgencyEffectiveNum(urgencyEffectiveNum);
        statisticsVersionIssue.setHighEffectiveNum(hignEffectiveNum);
        statisticsVersionIssue.setMiddleEffectiveNum(middleEffectiveNum);
        statisticsVersionIssue.setLowEffectiveNum(lowEffectiveNum);
        statisticsVersionIssue.setRequirementIssueNum(requirementIssueNum);
    }

    private List<TransitionNodeEntityDO> listIssueTransitionNode(List<String> issueCodes) {
        return issueRepository.listIssueTransitionNode(issueCodes);
    }

    /**
     * @param transitionNodeAllMap 上一步已按照创建时间倒序排序了
     */
    private void doStatisticsTime(StatisticsVersionIssueEntityDO statisticsVersionIssue, List<IssueEntityDO> entityList,
                                  List<HolidayVO> holidayVOS, Map<String, List<TransitionNodeEntityDO>> transitionNodeAllMap) {
        //紧急优先级缺陷修复时长(秒)
        Long urgencyRepairTime = 0L;
        //高优先级缺陷修复时长(秒)
        Long highRepairTime = 0L;
        //中优先级缺陷开修复长(秒)
        Long middleRepairTime = 0L;
        //低优先级缺陷开修复长(秒)
        Long lowRepairTime = 0L;

        //紧急优先级缺陷按期修复数量
        Long urgencyPunctualNum = 0L;
        //高优先级缺陷按期修复数量
        Long highPunctualNum = 0L;
        //中优先级缺陷按期修复数量
        Long middlePunctualNum = 0L;
        if (transitionNodeAllMap != null && !transitionNodeAllMap.isEmpty()) {
            Map<String, IssueEntityDO> issueMap = entityList.stream().collect(Collectors.toMap(IssueEntityDO::getCode, o -> o, (l1, l2) -> l1));
            for (Map.Entry<String, List<TransitionNodeEntityDO>> entry : transitionNodeAllMap.entrySet()) {
                IssueEntityDO issue = issueMap.get(entry.getKey());
                List<TransitionNodeEntityDO> itemNodes = entry.getValue();
                IssuePriority priority = issue.getPriority();
                Long itemTotalTime = 0L;
                //单个缺陷时间处理
                for (int i = 0; i < itemNodes.size() - 1; i++) {
                    //『待修复』『修复中』『延期修复』状态的时间之和
                    TransitionNodeEntityDO node = itemNodes.get(i);
                    TransitionNodeEntityDO preNode = null;
                    if (node.getNextStatus() == IssueStatus.WAIT_FIX || node.getNextStatus() == IssueStatus.FIXING || node.getNextStatus() == IssueStatus.DELAY_FIX) {
                        preNode = itemNodes.get(i + 1);
                    }
                    if (preNode == null) {
                        continue;
                    }
                    Date start = preNode.getGmtCreate();
                    Date end = node.getGmtCreate();
                    //计算修复时效
                    Long itemTime = QcDateUtil.betweenAndExclude(start, end, holidayVOS.stream().map(HolidayVO::getHoliday).collect(Collectors.toList()));
                    itemTotalTime += itemTime;
                }
                switch (priority) {
                    case URGENCY:
                        urgencyRepairTime += itemTotalTime;
                        if (itemTotalTime <= EFFECTIVE_HIGH) {
                            urgencyPunctualNum++;
                        }
                        break;
                    case HIGH:
                        highRepairTime += itemTotalTime;
                        if (itemTotalTime <= EFFECTIVE_HIGH) {
                            highPunctualNum++;
                        }
                        break;
                    case MIDDLE:
                        middleRepairTime += itemTotalTime;
                        if (itemTotalTime <= EFFECTIVE_MIDDLE) {
                            middlePunctualNum++;
                        }
                        break;
                    case LOW:
                        lowRepairTime += itemTotalTime;
                        break;
                }
            }
        }
        statisticsVersionIssue.setUrgencyRepairTime(urgencyRepairTime);
        statisticsVersionIssue.setHighRepairTime(highRepairTime);
        statisticsVersionIssue.setMiddleRepairTime(middleRepairTime);
        statisticsVersionIssue.setLowRepairTime(lowRepairTime);
        statisticsVersionIssue.setUrgencyPunctualNum(urgencyPunctualNum);
        statisticsVersionIssue.setHighPunctualNum(highPunctualNum);
        statisticsVersionIssue.setMiddlePunctualNum(middlePunctualNum);
    }

    /**
     * 获取有效缺陷
     *
     * @param issueEntityList      缺陷列表
     * @param transitionNodeAllMap 缺陷-流转信息
     *                             key：缺陷code。value:流转信息，按时间倒序排列
     */
    private List<IssueEntityDO> getEffective(List<IssueEntityDO> issueEntityList, Map<String, List<TransitionNodeEntityDO>> transitionNodeAllMap) {
        if (CollectionUtil.isEmpty(issueEntityList)) {
            return Collections.emptyList();
        }
        if (transitionNodeAllMap == null || transitionNodeAllMap.isEmpty()) {
            return issueEntityList;
        }
        // 有效
        List<IssueEntityDO> result = new ArrayList<>();
        for (IssueEntityDO item : issueEntityList) {
            if (item.getStatus() == null || item.getStatus() != IssueStatus.CLOSED) {
                result.add(item);
                continue;
            }
            List<TransitionNodeEntityDO> itemList = transitionNodeAllMap.get(item.getCode());
            if (CollectionUtil.isEmpty(itemList) || itemList.size() < 2) {
                result.add(item);
                continue;
            }
            TransitionNodeEntityDO transitionNode = itemList.get(0);
            if (transitionNode.getCurStatus() == IssueStatus.REJECTED && transitionNode.getNextStatus() == IssueStatus.CLOSED) {
                boolean addFlg = true;
                for (TransitionNodeEntityDO node : itemList) {
                    //关闭原因为非BUG、重复提交  则为无效缺陷
                    boolean invalidFlg = node.getNextStatus() != null && node.getNextStatus() == IssueStatus.REJECTED && node.getReason() != null &&
                            (Reason.NO_ISSUE.equals(node.getReason()) || Reason.REPEAT_SUBMIT.equals(node.getReason()));
                    //无效的
                    if (invalidFlg) {
                        addFlg = false;
                        //跳出当前循环，外面循环继续
                        break;
                    }
                }
                if (addFlg) {
                    result.add(item);
                }
            } else {
                result.add(item);
            }
        }
        return result;
    }
}
