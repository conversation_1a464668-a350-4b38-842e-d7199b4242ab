package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.dto.AutomaticSchedulerEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;

import java.util.List;
import java.util.Set;

public interface AutomaticSchedulerMsgRepository {

    List<AutomaticTaskEntityDO> getAllTaskByTaskId(String taskId);

    AutomaticSchedulerEntityDO getSchedulerEntityDOByCode(String code);

    Set<String> getAllReceivedUsers(String schedulerCode, Long creatorId);

}
