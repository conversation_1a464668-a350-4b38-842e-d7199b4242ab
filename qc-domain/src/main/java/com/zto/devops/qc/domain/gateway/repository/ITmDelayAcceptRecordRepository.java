package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.dto.TmDelayAcceptRecordEntityDO;

import java.util.List;

public interface ITmDelayAcceptRecordRepository {

    TmDelayAcceptRecordEntityDO selectByVersionCode(String versionCode);

    void insert(TmDelayAcceptRecordEntityDO entityDO);

    void updateEnableByFlowCode(String code);

    TmDelayAcceptRecordEntityDO selectByFlowCode(String code);

    List<TmDelayAcceptRecordEntityDO> selectListBySendFlag(Boolean sendFlag);

    void updateIsSendByFlowCodeList(List<String> codeList, Boolean aTrue);

    void updateSendSuccess(TmDelayAcceptRecordEntityDO entityDO);

    List<TmDelayAcceptRecordEntityDO> selectByVersionCodeList(List<String> versionCodeList);
}
