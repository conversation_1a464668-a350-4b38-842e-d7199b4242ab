package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.rpc.pipeline.event.ReleasedQcEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.command.CoverageNotStandardReasonEditCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.command.EditCoverageReasonCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordBasicVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordGenerateVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageStatusVO;
import com.zto.devops.qc.client.model.testmanager.coverage.event.CoverageNotStandardReasonEditEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.event.CoverageReasonEditEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoveragePublishQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.ReportCoveredDto;
import com.zto.devops.qc.client.service.coverage.model.resp.CoverageReportClassInfoResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @create 2022/10/19 10:50
 */
@Mapper(componentModel = "spring")
public interface CoverageDomainConverter {

    CoverageDomainConverter INSTANCE = Mappers.getMapper(CoverageDomainConverter.class);

    @Mapping(target = "branchGmtCreate", source = "branchGmtCreate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    CoverageStatusVO converter(CoverageRecordVO coverageRecordVO);

    @Mapping(target = "creatorId", source = "transactor.userId")
    @Mapping(target = "creator", source = "transactor.userName")
    CoveragePublishQuery converter(ReleasedQcEvent event);

    CoverageReasonEditEvent converter(EditCoverageReasonCommand command);

    CoverageNotStandardReasonEditEvent converter(CoverageNotStandardReasonEditCommand command);

    CoverageReportClassInfoResp converter(String classInfo);

    CoverageRecordGenerateVO converter(CoverageRecordBasicVO coverageRecord);

    ReportCoveredDto converterReportCoveredSelf(ReportCoveredDto coverage);
}
