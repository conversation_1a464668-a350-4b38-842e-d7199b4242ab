package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.dto.TagEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseStepEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.*;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.event.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseExpQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseNewQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageTestcaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.XmindFilterQuery;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListXmindDetailResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TestcaseDomainConverter {

    TestcaseMovedEvent converter(MoveTestcaseCommand command);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseDomainAssociatedEvent convertor(AssociateTestcaseDomainCommand command);

    @Mapping(target = "code", source = "aggregateId")
    AddTestcaseEvent convert(AddTestcaseCommand command);

    List<AddTestcaseEvent> convertList(List<AddTestcaseCommand> list);

    @Mapping(target = "aggregateId", source = "code")
    AddTestcaseCommand convertVO(TestcaseVO vo);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseVO convertVO(AddTestcaseCommand command);

    AddTestcaseCommand convertSame(AddTestcaseCommand command);

    TestcaseVO convertVO(TestcaseEntityDO entityDO);

    EditTestcaseCommand convertEdit(AutomaticSourceLogTestcaseVO vo);

    @Mapping(target = "code", source = "aggregateId")
    EditTestcaseEvent convert(EditTestcaseCommand command);

    EditTestcaseCommand convert(TestcaseEntityDO entityDO);

    AddTestcaseCommand convertAdd(AutomaticSourceLogTestcaseVO vo);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseModuleDraggedEvent converter(DragTestcaseModuleCommand command);

    ListTestcaseNewQuery convertor(ListTestcaseExpQuery query);

    void converter(TestcaseEntityDO entity, @MappingTarget ListTestcaseModuleVO vo);

    void converter(TestcaseEntityDO entity, @MappingTarget ListTestcaseVO vo);

    XmindCaseEditEvent convert(XmindCaseEditCommand command);

    XmindCaseAddEvent convert(XmindCaseAddCommand command);

    List<ListXmindDetailVO> convertToList(List<ListTestcaseVO> list);

    @Mapping(target = "incId", source = "id")
    @Mapping(target = "id", source = "code")
    @Mapping(target = "topic", source = "name")
    @Mapping(target = "testcaseStatus", source = "status")
    ListXmindDetailVO converter(ListTestcaseVO vo);

    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "gmtModified", source = "occurred")
    TestcaseEntityDO convertorDeleteToEntity(DeleteTestcaseEvent event);

    @Mapping(target = "code", source = "id")
    @Mapping(target = "name", source = "topic")
    MoveModuleEvent converter(XmindCaseEditCommand command);

    PageTestcaseQuery convert(XmindFilterQuery filterQuery);

    TestcaseVO converter(TestcaseEntityDO entity);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseStatusChangedEvent converter(ChangeTestcaseStatusCommand command);

    @Mapping(target = "caseCode", source = "aggregateId")
    PlanCaseExecutorChangedEvent convertor(ChangePlanCaseExecutorCommand command);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseDutyUserChangedEvent convertor(ChangeTestcaseDutyUserCommand command);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseChangeVersionEvent converter(ChangeVersionCommand command);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseTagRemovedEvent converter(RemoveTestcaseTagCommand command);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseTagAddedEvent converter(AddTestcaseTagCommand command);

    @Mapping(target = "creatorId", source = "transactor.userId")
    @Mapping(target = "creator", source = "transactor.userName")
    @Mapping(target = "gmtCreate", source = "occurred")
    @Mapping(target = "modifierId", source = "transactor.userId")
    @Mapping(target = "modifier", source = "transactor.userName")
    @Mapping(target = "gmtModified", source = "occurred")
    TestcaseEntityDO convertor(AddTestcaseEvent event);

    List<TagEntityDO> convert(List<TagVO> vo);

    List<TestcaseStepEntityDO> convertToStepList(List<TestcaseStepVO> vo);

    EditTestcaseTitleEvent convertor(EditTestcaseTitleCommand command);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseRelationReleasedEvent convertor(ReleaseTestcaseRelationCommand command);

    List<ListXmindDetailResp> convertXmindList(List<ListXmindDetailVO> list);

}
