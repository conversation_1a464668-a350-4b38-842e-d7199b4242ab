package com.zto.devops.qc.domain.service;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.service.agent.model.IsIncludeChaosRuleReq;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.repository.QcAgentRuleConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AgentQueryDomainService {

    @Autowired
    private QcAgentRuleConfigRepository qcAgentRuleConfigRepository;
    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    public List<String> isContainChaosRule(IsIncludeChaosRuleReq req) {
        return qcAgentRuleConfigRepository.isContainChaosRule(req);
    }

    public Map<String, List<Map<String, String>>> getChaosExceptionTypeList() {
        String json = qcConfigBasicService.getChaosException();
        if (StringUtil.isEmpty(json)) {
            throw new ServiceException("获取混沌工程故障注入类型为空");
        }
        ObjectMapper mapper = new ObjectMapper();
        Map<String, List<Map<String, String>>> map;
        try {
            map = mapper.readValue(json, Map.class);
        } catch (JsonProcessingException e) {
            throw new ServiceException("获取混沌工程故障注入类型异常");
        }
        return map;
    }

}
