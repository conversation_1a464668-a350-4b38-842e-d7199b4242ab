package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.dto.QcNoticeResultEntityDO;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testmanager.email.entity.DetailEmailVO;

import java.util.List;

public interface QcNoticeResultRepository {

    DetailEmailVO findSendUserInfo(DetailEmailVO vo);

    /**
     * 更新收件人，抄送人信息 （异步）
     * @param event
     * @param code
     * @param ccUsers
     * @param receiveUsers
     */
    void addDB(BaseEvent event, String code, List<SendUserInfoVO> ccUsers, List<SendUserInfoVO> receiveUsers);

    Object getAttachment(List<AttachmentVO> attachments);

    void insertBatch(List<SendUserInfoVO> allList, String emailCode, User operator);

    List<QcNoticeResultEntityDO> selectByPlanCode(String businessCode);
}
