package com.zto.devops.qc.domain.gateway.auth;

import com.zto.devops.qc.client.model.auth.ApiKeyInfo;

/**
 * API密钥管理服务
 * 
 * <AUTHOR>
 * @since 2025-08-14
 */
public interface ApiKeyService {
    
    /**
     * 根据AccessKey查询密钥信息
     * 
     * @param accessKey 访问密钥ID
     * @return 密钥信息，如果不存在返回null
     */
    ApiKeyInfo getApiKeyInfo(String accessKey);
    
    /**
     * 创建新的API密钥
     * 
     * @param appName 应用名称
     * @param appDescription 应用描述
     * @param creator 创建人
     * @return 新创建的密钥信息
     */
    ApiKeyInfo createApiKey(String appName, String appDescription, String creator);
    
    /**
     * 启用/禁用API密钥
     * 
     * @param accessKey 访问密钥ID
     * @param enabled 是否启用
     */
    void updateApiKeyStatus(String accessKey, boolean enabled);
    
    /**
     * 更新密钥使用统计
     * 
     * @param accessKey 访问密钥ID
     */
    void updateUsageStats(String accessKey);
    
    /**
     * 轮换密钥Secret
     * 
     * @param accessKey 访问密钥ID
     * @return 新的密钥信息
     */
    ApiKeyInfo rotateSecretKey(String accessKey);
}
