package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.dto.MethodDependenceEntityDO;

import java.util.List;

public interface MethodDependenceRepository {

    Integer deleteHistoryData(String versionCode, String appId, Integer limit);

    /**
     * 在独立事务中批量保存方法依赖数据
     * 每个批次对应一个独立事务和一次SQL调用
     *
     * @param batch        批次数据
     * @param batchIndex   批次索引
     * @param totalBatches 总批次数
     * @param tag          批次标识
     */
    void batchSaveInTransaction(List<MethodDependenceEntityDO> batch, int batchIndex, int totalBatches, String tag);

    List<String> queryAffectedEntryCodeList(MethodDependenceEntityDO entityDO);

    List<MethodDependenceEntityDO> queryAffectedInterfaceEntity(List<String> entryCodeList);

    Integer queryCountByCommitId(String commitId);

}
