package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import com.zto.devops.qc.client.model.dto.RelevantUserEntityDO;
import com.zto.devops.qc.client.model.issue.query.ListRelevantUserTypeCountQuery;
import com.zto.devops.qc.client.model.parameter.TaskBaseParameter;
import com.zto.devops.qc.client.model.parameter.TaskResultParameter;
import com.zto.devops.qc.client.model.relevantUser.entity.MyTaskTotalVO;
import com.zto.devops.qc.client.model.relevantUser.query.MyIssueObjQuery;
import com.zto.devops.qc.client.model.relevantUser.query.MyIssueTotalQuery;
import com.zto.devops.qc.client.model.relevantUser.query.MyTaskVO;
import com.zto.devops.qc.domain.converter.RelevantUserConverter;
import com.zto.devops.qc.domain.gateway.repository.IRelevantUserRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/4/9
 * @Version 1.0
 */
@Component
public class MyTaskQueryDomainService {

    @Autowired
    private IRelevantUserRepository relevantUserRepository;

    @Autowired
    private RelevantUserConverter relevantUserConverter;


    public List<MyTaskVO> myIssueObjQuery(MyIssueObjQuery query) {
        List<MyTaskVO> myTaskVOS = new ArrayList<>();
        TaskBaseParameter taskBaseParameter = new TaskBaseParameter();
        taskBaseParameter.setUserId(query.getUserId());
        taskBaseParameter.setName(query.getName());
        List<TaskResultParameter> taskResultParameters = relevantUserRepository.queryMyObj(taskBaseParameter);
        if (CollectionUtils.isNotEmpty(taskResultParameters)) {
            myTaskVOS = taskResultParameters.stream().map(taskResultParameter -> relevantUserConverter.convertIssue(taskResultParameter)).collect(Collectors.toList());
        }
        return myTaskVOS;
    }

    public MyTaskTotalVO myIssueTotalQuery(MyIssueTotalQuery query) {
        List<RelevantUserEntityDO> relevantUserEntities = relevantUserRepository.queryMyTaskTotal(query.getTransactor().getUserId(),
                Collections.singletonList(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name()), query.getIssueStatuses(),query.getProductCode());
        MyTaskTotalVO vo = new MyTaskTotalVO();
        vo.setIssueTotal(relevantUserEntities.size());
        return vo;
    }

    public Long listRelevantUserTypeCountQuery(ListRelevantUserTypeCountQuery query) {
        if (StringUtil.isEmpty(query.getUserId())){
            throw new ServiceException("参数异常");
        }
        return relevantUserRepository.countUserTypeIssueCount(query.getUserId(),query.getStatus());
    }
}
