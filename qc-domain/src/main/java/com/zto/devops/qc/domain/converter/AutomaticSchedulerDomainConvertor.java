package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.testmanager.scheduler.command.*;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.AutomaticSchedulerVO;
import com.zto.devops.qc.client.model.testmanager.scheduler.event.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface AutomaticSchedulerDomainConvertor {

    @Mapping(target = "schedulerCode", source = "aggregateId")
    AutomaticSchedulerAddedEvent converter(AddAutomaticSchedulerCommand command);

    void domainConverter(AutomaticSchedulerAddedEvent event, @MappingTarget AutomaticSchedulerVO domain);

    @Mapping(target = "schedulerCode", source = "aggregateId")
    AutomaticSchedulerEditedEvent converter(EditAutomaticSchedulerCommand command);

    AddSchedulerCasesEvent converter(AddSchedulerCasesCommand command);

    RemoveSchedulerCasesEvent converter(RemoveSchedulerCasesCommand command);

    @Mapping(target = "schedulerCode", source = "aggregateId")
    AutomaticSchedulerExecutionEvent converter(AutomaticSchedulerExecutionCommand command);

    @Mapping(target = "schedulerCode", source = "aggregateId")
    AutomaticPreExecutionUpdateEvent convert(AutomaticSchedulerExecutionCommand command);
}
