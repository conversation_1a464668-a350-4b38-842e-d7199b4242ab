package com.zto.devops.qc.domain.service;

import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.enums.ActionEnum;
import com.zto.devops.framework.domain.gateway.mq.IProducerService;
import com.zto.devops.qc.client.enums.constants.BasicTagEnum;
import com.zto.devops.qc.client.model.issue.entity.IssueMatterDataResp;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.issue.event.IssueAddedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueEditedEvent;
import com.zto.devops.qc.client.model.issue.event.IssueRemovedEvent;
import com.zto.devops.qc.client.model.issue.query.SimpleSingleIssueQuery;
import com.zto.devops.qc.domain.converter.IssueMatterMatterMqConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@Async
public class IssueMatterMqSender {

    @Autowired
    private IProducerService producerService;

    @Autowired
    private IssueMatterMatterMqConverter issueMatterMatterMqConverter;

    @Autowired
    private IssueQueryDomainService issueQueryDomainService;

    public void handleIssueAddedEvent(IssueAddedEvent event) {
        log.info("handleIssueAddedEvent >>> {}", JSON.toJSONString(event));
        IssueMatterDataResp vo = issueMatterMatterMqConverter.convert(event);
        producerService.sendBasic(BasicTagEnum.ISSUE_MATTER.getTag(), ActionEnum.I, event.getCode(), vo);
    }

    public void handleIssueEditedEvent(IssueEditedEvent event) {
        log.info("handleIssueEditedEvent >>> {}", JSON.toJSONString(event));
        SimpleSingleIssueQuery query = new SimpleSingleIssueQuery();
        query.setCode(event.getCode());
        IssueVO voResp = issueQueryDomainService.findIssueByIssueCode(query);
        if (Objects.isNull(voResp)) {
            return;
        }
        IssueMatterDataResp vo = issueMatterMatterMqConverter.convert(voResp);
        issueMatterMatterMqConverter.converter(event, vo);
        producerService.sendBasic(BasicTagEnum.ISSUE_MATTER.getTag(), ActionEnum.U, event.getCode(), vo);
    }

    public void handleIssueRemovedEvent(IssueRemovedEvent event) {
        log.info("handleIssueRemovedEvent >>> {}", JSON.toJSONString(event));
        IssueMatterDataResp vo = issueMatterMatterMqConverter.convert(event);
        producerService.sendBasic(BasicTagEnum.ISSUE_MATTER.getTag(), ActionEnum.D, event.getCode(), vo);
    }

    public void handleIssueVersionEditedEvent(List<String> codes, String versionCode, String versionName, String versionConfirm) {
        log.info("handleIssueVersionEditedEvent >>> codes: {} versionCode: {} versionName: {} versionConfirm: {}",
                codes, versionCode, versionName, versionConfirm);
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        codes.forEach(code -> {
            SimpleSingleIssueQuery query = new SimpleSingleIssueQuery();
            query.setCode(code);
            IssueVO voResp = issueQueryDomainService.findIssueByIssueCode(query);
            if (Objects.isNull(voResp)) {
                return;
            }
            IssueMatterDataResp vo = issueMatterMatterMqConverter.convert(voResp);
            vo.setCode(code);
            vo.setFixVersionCode(versionCode);
            vo.setFixVersionName(versionName);
            vo.setVersionConfirm(versionConfirm);
            producerService.sendBasic(BasicTagEnum.ISSUE_MATTER.getTag(), ActionEnum.U, code, vo);
        });
    }
}
