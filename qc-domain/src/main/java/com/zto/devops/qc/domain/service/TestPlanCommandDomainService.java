package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.rpc.VersionTypeEnum;
import com.zto.devops.qc.client.enums.testPlan.TestPlanTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.*;
import com.zto.devops.qc.client.enums.testmanager.report.TestReportTypeEnum;
import com.zto.devops.qc.client.model.dto.TestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestReportEntityDO;
import com.zto.devops.qc.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.qc.client.model.rpc.product.query.ListProductMemberByPIdQuery;
import com.zto.devops.qc.client.model.rpc.project.*;
import com.zto.devops.qc.client.model.testmanager.plan.command.AddCommonTestPlanCommand;
import com.zto.devops.qc.client.model.testmanager.plan.command.AddSafeTestPlanCommand;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.query.VersionPlanQuery;
import com.zto.devops.qc.domain.gateway.repository.ITmTestPlanRepository;
import com.zto.devops.qc.domain.gateway.repository.ITmTestReportRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/4/13
 * @Version 1.0
 */
@Component
@Slf4j
public class TestPlanCommandDomainService extends BaseDomainService {

    @Autowired
    private IProjectRpcService projectRpcService;

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private TmTestPlanCommandDomainService tmTestPlanCommandDomainService;

    @Autowired
    private ITmTestPlanRepository testPlanRepository;

    @Autowired
    private ITmTestReportRepository testReportRepository;

    public void handleVersionConfirmedEvent(VersionConfirmedEvent event) {
        log.info("接收到项目域版本确认计划 VersionConfirmedEvent {}", event);
        SimpleVersionVO versionVO = getVersionInfo(event.getCode());
        if (!VersionTypeEnum.COMMON_TYPE.name().equals(versionVO.getType())) {
            return;
        }
        String commonPlanCode = generateId(AggregateType.TEST_PLAN);
        AddCommonTestPlanCommand addCommonTestPlanCommand = new AddCommonTestPlanCommand(commonPlanCode);
        TmTestPlanVO commonVO = getCommonTmTestPlanVO(commonPlanCode, versionVO, event);

        String safePlanCode = generateId(AggregateType.TEST_PLAN);
        AddSafeTestPlanCommand addSafeTestPlanCommand = new AddSafeTestPlanCommand(safePlanCode);
        TmTestPlanVO safeVO = getSafeTmTestPlanVO(safePlanCode, versionVO, event);
        safeVO.setRelationPlanCode(commonPlanCode);
        safeVO.setRelationPlanName(commonVO.getPlanName());
        addSafeTestPlanCommand.setTestPlanVO(safeVO);

        commonVO.setRelationPlanCode(safePlanCode);
        commonVO.setRelationPlanName(safeVO.getPlanName());
        addCommonTestPlanCommand.setTestPlanVO(commonVO);

        addSafeTestPlanCommand.setTransactor(event.getTransactor());
        addCommonTestPlanCommand.setTransactor(event.getTransactor());
        tmTestPlanCommandDomainService.handleAddSafeTestPlanCommand(addSafeTestPlanCommand);
        tmTestPlanCommandDomainService.handleAddCommonTestPlanCommand(addCommonTestPlanCommand);
    }

    public void handleVersionEditedEvent(VersionEditedEvent event) {
        testPlanRepository.editTestPlan(event);
    }

    private TmTestPlanVO getCommonTmTestPlanVO(String code, SimpleVersionVO versionEntity, VersionConfirmedEvent event) {
        TmTestPlanVO vo = new TmTestPlanVO();
        vo.setCode(code);
        vo.setType(TestPlanNewTypeEnum.TEST_PLAN);
        vo.setProductCode(versionEntity.getProductCode());
        vo.setProductName(versionEntity.getProductName());
        vo.setVersionCode(versionEntity.getCode());
        vo.setVersionName(versionEntity.getName());
        vo.setTestStrategy(event.getTestStrategy() == null ? null : TestPlanStrategyEnum.getEnumByName(event.getTestStrategy().name()));
        vo.setPlanName(versionEntity.getName() + "测试计划");
        vo.setStatus(TestPlanNewStatusEnum.NOT_STARTED);
        vo.setPerformanceTest(false);
        vo.setExploratoryTest(false);
        vo.setStaticAnalysis(false);
        vo.setSecurityScan(true);
        vo.setMobileSpecialTest(false);
        vo.setDeptId(versionEntity.getDeptId());
        vo.setDeptName(versionEntity.getDeptName());
        buildProductDirector(vo);
        vo.setAccessDate(event.getPresentationDate());
        vo.setPermitDate(event.getApprovalExitDate());
        vo.setPublishDate(event.getPublishDate());
        vo.setStartDate(event.getStartDate());
        if (event.getTestStrategy() == null) {
            return vo;
        }
        if (TestPlanStrategyEnum.ALL_TEST.equals(event.getTestStrategy())
                || TestPlanStrategyEnum.ALLOW_EXIT_TEST.equals(event.getTestStrategy())
                || TestPlanStrategyEnum.STANDARD_TEST.equals(event.getTestStrategy())) {
            Map<String, Object> stageStatus = new HashMap<>();
            stageStatus.put(TestPlanStageEnum.SMOKE_TEST.name(), TestPlanStageStatusEnum.INITIAL);
            stageStatus.put(TestPlanStageEnum.FUNCTIONAL_TEST.name(), TestPlanStageStatusEnum.INITIAL);
            stageStatus.put(TestPlanStageEnum.ONLINE_SMOKE_TEST.name(), TestPlanStageStatusEnum.INITIAL);
            vo.setStageStatus(stageStatus);
        }
        return vo;
    }

    private TmTestPlanVO getSafeTmTestPlanVO(String code, SimpleVersionVO versionEntity, VersionConfirmedEvent event) {
        TmTestPlanVO vo = new TmTestPlanVO();
        vo.setCode(code);
        vo.setType(TestPlanNewTypeEnum.SAFETY_TEST);
        vo.setPlanName(versionEntity.getName() + "安全测试计划");
        vo.setProductCode(versionEntity.getProductCode());
        vo.setProductName(versionEntity.getProductName());
        vo.setVersionCode(versionEntity.getCode());
        vo.setVersionName(versionEntity.getName());
        vo.setDeptId(versionEntity.getDeptId());
        vo.setDeptName(versionEntity.getDeptName());
        vo.setTestStrategy(TestPlanStrategyEnum.NULL_TEST);
        vo.setStatus(TestPlanNewStatusEnum.NOT_STARTED);
        buildProductDirector(vo);
        // 创建安全计划时增加计划提测日期、计划准出日期
        vo.setAccessDate(event.getPresentationDate());
        vo.setPermitDate(event.getApprovalExitDate());
        vo.setPermissionsTest(false);
        vo.setPriority(TestPlanPriorityEnum.MEDIUM);
        return vo;
    }

    private SimpleVersionVO getVersionInfo(String versionCode) {
        SimpleVersionListQuery query = new SimpleVersionListQuery();
        query.setCode(Collections.singletonList(versionCode));
        try {
            SimpleListVersionVO simpleListVersionVO = projectRpcService.simpleVersionListQuery(query);
            return simpleListVersionVO.getSimpleVersionVOList().get(0);
        } catch (Exception e) {
            throw new ServiceException("执行项目域SimpleVersionQuery异常:" + e.getMessage());
        }
    }

    private void buildProductDirector(TmTestPlanVO vo) {
        List<ProductMemberVO> list = findProductMemberByIdQuery(vo.getProductCode(), Collections.singletonList("PRODUCTER_OWNER"));
        if (CollectionUtil.isEmpty(list)) {
            log.warn("版本产品负责人不存在:" + vo.getProductCode());
            return;
        }
        vo.setProductDirectorId(list.get(0).getUserId());
        vo.setProductDirectorName(list.get(0).getUserName());
    }

    private List<ProductMemberVO> findProductMemberByIdQuery(String productCode, List<String> memberTypes) {
        List<ProductMemberVO> list = new LinkedList<>();
        if (StringUtil.isEmpty(productCode)) {
            return list;
        }
        ListProductMemberByPIdQuery query = new ListProductMemberByPIdQuery();
        query.setProductCode(productCode);
        if (CollectionUtil.isNotEmpty(memberTypes)) {
            query.setMemberTypes(memberTypes);
        }
        try {
            list = productRpcService.findProductMemberByIdQuery(productCode, memberTypes);
        } catch (Exception e) {
            log.error("执行产品域ListProductMemberByPIdQuery异常:" + e.getMessage());
        }
        return list;
    }


    public void handleVersionDeleteEvent(VersionDeleteEvent deleteEvent) {
        log.info("版本删除，自动删除测试计划:{}", deleteEvent);
        tmTestPlanCommandDomainService.deleteTestPlan(deleteEvent);
    }

    public void updateVersionInfo(VersionEditedEvent qcEvent) {
        //更新测试计划
        List<TmTestPlanEntityDO> planList = testPlanRepository.listByVersionCodesAndTypes(qcEvent.getCode(), Arrays.asList(TestPlanTypeEnum.values()));
        if (CollectionUtil.isNotEmpty(planList)) {
            List<TmTestPlanEntityDO> versionChangePlanList = planList.stream().filter(item -> (!item.getVersionName().equals(qcEvent.getName()))).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(versionChangePlanList)) {
                versionChangePlanList.forEach(item -> testPlanRepository.updateVersionInfo(item, qcEvent.getName(), qcEvent.getTransactor()));
            }
        }

        //更新测试报告
        List<TmTestReportEntityDO> reportList = testReportRepository.listByVersionCodesAndTypes(Collections.singletonList(qcEvent.getCode()),
                Arrays.asList(TestReportTypeEnum.values()));
        if (CollectionUtil.isNotEmpty(reportList)) {
            List<TmTestReportEntityDO> versionChangeReportList = reportList.stream().filter(item -> (!item.getVersionName().equals(qcEvent.getName()))).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(versionChangeReportList)) {
                versionChangeReportList.forEach(item -> testReportRepository.updateVersionInfo(item, qcEvent.getName(), qcEvent.getTransactor()));
            }
        }
    }
}
