package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.issue.command.AddIssueCommand;
import com.zto.devops.qc.client.model.issue.command.AddVersionIssueCommand;
import com.zto.devops.qc.client.model.issue.event.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface IssueAddedEventConverter {
    //需要改动
    IssueAddedEventConverter INSTANCE = Mappers.getMapper(IssueAddedEventConverter.class);

    @Mapping(target = "finder", source = "transactor")
//    @Mapping(target = "tester", source = "transactor")
    @Mapping(target = "code", source = "aggregateId")
    @Mapping(target = "occurred", expression = "java(new java.util.Date())")
    @Mapping(target = "sprint.code", expression = "java((sprint!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(sprint.getCode())) ?sprint.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.SPRINT_VALUE.getValue())")
    @Mapping(target = "fixVersion.code", expression = "java((version!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(version.getCode())) ?version.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.VERSION_VALUE.getValue())")
    @Mapping(target = "requirement.code", expression = "java((requirement!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(requirement.getCode())) ?requirement.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.REQUIREMENT_VALUE.getValue())")
    IssueAddedEvent convert(AddIssueCommand command);

    @Mapping(target = "finder", source = "transactor")
    @Mapping(target = "tester", source = "transactor")
    @Mapping(target = "code", source = "aggregateId")
    @Mapping(target = "occurred", expression = "java(new java.util.Date())")
    @Mapping(target = "sprint.code", expression = "java((sprint!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(sprint.getCode())) ?sprint.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.SPRINT_VALUE.getValue())")
    @Mapping(target = "fixVersion.code", expression = "java((version!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(version.getCode())) ?version.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.VERSION_VALUE.getValue())")
    @Mapping(target = "requirement.code", expression = "java((requirement!=null && com.zto.devops.framework.common.util.StringUtil.isNotBlank(requirement.getCode())) ?requirement.getCode(): com.zto.devops.qc.client.enums.constants.DefaultValueEnum.REQUIREMENT_VALUE.getValue())")
    IssueAddedEvent convert(AddVersionIssueCommand command);

}
