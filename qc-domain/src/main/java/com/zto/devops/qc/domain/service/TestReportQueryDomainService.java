package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.report.OperatedButtonEnum;
import com.zto.devops.qc.client.enums.report.ReportType;
import com.zto.devops.qc.client.enums.rpc.MemberTypeEnum;
import com.zto.devops.qc.client.enums.testPlan.TestPlanTypeEnum;
import com.zto.devops.qc.client.model.dto.TestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TestReportEntityDO;
import com.zto.devops.qc.client.model.report.entity.OperatedTestManageVO;
import com.zto.devops.qc.client.model.report.entity.OpertatedButton;
import com.zto.devops.qc.client.model.report.query.OperatedTestManageQuery;
import com.zto.devops.qc.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.qc.domain.gateway.repository.ITmTestPlanRepository;
import com.zto.devops.qc.domain.gateway.repository.TestReportRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TestReportQueryDomainService extends BaseDomainService {

    @Autowired
    private TestReportRepository testReportRepository;

    @Autowired
    private ITmTestPlanRepository iTmTestPlanRepository;

    @Autowired
    private IProductRpcService iProductRpcService;

    public OperatedTestManageVO query(OperatedTestManageQuery query) {
        OperatedTestManageVO operatedTestManageVO = new OperatedTestManageVO();

        List<MemberTypeEnum> test = Arrays.asList(MemberTypeEnum.TESTER, MemberTypeEnum.TESTER_OWNER, MemberTypeEnum.TESTER_M);
        List<ProductMemberVO> productMembers = this.getProductMember(query.getProductCode(), test);
        if (CollectionUtil.isEmpty(productMembers)) {
            return operatedTestManageVO;
        }
        Boolean contain = productMembers.stream().map(ProductMemberVO::getUserId).collect(Collectors.toList()).contains(query.getUserId());
        if (!contain) {
            return operatedTestManageVO;
        }
        operatedTestManageVO.setTestPlanOperations(handleOperatTestPlan(query));
        operatedTestManageVO.setTestReportOperations(handleOperatTestReport(query));
        return operatedTestManageVO;
    }


    public List<ProductMemberVO> getProductMember(String productCode, List<MemberTypeEnum> memberTypes) {
        List<ProductMemberVO> vo = new ArrayList<>();
        try {
            List<String> members = memberTypes.stream().map(Enum::name).collect(Collectors.toList());
            List<ProductMemberVO> memberVOS = iProductRpcService.getProductMember(productCode,members);
            vo = memberVOS;
        } catch (Exception e) {
            log.error("query getProductMember is error ,productCode {}  error {}", productCode, e.toString(), e);
        }
        return vo;
    }



    /**
     * 显示测试计划中的可创建的报告种类（不可创建的报告选项隐藏）
     * a：测试计划：不存在测试计划、简易测试流程报告、验收测试报告时，可创建。
     * b：安全测试计划：存在测试计划，未创建安全测试计划时，可创建。
     * @param query
     * @return
     */
    private List<OpertatedButton> handleOperatTestPlan(OperatedTestManageQuery query) {
        List<OpertatedButton> result = new ArrayList<>();
        List<TestReportEntityDO> testReportEntityList = testReportRepository.selectTestReportByVersionAnReportTypeList(query.getVersionCode(),Arrays.asList(ReportType.SIMPLE_PROCESS, ReportType.CHECED_TEST));
        List<TestPlanEntityDO> testPlanEntityList = iTmTestPlanRepository.selectTestPlanByVersionCodeAndTypeList(query.getVersionCode(),Arrays.asList(TestPlanTypeEnum.TEST_PLAN));
        if (CollectionUtil.isEmpty(testReportEntityList) && CollectionUtil.isEmpty(testPlanEntityList)) {
            result.add(new OpertatedButton(OperatedButtonEnum.TEST_PLAN, OperatedButtonEnum.TEST_PLAN.getValue()));
        }
        List<TestPlanEntityDO> testPlanEntityList2 = iTmTestPlanRepository.selectTestPlanByVersionCodeAndTypeList(query.getVersionCode(),Arrays.asList(TestPlanTypeEnum.SAFETY_TEST));
        if (CollectionUtil.isNotEmpty(testPlanEntityList) && CollectionUtil.isEmpty(testPlanEntityList2)) {
            result.add(new OpertatedButton(OperatedButtonEnum.SAFETY_TEST, OperatedButtonEnum.SAFETY_TEST.getValue()));
        }
        return result;
    }

    /**
     *
     4、点击『创建报告』，显示测试计划中的可创建的报告种类（不可创建的报告选项隐藏）
     a：用例评审报告：存在测试计划，未创建用例评审报告时，可创建。
     b：测试准入报告：存在测试计划，未创建测试准入报告时，可创建。
     c：测试准出报告：存在测试计划，未创建测试准出报告，且测试准入报告已创建时，可创建
     d：线上冒烟报告：存在测试计划，未创建线上冒烟报告，且测试准出报告已创建时，可创建。
     e：简易流程测试报告：不存在测试计划、简易测试流程报告、验收测试报告时，可创建。
     f：验收测试报告：不存在测试计划、简易测试流程报告、验收测试报告时，可创建。
     * @param query
     * @return
     */
    private List<OpertatedButton> handleOperatTestReport(OperatedTestManageQuery query) {
        List<OpertatedButton> result = new ArrayList<>();
        List<TestPlanEntityDO> testPlanEntityList = iTmTestPlanRepository.selectTestPlanByVersionCodeAndType(query.getVersionCode());
        if (CollectionUtil.isNotEmpty(testPlanEntityList)) {
            List<TestReportEntityDO> testReportEntityList = testReportRepository.selectTestReportByVersionAnReportTypeList(query.getVersionCode(), Arrays.asList(ReportType.CASE_REVIEW, ReportType.TEST_ACCESS, ReportType.TEST_PERMIT, ReportType.ONLINE_SMOKE));
            List<ReportType> reportTypeList = testReportEntityList.stream().map(TestReportEntityDO::getReportType).collect(Collectors.toList());
            if (!reportTypeList.contains(ReportType.CASE_REVIEW)) {
                result.add(new OpertatedButton(OperatedButtonEnum.CASE_REVIEW, OperatedButtonEnum.CASE_REVIEW.getValue()));
            }
            if (!reportTypeList.contains(ReportType.TEST_ACCESS)) {
                result.add(new OpertatedButton(OperatedButtonEnum.TEST_ACCESS, OperatedButtonEnum.TEST_ACCESS.getValue()));
            }
            if (!reportTypeList.contains(ReportType.TEST_PERMIT) && reportTypeList.contains(ReportType.TEST_ACCESS)) {
                result.add(new OpertatedButton(OperatedButtonEnum.TEST_PERMIT, OperatedButtonEnum.TEST_PERMIT.getValue()));
            }
            if (!reportTypeList.contains(ReportType.ONLINE_SMOKE) && reportTypeList.contains(ReportType.TEST_PERMIT)) {
                result.add(new OpertatedButton(OperatedButtonEnum.ONLINE_SMOKE, OperatedButtonEnum.ONLINE_SMOKE.getValue()));
            }
        } else {
            List<TestReportEntityDO> testReportEntityList = testReportRepository.selectTestReportByVersionAnReportTypeList(query.getVersionCode(), Arrays.asList(ReportType.SIMPLE_PROCESS, ReportType.CHECED_TEST));
            if (CollectionUtil.isEmpty(testReportEntityList)) {
                result.add(new OpertatedButton(OperatedButtonEnum.SIMPLE_PROCESS, OperatedButtonEnum.SIMPLE_PROCESS.getValue()));
                result.add(new OpertatedButton(OperatedButtonEnum.CHECED_TEST, OperatedButtonEnum.CHECED_TEST.getValue()));
            }
        }

        return result;
    }

}
