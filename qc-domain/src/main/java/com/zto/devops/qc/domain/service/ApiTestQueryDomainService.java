package com.zto.devops.qc.domain.service;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.rpc.MemberTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.*;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.rpc.pipeline.ApplicationResp;
import com.zto.devops.qc.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.qc.client.model.rpc.product.SimpleProductQueryListVO;
import com.zto.devops.qc.client.model.rpc.product.query.ListProductMemberByPIdQuery;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.*;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.metadata.MetaDataDocHttpVO;
import com.zto.devops.qc.client.model.testmanager.apitest.query.*;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ApiTestCaseExecuteDetailTiledVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.AmazonS3ConfigVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.PageApiTestVariableVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.PageSceneInfoVO;
import com.zto.devops.qc.client.service.testmanager.apitest.model.*;
import com.zto.devops.qc.client.service.testmanager.cases.model.ApiTestCaseExecuteDetailReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ApiTestCaseExecuteDetailResp;
import com.zto.devops.qc.domain.converter.ApiTestDomainConverter;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.metadata.MetaDataService;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.*;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.util.ApiDebugLogService;
import com.zto.devops.qc.domain.gateway.zbase.ZbaseService;
import com.zto.devops.qc.domain.util.GenerateApiCaseUtil;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApiTestQueryDomainService extends BaseDomainService {

    @Autowired
    private ApiTestRepository apiTestRepository;

    @Autowired
    private AuthCookieDomainService authCookieDomainService;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    @Autowired
    private IAutomaticTaskRepository automaticTaskRepository;

    @Autowired
    private IPipelineRpcService pipelineRpcService;

    @Autowired
    private MetaDataService metaDataService;

    @Autowired
    private ApiTestDomainConverter apiTestDomainConverter;

    @Autowired
    private AutomaticSourceRecordRepository automaticSourceRecordRepository;

    @Autowired
    private ApiDebugLogService apiDebugLogService;

    @Autowired
    private ZbaseService zbaseService;

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private TmSceneDebugRecordRepository tmSceneDebugRecordRepository;

    @Autowired
    private ZtoOssService ossService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ApiGlobalConfigurationRepository apiGlobalConfigurationRepository;

    @Autowired
    private TmSceneCollectionRepository tmSceneCollectionRepository;

    public PageApiTestVariableVO queryApiTestVariablePage(PageApiTestVariableReq req) {
        Page<Object> page = PageHelper.startPage(req.getPage(), req.getSize());
        List<ApiTestVariableVO> voList = apiTestRepository.queryApiTestVariablePage(req);
        PageApiTestVariableVO pageApiTestVariableVO = new PageApiTestVariableVO();
        if (CollectionUtil.isEmpty(voList)) {
            pageApiTestVariableVO.setList(new ArrayList<>());
            pageApiTestVariableVO.setTotal(0L);
            return pageApiTestVariableVO;
        }
        pageApiTestVariableVO.setList(voList);
        pageApiTestVariableVO.setTotal(page.getTotal());
        return pageApiTestVariableVO;

    }

    public List<ListExecuteDetailResp> listExecuteDetail(ListExecuteDetailReq req) {
        return CollectionUtil.newEmptyList();
    }

    private Integer getSumTestCaseCount(List<ListExecuteDetailResp> list) {
        int count = 0;
        for (ListExecuteDetailResp resp : list) {
            if (null != resp.getTestcaseCount()) {
                count = count + resp.getTestcaseCount();
            }
        }
        return count;
    }

    public List<ListExecuteDetailResp> buildCaseExecuteDetail(List<TestcaseExecuteRecordEntityDO> recordList, List<ApiCaseEntityDO> apiCaseList) {
        List<ListExecuteDetailResp> caseList = new ArrayList<>();
        Map<String, List<ApiCaseEntityDO>> caseMap = apiCaseList.stream().collect(Collectors.groupingBy(ApiCaseEntityDO::getCaseCode));
        recordList.stream().forEach(r -> {
            if (null != caseMap.get(r.getTestcaseCode())) {
                ListExecuteDetailResp resp = new ListExecuteDetailResp();
                ApiCaseEntityDO entityDO = caseMap.get(r.getTestcaseCode()).get(0);
                resp.setCode(r.getTestcaseCode());
                resp.setApiCode(entityDO.getApiCode());
                resp.setAutomaticTaskCode(r.getAutomaticTaskCode());
                resp.setName(entityDO.getCaseName());
                resp.setAttribute(TestcaseAttributeEnum.TESTCASE);
                resp.setParentCode(entityDO.getParentCode());
                resp.setEnable(true);
                resp.setResult(r.getResult());
                resp.setExecutorId(r.getCreatorId());
                resp.setExecutor(r.getCreator());
                resp.setStartTime(r.getStartTime());
                resp.setFinishTime(r.getFinishTime());
                resp.setReportFile(getFullFilepath(r.getReportFile()));
                resp.setExecLogFile(getFullFilepath(r.getExecLogFile()));
                caseList.add(resp);
            }
        });
        return caseList;
    }

    /**
     * 获取完整文件路径
     *
     * @param filepath 文件路径
     * @return 完整文件路径
     */
    private String getFullFilepath(String filepath) {
        AmazonS3ConfigVO configVO = qcConfigBasicService.getAmazonS3Config();
        String callbackBucketName = qcConfigBasicService.getCallbackBucketName();
        if (StringUtils.isBlank(filepath)) {
            return null;
        }
        return MessageFormat.format("{0}/{1}/{2}", configVO.getEndPoint(), callbackBucketName, filepath);
    }

    public PageApiCaseVO queryApiCasePage(PageApiCaseReq req) {
        PageApiCaseQuery query = apiTestDomainConverter.convert(req);
        //用例列表
        Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        List<ApiCaseVO> doList = apiTestRepository.queryApiCasePage(query);
        return PageApiCaseVO.buildSelf(doList, page.getTotal());
    }

    public PageApiVO queryApiPage(PageApiReq req) {
        if (StringUtils.isBlank(req.getProductCode())) {
            return PageApiVO.buildSelf(new ArrayList<>(), 0L);
        }
        Page<Object> page = PageHelper.startPage(req.getPage(), req.getSize());
        List<ApiVO> doList = apiTestRepository.queryApiPage(req);
        if (CollectionUtil.isNotEmpty(doList)) {
            List<String> mainApiCodeList =
                    doList.stream().map(ApiVO::getApiCode).collect(Collectors.toList());
            List<ApiTestEntityDO> docVersionList =
                    apiTestRepository.queryApiTestDocVersionByMainApiCode(mainApiCodeList);
            Map<String, String> docVersionMap = docVersionList.stream().collect(Collectors.toMap(
                    ApiTestEntityDO::getMainApiCode, ApiTestEntityDO::getDocVersion, (i1, i2) -> i1 + "," + i2));
            doList.forEach(vo -> vo.setDocVersion(docVersionMap.get(vo.getApiCode())));
        }
        return PageApiVO.buildSelf(doList, page.getTotal());
    }

    public List<ApplicationResp> queryAppIdList(ListAppIdReq req) {
        if (null == req || StringUtils.isBlank(req.getProductCode())) {
            return new ArrayList<>();
        }

        List<ApplicationResp> respList = pipelineRpcService.getApplicationList(req.getProductCode());
        if (CollectionUtil.isEmpty(respList)) {
            return new ArrayList<>();
        }

        if (StringUtils.isNotBlank(req.getAppIdOrAppName())) {
            respList = respList.stream().filter(item -> (item.getAppId().contains(req.getAppIdOrAppName()) || item.getName().contains(req.getAppIdOrAppName()))).collect(Collectors.toList());
        }
        return respList;
    }

    public PageApiDocVersionVO queryApiDocVersionPage(PageApiDocVersionReq req) {
        Page<Object> page = PageHelper.startPage(req.getPage(), req.getSize());
        List<ApiDocVersionVO> doList = apiTestRepository.queryApiDocVersionPage(req);
        return PageApiDocVersionVO.buildSelf(doList, page.getTotal());
    }

    public PageApiLiteInfoVO queryApiLiteInfoPage(PageApiLiteInfoReq req) {
        if (StringUtils.isBlank(req.getProductCode())) {
            return PageApiLiteInfoVO.buildSelf(new ArrayList<>(), 0L);
        }
        Page<Object> page = PageHelper.startPage(req.getPage(), req.getSize());
        List<ApiLiteInfoVO> list = apiTestRepository.queryApiLiteInfoPage(req);
        return PageApiLiteInfoVO.buildSelf(list, page.getTotal());
    }

    public PageApiCaseChildrenVO queryApiCaseChildrenPage(PageApiCaseChildrenReq req) {
        Page<Object> page = PageHelper.startPage(req.getPage(), req.getSize());
        List<ApiCaseChildVO> doList = apiTestRepository.queryApiCaseChildrenPage(req);
        return PageApiCaseChildrenVO.buildSelf(doList, page.getTotal());
    }

    public Integer countCase(CountCaseReq req) {
        return apiTestRepository.countCase(req);
    }

    public ApiCaseDetailResp getApiCaseDetail(ApiCaseDetailReq req) {
        ApiTestCaseEntityDO apiCase = apiTestRepository.queryApiCaseByCodeAndStatus(req.getCaseCode(), req.getStatus());
        if (null == apiCase && ApiCaseStatusEnum.publish.equals(req.getStatus())) {
            apiCase = apiTestRepository.queryApiCaseByCodeAndStatus(req.getCaseCode(), ApiCaseStatusEnum.edit);
        }
        if (null == apiCase) {
            throw new ServiceException("接口用例不存在！");
        }
        return apiTestDomainConverter.convertApiCaseDetailResp(apiCase);
    }

    public List<ApiTestInitMethodResp> queryApiTestInitMethod(ApiTestInitMethodReq req) {
        String jmeterInitMethod = qcConfigBasicService.getJmeterInitMethod();
        List<ApiTestInitMethodResp> resp = JSON.parseArray(jmeterInitMethod, ApiTestInitMethodResp.class);
        if (StringUtils.isNotEmpty(req.getSearch())) {
            resp = resp.stream().filter(method -> method.getMethodDesc().contains(req.getSearch().trim()) || method.getMethodName().contains(req.getSearch().trim())).collect(Collectors.toList());
        }
        return resp;
    }

    public PageSceneInfoVO querySceneInfoPage(PageSceneInfoReq req) {
        PageSceneInfoVO pageSceneInfoVO = new PageSceneInfoVO();
        boolean flag = (req.getShareProductFlag() != null && req.getShareProductFlag());

        List<SceneIndexVO> list = apiTestRepository.queryAllSceneModule(convertSceneModuleQueryReq(req));
        if (CollectionUtils.isEmpty(list)) {
            return initPageSceneInfoVO();
        }
        PageSceneInfoReq pageSceneInfoReq = convertPageSceneInfoReq(req, list);
        if (null == pageSceneInfoReq) {
            return initPageSceneInfoVO();
        }
        List<SceneInfoEntityDO> sceneInfoEntityDOS = apiTestRepository.querySceneInfoTagList(pageSceneInfoReq.getSceneCodeList());
        Map<String, String> sceneTagMap = ApiTestCommandDomainService.convertToMap(sceneInfoEntityDOS);
        // 查场景
        Page<Object> page = PageHelper.startPage(req.getPage(), req.getSize());
        List<SceneInfoEntityDO> voList = apiTestRepository.pageQuerySceneList(pageSceneInfoReq);
        if (CollectionUtil.isEmpty(voList)) {
            return initPageSceneInfoVO();
        }
        pageSceneInfoVO.setList(convertSceneInfoEntityDOList(voList, pageSceneInfoReq, flag, sceneTagMap, list));
        pageSceneInfoVO.setTotal(page.getTotal());
        return pageSceneInfoVO;
    }

    private static PageSceneInfoVO initPageSceneInfoVO() {
        PageSceneInfoVO pageSceneInfoVO = new PageSceneInfoVO();
        pageSceneInfoVO.setList(new ArrayList<>());
        pageSceneInfoVO.setTotal(0L);
        return pageSceneInfoVO;
    }

    /**
     * Convert PageSceneInfoReq to SceneModuleQueryReq
     */
    private SceneModuleQueryReq convertSceneModuleQueryReq(PageSceneInfoReq req) {
        SceneModuleQueryReq queryReq = new SceneModuleQueryReq();
        queryReq.setProductCode(req.getProductCode());
        queryReq.setSceneType(req.getSceneType());
        queryReq.setShareProductFlag((req.getShareProductFlag() != null && req.getShareProductFlag() && req.getParentCode().equals("ALL")));        //ALL查询全部非product
        if (Boolean.TRUE.equals(req.getShareProductFlag())) {
            queryReq.setStatus(Collections.singletonList(2));
        }
        return queryReq;
    }

    /**
     * Convert PageSceneInfoReq to PageSceneInfoReq
     */
    private PageSceneInfoReq convertPageSceneInfoReq(PageSceneInfoReq req, List<SceneIndexVO> list) {
        List<String> sceneCodeList = buildSceneCodeList(req, list);
        if (CollectionUtil.isEmpty(sceneCodeList)) {
            return null;
        }
        req.setParentCode(null);
        req.setSceneIndexType(SceneIndexTypeEnum.SCENE.getCode());
        req.setSceneCodeList(sceneCodeList);
        if (CollectionUtil.isNotEmpty(req.getTagNameList()) && req.getTagNameList().contains(SceneTagEnum.BE_SHARING)) {
            req.getTagNameList().remove(SceneTagEnum.BE_SHARING);
            req.setShareStatus(Boolean.TRUE);
        }
        return req;
    }

    private List<String> buildSceneCodeList(PageSceneInfoReq req, List<SceneIndexVO> list) {
        List<String> sceneCodeList = new ArrayList<>();
        if (req.getShareProductFlag() != null && req.getShareProductFlag()) {
            handleSharedProduct(req, list, sceneCodeList);
        } else {
            sceneCodeList.addAll(getSceneCodeList(list, req.getParentCode()));
        }
        if (StringUtils.isEmpty(req.getSceneType())) {
            if (CollectionUtil.isNotEmpty(sceneCodeList)) {
                List<SceneInfoEntityDO> sceneInfoEntityDOS = apiTestRepository.querySceneInfoTagList(sceneCodeList);
                Map<String, String> sceneTagMap = ApiTestCommandDomainService.convertToMap(sceneInfoEntityDOS);
                if (CollectionUtil.isNotEmpty(req.getSceneTagNameList())) {
                    sceneCodeList = getSceneCodesByTagNameList(sceneCodeList, sceneTagMap, req.getSceneTagNameList());
                }
            }
        }
        return sceneCodeList;
    }


    private void handleSharedProduct(PageSceneInfoReq req, List<SceneIndexVO> list, List<String> sceneCodeList) {
        if (PreDataTagEnum.MY_COLLECT.equals(req.getPreDataTagType())) {
            List<SceneCollectionEntityDO> sceneCollectionEntityDOS = tmSceneCollectionRepository.selectFavoritesPreData(req.getCurrentUserId());
            if (CollectionUtil.isEmpty(sceneCollectionEntityDOS)) {
                req.setShareProductFlag(false);
                req.setShareStatus(true);
                return;
            }
            Set<String> sceneIndexCodes = sceneCollectionEntityDOS.stream()
                    .map(SceneCollectionEntityDO::getSceneIndexCode)
                    .collect(Collectors.toSet());
            list.removeIf(sceneIndex -> !sceneIndexCodes.contains(sceneIndex.getSceneIndexCode()));
        }
        boolean shareProductFlag = false;
        if (req.getParentCode().equals("ALL")) { //共享所有产品
            shareProductFlag = true;
            Map<String, List<SceneIndexVO>> productCodeMap = list.stream().collect(Collectors.groupingBy(SceneIndexVO::getProductCode));
            productCodeMap.values().forEach(sceneIndexVOS -> sceneCodeList.addAll(getSceneCodeList(sceneIndexVOS, req.getParentCode())));
        } else if (req.getParentCode().equals(req.getProductCode())) { // 共享单个产品
            sceneCodeList.addAll(getSceneCodeList(list, "ALL"));
        } else {// 共享模块与造数
            sceneCodeList.addAll(getSceneCodeList(list, req.getParentCode()));
        }
        req.setShareProductFlag(shareProductFlag);
        req.setShareStatus(true);
    }


    /**
     * 获取场景或者造数code集合
     *
     * @param list
     * @param parentCode
     * @return
     */
    public List<String> getSceneCodeList(List<SceneIndexVO> list, String parentCode) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        Map<String, List<SceneIndexVO>> moduleMap = list.stream().collect(Collectors.groupingBy(SceneIndexVO::getParentCode));
        moduleMap.put("resultList", new ArrayList<>());
        querySceneListIterator(moduleMap, moduleMap.get(parentCode));
        if (CollectionUtils.isEmpty(moduleMap.get("resultList"))) {
            return Collections.emptyList();
        }
        return moduleMap.get("resultList").stream().map(SceneIndexVO::getSceneIndexCode).collect(Collectors.toList());
    }

    /**
     * 获取匹配场景code集合
     *
     * @param sceneCodeList
     * @param sceneTagMap
     * @param sceneTagNameList
     * @return
     */
    private List<String> getSceneCodesByTagNameList(List<String> sceneCodeList, Map<String, String> sceneTagMap, List<String> sceneTagNameList) {
        List<String> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(sceneCodeList)) {
            return Collections.emptyList();
        }
        sceneCodeList.forEach(x -> {
            String sceneTag = sceneTagMap.get(x);
            if (StringUtils.isEmpty(sceneTag)) {
                return;
            }
            List<String> trimmedTags = Arrays.stream(sceneTag.split(",")).map(String::trim).collect(Collectors.toList());
            for (String tag : sceneTagNameList) {
                if (tag != null && trimmedTags.contains(tag)) {
                    result.add(x);
                }
            }
        });
        return result;
    }

    private List<SceneInfoEntityDO> convertSceneInfoEntityDOList(List<SceneInfoEntityDO> voList, PageSceneInfoReq req, Boolean flag,
                                                                 Map<String, String> sceneTagMap, List<SceneIndexVO> moduleList) {
        Map<String, List<SceneIndexVO>> moduleMap = moduleList.stream().collect(Collectors.groupingBy(SceneIndexVO::getSceneIndexCode));
        voList.stream().forEach(vo -> {
            List<SceneTagVO> tagVOS = new ArrayList<>();
            if (StringUtils.isNotBlank(vo.getTagName())) {
                buildTagVOList(vo.getTagName(), vo.getStepRecord(), tagVOS);
            }
            if (vo.getShareStatus()) {
                buildTagVOList(vo.getShareStatus(), tagVOS);
            }
            vo.setTagVOList(tagVOS);
            if (StringUtils.isNotEmpty(vo.getStepRecord())) {
                JSONObject jsonObject = JSON.parseObject(vo.getStepRecord());
                String result = jsonObject.getString("result");
                if ("IN_PROGRESS".equals(result)) {
                    vo.setEnableDesc("发布中");
                    vo.setEnable(SceneInfoEnableEnum.PUBLISHING);
                }
            }
            vo.setSceneIndexType(SceneIndexTypeEnum.SCENE);
            if (StringUtils.isNotEmpty(req.getSceneType()) && req.getSceneType().equals(UseCaseFactoryTypeEnum.CREATE.name())) {
                Map<String, List<Button>> querySceneInfoPagePermissionInfo = getQuerySceneInfoPagePermissionInfo(vo, req, flag);
                if (MapUtils.isNotEmpty(querySceneInfoPagePermissionInfo)) {
                    vo.setButtonList(querySceneInfoPagePermissionInfo.get("BUTTON"));
                    vo.setCollapseButtonList(querySceneInfoPagePermissionInfo.get("COLLAPSE_BUTTON"));
                }
                if (creatorAndSuperManagerPermission(req.getCurrentUserId(), vo.getCreatorId(), req.getPermissions())) {
                    vo.setIsCheck(Boolean.TRUE);
                } else {
                    vo.setIsCheck(Boolean.FALSE);
                }
            }
            if (StringUtils.isEmpty(req.getSceneType()) && MapUtils.isNotEmpty(sceneTagMap)) {
                vo.setSceneTagData(sceneTagMap.get(vo.getSceneCode()));
            }
            vo.setScenePath(findScenePath(req, flag, vo.getParentCode(), moduleMap));
        });
        voList.sort((t1, t2) -> t2.getGmtModified().compareTo(t1.getGmtModified()));
        return voList;
    }

    private String findScenePath(PageSceneInfoReq req, boolean flag, String moduleCode, Map<String, List<SceneIndexVO>> moduleMap) {
        if("CREATE".equals(req.getSceneType())) {
            if(!flag) {
                return "全部造数/" + buildScenePath(moduleCode, moduleMap, "");
            }
        } else {
            return "全部场景/" + buildScenePath(moduleCode, moduleMap, "");
        }
        return "/";
    }

    private String buildScenePath(String moduleCode, Map<String, List<SceneIndexVO>> moduleMap, String path) {
        if (!moduleMap.containsKey(moduleCode)) {
            return path;
        }
        SceneIndexVO moduleVO = moduleMap.get(moduleCode).get(0);
        return buildScenePath(moduleVO.getParentCode(), moduleMap,  moduleVO.getSceneIndexName() + "/" + path);
    }

    private void querySceneListIterator(Map<String, List<SceneIndexVO>> moduleMap, List<SceneIndexVO> list) {
        List<SceneIndexVO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (SceneIndexVO indexVO : list) {
            if (SceneIndexTypeEnum.MODULE.equals(indexVO.getSceneIndexType())) {
                querySceneListIterator(moduleMap, moduleMap.get(indexVO.getSceneIndexCode()));
            } else {
                resultList.add(indexVO);
            }
        }
        moduleMap.get("resultList").addAll(resultList);
    }

    private List<SceneTagVO> buildTagVOList(String tagName, String stepRecord, List<SceneTagVO> tagVOS) {
        String[] tagNameList = tagName.split(",");
        for (String tag : tagNameList) {
            SceneTagVO tagVO = new SceneTagVO();
            tagVO.setTagName(SceneTagEnum.enumOf(tag));
            tagVO.setTagNameDesc(SceneTagEnum.getValue(tag));
            if (SceneTagEnum.GENERATE_ERROR.name().equals(tag)) {
                JSONObject object = JSON.parseObject(stepRecord);
                if (Objects.nonNull(object) && StringUtil.isNotEmpty(object.getString("result")) && !object.getString("result").equals("SUCCESS")) {
                    tagVO.setPublishErrorMsg(object.getString("message"));
                }
            }
            tagVOS.add(tagVO);
        }
        return tagVOS;
    }

    private List<SceneTagVO> buildTagVOList(Boolean shareStatus, List<SceneTagVO> tagVOS) {
        if (shareStatus) {
            SceneTagVO tagVO = new SceneTagVO();
            tagVO.setTagName(SceneTagEnum.BE_SHARING);
            tagVO.setTagNameDesc(SceneTagEnum.BE_SHARING.getAlias());
            tagVOS.add(tagVO);
        }
        return tagVOS;
    }

    public SceneInfoResp querySceneInfo(SceneInfoReq req, Long userId) {
        SceneInfoEntityDO entityDO = apiTestRepository.queryLatestSceneInfo(req.getSceneCode(), req.getStatus());
        if (entityDO == null && SceneInfoStatusEnum.publish == req.getStatus()) {
            req.setStatus(SceneInfoStatusEnum.edit);
            entityDO = apiTestRepository.queryLatestSceneInfo(req.getSceneCode(), req.getStatus());
        }
        if (null != entityDO && StringUtils.isNotBlank(entityDO.getSceneCode())) {
            SceneIndexVO indexVO = apiTestRepository.querySceneIndexByCode(entityDO.getSceneCode());
            if (null != indexVO) {
                entityDO.setParentCode(indexVO.getParentCode());
            }
        }
        SceneInfoResp resp = new SceneInfoResp();
        if (null != entityDO) {
            resp = apiTestDomainConverter.convert2SceneInfoResp(entityDO);
            if (StringUtils.isNotEmpty(entityDO.getSceneBackData())) {
                resp.setSceneBackData(JSON.parseObject(entityDO.getSceneBackData()));
            }
        }
        resp.setIsUploadShow(hasUploadPermission(userId));
        return resp;
    }

    private Boolean hasUploadPermission(Long userId) {
        String permissionConfig = qcConfigBasicService.getSceneJmxUploadPermission();
        if (StringUtils.isEmpty(permissionConfig)) {
            return false;
        }
        String[] permissionArray = permissionConfig.split(",");
        if (CollectionUtil.isEmpty(permissionArray)) {
            return false;
        }
        List<String> permissionList = new ArrayList<>(Arrays.asList(permissionArray));
        return permissionList.contains(String.valueOf(userId));
    }

    public SceneInfoResp queryLatestEditSceneInfo(QueryLatestEditSceneInfoReq req) {
        return apiTestDomainConverter.convert2SceneInfoResp(apiTestRepository.queryLatestEditSceneInfo(req));
    }

    public EnterAutoSourceTipResp autoSourceEnterTip(EnterAutoSourceTipReq req) {
        EnterAutoSourceTipResp resp = new EnterAutoSourceTipResp();
        SceneInfoEntityDO sceneDO = apiTestRepository.queryLatestSceneInfo(req.getSceneCode(), req.getStatus());
        if (null == sceneDO) {
            resp.setTip(AutoSourceEnterTipEnum.NOT_SAVED);
            return resp;
        }
        if (StringUtils.isNotEmpty(sceneDO.getStepRecord())) {
            JSONObject stepRecord = JSON.parseObject(sceneDO.getStepRecord());
            if (null == stepRecord) {
                resp.setTip(AutoSourceEnterTipEnum.NOT_SAVED);
                return resp;
            }
            if (AutomaticStatusEnum.SUCCESS.name().equals(stepRecord.get("result")) && null != stepRecord.getString("AutomaticSourceRecord")) {
                resp.setTip(AutoSourceEnterTipEnum.ENTER_SOURCE);
                return resp;
            }
        }
        resp.setTip(AutoSourceEnterTipEnum.NEED_UPDATE);
        return resp;
    }

    public SourceCurrentStatusResp queryCurrentStatus(SourceCurrentStatusReq req) {
        SourceCurrentStatusResp resp = new SourceCurrentStatusResp();
        SceneInfoEntityDO sceneDO = apiTestRepository.queryLatestSceneInfo(req.getSceneCode(), req.getStatus());
        if (null == sceneDO) {
            return resp;
        }
        if (StringUtils.isBlank(sceneDO.getStepRecord())) {
            if (UseCaseFactoryTypeEnum.CREATE.getCode() == sceneDO.getSceneType() && SceneInfoEnableEnum.PUBLISHED.equals(sceneDO.getEnable())) {
                resp.setSourceStatus(AutomaticStatusEnum.SUCCESS);
                resp.setAutomaticSourceCode(sceneDO.getAutomaticSourceCode());
            }
            return resp;
        }
        JSONObject stepJson = JSON.parseObject(sceneDO.getStepRecord());
        Map<String, Object> stepMap = stepJson.getInnerMap();
        if (null != stepMap && null != stepMap.get("result")) {
            if (AutomaticStatusEnum.IN_PROGRESS.name().equals(stepMap.get("result").toString())) {
                resp.setSourceStatus(AutomaticStatusEnum.IN_PROGRESS);
            }
            if (AutomaticStatusEnum.FAIL.name().equals(stepMap.get("result").toString())) {
                resp.setSourceStatus(AutomaticStatusEnum.FAIL);
                resp.setFailReason(null == stepMap.get("message") ? "" : stepMap.get("message").toString());
            }
            if (AutomaticStatusEnum.SUCCESS.name().equals(stepMap.get("result").toString())) {
                if (null != stepMap.get(SceneInfoStepEnum.AutomaticSourceRecord.name()) || null != stepMap.get(SceneInfoStepEnum.Publish.name())) {
                    resp.setSourceStatus(AutomaticStatusEnum.SUCCESS);
                    resp.setAutomaticSourceCode(sceneDO.getAutomaticSourceCode());
                }
            }
        }
        return resp;
    }

    public PageInfo<PageApiInfoResp> queryPageApiInfo(PageApiInfoReq req) {
        PageInfo<ApiSampleCaseVO> apiTestList = apiTestRepository.queryPageApiTest(req);
        PageInfo<PageApiInfoResp> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(apiTestList.getPageNum());
        pageInfo.setPageSize(apiTestList.getPageSize());
        pageInfo.setTotal(apiTestList.getTotal());
        if (CollectionUtil.isEmpty(apiTestList.getList())) {
            return pageInfo;
        }
        List<PageApiInfoResp> respList = apiTestList.getList().stream().map(apiTest -> {
            PageApiInfoResp resp = apiTestDomainConverter.convert(apiTest);
            GenerateApiCaseUtil.convertPageApiInfoResp(apiTest, resp);
            return resp;
        }).collect(Collectors.toList());
        pageInfo.setList(respList);
        return pageInfo;
    }

    public PageAutomaticRecordLiteInfoVO queryAutomaticRecordLiteInfoPage(PageAutomaticRecordLiteInfoReq req) {
        if (StringUtils.isBlank(req.getProductCode())) {
            return PageAutomaticRecordLiteInfoVO.buildSelf(new ArrayList<>(), 0L);
        }
        Page<Object> page = PageHelper.startPage(req.getPage(), req.getSize());
        List<AutomaticRecordLiteInfoVO> list = automaticSourceRecordRepository.pageLiteInfoByProductCode(req.getProductCode(), req.getNameOrCode());
        return PageAutomaticRecordLiteInfoVO.buildSelf(list, page.getTotal());
    }

    public CheckDisableSceneResp querySceneRelatedPlanAndTask(String sceneCode) {
        CheckDisableSceneResp resp = new CheckDisableSceneResp();
        SceneInfoEntityDO sceneInfo = apiTestRepository.queryLatestSceneInfo(sceneCode, SceneInfoStatusEnum.publish);
        if (null != sceneInfo && StringUtils.isNotEmpty(sceneInfo.getAutomaticSourceCode())) {
            resp.setPlanNameList(automaticSourceRecordRepository.queryRelatedPlanName(sceneInfo.getAutomaticSourceCode()));
            resp.setTaskNameList(automaticSourceRecordRepository.queryRelatedTaskName(sceneInfo.getAutomaticSourceCode()));
        }
        return resp;
    }

    public JSONArray queryProductDb(QueryProductDbReq req) {
        JSONArray dbResult = zbaseService.queryProductDb(req.getProductCode());
        if (null != req.getOnlyProduct() && req.getOnlyProduct()) {
            return null == dbResult ? new JSONArray() : dbResult;
        }
        List<SceneDatabaseAuthorizeEntityDO> list = apiTestRepository.queryAuthorizeInfo(req.getProductCode());
        JSONArray authorizeArray = null != dbResult ? dbResult : new JSONArray();
        if (CollectionUtil.isNotEmpty(list)) {
            list.stream().distinct().forEach(item -> {
                JSONObject object = new JSONObject();
                object.put("id", item.getDbId());
                object.put("dbName", item.getDbName());
                object.put("physicDbName", item.getPhysicDbName());
                authorizeArray.add(object);
            });
        }
        return authorizeArray;
    }

    public ApiDebugLogResp queryApiDebugLog(String taskId) {
        return apiDebugLogService.queryApiDebugLog(taskId);
    }

    public void pushApiDebugLog(ApiDebugLogPushReq req) {
        if (StringUtils.isNotBlank(req.getContent())) {
            apiDebugLogService.setApiDebugLog(req.getTaskId(), req.getContent(), false);
        }
    }

    public String getPlainVariable(String variableCode) {
        try {
            ApiTestVariableVO vo = apiTestRepository.getApiTestVariableByVariableCode(variableCode);
            if (null != vo) {
                byte[] decodedBytes = Base64.getDecoder().decode(vo.getVariableValue());
                String decodedString = new String(decodedBytes);
                return decodedString;
            }
            return "";
        } catch (Exception e) {
            throw new ServiceException("查看密钥/密码数据异常，请重新保存");
        }
    }

    public QueryApiMockUrlResp queryApiMockUrl(String apiCode) {
        QueryApiMockUrlResp resp = new QueryApiMockUrlResp();
        ApiTestEntityDO apiTest = apiTestRepository.queryLatestApiTestByMainCode(apiCode);
        if (null == apiTest || StringUtils.isEmpty(apiTest.getApiData())) {
            return resp;
        }
        JSONObject apiData = JSONObject.parseObject(apiTest.getApiData());
        String mockUrl = apiData.getString("mockUrl");
        if (StringUtils.isEmpty(mockUrl) && null != apiTest.getDocId()) {
            MetaDataDocHttpVO doc = metaDataService.getMetaDataDocDetail(apiTest.getDocId());
            if (null != doc) {
                mockUrl = doc.getMockUrl();
            }
        }
        String completeUrl = GenerateApiCaseUtil.completeUrl(qcConfigBasicService.getApiMockBaseUrl(), mockUrl);
        try {
            URL url = new URL(completeUrl);
            resp.setMockProtocol(url.getProtocol());
            resp.setMockServerNameOrIp(url.getHost());
            resp.setMockPathUrl(url.getPath());
            resp.setMockPortNumber(String.valueOf(url.getPort()));
        } catch (Exception e) {
            log.error("解析url失败！", e);
            throw new ServiceException("解析url失败！" + completeUrl);
        }
        return resp;
    }

    public List<SceneModuleQueryResp> listSceneModule(SceneModuleQueryReq req) {
        List<SceneIndexVO> list = apiTestRepository.queryAllSceneModule(req);
        if (CollectionUtils.isEmpty(list)) {
            SceneModuleQueryVO moduleQueryVO = new SceneModuleQueryVO();
            moduleQueryVO.setId(0L);
            moduleQueryVO.setCode("ALL");
            if (StringUtils.isNotEmpty(req.getSceneType()) && req.getSceneType().equals(UseCaseFactoryTypeEnum.CREATE.name())) {
                moduleQueryVO.setName("全部造数");
            } else {
                moduleQueryVO.setName("全部场景");
            }
            moduleQueryVO.setType(SceneIndexTypeEnum.MODULE);
            moduleQueryVO.setParentCode("");
            moduleQueryVO.setChildren(new ArrayList<>());
            moduleQueryVO.setIsLeaf(true);
            moduleQueryVO.setSceneCount(0);
            return apiTestDomainConverter.converterList(Collections.singletonList(moduleQueryVO));
        }
        Map<String, List<SceneIndexVO>> moduleMap = list.stream().collect(Collectors.groupingBy(SceneIndexVO::getParentCode));
        List<SceneIndexVO> allSceneModule = new ArrayList<>();
        if (StringUtils.isBlank(req.getParentCode())) {
            SceneIndexVO indexVO = new SceneIndexVO();
            indexVO.setId(0L);
            indexVO.setSceneIndexCode("ALL");
            if (StringUtils.isNotEmpty(req.getSceneType()) && req.getSceneType().equals(UseCaseFactoryTypeEnum.CREATE.name())) {
                indexVO.setSceneIndexName("全部造数");
            } else {
                indexVO.setSceneIndexName("全部场景");
            }
            indexVO.setSceneIndexType(SceneIndexTypeEnum.MODULE);
            indexVO.setParentCode("");
            allSceneModule = Collections.singletonList(indexVO);
        } else {
            allSceneModule = null == moduleMap.get(req.getParentCode()) ? new ArrayList<>() : moduleMap.get(req.getParentCode());
        }
        boolean hasProductMemberPermission = productMembersPermission(req.getCurrentUserId(), req.getProductCode(), req.getPermissions());
        SceneModuleIteratorVO children = detailModuleIterator(moduleMap, allSceneModule, req, hasProductMemberPermission);
        List<SceneModuleQueryVO> childrenList = children.getList();
        if (null != req.getParentCode()) {
            childrenList.stream().forEach(vo -> vo.setChildren(new ArrayList<>()));
        }
        return apiTestDomainConverter.converterList(childrenList);
    }

    public Set<Long> queryAuthorizedUsers(QueryAuthorizedUsersReq req, List<String> memberTypes) {
        List<ProductMemberVO> productMembers = null;
        try {
            productMembers = productRpcService.findProductMemberByIdQuery(req.getProductCode(), memberTypes);
        } catch (Exception e) {
            log.error("ListProductMemberByPIdQuery ERROR", e);
        }
        if (CollectionUtils.isEmpty(productMembers)) {
            return new HashSet<>();
        }
        Set<Long> members = productMembers.stream().map(ProductMemberVO::getUserId).collect(Collectors.toSet());
        return members;
    }

    private SceneModuleIteratorVO detailModuleIterator(Map<String, List<SceneIndexVO>> moduleMap,
                                                       List<SceneIndexVO> list,
                                                       SceneModuleQueryReq req,
                                                       boolean hasProductMemberPermission) {
        SceneModuleIteratorVO iterator = new SceneModuleIteratorVO();
        List<SceneModuleQueryVO> moduleList = new ArrayList<>();
        for (SceneIndexVO indexVO : list) {
            SceneModuleQueryVO moduleQueryVO = new SceneModuleQueryVO();
            moduleQueryVO.setId(indexVO.getId());
            moduleQueryVO.setCode(indexVO.getSceneIndexCode());
            moduleQueryVO.setName(indexVO.getSceneIndexName());
            moduleQueryVO.setType(indexVO.getSceneIndexType());
            moduleQueryVO.setDesc(indexVO.getSceneInfoDesc());
            moduleQueryVO.setParentCode(indexVO.getParentCode());
            moduleQueryVO.setGmtCreate(indexVO.getGmtCreate());
            moduleQueryVO.setGmtModified(indexVO.getGmtModified());
            moduleQueryVO.setSceneEnable(indexVO.getSceneEnable());
            moduleQueryVO.setShareStatus(indexVO.getShareStatus());
            if (indexVO.getSceneIndexType().equals(SceneIndexTypeEnum.SCENE) && StringUtils.isNotEmpty(req.getSceneType()) && req.getSceneType().equals(UseCaseFactoryTypeEnum.CREATE.name())) {
                moduleQueryVO.setPermissionList(getPermissionInfo(req.getCurrentUserId(), indexVO.getCreatorId(), req.getPermissions(), hasProductMemberPermission));
            }
            //获取非请求code的下级分组
            if (null != moduleMap.get(indexVO.getSceneIndexCode()) && (StringUtils.isBlank(req.getCode()) || !req.getCode().equals(indexVO.getSceneIndexCode()))) {
                SceneModuleIteratorVO children = detailModuleIterator(moduleMap, moduleMap.get(indexVO.getSceneIndexCode()), req, hasProductMemberPermission);
                moduleQueryVO.setChildren(children.getList());
                setSceneCount(moduleQueryVO, children.getList());
                if (StringUtils.isNotBlank(req.getNameOrGroup())) {
                    moduleQueryVO.setIsDisplay(children.getIsDisplay() || moduleQueryVO.getName().contains(req.getNameOrGroup()));
                }
            } else {
                //无下级菜单 分组 / 数据
                if (indexVO.getSceneIndexType().equals(SceneIndexTypeEnum.MODULE) && CollectionUtil.isNotEmpty(req.getStatus()) && StringUtils.isNotEmpty(req.getSceneType()) && req.getSceneType().equals(UseCaseFactoryTypeEnum.CREATE.name())) {
                    continue;
                }
                moduleQueryVO.setChildren(new ArrayList<>());
                moduleQueryVO.setSceneCount(0);
                if (StringUtils.isNotBlank(req.getNameOrGroup())) {
                    moduleQueryVO.setIsDisplay(StringUtils.isNotEmpty(moduleQueryVO.getName()) && moduleQueryVO.getName().contains(req.getNameOrGroup()));
                }
            }
            moduleQueryVO.setIsLeaf(CollectionUtils.isEmpty(moduleQueryVO.getChildren()));
            moduleList.add(moduleQueryVO);
        }
        if (StringUtils.isNotBlank(req.getNameOrGroup())) {
            moduleList = moduleList.stream().filter(SceneModuleQueryVO::getIsDisplay).collect(Collectors.toList());
        }
        iterator.setList(sortSceneModuleVOList(moduleList));
        iterator.setIsDisplay(!CollectionUtils.isEmpty(moduleList));
        return iterator;
    }

    private List<SceneModuleQueryVO> sortSceneModuleVOList(List<SceneModuleQueryVO> moduleList) {
        if (!CollectionUtils.isEmpty(moduleList)) {
            List<SceneModuleQueryVO> moduleVOS = moduleList.stream().filter(vo -> SceneIndexTypeEnum.MODULE.equals(vo.getType())).sorted(Comparator.comparing(SceneModuleQueryVO::getGmtModified, Comparator.nullsFirst(Comparator.reverseOrder()))).collect(Collectors.toList());
            List<SceneModuleQueryVO> sceneVOS = moduleList.stream().filter(vo -> SceneIndexTypeEnum.SCENE.equals(vo.getType())).sorted(Comparator.comparing(SceneModuleQueryVO::getGmtModified, Comparator.nullsFirst(Comparator.reverseOrder()))).collect(Collectors.toList());
            moduleVOS.addAll(sceneVOS);
            moduleList = moduleVOS;
        }
        return moduleList;
    }

    private void setSceneCount(SceneModuleQueryVO moduleQueryVO, List<SceneModuleQueryVO> children) {
        moduleQueryVO.setSceneCount(0);
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        children.stream().forEach(vo -> {
            if (SceneIndexTypeEnum.SCENE.equals(vo.getType())) {
                moduleQueryVO.setSceneCount(moduleQueryVO.getSceneCount() + 1);
            } else {
                moduleQueryVO.setSceneCount(moduleQueryVO.getSceneCount() + vo.getSceneCount());
            }
        });
    }

    private Map<String, List<Button>> getQuerySceneInfoPagePermissionInfo(SceneInfoEntityDO sceneInfoEntityDO, PageSceneInfoReq req, Boolean flag) {
        Map<String, List<Button>> result = new HashMap<>(2);
        List<Button> buttonList = new ArrayList<>();
        List<Button> collapseButtonList = new ArrayList<>();
        if (sceneInfoEntityDO.getSceneType().equals(UseCaseFactoryTypeEnum.SCENE.getCode())) {
            return Collections.emptyMap();
        }
        boolean isSuperUser = checkSuperUser(new HashSet<>(req.getPermissions()));
        boolean isProductTestMember = productTestMembersPermission(req.getCurrentUserId(), req.getProductCode());
        boolean isCreator = sceneInfoEntityDO.getCreatorId().equals(req.getCurrentUserId());

        switch (sceneInfoEntityDO.getEnable().getCode()) {
            case 1:
                buttonList.add(new Button("运行", "execute", 1));
                if (!flag && (isProductTestMember || isSuperUser || isCreator)) {  //创建人+产品下的四个负责人+超管权限 + 测试人员
                    buttonList.add(new Button("编辑", "edit", 2));
                    buttonList.add(new Button("发布", "publish", 3));
                    collapseButtonList.add(new Button("移动", "move", 4));
                    collapseButtonList.add(new Button("复制", "copy", 7));
                    collapseButtonList.add(new Button("删除", "delete", 8));
                }
                break;
            case 2:
                buttonList.add(new Button("运行", "execute", 1));
                if (flag && isSuperUser) {  //共享产品超管只有buttonList
                    buttonList.add(new Button("编辑", "edit", 2));
                    buttonList.add(new Button("发布", "publish", 3));
                    if (!sceneInfoEntityDO.getShareStatus()) {
                        buttonList.add(new Button("共享", "share", 9));
                    } else {
                        buttonList.add(new Button("取消共享", "unshare", 10));
                    }
                } else if (isSuperUser || isProductTestMember || isCreator) {
                    buttonList.add(new Button("编辑", "edit", 2));
                    buttonList.add(new Button("发布", "publish", 3));
                    collapseButtonList.add(new Button("移动", "move", 4));
                    collapseButtonList.add(new Button("停用", "stop", 5));
                    collapseButtonList.add(new Button("复制", "copy", 7));
                    collapseButtonList.add(new Button("删除", "delete", 8));
                    if (!sceneInfoEntityDO.getShareStatus()) {
                        buttonList.add(new Button("共享", "share", 9));
                    } else {
                        buttonList.add(new Button("取消共享", "unshare", 10));
                    }
                }
                break;
            case 3:
                if (!flag) {
                    if (isCreator || isSuperUser || isProductTestMember) {
                        collapseButtonList.add(new Button("移动", "move", 4));
                        collapseButtonList.add(new Button("启用", "start", 6));
                        collapseButtonList.add(new Button("复制", "copy", 7));
                        collapseButtonList.add(new Button("删除", "delete", 8));
                    }
                }
                break;
            default:
                return Collections.emptyMap();
        }
        if (CollectionUtil.isNotEmpty(buttonList)) {
            result.put("BUTTON", buttonList.stream().sorted(Comparator.comparing(Button::getIndex)).collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(collapseButtonList)) {
            result.put("COLLAPSE_BUTTON", collapseButtonList.stream().sorted(Comparator.comparing(Button::getIndex)).collect(Collectors.toList()));
        }
        return result;
    }

    private List<Button> getPermissionInfo(Long userId,
                                           Long creatorId,
                                           List<String> permissions,
                                           boolean hasMemberPermission) {
        List<Button> result = new ArrayList<>();
        if (null == userId) {
            return Collections.emptyList();
        }
        if (creatorAndSuperManagerPermission(userId, creatorId, permissions)) {
            result.add(new Button("编辑", "edit", 1));
            result.add(new Button("删除", "delete", 2));
        }
        if (hasMemberPermission) {
            result.add(new Button("复制", "copy", 3));
        }
        return result;
    }

    /**
     * 创建人与超级管理员
     *
     * @param userId
     * @param creatorId
     * @return
     */
    public boolean creatorAndSuperManagerPermission(Long userId, Long creatorId, List<String> permissions) {
        if (null == userId) {
            return false;
        }
        if (userId.equals(creatorId) || checkSuperUser(new HashSet<>(permissions))) {
            return true;
        }
        return false;
    }

    /**
     * 产品下的测试成员权限
     *
     * @param userId
     * @param productCode
     * @return
     */
    public boolean productTestMembersPermission(Long userId, String productCode) {
        List<String> memberTypes = Arrays.asList(
                MemberTypeEnum.TESTER_OWNER.name(),
                MemberTypeEnum.PRODUCTER_OWNER.name(),
                MemberTypeEnum.DEVELOPER_OWNER.name(),
                MemberTypeEnum.ARCHITECT.name(),
                MemberTypeEnum.PROJECTER_OWNER.name(),
                MemberTypeEnum.TESTER_M.name(),
                MemberTypeEnum.TESTER.name());
        List<ProductMemberVO> list = productRpcService.findProductMemberByIdQuery(productCode, memberTypes);
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        list = list.stream().filter(t -> t.getUserId().equals(userId)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        return true;
    }


    /**
     * 产品下的成员与超级管理员权限
     *
     * @param userId
     * @param productCode
     * @return
     */
    private boolean productMembersPermission(Long userId, String productCode, List<String> permissions) {
        if (null == userId) {
            return false;
        }
        // 超管校验
        if (checkSuperUser(new HashSet<>(permissions))) {
            return true;
        }
        List<ProductMemberVO> list = productRpcService.findProductMemberByIdQuery(productCode, null);
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        list = list.stream().filter(t -> t.getUserId().equals(userId)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        return true;
    }

    /**
     * 超管权限校验
     *
     * @param userId
     * @param productCode
     * @return
     */
    /**
     * 超级管理员标识
     */
    private static final String SUPPER_USER_PERMISSION_KEY = "globalpermCanWriteProd";

    public static boolean checkSuperUser(Set<String> permissions) {
        try {
            if (CollectionUtil.isNotEmpty(permissions)) {
                return permissions.contains(SUPPER_USER_PERMISSION_KEY);
            }
        } catch (Exception e) {
            log.error("判定超管出错", e);
        }
        return false;
    }


    public DeleteSceneModuleTipsResp queryDeleteSceneModuleTips(String code) {
        SceneIndexVO sceneIndex = apiTestRepository.querySceneIndexByCode(code);
        if (null == sceneIndex || !sceneIndex.getEnable() || !SceneIndexTypeEnum.MODULE.equals(sceneIndex.getSceneIndexType())) {
            return null;
        }
        List<SceneIndexVO> list = apiTestRepository.querySceneIndexModuleInfo(sceneIndex.getProductCode());
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        DeleteSceneModuleTipsResp resp = new DeleteSceneModuleTipsResp().setCode(code).setName(sceneIndex.getSceneIndexName()).setChildModuleNum(0).setSceneNum(0).setPublishedSceneNum(0);
        Map<String, List<SceneIndexVO>> map = list.stream().collect(Collectors.groupingBy(SceneIndexVO::getParentCode));
        countSceneModuleNum(code, map, resp);
        return resp;
    }

    private void countSceneModuleNum(String code, Map<String, List<SceneIndexVO>> map, DeleteSceneModuleTipsResp resp) {
        if (CollectionUtil.isEmpty(map.get(code))) {
            return;
        }
        long childCount = map.get(code).stream().filter(sceneIndex -> SceneIndexTypeEnum.MODULE.equals(sceneIndex.getSceneIndexType())).count();
        long sceneCount = map.get(code).stream().filter(sceneIndex -> SceneIndexTypeEnum.SCENE.equals(sceneIndex.getSceneIndexType())).count();
        long publishSceneCount = map.get(code).stream().filter(sceneIndex -> SceneIndexTypeEnum.SCENE.equals(sceneIndex.getSceneIndexType()) && SceneInfoEnableEnum.PUBLISHED.equals(sceneIndex.getSceneInfoEnable())).count();
        resp.addChildModuleNum((int) childCount);
        resp.addSceneNum((int) sceneCount);
        resp.addPublishedSceneNum((int) publishSceneCount);
        map.get(code).stream().filter(sceneIndex -> SceneIndexTypeEnum.MODULE.equals(sceneIndex.getSceneIndexType())).forEach(sceneIndex -> countSceneModuleNum(sceneIndex.getSceneIndexCode(), map, resp));
    }

    public List<BaseApiTestVariableResp> querySceneVariable(QuerySceneVariableReq req) {
        List<ApiTestVariableVO> list = apiTestRepository.querySceneVariable(req.getProductCode(), req.getLinkCode(), req.getType(), SubVariableTypeEnum.valueOf(req.getSubVariableType()), Arrays.asList(VariableUsageTypeEnum.UNKNOWN));
        return apiTestDomainConverter.converter(list);
    }

    public List<SharedSceneModuleQueryResp> listSharedModule(SharedSceneModuleQueryReq req) {
        req.setShareProductFlag(true);
        if (StringUtils.isNotEmpty(req.getSearch())) {
            req.setParentCode(null);
        }

        List<SceneCollectionEntityDO> sceneCollectionEntityDOS = tmSceneCollectionRepository.selectFavoritesPreData(req.getCurrentUserId());
        List<SceneIndexVO> preDataList = apiTestRepository.querySharedPreData(req);

        Set<String> sceneIndexCodes = sceneCollectionEntityDOS.stream()
                .map(SceneCollectionEntityDO::getSceneIndexCode)
                .collect(Collectors.toSet());

        //我的收藏
        if (PreDataTagEnum.MY_COLLECT.equals(req.getPreDataTagType())) {
            preDataList = preDataList.stream()
                    .filter(sceneIndex -> sceneIndexCodes.contains(sceneIndex.getSceneIndexCode()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtil.isEmpty(preDataList)) {
            return Collections.emptyList();
        }

        List<String> productList = preDataList.stream().map(SceneIndexVO::getProductCode).distinct().collect(Collectors.toList());
        if ("ALL".equals(req.getParentCode())) {
            return getSharedPreDataProduct(productList, sceneCollectionEntityDOS);
        }

        if (CollectionUtil.isEmpty(productList)) {
            return Collections.emptyList();
        }

        List<SceneIndexVO> result = new ArrayList<>();
        Map<String, SceneIndexVO> sceneMap = new HashMap<>();

        List<SceneIndexVO> list = getAllModuleAndSceneByProductList(preDataList, productList, req);
        for (SceneIndexVO scene : list) {
            sceneMap.put(scene.getSceneIndexCode(), scene);
        }
        for (int i = sceneCollectionEntityDOS.size() - 1; i >= 0; i--) {
            SceneCollectionEntityDO entity = sceneCollectionEntityDOS.get(i);
            String sceneIndexCode = entity.getSceneIndexCode();
            SceneIndexVO foundScene = sceneMap.get(sceneIndexCode);
            if (foundScene != null) {
                foundScene.setIsCollect(true);
                result.add(foundScene);
                sceneMap.remove(sceneIndexCode);
            }
        }
        for (SceneIndexVO scene : sceneMap.values()) {
            scene.setIsCollect(false);
        }
        result.addAll(sceneMap.values());
        if (StringUtils.isEmpty(req.getSearch())) {
            return result.stream().filter(vo -> req.getParentCode().equals(vo.getParentCode())).map(this::convertSharedSceneModuleQueryResp).collect(Collectors.toList());
        }
        productList = result.stream().map(SceneIndexVO::getProductCode).distinct().collect(Collectors.toList());
        List<SharedSceneModuleQueryResp> respList = getSharedPreDataProduct(productList, sceneCollectionEntityDOS);
        result.forEach(vo -> respList.add(convertSharedSceneModuleQueryResp(vo)));
        respList.stream().filter(resp -> SceneIndexTypeEnum.MODULE.equals(resp.getType())).forEach(module -> respList.stream().filter(child -> module.getCode().equals(child.getParentCode())).forEach(module::addChild));
        return respList.stream().filter(resp -> resp.getProductCode().equals(resp.getCode())).collect(Collectors.toList());
    }


    private List<SceneIndexVO> getAllModuleAndSceneByProductList(List<SceneIndexVO> preDataList, List<String> productList, SharedSceneModuleQueryReq req) {
        List<SceneIndexVO> list = new ArrayList<>();
        List<SceneIndexVO> children = new ArrayList<>();
        List<SceneIndexVO> moduleList = apiTestRepository.querySharedPreDataModuleByProduct(productList);
        if (StringUtils.isNotEmpty(req.getSearch())) {
            moduleList.stream().filter(vo -> vo.getSceneIndexName().contains(req.getSearch())).forEach(vo -> {
                if (vo.getProductCode().equals(vo.getParentCode())) {
                    list.add(vo);
                } else {
                    children.add(vo);
                }
            });
            preDataList.removeIf(vo -> !vo.getSceneIndexName().contains(req.getSearch()));
        }
        children.addAll(preDataList);
        if (CollectionUtil.isEmpty(children) && CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        this.filterSharedPreDataModule(moduleList, children, list);
        list.addAll(preDataList);
        return list;
    }

    private void filterSharedPreDataModule(List<SceneIndexVO> moduleList, List<SceneIndexVO> children, List<SceneIndexVO> list) {
        List<SceneIndexVO> parentList = moduleList.stream().filter(module -> children.stream().anyMatch(child -> module.getSceneIndexCode().equals(child.getParentCode()))).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(parentList)) {
            return;
        }
        parentList.stream().filter(vo -> !list.contains(vo)).forEach(list::add);
        this.filterSharedPreDataModule(moduleList, parentList, list);
    }

    private List<SharedSceneModuleQueryResp> getSharedPreDataProduct(List<String> productList, List<SceneCollectionEntityDO> sceneCollectionEntityDOS) {
        if (CollectionUtil.isEmpty(productList)) {
            return Collections.emptyList();
        }
        SimpleProductQueryListVO productQueryList = productRpcService.simpleProductQueryList(productList);
        if (null == productQueryList || CollectionUtil.isEmpty(productQueryList.getSimpleQueryVOlist())) {
            throw new ServiceException("获取产品域查询失败！");
        }
        Set<String> sceneCollectionProductCodes = sceneCollectionEntityDOS.stream()
                .map(SceneCollectionEntityDO::getProductCode)
                .collect(Collectors.toSet());
        return productQueryList.getSimpleQueryVOlist().stream()
                .map(product -> {
                    SharedSceneModuleQueryResp resp = new SharedSceneModuleQueryResp();
                    resp.setCode(product.getProductCode());
                    resp.setProductCode(product.getProductCode());
                    resp.setName(product.getProductName());
                    resp.setType(SceneIndexTypeEnum.MODULE);
                    resp.setIsLeaf(false);
                    resp.setIsCollect(CollectionUtil.isNotEmpty(sceneCollectionProductCodes) && sceneCollectionProductCodes.contains(product.getProductCode()));
                    return resp;
                })
                .sorted((o1, o2) -> {
                    boolean isCollect1 = o1.getIsCollect();
                    boolean isCollect2 = o2.getIsCollect();
                    if (isCollect1 && !isCollect2) {
                        return -1; // 匹配的产品排在前面
                    } else if (!isCollect1 && isCollect2) {
                        return 1; // 非匹配的产品排在后面
                    } else {
                        return 0; // 保持原顺序
                    }
                })
                .collect(Collectors.toList());
    }

    private SharedSceneModuleQueryResp convertSharedSceneModuleQueryResp(SceneIndexVO vo) {
        SharedSceneModuleQueryResp resp = new SharedSceneModuleQueryResp();
        resp.setCode(vo.getSceneIndexCode());
        resp.setName(vo.getSceneIndexName());
        resp.setProductCode(vo.getProductCode());
        resp.setType(vo.getSceneIndexType());
        resp.setParentCode(vo.getParentCode());
        resp.setIsLeaf(SceneIndexTypeEnum.SCENE.equals(vo.getSceneIndexType()));
        resp.setIsCollect(vo.getIsCollect());
        return resp;
    }

    public List<ListRedisQueryResp> listRedis(listRedisQueryReq req) {
        List<ListRedisQueryVO> voList = zbaseService.queryRedisList(req.getProductCode(), req.getSearch());
        return ListRedisQueryResp.buildList(voList);
    }

    public Map<String, Map<String, String>> listRedisConfig(String productCode, Set<String> clusterIdList) {
        log.info("listRedisConfig_productCode:{}_clusterIdList:{}", productCode, clusterIdList);
        if (CollectionUtil.isEmpty(clusterIdList)) {
            return new HashMap<>();
        }
        List<ListRedisQueryVO> voList = zbaseService.queryRedisList(productCode, Strings.EMPTY);
        return ListRedisQueryVO.buildConfigMap(clusterIdList, voList);
    }

    public List<ListESQueryResp> listES(listESQueryReq req) {
        log.info("listES_req:{}", JsonUtil.toJSON(req));
        List<ListESQueryVO> voList = zbaseService.queryESList(req);
        return ListESQueryResp.buildList(voList);
    }

    public PageInfo<PageMessageTopicResp> queryPageMessageTopic(PageMessageTopicReq req) {
        TopicSimpleQuery query = new TopicSimpleQuery();
        query.setCurrentPage(req.getPage());
        query.setPageSize(req.getSize());
        query.setName(req.getSearch());
        if (StringUtils.isNotEmpty(req.getProductCode())) {
            String productName = metaDataService.getMetaDataProductCode(req.getProductCode());
            query.setProductName(productName);
        }
        PageInfo<TopicSimpleVO> result = zbaseService.topicSimpleQuery(query);
        if (null == result) {
            return new PageInfo<>();
        }
        PageInfo<PageMessageTopicResp> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(result.getPageNum());
        pageInfo.setPageSize(result.getPageSize());
        pageInfo.setTotal(result.getTotal());
        if (CollectionUtil.isNotEmpty(result.getList())) {
            List<PageMessageTopicResp> list = result.getList().stream().map(vo -> {
                PageMessageTopicResp resp = new PageMessageTopicResp();
                resp.setName(vo.getName());
                resp.setMemo(vo.getMemo());
                resp.setAppId(vo.getAppId());
                resp.setProductName(vo.getProductName());
                resp.setZbaseProductCode(vo.getProductCode());
                return resp;
            }).collect(Collectors.toList());
            pageInfo.setList(list);
        }
        return pageInfo;
    }

    public boolean hasProductAuth(String code, User userInfo) {
        if (null == userInfo) {
            return false;
        }
        // 超管校验
        if (checkSuperUser(userInfo, code)) {
            return true;
        }
        List<ProductMemberVO> list = productRpcService.findProductMemberByIdQuery(code, null);
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        list = list.stream().filter(t -> t.getUserId().equals(userInfo.getUserId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        return true;
    }

    public boolean isProductOwner(String productCode, User userInfo) {
        if (null == userInfo) {
            return false;
        }
        List<String> memberTypes = Arrays.asList(MemberTypeEnum.TESTER_OWNER.name(), MemberTypeEnum.DEVELOPER_OWNER.name(),
                MemberTypeEnum.ARCHITECT.name(), MemberTypeEnum.PRODUCTER_OWNER.name(), MemberTypeEnum.PROJECTER_OWNER.name());
        List<ProductMemberVO> list = productRpcService.findProductMemberByIdQuery(productCode, memberTypes);
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        list = list.stream().filter(t -> t.getUserId().equals(userInfo.getUserId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        return true;
    }

    private Boolean checkSuperUser(User userInfo, String productCode) {
        ListProductMemberByPIdQuery productQuery = new ListProductMemberByPIdQuery();
        Long userId = userInfo.getUserId();
        List<String> memberTypes = Arrays.asList(MemberTypeEnum.SUPPER.name());
        productQuery.setProductCode(productCode);
        productQuery.setMemberTypes(memberTypes);
        return productRpcService.checkProductPermission(userId, productQuery);
    }

    public PageInfo<PageApiLiteResp> queryPageApiLite(PageApiLiteReq req) {
//        if (StringUtils.isNotEmpty(req.getNameOrAddress())) {
//            req.setNameOrAddress(req.getNameOrAddress().replaceAll("/", "//").replaceAll("%", "/%").replaceAll("_", "/_"));
//        }
        return apiTestRepository.queryPageSceneApi(req);
    }

    public QueryAuthorizeListResp queryAuthorizeList(QueryAuthorizeListReq req, User user) {
        log.info("查询数据库授权列表，QueryAuthorizeListReq：{}", JSON.toJSONString(req));
        QueryAuthorizeListResp resp = new QueryAuthorizeListResp();
        // 校验权限
        resp.setHasPermission(this.isProductOwner(req.getProductCode(), user));
        List<SceneDatabaseAuthorizeEntityDO> list = apiTestRepository.queryAuthorizeList(req.getProductCode());
        if (CollectionUtils.isEmpty(list)) {
            resp.setList(new ArrayList<>());
            return resp;
        }
        List<QueryAuthorizeListVO> resultList = list.stream().map(item -> {
            QueryAuthorizeListVO vo = new QueryAuthorizeListVO();
            vo.setAuthorizeProductCode(item.getAuthorizeProductCode());
            vo.setAuthorizeProductName(item.getAuthorizeProductName());
            vo.setDatabaseNames(item.getDbName());
            return vo;
        }).distinct().collect(Collectors.toList());
        resp.setList(resultList);
        return resp;
    }

    public QueryDebugLogResp queryDebugLog(QueryDebugLinkReq req) {
        QueryDebugLogResp resp = buildDebugLog(req);
        if (Objects.nonNull(resp)) {
            return resp;
        }
        boolean cacheFlag = updateCache(req.getProductCode(), req.getSceneCode());
        if (cacheFlag) {
            return buildDebugLog(req);
        }
        return new QueryDebugLogResp();
    }

    private QueryDebugLogResp buildDebugLog(QueryDebugLinkReq req) {
        String logStr = redisService.getKey(req.getProductCode() + "-" + req.getSceneCode() + "-log");
        if (StringUtils.isNotBlank(logStr)) {
            return QueryDebugLogResp.buildSelf(logStr);
        }
        return null;
    }

    public QueryDebugLinkResp queryDebugLink(QueryDebugLinkReq req) {
        QueryDebugLinkResp resp = buildDebugLink(req);
        if (Objects.nonNull(resp)) {
            return resp;
        }
        boolean cacheFlag = updateCache(req.getProductCode(), req.getSceneCode());
        if (cacheFlag) {
            return buildDebugLink(req);
        }
        return new QueryDebugLinkResp();
    }

    private QueryDebugLinkResp buildDebugLink(QueryDebugLinkReq req) {
        String linkStr = redisService.getKey(req.getProductCode() + "-" + req.getSceneCode() + "-link");
        if (StringUtils.isNotBlank(linkStr)) {
            return QueryDebugLinkResp.buildSelf(linkStr);
        }
        return null;
    }

    public QueryDebugNodeResultResp queryDebugNodeResult(QueryDebugNodeResultReq req) {
        QueryDebugNodeResultResp resp = buildDebugNodeResult(req);
        if (Objects.nonNull(resp)) {
            return resp;
        }
        boolean cacheFlag = updateCache(req.getProductCode(), req.getSceneCode());
        if (cacheFlag) {
            return buildDebugNodeResult(req);
        }
        return new QueryDebugNodeResultResp();
    }

    private QueryDebugNodeResultResp buildDebugNodeResult(QueryDebugNodeResultReq req) {
        String linkStr = redisService.getKey(req.getProductCode() + "-" + req.getSceneCode() + "-link");
        if (StringUtils.isNotBlank(linkStr)) {
            return QueryDebugNodeResultResp.buildSelf(req, linkStr);
        }
        return null;
    }

    public QueryApiGlobalConfigurationResp queryApiGlobalConfiguration(String productCode) {
        QueryApiGlobalConfigurationResp result = new QueryApiGlobalConfigurationResp();
        //todo 查询当前产品的全局配置
        List<ApiGlobalConfigurationEntityDO> apiGlobalConfigurationEntityDOList = getApiGlobalConfigurationEntityDOList(productCode);
        List<ApiGlobalConfigurationVO> apiGlobalConfigurationVOList = new ArrayList<>();
        if (CollectionUtil.isEmpty(apiGlobalConfigurationEntityDOList)) {
            for (ApiConfigTypeEnum apiConfigTypeEnum : ApiConfigTypeEnum.showList()) {
                ApiGlobalConfigurationVO apiGlobalConfigurationVO = new ApiGlobalConfigurationVO();
                apiGlobalConfigurationVO.setApiConfigType(apiConfigTypeEnum.getCode());
                apiGlobalConfigurationVO.setEnable(2);
                apiGlobalConfigurationVO.setApiConfigVOList(Collections.emptyList());
                apiGlobalConfigurationVOList.add(apiGlobalConfigurationVO);
            }
            result.setApiGlobalConfigurationVOList(apiGlobalConfigurationVOList);
            return result;
        }
        Map<Integer, List<ApiGlobalConfigurationEntityDO>> groupedData = apiGlobalConfigurationEntityDOList.stream().collect(Collectors.groupingBy(ApiGlobalConfigurationEntityDO::getApiConfigType, Collectors.collectingAndThen(Collectors.toList(), list -> {
            list.sort(Comparator.comparing(ApiGlobalConfigurationEntityDO::getGmtCreate));
            return Optional.ofNullable(list.isEmpty() ? null : list);
        }))).entrySet().stream().filter(entry -> entry.getValue().isPresent()).collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().get()));
        EnumSet<ApiConfigTypeEnum> allConfigTypes = ApiConfigTypeEnum.showList();

        for (ApiConfigTypeEnum configType : allConfigTypes) {
            boolean matched = false;
            for (Map.Entry<Integer, List<ApiGlobalConfigurationEntityDO>> entry : groupedData.entrySet()) {
                Integer key = entry.getKey();
                List<ApiGlobalConfigurationEntityDO> value = entry.getValue();
                if (configType.getCode() == key) {
                    ApiGlobalConfigurationVO apiGlobalConfigurationVO = new ApiGlobalConfigurationVO();
                    List<ApiConfigVO> apiConfigVOList = new ArrayList<>();
                    value.forEach(x -> {
                        ApiConfigVO apiConfigVO = new ApiConfigVO();
                        apiConfigVO.setApiConfigScope(x.getApiConfigScope());
                        apiConfigVO.setApiConfigValue(x.getApiConfigValue());
                        apiConfigVO.setAsserts(x.getApiConfigAssert());
                        apiConfigVOList.add(apiConfigVO);
                    });
                    apiGlobalConfigurationVO.setApiConfigType(key);
                    apiGlobalConfigurationVO.setEnable(value.get(0).getEnable());
                    apiGlobalConfigurationVO.setApiConfigVOList(apiConfigVOList);
                    apiGlobalConfigurationVOList.add(apiGlobalConfigurationVO);
                    matched = true;
                    break;
                }
            }
            if (!matched) {
                // 无法匹配的枚举值，创建空集合
                ApiGlobalConfigurationVO apiGlobalConfigurationVO = new ApiGlobalConfigurationVO();
                apiGlobalConfigurationVO.setApiConfigType(configType.getCode());
                apiGlobalConfigurationVO.setEnable(2);
                apiGlobalConfigurationVO.setApiConfigVOList(Collections.emptyList());
                apiGlobalConfigurationVOList.add(apiGlobalConfigurationVO);
            }
        }
        result.setApiGlobalConfigurationVOList(apiGlobalConfigurationVOList);
        return result;
    }

    private List<ApiGlobalConfigurationEntityDO> getApiGlobalConfigurationEntityDOList(String productCode) {
        // 查询全局配置数据列表
        return apiGlobalConfigurationRepository.selectApiGlobalConfigurationByProductCode(productCode);
    }


    private boolean updateCache(String productCode, String sceneCode) {
        //查数据库是否有记录
        TmSceneDebugRecordEntityDO entityDO = tmSceneDebugRecordRepository.selectOne(productCode, sceneCode);
        if (null == entityDO || StringUtils.isBlank(entityDO.getLogOssPath())) {
            log.error("updateCache_entity_is_null_sceneCOde:{}", sceneCode);
            return false;
        }
        //下载并缓存日志信息
        return downloadAndCachedData(entityDO);
    }

    private boolean downloadAndCachedData(TmSceneDebugRecordEntityDO entityDO) {
        try {
            String content = ossService.getObjectTextWithRange(entityDO.getBucketName(), entityDO.getLogOssPath());
            if (StringUtils.isBlank(content)) {
                log.error("downloadDebugLog_is_blank_recordCode :{}", entityDO.getRecordCode());
                return false;
            }
            DebugInfoVO debugInfoVO = JSONObject.parseObject(content, DebugInfoVO.class);
            //解析放入缓存
            redisService.setKey(entityDO.getProductCode() + "-" + entityDO.getSceneCode() + "-log", JSONObject.toJSONString(DebugLogVO.buildSelf(debugInfoVO.getStatus(), debugInfoVO.getDebugLog())), 120, TimeUnit.MINUTES);
            redisService.setKey(entityDO.getProductCode() + "-" + entityDO.getSceneCode() + "-link", JSONObject.toJSONString(debugInfoVO.getLinkBaseInfo()), 120, TimeUnit.MINUTES);
            return true;
        } catch (Exception e) {
            log.error("downloadDebugLog_error:{}", e.getMessage());
            return false;
        }
    }


    public ApiCaseExceptionResp queryApiCaseException(QueryApiCaseExceptionReq req) {
        String key = String.format("process::%s", req.getParentCaseCode());
        boolean processingFlag = ApiCaseStatusEnum.edit.equals(req.getStatus()) && redisService.hasKey(key) && StringUtils.isNotEmpty(redisService.getKey(key));
        if (processingFlag) {
            return ApiCaseExceptionResp.init(true);
        }
        PageApiExceptionCaseQuery query = apiTestDomainConverter.convert(req);
        Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        List<ApiCaseExceptionVO> list = apiTestRepository.queryExceptionCaseList(query);
        return ApiCaseExceptionResp.buildSelf(list, page.getTotal());
    }

    public List<QueryApiFieldConfigResp> queryApiFieldConfig(QueryApiFieldConfigReq req) {
        ApiTestEntityDO apiTest = apiTestRepository.queryLatestApiTestByMainCode(req.getApiCode());
        if (null == apiTest) {
            throw new ServiceException("接口code不存在！");
        }
        List<ApiConfigTypeEnum> apiConfigTypeList = new ArrayList<>();
        apiConfigTypeList.add(ApiConfigTypeEnum.Customize);
        List<ApiGlobalConfigurationEntityDO> configList = getApiConfig(apiTest.getProductCode(), apiTest.getAppId(), apiTest.getMainApiCode());
        if (CollectionUtil.isNotEmpty(configList)) {
            configList.forEach(config -> apiConfigTypeList.add(ApiConfigTypeEnum.codeOf(config.getApiConfigType())));
        }
        apiConfigTypeList.sort(Comparator.comparingInt(ApiConfigTypeEnum::getCode));
        List<String> keys = GenerateApiCaseUtil.parseApiFieldKey(apiTest.getApiType(), apiTest.getApiData());
        return keys.stream().map(key -> {
            QueryApiFieldConfigResp resp = new QueryApiFieldConfigResp();
            resp.setKey(key);
            resp.setApiConfigTypeList(apiConfigTypeList);
            return resp;
        }).collect(Collectors.toList());
    }

    public List<ApiGlobalConfigurationEntityDO> getApiConfig(String productCode, String appId, String apiCode) {
        List<ApiGlobalConfigurationEntityDO> configList = apiGlobalConfigurationRepository.queryEnabledConfigByProductCode(productCode);
        if (CollectionUtil.isEmpty(configList)) {
            return null;
        }
        List<ApiGlobalConfigurationEntityDO> apiConfigList = new ArrayList<>();
        configList.stream().collect(Collectors.groupingBy(ApiGlobalConfigurationEntityDO::getApiConfigType)).forEach((type, list) -> {
            ApiGlobalConfigurationEntityDO apiConfig = list.stream().filter(config -> apiCode.equals(config.getApiConfigValue())).findFirst().orElse(null);
            if (null != apiConfig) {
                apiConfigList.add(apiConfig);
                return;
            }
            if (StringUtils.isNotEmpty(appId)) {
                list.stream().filter(config -> appId.equals(config.getApiConfigValue())).findFirst().ifPresent(apiConfigList::add);
            }
        });
        return apiConfigList;
    }

    public PageApiTestCaseResp queryApiTestCasePage(PageApiTestCaseReq req) {
        PageApiTestCaseQuery query = apiTestDomainConverter.convert(req);
        //用例列表
        Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        List<PageApiTestCaseVO> doList = apiTestRepository.queryParentApiTestCaseList(query);
        if (CollectionUtil.isEmpty(doList)) {
            return PageApiTestCaseResp.init();
        }
        return PageApiTestCaseResp.buildSelf(doList, page.getTotal());
    }

    public List<ApiTestCaseExecuteDetailResp> queryApiTestCaseExecuteDetail(ApiTestCaseExecuteDetailReq req) {
        ApiTestCaseExecuteDetailQuery query = apiTestDomainConverter.convert(req);
        List<ApiTestCaseExecuteDetailTiledVO> doList = apiTestRepository.queryApiTestCaseExecuteDetail(query);
        return ApiTestCaseExecuteDetailResp.buildSelf(doList);
    }

    public PageApiTestCaseChildrenResp queryApiTestCasePageChildren(PageApiTestCaseChildrenReq req) {
        PageApiTestCaseChildrenQuery childrenQuery = apiTestDomainConverter.convert(req);
        PageApiTestCaseQuery query = new PageApiTestCaseQuery();
        BeanUtils.copyProperties(childrenQuery, query);
        //用例列表
        Page<Object> page = PageHelper.startPage(query.getPage(), query.getSize());
        List<PageApiTestCaseVO> doList = apiTestRepository.queryApiTestCaseList(query, childrenQuery.getParentCaseCode());
        if (CollectionUtil.isEmpty(doList)) {
            return PageApiTestCaseChildrenResp.init();
        }
        return PageApiTestCaseChildrenResp.buildSelf(doList, page.getTotal());
    }

    public List<ApiCaseDocVO> listApiCaseByDocIds(String productCode, List<Long> docIds) {
        if (CollectionUtil.isEmpty(docIds)) {
            return Collections.emptyList();
        }

        List<ApiTestEntityDO> apiTests = apiTestRepository.getApiTestByDocIds(docIds, productCode);
        if (CollectionUtil.isEmpty(apiTests)) {
            return Collections.emptyList();
        }

        Set<String> mainApiCodes = apiTests.stream()
                .map(ApiTestEntityDO::getMainApiCode)
                .filter(StringUtils::isNotEmpty) // 过滤掉空的mainApiCode
                .collect(Collectors.toSet());

        List<ApiTestCaseEntityDO> testCases = apiTestRepository.getApiTestCaseByMainApiCodes(new ArrayList<>(mainApiCodes), productCode);
        if (CollectionUtil.isEmpty(testCases)) {
            return Collections.emptyList();
        }

        Map<Long, List<ApiTestEntityDO>> docIdToApiTests = apiTests.stream()
                .collect(Collectors.groupingBy(ApiTestEntityDO::getDocId));

        Map<String, List<ApiTestCaseEntityDO>> apiCodeToTestCases = testCases.stream()
                .collect(Collectors.groupingBy(ApiTestCaseEntityDO::getApiCode));

        return docIds.stream()
                .map(docId -> {
                    ApiCaseDocVO vo = new ApiCaseDocVO();
                    vo.setDocId(docId);

                    List<ApiTestEntityDO> apiTestsForDoc = docIdToApiTests.getOrDefault(docId, Collections.emptyList());
                    boolean isCaseCoverage = apiTestsForDoc.stream()
                            .map(ApiTestEntityDO::getMainApiCode)
                            .anyMatch(apiCodeToTestCases::containsKey);

                    vo.setIsCaseCoverage(isCaseCoverage);
                    return vo;
                })
                .collect(Collectors.toList());
    }
}
