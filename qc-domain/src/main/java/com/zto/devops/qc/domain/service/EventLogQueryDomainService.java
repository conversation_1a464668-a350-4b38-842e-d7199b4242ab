package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.client.entity.action.PageActionLogVO;
import com.zto.devops.framework.client.query.ListLogQuery;
import com.zto.devops.framework.domain.gateway.repository.IEventLogRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * event log query domain service
 *
 * <AUTHOR>
 * @date 2023-03-29 17:36
 */
@Component
public class EventLogQueryDomainService {
    @Autowired
    private IEventLogRepository iEventLogRepository;

    public PageActionLogVO queryActionLog(ListLogQuery query) {
        return iEventLogRepository.listLog(query);
    }
}
