package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.dto.TagEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.command.AddTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.BatchAddTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.command.RemoveTestcaseTagCommand;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagBatchAddedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.event.TestcaseTagRemovedEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TestcaseTagDomainConverter {

    @Mapping(target = "code", source = "aggregateId")
    TestcaseTagAddedEvent converter(AddTestcaseTagCommand command);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseTagBatchAddedEvent converter(BatchAddTestcaseTagCommand command);

    List<TagVO> convert(List<TagEntityDO> entity);

    @Mapping(target = "code", source = "aggregateId")
    TestcaseTagRemovedEvent converter(RemoveTestcaseTagCommand command);

}
