package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.dto.QcAgentRuleConfigEntityDO;
import com.zto.devops.qc.client.service.agent.model.BatchOptRuleConfigReq;
import com.zto.devops.qc.client.service.agent.model.IsIncludeChaosRuleReq;
import com.zto.devops.qc.client.service.agent.model.PageRuleConfigReq;
import com.zto.devops.qc.client.service.agent.model.RuleConfigReq;

import java.util.List;

public interface QcAgentRuleConfigRepository {

    Boolean newRuleConfig(RuleConfigReq req, User transactor);

    Boolean modifyRuleConfig(RuleConfigReq req, User transactor);

    Boolean batchOptRuleConfig(BatchOptRuleConfigReq batchOptRuleConfigReq, User transactor);

    Boolean removeRuleConfig(RuleConfigReq req, User transactor);

    List<QcAgentRuleConfigEntityDO> queryAgentRuleConfigPage(PageRuleConfigReq req);

    List<QcAgentRuleConfigEntityDO> queryAgentRuleConfig(RuleConfigReq req);

    QcAgentRuleConfigEntityDO queryAgentRuleConfig(Long id);

    void modifyRuleStatus(RuleConfigReq req, User transactor);

    List<String> isContainChaosRule(IsIncludeChaosRuleReq req);

    void batchRemoveByVersionCode(String versionCode);
}
