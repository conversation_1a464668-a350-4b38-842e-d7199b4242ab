package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.dto.AutomaticSourceLogEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.AutomaticRecordLiteInfoVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordLogVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticSourceLogTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.AnalysisAutomaticRecordAbortQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.AutomaticRecordByNameQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.AutomaticRecordLogQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.MoveModuleReq;

import java.util.List;

public interface AutomaticSourceRecordRepository {

    List<AutomaticRecordVO> listAutomaticRecordVO(AutomaticRecordByNameQuery query);

    void addAutomaticRecord(AddAutomaticRecordEvent event);

    void update(AutomaticSourceRecordEntityDO entityDO);

    List<String> selectByTestcaseCodeList(List<String> testcaseCodeList);

    void updateByCodeList(AutomaticSourceRecordEntityDO entityDO, List<String> testcaseCodeList);

    AutomaticSourceRecordEntityDO find(String code);

    void editAutomaticRecord(EditAutomaticRecordEvent event);

    List<AutomaticRecordLogVO> listAutomaticRecordLogVO(AutomaticRecordLogQuery query);

    void addLog(AddAutomaticRecordLogEvent event);

    void editLog(EditAutomaticRecordLogEvent event);

    void updateByPrimaryKeySelective(AutomaticSourceRecordEntityDO recordEntity);

    List<AutomaticSourceRecordEntityDO> selectByCodeList(List<String> automaticSourceCodeList, Boolean enable);

    void cancelAnalysisAutomatic(CancelAnalysisAutomaticEvent event);

    AutomaticRecordLogVO findAutomaticRecordLogVO(String code);

    void editAutomaticLogStatus(String automaticSourceLogCode);

    void submitAnalysisAutomatic(SubmitAnalysisAutomaticEvent event);

    List<AutomaticSourceLogTestcaseVO> listAutomaticSourceLogTestcaseVO(String automaticSourceLogCode);

    List<AutomaticSourceRecordEntityDO> queryAutomaticSourceByTestcaseCode(List<String> testcaseCodeList);

    void automaticSuccess(AutomaticSuccessEvent event);

    void deleteAutomaticRecord(DeleteAutomaticRecordEvent event);

    void deleteTempFileName(DeleteTempFileNameEvent event);

    void editAutomaticPersonLiable(EditAutomaticPersonLiableEvent event);

    List<AutomaticSourceLogTestcaseVO> selectListAndPlanNameLog(String logCode, String flag);

    void update(AutomaticSourceLogEntityDO entityDO);

    AutomaticSourceRecordEntityDO selectByPrimaryKey(String code);

    /**
     * 登记库校验
     *
     * @param req
     */
    void checkRecordName(MoveModuleReq req);

    List<AutomaticRecordVO> analysisAutomaticRecordAbortQuery(AnalysisAutomaticRecordAbortQuery query);

    List<AutomaticRecordLiteInfoVO> pageLiteInfoByProductCode(String productCode, String nameOrCode);

    List<String> queryRelatedPlanName(String automaticSourceCode);

    List<String> queryRelatedTaskName(String automaticSourceCode);

    /**
     * 根据git地址，查询支持自动解析的登记库
     *
     * @param gitHttpUrl Git地址
     * @return {@link AutomaticSourceRecordEntityDO}
     */
    List<AutomaticSourceRecordEntityDO> selectAutoAnalysisByAddress(String gitHttpUrl);

    AutomaticSourceLogEntityDO findAutomaticSourceByLogCode(String logCode);

    List<AutomaticRecordVO> queryAutomaticRecordByType(String type);

}
