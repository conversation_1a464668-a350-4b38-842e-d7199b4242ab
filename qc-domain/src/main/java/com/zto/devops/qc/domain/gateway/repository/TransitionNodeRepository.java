package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.common.TransitionNodeAddEvent;
import com.zto.devops.qc.client.model.dto.TransitionNodeEntityDO;
import com.zto.devops.qc.client.model.issue.entity.TransitionNodeVO;
import com.zto.devops.qc.client.model.issue.event.TransitionNodeAddedEvent;

import java.util.List;

public interface TransitionNodeRepository {

    List<TransitionNodeVO>  getTransitionNodeList(String code);

    void handleTransitionNodeAddEvent(TransitionNodeAddEvent event);

    List<TransitionNodeVO> findIssueTransitionNodeByIssueCode(String businessCode);

    List<TransitionNodeEntityDO> listIssueTransitionNode(List<String> issueCodes);

    void handleTransitionNodeAddedEvent(TransitionNodeAddedEvent event);

    List<TransitionNodeEntityDO> selectContentByIssueList(List<String> businessCodes);

    List<TransitionNodeEntityDO> queryRejectReasonByIssueCodeList(List<String> issueCodeList);
}
