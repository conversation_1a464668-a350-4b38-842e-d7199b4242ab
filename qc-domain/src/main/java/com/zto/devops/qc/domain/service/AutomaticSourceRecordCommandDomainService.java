package com.zto.devops.qc.domain.service;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.command.impexp.FinishJobCommand;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.event.RevisionEvent;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.service.AsyncImpExpService;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.channel.TestEventEnums;
import com.zto.devops.qc.client.model.dto.AutomaticSourceLogEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.command.*;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.event.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.AnalysisAutomaticRecordAbortQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.AutomaticRecordLogByCodeQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.AutomaticRecordLogQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.AutomaticRecordQuery;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.SchedulerCaseVO;
import com.zto.devops.qc.domain.converter.AutomaticSourceRecordDomainConverter;
import com.zto.devops.qc.domain.converter.TestcaseDomainConverter;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.jenkins.IJenkinsService;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.repository.ApiTestRepository;
import com.zto.devops.qc.domain.gateway.repository.AutomaticSchedulerRepository;
import com.zto.devops.qc.domain.gateway.repository.AutomaticSourceRecordRepository;
import com.zto.devops.qc.domain.gateway.repository.TestcaseRepository;
import com.zto.devops.qc.domain.gateway.util.JmxUtil;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class AutomaticSourceRecordCommandDomainService extends BaseDomainService {

    @Autowired
    private AutomaticSourceRecordDomainConverter automaticSourceRecordDomainConverter;

    @Autowired
    private AutomaticSourceRecordRepository automaticSourceRecordRepository;

    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;

    @Autowired
    private JmxUtil jmxUtil;

    @Autowired
    private TestcaseRepository testcaseRepository;

    @Autowired
    private TestcaseDomainConverter testcaseDomainConverter;

    @Autowired
    private AutomaticSourceRecordQueryDomainService automaticSourceRecordQueryDomainService;

    @Autowired
    private AsyncImpExpService asyncImpExpService;

    @Autowired
    private ZtoOssService ztoOssService;

    @Autowired
    private TestcaseCommandDomainService testcaseCommandDomainService;

    @Autowired
    private IJenkinsService jenkinsService;

    @Autowired
    private QcConfigBasicService qcConfigBasicService;

    @Autowired
    private AutomaticSchedulerRepository automaticSchedulerRepository;

    @Autowired
    private ApiTestRepository apiTestRepository;

    private final Executor AUTOMATIC_ANALYTIC_EXECUTOR = new ThreadPoolExecutor(
            10,
            100,
            1L,
            TimeUnit.HOURS,
            new SynchronousQueue<>(),
            ThreadFactoryBuilder.create().setNamePrefix("Automatic-Analytic-").build());

    private static ThreadPoolExecutor THREAD_POOL_EMITTER = new ThreadPoolExecutor(
            1, 10, 10,
            TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(100),
            ThreadFactoryBuilder.create().setNamePrefix("THREAD_POOL_EMITTER-").build()
    );

    public void addAutomaticRecordCommand(AddAutomaticRecordCommand command) {
        log.info("addAutomaticRecordCommand >>> {}", JSON.toJSONString(command));
        AddAutomaticRecordEvent event = automaticSourceRecordDomainConverter.convert(command);
        automaticSourceRecordRepository.addAutomaticRecord(event);
        apply(event);
    }

    public void sendCaseCommand(AddAutomaticRecordCommand command) {
        AUTOMATIC_ANALYTIC_EXECUTOR.execute(() -> {
            try {
                sendCase(command);
            } catch (Exception e) {
                log.error("解析失败！", e);
            }
        });
    }

    public void sendCase(AddAutomaticRecordCommand command) {
        log.info("sendCaseCommand >>> {}", JSON.toJSONString(command));
        AutomaticSourceRecordEntityDO entityDO = new AutomaticSourceRecordEntityDO();
        entityDO.setCode(command.getAggregateId());
        entityDO.setProductCode(command.getProductCode());
        entityDO.preUpdate(command);
        try {
            Document document = jmxUtil.getJmeterDocument(
                    command.getBucketName(),
                    command.getAddress(),
                    command.getFileName());
            AutomaticNode automaticNode = jmxUtil.getJmeterPlanTree(document);
            List<AutomaticNode> automaticNodeList = new ArrayList<>();
            automaticNodeList.add(automaticNode);
            automaticNodeToCase(
                    automaticNodeList,
                    command.getAggregateId(),
                    command.getTransactor(),
                    command.getProductCode(),
                    command.getTestcaseCode(),
                    command.getPath());
        } catch (DocumentException e) {
            log.error("获取用例相关失败！", e);
            String failInformation = StringUtils.defaultIfBlank(e.getMessage(), "获取用例相关失败！");
            entityDO.setStatus(AutomaticStatusEnum.FAIL);
            entityDO.setFailInformation(StrUtil.maxLength(failInformation, 252));
            updateAutomaticSourceRecordStatus(entityDO);
            throw new ServiceException(failInformation);
        } catch (ServiceException e) {
            log.error("", e);
            entityDO.setStatus(AutomaticStatusEnum.FAIL);
            entityDO.setFailInformation(e.getMsg());
            updateAutomaticSourceRecordStatus(entityDO);
            throw new ServiceException(e.getMsg());
        } catch (Exception e) {
            log.error("Jmeter解析异常！", e);
            String failInformation = StringUtils.defaultIfBlank(e.getMessage(), "Jmeter解析异常！");
            entityDO.setStatus(AutomaticStatusEnum.FAIL);
            entityDO.setFailInformation(StrUtil.maxLength(failInformation, 252));
            updateAutomaticSourceRecordStatus(entityDO);
            throw new ServiceException(failInformation);
        }
        updateAutomaticSourceRecordStatus(entityDO);
    }

    private void updateAutomaticSourceRecordStatus(AutomaticSourceRecordEntityDO entityDO) {
        if (null != entityDO.getStatus()) {
            automaticSourceRecordRepository.update(entityDO);
            emit(TestEventEnums.TEST_AUTOMATIC_ANALYTIC.toReactiveId(entityDO.getProductCode()), entityDO);
        }
    }

    public void cancelAnalysisAutomaticRecord(CancelAnalysisAutomaticCommand command) {
        log.info("CancelAnalysisAutomaticCommand>>>>>>>>>>>>>>>> = :{}", JsonUtil.toJSON(command));
        CancelAnalysisAutomaticEvent event = new CancelAnalysisAutomaticEvent();
        event.setTransactor(command.getTransactor());
        event.setCode(command.getAggregateId());
        event.setAggregateId(command.getAggregateId());
        event.setAutomaticSourceLogCode(command.getAutomaticSourceLogCode());
        automaticSourceRecordRepository.cancelAnalysisAutomatic(event);
        AutomaticSourceRecordEntityDO entityDO = automaticSourceRecordRepository.find(event.getCode());
        emit(TestEventEnums.TEST_AUTOMATIC_ANALYTIC.toReactiveId(event.getProductCode()), entityDO);
        apply(event);
    }

    private void automaticNodeToCase(
            List<AutomaticNode> automaticNodeList,
            String automaticCode,
            User user,
            String productCode,
            String testcaseCode,
            String path) {

        log.info("automaticNodeList:{} automaticCode:{} productCode:{} testcaseCode:{} path:{}",
                JSON.toJSONString(automaticNodeList), automaticCode, productCode, testcaseCode, path);

        AutomaticSourceRecordEntityDO entityDO = new AutomaticSourceRecordEntityDO();
        entityDO.setCode(automaticCode);
        entityDO.setProductCode(productCode);
        entityDO.preUpdate(user);
        try {
            if (CollectionUtils.isNotEmpty(automaticNodeList)) {
                List<TestcaseEntityDO> list = new ArrayList<>();
                String testcaseModulePath = (StringUtils.isEmpty(path) ? "" : path + ".")
                        + (StringUtils.isEmpty(testcaseCode) ? "" : testcaseCode + ".")
                        + automaticCode;
                for (AutomaticNode automaticNode : automaticNodeList) {
                    this.sendCaseCommandList(
                            null,
                            automaticNode,
                            null,
                            user,
                            automaticCode,
                            productCode,
                            list,
                            null,
                            null,
                            testcaseModulePath);
                }
                testcaseRepository.batchInsertCase(list);
            }
            entityDO.setStatus(AutomaticStatusEnum.SUCCESS);
        } catch (Exception e) {
            log.error("未知错误！", e);
            entityDO.setStatus(AutomaticStatusEnum.FAIL);
            String failInformation = Optional.ofNullable(e.getMessage()).orElse("未知错误！");
            entityDO.setFailInformation(StrUtil.maxLength(failInformation, 252));
        } finally {
            automaticSourceRecordRepository.update(entityDO);
            emit(TestEventEnums.TEST_AUTOMATIC_ANALYTIC.toReactiveId(productCode), entityDO);
        }
    }

    private void sendCaseCommandList(
            String path,
            AutomaticNode node,
            String parentCode,
            User transactor,
            String automaticSourceCode,
            String productCode,
            List<TestcaseEntityDO> list,
            String nodeTypePath,
            String parentNodeType,
            String testcaseModulePath) {
        if (StringUtils.isNotEmpty(node.getName()) && node.getName().length() > 256) {
            throw new ServiceException("名称长度不超过256字符！");
        }
        TestcaseEntityDO testcase = new TestcaseEntityDO();
        testcase.setCode(aggregateIdGenerateService.generateId(AggregateType.TEST_CASE));
        if (null != AutomaticNodeTypeEnum.getAttributeByDesc(node.getType())) {
            testcase.setAttribute(AutomaticNodeTypeEnum.getAttributeByDesc(node.getType()));
        } else {
            testcase.setAttribute(TestcaseAttributeEnum.MODULE);
        }
        testcase.setType(TestcaseTypeEnum.AUTO);
        testcase.setComment(StrUtil.maxLength(node.getComment(), 197));
        testcase.setName(trimTrailingSpaces(node.getName())); // todo 去空格
        testcase.setParentCode(StringUtils.defaultString(parentCode));
        testcase.preCreate(transactor);
        if (null != transactor) {
            testcase.setDutyUser(transactor.getUserName());
            testcase.setDutyUserId(transactor.getUserId());
        }
        testcase.setAutomaticSourceCode(automaticSourceCode);
        testcase.setProductCode(productCode);
        testcase.setPriority(TestcasePriorityEnum.MIDDLE);
        if (!node.getEnable()) {
            testcase.setStatus(TestcaseStatusEnum.DISABLE);
        } else {
            testcase.setStatus(TestcaseStatusEnum.NORMAL);
        }
        testcase.setNodeType(AutomaticNodeTypeEnum.getEnumByDesc(node.getType()));
        testcase.setSort(list.size());
        if (null == path) {
            path = StringUtils.defaultString(parentCode);
        } else {
            if (!path.contains(parentCode)) {
                path = (StringUtils.isBlank(path) ? "" : path + ".") + parentCode;
            }
        }
        if (null == nodeTypePath) {
            nodeTypePath = parentNodeType;
        } else {
            if (!nodeTypePath.contains(parentNodeType)) {
                nodeTypePath = nodeTypePath + "." + parentNodeType;
            }
        }
        testcase.setNodeTypePath(nodeTypePath);
        testcase.setPath(path);
        testcase.setTestcaseModulePath(testcaseModulePath);
        testcase.setInterfaceName(StringUtils.defaultString(node.getInterfaceName()));
        if (CollectionUtil.isNotEmpty(node.getAutomaticNodeList())) {
            for (AutomaticNode automaticNode : node.getAutomaticNodeList()) {
                this.sendCaseCommandList(
                        path,
                        automaticNode,
                        testcase.getCode(),
                        transactor,
                        automaticSourceCode,
                        productCode,
                        list,
                        nodeTypePath,
                        testcase.getName(),
                        testcaseModulePath);
            }
        }
        log.info("-------sendCaseCommandList@TestcaseEntityDO--------{}", JSON.toJSONString(testcase));
        list.add(testcase);
    }

    public void editAutomaticRecordCommand(EditAutomaticRecordCommand command) {
        log.info("editAutomaticRecordCommand >>> {}", JSON.toJSONString(command));
        EditAutomaticRecordEvent event = automaticSourceRecordDomainConverter.convert(command);
        AutomaticSourceRecordEntityDO entityDO = automaticSourceRecordRepository.find(event.getCode());
        EditAutomaticRecordCommand source = automaticSourceRecordDomainConverter.convertDO(entityDO);
        RevisionEvent.setChangeLog(source, command, EditAutomaticRecordCommand.class, event);
        automaticSourceRecordRepository.editAutomaticRecord(event);
        apply(event);
    }

    public void addAutomaticRecordLogCommand(AddAutomaticRecordLogCommand command) {
        log.info("addAutomaticRecordLogCommand >>> {}", JSON.toJSONString(command));
        AddAutomaticRecordLogEvent event = automaticSourceRecordDomainConverter.convert(command);
        event.setList(testcaseDomainConverter.convertList(command.getAddCaseList()));
        automaticSourceRecordRepository.addLog(event);
        apply(event);
    }

    public void sendEditCaseCommand(EditAutomaticRecordCommand command, String name, String logCode) {
        AUTOMATIC_ANALYTIC_EXECUTOR.execute(() -> {
            try {
                sendEditCase(command, name, logCode);
            } catch (Exception e) {
                log.error("解析失败！", e);
            }
        });
    }

    public void sendEditCase(EditAutomaticRecordCommand command, String name, String logCode) {
        log.info("sendEditCaseCommand >>> {} {} {}", JSON.toJSONString(command), name, logCode);
        // 添加log
        EditAutomaticRecordLogCommand editAutomaticRecordLogCommand =
                new EditAutomaticRecordLogCommand(command.getAggregateId());
        editAutomaticRecordLogCommand.setAutomaticSourceCode(logCode);
        try {
            Document document = jmxUtil.getJmeterDocument(command.getBucketName(), command.getAddress(), name);
            AutomaticNode automaticNode = jmxUtil.getJmeterPlanTree(document);
            List<AutomaticNode> list = new ArrayList<>();
            list.add(automaticNode);
            automaticNodeToCaseEdit(
                    list,
                    command.getAggregateId(),
                    command.getTransactor(),
                    command.getProductCode(),
                    command.getTestcaseCode(),
                    command.getPath(),
                    logCode);
        } catch (DocumentException e) {
            log.error("获取用例相关失败！", e);
            String failInformation = StringUtils.defaultIfBlank(e.getMessage(), "获取用例相关失败！");
            editAutomaticRecordLogCommand.setFailInformation(StrUtil.maxLength(failInformation, 252));
            editAutomaticRecordLogCommand.setStatus(AutomaticStatusEnum.FAIL.name());
            updateAutomaticRecordLogStatus(editAutomaticRecordLogCommand);
            throw new ServiceException(failInformation);
        } catch (ServiceException e) {
            log.error("", e);
            editAutomaticRecordLogCommand.setFailInformation(e.getMsg());
            editAutomaticRecordLogCommand.setStatus(AutomaticStatusEnum.FAIL.name());
            updateAutomaticRecordLogStatus(editAutomaticRecordLogCommand);
            throw new ServiceException(e.getMsg());
        } catch (Exception e) {
            log.error("Jmeter解析异常！", e);
            String failInformation = StringUtils.defaultIfBlank(e.getMessage(), "Jmeter解析异常！");
            editAutomaticRecordLogCommand.setFailInformation(StrUtil.maxLength(failInformation, 252));
            editAutomaticRecordLogCommand.setStatus(AutomaticStatusEnum.FAIL.name());
            updateAutomaticRecordLogStatus(editAutomaticRecordLogCommand);
            throw new ServiceException(failInformation);
        }
        updateAutomaticRecordLogStatus(editAutomaticRecordLogCommand);
    }

    private void updateAutomaticRecordLogStatus(EditAutomaticRecordLogCommand editCommand) {
        if (null != editCommand.getStatus()) {
            this.editAutomaticRecordLogCommand(editCommand);
            emit(TestEventEnums.TEST_AUTOMATIC_EDIT.toReactiveId(editCommand.getAggregateId()), null);
        }
    }

    private void automaticNodeToCaseEdit(
            List<AutomaticNode> automaticNodeList,
            String automaticCode,
            User user,
            String productCode,
            String testcaseCode,
            String path,
            String logCode) {

        log.info("automaticNode:{} automaticCode:{} productCode:{} testcaseCode:{} path:{} logCode:{}",
                JSON.toJSONString(automaticNodeList), automaticCode, productCode, testcaseCode, path, logCode);

        Map<String, Object> resMap = new HashMap<>();
        // 添加log
        EditAutomaticRecordLogCommand editAutomaticRecordLogCommand = new EditAutomaticRecordLogCommand(automaticCode);
        editAutomaticRecordLogCommand.setAutomaticSourceCode(logCode);
        // 查询登记库下 所有用例
        List<TestcaseVO> listTestCase = testcaseRepository.listCaseByAutomaticCode(automaticCode);
        try {
            List<AddTestcaseCommand> updateList = new ArrayList<>();
            List<AddTestcaseCommand> addList = new ArrayList<>();
            for (AutomaticNode automaticNode : automaticNodeList) {
                this.sendEditCaseCommandList(
                        null,
                        automaticNode,
                        "",
                        user,
                        automaticCode,
                        productCode,
                        new ArrayList<>(),
                        listTestCase,
                        updateList,
                        addList,
                        null,
                        null);
            }
            int insertNo = (int) addList.stream()
                    .filter(it -> it.getAttribute().name().equals(TestcaseAttributeEnum.TESTCASE.name()))
                    .count();
            resMap.put("insertNo", insertNo);
            if (insertNo > 0) {
                List<TestcaseVO> insertVoList = this.composeTreeModule(addList, listTestCase);
                resMap.put("insertList", insertVoList);
            }
            int updateNo = (int) updateList.stream()
                    .filter(it -> it.getAttribute().name().equals(TestcaseAttributeEnum.TESTCASE.name()))
                    .count();
            resMap.put("updateNo", updateNo);
            if (updateNo > 0) {
                List<TestcaseVO> updateVoList = this.composeTreeModule(updateList, listTestCase);
                resMap.put("updateList", updateVoList);
            }
            List<AddTestcaseCommand> deleteList = listTestCase.stream()
                    .filter(it -> updateList.stream().noneMatch(o -> o.getAggregateId().equals(it.getCode())))
                    .map(vo -> {
                        AddTestcaseCommand it = testcaseDomainConverter.convertVO(vo);
                        it.setFlag(TestcaseFlagEnum.delete.name());
                        it.setTransactor(user);
                        return it;
                    }).collect(Collectors.toList());
            int deleteNo = (int) deleteList.stream()
                    .filter(it -> it.getAttribute().name().equals(TestcaseAttributeEnum.TESTCASE.name()))
                    .count();
            resMap.put("deleteNo", deleteNo);
            if (deleteNo > 0) {
                List<TestcaseVO> deleteVoList = this.composeTreeModule(deleteList, listTestCase);
                resMap.put("deleteList", deleteVoList);
            }
            List<AddTestcaseCommand> caseAddList = new ArrayList<>();
            caseAddList.addAll(addList);
            caseAddList.addAll(updateList);
            caseAddList.addAll(deleteList);
            editAutomaticRecordLogCommand.setAddCaseList(caseAddList);
            editAutomaticRecordLogCommand.setStatus(AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name());
            editAutomaticRecordLogCommand.setTransactor(user);
            resMap.put("automaticSourceLogCode", logCode);
        } catch (ServiceException e) {
            log.error("未知错误", e);
            editAutomaticRecordLogCommand.setFailInformation(e.getMsg());
            editAutomaticRecordLogCommand.setStatus(AutomaticStatusEnum.FAIL.name());
            resMap = null;
        } catch (Exception e) {
            log.error("未知错误", e);
            String failInformation = Optional.ofNullable(e.getMessage()).orElse("未知错误！");
            editAutomaticRecordLogCommand.setFailInformation(StrUtil.maxLength(failInformation, 252));
            editAutomaticRecordLogCommand.setStatus(AutomaticStatusEnum.FAIL.name());
            resMap = null;
        }
        this.editAutomaticRecordLogCommand(editAutomaticRecordLogCommand);
        emit(TestEventEnums.TEST_AUTOMATIC_EDIT.toReactiveId(automaticCode), resMap);
    }

    private void sendEditCaseCommandList(
            String path,
            AutomaticNode node,
            String parentCode,
            User transactor,
            String automaticSourceCode,
            String productCode,
            List<AddTestcaseCommand> list,
            List<TestcaseVO> selectList,
            List<AddTestcaseCommand> updateList,
            List<AddTestcaseCommand> addList,
            String nodeTypePath,
            String parentNodeType) {
        if (StringUtils.isNotEmpty(node.getName()) && node.getName().length() > 256) {
            throw new ServiceException("名称长度不超过256字符！");
        }
        AddTestcaseCommand testcase =
                new AddTestcaseCommand(aggregateIdGenerateService.generateId(AggregateType.TEST_CASE));
        if (null != AutomaticNodeTypeEnum.getAttributeByDesc(node.getType())) {
            testcase.setAttribute(AutomaticNodeTypeEnum.getAttributeByDesc(node.getType()));
        } else {
            testcase.setAttribute(TestcaseAttributeEnum.MODULE);
        }
        testcase.setType(TestcaseTypeEnum.AUTO);
        testcase.setComment(StrUtil.maxLength(node.getComment(), 197));
        testcase.setName(trimTrailingSpaces(node.getName())); // todo 去空格
        testcase.setParentCode(StringUtils.defaultString(parentCode));
        testcase.setTransactor(transactor);
        if (null != transactor) {
            testcase.setDutyUser(transactor.getUserName());
            testcase.setDutyUserId(transactor.getUserId());
        }
        testcase.setAutomaticSourceCode(automaticSourceCode);
        testcase.setProductCode(productCode);
        if (!node.getEnable()) {
            testcase.setStatus(TestcaseStatusEnum.DISABLE);
        } else {
            testcase.setStatus(TestcaseStatusEnum.NORMAL);
        }
        if (StringUtils.isBlank(path)) {
            path = StringUtils.defaultString(parentCode);
        } else {
            path = path + "." + parentCode;
        }
        if (StringUtils.isBlank(nodeTypePath)) {
            nodeTypePath = StringUtils.defaultString(parentNodeType);
        } else {
            nodeTypePath = nodeTypePath + "." + parentNodeType;
        }
        testcase.setNodeTypePath(nodeTypePath);
        testcase.setPath(path);
        testcase.setNodeType(AutomaticNodeTypeEnum.getEnumByDesc(node.getType()));
        testcase.setSort(list.size());
        testcase.setInterfaceName(StringUtils.defaultString(node.getInterfaceName()));
        TestcaseVO oldCase = null;
        if (CollectionUtil.isNotEmpty(selectList)) {
            for (TestcaseVO vo : selectList) {
                if (!vo.getName().equals(testcase.getName())) {
                    continue;
                }
                if (!vo.getNodeType().equals(testcase.getNodeType())) {
                    continue;
                }
                if (StringUtils.isBlank(vo.getNodeTypePath()) && StringUtils.isBlank(testcase.getNodeTypePath())) {
                    oldCase = vo;
                }
                if (StringUtils.isNotBlank(vo.getNodeTypePath())
                        && vo.getNodeTypePath().equals(testcase.getNodeTypePath())) {
                    oldCase = vo;
                }
            }
            if (null != oldCase) {
                path = oldCase.getPath();
                testcase.setPath(path);
                testcase.setTestPlanList(oldCase.getTestPlanList());
                testcase.setAggregateId(oldCase.getCode());
                if (!oldCase.getStatus().name().equals(testcase.getStatus().name())) {
                    testcase.setIfUpdateStatus(true);
                }
                testcase.setFlag(TestcaseFlagEnum.update.name());
                updateList.add(testcase);
            } else {
                testcase.setFlag(TestcaseFlagEnum.insert.name());
                addList.add(testcase);
            }
        } else {
            testcase.setFlag(TestcaseFlagEnum.insert.name());
            addList.add(testcase);
        }
        if (CollectionUtil.isNotEmpty(node.getAutomaticNodeList())) {
            for (AutomaticNode automaticNode : node.getAutomaticNodeList()) {
                this.sendEditCaseCommandList(
                        path,
                        automaticNode,
                        testcase.getAggregateId(),
                        transactor,
                        automaticSourceCode,
                        productCode,
                        list,
                        selectList,
                        updateList,
                        addList,
                        nodeTypePath,
                        testcase.getName());
            }
        }
        log.info("-------sendEditCaseCommandList@AddTestcaseCommand--------{}", JSON.toJSONString(testcase));
        list.add(testcase);
    }


    public static String trimTrailingSpaces(String input) {
        if (input == null) {
            return null;
        }
        int length = input.length();
        int startIndex = 0;
        int endIndex = length - 1;
        while (startIndex < length && Character.isWhitespace(input.charAt(startIndex))) {
            startIndex++;
        }
        while (endIndex >= startIndex && Character.isWhitespace(input.charAt(endIndex))) {
            endIndex--;
        }
        if (startIndex > endIndex) {
            return "";
        }
        return input.substring(startIndex, endIndex + 1);
    }

    private List<TestcaseVO> composeTreeModule(List<AddTestcaseCommand> operateList, List<TestcaseVO> testcaseList) {
        Map<String, Long> countMap = operateList.stream()
                .filter(item -> TestcaseAttributeEnum.TESTCASE.equals(item.getAttribute()))
                .flatMap(item -> Stream.of(item.getPath().split("\\.")))
                .collect(Collectors.groupingBy(s -> s, Collectors.counting()));
        List<AddTestcaseCommand> list = testcaseList.stream()
                .filter(item -> countMap.containsKey(item.getCode()))
                .map(item -> testcaseDomainConverter.convertVO(item))
                .collect(Collectors.toList());
        List<String> codes = list.stream().map(AddTestcaseCommand::getAggregateId).collect(Collectors.toList());
        operateList.stream().filter(item -> !codes.contains(item.getAggregateId())).forEach(list::add);
        List<TestcaseVO> finalList = list.stream().map(item -> testcaseDomainConverter.convertVO(item)).collect(Collectors.toList());
        return finalList.stream().peek(item -> {
                    finalList.stream()
                            .filter(o -> item.getCode().equals(o.getParentCode()))
                            .forEach(o -> {
                                if (null == item.getSonList()) {
                                    item.setSonList(new ArrayList<>());
                                }
                                item.getSonList().add(o);
                            });
                    item.setCount(countMap.getOrDefault(item.getCode(), 0L).intValue());
                })
                .filter(item -> StringUtils.isBlank(item.getParentCode()))
                .collect(Collectors.toList());
    }

    private void editAutomaticRecordLogCommand(EditAutomaticRecordLogCommand command) {
        log.info("editAutomaticRecordLogCommand >>> {}", JSON.toJSONString(command));
        EditAutomaticRecordLogEvent event = automaticSourceRecordDomainConverter.convert(command);
        event.setList(testcaseDomainConverter.convertList(command.getAddCaseList()));
        automaticSourceRecordRepository.editLog(event);
        doSubmit(command.getAggregateId(), command.getTransactor());
        apply(event);
    }

    public void editAutomaticLogStatusCommand(String automaticSourceLogCode) {
        log.info("editAutomaticLogStatusCommand >>> {}", automaticSourceLogCode);
        automaticSourceRecordRepository.editAutomaticLogStatus(automaticSourceLogCode);
    }

    public void submitAnalysisAutomaticRecord(SubmitAnalysisAutomaticCommand command) {
        AUTOMATIC_ANALYTIC_EXECUTOR.execute(() -> submitAnalysisAutomatic(command));
    }

    public void submitAnalysisAutomatic(SubmitAnalysisAutomaticCommand command, User operator) {
        log.info("submitAnalysisAutomatic >>> {}", JSON.toJSONString(command));
        // 查询记录表数据
        AutomaticRecordLogVO automaticRecordLogVO =
                automaticSourceRecordRepository.findAutomaticRecordLogVO(command.getAutomaticSourceLogCode());
        if (Objects.nonNull(automaticRecordLogVO)) {
            AutomaticRecordQuery automaticRecordQuery = new AutomaticRecordQuery();
            automaticRecordQuery.setCode(command.getCode());
            automaticRecordQuery.setTransactor(command.getTransactor());
            AutomaticRecordVO automaticRecordVO =
                    automaticSourceRecordQueryDomainService.automaticRecordQuery(automaticRecordQuery);
            String filename = automaticRecordLogVO.getFileName();
            if (AutomaticRecordTypeEnum.JMETER.equals(automaticRecordVO.getType())
                    && !automaticRecordVO.getFileName().equals(filename)) {
                filename = StringUtils.substringBeforeLast(automaticRecordLogVO.getFileName(), "_temp")
                        + StringUtils.substringAfterLast(automaticRecordLogVO.getFileName(), "_temp");
                if (StringUtils.isEmpty(command.getAddress())) {
                    command.setAddress(automaticRecordVO.getAddress());
                }
                try {
                    //oss替换新文件
                    ztoOssService.copyObject(automaticRecordVO.getBucketName(), command.getAddress(),
                            automaticRecordLogVO.getFileName(), filename);
                    if (!filename.equals(automaticRecordLogVO.getFileName())) {
                        ztoOssService.cleanObject(automaticRecordVO.getBucketName(),
                                command.getAddress() + automaticRecordLogVO.getFileName());
                    }
                } catch (Exception e) {
                    log.error("重新保存oss异常", e);
                }
            }
            String testPath = testcaseRepository.selectAllCodeByParentCode(automaticRecordVO.getTestcaseCode());
            //当前一次解析的数据
            List<AutomaticSourceLogTestcaseVO> logCaseList =
                    automaticSourceRecordRepository.listAutomaticSourceLogTestcaseVO(command.getAutomaticSourceLogCode());
            //最近一次解析数据
            List<TestcaseVO> listTestCase = testcaseRepository.listCaseByAutomaticCode(command.getCode());
            List<EditTestcaseCommand> updateList = new ArrayList<>();
            logCaseList.forEach(vo -> {
                if (TestcaseFlagEnum.insert.name().equals(vo.getFlag())) {
                    AddTestcaseCommand testcase = testcaseDomainConverter.convertAdd(vo);
                    testcase.setAggregateId(vo.getTestcaseCode());
                    testcase.setAutomaticSourceCode(command.getCode());
                    if (StringUtils.isEmpty(testPath)) {
                        testcase.setTestcaseModulePath(command.getCode());
                    } else {
                        testcase.setTestcaseModulePath(testPath + "." + command.getCode());
                    }
                    testcase.setTransactor(command.getTransactor());
                    testcaseCommandDomainService.addTestcaseCommand(testcase);
                }
                if (TestcaseFlagEnum.update.name().equals(vo.getFlag())) {
                    EditTestcaseCommand editCommand = testcaseDomainConverter.convertEdit(vo);
                    editCommand.setAggregateId(vo.getTestcaseCode());
                    editCommand.setTransactor(command.getTransactor());
                    updateList.add(editCommand);
                    testcaseCommandDomainService.editTestcaseCommand(editCommand);
                }
            });
            List<String> planCodes = new ArrayList<>();
            List<TestcaseVO> deleteList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(updateList)) {
                List<String> codeUpdateList = updateList.stream()
                        .map(EditTestcaseCommand::getAggregateId)
                        .collect(Collectors.toList());
                //剔除更新就是删除，老解析数据不存在insert
                deleteList = listTestCase.stream()
                        .filter(it -> !codeUpdateList.contains(it.getCode()))
                        .collect(Collectors.toList());
                for (TestcaseVO deleteVo : deleteList) {
                    DeleteTestcaseCommand commandDelete = new DeleteTestcaseCommand(deleteVo.getCode());
                    commandDelete.setAttribute(deleteVo.getAttribute());
                    commandDelete.setTransactor(command.getTransactor());
                    commandDelete.setType(deleteVo.getType());
                    testcaseCommandDomainService.deleteTestcaseCommand(command.getCode(), commandDelete, planCodes, codeUpdateList, operator);
                }
            } else {
                for (TestcaseVO vo : listTestCase) {
                    DeleteTestcaseCommand commandDelete = new DeleteTestcaseCommand(vo.getCode());
                    commandDelete.setAttribute(vo.getAttribute());
                    commandDelete.setTransactor(command.getTransactor());
                    commandDelete.setType(vo.getType());
                    testcaseCommandDomainService.deleteTestcaseCommand(command.getCode(), commandDelete, planCodes, null, operator);
                    deleteList = listTestCase;
                }
            }

            SubmitAnalysisAutomaticCommand submitAnalysisAutomaticCommand =
                    new SubmitAnalysisAutomaticCommand(command.getAggregateId());
            submitAnalysisAutomaticCommand.setAutomaticSourceLogCode(command.getAutomaticSourceLogCode());
            submitAnalysisAutomaticCommand.setTransactor(command.getTransactor());
            submitAnalysisAutomaticCommand.setCode(command.getAggregateId());
            submitAnalysisAutomaticCommand.setFileName(filename);
            submitAnalysisAutomaticCommand.setCommitId(automaticRecordLogVO.getCommitId());
            submitAnalysisAutomaticCommand.setAnalyticMethod(command.getAnalyticMethod());
            this.submitAnalysisAutomaticCommand(submitAnalysisAutomaticCommand);

            /**
             * 发布场景图，定时任务关联场景图同步更新
             */
            int countNum = apiTestRepository.countEnableBySourceCode(command.getCode());
            if (countNum > 0) {
                this.schedulerAutomaticSourceCase(logCaseList, listTestCase, operator);
            }

            if (CollectionUtil.isNotEmpty(deleteList)) {
                List<String> deleteCaseCodes = deleteList.stream().filter(vo -> "TESTCASE".equals(vo.getAttribute().name())).map(TestcaseVO::getCode).distinct().collect(Collectors.toList());
                // 删除旧用例与定时任务的关联
                automaticSchedulerRepository.deleteSchedulerCaseByCodes(deleteCaseCodes);
            }


//            testcaseCommandDomainService.batchAddSceneTestCaseToPlan(command.getCode(),planCodes,operator);
        }
        AutomaticSuccessCommand automaticSuccessCommand = new AutomaticSuccessCommand(command.getCode());
        automaticSuccessCommand.setTransactor(command.getTransactor());
        this.automaticSuccessCommand(automaticSuccessCommand);
    }

    private void schedulerAutomaticSourceCase(List<AutomaticSourceLogTestcaseVO> newCaseList,
                                              List<TestcaseVO> oldCaseList, User user) {
        if (CollectionUtils.isNotEmpty(oldCaseList)) {
            // 查出旧用例关联的定时任务
            List<String> oldCaseCodes = oldCaseList.stream().map(TestcaseVO::getCode).collect(Collectors.toList());
            List<String> newCaseCodes = newCaseList.stream().filter(vo -> "TESTCASE".equals(vo.getAttribute()))
                    .map(AutomaticSourceLogTestcaseVO::getTestcaseCode).distinct().collect(Collectors.toList());
            List<String> schedulerList = automaticSchedulerRepository.selectSchedulerByCaseCodes(oldCaseCodes);
            // 删除旧用例与定时任务的关联
            automaticSchedulerRepository.deleteSchedulerCaseByCodes(oldCaseCodes);
            // 定时任务关联新用例
            for (String schedulerCode : schedulerList) {
                List<SchedulerCaseVO> newSchedulerCaseVOS = new ArrayList<>();
                for (String caseCode : newCaseCodes) {
                    SchedulerCaseVO caseVO = new SchedulerCaseVO();
                    caseVO.setCaseCode(caseCode);
                    caseVO.setSchedulerCode(schedulerCode);
                    if (null != user) {
                        caseVO.setCreator(user.getUserName());
                        caseVO.setCreatorId(user.getUserId());
                        caseVO.setModifier(user.getUserName());
                        caseVO.setModifierId(user.getUserId());
                    }
                    newSchedulerCaseVOS.add(caseVO);
                }
                automaticSchedulerRepository.batchInsert(newSchedulerCaseVOS);
            }
        }
    }

    public void submitAnalysisAutomatic(SubmitAnalysisAutomaticCommand command) {
        this.submitAnalysisAutomatic(command, command.getTransactor());
    }

    private void submitAnalysisAutomaticCommand(SubmitAnalysisAutomaticCommand command) {
        log.info("submitAnalysisAutomaticCommand >>> {}", JSON.toJSONString(command));
        SubmitAnalysisAutomaticEvent event = automaticSourceRecordDomainConverter.convert(command);
        automaticSourceRecordRepository.submitAnalysisAutomatic(event);
        AutomaticSourceRecordEntityDO entityDO = new AutomaticSourceRecordEntityDO();
        entityDO.setCode(event.getCode());
        entityDO.setStatus(AutomaticStatusEnum.SUCCESS);
        entityDO.setLastAutomaticSourceLogCode(event.getAutomaticSourceLogCode());
        entityDO.setFileName(event.getFileName());
        entityDO.setCommitId(event.getCommitId());
        emit(TestEventEnums.TEST_AUTOMATIC_ANALYTIC.toReactiveId(event.getProductCode()), entityDO);
        apply(event);
    }

    private void automaticSuccessCommand(AutomaticSuccessCommand command) {
        log.info("automaticSuccessCommand >>> {}", JSON.toJSONString(command));
        AutomaticSuccessEvent event = automaticSourceRecordDomainConverter.convert(command);
        automaticSourceRecordRepository.automaticSuccess(event);
        AutomaticSourceRecordEntityDO entityDO = automaticSourceRecordDomainConverter.convert(event);
        entityDO.setStatus(AutomaticStatusEnum.SUCCESS);
        emit(TestEventEnums.TEST_AUTOMATIC_ANALYTIC.toReactiveId(event.getProductCode()), entityDO);
        apply(event);
    }

    public void deleteAutomaticRecord(DeleteAutomaticRecordCommand command) {
        log.info("DeleteAutomaticRecordCommand>>>>>>>>>>>>>>>>aggregateId = :{}", command.getAggregateId());
        DeleteAutomaticRecordEvent event = new DeleteAutomaticRecordEvent();
        event.setCode(command.getAggregateId());
        event.setAggregateId(command.getAggregateId());
        event.setOccurred(command.getOccurred());
        event.setTransactor(command.getTransactor());
        automaticSourceRecordRepository.deleteAutomaticRecord(event);
        apply(event);
        AUTOMATIC_ANALYTIC_EXECUTOR.execute(() -> {
            //查询登记库下 所有用例
            List<TestcaseVO> listTestCase = testcaseRepository.ListCaseByAutomaticCode(command.getCode());
            if (CollectionUtil.isNotEmpty(listTestCase)) {
                for (TestcaseVO vo : listTestCase) {
                    DeleteTestcaseCommand deleteTestcaseCommand = new DeleteTestcaseCommand(vo.getCode());
                    deleteTestcaseCommand.setAttribute(vo.getAttribute());
                    deleteTestcaseCommand.setTransactor(command.getTransactor());
                    deleteTestcaseCommand.setType(vo.getType());
                    testcaseCommandDomainService.deleteTestcaseCommand(deleteTestcaseCommand);
                }
            }
        });
    }

    public void deleteTempFileName(DeleteTempFileNameCommand command) {
        log.info("DeleteTempFileNameCommand>>>>>>>>>>>>>>> = :{}", JsonUtil.toJSON(command));
        DeleteTempFileNameEvent event = new DeleteTempFileNameEvent();
        event.setCode(command.getAggregateId());
        event.setAggregateId(command.getAggregateId());
        event.setOccurred(command.getOccurred());
        event.setTransactor(command.getTransactor());
        event.setLogCode(command.getLogCode());
        automaticSourceRecordRepository.deleteTempFileName(event);
        apply(event);
    }

    private void addStringName(List<Map<String, String>> addressAndFileNameList, String st, String address) {
        if (StringUtil.isNotBlank(st)) {
            String[] fileName = st.split(",");
            for (String name : fileName) {
                Map<String, String> map = new HashMap<>();
                map.put("name", name);
                map.put("addressAndName", address + name);
                addressAndFileNameList.add(map);
            }
        }
    }

    @Async
    public void downloadAutomaticNew(AutomaticRecordVO automaticRecordVO, String generateId, User transactor) {
        if (Objects.nonNull(automaticRecordVO)) {
            List<String> extendJar = ztoOssService.getListObjectKey(automaticRecordVO.getBucketName(), automaticRecordVO.getAddress() + "lib/ext");
            List<String> allJar = ztoOssService.getListObjectKey(automaticRecordVO.getBucketName(), automaticRecordVO.getAddress() + "lib");
            List<String> csvJar = ztoOssService.getListObjectKey(automaticRecordVO.getBucketName(), automaticRecordVO.getAddress() + "data");
            List<String> thirdJar = allJar.stream().filter(item -> !extendJar.contains(item)).collect(Collectors.toList());

            List<Map<String, String>> addressAndFileNameList = new ArrayList<>();
            addStringName(addressAndFileNameList, automaticRecordVO.getFileName(), automaticRecordVO.getAddress());
            String msg = null;
            Map<String, String> map = new HashMap<>();
            try {
                map = ztoOssService.downloadFileToZip(automaticRecordVO.getBucketName(), addressAndFileNameList, automaticRecordVO.getName(), csvJar, thirdJar, extendJar);
            } catch (IOException e) {
                msg = e.getMessage();
                e.printStackTrace();
            }
            FinishJobCommand finish = new FinishJobCommand(generateId);
            if (!map.isEmpty()) {
                finish.setSuccess(true);
                finish.setRemoteFileId(map.get("url"));
                finish.setFileSize(Long.valueOf(map.get("size")));
            } else {
                finish.setSuccess(false);
            }

            finish.setErrorMsg(msg);
            finish.setTransactor(transactor);
            asyncImpExpService.finishJob(finish);
        }
    }

    public void editAutomaticPersonLiable(EditAutomaticPersonLiableCommand command) {
        log.info("EditAutomaticPersonLiableCommand>>>>>>>>>>>>>>> = :{}", JsonUtil.toJSON(command));
        EditAutomaticPersonLiableEvent event = new EditAutomaticPersonLiableEvent();
        event.setCode(command.getAggregateId());
        event.setAggregateId(command.getAggregateId());
        event.setOccurred(command.getOccurred());
        event.setTransactor(command.getTransactor());
        event.setPersonLiable(command.getPersonLiable());
        event.setPersonLiableId(command.getPersonLiableId());
        automaticSourceRecordRepository.editAutomaticPersonLiable(event);
        apply(event);
    }

    public void getFileName(AutomaticRecordVO automaticRecordVO) {
        List<String> extendJar = ztoOssService.getListObjectKey(automaticRecordVO.getBucketName(), automaticRecordVO.getAddress() + "lib/ext");
        List<String> thirdJar = ztoOssService.getListObjectKey(automaticRecordVO.getBucketName(), automaticRecordVO.getAddress() + "lib");
        thirdJar.removeAll(extendJar);
        List<String> csvJar = ztoOssService.getListObjectKey(automaticRecordVO.getBucketName(), automaticRecordVO.getAddress() + "data");
        automaticRecordVO.setDataFileAddress(StringUtils.join(csvJar, ","));
        automaticRecordVO.setThirdJarAddress(StringUtils.join(thirdJar, ","));
        automaticRecordVO.setExtendJarAddress(StringUtils.join(extendJar, ","));
    }

    public void submitAutomaticRecordAnalysis(AnalysisAutomaticRecordVO vo, AutomaticRecordTypeEnum type) {
        AUTOMATIC_ANALYTIC_EXECUTOR.execute(() -> {
            boolean failed = jenkinsService.analyse(vo, type);
            if (failed) {
                AutomaticSourceRecordEntityDO entityDO = new AutomaticSourceRecordEntityDO();
                entityDO.setCode(vo.getCode());
                entityDO.setStatus(AutomaticStatusEnum.ERROR);
                entityDO.setFailInformation("提交Jenkins解析请求失败！");
                automaticSourceRecordRepository.update(entityDO);
            }
        });
    }

    public void analysisAutomaticRecord(AnalysisAutomaticRecordVO vo) {
        AUTOMATIC_ANALYTIC_EXECUTOR.execute(() -> {
            log.info("Jenkins解析登记库 >>> {}", vo);
            AutomaticSourceRecordEntityDO entityDO = new AutomaticSourceRecordEntityDO();
            entityDO.setCode(vo.getCode());
            entityDO.setProductCode(vo.getProductCode());
            entityDO.setGmtModified(new Date());
            if (AutomaticStatusEnum.FAIL.equals(vo.getStatus()) || AutomaticStatusEnum.ERROR.equals(vo.getStatus())) {
                entityDO.setStatus(AutomaticStatusEnum.FAIL);
                entityDO.setFailInformation("Jenkins任务解析失败！");
                if (StringUtils.isNotBlank(vo.getOssPath())) {
                    entityDO.setErrorLogFile(jenkinsService.getErrorLogFile(vo.getOssPath()));
                }
            }
            List<AutomaticNode> automaticNodeList = null;
            if (AutomaticStatusEnum.SUCCESS.equals(vo.getStatus())) {
                try {
                    automaticNodeList = jenkinsService.getCallbackFileContent(vo.getFilename());
                    if (vo.getInsert()) {
                        entityDO.setCommitId(vo.getCommitId());
                    }
                } catch (Exception e) {
                    log.error("读取解析文件异常 {}", vo.getFilename(), e);
                    entityDO.setStatus(AutomaticStatusEnum.FAIL);
                    entityDO.setFailInformation(vo.getFilename() + "解析异常！");
                }
            }
            automaticSourceRecordRepository.update(entityDO);
            if (StringUtils.isNotEmpty(vo.getLogCode())) {
                AutomaticSourceLogEntityDO logEntityDO = new AutomaticSourceLogEntityDO();
                logEntityDO.setCode(vo.getLogCode());
                logEntityDO.setCommitId(vo.getCommitId());
                logEntityDO.setFailInformation(entityDO.getFailInformation());
                logEntityDO.setErrorLogFile(entityDO.getErrorLogFile());
                if (null != entityDO.getStatus()) {
                    logEntityDO.setStatus(entityDO.getStatus().name());
                }
                automaticSourceRecordRepository.update(logEntityDO);
            }
            if (AutomaticStatusEnum.FAIL.equals(entityDO.getStatus())) {
                emit(TestEventEnums.TEST_AUTOMATIC_ANALYTIC.toReactiveId(entityDO.getProductCode()), entityDO);
                return;
            }
            if (vo.getInsert()) {
                this.automaticNodeToCase(
                        automaticNodeList,
                        vo.getCode(),
                        vo.getTransactor(),
                        vo.getProductCode(),
                        vo.getTestcaseCode(),
                        vo.getPath());
            } else {
                this.automaticNodeToCaseEdit(
                        automaticNodeList,
                        vo.getCode(),
                        vo.getTransactor(),
                        vo.getProductCode(),
                        vo.getTestcaseCode(),
                        vo.getPath(),
                        vo.getLogCode());
            }
        });
    }

    public Boolean checkFileSize(String url) {
        if (StringUtil.isEmpty(url)) {
            throw new ServiceException("URL不能为空");
        }
        String bucketName = StringUtils.substringBefore(
                url.replace(qcConfigBasicService.getAmazonS3Config().getEndPoint() + "/", ""),
                "/");
        String fileName = StringUtils.substringAfter(
                url.replace(qcConfigBasicService.getAmazonS3Config().getEndPoint() + "/", ""),
                "/");
        if (StringUtil.isEmpty(bucketName) || StringUtil.isEmpty(fileName)) {
            throw new ServiceException("存储桶或文件不存在");
        }
        long size = ztoOssService.getFileSize(bucketName, fileName);
        if (size > qcConfigBasicService.getCheckFileSize()) {
            return false;
        }
        return true;
    }

    public void analysisAutomaticAbort() {
        log.info("开始终止解析登记库超时任务");
        int duration = qcConfigBasicService.getJobAnalysisAutomaticRecordAbortDuration();
        AnalysisAutomaticRecordAbortQuery query = new AnalysisAutomaticRecordAbortQuery();
        query.setRecordAbortSecond(duration);
        query.setTypeList(Arrays.asList(AutomaticStatusEnum.IN_PROGRESS, AutomaticStatusEnum.ANALYSIS));
        List<AutomaticRecordVO> vos = automaticSourceRecordQueryDomainService.analysisAutomaticRecordAbortQuery(query);
        vos.forEach(this::analysisAutomaticRecordAbort);
    }

    private void analysisAutomaticRecordAbort(AutomaticRecordVO vo) {
        AutomaticSourceRecordEntityDO entityDO = new AutomaticSourceRecordEntityDO();
        entityDO.setCode(vo.getCode());
        entityDO.setStatus(AutomaticStatusEnum.FAIL);
        entityDO.setFailInformation("解析超过一小时，自动默认失败");
        entityDO.setGmtModified(new Date());
        entityDO.setModifier(vo.getModifier());
        entityDO.setModifierId(vo.getModifierId());
        automaticSourceRecordRepository.updateByPrimaryKeySelective(entityDO);
        log.info("-----失败返回通道数据---------" + JsonUtil.toJSON(entityDO));
        THREAD_POOL_EMITTER.execute(() -> {
            try {
                Thread.sleep(3000);
                emit(TestEventEnums.TEST_AUTOMATIC_ANALYTIC.toReactiveId(vo.getProductCode()), entityDO);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
        log.info("-----失败返回通道数据完成---------" + JsonUtil.toJSON(entityDO));
    }

    public void analysisGitJmeter(AnalysisAutomaticRecordVO vo) {
        AUTOMATIC_ANALYTIC_EXECUTOR.execute(() -> {
            log.info("Jenkins解析登记库 >>> {}", vo);
            AutomaticSourceRecordEntityDO entityDO = new AutomaticSourceRecordEntityDO();
            entityDO.setCode(vo.getCode());
            entityDO.setProductCode(vo.getProductCode());
            entityDO.setBucketName("autojmx");
            entityDO.setGmtModified(new Date());
            if (AutomaticStatusEnum.FAIL.equals(vo.getStatus()) || AutomaticStatusEnum.ERROR.equals(vo.getStatus())) {
                entityDO.setStatus(AutomaticStatusEnum.FAIL);
                entityDO.setFailInformation("Jenkins任务解析失败！");
                if (StringUtils.isNotBlank(vo.getOssPath())) {
                    entityDO.setErrorLogFile(jenkinsService.getErrorLogFile(vo.getOssPath()));
                }
            }
            if (AutomaticStatusEnum.SUCCESS.equals(vo.getStatus()) && vo.getInsert()) {
                entityDO.setCommitId(vo.getCommitId());
                entityDO.setFileName(vo.getFilename());
            }
            automaticSourceRecordRepository.update(entityDO);
            if (StringUtils.isNotEmpty(vo.getLogCode())) {
                AutomaticSourceLogEntityDO logEntityDO = new AutomaticSourceLogEntityDO();
                logEntityDO.setCode(vo.getLogCode());
                logEntityDO.setCommitId(vo.getCommitId());
                logEntityDO.setFileName(vo.getFilename());
                logEntityDO.setFailInformation(entityDO.getFailInformation());
                logEntityDO.setErrorLogFile(entityDO.getErrorLogFile());
                if (null != entityDO.getStatus()) {
                    logEntityDO.setStatus(entityDO.getStatus().name());
                }
                automaticSourceRecordRepository.update(logEntityDO);
            }
            if (AutomaticStatusEnum.FAIL.equals(entityDO.getStatus())) {
                emit(TestEventEnums.TEST_AUTOMATIC_ANALYTIC.toReactiveId(entityDO.getProductCode()), entityDO);
                return;
            }
            if (vo.getInsert()) {
                AddAutomaticRecordCommand command = new AddAutomaticRecordCommand(vo.getCode());
                command.setProductCode(vo.getProductCode());
                command.setBucketName("autojmx");
                command.setAddress(vo.getCommitId() + "/" + vo.getCode() + "/");
                command.setFileName(vo.getFilename());
                command.setTestcaseCode(vo.getTestcaseCode());
                command.setPath(vo.getPath());
                command.setTransactor(vo.getTransactor());
                this.sendCaseCommand(command);
            } else {
                EditAutomaticRecordCommand command = new EditAutomaticRecordCommand(vo.getCode());
                command.setProductCode(vo.getProductCode());
                command.setBucketName("autojmx");
                command.setAddress(vo.getCommitId() + "/" + vo.getCode() + "/");
                command.setTestcaseCode(vo.getTestcaseCode());
                command.setPath(vo.getPath());
                command.setTransactor(vo.getTransactor());
                this.sendEditCaseCommand(command, vo.getFilename(), vo.getLogCode());
            }
        });
    }

    @Async
    public void analysisAndSubmit(AutomaticSourceRecordEntityDO source, User transactor, Long gitProjectId) {
        //解析
        if (source.getType().equals(AutomaticRecordTypeEnum.JMETER_GIT)) {
            log.info(gitProjectId + "_doAnalysisJmeterGit_code_{}_user_{}", source.getCode(), JsonUtil.toJSON(transactor));
            doAnalysisJmeterGit(source.getCode(), transactor);
        }
        if (source.getType().equals(AutomaticRecordTypeEnum.TESTNG)) {
            log.info(gitProjectId + "_doAnalysisTESTNG_code_{}_user_{}", source.getCode(), JsonUtil.toJSON(transactor));
            doAnalysisTestNG(source.getCode(), transactor);
        }
        if (source.getType().equals(AutomaticRecordTypeEnum.PYTEST)) {
            log.info(gitProjectId + "_doAnalysisPYTEST_code_{}_user_{}", source.getCode(), JsonUtil.toJSON(transactor));
            doAnalysisPYTEST(source.getCode(), transactor);
        }
    }

    private void doAnalysisJmeterGit(String code, User user) {
        if (StringUtil.isBlank(code)) {
            log.info("doAnalysisJmeterGit_code_is_null!");
            return;
        }
        AutomaticRecordQuery automaticRecordQuery = new AutomaticRecordQuery();
        automaticRecordQuery.setCode(code);
        automaticRecordQuery.setTransactor(user);
        AutomaticRecordVO automaticRecordVO = automaticSourceRecordQueryDomainService.automaticRecordQuery(automaticRecordQuery);
        if (null == automaticRecordVO) {
            log.info(code + "_doAnalysisJmeterGit_automaticRecordVO_is_null");
            return;
        }
        if (AutomaticStatusEnum.IN_PROGRESS.equals(automaticRecordVO.getStatus())) {
            log.info(code + "_doAnalysisJmeterGit_is_IN_PROGRESS");
            return;
        }
        AutomaticRecordLogQuery automaticRecordLogQuery = new AutomaticRecordLogQuery();
        automaticRecordLogQuery.setCode(code);
        List<AutomaticRecordLogVO> automaticRecordLogVOList = automaticSourceRecordQueryDomainService.automaticRecordLogQuery(automaticRecordLogQuery);
        List<String> checkStatus = Arrays.asList(
                AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name(),
                AutomaticStatusEnum.IN_PROGRESS.name(),
                AutomaticStatusEnum.ANALYSIS.name());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(automaticRecordLogVOList)
                && checkStatus.contains(automaticRecordLogVOList.get(0).getStatus())) {
            log.info(code + "_doAnalysisJmeterGit_is_ANALYSISSUCCESSNOTCONFIRM");
            return;
        }
        String aggregateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddAutomaticRecordLogCommand command = new AddAutomaticRecordLogCommand(code);
        command.setTransactor(user);
        command.setProductCode(automaticRecordVO.getProductCode());
        command.setAutomaticSourceCode(aggregateId);
        command.setAddress(automaticRecordVO.getAddress());
        command.setStatus(AutomaticStatusEnum.IN_PROGRESS.name());
        command.setAnalyticMethod(AutomaticRecordAnalyticMethodEnum.AUTO);
        addAutomaticRecordLogCommand(command);
        AnalysisAutomaticRecordVO vo = new AnalysisAutomaticRecordVO();
        vo.setGitUrl(automaticRecordVO.getAddress());
        vo.setBranchName(automaticRecordVO.getBranch());
        vo.setWorkDir(automaticRecordVO.getWorkSpace());
        vo.setProductCode(automaticRecordVO.getProductCode());
        vo.setCode(automaticRecordVO.getCode());
        vo.setTestcaseCode(automaticRecordVO.getTestcaseCode());
        vo.setLogCode(aggregateId);
        vo.setInsert(false);
        vo.setTransactor(command.getTransactor());
        if (null != automaticRecordVO.getTestcaseVO()) {
            vo.setPath(automaticRecordVO.getTestcaseVO().getPath());
        }
        vo.setDataPath(automaticRecordVO.getDataFileAddress());
        vo.setLibPath(automaticRecordVO.getExtendJarAddress());
        submitAutomaticRecordAnalysis(vo, AutomaticRecordTypeEnum.JMETER_GIT);
    }

    private void doAnalysisTestNG(String code, User user) {
        if (StringUtil.isBlank(code)) {
            log.info("doAnalysisTestNG_code_is_null!");
            return;
        }
        AutomaticRecordQuery query = new AutomaticRecordQuery();
        query.setTransactor(user);
        query.setCode(code);
        AutomaticRecordVO automaticRecordVO = automaticSourceRecordQueryDomainService.automaticRecordQuery(query);
        if (Objects.isNull(automaticRecordVO)) {
            log.info(code + "_doAnalysisTestNG_automaticRecordVO_is_null_user_{}", JsonUtil.toJSON(user));
        }
        if (automaticRecordVO.getStatus().name().equals(AutomaticStatusEnum.IN_PROGRESS.name())) {
            log.info(code + "_doAnalysisTestNG_IN_PROGRESS_user_{}", JsonUtil.toJSON(user));
            return;
        }
        AutomaticRecordLogQuery automaticRecordLogQuery = new AutomaticRecordLogQuery();
        automaticRecordLogQuery.setCode(code);
        List<AutomaticRecordLogVO> voList = automaticSourceRecordQueryDomainService.automaticRecordLogQuery(automaticRecordLogQuery);
        if (CollectionUtil.isNotEmpty(voList)
                && (voList.get(0).getStatus().equals(AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name())
                || voList.get(0).getStatus().equals(AutomaticStatusEnum.IN_PROGRESS.name())
                || voList.get(0).getStatus().equals(AutomaticStatusEnum.ANALYSIS.name()))) {
            log.info(code + "_doAnalysisTestNG_ANALYSISSUCCESSNOTCONFIRM_user_{}", JsonUtil.toJSON(user));
            return;
        }
        String logCode = aggregateIdGenerateService.generateId(AggregateType.TEST_CASE);
        AddAutomaticRecordLogCommand addAutomaticRecordLogCommand = new AddAutomaticRecordLogCommand(code);
        addAutomaticRecordLogCommand.setTransactor(user);
        addAutomaticRecordLogCommand.setProductCode(automaticRecordVO.getProductCode());
        addAutomaticRecordLogCommand.setAutomaticSourceCode(logCode);
        addAutomaticRecordLogCommand.setAddress(automaticRecordVO.getAddress());
        addAutomaticRecordLogCommand.setStatus(AutomaticStatusEnum.IN_PROGRESS.name());
        addAutomaticRecordLogCommand.setAnalyticMethod(AutomaticRecordAnalyticMethodEnum.AUTO);
        addAutomaticRecordLogCommand(addAutomaticRecordLogCommand);
        AnalysisAutomaticRecordVO vo = new AnalysisAutomaticRecordVO();
        vo.setGitUrl(automaticRecordVO.getAddress());
        vo.setBranchName(automaticRecordVO.getBranch());
        vo.setWorkDir(automaticRecordVO.getWorkSpace());
        vo.setProductCode(automaticRecordVO.getProductCode());
        vo.setCode(automaticRecordVO.getCode());
        vo.setTestcaseCode(automaticRecordVO.getTestcaseCode());
        vo.setLogCode(logCode);
        vo.setInsert(false);
        vo.setTransactor(user);
        submitAutomaticRecordAnalysis(vo, AutomaticRecordTypeEnum.TESTNG);
    }

    private void doAnalysisPYTEST(String code, User user) {
        if (StringUtil.isBlank(code)) {
            log.info("doAnalysisPYTEST_code_is_null");
            return;
        }
        AutomaticRecordQuery automaticRecordQuery = new AutomaticRecordQuery();
        automaticRecordQuery.setCode(code);
        automaticRecordQuery.setTransactor(user);
        AutomaticRecordVO automaticRecordVO = automaticSourceRecordQueryDomainService.automaticRecordQuery(automaticRecordQuery);
        if (null == automaticRecordVO) {
            log.info(code + "_doAnalysisPYTEST_automaticRecordVO_is_null");
            return;
        }
        if (AutomaticStatusEnum.IN_PROGRESS.equals(automaticRecordVO.getStatus())) {
            log.info(code + "_doAnalysisPYTEST_is_IN_PROGRESS");
            return;
        }
        AutomaticRecordLogQuery automaticRecordLogQuery = new AutomaticRecordLogQuery();
        automaticRecordLogQuery.setCode(code);
        List<AutomaticRecordLogVO> automaticRecordLogVOList =
                automaticSourceRecordQueryDomainService.automaticRecordLogQuery(automaticRecordLogQuery);
        List<String> checkStatus = Arrays.asList(AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name(),
                AutomaticStatusEnum.IN_PROGRESS.name(), AutomaticStatusEnum.ANALYSIS.name());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(automaticRecordLogVOList)
                && checkStatus.contains(automaticRecordLogVOList.get(0).getStatus())) {
            log.info(code + "_doAnalysisPYTEST_is_ANALYSISSUCCESSNOTCONFIRM");
            return;
        }
        String aggregateId = aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE);
        AddAutomaticRecordLogCommand command = new AddAutomaticRecordLogCommand(code);
        command.setTransactor(user);
        command.setProductCode(automaticRecordVO.getProductCode());
        command.setAutomaticSourceCode(aggregateId);
        command.setAddress(automaticRecordVO.getAddress());
        command.setStatus(AutomaticStatusEnum.IN_PROGRESS.name());
        command.setAnalyticMethod(AutomaticRecordAnalyticMethodEnum.AUTO);
        addAutomaticRecordLogCommand(command);
        AnalysisAutomaticRecordVO vo = new AnalysisAutomaticRecordVO();
        vo.setGitUrl(automaticRecordVO.getAddress());
        vo.setBranchName(automaticRecordVO.getBranch());
        vo.setWorkDir(automaticRecordVO.getWorkSpace());
        vo.setProductCode(automaticRecordVO.getProductCode());
        vo.setCode(automaticRecordVO.getCode());
        vo.setTestcaseCode(automaticRecordVO.getTestcaseCode());
        vo.setLogCode(aggregateId);
        vo.setInsert(false);
        vo.setTransactor(command.getTransactor());
        if (null != automaticRecordVO.getTestcaseVO()) {
            vo.setPath(automaticRecordVO.getTestcaseVO().getPath());
        }
        submitAutomaticRecordAnalysis(vo, AutomaticRecordTypeEnum.PYTEST);
    }

    /**
     * 自动确认登记库解析结果
     * 登记库配置自动解析开关 == TRUE && 登记库最近一次解析方式 == AUTO
     *
     * @param code
     * @param user
     */
    private void doSubmit(String code, User user) {
        if (StringUtil.isBlank(code)) {
            log.info("doSubmit_code_is_null");
            return;
        }
        AutomaticSourceRecordEntityDO entityDO = automaticSourceRecordRepository.selectByPrimaryKey(code);
        if (null == entityDO) {
            log.info(code + "_doSubmit_entityDO_is_null");
            return;
        }
        if (!entityDO.getAutoAnalysisFlag()) {
            log.info(code + "_doSubmit_entity_is_not_autoAnalysis");
            return;
        }
        AutomaticRecordLogQuery automaticRecordLogQuery = new AutomaticRecordLogQuery();
        automaticRecordLogQuery.setCode(code);
        List<AutomaticRecordLogVO> logVOList = automaticSourceRecordRepository.listAutomaticRecordLogVO(automaticRecordLogQuery);
        if (CollectionUtil.isEmpty(logVOList)) {
            log.info(code + "_doSubmit_logVOList_is_null");
            return;
        }
        if (!logVOList.get(0).getAnalyticMethod().equals(AutomaticRecordAnalyticMethodEnum.AUTO)) {
            log.info(code + "_doSubmit_latestLog_not_auto");
            return;
        }
        String automaticSourceLogCode = logVOList.get(0).getCode();
        //查询log状态  如果不是终态 不可以修改
        AutomaticRecordLogByCodeQuery query = new AutomaticRecordLogByCodeQuery();
        query.setCode(automaticSourceLogCode);
        AutomaticRecordLogVO automaticRecordLogVO = automaticSourceRecordQueryDomainService.automaticRecordLogByCodeQuery(query);
        if (!automaticRecordLogVO.getStatus().equals(AutomaticStatusEnum.ANALYSISSUCCESSNOTCONFIRM.name())
                && !automaticRecordLogVO.getStatus().equals(AutomaticStatusEnum.FAIL.name())) {
            log.info(code + "_doSubmit_code_is_CONFIRMING");
            return;
        }
        editAutomaticLogStatusCommand(automaticSourceLogCode);
        SubmitAnalysisAutomaticCommand command = new SubmitAnalysisAutomaticCommand(code);
        command.setAutomaticSourceLogCode(automaticSourceLogCode);
        command.setCode(code);
        command.setTransactor(user);
        command.setAnalyticMethod(AutomaticRecordAnalyticMethodEnum.AUTO);
        submitAnalysisAutomaticRecord(command);
    }
}
