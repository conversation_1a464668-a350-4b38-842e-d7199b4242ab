package com.zto.devops.qc.domain.converter;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.issue.entity.CurrentHandlerVO;
import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import com.zto.devops.qc.client.model.parameter.TaskBaseParameter;
import com.zto.devops.qc.client.model.parameter.TaskResultParameter;
import com.zto.devops.qc.client.model.relevantUser.query.MyIssueQuery;
import com.zto.devops.qc.client.model.relevantUser.query.MyTaskVO;
import com.zto.devops.qc.domain.model.RelevantUser;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Mapper(componentModel = "spring")
public interface RelevantUserConverter {

    RelevantUserConverter INSTANCE = Mappers.getMapper(RelevantUserConverter.class);

    RelevantUser convert(RelevantUserVO vo);
    List<RelevantUser> convert(Collection<RelevantUserVO> vo);
    CurrentHandlerVO convertCurrentHandlerVO(User user);
//    Set<RelevantUser> convertList(Collection<RelevantUserEntity> relevantUserEntities);

//    RelevantUserVO convert(RelevantUserEntity entity);
//    Set<RelevantUserVO> convert(List<RelevantUserEntity> relevantUserEntities);

    CurrentHandlerVO convertCurrentHandlerVO(RelevantUser currentHandlerVO);
    Set<CurrentHandlerVO> convertCurrentHandlerVO(Collection<RelevantUser> currentHandlerVO);


    TaskBaseParameter convertTask(MyIssueQuery taskBaseParameter);



    @Mapping(target = "statusDesc",expression = "java(com.zto.devops.qc.client.enums.issue.IssueStatus.getValueByName(taskResultParameter.getStatus()))")
    @Mapping(target = "priorityDesc",expression = "java(com.zto.devops.qc.client.enums.issue.IssuePriority.getValueByName(taskResultParameter.getPriority()))")
    @Mapping(target = "findVersion.code" ,source = "findVersionCode")
    @Mapping(target = "findVersion.name" ,source = "findVersionName")
    @Mapping(target = "developer.userId" ,source = "developUserId")
    @Mapping(target = "developer.userName" ,source = "developUserName")
    @Mapping(target = "tester.userId" ,source = "testUserId")
    @Mapping(target = "tester.userName" ,source = "testUserName")
    @Mapping(target = "handlerId",source = "handleUserId")
    @Mapping(target = "handler",source = "handleUserName")
    @Mapping(target = "findUserId",source = "findUserId")
    @Mapping(target = "domainEnum",expression = "java(com.zto.devops.framework.client.enums.DomainEnum.ISSUE)")
    @Mapping(target = "product.code",source = "productCode")
    @Mapping(target = "product.name",source = "productName")
    MyTaskVO convertIssue(TaskResultParameter taskResultParameter);

    Set<RelevantUserVO> convert(List<RelevantUserVO> relevantUserEntities);


}

