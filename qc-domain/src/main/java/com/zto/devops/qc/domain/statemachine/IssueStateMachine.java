package com.zto.devops.qc.domain.statemachine;

import com.alibaba.cola.statemachine.StateMachine;
import com.alibaba.cola.statemachine.StateMachineFactory;
import com.alibaba.cola.statemachine.builder.StateMachineBuilder;
import com.alibaba.cola.statemachine.builder.StateMachineBuilderFactory;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.fsm.Event;
import com.zto.devops.framework.common.fsm.State;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.issue.IssueEditFieldEnum;
import com.zto.devops.qc.client.enums.issue.IssueEvent;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.rpc.ProductRoleEnum;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 缺陷状态机定义
 *
 * <AUTHOR>
 */
@Configuration
public class IssueStateMachine {

    // 状态机名称
    private final static String MACHINE_ID = "issue";

    // 报告人可编辑的字段
    public final static String FIND_EDITABLE_FIELDS = "find_editable_fields";
    // 开发可编辑的字段
    public final static String DEVELOP_EDITABLE_FIELDS = "develop_editable_fields";
    // 测试可编辑的字段
    public final static String TEST_EDITABLE_FIELDS = "test_editable_fields";
    // 是否记录为已办
    public final static String HANDLED = "handled";

    public final static String ADMIN_EDIT_FIELDS= "admin_edit_fields";

    public final static String GLOBAL_PERM_CAN_MODIFY_ISSUE = "globalpermCanModifyIssue";

    /**
     * 超级管理员标识
     */
//    private static final String SUPPER_USER_PERMISSION_KEY = "globalpermCanWriteProd";

    // 状态
    private final static State START_STATE = IssueStatus.START.toState(); // 起点 非状态
    private final static State END_STATE = IssueStatus.END.toState(); // 终点 非状态
    private final static State WAIT_FIX_STATE = IssueStatus.WAIT_FIX.toState();
    private final static State DELAY_FIX_STATE = IssueStatus.DELAY_FIX.toState();
    private final static State FIXING_STATE = IssueStatus.FIXING.toState();
    private final static State REJECTED_STATE = IssueStatus.REJECTED.toState();
    private final static State TESTING_STATE = IssueStatus.TESTING.toState();
    private final static State CLOSED_STATE = IssueStatus.CLOSED.toState();
    private final static State REMOVED_STATE = IssueStatus.REMOVED.toState();


    // 事件
    private final static Event START_EVENT = IssueEvent.START.toEvent(); // 起点 非事件
    private final static Event END_EVENT = IssueEvent.END.toEvent(); // 终点 非事件
    private final static Event REJECT_EVENT = IssueEvent.REJECT.toEvent();
    private final static Event TRANSFER_EVENT = IssueEvent.TRANSFER.toEvent();
    private final static Event REMOVE_EVENT = IssueEvent.REMOVE.toEvent();
    private final static Event RETURN_EVENT = IssueEvent.RETURN.toEvent();
    private final static Event DELAY_EVENT = IssueEvent.DELAY.toEvent();
    private final static Event FIX_EVENT = IssueEvent.FIX.toEvent();
    private final static Event DELIVER_EVENT = IssueEvent.DELIVER.toEvent();
    private final static Event REOPEN_EVENT = IssueEvent.REOPEN.toEvent();
    private final static Event CONFIRM_CLOSE_EVENT = IssueEvent.CONFIRM_CLOSE.toEvent();
    private final static Event TEST_PASS_CLOSE_EVENT = IssueEvent.TEST_PASS_CLOSE.toEvent();

    static {

        // 是否会记为已办
        START_EVENT.addMeta(HANDLED, Boolean.TRUE);
        REJECT_EVENT.addMeta(HANDLED, Boolean.TRUE);
        TRANSFER_EVENT.addMeta(HANDLED, Boolean.TRUE);
        RETURN_EVENT.addMeta(HANDLED, Boolean.TRUE);
        DELAY_EVENT.addMeta(HANDLED, Boolean.TRUE);
        FIX_EVENT.addMeta(HANDLED, Boolean.TRUE);
        DELIVER_EVENT.addMeta(HANDLED, Boolean.TRUE);
        REOPEN_EVENT.addMeta(HANDLED, Boolean.TRUE);
        CONFIRM_CLOSE_EVENT.addMeta(HANDLED, Boolean.TRUE);


        // 报告人可编辑的字段 都可以编辑
        WAIT_FIX_STATE.addMeta(FIND_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));
        FIXING_STATE.addMeta(FIND_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));
        TESTING_STATE.addMeta(FIND_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));
        CLOSED_STATE.addMeta(FIND_EDITABLE_FIELDS, Collections.singletonList(IssueEditFieldEnum.TAG));
        DELAY_FIX_STATE.addMeta(FIND_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));
        REJECTED_STATE.addMeta(FIND_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));



        // 测试可编辑的字段 都可以编辑
        WAIT_FIX_STATE.addMeta(TEST_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));
        FIXING_STATE.addMeta(TEST_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));
        TESTING_STATE.addMeta(TEST_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));
        CLOSED_STATE.addMeta(TEST_EDITABLE_FIELDS, Collections.singletonList(IssueEditFieldEnum.TAG));
        DELAY_FIX_STATE.addMeta(TEST_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));
        REJECTED_STATE.addMeta(TEST_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));

        //超管可以编辑所有字段
        WAIT_FIX_STATE.addMeta(ADMIN_EDIT_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));
        FIXING_STATE.addMeta(ADMIN_EDIT_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));
        TESTING_STATE.addMeta(ADMIN_EDIT_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));
        CLOSED_STATE.addMeta(ADMIN_EDIT_FIELDS, Collections.singletonList(IssueEditFieldEnum.TAG));
        DELAY_FIX_STATE.addMeta(ADMIN_EDIT_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));
        REJECTED_STATE.addMeta(ADMIN_EDIT_FIELDS, Arrays.asList(IssueEditFieldEnum.values()));



        // 开发可以编辑的字段
        WAIT_FIX_STATE.addMeta(DEVELOP_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.TITLE, IssueEditFieldEnum.DESCRIPTION, IssueEditFieldEnum.FIND_VERSION,
                IssueEditFieldEnum.ROOT_CAUSE, IssueEditFieldEnum.TYPE, IssueEditFieldEnum.FIX_VERSION, IssueEditFieldEnum.REQUIREMENT,
                IssueEditFieldEnum.TAG,IssueEditFieldEnum.SPRINT,IssueEditFieldEnum.APPLICATIONTYPE,IssueEditFieldEnum.CCMANLIST, IssueEditFieldEnum.PlanTime));

        FIXING_STATE.addMeta(DEVELOP_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.TITLE, IssueEditFieldEnum.DESCRIPTION, IssueEditFieldEnum.FIND_VERSION,
                IssueEditFieldEnum.ROOT_CAUSE, IssueEditFieldEnum.TYPE, IssueEditFieldEnum.REQUIREMENT, IssueEditFieldEnum.TAG,
                IssueEditFieldEnum.FIX_VERSION,IssueEditFieldEnum.SPRINT,IssueEditFieldEnum.APPLICATIONTYPE,IssueEditFieldEnum.CCMANLIST, IssueEditFieldEnum.PlanTime));

        TESTING_STATE.addMeta(DEVELOP_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.TITLE, IssueEditFieldEnum.DESCRIPTION, IssueEditFieldEnum.FIND_VERSION,
                IssueEditFieldEnum.ROOT_CAUSE, IssueEditFieldEnum.TYPE, IssueEditFieldEnum.FIX_VERSION, IssueEditFieldEnum.REQUIREMENT,
                IssueEditFieldEnum.TAG,IssueEditFieldEnum.SPRINT,IssueEditFieldEnum.APPLICATIONTYPE,IssueEditFieldEnum.CCMANLIST));

        CLOSED_STATE.addMeta(DEVELOP_EDITABLE_FIELDS, Collections.singletonList(IssueEditFieldEnum.TAG));

        DELAY_FIX_STATE.addMeta(DEVELOP_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.TITLE, IssueEditFieldEnum.DESCRIPTION, IssueEditFieldEnum.FIND_VERSION,
                IssueEditFieldEnum.ROOT_CAUSE, IssueEditFieldEnum.TYPE, IssueEditFieldEnum.FIX_VERSION,
                IssueEditFieldEnum.REQUIREMENT, IssueEditFieldEnum.TAG,IssueEditFieldEnum.SPRINT,IssueEditFieldEnum.APPLICATIONTYPE,IssueEditFieldEnum.CCMANLIST, IssueEditFieldEnum.PlanTime));

        REJECTED_STATE.addMeta(DEVELOP_EDITABLE_FIELDS, Arrays.asList(IssueEditFieldEnum.TITLE,
                IssueEditFieldEnum.DESCRIPTION, IssueEditFieldEnum.FIND_VERSION,
                IssueEditFieldEnum.ROOT_CAUSE, IssueEditFieldEnum.TYPE, IssueEditFieldEnum.FIX_VERSION,
                IssueEditFieldEnum.REQUIREMENT, IssueEditFieldEnum.TAG,IssueEditFieldEnum.SPRINT,IssueEditFieldEnum.APPLICATIONTYPE,IssueEditFieldEnum.CCMANLIST, IssueEditFieldEnum.PlanTime));


    }

    @Bean("stateMachine")
    public StateMachine<State, Event, IssueContext> buildStateMachine() {
        StateMachineBuilder<State, Event, IssueContext> builder = StateMachineBuilderFactory.create();

        // 起点与终点
        builder.externalTransition().from(START_STATE).to(WAIT_FIX_STATE).on(START_EVENT);
        builder.externalTransition().from(REMOVED_STATE).to(END_STATE).on(END_EVENT);
        builder.externalTransition().from(CLOSED_STATE).to(END_STATE).on(END_EVENT);

        // 待修复
        this.buildWaitFix(builder);

        // 延期修复
        this.buildDelayFix(builder);

        // 修复中
        this.buildFixing(builder);

        // 验证中
        this.buildTesting(builder);

        // 已拒绝
        this.buildRejected(builder);

        // 已关闭
        this.buildClosed(builder);

        builder.build(MACHINE_ID);
        StateMachine<State, Event, IssueContext> stateMachine = StateMachineFactory.get(MACHINE_ID);
        stateMachine.showStateMachine();
        return stateMachine;
    }

    /**
     * 待修复
     */
    private void buildWaitFix(StateMachineBuilder<State, Event, IssueContext> builder) {
        builder.externalTransition().from(WAIT_FIX_STATE).to(REMOVED_STATE).on(REMOVE_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getFindUserId())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        builder.externalTransition().from(WAIT_FIX_STATE).to(FIXING_STATE).on(FIX_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getDevelopUserId()) || isDeveloper(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        builder.externalTransition().from(WAIT_FIX_STATE).to(DELAY_FIX_STATE).on(DELAY_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getDevelopUserId()) || isDeveloper(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        builder.externalTransition().from(WAIT_FIX_STATE).to(REJECTED_STATE).on(REJECT_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getDevelopUserId()) || isDeveloper(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        buildBase(builder, WAIT_FIX_STATE);
    }


    /**
     * 延期修复
     */
    private void buildDelayFix(StateMachineBuilder<State, Event, IssueContext> builder) {
        builder.externalTransition().from(DELAY_FIX_STATE).to(FIXING_STATE).on(FIX_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getDevelopUserId()) || isDeveloper(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        builder.externalTransition().from(DELAY_FIX_STATE).to(FIXING_STATE).on(RETURN_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getTestUserId()) || isTester(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        builder.externalTransition().from(DELAY_FIX_STATE).to(REJECTED_STATE).on(REJECT_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getDevelopUserId()) || isDeveloper(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        buildBase(builder, DELAY_FIX_STATE);
    }

    /**
     * 修复中
     */
    private void buildFixing(StateMachineBuilder<State, Event, IssueContext> builder) {
        builder.externalTransition().from(FIXING_STATE).to(DELAY_FIX_STATE).on(DELAY_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getDevelopUserId()) || isDeveloper(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        builder.externalTransition().from(FIXING_STATE).to(TESTING_STATE).on(DELIVER_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getDevelopUserId()) || isDeveloper(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        builder.externalTransition().from(FIXING_STATE).to(REJECTED_STATE).on(REJECT_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getDevelopUserId()) || isDeveloper(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        buildBase(builder, FIXING_STATE);
    }

    /**
     * 验证中
     */
    private void buildTesting(StateMachineBuilder<State, Event, IssueContext> builder) {
        builder.externalTransition().from(TESTING_STATE).to(WAIT_FIX_STATE).on(RETURN_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getTestUserId()) || isTester(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        builder.externalTransition().from(TESTING_STATE).to(CLOSED_STATE).on(TEST_PASS_CLOSE_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getTestUserId()) || isTester(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        buildBase(builder, TESTING_STATE);
    }

    /**
     * 已拒绝
     */
    private void buildRejected(StateMachineBuilder<State, Event, IssueContext> builder) {
        builder.externalTransition().from(REJECTED_STATE).to(FIXING_STATE).on(RETURN_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getTestUserId()) || isTester(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        builder.externalTransition().from(REJECTED_STATE).to(CLOSED_STATE).on(CONFIRM_CLOSE_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getTestUserId()) || isTester(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
        buildBase(builder, REJECTED_STATE);
    }

    /**
     * 已关闭
     */
    private void buildClosed(StateMachineBuilder<State, Event, IssueContext> builder) {
        builder.externalTransition().from(CLOSED_STATE).to(WAIT_FIX_STATE).on(REOPEN_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getTestUserId()) || isTester(issueContext.getRoles())
                        || Objects.equals(issueContext.getCurrentUserId(), issueContext.getFindUserId())
                        || hasSuperPermission(issueContext.getPermissions()) || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
    }


    /**
     * 构建基础的事件
     */
    private void buildBase(StateMachineBuilder<State, Event, IssueContext> builder, State state) {
        // 流转缺陷
        builder.internalTransition().within(state).on(TRANSFER_EVENT)
                .when(issueContext -> Objects.equals(issueContext.getCurrentUserId(), issueContext.getDevelopUserId()) || isDeveloper(issueContext.getRoles())
                        || Objects.equals(issueContext.getCurrentUserId(), issueContext.getTestUserId()) || isTester(issueContext.getRoles())
                        || hasSuperPermission(issueContext.getPermissions())
                        || isOwnerDeveloperOrOwnerTester(issueContext.getRoles()));
    }

    /**
     * 是否包含管理员权限
     * @param permissions
     * @return
     */
    private boolean hasSuperPermission(List<String> permissions) {
       return CollectionUtil.isNotEmpty(permissions)
//               && (permissions.contains(GLOBAL_PERM_CAN_MODIFY_ISSUE) || permissions.contains(SUPPER_USER_PERMISSION_KEY));
               && permissions.contains(GLOBAL_PERM_CAN_MODIFY_ISSUE);
    }

    private boolean isOwnerDeveloperOrOwnerTester(List<ProductRoleEnum> roles) {
        if (CollectionUtil.isEmpty(roles)) {
            return false;
        }
        return roles.contains(ProductRoleEnum.DEVELOPER_OWNER)
                || roles.contains(ProductRoleEnum.ARCHITECT)
                || roles.contains(ProductRoleEnum.TESTER_OWNER);
    }

    public static boolean isDeveloper(List<ProductRoleEnum> roles) {
        if (CollectionUtil.isEmpty(roles)) {
            return false;
        }
        return roles.contains(ProductRoleEnum.DEVELOPER_OWNER) || roles.contains(ProductRoleEnum.ARCHITECT)
                || roles.contains(ProductRoleEnum.DEVELOPER) || roles.contains(ProductRoleEnum.MAJOR_DEVELOPER_OWNER);
    }

    public static boolean isTester(List<ProductRoleEnum> roles) {
        if (CollectionUtil.isEmpty(roles)) {
            return false;
        }
        return roles.contains(ProductRoleEnum.TESTER_OWNER)
                || roles.contains(ProductRoleEnum.TESTER) || roles.contains(ProductRoleEnum.MAJOR_TESTER_OWNER);
    }

    public static void main(String[] args) {
        StateMachine<State, Event, IssueContext> stateMachine = new IssueStateMachine().buildStateMachine();
        List<Event> events1 = stateMachine.readyEvent(IssueStatus.CLOSED.toState(), new IssueContext());
        if(!events1.contains(IssueEvent.REOPEN.toEvent())){
            throw new ServiceException("当前用户没有权限操作！");
        }

    }

}