package com.zto.devops.qc.domain.service;

import com.zto.devops.qc.domain.gateway.redis.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SceneCookieDomainService {
    @Autowired
    private RedisService redisService;

    public boolean deleteCookie(String type, String key) {
        switch (type) {
            case "testCaseCookie":
                return redisService.delete("testCaseCookie_" + key);
            case "testCaseDbInfo":
                return redisService.delete("testCaseDbInfo_ZBase_Db_Info_" + key);
            default:
                return false;
        }
    }
}
