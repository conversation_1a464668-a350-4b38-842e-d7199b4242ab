package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testPlan.TestPlanTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.model.dto.TestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestPlanEntityDO;
import com.zto.devops.qc.client.model.dto.TmTestPlanRangeEntityDO;
import com.zto.devops.qc.client.model.rpc.project.VersionDeleteEvent;
import com.zto.devops.qc.client.model.rpc.project.VersionEditedEvent;
import com.zto.devops.qc.client.model.testPlan.query.TestPlanListQuery;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.event.ChangeCaseExecuteResultEvent;
import com.zto.devops.qc.client.model.testmanager.plan.event.PlanCaseResultCommentChangedEvent;
import com.zto.devops.qc.client.model.testmanager.plan.query.*;

import java.util.List;
import java.util.Set;

public interface ITmTestPlanRepository {

    boolean checkSendEmailPlan(String planCode);

    void addTestPlan(TmTestPlanVO vo, BaseEvent event);

    void editTestPlan(VersionEditedEvent event);

    void deleteTestPlanByVersionCode(VersionDeleteEvent event);

    List<TmTestPlanEntityDO> selectTmTestPlanEntityDOByPlanCodeList(Set<String> codeList);

    TmTestPlanEntityDO getTestPlanByCode(String code);

    List<TestPlanEntityDO> selectTestPlanByVersionCodeAndTypeList(String versionCode, List<TestPlanTypeEnum> testPlanTypeEnumList);

    List<TestPlanEntityDO> selectTestPlanByVersionCodeAndType(String versionCode);

    TmTestPlanEntityDO selectTmTestPlanByCode(String code);

    TmTestPlanEntityDO getSafePlanCode(String relationPlanCode);

    List<TmTestPlanVO> pageTestPlanList(PlanListQuery query);

    List<TmTestPlanEntityDO> selectByProductCodeAndStatusList(ListPlanPhaseQuery query, List<TestPlanNewStatusEnum> asList);

    TmTestPlanEntityDO selectByCD(String productCode, String versionCode, String testPlanType);

    List<TmTestPlanEntityDO> selectPlanOrSafePlan(TmTestPlanEntityDO entityDO);

    List<TmTestPlanEntityDO> selectVersionPlanList(VersionPlanQuery query);

    TmTestPlanEntityDO getSafetyPlanByVersion(String versionCode);

    TmTestPlanEntityDO getTestPlanByVersion(String versionCode);

    TmTestPlanRangeEntityDO getSafePlanRangeByPlanCode(String planCode);

    void updateStageStatusByPrimaryKey(TmTestPlanEntityDO updateEntityDO, String planCode);

    void updateHistoryStatusByPrimaryKey(TmTestPlanEntityDO entityDO, String planCode);

    TmTestPlanEntityDO selectEnableByPrimaryKey(String planCode);

    Boolean checkTestPlanName(FindPlanByProductQuery query);

    List<TmTestPlanEntityDO> selectTestPlanEntityListByQuery(FindPlanByProductQuery query);

    List<TmTestPlanEntityDO> selectTestPlanEntityListByQuery(AssociatedTestPlanListQuery query, List<String> codeList);

    void changeCaseExecuteResult(ChangeCaseExecuteResultEvent event);

    void changePlanCaseResultComment(PlanCaseResultCommentChangedEvent event);

    void editTestPlan(TmTestPlanVO vo, BaseEvent event);

    List<TestPlanEntityDO> selectTestPlanList(TestPlanListQuery query);

    List<TmTestPlanVO> selectTmTestPlanList(PlanListQuery planListQuery);

    /**
     * 更新覆盖率确认标志
     *
     * @param versionCodeSet 版本code
     * @param transactor     当前操作人
     */
    void updateCheckFlag(String planCode, Set<String> versionCodeSet, boolean checkFlag, User transactor);

    /**
     * 根据版本code，查询未确认覆盖率的测试计划个数
     *
     * @param versionCodeList 版本code
     * @return {@link  TmTestPlanEntityDO}
     */
    Integer countNotCheckListByVersionCodes(List<String> versionCodeList);

    List<String> filterCompletedPlanCode(List<String> codeList);

    /**
     * 更新测试计划版本信息
     *
     * @param planEntity  测试计划
     * @param versionName 版本名称
     * @param transactor  操作人
     */
    void updateVersionInfo(TmTestPlanEntityDO planEntity, String versionName, User transactor);

    /**
     * 根据版本code和测试计划类型，查询测试计划
     *
     * @param versionCode          版本code
     * @param testPlanTypeEnumList 测试计划类型
     * @return {@link  TmTestPlanEntityDO}
     */
    List<TmTestPlanEntityDO> listByVersionCodesAndTypes(String versionCode, List<TestPlanTypeEnum> testPlanTypeEnumList);
}
