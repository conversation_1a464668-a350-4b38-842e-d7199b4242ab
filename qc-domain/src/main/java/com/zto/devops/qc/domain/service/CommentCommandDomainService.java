package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.model.issue.command.AddCommentCommand;
import com.zto.devops.qc.client.model.issue.command.RemoveCommentCommand;
import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import com.zto.devops.qc.client.model.issue.event.CommentAddedEvent;
import com.zto.devops.qc.client.model.issue.event.CommentRemovedSimpleEvent;
import com.zto.devops.qc.domain.converter.CommentVOConverter;
import com.zto.devops.qc.domain.gateway.repository.CommentRepository;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class CommentCommandDomainService extends BaseDomainService {

    @Autowired
    private CommentRepository commentRepository;

    private final static CommentVOConverter COMMENTVO_CONVERTER = CommentVOConverter.INSTANCE;

    private final static Integer MILLISECONDS = 86400000;

    public void addComment(AddCommentCommand command) {
        CommentVO commentVO = COMMENTVO_CONVERTER.convert(command);
        Set<CommentVO> commentVOSet = new HashSet<>();
        commentVOSet.add(commentVO);
        CommentAddedEvent commentAddedEvent = new CommentAddedEvent();
        commentAddedEvent.setAggregateId(command.getAggregateId());
        commentAddedEvent.setBusinessCode(command.getAggregateId());
        commentAddedEvent.setCommentVOS(commentVOSet);
        commentAddedEvent.setTransactor(command.getTransactor());
        commentAddedEvent.setOccurred(new Date());
        log.info("----------------AddCommentCommand {}", JsonUtil.toJSON((command)));
        commentRepository.addComment(commentAddedEvent);
        apply(commentAddedEvent);
    }

    public void removeComment(RemoveCommentCommand command) {
        CommentRemovedSimpleEvent event = new CommentRemovedSimpleEvent();
        event.setAggregateId(command.getAggregateId());
        event.setOccurred(new Date());
        event.setTransactor(command.getTransactor());
        event.setCode(command.getCode());
        // 此处重构时对原代码有改动
        CommentVO comment = commentRepository.queryCommentByCode(event.getCode());
        if(null != comment) {
            if (!comment.getCreatorId().equals(command.getTransactor().getUserId())) {
                throw new ServiceException("只能删除自己的评论！");
            }
            long difference = System.currentTimeMillis() - comment.getGmtCreate().getTime();
            if (difference > MILLISECONDS) {
                throw new ServiceException("超过24小时的评论，不允许删除！");
            }
        }
        commentRepository.removeComment(event);
        apply(event);
    }

}
