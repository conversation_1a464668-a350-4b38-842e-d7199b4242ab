package com.zto.devops.qc.domain.statemachine;

import com.zto.devops.qc.client.enums.rpc.ProductRoleEnum;
import com.zto.devops.qc.client.model.issue.entity.IssueVO;
import com.zto.devops.qc.client.model.relevantUser.query.MyTaskVO;
import com.zto.devops.qc.domain.model.Issue;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 缺陷状态机上下文
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Mapper
public class IssueContext {

    private Long handleUserId;
    private Long findUserId;
    private Long developUserId;
    private Long testUserId;
    private List<String> permissions;

    /**
     * 当前登录用户ID
     */
    private Long currentUserId;

    /**
     * 当前用户角色
     */
    private List<ProductRoleEnum> roles;

//    private Boolean isProductMember;

    /*public static IssueContext convert(Issue issue, Long currentUserId) {
        IssueContext issueContext = new IssueContext();
        issueContext.setCurrentUserId(currentUserId);
        issueContext.setHandleUserId(issue.getHandler().getUserId());
        issueContext.setFindUserId(issue.getFinder().getUserId());
        issueContext.setDevelopUserId(issue.getDeveloper().getUserId());
        issueContext.setTestUserId(issue.getTester().getUserId());
        return issueContext;
    }*/

    /*public static IssueContext convert(IssueEntity entity, Long currentUserId) {
        IssueContext issueContext = new IssueContext();
        issueContext.setCurrentUserId(currentUserId);
        issueContext.setHandleUserId(entity.getHandleUserId());
        issueContext.setFindUserId(entity.getFindUserId());
        issueContext.setDevelopUserId(entity.getDevelopUserId());
        issueContext.setTestUserId(entity.getTestUserId());
        return issueContext;
    }*/

    /*public static IssueContext convert(IssueEntity entity, Long currentUserId, List<String> permissions) {
        IssueContext issueContext = new IssueContext();
        issueContext.setCurrentUserId(currentUserId);
        issueContext.setHandleUserId(entity.getHandleUserId());
        issueContext.setFindUserId(entity.getFindUserId());
        issueContext.setDevelopUserId(entity.getDevelopUserId());
        issueContext.setTestUserId(entity.getTestUserId());
        issueContext.setPermissions(permissions);
        return issueContext;
    }*/

    //DOMAIN使用
    public static IssueContext convert(Issue issue, Long currentUserId, List<String> permissions, List<ProductRoleEnum> roles) {
        IssueContext issueContext = new IssueContext();
        issueContext.setCurrentUserId(currentUserId);
        issueContext.setHandleUserId(issue.getHandler() != null ? issue.getHandler().getUserId() : null);
        issueContext.setFindUserId(issue.getFinder() != null ? issue.getFinder().getUserId() : null);
        issueContext.setDevelopUserId(issue.getDeveloper() != null ? issue.getDeveloper().getUserId() : null);
        issueContext.setTestUserId(issue.getTester() != null ? issue.getTester().getUserId() : null);
        issueContext.setPermissions(permissions);
        issueContext.setRoles(roles);
        return issueContext;
    }

    // 事项查询
    public static IssueContext convert(IssueVO issueVO, Long currentUserId, List<String> permissions) {
        IssueContext issueContext = new IssueContext();
        issueContext.setCurrentUserId(currentUserId);
        issueContext.setHandleUserId(issueVO.getHandleUserId());
        issueContext.setFindUserId(issueVO.getFindUserId());
        issueContext.setDevelopUserId(issueVO.getDevelopUserId());
        issueContext.setTestUserId(issueVO.getTestUserId());
        issueContext.setPermissions(permissions);
        return issueContext;
    }

    // 工作台使用的
    public static IssueContext convert(MyTaskVO myTaskVO, Long currentUserId) {
        IssueContext issueContext = new IssueContext();
        issueContext.setCurrentUserId(currentUserId);
        issueContext.setHandleUserId(myTaskVO.getHandlerId());
        issueContext.setFindUserId(myTaskVO.getFindUserId());
        if (myTaskVO.getDeveloper() != null) {
            issueContext.setDevelopUserId(myTaskVO.getDeveloper().getUserId());
        }
        if (myTaskVO.getTester() != null) {
            issueContext.setTestUserId(myTaskVO.getTester().getUserId());
        }
        return issueContext;
    }
}