package com.zto.devops.qc.domain.gateway.repository;

import com.github.pagehelper.PageInfo;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseExecuteRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTask;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTaskVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteTagVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListExecuteEnvQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.PageAutomaticTaskGroupQuery;
import com.zto.devops.qc.client.service.testmanager.apitest.model.ListExecuteDetailReq;

import java.util.List;

public interface IAutomaticTaskRepository {

    PageInfo<String> selectParentTaskIdList(PageAutomaticTaskGroupQuery query);

    List<AutomaticTaskVO> selectTaskList(List<String> taskIdList);

    AutomaticTask loadFromDb(String code);

    void insertSelective(AutomaticTaskEntityDO taskEntityDO);

    List<AutomaticTaskEntityDO> selectListByTaskId(String taskId);

    void updateByPrimaryKeySelective(AutomaticTaskEntityDO taskEntityDO);

    void updateByAutomaticTaskEntityDO(AutomaticTaskEntityDO entityDO);

    void updateResultByAutomaticTaskCode(String code, TestPlanCaseStatusEnum status);

    void updateResultNotFinished(String code, TestPlanCaseStatusEnum status);

    void updateTestcaseStatus(TestcaseExecuteRecordEntityDO entityDO, String testcaseCode, String automaticTaskCode);

    void updatePlanCaseStatus(String code, AutomaticStatusEnum status, String testPlanCode, TestPlanStageEnum testStage);

    List<TestcaseEntityDO> getTestcaseByAutomaticSourceCode(String automaticSourceCode);

    List<String> selectTestcaseCodeList(String code);

    /**
     * 查询排队中、待执行、执行中的任务
     *
     * @param code
     * @param parentFlag
     * @param statusEnums
     * @return
     */
    List<AutomaticTaskEntityDO> selectAutomaticTaskByStatusList(String code, Boolean parentFlag,
                                                                List<AutomaticStatusEnum> statusEnums);

    List<AutomaticTaskEntityDO> selectByTaskIdAndVersionCode(String taskId, String versionCode);

    List<String> getAbortAutomaticTasks(Integer duration);

    AutomaticTaskEntityDO getAutomaticTaskByCode(String code);

    void updateResultFilesByTaskCode(TestcaseExecuteRecordEntityDO entityDO, String code);

    List<Long> getRecordNotSuccess(String taskCode);

    void updateRecordToStatusByIds(List<Long> ids);

    List<TestcaseExecuteRecordEntityDO> getTestcaseRecordByTaskCode(ListExecuteDetailReq req);

    List<TestPlanCaseStatusEnum> getTestcaseResultByTaskId(String taskId);

    List<ExecuteTagVO> listExecuteEnv(ListExecuteEnvQuery query);

    List<String> getApiTestWaitTasks(Integer duration);

    List<AutomaticTaskEntityDO> selectListByCodeList(List<String> codeList);

    void deleteTaskCase(String taskCode, List<String> list);

    void updateCaseFileByAutomaticTask(AutomaticTaskEntityDO entityDO);
}
