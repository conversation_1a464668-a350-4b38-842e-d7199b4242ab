package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseByBusinessCodeVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListRelationTestCaseQuery;
import com.zto.devops.qc.domain.gateway.repository.ITestcaseRelationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class TestcaseRelationQueryDomainService {

    @Autowired
    private ITestcaseRelationRepository testcaseRelationRepository;

    //查询关联表用例数据
    public List<TestcaseByBusinessCodeVO> listRelationTestCaseQuery(ListRelationTestCaseQuery query){
        log.info("ListRelationTestCaseQuery >>> {}", query.getCodeList());
        return testcaseRelationRepository.selectTestCaseByCodeList(query.getCodeList(), DomainEnum.REQUIREMENT);
    }

}
