package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.dto.AutomaticTaskEntityDO;
import com.zto.devops.qc.client.model.dto.TestcaseExecuteRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.command.ExecuteAutomaticTaskCommand;
import com.zto.devops.qc.client.model.testmanager.cases.event.AutomaticTaskExecutedEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface AutomaticTaskDomainConverter {

    @Mapping(target = "code", source = "aggregateId")
    AutomaticTaskExecutedEvent converter(ExecuteAutomaticTaskCommand command);

    AutomaticTaskEntityDO converter(AutomaticTaskExecutedEvent event);

    @Mapping(target = "sourceAddress", source = "address")
    @Mapping(target = "filename", source = "fileName")
    @Mapping(target = "branchName", source = "branch")
    @Mapping(target = "workDir", source = "workSpace")
    @Mapping(ignore = true, target = "code")
    void converter(AutomaticSourceRecordEntityDO entityDO, @MappingTarget AutomaticTaskExecutedEvent event);

    @Mapping(target = "automaticTaskCode", source = "code")
    TestcaseExecuteRecordEntityDO converter(AutomaticTaskEntityDO entity);
}
