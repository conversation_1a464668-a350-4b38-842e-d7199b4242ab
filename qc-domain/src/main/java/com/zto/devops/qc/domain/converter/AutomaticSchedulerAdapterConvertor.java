package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.testmanager.scheduler.command.AddAutomaticSchedulerCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.AddSchedulerCasesCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.EditAutomaticSchedulerCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.command.RemoveSchedulerCasesCommand;
import com.zto.devops.qc.client.model.testmanager.scheduler.query.*;
import com.zto.devops.qc.client.service.testmanager.cases.model.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface AutomaticSchedulerAdapterConvertor {

    void converter(AddAutomaticSchedulerReq req, @MappingTarget AddAutomaticSchedulerCommand command);

    void converter(EditAutomaticSchedulerReq req, @MappingTarget EditAutomaticSchedulerCommand command);

    void converter(PageSchedulerReq req, @MappingTarget PageSchedulerQuery query);

    void convertor(AddSchedulerCasesReq req, @MappingTarget AddSchedulerCasesCommand command);

    void convertor(RemoveSchedulerCasesReq req, @MappingTarget RemoveSchedulerCasesCommand command);

    SchedulerModuleListQuery converter(SchedulerModuleListReq req);

    @Mapping(target = "search", source = "codeOrTitle")
    ListSchedulerCaseQuery converter(ListSchedulerCaseReq req);

    @Mapping(target = "search", source = "codeOrTitle")
    SchedulerCaseCodeListQuery converter(SchedulerCaseCodeListReq req);

    void converter(ProductSchedulerReq req, @MappingTarget ProductSchedulerQuery query);
}
