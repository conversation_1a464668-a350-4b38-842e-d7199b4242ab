package com.zto.devops.qc.domain.converter;

import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.enums.testmanager.plan.PlanEventFieldEnum;
import com.zto.devops.qc.client.model.dto.*;
import com.zto.devops.qc.client.model.testPlan.query.TestPlanListQuery;
import com.zto.devops.qc.client.model.testPlan.query.TestPlanVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ButtonVO;
import com.zto.devops.qc.client.model.testmanager.plan.command.ChangeCaseExecuteResultCommand;
import com.zto.devops.qc.client.model.testmanager.plan.command.ChangePlanCaseResultCommentCommand;
import com.zto.devops.qc.client.model.testmanager.plan.command.EditPlanStageStatusCommand;
import com.zto.devops.qc.client.model.testmanager.plan.command.EditPlanStatusCommand;
import com.zto.devops.qc.client.model.testmanager.plan.entity.*;
import com.zto.devops.qc.client.model.testmanager.plan.event.*;
import com.zto.devops.qc.client.model.testmanager.plan.query.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;

@Mapper(componentModel = "spring")
public interface TmTestPlanDomainConverter {

    TmTestPlanVO convertDO2VO(TmTestPlanEntityDO entityDO);

    List<TestFunctionPointVO> convertPointVOList(List<TestFunctionPointEntityDO> entityDOList);

    @Mapping(target = "name", expression = "java(em.getValue())")
    @Mapping(target = "code", expression = "java(em.getFieldName())")
    Button convertButtonVOS(PlanEventFieldEnum em);

    List<Button> convertButtonVOS(LinkedHashSet<PlanEventFieldEnum> planEventFieldLists);

    List<RelatedPlanVO> convertPlanVOList(List<TmEmailEntityDO> entityDOList);

    @Mapping(target = "planCode", source = "businessCode")
    @Mapping(target = "planType", source = "emailType")
    @Mapping(target = "planName", source = "businessName")
    @Mapping(target = "accessDate", source = "planPresentationDate")
    @Mapping(target = "permitDate", source = "planApprovalExitDate")
    @Mapping(target = "emailCode", source = "emailCode")
    @Mapping(target = "name", source = "emailName")
    RelatedPlanVO convertPlanVO(TmEmailEntityDO entity);

    List<RelatedReportVO> convertReportVOList(List<TmEmailEntityDO> list);

    @Mapping(target = "reportCode", source = "businessCode")
    @Mapping(target = "reportType", source = "emailType")
    @Mapping(target = "reportName", source = "businessName")
    @Mapping(target = "emailCode", source = "emailCode")
    @Mapping(target = "name", source = "emailName")
    RelatedReportVO convertReportVO(TmEmailEntityDO entity);

    TmPlanIssueEntityDO convert(PagePlanIssueQuery query);

    ListPlanCaseQuery convert(PageListPlanCaseQuery query);

    TmTestPlanEntityDO convert(PageTestPlanQuery query);

    List<TmTestPlanVO> convertLists(List<TmTestPlanEntityDO> tmTestPlanEntityLists);

    EditPlanStageStatusEvent convert(EditPlanStageStatusCommand command);

    EditPlanStatusEvent convert(EditPlanStatusCommand command);

    @Mapping(target = "code", source = "planCode")
    @Mapping(target = "type", source = "planType")
    TmTestPlanEntityDO convert(EditPlanStatusEvent event);

    @Mapping(target = "accessDate", expression = "java(com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanSendEmailVO.getAccessDate(vo))")
    @Mapping(target = "permitDate", expression = "java(com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanSendEmailVO.getPermitDate(vo))")
    SendTestPlanEvent convertor(TmTestPlanSendEmailVO vo);

    @Mapping(target = "emailName", source = "name")
    @Mapping(target = "planPresentationDate", source = "accessDate")
    @Mapping(target = "planApprovalExitDate", source = "permitDate")
    @Mapping(target = "relatePlanCode", source = "relationPlanCode")
    @Mapping(target = "relatePlanName", source = "relationPlanName")
    TmEmailEntityDO convert(SendTestPlanEvent event);

    ChangeCaseExecuteResultEvent convert(ChangeCaseExecuteResultCommand command);

    @Mapping(target = "planCode", source = "aggregateId")
    PlanCaseResultCommentChangedEvent convert(ChangePlanCaseResultCommentCommand command);

    @Mapping(target = "name", expression = "java(em.getValue())")
    @Mapping(target = "code", expression = "java(em.getFieldName())")
    ButtonVO convertButtonVOList(PlanEventFieldEnum em);

    List<ButtonVO> convertButtonVOList(Collection<PlanEventFieldEnum> emList);

    @Mapping(target = "status", source = "statusList")
    PlanListQuery convertQuery(TestPlanListQuery query);

    List<TestPlanVO> convert(List<TestPlanEntityDO> list);

    SimpleTmTestPlanVO convertSimpleVO(TmTestPlanEntityDO vo);

    ListPlanCaseQuery convert(ListPlanCaseCodeQuery query);
}
