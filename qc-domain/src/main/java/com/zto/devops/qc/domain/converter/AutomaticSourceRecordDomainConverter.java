package com.zto.devops.qc.domain.converter;

import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.cases.command.*;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticRecordVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticSourceLogTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.event.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface AutomaticSourceRecordDomainConverter {

    @Mapping(target = "code", source = "aggregateId")
    AddAutomaticRecordEvent convert(AddAutomaticRecordCommand command);

    EditAutomaticRecordEvent convert(EditAutomaticRecordCommand command);

    AddAutomaticRecordLogCommand convertCommand(EditAutomaticRecordCommand command);

    @Mapping(target = "aggregateId", source = "code")
    EditAutomaticRecordCommand convertDO(AutomaticSourceRecordEntityDO entityDO);

    @Mapping(target = "code", source = "aggregateId")
    AddAutomaticRecordLogEvent convert(AddAutomaticRecordLogCommand command);

    @Mapping(target = "code", source = "aggregateId")
    EditAutomaticRecordLogEvent convert(EditAutomaticRecordLogCommand command);

    AutomaticRecordVO convert(AutomaticSourceRecordEntityDO entity);

    @Mapping(target = "code", source = "aggregateId")
    SubmitAnalysisAutomaticEvent convert(SubmitAnalysisAutomaticCommand command);

    @Mapping(target = "code", source = "aggregateId")
    AutomaticSuccessEvent convert(AutomaticSuccessCommand command);

    AutomaticSourceRecordEntityDO convert(AutomaticSuccessEvent event);

    @Mapping(target = "testcaseCode", source = "code")
    AutomaticSourceLogTestcaseVO convert(ListTestcaseVO vo);
}
