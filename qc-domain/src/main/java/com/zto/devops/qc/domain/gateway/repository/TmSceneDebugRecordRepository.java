package com.zto.devops.qc.domain.gateway.repository;

import com.zto.devops.qc.client.model.dto.TmSceneDebugRecordEntityDO;
import com.zto.devops.qc.client.model.testmanager.apitest.command.SaveDebugInfoCommand;

public interface TmSceneDebugRecordRepository {

    void insertSelective(SaveDebugInfoCommand command);

    void updateSelective(SaveDebugInfoCommand command);

    TmSceneDebugRecordEntityDO selectOne(String productCode, String sceneCode);

    TmSceneDebugRecordEntityDO selectOneByTaskId(String taskId);

    void updateOssPathByTaskId(String taskId, String logOssPath);
}
