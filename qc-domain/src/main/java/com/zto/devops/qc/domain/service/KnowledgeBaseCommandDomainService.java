package com.zto.devops.qc.domain.service;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.rpc.MemberTypeEnum;
import com.zto.devops.qc.client.model.dto.KnowledgeBaseEntityDO;
import com.zto.devops.qc.client.model.knowledgebase.command.RemoveKnowledgeBaseCommand;
import com.zto.devops.qc.client.model.knowledgebase.command.UpdateKnowledgeBaseCommand;
import com.zto.devops.qc.client.model.knowledgebase.query.KnowledgeBaseByProductCodeQuery;
import com.zto.devops.qc.client.model.knowledgebase.query.KnowledgeBaseVO;
import com.zto.devops.qc.client.model.rpc.product.ProductAll;
import com.zto.devops.qc.client.model.rpc.product.ProductAllVO;
import com.zto.devops.qc.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.qc.client.model.rpc.product.SimpleQueryVO;
import com.zto.devops.qc.client.model.rpc.product.query.AllProductsQuery;
import com.zto.devops.qc.client.model.rpc.product.query.ListProductMemberByPIdQuery;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.http.HttpService;
import com.zto.devops.qc.domain.gateway.repository.KnowledgeBaseRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class KnowledgeBaseCommandDomainService {

    @Autowired
    private KnowledgeBaseRepository knowledgeBaseRepository;
    @Autowired
    private IProductRpcService productRpcService;
    @Autowired
    private QcConfigBasicService qcConfigBasicService;
    @Autowired
    private KnowledgeBaseQueryDomainService knowledgeBaseQueryDomainService;
    @Autowired
    private HttpService httpService;


    public void addMemberPermission(UpdateKnowledgeBaseCommand command) {
        log.info("UpdateKnowledgeBaseCommand >>> {}", command.getProductCode());

        HashMap<String, Object> membersPermissions = new HashMap<>();
        HashMap<String, String> member = new HashMap<>();
        KnowledgeBaseByProductCodeQuery query = new KnowledgeBaseByProductCodeQuery();
        query.setProductCode(command.getProductCode());

        KnowledgeBaseVO knowledgeBaseDetail = new KnowledgeBaseVO();
        try {
            knowledgeBaseDetail = knowledgeBaseRepository.getKnowledgeBaseDetail(query);
        } catch (Exception e) {
            log.error("当前产品没有创建知识库，无法更新知识库权限{}", command);
        }

        if (knowledgeBaseDetail != null) {
            member.put("roleType", command.getRoleType().name());
            member.put("memberType", "USER");
            member.put("memberId", command.getUserId());

            membersPermissions.put("userId", command.getOperatedUserId());
            membersPermissions.put("spaceId", knowledgeBaseDetail.getSpaceId());
            membersPermissions.put("members", member);
        }
        //添加产品下成员权限
        addMemberPermissions(membersPermissions);
    }


    public void removeMemberPermission(RemoveKnowledgeBaseCommand command) {
        log.info("RemoveKnowledgeBaseCommand >>> {}", command.getProductCode());

        HashMap<String, Object> membersPermissions = new HashMap<>();
        HashMap<String, String> member = new HashMap<>();
        KnowledgeBaseByProductCodeQuery query = new KnowledgeBaseByProductCodeQuery();
        query.setProductCode(command.getProductCode());

        KnowledgeBaseVO knowledgeBaseDetail = new KnowledgeBaseVO();
        try {
            knowledgeBaseDetail = knowledgeBaseRepository.getKnowledgeBaseDetail(query);
        } catch (Exception e) {
            log.error("当前产品没有创建知识库，无法更新知识库权限{}", command);
        }

        if (knowledgeBaseDetail != null) {
            member.put("roleType", command.getRoleType().name());
            member.put("memberType", "USER");
            member.put("memberId", command.getUserId());

            membersPermissions.put("userId", command.getOperatedUserId());
            membersPermissions.put("spaceId", knowledgeBaseDetail.getSpaceId());
            membersPermissions.put("members", member);
        }

        //移除产品下成员权限
        removeMemberPermissions(membersPermissions);
    }

    public void synchronizePermissionData() {
        List<KnowledgeBaseEntityDO> list = new ArrayList<>();

        AllProductsQuery query = new AllProductsQuery();
        query.setPage(1);
        query.setSize(99999);
        ProductAllVO vo = productRpcService.allProductsQuery(query);

        for (ProductAll productVO : vo.getList()) {
            KnowledgeBaseByProductCodeQuery knowledgeBaseByProductCodeQuery = new KnowledgeBaseByProductCodeQuery();
            knowledgeBaseByProductCodeQuery.setProductCode(productVO.getCode());
            KnowledgeBaseVO knowledgeBaseVO = knowledgeBaseQueryDomainService.getKnowledgeBaseDetail(knowledgeBaseByProductCodeQuery);

            if (knowledgeBaseVO != null && knowledgeBaseVO.getProductCode().equals(productVO.getCode())) {
                log.info("当前知识库已创建{}", productVO);
                synchronizePermissionByProduct(productVO, knowledgeBaseVO.getSpaceId());
            } else {
                log.info("开始创建知识库{}", productVO);
                HashMap<String, Object> req = new HashMap<>();
                req.put("userId", qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId());
                req.put("name", productVO.getName() + "知识库");
                req.put("description", "该知识库包含" + productVO.getName() + "产品下的全部文档信息，支持产品下全部成员对文档的新建、查看、编辑等操作，便于资产沉淀，提高协作性");
                Map<String, Object> resultData = getResultData(httpService.doPost(qcConfigBasicService.getKnowledgeBaseUrl() + "/docs/space/create", new JSONObject(req).toString()));

                log.info("知识库创建接口请求信息{}", new JSONObject(req));

                if (resultData == null) {
                    log.error("该产品创建知识库失败！！！{}", productVO);
                    continue;
                }
                Map<String, Object> knowledgeData = (Map<String, Object>) resultData.get("result");

                log.info("知识库创建接口返回信息{}", knowledgeData.toString());

                if (knowledgeData == null) {
                    log.error("该产品创建知识库未返回数据！！！{}", productVO);
                    continue;
                }
                KnowledgeBaseEntityDO entity = new KnowledgeBaseEntityDO();
                entity.setSpaceId((String) knowledgeData.get("id"));
                entity.setUrl((String) knowledgeData.get("url"));
                entity.setProductCode(productVO.getCode());
                entity.setCreatorId(Long.valueOf(productVO.getProductUserId()));
                entity.setCreator(productVO.getProductUserName());
                entity.setModifierId(Long.valueOf(productVO.getProductUserId()));
                entity.setModifier(productVO.getProductUserName());
                entity.setGmtCreate(new Date());
                entity.setGmtModified(new Date());
                list.add(entity);
                synchronizePermissionByProduct(productVO, knowledgeData.get("id").toString());
            }
        }
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        knowledgeBaseRepository.insertBatch(list);
    }

    public void synchronizePermissionByProduct(ProductAll productVO, String knowledgeId) {

        log.info("开始同步当前知识库的人员权限{},{}", productVO, knowledgeId);

        ListProductMemberByPIdQuery listProductMemberByPIdQuery = new ListProductMemberByPIdQuery();
        listProductMemberByPIdQuery.setProductCode(productVO.getCode());
        List<ProductMemberVO> productMemberVOS = productRpcService.listProductMemberByPIdQuery(listProductMemberByPIdQuery);

        if (CollectionUtil.isEmpty(productMemberVOS)) {
            log.error("该产品下没有产品成员信息！！！{}", productVO);
            return;
        }
        List<Long> productUser = productMemberVOS.stream().distinct().filter(x -> x.getMemberType().equals(MemberTypeEnum.PRODUCTER_OWNER)).map(ProductMemberVO::getUserId).collect(Collectors.toList());

        Set<Long> collect = productMemberVOS.stream().map(ProductMemberVO::getUserId).collect(Collectors.toSet());
        List<Map> members = new ArrayList<>();

        if (CollectionUtil.isEmpty(collect)) {
            log.error("当前产品没有产品成员信息{}", productVO.getCode());
            return;
        }
        if (CollectionUtil.isEmpty(productUser)) {
            log.error("当前产品没有产品负责人成员信息{}", productVO.getCode());
            return;
        }

        for (Long aLong : collect) {
            if (aLong.equals(productUser.get(0))) {
                continue;
            }
            HashMap<String, String> member = new HashMap<>();
            member.put("roleType", "EDITOR");
            member.put("memberType", "USER");
            member.put("memberId", String.valueOf(aLong));
            members.add(member);
        }

        //移除产品负责人的编辑权限
        HashMap<String, String> removeProductMember = new HashMap<>();
        removeProductMember.put("roleType", "EDITOR");
        removeProductMember.put("memberType", "USER");
        removeProductMember.put("memberId", String.valueOf(productUser.get(0)));

        HashMap<String, Object> removeProductMembersPermissions = new HashMap<>();
        removeProductMembersPermissions.put("userId", qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId());
        removeProductMembersPermissions.put("spaceId", knowledgeId);
        removeProductMembersPermissions.put("members", removeProductMember);
        removeMemberPermissions(removeProductMembersPermissions);

        //添加产品负责人管理权限
        HashMap<String, String> productMember = new HashMap<>();
        productMember.put("roleType", "MANAGER");
        productMember.put("memberType", "USER");
        productMember.put("memberId", String.valueOf(productUser.get(0)));

        HashMap<String, Object> productMembersPermissions = new HashMap<>();
        productMembersPermissions.put("userId", qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId());
        productMembersPermissions.put("spaceId", knowledgeId);
        productMembersPermissions.put("members", productMember);
        addMemberPermissions(productMembersPermissions);

        log.info("添加产品负责人{}", productMembersPermissions);
        if (CollectionUtil.isNotEmpty(members)) {
            List<List<Map>> split = ListUtil.split(members, 10);
            for (List<Map> maps : split) {
                HashMap<String, Object> membersPermissions = new HashMap<>();
                membersPermissions.put("userId", qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId());
                membersPermissions.put("spaceId", knowledgeId);
                membersPermissions.put("members", maps);
                //添加产品下成员权限
                addMemberPermissions(membersPermissions);
                log.info("添加产品成员{}", membersPermissions);

            }
        }
    }


    public void synchronizeData() {
        knowledgeBaseRepository.delete();
        AllProductsQuery query = new AllProductsQuery();
        query.setPage(1);
        query.setSize(99999);
        ProductAllVO vo = productRpcService.allProductsQuery(query);
        List<KnowledgeBaseEntityDO> list = new ArrayList<>();


        log.info("开始同步产品知识库信息 ==> ==> ==>");

        for (ProductAll productVO : vo.getList()) {
            createKnowledge(productVO, list);
        }
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        knowledgeBaseRepository.insertBatch(list);
    }

    @Async
    public void createProductKnowledge(String productCode) {
        SimpleQueryVO productVO = productRpcService.getProductVO(productCode);
        ProductAll productAll = new ProductAll();
        productAll.setCode(productCode);
        productAll.setName(productVO.getProductName());
        productAll.setProductUserId(String.valueOf(productVO.getProductUserId()));
        productAll.setProductUserName(productVO.getProductUserName());
        List<KnowledgeBaseEntityDO> list = new ArrayList<>();
        createKnowledge(productAll, list);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        knowledgeBaseRepository.insertBatch(list);
    }

    public void createKnowledge(ProductAll productVO, List<KnowledgeBaseEntityDO> list) {
        HashMap<String, Object> req = new HashMap<>();
        req.put("userId", qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId());
        req.put("name", productVO.getName() + "知识库");
        req.put("description", "该知识库包含" + productVO.getName() + "产品下的全部文档信息，支持产品下全部成员对文档的新建、查看、编辑等操作，便于资产沉淀，提高协作性");
        Map<String, Object> resultData = getResultData(httpService.doPost(qcConfigBasicService.getKnowledgeBaseUrl() + "/docs/space/create", new JSONObject(req).toString()));

        log.info("知识库创建接口请求信息{}", new JSONObject(req));

        if (resultData == null) {
            log.error("该产品创建知识库失败！！！{}", productVO);
            return;
        }
        Map<String, Object> knowledgeData = (Map<String, Object>) resultData.get("result");

        log.info("知识库创建接口返回信息{}", knowledgeData.toString());

        if (knowledgeData == null) {
            log.error("该产品创建知识库未返回数据！！！{}", productVO);
            return;
        }
        KnowledgeBaseEntityDO entity = new KnowledgeBaseEntityDO();
        entity.setSpaceId((String) knowledgeData.get("id"));
        entity.setUrl((String) knowledgeData.get("url"));
        entity.setProductCode(productVO.getCode());
        entity.setCreatorId(Long.valueOf(productVO.getProductUserId()));
        entity.setCreator(productVO.getProductUserName());
        entity.setModifierId(Long.valueOf(productVO.getProductUserId()));
        entity.setModifier(productVO.getProductUserName());
        entity.setGmtCreate(new Date());
        entity.setGmtModified(new Date());
        list.add(entity);

        ListProductMemberByPIdQuery listProductMemberByPIdQuery = new ListProductMemberByPIdQuery();
        listProductMemberByPIdQuery.setProductCode(productVO.getCode());
        List<ProductMemberVO> productMemberVOS = productRpcService.listProductMemberByPIdQuery(listProductMemberByPIdQuery);

        if (CollectionUtil.isEmpty(productMemberVOS)) {
            log.error("该产品下没有产品成员信息！！！{}", productVO);
            return;
        }
        List<Long> productUser = productMemberVOS.stream().distinct().filter(x -> x.getMemberType().equals(MemberTypeEnum.PRODUCTER_OWNER)).map(ProductMemberVO::getUserId).collect(Collectors.toList());

        Set<Long> collect = productMemberVOS.stream().map(ProductMemberVO::getUserId).collect(Collectors.toSet());
        List<Map> members = new ArrayList<>();
        HashMap<String, String> productMember = new HashMap<>();


        if (CollectionUtil.isEmpty(collect)) {
            log.error("当前产品没有产品成员信息{}", productVO.getCode());
            return;
        }

        if (CollectionUtil.isEmpty(productUser)) {
            log.error("当前产品没有产品负责人成员信息{}", productVO.getCode());
            return;
        }

        productMember.put("roleType", "MANAGER");
        productMember.put("memberType", "USER");
        productMember.put("memberId", String.valueOf(productUser.get(0)));

        for (Long aLong : collect) {
            if (aLong.equals(productUser.get(0))) {
                continue;
            }
            HashMap<String, String> member = new HashMap<>();
            member.put("roleType", "EDITOR");
            member.put("memberType", "USER");
            member.put("memberId", String.valueOf(aLong));
            members.add(member);
        }
        if (CollectionUtil.isNotEmpty(members)) {
            HashMap<String, Object> productMembersPermissions = new HashMap<>();
            productMembersPermissions.put("userId", qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId());
            productMembersPermissions.put("spaceId", knowledgeData.get("id").toString());
            productMembersPermissions.put("members", productMember);
            //添加产品f负责人权限
            addMemberPermissions(productMembersPermissions);

            List<List<Map>> split = ListUtil.split(members, 10);
            for (List<Map> maps : split) {
                HashMap<String, Object> membersPermissions = new HashMap<>();
                membersPermissions.put("userId", qcConfigBasicService.getDingDingKnowledgeBaseManagerUserId());
                membersPermissions.put("spaceId", knowledgeData.get("id").toString());
                membersPermissions.put("members", maps);
                //添加产品下成员权限
                addMemberPermissions(membersPermissions);
            }
        }
    }

    public void addMemberPermissions(Map<String, Object> memberInfos) {
        if (memberInfos != null) {
            JSONObject object = new JSONObject(memberInfos);
            String json = object.toString();
            try {
                httpService.doPost(qcConfigBasicService.getKnowledgeBaseUrl() + "/docs/space/members/add", json);
            } catch (Exception e) {

                log.error("添加知识库权限失败！！！ {}", memberInfos);
            }
        }
    }

    public void removeMemberPermissions(Map<String, Object> memberInfos) {
        if (memberInfos != null) {
            JSONObject object = new JSONObject(memberInfos);
            String json = object.toString();
            try {
                httpService.doPost(qcConfigBasicService.getKnowledgeBaseUrl() + "/docs/space/members/remove", json);
            } catch (Exception e) {
                log.error("移除知识库权限失败！！！ {}", memberInfos);
            }
        }
    }


    public Map<String, Object> getResultData(String data) {
        if (StringUtil.isEmpty(data)) {
            return null;
        }
        return JSONObject.parseObject(data, Feature.OrderedField);
    }


}
