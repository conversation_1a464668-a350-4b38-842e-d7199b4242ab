package com.zto.devops.qc.domain.service;

import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseStepQuery;
import com.zto.devops.qc.domain.gateway.repository.TestcaseStepRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class TestcaseStepQueryDomainService extends BaseDomainService {

    @Autowired
    private TestcaseStepRepository testcaseStepRepository;


    public List<TestcaseStepVO> listTestcaseStepQuery(ListTestcaseStepQuery query) {
        log.info("ListTestcaseStepQuery >>> {}", query.getTestcaseCode());
        return testcaseStepRepository.listTestcaseStepQuery(query);
    }


}
