<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>qc-parent</artifactId>
        <groupId>com.zto.devops.qc</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>qc-adapter</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zto.devops.pipeline</groupId>
            <artifactId>pipeline-client</artifactId>
            <version>${devopsPipeline.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zto.titans</groupId>
            <artifactId>titans-dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.qc</groupId>
            <artifactId>qc-domain</artifactId>
            <version>${revision}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.project</groupId>
            <artifactId>project-client</artifactId>
            <version>${project-client.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.zto.devops.framework</groupId>
            <artifactId>devops-client</artifactId>
            <version>${framework.version}</version>
        </dependency>
    </dependencies>

</project>