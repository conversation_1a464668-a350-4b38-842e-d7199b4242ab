package com.zto.devops.qc.adapter.handler;

import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.message.EventHandler;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.time.DateUtil;
import com.zto.devops.pipeline.client.enums.FlowStatusEnum;
import com.zto.devops.pipeline.client.model.application.event.ApplicationEditedEvent;
import com.zto.devops.pipeline.client.model.cluster.event.NamespaceTagEditedEvent;
import com.zto.devops.pipeline.client.model.flow.entity.BaseApplicationDO;
import com.zto.devops.pipeline.client.model.flow.event.CreateBranchQcEvent;
import com.zto.devops.pipeline.client.model.flow.event.VersionStatusChangedEvent;
import com.zto.devops.pipeline.client.model.flow.event.deployment.*;
import com.zto.devops.pipeline.client.model.instance.event.InstanceProcessPidChangedEvent;
import com.zto.devops.pipeline.client.model.rpc.qc.event.AutoAnalysisAutomaticRecordEvent;
import com.zto.devops.pipeline.client.model.rpc.qc.event.AutomaticTaskCDExecutedEvent;
import com.zto.devops.qc.adapter.converter.PipelineConverter;
import com.zto.devops.qc.client.enums.rpc.FlowLaneTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.GenerateTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.devops.qc.client.model.dto.AutomaticSourceRecordEntityDO;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.rpc.pipeline.JacocoApplicationVO;
import com.zto.devops.qc.client.model.rpc.pipeline.JacocoVersionVO;
import com.zto.devops.qc.client.model.rpc.pipeline.VersionInfo;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.service.*;
import com.zto.titans.common.util.JsonUtil;
import com.zto.titans.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PipelineHandler {

    @Autowired
    private RedisService redisTemplate;
    @Autowired
    private PipelineConverter pipelineConverter;
    @Autowired
    private CoverageDomainService coverageDomainService;
    @Autowired
    private AutomaticTaskCommandDomainService automaticTaskCommandDomainService;
    @Autowired
    private AutomaticSourceRecordCommandDomainService automaticSourceRecordCommandDomainService;
    @Autowired
    private AutomaticSourceRecordQueryDomainService automaticSourceRecordQueryDomainService;
    @Autowired
    private AutomaticSchedulerCommandDomainService automaticSchedulerCommandDomainService;
    @Autowired
    private TmTestPlanCommandDomainService tmTestPlanCommandDomainService;
    @Autowired
    private AgentDomainService agentDomainService;
    @Autowired
    private InterfaceCoverageDomainService interfaceCoverageDomainService;


    /**
     * Deployment测试通过-事件
     *
     * @param event
     */
    @EventHandler
    public void handleDeploymentTestedReportEvent(DeploymentTestedReportEvent event) {
        log.info("动态多环境测试通过事件：{}", JsonUtil.toJSON(event));
        if (!event.isResult()) {
            return;
        }
        JacocoVersionVO jacocoVersionVO = coverageDomainService.checkJacocoVersionVO(event.getCode());
        for (VersionInfo versionInfo : jacocoVersionVO.getVersions()) {
            CoverageRecordGenerateParameter parameter = makeGenerateParameter(
                    versionInfo.getProductCode(),
                    versionInfo.getCode(),
                    versionInfo.getName(),
                    new ArrayList<>(),
                    RecordTypeEnum.BRANCH,
                    event.getTransactor(),
                    GenerateTypeEnum.AUTO,
                    FlowLaneTypeEnum.FLOW_TEST.name());
            String key = "AUTO_GENERATE_" + versionInfo.getProductCode() + "_" + parameter.getVersionCode();
            redisTemplate.setKey(key, "ALL", 1800L, TimeUnit.SECONDS);
            coverageDomainService.generateCoverageReport(parameter);
        }
    }

    /**
     * Deployment关联应用
     *
     * @param event
     */
    @EventHandler
    public void handleDeploymentRelatedAppEvent(DeploymentRelatedAppEvent event) {
        log.info("动态多环境关联应用事件.{}", JsonUtil.toJSON(event));
        checkParams(event);
        String versionCode = event.getVersionCode();
        String branchName = event.getFeatureBranch();
        String releaseBranchName = event.getReleaseBranch();
        List<BaseApplicationDO> relatedApplications = event.getRelatedApplications();
        List<BaseApplicationDO> cancelApplications = event.getCancelApplications();
        coverageDomainService.initCoverageRecords(
                pipelineConverter.convertBaseApplicationDOList(relatedApplications),
                pipelineConverter.convertBaseApplicationDOList(cancelApplications),
                versionCode,
                branchName,
                releaseBranchName);
    }

    /**
     * Deployment回归结果
     *
     * @param event
     */
    @EventHandler
    public void handleDeploymentRegressedReportEvent(DeploymentRegressedReportEvent event) {
        log.info("动态多环境回归测试通过事件：{}", JsonUtil.toJSON(event));
    }

    private CoverageRecordGenerateParameter makeGenerateParameter(String productCode,
                                                                  String versionCode,
                                                                  String versionName,
                                                                  List<String> appIdList,
                                                                  RecordTypeEnum recordType,
                                                                  com.zto.devops.framework.client.simple.User user,
                                                                  GenerateTypeEnum generateType,
                                                                  String flowLaneType) {
        CoverageRecordGenerateParameter parameter = new CoverageRecordGenerateParameter();
        parameter.setProductCode(productCode);
        parameter.setVersionCode(versionCode);
        parameter.setVersionName(versionName);
        parameter.setAppIdList(appIdList);
        parameter.setRecordType(recordType);
        parameter.setGenerateType(generateType);
        parameter.setCreator(user.getUserName());
        parameter.setCreatorId(user.getUserId());
        parameter.setTaskId(versionCode + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss"));
        parameter.setFlowLaneType(flowLaneType);
        return parameter;
    }

    /**
     * Deployment部署结束
     *
     * @param event
     */
    @EventHandler
    public void handleDeploymentDeployEndedEvent(DeploymentDeployEndedEvent event) {
        log.info("动态多环境部署结束事件：{}", JsonUtil.toJSON(event));
        List<JacocoApplicationVO> apps = coverageDomainService.checkApps(event.getCode(), event.getPlanCode(), "DeployEndedEvent");
        coverageDomainService.saveCoveragePublish(apps, event.transactor());
    }

    /**
     * 创建分支-事件
     *
     * @param event
     */
    @EventHandler
    public void handleCreateBranchQcEvent(CreateBranchQcEvent event) {
        log.info("创建分支事件>>{}", JsonUtil.toJSON(event));
        checkCreateBranchQcEvent(event);
        coverageDomainService.createBranch(pipelineConverter.convert(event));
        doInitCoverageRecords(event.getVersions(), event.getBranchName(), event.getReleaseBranchName());
    }

    private void doInitCoverageRecords(List<CreateBranchQcEvent.Version> versions, String branchName, String releaseBranchName) {
        if (CollectionUtil.isEmpty(versions)) {
            return;
        }
        versions.forEach(version -> coverageDomainService.initCoverageRecordsByVersionCode(
                version.getCode(),
                pipelineConverter.convertBaseApplicationDOList(version.getApps()),
                branchName,
                releaseBranchName));
    }

    private void checkCreateBranchQcEvent(CreateBranchQcEvent event) {
        if (StringUtil.isEmpty(event.getBranchName())) {
            throw new ServiceException("分支名称branchName不能为空");
        }
        if (CollectionUtil.isEmpty(event.getVersions())) {
            throw new ServiceException("版本编号versionCodes不能为空");
        }
        if (null == event.getTransactor()) {
            throw new ServiceException("创建人信息transactor不能为空");
        }
    }

    private void checkParams(DeploymentRelatedAppEvent event) {
        if (StringUtil.isEmpty(event.getVersionCode())) {
            throw new ServiceException("版本编号versionCode不能为空");
        }
        if (StringUtil.isEmpty(event.getFeatureBranch())) {
            throw new ServiceException("分支名称branchName不能为空");
        }
        if (null == event.getTransactor()) {
            throw new ServiceException("创建人信息transactor不能为空");
        }
    }

    @EventHandler
    public void handleAutomaticTaskCDExecutedEvent(AutomaticTaskCDExecutedEvent event) {
        log.info("PipelineHandler事件消费【AutomaticTaskCDExecutedEvent】：{}", JsonUtil.toJSON(event));
        automaticTaskCommandDomainService.onAutomaticTaskCDExecutedEvent(pipelineConverter.convert(event));
    }

    /**
     * git代码提交或合并事件，触发重新解析登记库
     *
     * @param event
     */
    @EventHandler
    public void handleAutoAnalysisAutomaticRecordEvent(AutoAnalysisAutomaticRecordEvent event) {
        log.info("handleAutoAnalysisAutomaticRecordEvent：{}", JsonUtil.toJSON(event));
        if (null == event) {
            return;
        }
        //登记库列表
        List<AutomaticSourceRecordEntityDO> sourceRecordEntityList = automaticSourceRecordQueryDomainService.selectAutoAnalysisByAddress(event.getGitHttpUrl());
        if (CollectionUtil.isEmpty(sourceRecordEntityList)) {
            log.info(event.getGitProjectId() + "_handleAutoAnalysisAutomaticRecordEvent_sourceRecordEntityList_is_null!");
            return;
        }
        sourceRecordEntityList.forEach(source -> {
            automaticSourceRecordCommandDomainService.analysisAndSubmit(source, event.getTransactor(), event.getGitProjectId());
        });
    }

    @EventHandler
    public void handleNamespaceTagEditedEvent(NamespaceTagEditedEvent event) {
        log.info("PipelineHandler事件消费【NamespaceTagEditedEvent】：{}", JsonUtil.toJSON(event));
        automaticSchedulerCommandDomainService.updateOssTag(event.getProductCode(), event.getCode(), event.getTag());
    }

    @EventHandler
    public void handleDeploymentBackedEvent(DeploymentBackedEvent event) {
        log.info("PipelineHandler事件消费【DeploymentBackedEvent】：{}", JsonUtil.toJSON(event));
        tmTestPlanCommandDomainService.initPlanStageStatus(pipelineConverter.convert(event));
    }

    @EventHandler
    public void handleVersionStatusChangedEvent(VersionStatusChangedEvent event) {
        log.info("PipelineHandler事件消费【VersionStatusChangedEvent】：{}", JsonUtil.toJSON(event));
        tmTestPlanCommandDomainService.doChangeStageStatus(pipelineConverter.convert(event));
        if (null != event && null != event.getStatus() && event.getStatus().equals(FlowStatusEnum.ACCEPTING)) {
            agentDomainService.batchRemoveByVersionCodes(event.getVersionCodes());
            interfaceCoverageDomainService.cacheAcceptingVersions(event.getVersionCodes());
        }
    }

    @EventHandler
    public void handleDeploymentDelayAcceptedEvent(DeploymentDelayAcceptedEvent event) {
        log.info("PipelineHandler事件消费【DeploymentDelayAcceptedEvent】：{}", JsonUtil.toJSON(event));
        tmTestPlanCommandDomainService.doSaveDelayAcceptVersion(pipelineConverter.convert(event));
    }

    @EventHandler
    public void handleDeploymentAcceptedReportEvent(DeploymentAcceptedReportEvent event) {
        log.info("PipelineHandler事件消费【DeploymentAcceptedReportEvent】：{}", JsonUtil.toJSON(event));
        tmTestPlanCommandDomainService.doUpdateAcceptedVersion(pipelineConverter.convert(event));
    }

    @EventHandler
    public void handleInstanceProcessPidChangedEvent(InstanceProcessPidChangedEvent event) {
        log.info("实例进程PID发生变化， 重启、回滚、部署事件：{}", JsonUtil.toJSON(event));
        List<JacocoApplicationVO> apps = coverageDomainService.checkApps(event.getFlowCode(), event.getPlanCode(), "PidChangedEvent");
        coverageDomainService.saveCoveragePublish(apps, event.getTransactor(), false);
    }

    @EventHandler
    public  void handleApplicationEditedEvent(ApplicationEditedEvent event){
        log.info("ApplicationEditedEvent：{}", JsonUtil.toJSON(event));
        coverageDomainService.updateCoverageBasic(pipelineConverter.convert(event));
    }
}
