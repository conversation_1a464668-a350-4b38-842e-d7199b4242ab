package com.zto.devops.qc.adapter.handler;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.simple.*;
import com.zto.devops.framework.common.message.EventHandler;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.project.client.enums.common.DefaultValueEnum;
import com.zto.devops.project.client.model.sprint.event.SprintDeletedEvent;
import com.zto.devops.project.client.model.version.entity.RelatedItemVO;
import com.zto.devops.project.client.model.version.event.VersionAddedEvent;
import com.zto.devops.project.client.model.version.event.VersionEditedEvent;
import com.zto.devops.qc.adapter.converter.AddVersionIssueConvertor;
import com.zto.devops.qc.client.enums.rpc.FlowStatusEnum;
import com.zto.devops.qc.client.enums.rpc.VersionTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStrategyEnum;
import com.zto.devops.qc.client.model.dto.IssueEntityDO;
import com.zto.devops.qc.client.model.issue.command.AddVersionIssueCommand;
import com.zto.devops.qc.client.model.issue.command.EditSprintIssueCommand;
import com.zto.devops.qc.client.model.issue.event.SprintInIssueEditedEvent;
import com.zto.devops.qc.client.model.issue.query.SimpleIssueQuery;
import com.zto.devops.qc.client.model.rpc.product.SimpleQueryVO;
import com.zto.devops.qc.client.model.rpc.project.VersionConfirmedEvent;
import com.zto.devops.qc.client.model.rpc.project.VersionDeleteEvent;
import com.zto.devops.qc.client.model.rpc.project.VersionVO;
import com.zto.devops.qc.client.service.issue.model.VersionPlannedReq;
import com.zto.devops.qc.domain.gateway.repository.IIssueRepository;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.domain.service.IssueCommandDomainService;
import com.zto.devops.qc.domain.service.TestPlanCommandDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProjectHandler {

    @Autowired
    private IIssueRepository issueRepository;

    @Autowired
    private IssueCommandDomainService issueCommandDomainService;

    @Autowired
    private TestPlanCommandDomainService testPlanCommandDomainService;

    @Autowired
    private AddVersionIssueConvertor convertor;

    @Autowired
    private IProjectRpcService projectRpcService;

    @EventHandler
    public void handleSprintDeletedEvent(SprintDeletedEvent event) {
        log.info("进入SprintDeletedEvent：" + event.getSprintCode());
        List<IssueEntityDO> issueEntities = issueRepository.selectByProductCodeAndCode(event.getProduct().getCode(), event.getSprintCode());
        if (CollectionUtil.isEmpty(issueEntities)) {
            return;
        }
        for (IssueEntityDO entity : issueEntities) {
            log.info("进入缺陷域SprintDeletedEvent：{}-{}", event.getSprintCode(), entity.getCode());
            EditSprintIssueCommand command = new EditSprintIssueCommand(entity.getCode());
            command.setCode(entity.getCode());
            command.setSprint(new Sprint(DefaultValueEnum.SPRINT_VALUE.getValue(), "未规划迭代"));
            command.setAggregateId(entity.getCode());
            command.setTransactor(event.getTransactor());

            SprintInIssueEditedEvent sprintInIssueEditedEvent = new SprintInIssueEditedEvent();
            sprintInIssueEditedEvent.setCode(command.getCode());
            sprintInIssueEditedEvent.setSprint(command.getSprint());
            sprintInIssueEditedEvent.setTransactor(command.getTransactor());
            sprintInIssueEditedEvent.setOccurred(command.getOccurred());
            sprintInIssueEditedEvent.setAggregateId(command.getCode());
            issueCommandDomainService.handleSprintInIssueEditedEvent(sprintInIssueEditedEvent);
        }
    }

    @EventHandler
    public void handleVersionAddEvent(VersionAddedEvent event) {
        // 创建版本后，自动创建新的缺陷，筛选issuecode 不是空的做修改，为空的做新增
        log.info("接受到外部事件成功 VersionAddedEvent {}", JSONUtil.toJsonStr(event));
        issueCommandDomainService.planVersionIssue(convertor.convertVersionBaseEvent(event));
    }


    @EventHandler
    public void handleVersionEditedEvent(VersionEditedEvent event) {
        log.info("接收到项目域版本变更 VersionEditedEvent {}", JSONUtil.toJsonStr(event));
        if (event.getApprovalExitDate() != null
                || event.getPresentationDate() != null) {
            com.zto.devops.qc.client.model.rpc.project.VersionEditedEvent qcEvent = new com.zto.devops.qc.client.model.rpc.project.VersionEditedEvent();
            qcEvent.setApprovalExitDate(event.getApprovalExitDate());
            qcEvent.setPresentationDate(event.getPresentationDate());
            qcEvent.setTransactor(event.getTransactor());
            qcEvent.setCode(event.getCode());
            testPlanCommandDomainService.handleVersionEditedEvent(qcEvent);
        }
        issueCommandDomainService.planVersionIssue(convertor.convertVersionBaseEvent(event));

        //版本名称变更，更新测试计划和测试报告名称
        if (StringUtil.isNotBlank(event.getName()) && Objects.nonNull(event.getOldVersion()) && !event.getName().equals(event.getOldVersion().getName())) {
            com.zto.devops.qc.client.model.rpc.project.VersionEditedEvent qcEvent = new com.zto.devops.qc.client.model.rpc.project.VersionEditedEvent();
            qcEvent.setCode(event.getCode());
            qcEvent.setName(event.getName());
            qcEvent.setTransactor(event.getTransactor());
            testPlanCommandDomainService.updateVersionInfo(qcEvent);
        }
    }

    @EventHandler
    public void handleVersionConfirmedEvent(com.zto.devops.project.client.model.version.event.VersionConfirmedEvent event) {
        VersionConfirmedEvent qc = new com.zto.devops.qc.client.model.rpc.project.VersionConfirmedEvent();
        qc.setCode(event.getCode());
        qc.setProductCode(event.getProductCode());
        qc.setProductName(event.getProductName());
        qc.setType(VersionTypeEnum.getByName(event.getType().name()));
        if (event.getStatus() != null) {
            qc.setStatus(FlowStatusEnum.getEnumByName(event.getStatus().name()));
        }
        qc.setIsConfirm(event.getIsConfirm());
        qc.setName(event.getName());
        qc.setVersionNum(event.getVersionNum());
        qc.setVersionDesc(event.getVersionDesc());
        if (event.getTestStrategy() != null) {
            qc.setTestStrategy(TestPlanStrategyEnum.getEnumByName(event.getTestStrategy().name()));
        }
        qc.setStartDate(event.getStartDate());
        qc.setPresentationDate(event.getPresentationDate());
        qc.setApprovalExitDate(event.getApprovalExitDate());
        qc.setPublishDate(event.getPublishDate());
        qc.setTransactor(event.getTransactor());
        testPlanCommandDomainService.handleVersionConfirmedEvent(qc);
    }

    @EventHandler
    public void handleVersionClosedEvent(com.zto.devops.project.client.model.version.event.VersionClosedEvent event) {
        log.info("接受到外部事件成功 VersionClosedEvent {}", JSONUtil.toJsonStr(event));
        String versionCode = event.getCode();
        SimpleIssueQuery query = new SimpleIssueQuery();
        query.setVersionCodeList(Collections.singletonList(versionCode));
        List<IssueEntityDO> issueEntities = issueRepository.selectBySimpleIssueQuery(query);
        if (CollectionUtil.isNotEmpty(issueEntities)) {
            List<String> issueCodes = issueEntities.stream().map(IssueEntityDO::getCode).collect(Collectors.toList());
            VersionPlannedReq req = new VersionPlannedReq();
            req.setRemovedIssues(issueCodes);
            issueCommandDomainService.handleVersionPlanedEvent(req);
        }
    }

    @EventHandler
    public void handleVersionMatterRemovedEvent(com.zto.devops.project.client.model.version.event.VersionMatterRemovedEvent event) {
        log.info("接受到外部事件成功 VersionMatterRemovedEvent {}", JSONUtil.toJsonStr(event));
        List<String> issueCodes = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(event.getRelatedList())) {
            issueCodes = event.getRelatedList().stream()
                    .filter(e -> e.getDomain().equals(DomainEnum.ISSUE.name()))
                    .map(RelatedItemVO::getCode)
                    .collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(issueCodes)) {
            VersionPlannedReq req = new VersionPlannedReq();
            req.setRemovedIssues(issueCodes);
            issueCommandDomainService.handleVersionPlanedEvent(req);
        }
    }

    @EventHandler
    public void handleVersionPlanedEvent(com.zto.devops.project.client.model.version.event.VersionPlanedEvent event) {
        log.info("接受到外部事件成功 VersionPlanedEvent {}", JSONUtil.toJsonStr(event));
        List<String> issueCodes = new ArrayList<>();
        List<String> oldIssueCodes = new ArrayList<>();
        List<String> copyCodes = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(event.getRelatedList())) {
            issueCodes = event.getRelatedList().stream()
                    .filter(e -> e.getDomain().equals(DomainEnum.ISSUE.name()))
                    .map(RelatedItemVO::getCode)
                    .collect(Collectors.toList());
            copyCodes.addAll(issueCodes);
        }
        if (CollectionUtil.isNotEmpty(event.getOldRelatedList())) {
            oldIssueCodes = event.getOldRelatedList().stream()
                    .filter(e -> e.getDomain().equals(DomainEnum.ISSUE.name()))
                    .map(RelatedItemVO::getCode)
                    .collect(Collectors.toList());
        }

        issueCodes.removeAll(oldIssueCodes);
        oldIssueCodes.removeAll(copyCodes);

        if (CollectionUtil.isNotEmpty(oldIssueCodes)) {
            VersionPlannedReq req = new VersionPlannedReq();
            req.setRemovedIssues(oldIssueCodes);
            issueCommandDomainService.handleVersionPlanedEvent(req);
        }

        if (CollectionUtil.isNotEmpty(issueCodes)) {
            VersionPlannedReq req = new VersionPlannedReq();
            req.setVersionCode(event.getCode());
            VersionVO version = projectRpcService.findVersionQuery(event.getCode());
            if (null != version) {
                req.setVersionName(version.getName());
                req.setConfirm(version.getIsConfirm());
            }
            req.setAddedIssues(issueCodes);
            issueCommandDomainService.handleVersionPlanedEvent(req);
        }
    }

    @EventHandler
    public void handleVersionDeletedEvent(com.zto.devops.project.client.model.version.event.VersionDeletedEvent event) {
        log.info("接收到项目域版本删除事件 VersionDeleteEvent {}", event);
        VersionDeleteEvent deleteEvent = new VersionDeleteEvent();
        deleteEvent.setCode(event.getCode());
        deleteEvent.setOccurred(event.getOccurred());
        testPlanCommandDomainService.handleVersionDeleteEvent(deleteEvent);
    }
}
