package com.zto.devops.qc.adapter.converter;

import com.zto.devops.pipeline.client.model.application.event.ApplicationEditedEvent;
import com.zto.devops.pipeline.client.model.flow.entity.BaseApplicationDO;
import com.zto.devops.pipeline.client.model.flow.event.CreateBranchQcEvent;
import com.zto.devops.pipeline.client.model.flow.event.VersionStatusChangedEvent;
import com.zto.devops.pipeline.client.model.flow.event.deployment.DeploymentAcceptedReportEvent;
import com.zto.devops.pipeline.client.model.flow.event.deployment.DeploymentBackedEvent;
import com.zto.devops.pipeline.client.model.flow.event.deployment.DeploymentDelayAcceptedEvent;
import com.zto.devops.pipeline.client.model.plan.event.ReleasedQcEvent;
import com.zto.devops.pipeline.client.model.rpc.qc.event.AutomaticTaskCDExecutedEvent;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PipelineConverter {

    List<com.zto.devops.qc.client.model.rpc.pipeline.BaseApplicationDO> convertBaseApplicationDOList(List<BaseApplicationDO> appIds);

    com.zto.devops.qc.client.model.rpc.pipeline.event.CreateBranchQcEvent convert(CreateBranchQcEvent event);

    com.zto.devops.qc.client.model.rpc.pipeline.event.ReleasedQcEvent convert(ReleasedQcEvent event);

    com.zto.devops.qc.client.model.rpc.pipeline.AutomaticTaskCDExecutedEvent convert(AutomaticTaskCDExecutedEvent event);

    com.zto.devops.qc.client.model.rpc.pipeline.event.DeploymentBackedEvent convert(DeploymentBackedEvent event);
    com.zto.devops.qc.client.model.rpc.pipeline.event.VersionStatusChangedEvent convert(VersionStatusChangedEvent event);

    com.zto.devops.qc.client.model.rpc.pipeline.event.DeploymentDelayAcceptedEvent convert(DeploymentDelayAcceptedEvent event);

    com.zto.devops.qc.client.model.rpc.pipeline.event.DeploymentAcceptedReportEvent convert(DeploymentAcceptedReportEvent event);

    com.zto.devops.qc.client.model.rpc.pipeline.event.ApplicationEditedEvent convert(ApplicationEditedEvent event);
}
