package com.zto.devops.qc.adapter.converter;

import com.zto.devops.project.client.model.version.entity.RelatedItemVO;
import com.zto.devops.qc.client.model.issue.command.AddVersionIssueCommand;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/4/14
 * @Version 1.0
 */
@Mapper(componentModel = "spring")
public interface AddVersionIssueConvertor {

    List<AddVersionIssueCommand> convertVersion(List<RelatedItemVO> vos);

    com.zto.devops.qc.client.model.rpc.project.VersionBaseEvent convertVersionBaseEvent(com.zto.devops.project.client.model.version.event.VersionAddedEvent event);
    com.zto.devops.qc.client.model.rpc.project.VersionBaseEvent convertVersionBaseEvent(com.zto.devops.project.client.model.version.event.VersionEditedEvent event);

}
