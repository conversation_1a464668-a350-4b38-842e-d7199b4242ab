# API签名鉴权方案

## 概述

本方案基于AK/SK + HMAC-SHA256签名算法，为脚本定时调用的接口提供安全的鉴权机制，解决以下问题：

1. ✅ **无侵入式鉴权**：基于AOP注解实现，不修改业务代码
2. ✅ **无用户信息依赖**：不需要获取用户信息，适合脚本调用
3. ✅ **IP不固定支持**：不依赖IP白名单，支持动态IP
4. ✅ **防盗用机制**：多重安全措施防止token被盗用
5. ✅ **网关兼容**：通过Dubbo attachment传递签名参数

## 核心特性

### 防盗用机制
- **时间窗口限制**：签名5分钟内有效，超时自动失效
- **Nonce防重放**：每次请求使用唯一随机数，防止重放攻击
- **请求体绑定**：签名包含请求体MD5，防止请求被篡改
- **密钥轮换**：支持定期更换SecretKey

### 签名算法
```
StringToSign = HTTPMethod + "\n" + RequestURI + "\n" + Timestamp + "\n" + Nonce + "\n" + RequestBodyMD5
Signature = Base64(HMAC-SHA256(SecretKey, StringToSign))
```

## 使用方法

### 1. 在接口上添加注解

```java
@ApiSignAuth(
    expireSeconds = 300,        // 签名有效期5分钟
    validateBody = true,        // 验证请求体
    timeSkewSeconds = 60,       // 允许60秒时间偏差
    errorMessage = "自定义错误信息"
)
public Result<String> executeSceneDataCenter(SceneDataCenterExecuteReq req) {
    // 业务逻辑
}
```

### 2. 脚本调用示例

```java
// 1. 配置密钥
String accessKey = "AK_SCRIPT_TEST_001";
String secretKey = "sk_test_script_scene_data_center_secret_key_001";

// 2. 设置签名参数
ApiSignAuthClient.setSignAuthForDubboCall(
    accessKey, 
    secretKey, 
    "POST", 
    "/tm/apitest/executeSceneDataCenter", 
    requestBody
);

// 3. 调用接口
Result<String> result = sceneRecordService.executeSceneDataCenter(req);
```

### 3. 配置参数

在application.properties中添加：

```properties
# API签名鉴权配置
qc.api.sign.auth.enabled=true
qc.api.sign.auth.default-expire-seconds=300
qc.api.sign.auth.default-time-skew-seconds=60
qc.api.sign.auth.nonce-cache-expire-seconds=600
qc.api.sign.auth.log-enabled=true
qc.api.sign.auth.skip-in-dev=false
qc.api.sign.auth.redis-cache-prefix=qc:api:sign:auth
```

## 安全特性

### 1. 防重放攻击
- 每次请求必须包含唯一的nonce
- nonce在Redis中缓存，防止重复使用
- 时间戳限制请求的有效时间窗口

### 2. 防篡改
- 签名包含请求体的MD5值
- 任何请求参数的修改都会导致签名验证失败

### 3. 密钥安全
- SecretKey不在网络中传输
- 支持密钥轮换机制
- 可以随时禁用特定的AccessKey

### 4. 时间安全
- 签名包含时间戳，防止过期请求
- 支持时间偏差配置，处理时钟不同步

## 架构设计

```
脚本调用 -> Dubbo Attachment -> ApiSignAuthFilter -> AOP切面 -> 业务方法
    ↓              ↓                    ↓              ↓
  生成签名    -> 传递参数        -> 提取参数      -> 验证签名
```

## 已实现的接口

- ✅ `executeSceneDataCenter` - 执行造数并上传日志
- ✅ `querySceneTaskResult` - 查询造数结果

## 测试验证

运行测试用例验证鉴权功能：

```bash
# 运行签名工具类测试
mvn test -Dtest=SignatureUtilsTest

# 运行集成测试
mvn test -Dtest=SceneRecordServiceSignAuthTest
```

## 生产部署

1. **配置密钥**：在生产环境配置真实的AK/SK
2. **启用Redis**：确保Redis服务可用，用于nonce缓存
3. **监控日志**：关注鉴权相关的日志，及时发现异常
4. **定期轮换**：建议定期轮换SecretKey

## 故障排查

### 常见错误

1. **"缺少签名参数"**：检查Dubbo attachment是否正确设置
2. **"签名已过期"**：检查客户端和服务端时间是否同步
3. **"重复的请求"**：检查nonce是否重复使用
4. **"签名验证失败"**：检查签名算法和参数是否正确

### 调试方法

1. 启用详细日志：`qc.api.sign.auth.log-enabled=true`
2. 开发环境跳过鉴权：`qc.api.sign.auth.skip-in-dev=true`
3. 检查Redis中的nonce缓存：`redis-cli keys "qc:api:sign:auth:nonce:*"`

## 扩展说明

本方案可以轻松扩展到其他需要鉴权的接口，只需要在方法上添加`@ApiSignAuth`注解即可。
