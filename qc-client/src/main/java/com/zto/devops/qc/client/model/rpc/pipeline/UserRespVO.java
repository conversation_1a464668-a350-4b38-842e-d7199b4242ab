package com.zto.devops.qc.client.model.rpc.pipeline;

import java.io.Serializable;

import lombok.Data;

import java.util.Date;

@Data
public class UserRespVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String code;
    private String userName;
    private String realName;
    private String nickName;
    private String quickQuery;
    private String companyId;
    private String companyName;
    private String subcompanyId;
    private String subcompanyName;
    private String departmentId;
    private String departmentName;
    private String departmentCode;
    private String workgroupId;
    private String workgroupName;
    private String workCategory;
    private Integer securityLevel;
    private String signature;
    private String duty;
    private String title;
    private String gender;
    private String birthday;
    private String lang;
    private String theme;
    private Integer score;
    private Integer fans;
    private String homeAddress;
    private Integer isStaff;
    private Integer isVisible;
    private Integer enabled;
    private String auditStatus;
    private Integer deletionstatecode;
    private Integer sortCode;
    private String description;
    private String subdepartmentId;
    private String subdepartmentName;
    private String province;
    private String city;
    private String district;
    private String userFrom;
    private String simpleSpelling;
    private String idCard;
    private Integer managerId;
    private Integer isAdministrator;
    private String managerAuditStatus;
    private java.util.Date managerAuditDate;
    private Integer ischeckBalance;
    private java.util.Date createon;
    private String createUserId;
    private String createby;
    private Date modifiedon;
    private String modifiedUserId;
    private String modifiedby;
    private String mobile;
    private String telephone;
    private Integer mobileValiated;
    private String orgCode;
}
