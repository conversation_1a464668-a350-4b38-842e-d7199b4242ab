package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseRelationVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AddTestcaseEvent extends BaseEvent implements ActionEvent {

    private String code;

    private String productCode;

    private String versionCode;

    private String parentCode;

    private String name;

    private TestcaseAttributeEnum attribute;

    private TestcaseTypeEnum type;

    private TestcasePriorityEnum priority;

    private TestcaseStatusEnum status;

    private String precondition;

    private Long dutyUserId;

    private String dutyUser;

    private String comment;

    private Integer sort;

    private Integer layer;

    private String path;

    private AutomaticNodeTypeEnum nodeType;

    private List<AttachmentVO> attachments;

    private List<TagVO> tags;

    private List<TestcaseRelationVO> vos;

    private List<TestcaseStepVO> testSteps;

    private String automaticSourceCode;

    private String nodeTypePath;

    private String flag;

    private String testcaseModulePath;

    private Boolean setCore;

    private String interfaceName;

    @Override
    public String action() {
        if (TestcaseTypeEnum.AUTO.name().equals(this.type.name())) {
            return "新增自动化用例";
        } else if (TestcaseTypeEnum.MANUAL.name().equals(this.type.name())) {
            return "新增手工用例";
        } else {
            return "新增";
        }
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }
}
