package com.zto.devops.qc.client.model.testmanager.email.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class DetailEmailQuery extends BaseQuery {

    @GatewayModelProperty(description = "邮件记录code", required = false)
    private String emailCode;

    @GatewayModelProperty(description = "计划/报告code", required = false)
    private String businessCode;

    @GatewayModelProperty(description = "关联计划code", required = false)
    private String relatePlanCode;

    @GatewayModelProperty(description = "邮件类型", required = false)
    private EmailTypeEnum emailType;

    @GatewayModelProperty(description = "登陆用户ID", required = true)
    @JsonIgnore
    private Long currentUserId;
}
