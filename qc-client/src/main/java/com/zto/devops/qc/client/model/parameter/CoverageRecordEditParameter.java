package com.zto.devops.qc.client.model.parameter;

import java.io.Serializable;

import com.zto.devops.framework.client.entity.BaseEntityDO;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import lombok.Data;

@Data
public class CoverageRecordEditParameter extends BaseEntityDO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 版本编码
     */
    private String versionCode;

    /**
     * appId
     */
    private String appId;

    /**
     * 备注
     */
    private String comment;

    /**
     * 产品code
     */
    private String productCode;

    /**
     * 差异类型
     */
    private DiffTypeEnum diffType;

}
