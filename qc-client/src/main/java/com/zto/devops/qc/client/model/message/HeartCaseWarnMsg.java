package com.zto.devops.qc.client.model.message;

import com.zto.devops.framework.client.simple.BasePayload;
import lombok.Data;

import java.util.Set;

@Data
public class HeartCaseWarnMsg extends BasePayload {

    /**
     * 内容
     */
    private String content;

    /**
     * 用例名称
     */
    private String caseName;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用负责人
     */
    private String appOwner;

    /**
     * 告警时间
     */
    private String warnTime;

    /**
     * 跳转链接
     */
    private String linkUrl;

    /**
     * 消息接收人
     */
    private Set<String> receivedUsers;
}
