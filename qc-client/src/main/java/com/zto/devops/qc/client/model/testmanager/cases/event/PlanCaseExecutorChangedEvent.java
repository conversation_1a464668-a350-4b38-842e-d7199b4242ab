package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class PlanCaseExecutorChangedEvent extends BaseEvent implements ActionEvent {

    private String caseCode;

    private String planCode;

    private TestPlanStageEnum testStage;

    private Long executorId;

    private String executor;

    private String operateCaseCode;

    @Override
    public String action() {
        return "变更了用例执行人";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return String.format("修改为 >>> %s", this.executor);
    }

    @Override
    public String logBusinessCode() {
        return this.operateCaseCode;
    }
}
