package com.zto.devops.qc.client.model.report.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：SecurityTestResultResp
 * 类 描 述：TODO
 * 创建时间：2021/11/29 10:40 上午
 * 创 建 人：renxinhui
 *
 * <AUTHOR>
 */
@Data
public class SecurityTestResultVO implements Serializable {

    @GatewayModelProperty(description = "是否允许上线", required = false)
    private Boolean allowrelease;

    @GatewayModelProperty(description = "备注", required = false)
    private String comment;

    @GatewayModelProperty(description = "安全测试申请单状态", required = false)
    private String status;

    @GatewayModelProperty(description = "安全测试人员ID", required = false)
    private Long testUserId;

    @GatewayModelProperty(description = "安全测试人员", required = false)
    private String testuser;

    @GatewayModelProperty(description = "漏洞信息", required = false)
    private List<SecurityIssue> issues;

    @GatewayModelProperty(description = "安全测试结果")
    @JsonIgnore
    private SecurityTestResult securityTestResult;

    public SecurityTestResult getSecurityTestResult() {
        if (null != this.getAllowrelease() && this.getAllowrelease()) {
            return SecurityTestResult.PASS;
        } else {
            return SecurityTestResult.NOPASS;
        }
    }
}
