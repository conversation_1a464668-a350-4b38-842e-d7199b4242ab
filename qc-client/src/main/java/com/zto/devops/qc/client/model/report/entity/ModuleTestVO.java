package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@GatewayModel(description = "模块测试")
@Data
public class ModuleTestVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "模块code", required = true)
    private String code;

    @GatewayModelProperty(description = "报告code", required = true)
    private String reportCode;

    @GatewayModelProperty(description = "测试类型", required = true)
    private String testType;

    @GatewayModelProperty(description = "测试j结果 0失败；1通过", required = true)
    private Integer testResult;

    @GatewayModelProperty(description = "测试j结果描述 -1失败；1通过", required = true)
    private String testResultDesc;

    @GatewayModelProperty(description = "有效缺陷数", required = true)
    private Integer validIssueCount;

    @GatewayModelProperty(description = "遗留P012缺陷数", required = true)
    private Integer legacyIssueCount;

    @GatewayModelProperty(description = "报告人id", required = true)
    private Long reportUserId;

    @GatewayModelProperty(description = "报告人名称", required = true)
    private String reportUserName;

}