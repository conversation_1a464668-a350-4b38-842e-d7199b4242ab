package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ZModel(description = "查询测试计划按钮VO")
public class PlanCaseButtonVO implements Comparable<PlanCaseButtonVO>, Serializable {

    private static final long serialVersionUID = 1L;

    @ZModelProperty(description = "按钮名称")
    private String name;

    @ZModelProperty(description = "按钮code")
    private String code;

    @ZModelProperty(description = "类型")
    private String type;

    @ZModelProperty(description = "0-disable 1-enable")
    private Integer index;

    @ZModelProperty(description = "隐藏原因")
    private List<String> hiddenReasonList;

    public PlanCaseButtonVO(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public PlanCaseButtonVO(String name, String code, String type, Integer index) {
        this.name = name;
        this.code = code;
        this.type = type;
        this.index = index;
    }

    public PlanCaseButtonVO(String name, String code, String type, Integer index, List<String> hiddenReasonList) {
        this.name = name;
        this.code = code;
        this.type = type;
        this.index = index;
        this.hiddenReasonList = hiddenReasonList;
    }

    public PlanCaseButtonVO() {
    }

    @Override
    public int compareTo(PlanCaseButtonVO o) {
        return index.compareTo(o.getIndex());
    }
}
