package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AutomaticSuccessCommand extends BaseCommand {

    public AutomaticSuccessCommand(String aggregateId) {
        super(aggregateId);
    }

    private String code;

    private Integer addCaseNo;

    private Integer updateCaseNo;

    private Integer deleteCaseNo;

    private String fileName;

    private String productCode;

    private String commitId;
}