package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AutomaticFailEvent extends BaseEvent {
    private String code;
    private String productCode;
    private String comment;


    /**
     * 登记库名称
     */
    private String name;

    /**
     * 备注
     */

    /**
     * jmeter:源地址-OSS上的脚本文件地址;xunit-git地址
     */
    private String address;

    /**
     * 类型：1-jmeter, 2-testng, 3-postman, 4-junit,5-pyunit
     */
    private AutomaticRecordTypeEnum type;

    /**
     * 脚本文件名
     */
    private String fileName;

    /**
     * 数据文件地址
     */
    private String dataFileAddress;

    /**
     * 扩展jar包地址
     */
    private String extendJarAddress;

    /**
     * 第三方jar包地址
     */
    private String thirdJarAddress;


    private String bucketName;

    private String lastAutomaticSourceLogCode;
}
