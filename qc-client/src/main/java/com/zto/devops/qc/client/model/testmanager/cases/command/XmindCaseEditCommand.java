package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.XmindOperationEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class XmindCaseEditCommand extends BaseCommand {

    public XmindCaseEditCommand(String aggregateId) {
        super(aggregateId);
    }

    /**
     * 用例code
     */
    private String id;

    /**
     * 节点名称
     */
    private String topic;

    /**
     * 父节点code
     */
    private String parentCode;

    /**
     * 产品code
     */
    private String productCode;

    /**
     * 节点标签
     */
    private String tag;

    /**
     * 节点操作
     */
    private XmindOperationEnum operation;

    private TestcaseAttributeEnum attribute;

    private String oldParentCode;

    private String oldPath;

    private String newPath;

    private String versionCode;

    private Boolean setCore;

}
