package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ListExecuteCaseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private String code;

    private String automaticTaskCode;

    private String name;

    private TestcaseAttributeEnum attribute;

    private String parentCode;

    private String path;

    private Integer sort;

    private Boolean enable;

    private TestcaseTypeEnum type;

    private TestPlanCaseStatusEnum result;

    private Long executorId;

    private String executor;

    private Date startTime;

    private Date finishTime;

    private String reportFile;

    private String execLogFile;

    private Integer testcaseCount;

    private List<ListExecuteCaseVO> children;

    private String status;
}
