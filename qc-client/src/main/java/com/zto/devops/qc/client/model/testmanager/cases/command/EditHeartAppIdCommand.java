package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class EditHeartAppIdCommand extends BaseCommand {

    public EditHeartAppIdCommand(String aggregateId) {
        super(aggregateId);
    }

    @ZsmpModelProperty(description = "用例code", required = true)
    private String caseCode;

    @ZsmpModelProperty(description = "产品code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "AppID", required = true)
    private String appId;

    @ZsmpModelProperty(description = "AppID的名称", required = true)
    private String appIdName;

    @ZsmpModelProperty(description = "AppID的code", required = true)
    private String appIdCode;

}
