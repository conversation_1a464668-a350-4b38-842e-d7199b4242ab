package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class PageAutomaticTaskGroupQuery extends PageQueryBase {

    /**
     * 执行环境列表（多选）
     */
    private List<String> executeEnvList;

    private List<String> automaticCodeList;

    private String schedulerCode;

    private String productCode;

    private List<AutomaticStatusEnum> statusList;

    private List<String> versionCodeList;

    private List<String> testPlanCodeList;

    private List<AutomaticTaskTrigModeEnum> trigModeList;

    private List<Long> executorIdList;

    private Date startTimeBegin;

    private Date startTimeEnd;

    private Date finishTimeBegin;

    private Date finishTimeEnd;

    private String taskId;

    /**
     * 符合查询条件id集合，后端自用
     */
    @JsonIgnore
    private String taskIdList;
}
