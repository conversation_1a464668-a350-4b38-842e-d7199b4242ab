package com.zto.devops.qc.client.model.testPlan.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TestPlanListQuery extends PageQueryBase implements Serializable {

    private String productCode;

    private String title;

    private List<String> types;

    private Long testDirectorId;

    private Date createTimeStart;

    private Date createTimeEnd;

    private String status;

    private List<String> statusList;

    private String versionCode;

    private List<String> productCodes;

    private Long creatorId;

    private String orderField;
    private String orderType;
}
