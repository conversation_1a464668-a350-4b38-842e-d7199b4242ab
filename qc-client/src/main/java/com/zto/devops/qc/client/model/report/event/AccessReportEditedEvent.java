package com.zto.devops.qc.client.model.report.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.report.entity.CaseDetailVO;
import com.zto.devops.qc.client.model.report.entity.CaseStatisticsVO;
import com.zto.devops.qc.client.model.report.entity.IssueInfoVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class AccessReportEditedEvent extends ReportAddedEvent implements ActionEvent {

    private List<CaseDetailVO> caseDetailVOS;

    private List<CaseStatisticsVO> caseStatisticsVOS;

    private List<IssueInfoVO> issueInfoVOS;



    @Override
    public String action() {
        return "";
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        return "";
    }

}
