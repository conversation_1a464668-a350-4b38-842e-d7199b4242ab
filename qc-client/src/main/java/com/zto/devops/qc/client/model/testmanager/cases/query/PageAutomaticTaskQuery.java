package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class PageAutomaticTaskQuery extends PageQueryBase {

    private String productCode;

    private List<AutomaticStatusEnum> statusList;

    private List<String> versionCodeList;

    private List<String> testPlanCodeList;

    private List<AutomaticTaskTrigModeEnum> trigModeList;

    private List<Long> executorIdList;

    private Date startTimeBegin;

    private Date startTimeEnd;

    private Date finishTimeBegin;

    private Date finishTimeEnd;

    private String taskId;
}
