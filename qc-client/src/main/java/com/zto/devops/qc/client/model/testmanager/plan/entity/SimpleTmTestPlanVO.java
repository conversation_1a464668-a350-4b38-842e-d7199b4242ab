package com.zto.devops.qc.client.model.testmanager.plan.entity;

import com.zto.devops.qc.client.enums.testmanager.plan.*;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@GatewayModel(description = "测试计划")
public class SimpleTmTestPlanVO implements Serializable {
    @GatewayModelProperty(description = "计划编号", required = false)
    private String code;
    @GatewayModelProperty(description = "计划名", required = false)
    private String planName;
    @GatewayModelProperty(description = "计划类型", required = false)
    private TestPlanNewTypeEnum type;
    @GatewayModelProperty(description = "计划类型 描述", required = false)
    private String typeDesc;
    @GatewayModelProperty(description = "测试策略", required = false)
    private TestPlanStrategyEnum testStrategy;
    @GatewayModelProperty(description = "策略 描述", required = false)
    private String testStrategyDesc;
    @GatewayModelProperty(description = "状态", required = false)
    private TestPlanNewStatusEnum status;
    @GatewayModelProperty(description = "状态 描述", required = false)
    private String statusDesc;

    @GatewayModelProperty(description = "关联计划code", required = false)
    private String relationPlanCode;
    @GatewayModelProperty(description = "关联计划名称", required = false)
    private String relationPlanName;
    @GatewayModelProperty(description = "所属部门Id", required = false)
    private Long deptId;
    @GatewayModelProperty(description = "所属部门名称", required = false)
    private String deptName;
    @GatewayModelProperty(description = "产品code", required = false)
    private String productCode;
    @GatewayModelProperty(description = "产品名称", required = false)
    private String productName;
    @GatewayModelProperty(description = "版本code", required = false)
    private String versionCode;
    @GatewayModelProperty(description = "版本名称", required = false)
    private String versionName;
    @GatewayModelProperty(description = "版本开始时间", required = false)
    private Date startDate;
    @GatewayModelProperty(description = "版本上线时间", required = false)
    private Date publishDate;
    @GatewayModelProperty(description = "计划提测/准入时间", required = false)
    private Date accessDate;
    @GatewayModelProperty(description = "计划提测时间-上下午", required = false)
    private TestPlanDatePartitionEnum accessDatePartition;
    @GatewayModelProperty(description = "计划提测时间-上下午描述", required = false)
    private String accessDatePartitionDesc;
    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date permitDate;
    @GatewayModelProperty(description = "计划准出时间-上下午", required = false)
    private TestPlanDatePartitionEnum permitDatePartition;
    @GatewayModelProperty(description = "计划准出时间-上下午描述", required = false)
    private String permitDatePartitionDesc;
    @GatewayModelProperty(description = "开发人数", required = false)
    private Integer developerNum;
    @GatewayModelProperty(description = "测试人数", required = false)
    private Integer testerNum;
    @GatewayModelProperty(description = "备注", required = false)
    private String comment;
    @GatewayModelProperty(description = "产品负责人id", required = false)
    private Long productDirectorId;
    @GatewayModelProperty(description = "产品负责人名", required = false)
    private String productDirectorName;
    @GatewayModelProperty(description = "测试负责人id", required = false)
    private Long testDirectorId;
    @GatewayModelProperty(description = "测试负责人名", required = false)
    private String testDirectorName;
    @GatewayModelProperty(description = "计划负责人id", required = false)
    private Long planDirectorId;
    @GatewayModelProperty(description = "计划负责人姓名", required = false)
    private String planDirectorName;
    @GatewayModelProperty(description = "计划负责人姓名(数据库真实值)", required = false)
    private String planDirectorNameReal;

    @GatewayModelProperty(description = "移动专项测试", required = false)
    private Boolean mobileSpecialTest;
    @GatewayModelProperty(description = "安全扫描", required = false)
    private Boolean securityScan;

    @GatewayModelProperty(description = "静态分析", required = false)
    private Boolean staticAnalysis;
    @GatewayModelProperty(description = "静态分析时间", required = false)
    private Date staticAnalysisTime;
    @GatewayModelProperty(description = "静态分析负责人id", required = false)
    private Long staticAnalysisDirectorId;
    @GatewayModelProperty(description = "静态分析负责人姓名", required = false)
    private String staticAnalysisDirectorName;

    @GatewayModelProperty(description = "性能测试", required = false)
    private Boolean performanceTest;
    @GatewayModelProperty(description = "性能测试时间", required = false)
    private Date performanceTestTime;
    @GatewayModelProperty(description = "性能测试负责人id", required = false)
    private Long performanceTestDirectorId;
    @GatewayModelProperty(description = "性能测试负责人名", required = false)
    private String performanceTestDirectorName;

    @GatewayModelProperty(description = "探索性测试", required = false)
    private Boolean exploratoryTest;
    @GatewayModelProperty(description = "探索性测试负责人", required = false)
    private Long exploratoryTestDirectorId;
    @GatewayModelProperty(description = "探索性测试负责人名", required = false)
    private String exploratoryTestDirectorName;
    @GatewayModelProperty(description = "探索性测试时间", required = false)
    private Date exploratoryTestTime;

    @GatewayModelProperty(description = "权限测试", required = false)
    private Boolean permissionsTest;
    @GatewayModelProperty(description = "优先级", required = false)
    private TestPlanPriorityEnum priority;
    @GatewayModelProperty(description = "优先级 描述", required = false)
    private String priorityDesc;
    @GatewayModelProperty(description = "最晚测试日期", required = false)
    private Date lastTestDate;
    @GatewayModelProperty(description = "测试信息", required = false)
    private String testInformation;
    @GatewayModelProperty(description = "权限测试信息", required = false)
    private String permissionsTestInformation;

    @GatewayModelProperty(description = "创建人编码", required = false)
    private Long creatorId;

    @GatewayModelProperty(description = "创建人名称", required = false)
    private String creator;

    @GatewayModelProperty(description = "创建时间", required = false)
    private Date gmtCreate;

    @GatewayModelProperty(description = "修改时间", required = false)
    private Long modifierId;

    @GatewayModelProperty(description = "修改人编码", required = false)
    private String modifier;

    @GatewayModelProperty(description = "修改时间", required = false)
    private Date gmtModified;

    @GatewayModelProperty(description = "修改次数", required = false)
    private Integer editNo;

    @GatewayModelProperty(description = "阶段状态", sample = "(测试阶段，SMOKE_TEST-冒烟测试|FUNCTIONAL_TEST-功能测试|ONLINE_SMOKE_TEST-线上冒烟测试):(INITIAL-未开始|IN_PROGESS进行中|COMPLETED已完成)", required = false)
    private Map<String, Object> stageStatus;

    public String getStatusDesc() {
        return  status == null ? "" : status.getValue();
    }

    public String getTypeDesc() {
        return  type == null ? "" : type.getValue();
    }

    public String getTestStrategyDesc() {
        return  testStrategy == null || testStrategy.equals(TestPlanStrategyEnum.NULL_TEST) ? "-" : testStrategy.getValue();
    }

    public String getAccessDatePartitionDesc() {
        return  accessDatePartition == null ? "" : accessDatePartition.getValue();
    }

    public String getPermitDatePartitionDesc() {
        return  permitDatePartition == null ? "" : permitDatePartition.getValue();
    }

    public String getPriorityDesc() {
        return  priority == null ? "" : priority.getValue();
    }

}
