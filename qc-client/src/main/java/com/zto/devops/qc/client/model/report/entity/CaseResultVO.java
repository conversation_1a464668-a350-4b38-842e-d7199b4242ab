package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@GatewayModel(description = "用例执行结果")
@Data
public class CaseResultVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "编号code", required = false)
    private String code;

    @GatewayModelProperty(description = "报告code", required = false)
    private String reportCode;

    @GatewayModelProperty(description = "轮次", required = true)
    private Integer rounds;

    @GatewayModelProperty(description = "名称", required = true)
    private String name;

    @GatewayModelProperty(description = "用例总数", required = true)
    private Integer totalCaseCount;

    @GatewayModelProperty(description = "执行的用例总数", required = true)
    private Integer executeCaseCount;


    /**
     * 通过的用例总数
     */
    @GatewayModelProperty(description = "通过的用例总数", required = true)
    private Integer accessCaseCount;

    /**
     * 失败的用例总数
     */
    @GatewayModelProperty(description = "失败的用例总数", required = true)
    private Integer failedCaseCount;

    /**
     * 阻塞的用例总数
     */
    @GatewayModelProperty(description = "阻塞的用例总数", required = true)
    private Integer blockedCaseCount;

}