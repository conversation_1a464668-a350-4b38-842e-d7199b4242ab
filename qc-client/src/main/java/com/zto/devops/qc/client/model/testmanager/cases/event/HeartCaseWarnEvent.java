package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.MyOperatedEvent;
import com.zto.devops.framework.client.event.ObservedEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
public class HeartCaseWarnEvent extends BaseEvent implements ActionEvent, MyOperatedEvent, ObservedEvent {

    /**
     * 心跳用例code
     */
    private String caseCode;

    /**
     * 用例名称
     */
    private String caseName;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 自动化用例源登记code
     */
    private String automaticTaskCode;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用负责人
     */
    private String appOwner;

    /**
     * 告警时间
     */
    private Date warnTime;

    /**
     * 产品主测试
     */
    private User mainTester;

    /**
     * 心跳用例创建人
     */
    private User creator;
    /*
    心跳用例更新人
     */
    private User modifier;

    /**
     * 心跳用例关注人
     */
    private List<User> followerList;

    /**
     * 消息接收人
     */
    private Set<String> receivedUsers;

    public Set<String> getReceivedUsers() {
        List<User> userList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(followerList)) {
            userList.addAll(this.followerList);
        }
        if (null != mainTester) {
            userList.add(this.mainTester);
        }
        if (null != creator) {
            userList.add(this.creator);
        }
        if (null != modifier) {
            userList.add(this.modifier);
        }
        if (userList.size() > 0) {
            this.receivedUsers = userList.stream().map(user -> (String.valueOf(user.getUserId()))).collect(Collectors.toSet());
        }
        return this.receivedUsers;
    }

    @Override
    public String getBusinessCode() {
        return null;
    }

    @Override
    public User getOperator() {
        return null;
    }

    @Override
    public DomainEnum domain() {
        return null;
    }

    @Override
    public String actionCode() {
        return null;
    }

    @Override
    public String action() {
        return null;
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }
}
