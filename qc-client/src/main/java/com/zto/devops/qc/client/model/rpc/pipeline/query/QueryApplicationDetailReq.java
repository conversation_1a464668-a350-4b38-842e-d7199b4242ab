package com.zto.devops.qc.client.model.rpc.pipeline.query;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryApplicationDetailReq implements Serializable {
    /**
     * 应用编码 支持appCode和appId
     */
    @GatewayModelProperty(description = "应用编码")
    private String code;
}
