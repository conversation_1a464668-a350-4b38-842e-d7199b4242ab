package com.zto.devops.qc.client.model.testPlan.event;

import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class EditSafetyTestPlanEvent extends AddTestPlanEvent {

    private String testPlanMainCode;

    private String code;

    private Boolean permissionsTest;

    private String priority;

    private Date lastTestDate;

    /**
     * 测试信息
     */
    private String testInformation;

    /**
     * 权限测试信息
     */
    private String priorityTestInformation;

    private List<SendUserInfoVO> recipients;

    //抄送人
    private List<SendUserInfoVO> ccUsers;

    private String testPlanCode;

    private String productCode;

    private String planName;

    private String productName;

    private String versionCode;
    private String versionName;
}
