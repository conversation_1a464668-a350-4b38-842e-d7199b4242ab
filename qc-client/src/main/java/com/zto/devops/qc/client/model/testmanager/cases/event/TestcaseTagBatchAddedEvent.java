package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.issue.TagTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class TestcaseTagBatchAddedEvent extends BaseEvent {

    private String code;

    private DomainEnum domain;

    private List<String> businessCodes;

    private TagTypeEnum type;

    private List<String> tagNames;

}
