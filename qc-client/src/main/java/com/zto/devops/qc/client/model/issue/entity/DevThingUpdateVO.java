package com.zto.devops.qc.client.model.issue.entity;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DevThingUpdateVO implements Serializable {

    @GatewayModelProperty(description = "REQUIREMENT/TASK/ISSUE", required = false)
    private String domain;

    @GatewayModelProperty(description = "事项业务编码", required = false)
    private String businessCode;

    @GatewayModelProperty(description = "处理人id", required = false)
    private Long newId;

    @GatewayModelProperty(description = "处理人姓名", required = false)
    private String newName;

    @GatewayModelProperty(description = "原处理人id", required = false)
    private Long oldId;
}
