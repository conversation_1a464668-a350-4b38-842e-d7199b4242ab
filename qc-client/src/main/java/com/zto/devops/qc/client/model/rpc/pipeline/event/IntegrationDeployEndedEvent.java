package com.zto.devops.qc.client.model.rpc.pipeline.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.ObservedEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.rpc.FlowStatusEnum;
import com.zto.devops.qc.client.enums.rpc.FlowTagEnum;
import com.zto.devops.qc.client.enums.rpc.PlanStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * @Description: integration部署已结束事件
 * @Author: cher
 * @Date: 2021/9/6 10:49
 **/
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class IntegrationDeployEndedEvent extends BaseEvent implements ActionEvent, ObservedEvent {

    private String code;
    private FlowStatusEnum status;
    private FlowStatusEnum preStatus;
    private FlowTagEnum tag;
    private PlanStatusEnum lastDeployResult;
    private boolean retryOrFire;
    private boolean mergeCodeFail;

    /**
     * 通过planCode查询部署相关信息，便于发送钉钉消息
     */
    private String planCode;

    @Override
    public String action() {
        if (mergeCodeFail) {
            return "合并代码失败";
        }
        if (retryOrFire) {
            return "重试" + lastDeployResult.getValue();
        }
        return "部署" + lastDeployResult.getValue();
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return getOccurred();
    }

    @Override
    public String makeString() {
        if (mergeCodeFail) {
            return "【功能分支】合并代码到【TEST】分支失败，请先解决冲突";
        }
        return "";
    }
}
