package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.testmanager.cases.HeartCaseSwitchEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListTestcaseVO extends TestcaseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private long id;

    private String parentIds;

    /**
     * 执行次数
     */
    private Integer executeNum;

    /**
     * 用例数
     */
    private Integer testcaseCount = 0;

    /**
     * 子节点
     */
    private List<ListTestcaseVO> children;

    /**
     * 开关按钮状态
     */
    private HeartCaseSwitchEnum switchStatus;

    /**
     * 是否可勾选
     */
    private Boolean enableChecked;

    /**
     * 执行状态
     */
    private TestPlanCaseStatusEnum executionStatus;

    /**
     * 执行状态中文
     */
    private String executionStatusDesc;

    /**
     * 用例工厂顶级分组code
     */
    private String sceneTopModuleCode;

    /**
     * 是否关联场景
     */
    private Boolean sceneFlag;

    public Boolean getSceneFlag() {
        if (null == this.sceneFlag && StringUtil.isNotBlank(this.sceneTopModuleCode)) {
            this.sceneFlag = (StringUtil.isNotBlank(super.getCode()) && super.getCode().equals(this.sceneTopModuleCode))
                    || (StringUtil.isNotBlank(super.getPath()) && super.getPath().contains(this.sceneTopModuleCode));
        }
        return null == this.sceneFlag ? Boolean.FALSE : this.sceneFlag;
    }
}
