package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.EditHeartFollowerEnum;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
public class EditHeartFollowerCommand extends BaseCommand implements Serializable {

    public EditHeartFollowerCommand(String aggregateId) {
        super(aggregateId);
    }

    @ZsmpModelProperty(description = "产品code")
    private String productCode;

    @ZsmpModelProperty(description = "用例code")
    private String caseCode;

    @ZsmpModelProperty(description = "关注人")
    private SendUserInfoVO follower;

    @ZsmpModelProperty(description = "操作 添加ADD，删除DELETE")
    private EditHeartFollowerEnum operation;

}
