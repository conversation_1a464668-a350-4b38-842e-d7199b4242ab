package com.zto.devops.qc.client.model.testmanager.plan.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanDatePartitionEnum;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Data
public class TmTestPlanSendEmailVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "邮件类型", required = false)
    private EmailTypeEnum emailType;

    @GatewayModelProperty(description = "报告/计划code", required = false)
    private String businessCode;

    @GatewayModelProperty(description = "报告/计划名称", required = false)
    private String businessName;

    @GatewayModelProperty(description = "关联计划code", required = false)
    private String relationPlanCode;

    @GatewayModelProperty(description = "关联计划名称", required = false)
    private String relationPlanName;

    @GatewayModelProperty(description = "所属产品id", required = false)
    private String productCode;

    @GatewayModelProperty(description = "产品名称", required = false)
    private String productName;

    @GatewayModelProperty(description = "关联版本code", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "关联版本名称", required = false)
    private String versionName;

    @GatewayModelProperty(description = "计划提测/准入时间", required = false)
    private Date accessDate;

    @GatewayModelProperty(description = "计划提测时间:上午-AM | 下午-PM", required = false)
    private TestPlanDatePartitionEnum accessDatePartition;

    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date permitDate;

    @GatewayModelProperty(description = "计划提测时间:上午-AM | 下午-PM", required = false)
    private TestPlanDatePartitionEnum permitDatePartition;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<SendUserInfoVO> receiveUsers;

    @GatewayModelProperty(description = "抄送人", required = false)
    private List<SendUserInfoVO> ccUsers;

    @GatewayModelProperty(description = "预览html", required = false)
    private String preview;

    @GatewayModelProperty(description = "发送时间", required = false)
    private Date sendDate;

    @GatewayModelProperty(description = "发送人id", required = false)
    private Long senderId;

    @GatewayModelProperty(description = "发送人", required = false)
    private String sender;

    public static Date getAccessDate(TmTestPlanSendEmailVO vo) {
        if(vo.getAccessDatePartition() != null) {
            SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat secondFatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try{
                if (vo.getAccessDatePartition().equals(TestPlanDatePartitionEnum.AM)) {
                    return secondFatter.parse(dayFormat.format(vo.getAccessDate()) + " 09:00:00");
                } else {
                    return secondFatter.parse(dayFormat.format(vo.getAccessDate()) + " 18:00:00");
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static Date getPermitDate(TmTestPlanSendEmailVO vo) {
        if(vo.getPermitDatePartition() != null) {
            SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat secondFatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try{
                if (vo.getPermitDatePartition().equals(TestPlanDatePartitionEnum.AM)) {
                    return secondFatter.parse(dayFormat.format(vo.getPermitDate()) + " 09:00:00");
                } else {
                    return secondFatter.parse(dayFormat.format(vo.getPermitDate()) + " 18:00:00");
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

}
