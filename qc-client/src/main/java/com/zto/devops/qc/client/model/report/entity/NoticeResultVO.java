package com.zto.devops.qc.client.model.report.entity;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class NoticeResultVO implements Serializable {

    /**
     * code
     */
    @GatewayModelProperty(description = "code", required = true)
    private String code;
    /**
     * 业务编码
     */
    @GatewayModelProperty(description = "业务编码", required = true)
    private String businessCode;

    /**
     * 用户id
     */
    @GatewayModelProperty(description = "用户id", required = true)
    private Integer userId;

    /**
     * 用户姓名
     */
    @GatewayModelProperty(description = "用户姓名", required = true)
    private String userName;

    /**
     * 用户类型
     */
    @GatewayModelProperty(description = "用户类型", required = true)
    private String userType;

    /**
     * 通知类型
     */
    @GatewayModelProperty(description = "通知类型", required = true)
    private String noticeType;

    /**
     * 发送状态
     */
    private String status;

    /**
     * 发送时间
     */
    @GatewayModelProperty(description = "发送时间", required = true)
    private Date sendTime;

    /**
     * 投递失败原因
     */
    @GatewayModelProperty(description = "投递失败原因", required = true)
    private String failSendReson;

    /**
     * 消息id，用于快速检索
     */
    @GatewayModelProperty(description = "消息id", required = true)
    private String msgId;

}