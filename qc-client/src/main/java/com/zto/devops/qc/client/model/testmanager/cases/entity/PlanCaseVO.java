package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class PlanCaseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 测试计划code
     */
    private String planCode;

    /**
     * 用例code
     */
    private String caseCode;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 版本编码
     */
    private String versionCode;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 上级code
     */
    private String parentCode;

    /**
     * 全部上级名称
     */
    private List<String> parentFullName;

    /**
     * 名称
     */
    private String name;

    /**
     * 用例类型
     */
    private TestcaseTypeEnum type;

    /**
     * 等级
     */
    private TestcasePriorityEnum priority;

    /**
     * 状态
     */
    private TestPlanCaseStatusEnum status;

    /**
     * 前置条件
     */
    private String precondition;

    /**
     * 备注
     */
    private String comment;

    /**
     * 执行人id
     */
    private Long executorId;

    /**
     * 执行人
     */
    private String executor;

    /**
     * 已启用/删除
     */
    private Boolean enable;

    /**
     * 测试步骤
     */
    private List<TestcaseStepVO> testSteps;

    /**
     * 标签
     */
    private List<TagVO> tags;

    /**
     * 可编辑字段
     */
    private List<TestcaseEditFieldEnum> fields;

    /**
     * 可操作按钮
     */
    private List<TestcaseButtonEnum> buttons;

    /**
     * 节点类型
     */
    private AutomaticNodeTypeEnum nodeType;

    /**
     * 更新人id
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 上级code
     */
    private String path;

    /**
     * 测试阶段
     */
    private TestPlanStageEnum testStage;

    /**
     * 测试计划负责人id
     */
    private Long planDirectorId;

    /**
     * 用例创建人id
     */
    private Long creatorId;

    /**
     * 用例责任人id
     */
    private Long dutyUserId;

    /**
     * 测试计划状态
     */
    private TestPlanNewStatusEnum planStatus;

    /**
     * 测试计划阶段状态
     */
    private Map<String, Object> stageStatus;

    /**
     * 执行结果文件
     */
    private String resultFile;

    /**
     * 日志记录code
     */
    private String operateCaseCode;

    /**
     * 执行结果文件内容
     */
    private List<ExecuteCaseResultContentVO> resultContent;

    /**
     * 自动化任务id
     */
    private String automaticTaskId;

    /**
     * 报告
     */
    private String reportFile;

    /**
     * 执行日志
     */
    private String execLogFile;

    /**
     * 用例状态
     */
    private TestcaseStatusEnum testcaseStatus;

    /**
     * 自动化类型
     */
    private AutomaticRecordTypeEnum automaticRecordType;

    /**
     * dubbo接口名
     */
    private String interfaceName;

    /**
     * 结果描述
     */
    private String resultComment;
}
