package com.zto.devops.qc.client.model.issue.query;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.IssueTestMethod;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class IssueStatisticsQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    private String businessCode;
    /**
     * 匹配的测试方法
     */
    private List<IssueTestMethod> issueTestMethodList;
    /**
     * 过滤的测试方法
     */
    private List<IssueTestMethod> filterIssueTestMethodList;

    @GatewayModelProperty(description = "next节点", sample = "", required = false)
    private IssueStatus nextStatus;

    public IssueStatisticsQuery(String businessCode) {
        super();
        this.businessCode = businessCode;
    }

    public IssueStatisticsQuery() {
    }
}
