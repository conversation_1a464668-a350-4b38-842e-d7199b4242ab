package com.zto.devops.qc.client.model.report.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExcelModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目名称：qc-parent
 * 类 名 称：ReportMobileResp
 * 类 描 述：TODO
 * 创建时间：2021/11/24 3:12 下午
 * 创 建 人：bulecat
 */
@Data
public class ReportMobileResp implements Serializable , ExcelModel {
 /*   @ExcelProperty("编号")
    @GatewayModelProperty(description = "编号", required = false)
    private String frogSerialId;*/

    @ExcelProperty("用例名")
    @GatewayModelProperty(description = "用例名", required = false)
    private String name;

    @ExcelProperty("执行结果")
    @GatewayModelProperty(description = "执行结果", required = false)
    private String executeResultDesc;

    @GatewayModelProperty(description = "执行结果Code", required = false)
    private String executeResult;

    @Override
    public boolean isBeanNone() {
        return StringUtil.isBlank(this.name) && StringUtil.isBlank(this.executeResultDesc) && StringUtil.isBlank(this.executeResult);
    }
}
