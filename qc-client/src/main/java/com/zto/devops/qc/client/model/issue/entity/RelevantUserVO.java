package com.zto.devops.qc.client.model.issue.entity;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class RelevantUserVO implements Serializable {
    private RelevantUserTypeEnum type;
    private String businessCode;
    private String code;
    private Long userId;
    private String userName;
    private DomainEnum domain;
    private Boolean canDelete = false;
    //创建人ID
    private Long creatorId;
    //创建人
    private String creator;
}
