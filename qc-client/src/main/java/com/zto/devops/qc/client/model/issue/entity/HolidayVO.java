package com.zto.devops.qc.client.model.issue.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class HolidayVO implements Serializable {
    private Long id;
    private String holidayCode;
    private Date holiday;

    /**
     * 假期类型（0：工作日，1：周末，2：节假日，3：公司假日）
     */
    private Integer type;

    private String remark;

    private Long code;

    private Date createDate;

    private String createUserId;

    private String createUserName;

    private Date updateDate;

    private String updateUserId;

    private String updateUserName;

    private Integer workDay;

    private Integer totalDay;

    private static final long serialVersionUID = 1L;
}

