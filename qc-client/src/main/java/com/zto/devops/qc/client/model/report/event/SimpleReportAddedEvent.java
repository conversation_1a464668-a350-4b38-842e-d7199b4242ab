package com.zto.devops.qc.client.model.report.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.OperationTypeEnum;
import com.zto.devops.qc.client.enums.report.CodeCoverResult;
import com.zto.devops.qc.client.model.report.entity.CaseResultVO;
import com.zto.devops.qc.client.model.report.entity.IssueInfoVO;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class SimpleReportAddedEvent extends ReportAddedEvent implements ActionEvent {

    @GatewayModelProperty(description = "是否按计划范围上线", required = false)
    private Integer asPlanedOnline;

    @GatewayModelProperty(description = "是否延期", required = false)
    private Integer delay;

    @GatewayModelProperty(description = "开发人数", required = false)
    private Integer developerCount;

    @GatewayModelProperty(description = "测试人数", required = false)
    private Integer testerCount;

    @GatewayModelProperty(description = "计划冒烟用例数", required = false)
    private Integer planSmokeCase;

    @GatewayModelProperty(description = "首次冒烟用例数", required = false)
    private Integer firstPermitSmoke;

    @GatewayModelProperty(description = "用例执行结果", required = false)
    private List<CaseResultVO> caseResultVOS;

    @GatewayModelProperty(description = "缺陷信息统计", required = false)
    private List<IssueInfoVO> issueInfoVOS;

    @GatewayModelProperty(description = "遗留缺陷", required = false)
    private List<IssueLegacyVO> issueLegacyVOS;

    @GatewayModelProperty(description = "代码覆盖率结果", required = false)
    private CodeCoverResult codeCoverResult;

    @GatewayModelProperty(description = "代码覆盖率不达标原因", required = false)
    private List<Map<String, String>> codeCoverReason;

    @GatewayModelProperty(description = "版本开始时间", required = false)
    private Date planStartDate;


    @GatewayModelProperty(description = "版本上线时间", required = false)
    private Date planPublishDate;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date planPresentationDate;
    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date planApprovalExitDate;


    @Override
    public String action() {
        return OperationTypeEnum.ADDISSUE.getValue();
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }

}
