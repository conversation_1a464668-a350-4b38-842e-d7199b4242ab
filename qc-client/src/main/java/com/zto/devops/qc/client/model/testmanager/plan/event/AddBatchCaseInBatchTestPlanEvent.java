package com.zto.devops.qc.client.model.testmanager.plan.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.model.testmanager.plan.entity.BatchTestPlanVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class AddBatchCaseInBatchTestPlanEvent extends BaseEvent {

    private String caseCode;

    private TestcaseTypeEnum caseType;

    private List<BatchTestPlanVO> testPlanList;
}
