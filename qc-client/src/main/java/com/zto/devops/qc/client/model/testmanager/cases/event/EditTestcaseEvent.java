package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.RevisionEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseRelationVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EditTestcaseEvent extends RevisionEvent {

    private String code;

    private String productCode;

    private String parentCode;

    private String name;

    private TestcaseAttributeEnum attribute;

    private TestcaseTypeEnum type;

    private TestcasePriorityEnum priority;

    private TestcaseStatusEnum status;

    private String precondition;

    private Long dutyUserId;

    private String dutyUser;

    private String comment;

    private Integer sort;

    private Integer layer;

    private String path;

    private List<AttachmentVO> attachments;

    private List<TagVO> tags;

    private List<TestcaseRelationVO> vos;

    private List<TestcaseStepVO> testSteps;

    private Boolean enable;

    private Boolean ifUpdateStatus;

    @Override
    public String action() {
        return "编辑用例";
    }
}
