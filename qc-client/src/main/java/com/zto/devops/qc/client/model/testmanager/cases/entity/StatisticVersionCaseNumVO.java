package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "版本详情-统计个数")
public class StatisticVersionCaseNumVO implements Serializable {
    @ZsmpModelProperty(description = "手工用例")
    private StatisticThingVO manualCaseNum;

    @ZsmpModelProperty(description = "自动化用例")
    private StatisticThingVO autoCaseNum;

    @ZsmpModelProperty(description = "分支覆盖率")
    private CoverageRateVO branch;

    @ZsmpModelProperty(description = "主干覆盖率")
    private CoverageRateVO master;

    @ZsmpModelProperty(description = "用例分布")
    private  StatisticCaseVO  statisticCaseVO;

    @ZsmpModelProperty(description = "用例分布全状态")
    private  StatisticAllCaseVO  statisticAllCaseVO;

}
