package com.zto.devops.qc.client.model.report.command;

import com.zto.devops.qc.client.model.report.entity.*;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：AddIntegrateTestReportReq
 * 类 描 述：TODO
 * 创建时间：2021/11/10 5:56 下午
 * 创 建 人：bulecat
 */
@Getter
@Setter
public class AddIntegrateTestReportDraftCommand extends BaseReportInfoComnand {

    @GatewayModelProperty(description = "模块测试结果", required = false)
    private List<ModuleTestVO> moduleTestVOS;

    @GatewayModelProperty(description = "用例执行结果", required = false)
    private List<CaseResultVO> caseResultVOS;

    @GatewayModelProperty(description = "用例执行明细", required = false)
    private List<CaseDetailVO> caseDetailVOS;

    @GatewayModelProperty(description = "缺陷信息统计", required = false)
    private List<IssueInfoVO> issueInfoVOS;

    @GatewayModelProperty(description = "遗留缺陷", required = false)
    private List<IssueLegacyVO> issueLegacyVOS;

    public AddIntegrateTestReportDraftCommand(String aggregateId) {
        super(aggregateId);
    }

}
