package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.cases.AddHeartTestCaseEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class EditHeartStateEvent extends BaseEvent implements ActionEvent {

    @ZsmpModelProperty(description = "产品code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "用例code")
    private String caseCode;

    @ZsmpModelProperty(description = "操作 开启OPEN，关闭CLOSE")
    private AddHeartTestCaseEnum operation;

    @ZsmpModelProperty(description = "AppID")
    private String appId;

    @ZsmpModelProperty(description = "App名称")
    private String appName;

    @ZsmpModelProperty(description = "App的code")
    private String appCode;

    @Override
    public String action() {
        return AddHeartTestCaseEnum.OPEN.equals(this.operation) ? "设置了心跳用例" : "取消了心跳用例";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }

    @Override
    public String logBusinessCode() {
        return this.caseCode;
    }
}
