package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordAnalyticMethodEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import lombok.Data;

import java.util.Date;

@Data
public class AutomaticRecordLogVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 编号
     */
    private String code;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 自动化解析来源code
     */
    private String automaticSourceCode;

    /**
     * 创建人编码
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date gmtModified;
    /**
     * jmeter:源地址-OSS上的脚本文件地址;xunit-git地址
     */
    private String address;

    /**
     * 类型：1-jmeter, 2-testng, 3-postman, 4-junit,5-pyunit
     */
    private AutomaticRecordTypeEnum type;

    private String fileName;

    private String bucketName;

    private String failInformation;
    private String status;
    private String statusDesc;

    private Boolean enable;
    private String commitId;
    private String errorLogFile;
    private Integer autoSourceVersion;

    public void setStatus(String status) {
        this.status = status;
        if (null != status) {
            this.statusDesc = AutomaticStatusEnum.getDescByName(status);
            if (AutomaticStatusEnum.IN_PROGRESS.name().equals(this.status)) {
                this.statusDesc = "代码解析中";
            }
            if (AutomaticStatusEnum.FAIL.name().equals(this.status)) {
                this.statusDesc = "代码解析失败";
            }
        }
    }

    /**
     * 解析方式
     */
    private AutomaticRecordAnalyticMethodEnum analyticMethod;

    /**
     * 解析方式（中文描述）
     */
    private String analyticMethodDesc;
    public String getAnalyticMethodDesc() {
        if (null != this.analyticMethod) {
            this.analyticMethodDesc = this.analyticMethod.getDesc();
        }
        return this.analyticMethodDesc;
    }

}
