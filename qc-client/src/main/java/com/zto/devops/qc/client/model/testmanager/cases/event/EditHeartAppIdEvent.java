package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

@Data
public class EditHeartAppIdEvent extends BaseEvent {

    @ZsmpModelProperty(description = "用例code", required = true)
    private String caseCode;

    @ZsmpModelProperty(description = "产品code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "AppID的code", required = true)
    private String appId;

    @ZsmpModelProperty(description = "AppID的名称", required = true)
    private String appIdName;

    @ZsmpModelProperty(description = "AppID的code", required = true)
    private String appIdCode;

}
