package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TestcaseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private String code;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 上级code
     */
    private String parentCode;

    /**
     * 全部上级名称
     */
    private List<String> parentFullName;

    private String fullName;

    /**
     * 名称
     */
    private String name;

    /**
     * 属性
     */
    private TestcaseAttributeEnum attribute;

    /**
     * 用例类型
     */
    private TestcaseTypeEnum type;

    /**
     * 等级
     */
    private TestcasePriorityEnum priority;

    /**
     * 状态
     */
    private TestcaseStatusEnum status;

    /**
     * 前置条件
     */
    private String precondition;

    /**
     * 责任人编码
     */
    private Long dutyUserId;

    /**
     * 责任人
     */
    private String dutyUser;

    /**
     * 备注
     */
    private String comment;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 层级
     */
    private Integer layer;

    /**
     * 所有父节点code
     */
    private String path;

    /**
     * 自动化用例节点类型
     */
    private AutomaticNodeTypeEnum nodeType;

    /**
     * 创建人编码
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 测试步骤
     */
    private List<TestcaseStepVO> testSteps;

    /**
     * 标签
     */
    private List<TagVO> tags;

    /**
     * 可编辑字段
     */
    private List<TestcaseEditFieldEnum> fields;

    /**
     * 可操作按钮
     */
    private List<TestcaseButtonEnum> buttons;

    /**
     * 自动化解析来源code
     */
    private String automaticSourceCode;

    private String interfaceName;

    private List<TestcaseVO> sonList;

    private List<TmTestPlanVO> testPlanList;

    private String priorityDesc;

    private String statusDesc;

    private String typeDesc;

    private String nodeTypePath;

    private Boolean hasChildren = false;

    private String planName;

    private int count;

    private String testcaseModulePath;

    private AutomaticRecordTypeEnum automaticSourceType;

    /**
     * 是否心跳
     */
    private Boolean setHeart;

    private Boolean setCore;

    private String versionCode;

    private String versionName;

    public void setPriority(TestcasePriorityEnum priority) {
        this.priority = priority;
        if (null != priority) {
            this.priorityDesc = priority.getValue();
        }
    }

    public void setStatus(TestcaseStatusEnum status) {
        this.status = status;
        if (null != status) {
            this.statusDesc = status.getDesc();
        }
    }

    public void setType(TestcaseTypeEnum type) {
        this.type = type;
        if (null != type) {
            this.typeDesc = type.getValue();
        }
    }
}
