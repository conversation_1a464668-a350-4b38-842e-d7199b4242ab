package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.event.RevisionBusinessName;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class EditTestcaseTitleCommand extends BaseCommand {
    public EditTestcaseTitleCommand(String aggregateId) {
        super(aggregateId);
    }
    private String code;
    @RevisionBusinessName(name = "标题")
    private String name;
    @RevisionBusinessName(name = "优先级", method = "getValue")
    private TestcasePriorityEnum priority;
    @RevisionBusinessName(name = "版本编号")
    private String versionCode;
}
