package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Setter
@Getter
public class BatchMoveTestcaseModelEvent extends BaseEvent implements ActionEvent {

    private List<TestcaseVO> testcaseList;

    private String parentCode;

    private TestcaseTypeEnum caseType;

    private String parentName;

    @Override
    public String action() {
        return "批量移动用例";
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }

    @Override
    public Map<String, ?> metadata() {
        return ActionEvent.super.metadata();
    }

    @Override
    public String logBusinessCode() {
        return ActionEvent.super.logBusinessCode();
    }
}
