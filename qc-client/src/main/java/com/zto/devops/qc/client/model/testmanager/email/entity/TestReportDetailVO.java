package com.zto.devops.qc.client.model.testmanager.email.entity;

import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TestReportDetailVO implements Serializable {

    @ZsmpModelProperty(description = "报告code")
    private String reportCode;

    @ZsmpModelProperty(description = "报告名称")
    private String reportName;

    @ZsmpModelProperty(description = "测试计划类型")
    private ReportType reportType;

    @ZsmpModelProperty(description = "关联计划code")
    private String relatePlanCode;

    @ZsmpModelProperty(description = "关联计划名称")
    private String relatePlanName;

    @ZsmpModelProperty(description = "产品ID")
    private String productCode;

    @ZsmpModelProperty(description = "产品名称")
    private String productName;

    @ZsmpModelProperty(description = "版本code")
    private String versionCode;

    @ZsmpModelProperty(description = "版本名称")
    private String versionName;

    @ZsmpModelProperty(description = "预览的html")
    private String preview;

    @ZsmpModelProperty(description = "发送时间")
    private Date sendDate;

    @ZsmpModelProperty(description = "发送人ID")
    private Long senderId;

    @ZsmpModelProperty(description = "发送人")
    private String sender;

    @ZsmpModelProperty(description = "创建人id")
    private Long creatorId;

    @ZsmpModelProperty(description = "创建人")
    private String creator;

    @ZsmpModelProperty(description = "创建时间")
    private Date gmtCreate;

    @ZsmpModelProperty(description = "更新人id")
    private Long modifierId;

    @ZsmpModelProperty(description = "更新人")
    private String modifier;

    @ZsmpModelProperty(description = "更新时间")
    private Date gmtModified;

}
