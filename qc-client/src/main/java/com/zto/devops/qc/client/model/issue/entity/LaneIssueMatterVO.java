package com.zto.devops.qc.client.model.issue.entity;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.LaneStatusEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 泳道缺陷事项
 * @Date 2023/2/7
 * @Version 1.0
 */
@Data
public class LaneIssueMatterVO extends BaseLaneMatterVO<LaneIssueMapCondition> {

    private IssueStatus status;

    // 缺陷 reopen 次数
    private int reopen;

    // 缺陷等级
    private IssuePriority priority;

    // 等级描述
    private String priorityDesc;

    @Override
    public void build() {
        super.build();
        if (priority != null) {
            priorityDesc = priority.getValue();
        }
    }

    @Override
    protected LaneStatusEnum doGetLane(LaneIssueMapCondition condition) {
        if (status == null) {
            return null;
        }
        return new LaneIssueMap(condition).getLane(this.status.name());
    }

    @Override
    public boolean done() {
        return IssueStatus.CLOSED.equals(status);
    }

    @Override
    public String domain() {
        return DomainEnum.ISSUE.name();
    }

    @Override
    public String statusDesc() {
        return status.getValue();
    }
}
