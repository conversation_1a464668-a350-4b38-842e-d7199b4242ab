package com.zto.devops.qc.client.model.testmanager.plan.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import java.util.Date;
import lombok.Data;

@Data
public class RelatedPlanReportBaseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "邮件code", required = false)
    private String emailCode;

    @GatewayModelProperty(description = "邮件name", required = false)
    private String name;

    @GatewayModelProperty(description = "版本code", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "产品code", required = false)
    private String productCode;

    @GatewayModelProperty(description = "发送人Id", required = false)
    private Long senderId;

    @GatewayModelProperty(description = "发送人", required = false)
    private String sender;

    @GatewayModelProperty(description = "发送时间", required = false)
    private Date sendDate;

}
