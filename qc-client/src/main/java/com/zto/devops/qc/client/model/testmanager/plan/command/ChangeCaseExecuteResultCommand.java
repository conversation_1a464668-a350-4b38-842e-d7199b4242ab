package com.zto.devops.qc.client.model.testmanager.plan.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class ChangeCaseExecuteResultCommand extends BaseCommand {

    public ChangeCaseExecuteResultCommand(String aggregateId) {
        super(aggregateId);
    }

    private TestPlanCaseStatusEnum executeStatus;

    private List<String> caseIds;

    private TestPlanStageEnum testStage;

    private String planCode;
}
