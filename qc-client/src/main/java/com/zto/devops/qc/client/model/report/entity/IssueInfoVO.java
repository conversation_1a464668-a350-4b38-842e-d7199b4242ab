package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;


import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

@GatewayModel(description = "缺陷统计信息")
@Data
public class IssueInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ZModelProperty(description = "code", required = true, sample = "ISS240410002021")
    private String code;

    @ZModelProperty(description = "报告code", required = true, sample = "TR240416001001")
    private String reportCode;

    @ZModelProperty(description = "缺陷总数", required = true, sample = "10")
    private Integer issueCount;

    @ZModelProperty(description = "有效缺陷总数", required = true, sample = "1")
    private Integer validIssueCount;

    @ZModelProperty(description = "遗留缺陷数", required = true, sample = "1")
    private Integer legacyIssueCount;

    @ZModelProperty(description = "p012遗留数", required = true, sample = "1")
    private Integer legacyIssueHigh;
}