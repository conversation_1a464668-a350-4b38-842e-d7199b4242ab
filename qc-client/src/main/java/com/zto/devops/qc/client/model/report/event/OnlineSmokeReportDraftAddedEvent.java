package com.zto.devops.qc.client.model.report.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;


@Setter
@Getter
public class OnlineSmokeReportDraftAddedEvent extends OnlineSmokeReportAddedEvent implements ActionEvent {

    @Override
    public String action() {
        return null;
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }
}
