package com.zto.devops.qc.client.model.testPlan.query;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testPlan.TestPlanEditEnum;
import com.zto.devops.qc.client.model.testPlan.entity.TestFunctionPointVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TestPlanDto extends TestPlanBaseDto{
    private String testPlanCode;
    private String versionCode;
    private String versionName;
    private Integer developerNo;
    private Integer testerNo;
    private Long productDirectorId;
    private String productDirectorName;
    private Boolean mobileSpecialTest;
    private Boolean integrateTest;
    private Boolean securityScan;
    private Boolean staticAnalysis;
    private Boolean performanceTest;
    private Date performanceTestTime;
    private Long performanceTestDirectorId;
    private String performanceTestDirectorName;
    private Boolean exploratoryTest;
    private Long exploratoryTestDirectorId;
    private String exploratoryTestDirectorName;
    private Date exploratoryTestTime;
    private Long staticAnalysisDirectorId;
    private String staticAnalysisDirectorName;
    private Date staticAnalysisTime;
    private List<TestFunctionPointVO> pointList ;
    private Long deptId;
    private String deptName;
    @GatewayModelProperty(description = "版本开始时间", required = false)
    private Date startDate;
    @GatewayModelProperty(description = "版本上线时间", required = false)
    private Date publishDate;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date presentationDate;
    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date approvalExitDate;
    List<TestPlanEditEnum> fieldVOS;

    @GatewayModelProperty(description = "精准返回结果", required = false)
    private String accurateResult;
    @GatewayModelProperty(description = "安全返回结果", required = false)
    private String safetyResult;

    @GatewayModelProperty(description = "安全测试人员id", required = false)
    private String safetyUserId;
    @GatewayModelProperty(description = "安全测试人员名", required = false)
    private String safetyUserName;
}
