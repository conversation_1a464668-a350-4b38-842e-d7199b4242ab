package com.zto.devops.qc.client.model.report.query;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.report.ExecResultBusinessDomain;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class ApprovalExitExecResultQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "版本编号")
    private String versionCode;

    /**
     * 业务域,比如自动化测试等
     */
    private ExecResultBusinessDomain businessDomain;

}
