package com.zto.devops.qc.client.model.report.entity;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目名称：qc-parent
 * 类 名 称：SecurityTestResultResp
 * 类 描 述：TODO
 * 创建时间：2021/11/29 10:40 上午
 * 创 建 人：renxinhui
 * <AUTHOR>
 */
@Data
public class SecurityIssue implements Serializable {

    @GatewayModelProperty(description = "漏洞唯一id", required = false)
    private String uuid;

    @GatewayModelProperty(description = "漏洞名称", required = false)
    private String name;

    @GatewayModelProperty(description = "漏洞等级", required = false)
    private String level;

    @GatewayModelProperty(description = "漏洞危害", required = false)
    private String remarks;

    @GatewayModelProperty(description = "漏洞备注", required = false)
    private String harm;

    @GatewayModelProperty(description = "漏洞状态", required = false)
    private String status;
}
