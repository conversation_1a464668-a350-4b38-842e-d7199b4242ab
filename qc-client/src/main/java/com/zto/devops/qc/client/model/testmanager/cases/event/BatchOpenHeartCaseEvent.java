package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class BatchOpenHeartCaseEvent extends BaseEvent implements ActionEvent {

    private List<String> code;

    @Override
    public String action() {
        return "批量设置心跳用例";
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }

    @Override
    public Map<String, ?> metadata() {
        return ActionEvent.super.metadata();
    }

    @Override
    public String logBusinessCode() {
        return ActionEvent.super.logBusinessCode();
    }
}
