package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class TestcaseModuleVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "code", required = false)
    private String code;

    @GatewayModelProperty(description = "所属产品code", required = false)
    private String productCode;

    /**
     * 上级模块code
     */
    @GatewayModelProperty(description = "上级模块code", required = false)
    private String parentCode;

    /**
     * 模块名称
     */
    @GatewayModelProperty(description = "模块名称", required = false)
    private String name;

    /**
     * 排序
     */
    @GatewayModelProperty(description = "排序", required = false)
    private Integer sort;

    /**
     * 模块编号（旧）
     */
    @GatewayModelProperty(description = "模块编号", required = false)
    private Long moduleId;

    @GatewayModelProperty(description = "用例个数", required = false)
    private Long number;

    @GatewayModelProperty(description = "子节点", required = false)
    private List<TestcaseModuleVO> list;
}
