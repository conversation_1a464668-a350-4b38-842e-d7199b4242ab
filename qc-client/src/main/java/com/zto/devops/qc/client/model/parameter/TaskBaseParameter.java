package com.zto.devops.qc.client.model.parameter;

import java.io.Serializable;

import java.util.List;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

/**
 * 项目名称：project-parent
 * 类 名 称：TaskBaseQuery
 * 类 描 述：TODO
 * 创建时间：2021/12/16 3:35 下午
 * 创 建 人：bulecat
 */
@Data
public class TaskBaseParameter implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<String> relevantUserTypes;

    private String domain;

    private String orderField;

    private String orderType;

    private Long userId;

    private Long modifierId;

    private List<String> status;

    private String name;

    private String businessCode;

    private List<String> projectLevel;

    private List<String> projectScale;

    private List<String> worthLevel;

    private List<String> versionCodeList;

    private String productCode;

}
