package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Setter
@Getter
public class TestcaseDomainAssociatedEvent extends BaseEvent implements ActionEvent {

    private String code;

    private DomainEnum domain;

    private List<String> codeList;

    @Override
    public String action() {
        return String.format("关联了%s", this.domain.getValue());
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }
}
