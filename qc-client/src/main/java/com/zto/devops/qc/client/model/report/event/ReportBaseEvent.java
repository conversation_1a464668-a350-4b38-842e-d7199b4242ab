package com.zto.devops.qc.client.model.report.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.report.ReportType;
import com.zto.devops.qc.client.enums.report.TestResultEunm;
import lombok.Data;

import java.util.Date;

@Data
public class ReportBaseEvent extends BaseEvent {

    private String code;

    private String name;

    private String planCode;

    private String versionCode;

    private String versionName;

    private ReportType reportType;

    private TestResultEunm testResult;

    private Date presentationDate;

    private Date actualPresentationDate;

    private Date approvalExitDate;

    private Date actualApprovalExitDate;

    private Date actualPublishDate;

    private Date actualTestStart;

    private Date actualTestEnd;

    private Date checkStartDate;

    private Date checkEndDate;

    private String summary;
}
