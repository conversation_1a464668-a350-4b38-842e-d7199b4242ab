package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.query.BaseQuery;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

;

@Getter
@Setter
@AllArgsConstructor
public class FindTagTestcaseNoQuery extends BaseQuery {

    private String tagName;

    private DomainEnum domain;

    private String productCode;
}
