package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@GatewayModel(description = "遗留缺陷")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IssueNumStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "缺陷总数", required = false)
    private Integer issueCount;
    @GatewayModelProperty(description = "有效缺陷总数", required = false)
    private Integer validIssueCount;
    @GatewayModelProperty(description = "遗留效缺陷总数", required = false)
    private Integer legacyIssueCount;
    @GatewayModelProperty(description = "遗留效缺陷总数--P012", required = false)
    private Integer legacyIssueHigh;

    /**
     * 参数初始化
     *
     * @return {@link IssueNumStatisticsVO}
     */
    public static IssueNumStatisticsVO init() {
        return IssueNumStatisticsVO.builder()
                .issueCount(0)
                .validIssueCount(0)
                .legacyIssueCount(0)
                .legacyIssueHigh(0)
                .build();
    }

}