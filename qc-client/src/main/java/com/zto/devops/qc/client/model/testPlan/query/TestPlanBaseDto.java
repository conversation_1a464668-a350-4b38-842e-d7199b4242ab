package com.zto.devops.qc.client.model.testPlan.query;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testPlan.PlanButtonEnum;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@Slf4j
public class TestPlanBaseDto  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 编号
     */
    private String code;

    /**
     * 计划名
     */
    private String planName;

    /**
     * 所属产品id
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 类型
     */
    private String type;

    /**
     * 计划编号
     */
    private String planCode;

    /**
     * 测试负责人id
     */
    private Long testDirectorId;

    /**
     * 测试负责人名
     */
    private String testDirectorName;


    /**
     * 创建人编码
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 是否删除 1 未删除, 0 已删除
     */
    private Boolean enable;

    /**
     * 修改次数
     */
    private Integer editNo;

    /**
     * 状态
     */
    private String status;

    /**
     * 预览html
     */
    private String preview;

    private String remarks;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<SendUserInfoVO> receiveUsers;

    @GatewayModelProperty(description = "抄送人", required = false)
    private List<SendUserInfoVO> ccUsers;

    @GatewayModelProperty(description = "邮件发送时间", required = false)
    private Date sendTime;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date presentationDate;
    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date approvalExitDate;

    @GatewayModelProperty(description = "版本开始时间", required = false)
    private Date startDate;
    @GatewayModelProperty(description = "版本上线时间", required = false)
    private Date publishDate;

    @GatewayModelProperty(description = "计划提测时间-上下午", required = false)
    private String presentationDay;
    @GatewayModelProperty(description = "计划准出时间-上下午", required = false)
    private String approvalExitDay;

    @GatewayModelProperty(description = "版本开始时间-上下午", required = false)
    private String startDay;
    @GatewayModelProperty(description = "版本上线时间-上下午", required = false)
    private String publishDay;
    @GatewayModelProperty(description = "可操作按钮 (更新计划)", required = false)
    private PlanButtonEnum buttonVOS;
    public void setDateDay() {
        this.setPresentationDay(Objects.isNull(this.getPresentationDate())? null : (ifAfternoonByDate(this.getPresentationDate())?"09:00:00":"18:00:00"));
        this.setApprovalExitDay(Objects.isNull(this.getApprovalExitDate())? null : (ifAfternoonByDate(this.getApprovalExitDate())?"09:00:00":"18:00:00"));
        this.setStartDay(Objects.isNull(this.getStartDate())? null : (ifAfternoonByDate(this.getStartDate())?"09:00:00":"18:00:00"));
        this.setPublishDay(Objects.isNull(this.getPublishDate())? null : (ifAfternoonByDate(this.getPublishDate())?"09:00:00":"18:00:00"));
    }

    public  boolean ifAfternoonByDate(Date date){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH");
        String format = simpleDateFormat.format(date);
        log.info("format:"+format);
        if(Integer.parseInt(format)<12){
            return true;
        }else {
            return false;
        }
    }
}
