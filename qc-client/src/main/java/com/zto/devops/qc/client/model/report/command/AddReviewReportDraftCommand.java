package com.zto.devops.qc.client.model.report.command;

import com.zto.devops.qc.client.model.report.entity.ReviewInfoVO;
import com.zto.devops.qc.client.model.report.entity.ReviewOpinionVO;
import com.zto.devops.qc.client.model.report.entity.ReviewRenewalVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class AddReviewReportDraftCommand extends BaseReportInfoComnand {

    @GatewayModelProperty(description = "评审信息", required = false)
    private ReviewInfoVO reviewInfo;

    @GatewayModelProperty(description = "评审观点", required = false)
    private List<ReviewOpinionVO> reviewOpinions;

    @GatewayModelProperty(description = "评审更新信息", required = false)
    private List<ReviewRenewalVO> reviewRenewals;


    public AddReviewReportDraftCommand(String aggregateId) {
        super(aggregateId);
    }
}
