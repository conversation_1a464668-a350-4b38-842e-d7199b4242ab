package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.event.RevisionBusinessName;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseRelationVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EditTestcaseCommand extends BaseCommand {

    public EditTestcaseCommand(String aggregateId) {
        super(aggregateId);
    }

    private Boolean enable;

    private Boolean ifUpdateStatus;

    private String productCode;

    private String parentCode;

    @RevisionBusinessName(name = "名称")
    private String name;

    private TestcaseAttributeEnum attribute;

    private TestcaseTypeEnum type;

    @RevisionBusinessName(name = "优先级", method = "getValue")
    private TestcasePriorityEnum priority;

    private TestcaseStatusEnum status;

    @RevisionBusinessName(name = "前置条件")
    private String precondition;

    private Long dutyUserId;

    @RevisionBusinessName(name = "责任人")
    private String dutyUser;

    @RevisionBusinessName(name = "备注")
    private String comment;

    private Integer sort;

    private Integer layer;

    private String path;

    private List<AttachmentVO> attachments;

    private List<TagVO> tags;

    private List<TestcaseRelationVO> vos;

    private List<TestcaseStepVO> testSteps;

    private String automaticSourceCode;

    private AutomaticNodeTypeEnum nodeType;

    private String nodeTypePath;

    private List<TmTestPlanVO> testPlanList;

    private String flag;

    private String testcaseModulePath;

    private String planName;

    private String interfaceName;

    private String versionCode;
}
