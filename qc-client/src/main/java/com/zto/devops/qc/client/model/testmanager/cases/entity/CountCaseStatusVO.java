package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ZsmpModel(description = "用例执行结果VO")
public class CountCaseStatusVO implements Serializable {
    private static final long serialVersionUID = 148845762578487293L;

    @ZsmpModelProperty(description = "用例执行结果")
    private TestPlanCaseStatusEnum status;

    @ZsmpModelProperty(description = "用例执行结果描述")
    private String statusDesc;

    @ZsmpModelProperty(description = "数量")
    private Integer num;

    public String getStatusDesc() {
        return null != this.status ? this.status.getValue() : "";
    }

    /**
     * 组装参数
     *
     * @param status {@link TestPlanCaseStatusEnum}
     * @param voList {@link AutomaticTaskVO}
     * @return {@link CountCaseStatusVO}
     */
    public static CountCaseStatusVO buildSelf(TestPlanCaseStatusEnum status, List<AutomaticTaskVO> voList) {
        return CountCaseStatusVO.builder()
                .status(status)
                .num(CollectionUtils.isEmpty(voList) ? 0 : voList.size())
                .build();
    }
}
