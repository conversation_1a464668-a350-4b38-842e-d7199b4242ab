package com.zto.devops.qc.client.model.issue.entity;

import com.zto.devops.framework.client.entity.BaseEntityDO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class IssueMqResp extends BaseEntityDO implements Serializable {

    /**
     * 缺陷编号
     */
    private String code;

    /**
     * 标题
     */
    private String title;

    /**
     * 状态
     */
    private String status;

    /**
     * 优先级 1 紧急, 2 严重, 3 重要, 4 次要, 5 微小
     */
    private String priority;

    /**
     * 缺陷根源
     */
    private String rootCause;

    /**
     * 缺陷类型
     */
    private String type;

    /**
     * 测试方法
     */
    private String testMethod;

    /**
     * 发现阶段
     */
    private String findStage;

    /**
     * 发现环境 开发环境, 测试环境, 集成环境, 预发布环境, 生产环境
     */
    private String findEnv;

    /**
     * 复现概率
     */
    private String repetitionRate;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 重新打开时间
     */
    private Date reopenTime;
    /**
     * 实际工时
     */
    private Double actualWorkingHours;

    /**
     * 开始修复时间
     */
    private Date startFixTime;

    /**
     * 延期修复时间
     */
    private Date delayFixTime;

    /**
     * 交付验证时间
     */
    private Date deliverTime;

    /**
     * 拒绝时间
     */
    private Date rejectTime;

    /**
     * 关闭时间
     */
    private Date closeTime;

    /**
     * 发现时间
     */
    private Date findTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 产品ID
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 需求ID
     */
    private String requirementCode;

    /**
     * 需求名称
     */
    private String requirementName;

    private String requirementLevel;

    /**
     * 发现版本编码
     */
    private String findVersionCode;

    /**
     * 发现版本名称
     */
    private String findVersionName;

    /**
     * 修复版本编码
     */
    private String fixVersionCode;

    /**
     * 修复版本名称
     */
    private String fixVersionName;

    /**
     * 迭代code
     */
    private String sprintCode;
    /**
     * 迭代名称
     */
    private String sprintName;
    /**
     * 报告人ID
     */
    private Long findUserId;

    /**
     * 报告人名称
     */
    private String findUserName;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新人名称
     */
    private String updateUserName;

    /**
     * 经办人名称
     */
    private String handleUserName;

    /**
     * 经办人ID
     */
    private Long handleUserId;

    /**
     * 开发人员ID
     */
    private Long developUserId;

    /**
     * 开发人员名称
     */
    private String developUserName;

    /**
     * 测试人员ID
     */
    private Long testUserId;

    /**
     * 测试人员名称
     */
    private String testUserName;

    /**
     * 缺陷描述
     */
    private String description;

    private String  versionConfirm;

    private String  oldCode;

    /**
     * 审查状态  1已审查 0未审查
     */
    private Boolean examination;
    /**
     * 测试遗漏  1为是 0为否
     */
    private Boolean testOmission;
    /**
     * 代码缺陷  1为是 0为否
     */
    private Boolean codeDefect;

    private String testOmissionVersion;

    private String codeDefectVersion;
}
