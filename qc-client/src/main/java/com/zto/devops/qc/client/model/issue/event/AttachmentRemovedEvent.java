package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.OperationTypeEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class AttachmentRemovedEvent extends BaseEvent implements ActionEvent {

    private String businessCode;

    private List<AttachmentVO> attachments;

    @Override
    public String action() {
        return OperationTypeEnum.REMOVEATTACHMENT.getValue();
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        if (attachments != null && attachments.size() > 0) {
            String names = attachments.stream().map(AttachmentVO::getName)
                    .filter(StringUtils::isNotEmpty).collect(Collectors.joining(","));
            if (StringUtils.isNotEmpty(names)) {
                return "" + names;
            }
        }
        return "";
    }
}