package com.zto.devops.qc.client.model.testmanager.email.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.email.EmailSourceEnum;
import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class TmEmailEvent extends BaseEvent implements ActionEvent {

    /**
     * 邮件code（本表唯一标识）
     */
    private String code;

    /**
     * 邮件名称
     */
    private String name;

    /**
     * 邮件类型(准入、准出、简易...)
     */
    private EmailTypeEnum emailType;

    /**
     * 数据来源(报告、计划)
     */
    private EmailSourceEnum source;

    /**
     * 报告/计划code
     */
    private String businessCode;

    /**
     * 报告/计划名称
     */
    private String businessName;


    /**
     * 关联计划code
     */
    private String relatePlanCode;

    /**
     * 关联计划名称
     */
    private String relatePlanName;

    /**
     * 产品ID
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;


    /**
     * 版本code
     */
    private String versionCode;

    /**
     * 版本名称
     */
    private String versionName;


    /**
     * 计划提测时间
     */
    private Date planPresentationDate;

    /**
     * 计划准出时间
     */
    private Date planApprovalExitDate;

    /**
     * 抄送人json
     */
    private String ccUsers;

    /**
     * 收件人json
     */
    private String receiveUsers;

    /**
     * 预览的html
     */
    private String preview;

    /**
     * 发送时间
     */
    private Date sendDate;

    /**
     * 发送人ID
     */
    private Long senderId;

    /**
     * 发送人
     */
    private String sender;

    /**
     * 状态：暂存,已发送
     */
    private String status;

    @Override
    public String action() {
        return null;
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }
}
