package com.zto.devops.qc.client.model.rpc.pipeline.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Objects;

/**
 * @Description: integration已发送测试报告事件
 * @Author: cher
 * @Date: 2021/9/6 10:49
 **/
@Getter
@Setter
public class IntegrationTestedReportEvent extends FlowChangeStatusEvent implements ActionEvent {

    @Override
    public String action() {
        if (Objects.nonNull(getFlowEvent())) {
            return getFlowEvent().getValue();
        }
        return null;
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return getOccurred();
    }

    @Override
    public String makeString() {
        return "";
    }
}
