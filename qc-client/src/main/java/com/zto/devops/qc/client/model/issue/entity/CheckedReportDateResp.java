package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;

import lombok.Data;

import java.util.List;

//验收报告
@Data
public class CheckedReportDateResp implements Serializable {

    private static final long serialVersionUID = 1L;

    //版本编号
    private String versionCode;
    //计划名
    private String name;
    //实际提测时间
    private String actualTestStartDate;
    //实际准出时间
    private String actualTestEndDate;
    //实际上线时间
    private String actualVersionEndDate;
    //产品负责人
    private String prdLeader;
    //测试结果
    private String testResult;
    //是否延期
    private Integer delayFlag;
    //来源
    private String sourceType;
    //验收报告创建人
    private String createUser;
    //验收报告创建时间
    private String createTime;
    //类型
    private Integer type;

    //抄送人
    private List<String> ccUsersEmail;

    private List<String> recipientsEmail;

}
