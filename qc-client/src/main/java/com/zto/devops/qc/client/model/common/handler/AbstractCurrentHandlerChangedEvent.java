package com.zto.devops.qc.client.model.common.handler;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.issue.Reason;
import com.zto.devops.qc.client.model.issue.entity.CurrentHandlerVO;
import lombok.Data;

import java.util.Set;

@Data
public abstract class AbstractCurrentHandlerChangedEvent extends BaseEvent {

    /**
     * 当前处理人，需删除
     */
    protected Set<CurrentHandlerVO> deleteHandler;

    /**
     * 最新的处理人，需新增
     */
    protected Set<CurrentHandlerVO> lastHandler;

    protected boolean emptyString(String value) {
        return value == null || "".equals(value);
    }

    protected String breakLine(String value) {
        return value + "\n";
    }

    /**
     * 追加 原因 + 备注 注释
     * @param reason
     * @param remark
     * @return
     */
    protected String appendReasonAndMark(Reason reason, String reasonTitle, String remark) {
        String ctx = "";
        if (reason != null) {
            ctx = ctx + reasonTitle + ":" + reason.getValue();
        }
        return appendRemark(ctx, remark);
    }

    /**
     * 备注信息
     * @param context
     * @return
     */
    protected String appendRemark(String context, String remark) {
        if (remark == null || "".equals(remark)) {
            return context;
        }
        if (context != null && !context.endsWith("\n")) {
            context = breakLine(context);
        }
        if (!emptyString(remark)) {
            String rm = "备注：" + remark;
            if (context == null) {
                return rm ;
            }
            return context + rm;
        }
        return "";
    }

    protected String appendRemark(String remark) {
        return appendRemark(null, remark);
    }

}
