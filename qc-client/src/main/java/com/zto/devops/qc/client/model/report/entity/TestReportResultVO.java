package com.zto.devops.qc.client.model.report.entity;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-03-30 14:00
 */
@Data
public class TestReportResultVO implements Serializable {

    @ZModelProperty(description = "NO_PASS/NO_SEND(未通过/未发送)", required = true)
    private String result;

    @ZModelProperty(description = "原因", required = true)
    private String reason;

    @ZModelProperty(description = "版本code")
    private String versionCode;

    @ZModelProperty(description = "版本名称")
    private String versionName;

}
