package com.zto.devops.qc.client.model.testPlan.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.ObservedEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import lombok.Data;

import java.util.List;

@Data
public class AddTestPlanEvent extends BaseEvent implements ObservedEvent {

    private String code;
    private String planName;
    private String productCode;
    private String productName;
    // 产品类型
    private String productSource;
    private Long productDirectorId;
    private String productDirectorName;
    private String type;
    private String testPlanCode;
    private Long testDirectorId;
    private String testDirectorName;

    private Integer editNo;
    private TestPlanStatusEnum status;
    private String preview;
    private Long deptId;

    private String deptName;

    private Version version;
    private User productOwner;


    private List<SendUserInfoVO> receiveUsers;

    //抄送人
    private List<SendUserInfoVO> ccUsers;


}
