package com.zto.devops.qc.client.model.report.entity;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：OnlineSmokeReportDetailResp
 * 类 描 述：TODO
 * 创建时间：2021/11/10 8:13 下午
 * 创 建 人：bulecat
 */
@Data
public class OnlineSmokeReportDetailVO extends BaseReportInfoVO {

    @GatewayModelProperty(description = "是否按计划范围上线", required = false)
    private Integer asPlanedOnline;

    @GatewayModelProperty(description = "更新测试结果时间", required = false)
    private Date updateTestResultDate;

    @GatewayModelProperty(description = "用例执行明细", required = false)
    private List<CaseDetailVO> caseDetailVOS;


}
