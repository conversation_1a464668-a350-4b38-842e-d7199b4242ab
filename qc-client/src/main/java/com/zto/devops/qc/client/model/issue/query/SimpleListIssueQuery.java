package com.zto.devops.qc.client.model.issue.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：SimpleIssueQuery
 * 类 描 述：TODO
 * 创建时间：2021/9/14 11:00 上午
 * 创 建 人：bulecat
 * <AUTHOR>
 */
@Data
public class SimpleListIssueQuery implements Serializable {

    private List<String> code;

    public SimpleListIssueQuery(List<String> code) {
        this.code = code;
    }

    public SimpleListIssueQuery() {
    }
}
