package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ModifyOverTimeHeartResultVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 超时开始时间
     */
    private Long overTimeStart;

    /**
     * 超时结束时间
     */
    private Long overTimeEnd;

    /**
     * 触发方式
     */
    private AutomaticTaskTrigModeEnum trigMode;

    /**
     * 状态list
     */
    private List<TestPlanCaseStatusEnum> statusList;

    /**
     * 需要修改的结果状态
     */
    private TestPlanCaseStatusEnum result;

    /**
     * 当前时间
     */
    private Date nowTime;

    /**
     * 修改人id
     */
    private Long modifierId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 任务codeList
     */
    private List<String> codeList;

    /**
     * 任务状态
     */
    private AutomaticStatusEnum taskStatus;
}
