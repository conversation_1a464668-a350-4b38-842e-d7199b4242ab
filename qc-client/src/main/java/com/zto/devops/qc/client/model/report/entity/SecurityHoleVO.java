package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.report.SecurityLevel;
import com.zto.devops.qc.client.enums.report.SecurityStatus;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@GatewayModel(description = "安全漏洞")
@Data
public class SecurityHoleVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = " code", required = true)
    private String code;

    @GatewayModelProperty(description = "报告code", required = true)
    private String reportCode;

    @GatewayModelProperty(description = "漏洞名称", required = true)
    private String name;

    @GatewayModelProperty(description = "安全等级", required = true)
    private SecurityLevel level;

    @GatewayModelProperty(description = "安全等级", required = true)
    private String levelDesc;

    @GatewayModelProperty(description = "漏洞危害", required = true)
    private String harm;

    @GatewayModelProperty(description = "漏洞备注", required = true)
    private String note;

    @GatewayModelProperty(description = "漏洞状态", required = true)
    private SecurityStatus status;

    @GatewayModelProperty(description = "漏洞状态", required = true)
    private String statusDesc;

    @GatewayModelProperty(description = "漏洞详情链接", required = true)
    private String secId;

}