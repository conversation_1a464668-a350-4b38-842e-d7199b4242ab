package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ChangePlanCaseExecutorCommand extends BaseCommand {

    private String planCode;

    private TestPlanStageEnum testStage;

    private Long executorId;

    private String executor;

    private String operateCaseCode;

    public ChangePlanCaseExecutorCommand(String aggregateId) {
        super(aggregateId);
    }
}
