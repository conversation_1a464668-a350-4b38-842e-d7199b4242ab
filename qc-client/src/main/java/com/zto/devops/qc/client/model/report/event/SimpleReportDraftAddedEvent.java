package com.zto.devops.qc.client.model.report.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.OperationTypeEnum;
import lombok.Data;

import java.util.Date;

@Data
public class SimpleReportDraftAddedEvent extends SimpleReportAddedEvent implements ActionEvent {

    @Override
    public String action() {
        return OperationTypeEnum.ADDISSUE.getValue();
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }

}
