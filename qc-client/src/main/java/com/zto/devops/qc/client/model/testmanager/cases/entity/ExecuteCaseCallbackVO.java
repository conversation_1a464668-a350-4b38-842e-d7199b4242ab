package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import lombok.Data;

import java.util.Date;

@Data
public class ExecuteCaseCallbackVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private String code;

    private TestPlanCaseStatusEnum status;

    private Date startTime;

    private Date finishTime;
}
