package com.zto.devops.qc.client.model.issue.event;

import com.site.lookup.util.StringUtils;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.MyOperatedEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.OperationTypeEnum;
import com.zto.devops.qc.client.enums.issue.Reason;
import com.zto.devops.qc.client.model.common.TransitionNodeAddEvent;
import lombok.Data;

import java.util.Date;

@Data
public class IssueDelayFixedEvent extends BaseEvent implements ActionEvent, MyOperatedEvent, TransitionNodeAddEvent {

    private String businessCode;
    private String code;
    private IssueStatus status = IssueStatus.DELAY_FIX;
    private IssueStatus curStatus;
    private IssueStatus nextStatus;
    private String content;
    private Date delayFixTime;
    private Reason reason;
    private String transitionNodeCode;
    private Version fixVersion;
    @Override
    public String action() {
       return OperationTypeEnum.DELAYFIX.getValue();
    }

    @Override
    public String actionCode() {
        return OperationTypeEnum.DELAYFIX.name();
    }

    @Override
    public String processType() {
        return null;
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        String reasonCtx = "延期原因：" + reason.getValue();
        if (StringUtils.isNotEmpty(content)) {
            reasonCtx = reasonCtx + "\n";
            reasonCtx = reasonCtx + "备注：" + content;
        }
        return reasonCtx;    }


    @Override
    public User getOperator() {
        return getTransactor();
    }

    @Override
    public DomainEnum domain() {
        return DomainEnum.ISSUE;
    }

    @Override
    public String getBusinessCode() {
        return this.code;
    }

    @Override
    public IssueStatus getCurStatus() {
        return this.curStatus;
    }
    @Override
    public String getContent() {
        return this.content;
    }
    @Override
    public IssueStatus getNextStatus() {
        return this.nextStatus;
    }

    @Override
    public Reason getReason() {
        return this.reason;
    }
}
