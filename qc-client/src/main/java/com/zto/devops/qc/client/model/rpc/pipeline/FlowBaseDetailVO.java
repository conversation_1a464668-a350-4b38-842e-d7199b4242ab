package com.zto.devops.qc.client.model.rpc.pipeline;

import com.zto.devops.qc.client.enums.rpc.FlowBusinessTypeEnum;
import com.zto.devops.qc.client.enums.rpc.FlowStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程基本详情信息
 **/
@Data
public class FlowBaseDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品code
     */
    private String productCode;

    /**
     * 共同-流程类型
     */
    private FlowBusinessTypeEnum flowBusinessType;

    /**
     * 共同-流程编码
     */
    private String flowCode;

    /**
     * 共同-流程名称
     */
    private String flowName;

    /**
     * 共同-流程状态
     */
    private FlowStatusEnum flowStatus;

    /**
     * 共同-流程状态描述
     */
    private String flowStatusDesc;

    /**
     * 功能或紧急分支名
     */
    private String featureBranch;

    /**
     * feature分支是否加锁标识
     */
    private Boolean featureBranchLockType;

    /**
     * feature分支加锁标识说明
     */
    private String featureBranchLockMessage;

    /**
     * 退回备注
     */
    private String backRemark;

    /**
     * 验收备注
     */
    private String acceptRemark;

    /**
     * 是否需要代码审查
     */
    private Boolean codeReview;


    /**
     * =============================以下是版本详情=====================
     */
    /**
     * 版本-版本编码
     */
    private String versionCode;

    /**
     * 版本-版本名称
     */
    private String versionName;

    /**
     * 版本-计划提测日期
     */
    private Date planSubmitTestDate;

    /**
     * 共同-计划发布日期（版本是当前的，发布是最晚的）
     */
    private Date planOnlineDate;

    /**
     * 版本-描述
     */
    private String desc;

}
