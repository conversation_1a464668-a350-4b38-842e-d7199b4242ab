package com.zto.devops.qc.client.model.testmanager.plan.entity;

import com.zto.devops.qc.client.enums.testmanager.plan.*;
import com.zto.devops.framework.client.simple.Button;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@ZModel(description = "测试计划")
public class TmTestPlanVO implements Serializable {
    private static final long serialVersionUID = -3798719808946969986L;

    @ZModelProperty(description = "计划编号")
    private String code;

    @ZModelProperty(description = "计划名")
    private String planName;

    @ZModelProperty(description = "计划类型")
    private TestPlanNewTypeEnum type;

    @ZModelProperty(description = "计划类型 描述")
    private String typeDesc;

    @ZModelProperty(description = "测试策略")
    private TestPlanStrategyEnum testStrategy;

    @ZModelProperty(description = "策略 描述")
    private String testStrategyDesc;

    @ZModelProperty(description = "状态")
    private TestPlanNewStatusEnum status;

    @ZModelProperty(description = "状态 描述")
    private String statusDesc;

    @ZModelProperty(description = "关联计划code")
    private String relationPlanCode;

    @ZModelProperty(description = "关联计划名称")
    private String relationPlanName;

    @ZModelProperty(description = "所属部门Id")
    private Long deptId;

    @ZModelProperty(description = "所属部门名称")
    private String deptName;

    @ZModelProperty(description = "产品code")
    private String productCode;

    @ZModelProperty(description = "产品名称")
    private String productName;

    @ZModelProperty(description = "版本code")
    private String versionCode;

    @ZModelProperty(description = "版本名称")
    private String versionName;

    @ZModelProperty(description = "版本开始时间")
    private Date startDate;

    @ZModelProperty(description = "版本上线时间")
    private Date publishDate;

    @ZModelProperty(description = "计划提测/准入时间")
    private Date accessDate;

    @ZModelProperty(description = "计划提测时间-上下午")
    private TestPlanDatePartitionEnum accessDatePartition;

    @ZModelProperty(description = "计划提测时间-上下午描述")
    private String accessDatePartitionDesc;

    @ZModelProperty(description = "计划准出时间")
    private Date permitDate;

    @ZModelProperty(description = "计划准出时间-上下午")
    private TestPlanDatePartitionEnum permitDatePartition;

    @ZModelProperty(description = "计划准出时间-上下午描述")
    private String permitDatePartitionDesc;
    
    @ZModelProperty(description = "开发人数")
    private Integer developerNum;
    
    @ZModelProperty(description = "测试人数")
    private Integer testerNum;
    
    @ZModelProperty(description = "备注")
    private String comment;
    
    @ZModelProperty(description = "产品负责人id")
    private Long productDirectorId;
    
    @ZModelProperty(description = "产品负责人名")
    private String productDirectorName;
    
    @ZModelProperty(description = "测试负责人id")
    private Long testDirectorId;
    
    @ZModelProperty(description = "测试负责人名")
    private String testDirectorName;
    
    @ZModelProperty(description = "计划负责人id")
    private Long planDirectorId;
    
    @ZModelProperty(description = "计划负责人姓名")
    private String planDirectorName;
    
    @ZModelProperty(description = "计划负责人姓名(数据库真实值)")
    private String planDirectorNameReal;

    @ZModelProperty(description = "移动专项测试")
    private Boolean mobileSpecialTest;
    
    @ZModelProperty(description = "安全扫描")
    private Boolean securityScan;

    @ZModelProperty(description = "静态分析")
    private Boolean staticAnalysis;
    
    @ZModelProperty(description = "静态分析时间")
    private Date staticAnalysisTime;
    
    @ZModelProperty(description = "静态分析负责人id")
    private Long staticAnalysisDirectorId;
    
    @ZModelProperty(description = "静态分析负责人姓名")
    private String staticAnalysisDirectorName;

    @ZModelProperty(description = "性能测试")
    private Boolean performanceTest;
    
    @ZModelProperty(description = "性能测试时间")
    private Date performanceTestTime;
    
    @ZModelProperty(description = "性能测试负责人id")
    private Long performanceTestDirectorId;
    
    @ZModelProperty(description = "性能测试负责人名")
    private String performanceTestDirectorName;

    @ZModelProperty(description = "探索性测试")
    private Boolean exploratoryTest;
    
    @ZModelProperty(description = "探索性测试负责人")
    private Long exploratoryTestDirectorId;
    
    @ZModelProperty(description = "探索性测试负责人名")
    private String exploratoryTestDirectorName;
    
    @ZModelProperty(description = "探索性测试时间")
    private Date exploratoryTestTime;

    @ZModelProperty(description = "权限测试")
    private Boolean permissionsTest;
    
    @ZModelProperty(description = "优先级")
    private TestPlanPriorityEnum priority;
    
    @ZModelProperty(description = "优先级 描述")
    private String priorityDesc;
    
    @ZModelProperty(description = "最晚测试日期")
    private Date lastTestDate;
    
    @ZModelProperty(description = "测试信息")
    private String testInformation;
    
    @ZModelProperty(description = "权限测试信息")
    private String permissionsTestInformation;

    @ZModelProperty(description = "测试功能点List")
    List<TestFunctionPointVO> pointList = new ArrayList<>();

    @ZModelProperty(description = "相关报告")
    private List<TmTestPlanReportVO> reportList;

    @ZModelProperty(description = "可操作字段")
    private List<TmTestPlanEditEnum> fieldVOS;

    @ZModelProperty(description = "可操作按钮")
    private List<Button> buttonVOS;

    @ZModelProperty(description = "创建人编码")
    private Long creatorId;

    @ZModelProperty(description = "创建人名称")
    private String creator;

    @ZModelProperty(description = "创建时间")
    private Date gmtCreate;

    @ZModelProperty(description = "修改时间")
    private Long modifierId;

    @ZModelProperty(description = "修改人编码")
    private String modifier;

    @ZModelProperty(description = "修改时间")
    private Date gmtModified;

    @ZModelProperty(description = "修改次数")
    private Integer editNo;

    @ZModelProperty(description = "产品类型")
    private String productSource;

    @ZModelProperty(description = "邮件详情")
    private TmTestPlanSendEmailVO tmSendEmailVO;

    @ZModelProperty(description = "阶段状态", sample = "(测试阶段，SMOKE_TEST-冒烟测试|FUNCTIONAL_TEST-功能测试|ONLINE_SMOKE_TEST-线上冒烟测试):(INITIAL-未开始|IN_PROGESS进行中|COMPLETED已完成)")
    private Map<String, Object> stageStatus;

    @ZModelProperty(description = "测试用例评审按钮")
    private List<Button> caseReviewButtonVOS;

    @ZModelProperty(description = "是否是简易流程")
    private Boolean isSimpleTest;

    public String getStatusDesc() {
        return status == null ? "" : status.getValue();
    }

    public String getTypeDesc() {
        return type == null ? "" : type.getValue();
    }

    public String getTestStrategyDesc() {
        return testStrategy == null || testStrategy.equals(TestPlanStrategyEnum.NULL_TEST) ? "--" : testStrategy.getValue();
    }

    public String getAccessDatePartitionDesc() {
        return accessDatePartition == null ? "" : accessDatePartition.getValue();
    }

    public String getPermitDatePartitionDesc() {
        return permitDatePartition == null ? "" : permitDatePartition.getValue();
    }

    public String getPriorityDesc() {
        return priority == null ? "" : priority.getValue();
    }

}
