package com.zto.devops.qc.client.model.testmanager.email.query;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class VersionEmailQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "版本编号", required = false)
    private String versionCode;

    public VersionEmailQuery(String versionCode) {
        this.versionCode = versionCode;
    }

}
