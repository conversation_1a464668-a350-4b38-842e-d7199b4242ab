package com.zto.devops.qc.client.model.testmanager.plan.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class AddCaseInTestPlanCommand extends BaseCommand {
    public AddCaseInTestPlanCommand(String aggregateId) {
        super(aggregateId);
    }

    private String caseCode;

    private String testPlanCode;

    private TestcaseTypeEnum caseType;

    private List<TestPlanStageEnum> testStage;
}
