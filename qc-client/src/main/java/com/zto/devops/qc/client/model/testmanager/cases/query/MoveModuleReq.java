package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class MoveModuleReq implements Serializable {

    @ZModelProperty(description = "所选分组编号", required = true, sample = "TC231130064892")
    private String code;

    @ZModelProperty(description = "所选分组名称", required = true, sample = "111")
    private String name;

    @ZModelProperty(description = "所属产品code", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "所选分组父分组编号", required = false, sample = "TC231130064892")
    private String oldParentCode;

    @ZModelProperty(description = "所选分组父分组名称", required = false, sample = "222")
    private String oldParentName;

    @ZModelProperty(description = "移动后分组编号", required = true, sample = "TC231130064892")
    private String parentCode;

    @ZModelProperty(description = "移动后分组名称", required = true, sample = "333")
    private String parentName;

    @ZModelProperty(description = "层级", required = false, sample = "1")
    private Integer layer;

    @ZModelProperty(description = "移动前路径", required = false, sample = "0")
    private String oldPath;

    @ZModelProperty(description = "移动后路径", required = false, sample = "1")
    private String newPath;

    @ZModelProperty(description = "类型", required = true, sample = "MANUAL")
    private TestcaseTypeEnum type;

    @ZModelProperty(description = "属性 测试用例/模块", required = false, sample = "TESTCASE")
    private TestcaseAttributeEnum attribute;

    @ZModelProperty(description = "版本code", required = false, sample = "VER2302168133")
    private String versionCode;

    @ZModelProperty(description = "是否核心用例", required = false, sample = "0")
    private Boolean setCore;
}
