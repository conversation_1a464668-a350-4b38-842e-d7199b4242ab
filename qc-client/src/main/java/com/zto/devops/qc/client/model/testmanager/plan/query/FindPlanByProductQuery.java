package com.zto.devops.qc.client.model.testmanager.plan.query;

import com.zto.devops.framework.client.query.BaseQuery;
import lombok.Data;

import java.io.Serializable;

@Data
public class FindPlanByProductQuery extends BaseQuery implements Serializable {

    private String productCode;

    private String planName;

    private String planCode;

    public FindPlanByProductQuery(String planName, String productCode, String planCode) {
        this.planName = planName;
        this.productCode = productCode;
        this.planCode = planCode;
    }

}
