package com.zto.devops.qc.client.model.testmanager.report.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.ObservedEvent;
import com.zto.devops.qc.client.enums.report.AutoExecuteResult;
import com.zto.devops.qc.client.enums.report.CheckType;
import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.report.OperationEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.enums.testmanager.report.TmTestResultEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ReportAddedEvent extends BaseEvent implements ObservedEvent {

    /**
     * 报告编号
     */
    private String reportCode;

    /**
     * 报告名称
     */
    private String reportName;

    /**
     * 计划编号
     */
    private String planCode;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 报告类型(准入、准出、简易...)
     */
    private ReportType reportType;

    /**
     * 版本code--(获取计划信息；产品信息、版本信息)
     */
    private String versionCode;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 所属产品名称
     */
    private String productName;

    /**
     * 总体测试结果
     */
    private TmTestResultEnum testResult;

    /**
     * 计划提测时间
     */
    private Date planPresentationDate;

    /**
     * 计划提测时间-上下午
     */
    private String planPresentationDay;

    /**
     * 实际提测时间
     */
    private Date actualPresentationDate;

    /**
     * 计划准出时间
     */
    private Date planApprovalExitDate;

    /**
     * 计划准出时间-上下午
     */
    private String planApprovalExitDay;

    /**
     * 实际准出时间
     */
    private Date actualApprovalExitDate;

    /**
     * 实际上线时间
     */
    private Date actualOnlineDate;

    /**
     * 计划上线时间
     */
    private Date planOnlineDate;

    /**
     * 实际测试开始时间
     */
    private Date actualTestStart;

    /**
     * 实际测试结束时间
     */
    private Date actualTestEnd;

    /**
     * 版本开始时间
     */
    private Date startDate;

    /**
     * 验收开始时间
     */
    private Date checkStartDate;

    /**
     * 验收结束时间
     */
    private Date checkEndDate;

    /**
     * 更新测试结果时间
     */
    private Date updateTestResultDate;

    /**
     * 自动化测试结果--未执行
     */
    private AutoExecuteResult autoTestResult;


    /**
     * 安全测试人ID
     */
    private Long securityUserId;

    /**
     * 安全测试人名称
     */
    private String securityUserName;

    /**
     * 安全测试结果
     */
    private SecurityTestResult securityTestResult;

    /**
     * 验收类型 外采，内部
     */
    private CheckType checkType;

    /**
     * 开发人数
     */
    private Integer developerCount;

    /**
     * 测试人数
     */
    private Integer testerCount;

    /**
     * 用例数
     */
    private Integer testCaseNum;

    /**
     * 计划用例数
     */
    private Integer planCaseNum;

    /**
     * 通过用例数
     */
    private Integer permitNum;

    /**
     * 按计划范围上线  1 是, 0 否
     */
    private Integer asPlanedOnline;

    /**
     * 是否延期 1 是, 0 否
     */
    private Integer delay;

    /**
     * 总结、分析、描述
     */
    private String summary;

    /**
     * 状态： 草稿， 已发送 未发送
     */
    private TestPlanStatusEnum status;

    /**
     * 预览邮件
     */
    private String preview;

    //收件人
    private List<SendUserInfoVO> receiveUsers;

    //抄送人
    private List<SendUserInfoVO> ccUsers;

    private List<AttachmentVO> attachments;

    private OperationEnum operation;
}
