package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentDocumentTypeEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentFileTypeEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.util.Date;

@GatewayModel(description = "附件")
@Data
public class AttachmentVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ZModelProperty(description = "领域", required = true, sample = "REQUIREMENT")
    private DomainEnum domain;

    @ZModelProperty(description = "业务编码", required = true, sample = "SNF987293442717515776")
    private String businessCode;

    @ZModelProperty(description = "附件编码", required = true, sample = "SNF987293442717515776")
    private String code;

    @ZModelProperty(description = "附件路径", required = true, sample = "/url")
    private String url;

    @ZModelProperty(description = "附件名称", required = true, sample = "url.txt")
    private String name;

    @ZModelProperty(description = "私有组文件名", required = true, sample = "test")
    private String remoteFileId;

    @ZModelProperty(description = "附件类型 FILE 文件, URL 链接", required = true, sample = "FILE")
    private AttachmentTypeEnum type;

    @ZModelProperty(description = "文档类型", required = true, sample = "BUSINESS")
    private AttachmentDocumentTypeEnum documentType;

    @ZModelProperty(description = "文件类型", sample = "TXT")
    private AttachmentFileTypeEnum fileType;

    @ZModelProperty(description = "大小", sample = "100")
    private String size;

    @ZModelProperty(description = "创建人id", required = true, sample = "5984549")
    private Long creatorId;

    @ZModelProperty(description = "创建人", required = true, sample = "luban")
    private String creator;

    @ZModelProperty(description = "创建时间", required = true, sample = "1711987200000")
    private Date gmtCreate;

    @ZModelProperty(description = "创建人id", required = true, sample = "5984549")
    private Long modifierId;

    @ZModelProperty(description = "创建人id", required = true, sample = "luban")
    private String modifier;

    @ZModelProperty(description = "创建人id", required = true, sample = "1711987200000")
    private Date gmtModified;

}
