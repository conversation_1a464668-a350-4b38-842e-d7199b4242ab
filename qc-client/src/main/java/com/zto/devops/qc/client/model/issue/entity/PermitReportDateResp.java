package com.zto.devops.qc.client.model.issue.entity;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

//准出报告
@Data
public class PermitReportDateResp implements Serializable {
    //版本编号
    private String versionCode;
    //计划名
    private String name;

    //实际准出时间
    private String actualTestEndDate;

    //测试结果
    private String testResult;
    //准出报告创建人
    private String createUser;
    //准出报告创建时间
    private String createTime;
    //实际准出时段
    private Integer actualAccessHalf;
    //类型
    private Integer type;

    //抄送人
    private List<String> ccUsersEmail;

    private List<String> recipientsEmail;

}
