package com.zto.devops.qc.client.model.rpc.pipeline.query;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 查询版本流程实际时间
 * @Author:
 * @Date: 2021/10/26 16:06
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FindVersionFlowDateQuery implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 版本编码
     */
    private String versionCode;
}
