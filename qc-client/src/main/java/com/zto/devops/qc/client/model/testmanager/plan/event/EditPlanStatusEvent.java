package com.zto.devops.qc.client.model.testmanager.plan.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.ObservedEvent;
import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewTypeEnum;
import lombok.Data;

@Data
public class EditPlanStatusEvent extends BaseEvent implements ObservedEvent {
    private String planCode;
    private TestPlanNewStatusEnum status;
    private String relationPlanCode;
    private TestPlanNewTypeEnum planType;
    private SecurityTestResult securityTestResult;
}
