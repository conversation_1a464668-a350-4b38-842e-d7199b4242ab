package com.zto.devops.qc.client.model.issue.query;

import com.zto.devops.framework.client.query.ExpQueryBase;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
public class PageIssueThingQuery extends ExpQueryBase implements Serializable {

    @GatewayModelProperty(description = "是否有效缺陷",required = false)
    private Boolean validFlag;

    @GatewayModelProperty(description = "缺陷名称", required = false)
    private String codeOrTitle;

    @GatewayModelProperty(description = "缺陷编号集合", required = false)
    private List<String> issueCodeList;

    @GatewayModelProperty(description = "当前用户id", required = false)
    private Long currentUserId;

    @GatewayModelProperty(description = "缺陷状态", required = false)
    private List<IssueStatus> statusList;

    @GatewayModelProperty(description = "缺陷优先级", required = false)
    private List<IssuePriority> priorityList;

    @GatewayModelProperty(description = "关于我", required = false)
    private List<RelatedToMeEnum> relatedList;

    @GatewayModelProperty(description = "当前处理人", required = false)
    private List<Long> handleUserIdList;

    @GatewayModelProperty(description = "开发人员", required = false)
    private List<Long> developUserIdList;

    @GatewayModelProperty(description = "测试人员", required = false)
    private List<Long> testUserIdList;

    @GatewayModelProperty(description = "发现版本", required = false)
    private List<String> findVersionList;

    @GatewayModelProperty(description = "修复的版本", required = false)
    private List<String> fixVersionList;

    @GatewayModelProperty(description = "修复或发现的版本", required = false)
    private List<String> fixOrFindVersionList;

    @GatewayModelProperty(description = "关联需求", required = false)
    private List<String> relatedRequireList;

    @GatewayModelProperty(description = "关联产品", required = false)
    private List<String> relatedProductList;

    @GatewayModelProperty(description = "关联迭代code", required = false)
    private List<String>  sprintCode ;

    @GatewayModelProperty(description = "Bug根源", required = false)
    private List<IssueRootCause> rootCauseList;

    @GatewayModelProperty(description = "Bug类别", required = false)
    private List<IssueType> issueTypeList;

    @GatewayModelProperty(description = "测试方法", required = false)
    private List<IssueTestMethod> testMethodList;

    @GatewayModelProperty(description = "重现概率", required = false)
    private List<IssueRepetitionRate> repetitionRateList;

    @GatewayModelProperty(description = "发现阶段", required = false)
    private List<IssueFindStage> findStageList;

    @GatewayModelProperty(description = "发现环境", required = false)
    private List<IssueFindEnv> findEnvList;

    @GatewayModelProperty(description = "报告人", required = false)
    private List<Long> findUserIdList;

    @GatewayModelProperty(description = "标签", required = false)
    private List<String> tagName;

    @GatewayModelProperty(description = "创建时间", required = false)
    private Date createTimeStart;

    @GatewayModelProperty(description = "创建时间", required = false)
    private Date createTimeEnd;

    @GatewayModelProperty(description = "关闭时间", required = false)
    private Date closeTimeStart;

    @GatewayModelProperty(description = "关闭时间", required = false)
    private Date closeTimeEnd;
    @GatewayModelProperty(description = "修复的版本是否为空", required = false)
    private Boolean fixVersionIsNull;

    @GatewayModelProperty(description = "更新时间-开始", required = false)
    private Date gmtModifiedStart;

    @GatewayModelProperty(description = "更新时间-结束", required = false)
    private Date gmtModifiedEnd;

    @GatewayModelProperty(description = "before 版本确认之前，after 版本确认之前", sample = "before", required = false)
    private List<String> versionConfirm;

    @GatewayModelProperty(description = "权限", sample = "before", required = false)
    private List<String> permissions;

    @GatewayModelProperty(description = "是否展示按钮", required = false)
    private boolean needEvent = true;

    @GatewayModelProperty(description = "应用类型", required = false)
    private IssueApplicationType applicationType;

    @GatewayModelProperty(description = "应用类型List", required = false)
    private List<IssueApplicationType> applicationTypeList;

    private List<RefuseReason> refuseReasonList;

    @GatewayModelProperty(description = "排序字段", required = false)
    private String orderField;

    @GatewayModelProperty(description = "排序方式", required = false)
    private String orderType;

    @GatewayModelProperty(description = "抄送人id", required = false)
    private List<Long> ccUserIdList;
}
