package com.zto.devops.qc.client.model.testmanager.plan.query;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.report.TestReportTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class RelatedPlanReportQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "计划编号", required = false)
    private String planCode;

    @GatewayModelProperty(description = "报告编号", required = false)
    private String reportCode;

    @GatewayModelProperty(description = "报告类型;", required = false)
    private TestReportTypeEnum reportType;

    @GatewayModelProperty(description = "计划类型;", required = false)
    private TestPlanNewTypeEnum planType;

    @GatewayModelProperty(description = "版本编号;", required = false)
    private String versionCode;

}
