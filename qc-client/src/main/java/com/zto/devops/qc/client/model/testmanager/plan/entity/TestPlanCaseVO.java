package com.zto.devops.qc.client.model.testmanager.plan.entity;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@GatewayModel(description = "测试计划关联用例")
public class TestPlanCaseVO implements Serializable {

    private Long id;

    @GatewayModelProperty(description = "版本code", sample = "1", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "关联计划code", sample = "1", required = false)
    private String planCode;

    @GatewayModelProperty(description = "用例编号", sample = "1", required = false)
    private String caseCode;

    @GatewayModelProperty(description = "用例所属父用例code", sample = "1", required = false)
    private String parentCode;

    @GatewayModelProperty(description = "用例所属父用例名称", sample = "1", required = false)
    private String parentName;

    @GatewayModelProperty(description = "用例名称", sample = "1", required = false)
    private String caseName;

    @GatewayModelProperty(description = "用例属性", sample = "1", required = false)
    private TestcaseAttributeEnum caseAttribute;

    @GatewayModelProperty(description = "自动化用例节点类型", sample = "1", required = false)
    private String nodeType;

    @GatewayModelProperty(description = "用例path", sample = "1", required = false)
    private String casePath;

    @GatewayModelProperty(description = "测试阶段,SMOKE_TEST-冒烟测试|FUNCTIONAL_TEST-功能测试|ONLINE_SMOKE_TEST-线上冒烟测试", sample = "1", required = false)
    private TestPlanStageEnum testStage;

    @GatewayModelProperty(description = "测试阶段", sample = "1", required = false)
    private String testStageDesc;

    @GatewayModelProperty(description = "用例类型，MANUAL-手工|AUTO-自动化", sample = "1", required = false)
    private TestcaseTypeEnum type;

    @GatewayModelProperty(description = "用例类型", sample = "1", required = false)
    private String typeDesc;

    @GatewayModelProperty(description = "执行结果，INITIAL-未执行|PASSED-通过|FAILED-失败|BLOCK-阻塞|SKIP-跳过|RETEST-重测", sample = "1", required = false)
    private TestPlanCaseStatusEnum status;

    @GatewayModelProperty(description = "执行结果", sample = "1", required = false)
    private String statusDesc;

    @GatewayModelProperty(description = "停用状态", sample = "1", required = false)
    private TestcaseStatusEnum testcaseStatus;

    @GatewayModelProperty(description = "停用", sample = "1", required = false)
    private String testcaseStatusDesc;

    @GatewayModelProperty(description = "用例等级 HIGH-高|MIDDLE-中|LOW-低", sample = "1", required = false)
    private TestcasePriorityEnum priority;

    @GatewayModelProperty(description = "用例等级", sample = "1", required = false)
    private String priorityDesc;

    @GatewayModelProperty(description = "执行人id", sample = "1", required = false)
    private Long executorId;

    @GatewayModelProperty(description = "执行人姓名", sample = "1", required = false)
    private String executor;

    @GatewayModelProperty(description = "操作code", sample = "1", required = false)
    private String operateCaseCode;

    @GatewayModelProperty(description = "责任人id", sample = "1", required = false)
    private Long dutyUserId;

    @GatewayModelProperty(description = "责任人姓名", sample = "1", required = false)
    private String dutyUser;

    @GatewayModelProperty(description = "执行结果路径", sample = "1", required = false)
    private String resultPath;

    @GatewayModelProperty(description = "执行日志路径", sample = "1", required = false)
    private String logPath;

    @GatewayModelProperty(description = "缺陷总数", sample = "1", required = false)
    private Long issueCount;

    @GatewayModelProperty(description = "未修复的缺陷数", sample = "1", required = false)
    private Long notFixIssueCount;

    @GatewayModelProperty(description = "执行数", sample = "1", required = false)
    private Long executeNum;

    @GatewayModelProperty(description = "创建人id", sample = "1", required = false)
    private Long creatorId;

    @GatewayModelProperty(description = "创建人", sample = "1", required = false)
    private String creator;

    @GatewayModelProperty(description = "创建时间", sample = "1", required = false)
    private Date gmtCreate;

    @GatewayModelProperty(description = "更新人id", sample = "1", required = false)
    private Long modifierId;

    @GatewayModelProperty(description = "更新人", sample = "1", required = false)
    private String modifier;

    @GatewayModelProperty(description = "更新时间", sample = "1", required = false)
    private Date gmtModified;

    @GatewayModelProperty(description = "自动化记录类型", sample = "1", required = false)
    private AutomaticRecordTypeEnum automaticRecordType;

    @GatewayModelProperty(description = "结果备注", required = false)
    private String resultComment;

    private List<TestPlanCaseVO> children = new ArrayList<>();

    private Integer childrenNum = 0;

    private Integer enable;

    private String testcaseModulePath;

    private Integer sort;

    private String interfaceName;

    private Boolean enableChecked;

    public String getTestStageDesc() {
        return testStage == null ? "" : testStage.getValue();
    }

    public String getTypeDesc() {
        return type == null ? "" : type.getValue();
    }

    public String getStatusDesc() {
        return status == null ? "" : status.getValue();
    }

    public String getPriorityDesc() {
        return priority == null ? "" : priority.getValue();
    }

    public String getTestcaseStatusDesc() {
        return testcaseStatus == null ? "" : testcaseStatus.getDesc();
    }
}
