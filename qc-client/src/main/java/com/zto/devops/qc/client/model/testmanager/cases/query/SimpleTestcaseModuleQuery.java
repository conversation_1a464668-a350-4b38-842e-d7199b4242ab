package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SimpleTestcaseModuleQuery extends BaseQuery {

    private TestcaseTypeEnum type;

    private String productCode;

    private String versionCode;

    private TestPlanStageEnum testStage;

    private String planCode;

    private Boolean planPattern;

    private String automaticSourceCode;

    private List<TestcaseStatusEnum> statusList;

    private Boolean setCore;

}
