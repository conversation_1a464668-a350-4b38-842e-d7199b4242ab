package com.zto.devops.qc.client.model.testmanager.plan.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ListPlanCaseQuery extends BaseQuery {

    private String productCode;

    private String versionCode;

    private String planCode;

    private List<TestPlanStageEnum> testPlanStageList;

    private String parentCode;

    private List<TestcaseTypeEnum> caseTypeList;

    private List<String> caseCodeList;

    private String search;

    private List<TestPlanCaseStatusEnum> statusList;

    private List<Long> executorIdList;

    private List<TestcasePriorityEnum> priorityList;

    private List<AutomaticNodeTypeEnum> nodeTypeList;

    private List<AutomaticRecordTypeEnum> automaticTypeList;

    private List<String> tagList;

    private List<Boolean> setCoreList;

    private Boolean isAuto;
}
