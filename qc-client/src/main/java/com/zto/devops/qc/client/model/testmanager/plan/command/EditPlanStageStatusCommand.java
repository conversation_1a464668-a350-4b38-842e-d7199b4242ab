package com.zto.devops.qc.client.model.testmanager.plan.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanButttonTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EditPlanStageStatusCommand extends BaseCommand {

    public EditPlanStageStatusCommand(String aggregateId) {
        super(aggregateId);
    }

    @GatewayModelProperty(description = "主键Code", required = false)
    private String planCode;

    @GatewayModelProperty(description = "按钮类型", required = false)
    private TestPlanButttonTypeEnum type;

    @GatewayModelProperty(description = "阶段", required = false)
    private TestPlanStageEnum stage;

}
