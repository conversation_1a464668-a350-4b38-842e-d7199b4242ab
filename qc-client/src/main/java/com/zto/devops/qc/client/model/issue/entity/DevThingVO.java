package com.zto.devops.qc.client.model.issue.entity;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DevThingVO implements Serializable {

    @GatewayModelProperty(description = "REQUIREMENT/TASK/ISSUE", required = false)
    private String domain;

    @GatewayModelProperty(description = "事项业务编码", required = false)
    private String businessCode;

    @GatewayModelProperty(description = "事项名称", required = false)
    private String businessName;

    @GatewayModelProperty(description = "处理人id", required = false)
    private Long handlerId;

    @GatewayModelProperty(description = "处理人姓名", required = false)
    private String handlerName;

    @GatewayModelProperty(description = "产品编码", required = false)
    private String productCode;

    @GatewayModelProperty(description = "产品名称", required = false)
    private String productName;
}
