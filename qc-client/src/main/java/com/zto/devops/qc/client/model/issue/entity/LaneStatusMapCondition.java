package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.issue.LaneDomainEnum;

public abstract class LaneStatusMapCondition implements Serializable {

    private static final long serialVersionUID = 1L;


    protected LaneDomainEnum laneDomain;

    public LaneStatusMapCondition(LaneDomainEnum lane) {
        this.laneDomain = lane;
    }

    public LaneDomainEnum getLaneDomain() {
        return laneDomain;
    }
}
