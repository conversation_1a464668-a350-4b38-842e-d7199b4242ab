package com.zto.devops.qc.client.model.testPlan.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testPlan.entity.TestFunctionPointVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AddSafetyTestPlanCommand extends BaseCommand {
    public AddSafetyTestPlanCommand(String aggregateId) {
        super(aggregateId);
    }
    private String code;
    private String planName;
    private String productCode;
    private String productName;
    private Long testDirectorId;
    private String testDirectorName;
    private Date toTestDate;
    private Date approvalOutDate;
    private Long creatorId;
    private String preview;
    private String status;
    private String remarks;
    private String versionCode;
    private String versionName;
    private Boolean permissionsTest;

    private String priority;

    private Date lastTestDate;

    private String testPlanMainCode;

    private String testPlanCode;


    /**
     * 测试信息
     */
    private String testInformation;

    /**
     * 权限测试信息
     */
    private String priorityTestInformation;



    List<TestFunctionPointVO> pointList;

    private List<SendUserInfoVO> receiveUsers;

    //抄送人
    private List<SendUserInfoVO> ccUsers;

}
