package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.Reason;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class TransitionNodeVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 编码
     */
    private String code;

    /**
     * 所属领域
     */
    private DomainEnum domain;

    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 当前节点
     */
    private IssueStatus curStatus;

    /**
     * 节点内容json存储
     */
    private String content;

    private Reason reason;
    /**
     * next节点
     */
    private IssueStatus nextStatus;
    /**
     * 是否删除 1 未删除, 0 已删除
     */
    private Boolean enable;

    /**
     * 创建人编码
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新时间
     */
    private Date gmtModified;

}
