package com.zto.devops.qc.client.model.testmanager.plan.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ListPlanCaseModuleQuery extends BaseQuery {

    private String productCode;

    private String planCode;

    private List<TestPlanStageEnum> testPlanStageList;

    private List<TestcaseTypeEnum> caseTypeList;

    private Boolean isAuto;

    private Boolean setCore;
}
