package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.MyCreatedEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.OperationTypeEnum;
import lombok.Data;

import java.util.Date;

@Data
public class CommentRemovedSimpleEvent extends BaseEvent implements ActionEvent {
    private String code;

    @Override
    public String action() {
        return OperationTypeEnum.REMOVECOMMENT.getValue();
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        return "";
    }
}
