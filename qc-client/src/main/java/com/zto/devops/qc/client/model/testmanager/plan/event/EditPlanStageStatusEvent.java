package com.zto.devops.qc.client.model.testmanager.plan.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.ObservedEvent;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanButttonTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class EditPlanStageStatusEvent extends BaseEvent implements ObservedEvent {

    @GatewayModelProperty(description = "主键Code", required = false)
    private String planCode;

    @GatewayModelProperty(description = "按钮类型", required = false)
    private TestPlanButttonTypeEnum type;

    @GatewayModelProperty(description = "阶段", required = false)
    private TestPlanStageEnum stage;

}
