package com.zto.devops.qc.client.model.testmanager.report.event;

import com.zto.devops.qc.client.enums.report.CodeCoverResult;
import com.zto.devops.qc.client.enums.testmanager.report.UiTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.report.entity.IssueInfoVO;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageReasonVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.CaseExecuteResultVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SimpleReportEvent extends ReportAddedEvent {

    @GatewayModelProperty(description = "zui测试结果", required = false)
    private ZUITestResultEnum zuiTestResult;

    @GatewayModelProperty(description = "测试信息", required = false)
    private CaseExecuteResultVO caseExecuteResultVO;

    @GatewayModelProperty(description = "缺陷信息统计", required = false)
    private List<IssueInfoVO> issueInfoVOS;

    @GatewayModelProperty(description = "遗留缺陷", required = false)
    private List<IssueLegacyVO> issueLegacyVOS;

    @GatewayModelProperty(description = "覆盖率结果")
    private CodeCoverResult coverageResult;

    @GatewayModelProperty(description = "代码覆盖率不达标原因")
    private List<CoverageReasonVO> coverageReasonVOS;

    @GatewayModelProperty(description = "ui测试结果", required = false)
    private UiTestResultEnum uiTestResult;
}
