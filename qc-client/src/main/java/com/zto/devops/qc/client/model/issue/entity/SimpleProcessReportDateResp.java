package com.zto.devops.qc.client.model.issue.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

//简易报告
@Data
public class SimpleProcessReportDateResp implements Serializable {
    //版本编号
    private String versionCode;
    //计划名
    private String name;
    //计划提测时间
    private String planTestStartDate;
    //实际提测时间
    private String actualTestStartDate;
    //实际准出时间
    private String actualTestEndDate;
    //实际上线时间
    private String actualVersionEndDate;
    //产品负责人
    private String prdLeader;
    //测试结果
    private String testResult;
    //是否延期
    private Integer delayFlag;
    //是否按计划范围上线
    private Integer planFlag;
    //计划提测时段
    private Integer planTestHalf;
    //实际提测时段
    private Integer actualTestHalf;
    //计划准出时段
    private Integer planAccessHalf;
    //实际准出时段
    private Integer actualAccessHalf;
    //计划冒烟用例数
    private Integer smokeTestCase;
    //首次通过冒烟用例数
    private Integer smokeTestCasePassed;
    //开发人数
    private Integer developerNum;
    //测试人数
    private Integer testerNum;
    //简易报告创建人
    private String createUser;
    //简易报告创建时间
    private String createTime;
    //类型
    private Integer type;
    //抄送人
    private List<String> ccUsersEmail;

    private List<String> recipientsEmail;

}
