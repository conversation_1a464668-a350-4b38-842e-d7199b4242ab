package com.zto.devops.qc.client.model.report.entity;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class QcUserNoticeVO implements Serializable {

    /**
     * code
     */
    @GatewayModelProperty(description = "code")
    private String code;
    /**
     * 业务编码
     */
    @GatewayModelProperty(description = "业务编码code")
    private String businessCode;

    /**
     * 用户id
     */
    @GatewayModelProperty(description = "用户id")
    private Integer userId;

    /**
     * 用户姓名
     */
    @GatewayModelProperty(description = "用户姓名")
    private String userName;
    /**
     * 部门
     */
    @GatewayModelProperty(description = "部门id")
    private Integer deptId;

    /**
     * 部门
     */
    @GatewayModelProperty(description = "部门名称")
    private String deptName;

    /**
     * 用户类型
     */
    @GatewayModelProperty(description = "用户类型")
    private String userType;

    /**
     * 用户类型
     */
    @GatewayModelProperty(description = "用户类型code")
    private String userTypeCode;

    /**
     * 通知类型
     */
    @GatewayModelProperty(description = "通知类型")
    private String noticeType;


    @GatewayModelProperty(description = "发送时间")
    private Date sendDate;

}