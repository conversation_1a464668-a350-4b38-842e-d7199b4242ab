package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PageHeartRunDetailQuery extends PageQueryBase {

    @ZsmpModelProperty(description = "自动化任务code")
    private String automaticTaskCode;

    @ZsmpModelProperty(description = "运行结果")
    private List<TestPlanCaseStatusEnum> statusList;

    @ZsmpModelProperty(description = "开始时间-开始日期")
    private Date startTimeBegin;

    @ZsmpModelProperty(description = "开始时间-结束日期")
    private Date startTimeEnd;

    @ZsmpModelProperty(description = "结束时间-开始日期")
    private Date finishTimeBegin;

    @ZsmpModelProperty(description = "结束时间-结束日期")
    private Date finishTimeEnd;

}
