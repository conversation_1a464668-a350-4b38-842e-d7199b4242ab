package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAbandonReasonEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ChangeTestcaseStatusCommand extends BaseCommand {

    private TestcaseStatusEnum status;

    private TestcaseAbandonReasonEnum abandonReason;

    public ChangeTestcaseStatusCommand(String aggregateId) {
        super(aggregateId);
    }
}
