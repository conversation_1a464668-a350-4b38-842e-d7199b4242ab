package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.Sprint;
import com.zto.devops.framework.client.simple.User;
import lombok.Data;

import java.util.Date;

/**
 * @author: minjd
 * @date: Created in 2021/9/17 15:02
 */
@Data
public class SprintInIssueEditedEvent extends BaseEvent implements ActionEvent {

    private String code;

    private Sprint sprint;

    @Override
    public String action() {
        return "迭代信息变更";
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return getOccurred();
    }

    @Override
    public String makeString() {
        return this.sprint.getName();
    }
}
