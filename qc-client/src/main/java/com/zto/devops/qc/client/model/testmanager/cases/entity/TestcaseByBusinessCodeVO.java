package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TestcaseByBusinessCodeVO implements Serializable {

    @ZsmpModelProperty(description = "编号")
    private String code;

    @ZsmpModelProperty(description = "标题")
    private String title;

    @ZsmpModelProperty(description = "用例状态")
    private TestcaseStatusEnum status;

    @ZsmpModelProperty(description = "用例状态说明")
    private String statusDesc;

    @ZsmpModelProperty(description = "用例负责人id")
    private Long dutyUserId;

    @ZsmpModelProperty(description = "用例负责人名称")
    private String dutyUser;


    public String getStatusDesc() {
        return  status == null ? "" : status.getDesc();
    }
}
