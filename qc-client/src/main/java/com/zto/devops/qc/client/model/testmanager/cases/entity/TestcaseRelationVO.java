package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class TestcaseRelationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "用例code", required = false)
    private String testcaseCode;
    @GatewayModelProperty(description = "业务code", required = false)
    private String businessCode;
    @GatewayModelProperty(description = "领域", required = false)
    private DomainEnum domain;
}
