package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class HeartCaseDetailVO extends TestHeartCaseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ZsmpModelProperty(description = "产品code")
    private String productCode;

    @ZsmpModelProperty(description = "上级code")
    private String parentCode;

    @ZsmpModelProperty(description = "用例类型")
    private TestcaseTypeEnum type;

    @ZsmpModelProperty(description = "用例状态")
    private TestcaseStatusEnum testcaseStatus;

    @ZsmpModelProperty(description = "用例优先级")
    private TestcasePriorityEnum priority;

    @ZsmpModelProperty(description = "用例备注")
    private String comment;

    @ZsmpModelProperty(description = "上级code")
    private String path;

    @ZsmpModelProperty(description = "全部上级名称")
    private List<String> parentFullName;

    @ZsmpModelProperty(description = "用例责任人id")
    private Long dutyUserId;

    @ZsmpModelProperty(description = "执行结果文件内容")
    private List<ExecuteCaseResultContentVO> resultContent;

    @ZsmpModelProperty(description = "自动化任务id")
    private String automaticTaskId;

    @ZsmpModelProperty(description = "执行结果文件")
    private String resultFile;

    @ZsmpModelProperty(description = "报告文件")
    private String reportFile;

    @ZsmpModelProperty(description = "执行日志文件")
    private String execLogFile;

}
