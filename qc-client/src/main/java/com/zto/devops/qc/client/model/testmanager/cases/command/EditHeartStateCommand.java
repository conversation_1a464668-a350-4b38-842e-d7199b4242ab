package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.AddHeartTestCaseEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@ZsmpModel(description = "编辑心跳用例状态Command")
public class EditHeartStateCommand extends BaseCommand implements Serializable {
    public EditHeartStateCommand(String aggregateId) {
        super(aggregateId);
    }

    @ZsmpModelProperty(description = "产品code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "用例code")
    private String caseCode;

    @ZsmpModelProperty(description = "操作 开启OPEN，关闭CLOSE")
    private AddHeartTestCaseEnum operation;

    @ZsmpModelProperty(description = "App的code")
    private String appCode;

    @ZsmpModelProperty(description = "AppID")
    private String appId;

    @ZsmpModelProperty(description = "App名称")
    private String appName;
}
