package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.MyDeletedEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.OperationTypeEnum;
import com.zto.devops.qc.client.enums.issue.Reason;
import com.zto.devops.qc.client.model.common.TransitionNodeAddEvent;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class IssueRemovedEvent extends BaseEvent implements ActionEvent, TransitionNodeAddEvent, MyDeletedEvent {

    private String businessCode;
    private String code;
    private IssueStatus status = IssueStatus.REMOVED;

    private IssueStatus curStatus;
    private IssueStatus nextStatus;
    private String content;

    private Reason reason;
    private String transitionNodeCode;

    @Override
    public String action() {
        String returnStr = OperationTypeEnum.REMOVEDISSUE.getValue();
        if (StringUtils.isNotEmpty(content)) {
            returnStr = returnStr + "," + "备注:" + content;
        }
        return returnStr;
    }

    @Override
    public String actionCode() {
        return OperationTypeEnum.REMOVEDISSUE.name();
    }

    @Override
    public String processType() {
        return null;
    }

    @Override
    public User transactor() {
        return getOperator();
    }

    @Override
    public Date occurred() {
        return getOccurred();
    }


    @Override
    public String makeString() {
        return "";
    }

    @Override
    public DomainEnum domain() {
        return DomainEnum.ISSUE;
    }

    @Override
    public String getBusinessCode() {
        return this.code;
    }

    @Override
    public User getOperator() {
        return getTransactor();
    }

    @Override
    public IssueStatus getCurStatus() {
        return this.curStatus;
    }

    @Override
    public String getContent() {
        return this.content;
    }

    @Override
    public IssueStatus getNextStatus() {
        return this.nextStatus;
    }

    @Override
    public Reason getReason() {
        return this.reason;
    }
}
