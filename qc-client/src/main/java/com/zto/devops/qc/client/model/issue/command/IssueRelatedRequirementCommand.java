package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.qc.client.enums.issue.RequirementLevel;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class IssueRelatedRequirementCommand extends IssueBaseCommand {

    private String requirementCode;
    private String requirementName;
    private RequirementLevel requirementLevel;
    public IssueRelatedRequirementCommand(String aggregateId) {
        super(aggregateId);
    }
}
