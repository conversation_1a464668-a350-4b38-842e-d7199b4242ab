package com.zto.devops.qc.client.model.report.command;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.IssueInfoVO;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：AddExternalTestReportReq
 * 类 描 述：TODO
 * 创建时间：2021/11/13 5:24 下午
 * 创 建 人：bulecat
 * <AUTHOR>
 */
@Getter
@Setter
public class EditAndSendExternalTestReportCommand extends BaseReportInfoComnand {

    @GatewayModelProperty(description = "缺陷信息统计", required = false)
    private List<IssueInfoVO> issueInfoVOS;

    @GatewayModelProperty(description = "遗留缺陷", required = false)
    private List<IssueLegacyVO> issueLegacyVOS;

    @GatewayModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;

    public EditAndSendExternalTestReportCommand(String aggregateId) {
        super(aggregateId);
    }

}
