package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class XmindCaseAddCommand extends BaseCommand {

    public XmindCaseAddCommand(String aggregateId) {
        super(aggregateId);
    }

    @ZsmpModelProperty(description = "新增节点类型(用例/模块/步骤)")
    private TestcaseAttributeEnum attribute;

    @ZsmpModelProperty(description = "节点名称")
    private String topic;

    @ZsmpModelProperty(description = "层级")
    private Integer layer;

    @ZsmpModelProperty(description = "上级节点code")
    private String parentCode;

    @ZsmpModelProperty(description = "产品code")
    private String productCode;

    @ZsmpModelProperty(description = "类型")
    private TestcaseTypeEnum type;

    @ZsmpModelProperty(description = "用例等级 HIGH MIDDLE LOW")
    private TestcasePriorityEnum priority;

    @ZsmpModelProperty(description = "是否核心用例", required = false)
    private Boolean setCore;

    @ZsmpModelProperty(description = "版本code", required = false)
    private String versionCode;

}
