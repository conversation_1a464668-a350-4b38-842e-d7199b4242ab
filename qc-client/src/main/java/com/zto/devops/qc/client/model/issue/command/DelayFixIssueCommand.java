package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.enums.issue.Reason;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DelayFixIssueCommand extends IssueBaseCommand {

    public DelayFixIssueCommand(String aggregateId) {
        super(aggregateId);
    }
    private String content;
    private Reason reason;

    private Version fixVersion;
}
