package com.zto.devops.qc.client.model.testPlan.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testPlan.entity.TestFunctionPointVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class EditSafetyTestPlanCommand extends BaseCommand {
    public EditSafetyTestPlanCommand(String aggregateId) {
        super(aggregateId);
    }
    private String code;
    private String planName;
    private String productCode;
    private String productName;
    private Long testDirectorId;
    private String testDirectorName;
    private Date toTestDate;
    private Date approvalOutDate;
    private Long creatorId;
    private String preview;
    private String status;
    private String remarks;

    private Boolean permissionsTest;

    private String priority;

    private Date lastTestDate;
    private String testPlanCode;


    /**
     * 测试信息
     */
    private String testInformation;

    /**
     * 权限测试信息
     */
    private String priorityTestInformation;

    List<TestFunctionPointVO> pointList;


    @GatewayModelProperty(description = "收件人list", required = false)
    private List<SendUserInfoVO> receiveUsers;

    //抄送人
    @GatewayModelProperty(description = "抄送人list", required = false)
    private List<SendUserInfoVO> ccUsers;

}
