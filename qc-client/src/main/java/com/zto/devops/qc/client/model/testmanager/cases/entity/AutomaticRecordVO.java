package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AutomaticRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String code;
    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 登记库名称
     */
    private String name;

    /**
     * 备注
     */
    private String comment;

    /**
     * jmeter:源地址-OSS上的脚本文件地址;xunit-git地址
     */
    private String address;

    /**
     * 类型：1-jmeter, 2-testng, 3-postman, 4-junit,5-pyunit
     */
    private AutomaticRecordTypeEnum type;

    /**
     * 创建人编码
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date gmtModified;

    private String fileName;

    private String bucketName;

    /**
     * 数据文件地址
     */
    private String dataFileAddress;

    /**
     * 扩展jar包地址
     */
    private String extendJarAddress;

    /**
     * 第三方jar包地址
     */
    private String thirdJarAddress;

    /**
     * 失败原因
     */
    private String failInformation;

    private AutomaticStatusEnum status;

    /**
     * 更新人编码
     */
    private Long personLiableId;

    /**
     * 更新人
     */
    private String personLiable;


    /**
     * 可操作按钮
     */
    private List<Button> buttons;

    //所属目录code
    private String testcaseCode;

    private TestcaseVO testcaseVO;

    private List<AutomaticRecordLogVO> logList;

    private String workSpace;
    private String branch;
    private String commitId;
    private String scanDirectory;

    private String errorLogFile;

    private String dataDir;

    private String libDir;

    /**
     * 是否根据git提交操作，自动重新解析登记库（0:不是；1:是;）
     */
    private Boolean autoAnalysisFlag;
}
