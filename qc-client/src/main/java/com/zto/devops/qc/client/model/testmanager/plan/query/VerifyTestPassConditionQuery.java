package com.zto.devops.qc.client.model.testmanager.plan.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.rpc.FlowEventEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class VerifyTestPassConditionQuery extends BaseQuery implements Serializable {
    private static final long serialVersionUID = -8043110289540761109L;

    /**
     * 版本code
     */
    private String versionCode;

    /**
     * 泳道事件
     */
    private FlowEventEnum event;
}
