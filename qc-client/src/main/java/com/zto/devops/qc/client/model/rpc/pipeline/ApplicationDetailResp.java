package com.zto.devops.qc.client.model.rpc.pipeline;

import com.zto.devops.framework.client.simple.User;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ApplicationDetailResp implements Serializable {

    @GatewayModelProperty(description = "编码", required = false)
    private String code;

    @GatewayModelProperty(description = "产品编码", required = false)
    private String productCode;

    /**
     * 产品名称
     */
    @GatewayModelProperty(description = "产品名称", required = false)
    private String productName;


    /**
     * 应用别名
     */
    @GatewayModelProperty(description = "应用别名", required = false)
    private String name;

    /**
     * APPID
     */
    @GatewayModelProperty(description = "APPID", required = false)
    private String appId;

    /**
     * apolloAppId
     */
    @GatewayModelProperty(description = "apolloAppId", required = false)
    private String apolloAppId;


    /**
     * 应用类型id
     */
    @GatewayModelProperty(description = "应用类型code", required = false)
    private String applicationTypeCode;

    /**
     * 应用类型id
     */
    @GatewayModelProperty(description = "应用类型名", required = false)
    private String applicationTypeName;

    /**
     * 应用描述
     */
    @GatewayModelProperty(description = "应用描述", required = false)
    private String description;

    @GatewayModelProperty(description = "源码地址", required = false)
    private String gitProjectUrl;

    @GatewayModelProperty(description = "源码地址id", required = false)
    private Long gitProjectId;

    @GatewayModelProperty(description = "构建参数", required = false)
    private String buildParams;


    @GatewayModelProperty(description = "启动参数", required = false)
    private String deploymentParam;

    @GatewayModelProperty(description = "固定启动参数", required = false)
    private String defaultDeploymentParam;

    @GatewayModelProperty(description = "类型参数", required = false)
    private String typeInput;

    @GatewayModelProperty(description = "基础镜像", required = false)
    private String baseImage;


    @GatewayModelProperty(description = "jvm启动默认参数", required = false)
    private String jvmParam;

    @GatewayModelProperty(description = "第一告警人", required = false)
    private User firstAlerter;
    @GatewayModelProperty(description = "第二告警人", required = false)
    private User secondAlerter;

    @GatewayModelProperty(description = "覆盖率标准值")
    private BigDecimal coverageStandardValue;
    @GatewayModelProperty(description = "是否白名单")
    private Boolean whiteList;
    @GatewayModelProperty(description = "白名单原因", required = false)
    private String whiteListReason;

    @GatewayModelProperty(description = "按钮")
    private List<GeneralVO> backButtonList;

    @GatewayModelProperty(description = "当前用户是否能新增域名", required = false)
    private Boolean canAddDomain;

    @GatewayModelProperty(description = "小程序发布渠道列表", required = false)
    private List<String> appletReleaseChannelList;

    @GatewayModelProperty(description = "小程序测试二维码地址", required = false)
    private String appletFatQrCodeLink;

    @GatewayModelProperty(description = "小程序生产二维码地址", required = false)
    private String appletProQrCodeLink;

    @GatewayModelProperty(description = "是否是核心应用")
    private Boolean coreApplication;

    @GatewayModelProperty(description = "主开发人员", required = false)
    private List<String> mainDevelopers;

    @GatewayModelProperty(description = "主测试人员", required = false)
    private List<String> mainTester;

    @GatewayModelProperty(description = "成员", required = false)
    private List<String> members;
}
