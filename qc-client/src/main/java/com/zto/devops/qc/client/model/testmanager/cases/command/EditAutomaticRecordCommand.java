package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.event.RevisionBusinessName;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EditAutomaticRecordCommand extends AddAutomaticRecordCommand {

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 登记库名称
     */
    @RevisionBusinessName(name = "登记库名称")
    private String name;

    /**
     * 备注
     */
    @RevisionBusinessName(name = "备注")
    private String comment;

    /**
     * jmeter:源地址-OSS上的脚本文件地址;xunit-git地址
     */
    private String address;

    /**
     * 脚本文件名
     */
    @RevisionBusinessName(name = "脚本")
    private String fileName;

    /**
     * 数据文件地址
     */
    private String dataFileAddress;

    /**
     * 扩展jar包地址
     */
    private String extendJarAddress;

    /**
     * 第三方jar包地址
     */
    private String thirdJarAddress;

    private String bucketName;

    private String lastAutomaticSourceLogCode;

    //所属目录code
    private String testcaseCode;

    private String path;

    private String code;

    private AutomaticStatusEnum status;

    @RevisionBusinessName(name = "工作目录")
    private String workSpace;
    @RevisionBusinessName(name = "分支名")
    private String branch;

    @RevisionBusinessName(name = "扫描路径")
    private String scanDirectory;

    private String commitId;

    public EditAutomaticRecordCommand(String aggregateId) {
        super(aggregateId);
    }
}
