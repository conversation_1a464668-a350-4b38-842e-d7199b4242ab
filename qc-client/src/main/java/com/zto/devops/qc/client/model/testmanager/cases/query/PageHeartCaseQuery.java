package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PageHeartCaseQuery extends PageQueryBase {

    @GatewayModelProperty(description = "产品code")
    private String productCode;

    @GatewayModelProperty(description = "名称或code")
    private String codeOrTitle;

    @GatewayModelProperty(description = "运行结果")
    private List<TestPlanCaseStatusEnum> statusList;

    @GatewayModelProperty(description = "AppId")
    private List<String> appIdList;

    @GatewayModelProperty(description = "用例属性")
    private List<AutomaticNodeTypeEnum> nodeTypeList;

    @GatewayModelProperty(description = "更新人")
    private List<Long> modifierList;

}
