package com.zto.devops.qc.client.model.report.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.ObservedEvent;
import com.zto.devops.qc.client.enums.report.*;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 项目名称：qc-parent
 * 类 名 称：AddReportEvent
 * 类 描 述：TODO
 * 创建时间：2021/11/13 10:40 上午
 * 创 建 人：bulecat
 */

@Data
public class ReportAddedEvent extends BaseEvent implements ObservedEvent {


    /**
     * 报告编号
     */
    private String code;

    /**
     * 报告名称
     */
    private String name;

    /**
     * 计划编号
     */
    private String planCode;

    /**
     * 报告类型(准入、准出、简易...)
     */
    private ReportType reportType;

    /**
     * 版本code--(获取计划信息；产品信息、版本信息)
     */
    private String versionCode;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 总体测试结果
     */
    private TestResultEunm testResult;

    /**
     * 计划提测时间
     */
    private Date presentationDate;

    /**
     * 实际提测时间
     */
    private Date actualPresentationDate;

    /**
     * 计划准出时间
     */
    private Date approvalExitDate;

    /**
     * 实际准出时间
     */
    private Date actualApprovalExitDate;

    /**
     * 实际上线时间
     */
    private Date actualPublishDate;

    /**
     * 实际测试开始时间
     */
    private Date actualTestStart;

    /**
     * 实际测试结束时间
     */
    private Date actualTestEnd;

    /**
     * 验收开始时间
     */
    private Date checkStartDate;

    /**
     * 验收结束时间
     */
    private Date checkEndDate;

    /**
     * 更新测试结果时间
     */
    private Date updateTestResultDate;

    /**
     * 自动化测试结果--未执行
     */
    private AutoExecuteResult autoTestResult;


    /**
     * 安全测试人ID
     */
    private Long securityUserId;

    /**
     * 安全测试人名称
     */
    private String securityUserName;

    /**
     * 安全测试结果
     */
    private SecurityTestResult securityTestResult;

    /**
     * 验收类型 外采，内部
     */
    private CheckType checkType;

    /**
     * 开发人数
     */
    private Integer developerCount;

    /**
     * 测试人数
     */
    private Integer testerCount;

    /**
     * 计划冒烟用例数
     */
    private Integer planSmokeCase;

    /**
     * 首次通过冒烟用例数
     */
    private Integer firstPermitSmoke;

    /**
     * 按计划范围上线  1 是, 0 否
     */
    private Integer asPlanedOnline;

    /**
     * 是否延期 1 是, 0 否
     */
    private Integer delay;

    /**
     * 总结、分析、描述
     */
    private String summary;

    /**
     * 状态： 草稿， 已发送 未发送
     */
    private TestPlanStatusEnum status;

    /**
     * 预览邮件
     */
    private String preview;

    //收件人
    private List<SendUserInfoVO> receiveUsers;

    //抄送人
    private List<SendUserInfoVO> ccUsers;

    private List<AttachmentVO> attachments;

    // 代码覆盖率结果,准出、简易报告使用
    private CodeCoverResult codeCoverResult;

    private List<Map<String, String>> codeCoverReason;

}
