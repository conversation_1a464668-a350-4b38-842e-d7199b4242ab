package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticExecuteModeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Data;

import java.util.Date;

@Data
public class AutomaticTask implements Serializable {

    private static final long serialVersionUID = 1L;


    private String code;

    private String taskId;
    private String buildId;
    private String automaticSourceCode;
    private String productCode;
    private String sourceAddress;
    private String filename;
    private AutomaticRecordTypeEnum type;
    private AutomaticExecuteModeEnum executeMode;
    private String env;
    private String executeTag;
    private String content;
    private AutomaticStatusEnum status;
    private String resultFile;
    private String reportFile;
    private String execLogFile;
    private String comment;
    private String testPlanCode;
    private TestPlanStageEnum testStage;
    private Date startTime;
    private Date finishTime;
    private String caseFile;
    private String versionCode;
    private AutomaticTaskTrigModeEnum trigMode;
    private String branchName;
    private String workDir;
    private String commitId;
    private Boolean coverageFlag;
    private Long creatorId;
    private String creator;
    private String errorLogFile;

}
