package com.zto.devops.qc.client.model.rpc.pipeline;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ApplicationResp implements Serializable {
    private static final long serialVersionUID = 2561753535177051580L;

    @GatewayModelProperty(description = "应用code")
    private String code;

    @GatewayModelProperty(description = "应用名称")
    private String name;

    @GatewayModelProperty(description = "应用ID")
    private String appId;

    @GatewayModelProperty(description = "是否是核心应用")
    private Boolean coreApplication;
}
