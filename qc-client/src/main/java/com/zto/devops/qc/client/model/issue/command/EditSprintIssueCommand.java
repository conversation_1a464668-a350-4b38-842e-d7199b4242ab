package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.simple.Sprint;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: minjd
 * @date: Created in 2021/9/17 15:00
 */
@Setter
@Getter
public class EditSprintIssueCommand extends BaseCommand {
    public EditSprintIssueCommand(String aggregateId) {
        super(aggregateId);
    }

    private String code;

    private Sprint sprint;

}
