package com.zto.devops.qc.client.model.issue.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: renxinhui
 * @date: Created in 2023/2/8 17:24
 */
@Data
public class HandlerWaitFixIssueCountVO implements Serializable {

    private String productCode;
//    private String fixVersionCode;
//    private String fixVersionName;
    private Long userId;
    private String userName;
    // 遗留总缺陷(待修复、延期修复、修复中)
    private Integer count;
    // 紧急/高 数量
    private Integer highPriorityCount;
    // 逾期修复数量
    private Integer  overdueNotFixCount;
    // 延期修复数量
    private Integer delayFixCount;

}
