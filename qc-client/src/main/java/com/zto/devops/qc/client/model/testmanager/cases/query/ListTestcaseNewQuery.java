package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.zto.devops.framework.client.query.ExpQueryBase;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ListTestcaseNewQuery extends ExpQueryBase {

    private TestcaseGroupTypeEnum groupType;

    private String productCode;

    private TestcaseTypeEnum type;

    private String parentCode;

    private String codeOrTitle;

    private List<TestcasePriorityEnum> priorityList;

    private List<TestcaseStatusEnum> statusList;

    private List<String> tagList;

    private List<Long> dutyUserList;

    private List<Long> creatorList;

    private Date createTimeBegin;

    private Date createTimeEnd;

    private Date modifyTimeBegin;

    private Date modifyTimeEnd;

    private TestPlanStageEnum testStage;

    private String planCode;

    private Boolean planPattern;

    private String automaticSourceCode;

    private List<AutomaticNodeTypeEnum> nodeTypeList;

    private TestcaseAttributeEnum testcaseAttribute;

    // 用例导出使用
    private String path;

    private Boolean setCore;

    private String versionCode;
}
