package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AutomaticTaskExecutedEvent extends BaseEvent {

    /**
     * 是否场景登记库 【true：是；false：否】
     */
    private Boolean sceneCaseFlag;

    private String code;

    private String taskId;

    private String automaticSourceCode;

    private String sourceAddress;

    private String filename;

    private String env;

    /**
     * 执行空间tag，用于场景登记库执行
     */
    private String tag;

    private List<String> testcaseCodeList;

    private String testPlanCode;

    private TestPlanStageEnum testStage;

    private String versionCode;

    private AutomaticTaskTrigModeEnum trigMode;

    private AutomaticRecordTypeEnum type;

    private String branchName;

    private String workDir;

    private String commitId;

    private Boolean coverageFlag;

    private String schedulerCode;

    private String productCode;

    private String productName;

}
