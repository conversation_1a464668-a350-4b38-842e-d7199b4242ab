package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.enums.DomainEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class AddCommentCommand extends BaseCommand {

    private DomainEnum domain;
    private String businessCode;
    private String code;
    private String content;


    private String repliedCode;
    private String topRepliedCode;
    private Long repliedUserId;
    private String repliedUserName;
    private Date gmtCreate;

    public AddCommentCommand(String aggregateId) {
        super(aggregateId);
    }
}
