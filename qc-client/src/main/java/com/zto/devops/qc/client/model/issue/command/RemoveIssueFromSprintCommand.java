package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Description 从迭代中移除
 * @Date 2022/7/26
 * @Version 1.0
 */
@Getter
@Setter
public class RemoveIssueFromSprintCommand extends BaseCommand {

    private String sprintName;

    private String sprintCode;

    public RemoveIssueFromSprintCommand(String aggregateId) {
        super(aggregateId);
    }
}
