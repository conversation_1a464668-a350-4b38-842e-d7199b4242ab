package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import lombok.Data;

@Data
public class MoveModuleEvent extends BaseEvent {

    private String code;

    private String name;

    private String productCode;

    private String parentCode;

    private String parentName;

    private Integer layer;

    private String oldPath;

    private String newPath;

    private TestcaseTypeEnum type;

    private TestcaseAttributeEnum attribute;

}
