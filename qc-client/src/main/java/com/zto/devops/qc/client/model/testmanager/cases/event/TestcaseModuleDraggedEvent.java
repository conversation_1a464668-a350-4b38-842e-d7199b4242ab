package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.DragModuleActionEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TestcaseModuleDraggedEvent extends BaseEvent {

    private String code;

    private String parentCode;

    private String targetCode;

    private DragModuleActionEnum action;

    private TestcaseTypeEnum type;

    private String productCode;

    private String oldParentCode;

    private String oldPath;

    private String name;
}
