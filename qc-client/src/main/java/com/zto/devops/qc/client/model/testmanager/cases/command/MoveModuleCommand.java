package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MoveModuleCommand extends BaseCommand {

    private String code;

    private String name;

    private String productCode;

    private String parentCode;

    private String parentName;

    private Integer layer;

    private String oldPath;

    private String newPath;

    private TestcaseTypeEnum type;

    private TestcaseAttributeEnum attribute;

    public MoveModuleCommand(String aggregateId) {
        super(aggregateId);
    }


}
