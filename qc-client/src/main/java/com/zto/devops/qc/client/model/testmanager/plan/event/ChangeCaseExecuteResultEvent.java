package com.zto.devops.qc.client.model.testmanager.plan.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Data;

import java.util.List;

@Data
public class ChangeCaseExecuteResultEvent extends BaseEvent {

    private TestPlanCaseStatusEnum executeStatus;

    private List<String> caseIds;

    private TestPlanStageEnum testStage;

    private String planCode;

    private List<String> operateCaseCodeList;
}
