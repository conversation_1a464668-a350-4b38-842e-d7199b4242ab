package com.zto.devops.qc.client.model.rpc.pipeline;

import com.zto.devops.framework.client.event.BaseEvent;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AutomaticTaskCDExecutedEvent extends BaseEvent implements Serializable {

    private String productCode;

    private String taskId;

    private String spaceName;

    private String spaceTag;

    private List<String> versionCodeList;

    private String flowCode;

    /**
     * SMOKE_TEST, FUNCTIONAL_TEST, ALL, RETRY
     */
    private String executeType;

    private String dubboTag;

    private String nodeCode;

    private List<String> taskCodeList;
}
