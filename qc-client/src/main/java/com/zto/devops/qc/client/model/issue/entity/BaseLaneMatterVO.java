package com.zto.devops.qc.client.model.issue.entity;

import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.enums.issue.ColorEnumType;
import com.zto.devops.qc.client.enums.issue.LaneStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/7
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public abstract class BaseLaneMatterVO<C extends LaneStatusMapCondition> extends BaseMatterVO {

    /* 主按钮*/
    private Button mainBtn;

    //逾期描述信息
    private String warn;

    // 逾期描述 颜色
    private ColorEnumType warnColor;

    // 持续时长描述： 4H/5天
    private String duration;

    /*泳道之间跳转的限制和条件*/
    private List<LaneSkipCondition> skipConditions;

    private String productCode;

    @Override
    public void build() {
        super.build();
    }

    public LaneStatusEnum getLane(C condition) {
        LaneStatusEnum lane = doGetLane(condition);
        if (lane == null) {
            throw new ServiceException(domain() + "下的状态【" + statusDesc() + "】未配置泳道映射规则");
        }
        return lane;
    }

    public abstract boolean done();

    /**
     * 获取 lane
     * @return
     */
    protected abstract LaneStatusEnum doGetLane(C condition);
}
