package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.Reason;
import lombok.Data;

@Data
public class TransitionNodeAddedEvent extends BaseEvent {

    private String code;
    private DomainEnum domain;
    private String businessCode;
    private IssueStatus curStatus;
    private String content;
    private IssueStatus nextStatus;
    private Reason reason;
}
