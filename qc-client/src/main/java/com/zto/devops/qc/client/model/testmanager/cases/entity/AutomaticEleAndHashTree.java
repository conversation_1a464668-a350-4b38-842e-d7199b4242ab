package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import lombok.Data;
import org.dom4j.Element;

@Data
public class AutomaticEleAndHashTree implements Serializable {

    private static final long serialVersionUID = 1L;

    AutomaticNodeTypeEnum eleType;
    String name;
    boolean enable;
    Element mainEle;
    Element hashTree;
    AutomaticEleAndHashTree parentEle;
//    List<AutomaticEleAndHashTree> childEleList;
//
//    public boolean childIsEmpty(){
//        if(childEleList ==null){
//            return true;
//        }
//        if(childEleList.size()>0){
//            return false;
//        }else {
//            return true;
//        }
//    }

}
