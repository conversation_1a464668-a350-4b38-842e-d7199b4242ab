package com.zto.devops.qc.client.annotation;

import java.lang.annotation.*;

/**
 * API签名鉴权注解
 * 用于标记需要进行AK/SK签名鉴权的方法
 * 
 * 使用场景：
 * 1. 脚本定时调用的接口
 * 2. 无法获取用户信息的系统间调用
 * 3. IP不固定的调用场景
 * 
 * 鉴权机制：
 * 1. 基于AK/SK + HMAC-SHA256签名
 * 2. 时间戳防重放攻击
 * 3. Nonce防重复请求
 * 
 * <AUTHOR>
 * @since 2025-08-14
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiSignAuth {
    
    /**
     * 是否启用签名鉴权
     * 默认启用，可用于临时关闭鉴权（如调试时）
     */
    boolean enabled() default true;
    
    /**
     * 签名有效期（秒）
     * 默认300秒（5分钟）
     */
    long expireSeconds() default 300;
    
    /**
     * 是否需要验证请求体
     * 默认true，会对请求体进行MD5校验
     */
    boolean validateBody() default true;
    
    /**
     * 允许的时间偏差（秒）
     * 用于处理客户端和服务端时间不同步的情况
     * 默认60秒
     */
    long timeSkewSeconds() default 60;
    
    /**
     * 鉴权失败时的错误信息
     */
    String errorMessage() default "API签名鉴权失败";
}
