package com.zto.devops.qc.client.model.issue.query;

import com.zto.devops.qc.client.enums.issue.IssueTestMethod;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Data
public class IssueNumStatisticQuery implements Serializable {

    private String businessCode;
    /**
     * 匹配的测试方法
     */
    private List<IssueTestMethod> issueTestMethodList;
    /**
     * 过滤的测试方法
     */
    private List<IssueTestMethod> filterIssueTestMethodList;


    /**
     * 初始化测试方法列表
     *
     * @param reportType
     */
    public void initTestMethodList(ReportType reportType) {

        //准出或简易，排除测试方法
        if (ReportType.TEST_PERMIT.equals(reportType)
                || ReportType.SIMPLE_PROCESS.equals(reportType)
                || ReportType.CHECED_TEST.equals(reportType)) {

            List<IssueTestMethod> filterIssueTestMethodList = new ArrayList<>();
            filterIssueTestMethodList.add(IssueTestMethod.MOBILE_LINE_TEST);
            filterIssueTestMethodList.add(IssueTestMethod.INTEGRATION_TEST);
            filterIssueTestMethodList.add(IssueTestMethod.SECURITY_TEST);
            this.setFilterIssueTestMethodList(filterIssueTestMethodList);
        }

        //移动，匹配测试方法
        if (ReportType.SPECIAL_MOBILE.equals(reportType)) {
            List<IssueTestMethod> issueTestMethodList = Collections.singletonList(IssueTestMethod.MOBILE_LINE_TEST);
            this.setIssueTestMethodList(issueTestMethodList);
        }
    }
}
