package com.zto.devops.qc.client.model.report.query;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class ReportDetailBaseQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "当前用户id", required = false)
    private Long currentUserId;
    @GatewayModelProperty(description = "报告code", required = false)
    private String code;
    @GatewayModelProperty(description = "计划code", required = false)
    private String planCode;
}
