package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class RelatedBaseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "报告、计划CODE", required = false)
    private String code;

    @GatewayModelProperty(description = "报告、计划name", required = false)
    private String name;

    @GatewayModelProperty(description = "报告、计划 type", required = false)
    private String type;

    @GatewayModelProperty(description = "版本code", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "产品code", required = false)
    private String productCode;
}
