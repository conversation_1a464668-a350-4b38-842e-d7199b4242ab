package com.zto.devops.qc.client.model.testmanager.plan.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Data;

import java.util.List;

@Data
public class AddCaseInTestPlanEvent extends BaseEvent {

    private String caseCode;

    private String testPlanCode;

    private TestcaseTypeEnum caseType;

    private List<TestPlanStageEnum> testStage;

    private List<String> operateCaseCodeList;
}
