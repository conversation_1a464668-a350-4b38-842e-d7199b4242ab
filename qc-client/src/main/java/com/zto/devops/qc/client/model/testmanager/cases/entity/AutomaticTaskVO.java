package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

@Data
public class AutomaticTaskVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private String schedulerCode;

    private String schedulerName;

    private String code;

    private String taskId;

    private AutomaticStatusEnum status;

    private Integer totalCount;

    private Integer successCount;

    private Integer failedCount;

    private String testPlanCode;

    private String testPlanName;

    private TestPlanStageEnum testStage;

    private Date startTime;

    private Date finishTime;

    private Long executorId;

    private String executor;

    /**
     * 终止人id
     */
    private Long stopUserId;

    /**
     * 终止人
     */
    private String stopUser;

    private String reportFile;

    private String execLogFile;

    private String versionCode;

    private String versionName;

    private AutomaticTaskTrigModeEnum trigMode;

    private String env;

    /**
     * 登记库
     */
    private String automaticRecord;

    /**
     * 用例code
     */
    private String testcaseCode;

    /**
     * 用例执行结果
     */
    private TestPlanCaseStatusEnum testcaseResult;

    private String comment;

    private String errorLogFile;

    private String buildId;

    private String apiCode;

    /**
     * 是否单节口测试
     */
    private Boolean singleTestFlag;

    private AutomaticRecordTypeEnum type;

    public Boolean getSingleTestFlag() {
        return StringUtils.isNotBlank(this.apiCode);
    }
}
