package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordAnalyticMethodEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SubmitAnalysisAutomaticEvent extends BaseEvent implements ActionEvent {

    /**
     * 解析方式
     */
    private AutomaticRecordAnalyticMethodEnum analyticMethod;

    private String code;

    private String automaticSourceLogCode;

    private AutomaticStatusEnum status;

    private String productCode;

    private String fileName;

    private String commitId;

    @Override
    public String action() {
        String prefix = (null != this.analyticMethod && this.analyticMethod.equals(AutomaticRecordAnalyticMethodEnum.AUTO))
                ? AutomaticRecordAnalyticMethodEnum.AUTO.getDesc()
                : StringUtils.EMPTY;
        return prefix + "确认解析结果";
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }
}
