package com.zto.devops.qc.client.model.testmanager.cases.query;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class FindTestcaseModuleByNameQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模块名称
     */
    @GatewayModelProperty(description = "模块名称", required = false)
    private String name;

    @GatewayModelProperty(description = "所属产品code", required = true)
    private String productCode;
}
