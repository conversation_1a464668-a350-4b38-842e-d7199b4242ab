package com.zto.devops.qc.client.model.report.query;

import com.zto.devops.framework.client.query.BaseQuery;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
public class ListReportDateByVersionCodesQuery extends BaseQuery implements Serializable {
    private static final long serialVersionUID = 6868091756888430453L;

    private List<String> codeList;
}
