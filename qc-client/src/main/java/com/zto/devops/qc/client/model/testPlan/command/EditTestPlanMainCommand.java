package com.zto.devops.qc.client.model.testPlan.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.model.report.entity.ExternalTestReportDetailVO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.report.entity.SimpleTestReportVO;
import com.zto.devops.qc.client.model.testPlan.entity.TestFunctionPointVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class EditTestPlanMainCommand extends BaseCommand {
    public EditTestPlanMainCommand(String aggregateId) {
        super(aggregateId);
    }
    private String code;
    private String planName;
    private String productCode;
    private String productName;
    private String type;
    private String planCode;
    private String testPlanCode;
    private String versionCode;
    private String versionName;
    private Integer developerNo;
    private Integer testerNo;
    private Long productDirectorId;
    private String productDirectorName;
    private Boolean mobileSpecialTest;
    private Boolean integrateTest;
    private Boolean securityScan;
    private Boolean staticAnalysis;
    private Boolean performanceTest;
    private Date performanceTestTime;
    private Long performanceTestDirectorId;
    private String performanceTestDirectorName;
    private Boolean exploratoryTest;
    private Long exploratoryTestDirectorId;
    private String exploratoryTestDirectorName;
    private Date exploratoryTestTime;
    /**
     * 测试负责人id
     */
    private Long testDirectorId;

    /**
     * 测试负责人名
     */
    private String testDirectorName;

    private Long staticAnalysisDirectorId;

    private String staticAnalysisDirectorName;

    private Date staticAnalysisTime;
    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date presentationDate;
    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date approvalExitDate;

    @GatewayModelProperty(description = "版本开始时间", required = false)
    private Date startDate;
    @GatewayModelProperty(description = "版本上线时间", required = false)
    private Date publishDate;

    @GatewayModelProperty(description = "计划提测时间-上下午", required = false)
    private String presentationDay;
    @GatewayModelProperty(description = "计划准出时间-上下午", required = false)
    private String approvalExitDay;

    @GatewayModelProperty(description = "版本开始时间-上下午", required = false)
    private String startDay;
    @GatewayModelProperty(description = "版本上线时间-上下午", required = false)
    private String publishDay;
    private TestPlanStatusEnum status;
    private String preview;

    private SimpleTestReportVO simpleTestReportVO;

    private ExternalTestReportDetailVO externalTestReportVO;

    List<TestFunctionPointVO> pointList;

    private String remarks;

    private List<SendUserInfoVO> receiveUsers;

    //抄送人
    private List<SendUserInfoVO> ccUsers;
}
