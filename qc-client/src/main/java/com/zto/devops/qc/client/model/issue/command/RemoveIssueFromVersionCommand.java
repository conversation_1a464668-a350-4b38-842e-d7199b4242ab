package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Description 从规划中移除
 * @Date 2022/7/26
 * @Version 1.0
 */
@Getter
@Setter
public class RemoveIssueFromVersionCommand extends BaseCommand {

    private String versionName;

    private String versionCode;

    public RemoveIssueFromVersionCommand(String aggregateId) {
        super(aggregateId);
    }
}
