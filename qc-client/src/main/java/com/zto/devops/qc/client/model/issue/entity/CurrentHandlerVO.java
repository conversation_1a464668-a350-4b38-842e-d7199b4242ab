package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

import java.util.HashSet;
import java.util.Set;

/**
 * @author: minjd
 * @date: Created in 2021/8/5 15:08
 */
@Data
@Builder
public class CurrentHandlerVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private String type;
    private String businessCode;
    private String userId;
    private String userName;
    private String handleType;
    private String domain;
    private String code;
    private String action;
    private String accessTag;

    public CurrentHandlerVO() {
    }

    public CurrentHandlerVO(String type, String businessCode, String userId, String userName, String handleType, String domain, String code, String action, String accessTag) {
        this.type = type;
        this.businessCode = businessCode;
        this.userId = userId;
        this.userName = userName;
        this.handleType = handleType;
        this.domain = domain;
        this.code = code;
        this.action = action;
        this.accessTag = accessTag;
    }

    public static Set<CurrentHandlerVO> buildSet(CurrentHandlerVO... handlerVOS) {
        Set<CurrentHandlerVO> result = new HashSet<>();
        for (CurrentHandlerVO user : handlerVOS) {
            result.add(user);
        }
        return result;
    }


   /* public static CurrentHandlerVO build(User user, String businessCode, String domain, String code ,String action,String assessTag) {
        CurrentHandlerVO result = new CurrentHandlerVO();
        result.setBusinessCode(businessCode);
        result.setDomain(domain);
        result.setUserId(user.getUserId());
        result.setUserName(user.getUserName());
        result.setType(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name());
        result.setCode(code);
        result.setAction(action);
        result.setAccessTag(assessTag);
        return result;
    }
    public static CurrentHandlerVO build(User user, String businessCode, String domain, String code) {
        CurrentHandlerVO result = new CurrentHandlerVO();
        result.setBusinessCode(businessCode);
        result.setDomain(domain);
        result.setUserId(user.getUserId());
        result.setUserName(user.getUserName());
        result.setType(RelevantUserTypeEnum.CURRENT_HANDLE_USER.name());
        result.setCode(code);
        return result;
    }*/


    /*public static final class CurrentHandlerVOBuilder {
        private String type;
        private String businessCode;
        private Long userId;
        private String userName;
        private String domain;
        private String code;

        private CurrentHandlerVOBuilder() {
        }

        public static CurrentHandlerVOBuilder create() {
            return new CurrentHandlerVOBuilder();
        }

        public CurrentHandlerVOBuilder withType(String type) {
            this.type = type;
            return this;
        }

        public CurrentHandlerVOBuilder withBusinessCode(String businessCode) {
            this.businessCode = businessCode;
            return this;
        }

        public CurrentHandlerVOBuilder withUserId(Long userId) {
            this.userId = userId;
            return this;
        }

        public CurrentHandlerVOBuilder withUserName(String userName) {
            this.userName = userName;
            return this;
        }

        public CurrentHandlerVOBuilder withDomain(String domain) {
            this.domain = domain;
            return this;
        }

        public CurrentHandlerVOBuilder withCode(String code) {
            this.code = code;
            return this;
        }

        public CurrentHandlerVO build() {
            CurrentHandlerVO currentHandlerVO = new CurrentHandlerVO();
            currentHandlerVO.setType(type);
            currentHandlerVO.setBusinessCode(businessCode);
            currentHandlerVO.setUserId(userId);
            currentHandlerVO.setUserName(userName);
            currentHandlerVO.setDomain(domain);
            currentHandlerVO.setCode(code);
            return currentHandlerVO;
        }
    }*/
}
