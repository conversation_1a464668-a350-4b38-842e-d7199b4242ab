package com.zto.devops.qc.client.model.issue.query;

import com.zto.devops.framework.client.query.BaseQuery;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 查询缺陷，并附带操作权限
 * @Date 2022/12/7
 * @Version 1.0
 */
@Data
public class ListIssueForVersionQuery extends BaseQuery implements Serializable {

    // 修复版本 code
    private String fixVersionCode;

    // 发现版本
    private String findVersionCode;

    private List<String> requirementCodeList;

    private String productCode;

    private List<String> tagName;

}
