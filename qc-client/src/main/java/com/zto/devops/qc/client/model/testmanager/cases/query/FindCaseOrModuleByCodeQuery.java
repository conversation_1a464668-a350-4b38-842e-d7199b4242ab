package com.zto.devops.qc.client.model.testmanager.cases.query;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class FindCaseOrModuleByCodeQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 模块名称
     */
    @GatewayModelProperty(description = "模块code", required = false)
    private String code;

    public FindCaseOrModuleByCodeQuery(String code) {
        this.code = code;
    }

}
