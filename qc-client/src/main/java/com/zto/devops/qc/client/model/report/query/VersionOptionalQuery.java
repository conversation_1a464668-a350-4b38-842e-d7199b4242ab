package com.zto.devops.qc.client.model.report.query;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class VersionOptionalQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "产品编号", required = false)
    private String productCode;
    @GatewayModelProperty(description = "版本名称", required = false)
    private String versionName;

}
