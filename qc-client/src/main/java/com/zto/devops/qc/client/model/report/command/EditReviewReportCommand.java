package com.zto.devops.qc.client.model.report.command;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.ReviewInfoVO;
import com.zto.devops.qc.client.model.report.entity.ReviewOpinionVO;
import com.zto.devops.qc.client.model.report.entity.ReviewRenewalVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class EditReviewReportCommand extends BaseReportInfoComnand {

    public EditReviewReportCommand(String aggregateId) {
        super(aggregateId);
    }
    @GatewayModelProperty(description = "评审信息", required = false)
    private ReviewInfoVO reviewInfo;

    @GatewayModelProperty(description = "评审观点", required = false)
    private List<ReviewOpinionVO> reviewOpinions;

    @GatewayModelProperty(description = "评审更新信息", required = false)
    private List<ReviewRenewalVO> reviewRenewals;

    @GatewayModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;

}
