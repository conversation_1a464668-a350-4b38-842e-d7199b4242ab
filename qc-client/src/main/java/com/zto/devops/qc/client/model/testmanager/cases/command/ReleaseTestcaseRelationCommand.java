package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.enums.DomainEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ReleaseTestcaseRelationCommand extends BaseCommand {

    private String businessCode;

    private DomainEnum domain;

    private String operateCaseCode;

    public ReleaseTestcaseRelationCommand(String aggregateId) {
        super(aggregateId);
    }
}
