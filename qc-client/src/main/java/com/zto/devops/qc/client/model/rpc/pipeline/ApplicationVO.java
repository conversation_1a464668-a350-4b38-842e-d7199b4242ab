package com.zto.devops.qc.client.model.rpc.pipeline;

import com.zto.devops.qc.client.enums.rpc.DeployTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date Created in 2021/7/26
 */
@Data
public class ApplicationVO implements Serializable {
    /**
     * 所属产品id
     */
    @GatewayModelProperty(description = "产品编码", required = false)
    private String productCode;


    @GatewayModelProperty(description = "应用编码", required = false)
    private String code;

    /**
     * 应用别名
     */
    @GatewayModelProperty(description = "应用别名", required = false)
    private String name;

    /**
     * APPID
     */
    @GatewayModelProperty(description = "APPID", required = false)
    private String appId;


    /**
     * 应用类型id
     */
    @GatewayModelProperty(description = "应用类型", required = false)
    private String applicationTypeCode;

    /**
     * 应用类型名字
     */
    @GatewayModelProperty(description = "应用类型名字", required = false)
    private String applicationTypeName;

    /**
     * 部署类型
     */
    @GatewayModelProperty(description = "部署类型", required = false)
    private DeployTypeEnum deployType;

    /**
     * 创建时间
     */
    @GatewayModelProperty(description = "创建时间", required = false)
    private String gmtCreate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 第一负责人id
     */
    private Long firstAlerterId;

    /**
     * 第一负责人名称
     */
    private String firstAlerterName;

    /**
     * 第二负责人id
     */
    private Long secondAlerterId;

    /**
     * 第二负责人名称
     */
    private String secondAlerterName;

    private Integer gitProjectId;

    private BigDecimal coverageStandardValue;
    private Boolean whiteList;
    private String whiteListReason;

    /**
     * 更新时间
     */
    @GatewayModelProperty(description = "更新时间", required = false)
    private String gmtModified;
    private String buildType;
    private Long qaxProjectId;
    private List<GeneralVO> backButtonList;
    /**
     * 流水线详情
     */
    private Map<String, Object> typeInput;


    private String productName;
    private String apolloAppId;
    private String typeCode;
    /**
     * 基础镜像
     */
    private String baseImage;
    /**
     * 应用描述
     */
    private String description;
    private Boolean enable;
    private Long modifierId;
    private String modifier;
}
