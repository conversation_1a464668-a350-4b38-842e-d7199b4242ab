package com.zto.devops.qc.client.model.testmanager.plan.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Data;

@Data
public class PlanCaseResultCountQuery extends BaseQuery {

    private String planCode;

    private TestPlanStageEnum testStage;

    /**
     * 已完成阶段，查询已删除用例结果
     */
    private boolean queryDelete;
}
