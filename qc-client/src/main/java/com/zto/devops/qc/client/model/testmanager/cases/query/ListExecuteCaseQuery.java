package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ListExecuteCaseQuery extends BaseQuery {

    private  List<String> executorIdList;

    private String automaticTaskCode;

    private List<TestPlanCaseStatusEnum> status;

    private Date startTimeBegin;

    private Date startTimeEnd;

    private Date finishTimeBegin;

    private Date finishTimeEnd;

}
