package com.zto.devops.qc.client.model.report.command;

import com.zto.devops.qc.client.enums.report.CodeCoverResult;
import com.zto.devops.qc.client.model.report.entity.*;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class AddPermitReportCommand extends BaseReportInfoComnand {


    @GatewayModelProperty(description = "模块测试", required = false)
    private List<ModuleTestVO> moduleTestVOS;

    @GatewayModelProperty(description = "用例测试结果", required = false)
    private List<CaseResultVO> caseResultVOS;

    @GatewayModelProperty(description = "缺陷信息统计", required = false)
    private List<IssueInfoVO> issueInfoVOS;

    @GatewayModelProperty(description = "遗留缺陷", required = false)
    private List<IssueLegacyVO> issueLegacyVOS;

    @GatewayModelProperty(description = "安全漏洞", required = false)
    private List<SecurityHoleVO> securityHoles;

    @GatewayModelProperty(description = "代码覆盖率结果", required = false)
    private CodeCoverResult codeCoverResult;

    @GatewayModelProperty(description = "代码覆盖率不达标原因", required = false)
    private List<Map<String, String>> codeCoverReason;

    public AddPermitReportCommand(String aggregateId) {
        super(aggregateId);
    }
}
