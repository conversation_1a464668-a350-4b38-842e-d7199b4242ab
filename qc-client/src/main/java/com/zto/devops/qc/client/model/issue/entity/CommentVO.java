package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@GatewayModel(description = "实体")
@Data
public class CommentVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "领域")
    private DomainEnum domain;

    @GatewayModelProperty(description = "评论CODE")
    private String code;

    @GatewayModelProperty(description = "业务编码: 业务实例 如 缺陷编码等")
    private String businessCode;

    @GatewayModelProperty(description = "评论内容")
    private String content;


    @GatewayModelProperty(description = "评论人id")
    private Long creatorId;

    @GatewayModelProperty(description = "评论人名称")
    private String creator;

    @GatewayModelProperty(description = "被回复评论的CODE")
    private String repliedCode;

    @GatewayModelProperty(description = "被回复评论的最上层code")
    private String topRepliedCode;

    @GatewayModelProperty(description = "被回复人ID")
    private Long repliedUserId;
    @GatewayModelProperty(description = "被回复人名称")
    private String repliedUserName;


    @GatewayModelProperty(description = "评论的层级")
    private Integer level;

    @GatewayModelProperty(description = "是否删除")
    private Boolean enable;

    @GatewayModelProperty(description = "创建时间")
    private Date gmtCreate;

    @GatewayModelProperty(description = "回复列表")
    List<CommentVO> list;
}
