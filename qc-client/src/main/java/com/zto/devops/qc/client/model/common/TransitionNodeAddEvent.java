package com.zto.devops.qc.client.model.common;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.Reason;

/**
 * 项目名称：qc-parent
 * 类 名 称：TransitionNodeAddedEvent
 * 类 描 述：TODO
 * 创建时间：2021/10/18 3:52 下午
 * 创 建 人：bulecat
 */
public interface TransitionNodeAddEvent {

     String getTransitionNodeCode();

     String getBusinessCode();

     User getOperator();

     DomainEnum domain();

     String actionCode();
     IssueStatus getCurStatus();
     String getContent();
     IssueStatus getNextStatus();
     Reason getReason();
}
