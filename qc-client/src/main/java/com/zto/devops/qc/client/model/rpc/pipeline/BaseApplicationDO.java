package com.zto.devops.qc.client.model.rpc.pipeline;

import lombok.Data;

import java.io.Serializable;

@Data
public class BaseApplicationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 应用编码
     */
    private String code;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * APPID
     */
    private String appId;

    /**
     * apollo app id
     */
    private String apolloAppId;

    /**
     * 关联源码id
     */
    private Long gitProjectId;

    /**
     * 应用类型编码
     */
    private String typeCode;

    /**
     * 应用类型名称
     */
    private String typeName;

    private String deployType;

    /**
     * 项目地址
     */
    private String webUrl;

}
