package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.RevisionEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EditTestcaseTitleEvent extends RevisionEvent {
    private String code;
    private String name;
    private String parentCode;
    private TestcaseTypeEnum type;
    private TestcaseAttributeEnum attribute;
    private TestcasePriorityEnum priority;
    private String productCode;
    private String versionCode;

    @Override
    public String action() {
        return "修改手工用例";
    }
}
