package com.zto.devops.qc.client.model.issue.query;

import com.zto.devops.framework.client.query.ExpQueryBase;
import com.zto.devops.qc.client.enums.issue.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ExpIssueQuery extends ExpQueryBase implements Serializable {

    // 缺陷code集合
    private List<String> issueCodeList;

    // 缺陷名称
    private String codeOrTitle;

    // 当前用户id
    private Long currentUserId;

    // 缺陷状态
    private List<IssueStatus> statusList;

    // 缺陷优先级
    private List<IssuePriority> priorityList;

    // 关于我
    private List<RelatedToMeEnum> relatedList;

    // 当前处理人
    private List<Long> handleUserIdList;

    // 开发人员
    private List<Long> developUserIdList;

    // 测试人员
    private List<Long> testUserIdList;

    // 发现版本
    private List<String> findVersionList;

    // 修复的版本
    private List<String> fixVersionList;

    // 修复或发现的版本
    private List<String> fixOrFindVersionList;

    // 关联需求
    private List<String> relatedRequireList;

    // 关联项目
    private List<String> relatedProductList;

    // 关联迭代code
    private List<String> sprintCode;

    // Bug根源
    private List<IssueRootCause> rootCauseList;

    // Bug类别
    private List<IssueType> issueTypeList;

    // 测试方法
    private List<IssueTestMethod> testMethodList;

    // 重现概率
    private List<IssueRepetitionRate> repetitionRateList;

    // 发现阶段
    private List<IssueFindStage> findStageList;

    // 发现环境
    private List<IssueFindEnv> findEnvList;

    // 报告人
    private List<Long> findUserIdList;

    // 标签
    private List<String> tagName;

    // 创建时间
    private Date createTimeStart;

    // 创建时间
    private Date createTimeEnd;

    // 关闭时间
    private Date closeTimeStart;

    // 关闭时间
    private Date closeTimeEnd;

    // 修复的版本是否为空
    private Boolean fixVersionIsNull;

    // 更新时间-开始
    private Date gmtModifiedStart;

    // 更新时间-结束
    private Date gmtModifiedEnd;

    // before 版本确认之前，after 版本确认之前
    private List<String> versionConfirm;

    // 审查状态
    private List<Boolean> examination;

    // 测试遗漏
    private List<Boolean> testOmission;

    // 代码缺陷
    private List<Boolean> codeDefect;

    // 应用类型
    private List<IssueApplicationType> applicationTypeList;

    // 版本类型
    private String versionType;

    // 拒绝原因集合
    private List<RefuseReason> refuseReasonList;

    // 抄送人id
    private Long CCUserId;

    // 干系人类型
    private RelevantUserTypeEnum type;

    // 排序字段
    private String orderField;

    // 排序方式
    private String orderType;

    // 抄送人id
    private List<Long> ccUserIdList;

    private String groupId;

    // 是否有效缺陷
    private Boolean validFlag;

    private int page;

    private int size;

}
