package com.zto.devops.qc.client.model.report.query;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.report.ReportType;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

//普通草稿报告校验  准入，准出，评审，冒烟，移动，集成
@Data
public class CheckReportDratfQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "报告类型", required = true)
    private ReportType reportType;

    @GatewayModelProperty(description = "计划code", required = true)
    private String planCode;

    @GatewayModelProperty(description = "版本code", required = true)
    private String versionCode;
}
