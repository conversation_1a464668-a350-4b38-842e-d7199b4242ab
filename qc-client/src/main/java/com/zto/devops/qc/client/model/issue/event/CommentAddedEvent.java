package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class CommentAddedEvent extends BaseEvent
        //implements ActionEvent
{

    private String businessCode;

    private Set<CommentVO> commentVOS;

    /*@Override
    public String action() {
        return OperationTypeEnum.ADDCOMMENT.getValue();
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        return "";

    }*/
}
