package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.qc.client.enums.issue.ColorEnumType;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@GatewayModel(description = "版本概览事项")
public class StatisticThingVO implements Serializable {
    private static final long serialVersionUID = 1;

    @ZsmpModelProperty(description = "总数")
    private Integer totalNum;

    @ZsmpModelProperty(description = "未测数")
    private Integer notTestedNum;

    @ZsmpModelProperty(description = "名字")
    private String name;

    @ZsmpModelProperty(description = "颜色")
    private ColorEnumType color;

    @GatewayModelProperty(description = "比例率")
    private String proportion;

    @ZsmpModelProperty(description = "通过率颜色")
    private ColorEnumType proportionColor;

    @ZsmpModelProperty(description = "通过率name")
    private String proportionName;
}
