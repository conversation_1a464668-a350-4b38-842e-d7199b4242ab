package com.zto.devops.qc.client.model.rpc.pipeline;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class VersionContainSubVO implements Serializable {
    /**
     * 流程编码
     */
    private String code;

    /**
     * 流程名称
     */
    private String name;


    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 需求域版本编码，同流程版本编码一一对应
     */
    private String versionCode;


    private String mergeType;
    //子版本
    private List<String> subVersionCodes;
}
