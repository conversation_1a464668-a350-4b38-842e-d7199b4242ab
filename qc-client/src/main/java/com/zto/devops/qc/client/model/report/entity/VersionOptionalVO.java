package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class VersionOptionalVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "版本optional", required = false)
    private List<RelatedBaseVO> versionSelections;
}
