package com.zto.devops.qc.client.model.report.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExcelModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目名称：qc-parent
 * 类 名 称：PlanFunctionPointResp
 * 类 描 述：TODO
 * 创建时间：2021/11/24 2:09 下午
 * 创 建 人：bulecat
 * <AUTHOR>
 */

@Data
public class PlanFunctionPointResp implements Serializable , ExcelModel {

    @GatewayModelProperty(description = "功能测试点", required = false)
    @ExcelProperty("功能测试点")
    private String functionPoint;

    @Override
    public boolean isBeanNone() {
        return StringUtil.isBlank(this.functionPoint);
    }
}
