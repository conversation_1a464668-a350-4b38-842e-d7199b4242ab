package com.zto.devops.qc.client.model.testmanager.cases.query;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import lombok.Data;

import java.util.List;

@Data
public class SimpleTestcaseQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    private String name;

    private List<String> parentCodeList;

    private String productCode;

    private TestcaseAttributeEnum attribute;

    private TestcaseTypeEnum type;

    private String versionCode;
}
