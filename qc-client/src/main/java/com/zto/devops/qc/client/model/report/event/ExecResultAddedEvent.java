package com.zto.devops.qc.client.model.report.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.report.ExecResultBusinessDomain;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

@GatewayModel(description = "执行结果添加")
@Data
public class ExecResultAddedEvent extends BaseEvent {
    private String code;

    private String businessCode;

    private ExecResultBusinessDomain businessDomain;

    private String executeResult;

    private static final long serialVersionUID = 1L;
}