package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Objects;

@GatewayModel(description = "遗留缺陷")
@Data
public class IssueLegacyVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "遗留缺陷code", required = true)
    private String code;

    @GatewayModelProperty(description = "报告code", required = true)
    private String reportCode;

    @GatewayModelProperty(description = "老缺陷code", required = true)
    private String issueCode;

    @GatewayModelProperty(description = "缺陷名称", required = true)
    private String title;

    @GatewayModelProperty(description = "缺陷状态", required = true)
    private IssueStatus status;

    @GatewayModelProperty(description = "缺陷状态描述", required = true)
    private String statusDesc;

    @GatewayModelProperty(description = "开发人ID", required = true)
    private Long developUserId;

    @GatewayModelProperty(description = "开发人名称", required = true)
    private String developUserName;

    @GatewayModelProperty(description = "当前处理人人ID", required = true)
    private Long handleUserId;

    @GatewayModelProperty(description = "当前处理人", required = true)
    private String handleUserName;

    @GatewayModelProperty(description = "优先级", required = true)
    private IssuePriority priority;

    @GatewayModelProperty(description = "优先级描述", required = true)
    private String priorityDesc;

    @GatewayModelProperty(description = "报告人ID", required = true)
    private Long findUserId;

    @GatewayModelProperty(description = "报告人", required = true)
    private String findUserName;

    @GatewayModelProperty(description = "发现版本code", required = true)
    private String findVersionCode;
    @GatewayModelProperty(description = "发现版本", required = true)
    private String findVersionName;

    public void setEnumrationDesc() {
        this.setStatusDesc(Objects.nonNull(this.getStatus()) ? this.getStatus().getValue() : null);
        this.setPriorityDesc(Objects.nonNull(this.getPriority()) ? this.getPriority().getValue() : null);
    }

}