package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ExecuteCallbackCDEvent extends BaseEvent implements Serializable {

    private String taskId;

    /**
     * IN_PROGRESS, SUCCESS, FAIL, TERMINATION
     */
    private AutomaticStatusEnum status;

    private String flowCode;

    private int totalCaseCount;

    private int passedCaseCount;

    private int failedCaseCount;

    private int terminatedCaseCount;

    private int otherCaseCount;

    private String nodeCode;
}
