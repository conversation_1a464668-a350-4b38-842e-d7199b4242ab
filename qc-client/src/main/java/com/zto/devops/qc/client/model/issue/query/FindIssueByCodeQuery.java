package com.zto.devops.qc.client.model.issue.query;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FindIssueByCodeQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "登陆用户ID", required = true)
    private Long currentUserId;
    @GatewayModelProperty(description = "缺陷编码", required = true)
    private String code;

    @GatewayModelProperty(description = "来源 true 为缺陷  false为审查")
    private Boolean issue;

    private List<String> permissions;
}
