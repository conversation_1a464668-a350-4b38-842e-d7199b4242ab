package com.zto.devops.qc.client.model.message;

import com.zto.devops.framework.client.simple.BasePayload;
import lombok.Data;

import java.util.Set;

@Data
public class AutomaticSchedulerMsg extends BasePayload {

    /**
     * 标题
     */
    private String title;

    /**
     * key1
     */
    private String key1;

    /**
     * key2
     */
    private String key2;

    private String key3;

    /**
     * value1
     */
    private String value1;

    /**
     * value2
     */
    private String value2;

    private String value3;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品名称值
     */
    private String productNameValue;

    /**
     * 任务名称
     */
    private String schedulerName;

    /**
     * 任务名称-值
     */
    private String schedulerNameValue;

    /**
     * 运行结果
     */
    private String executeResult;

    /**
     * 运行结果-值
     */
    private String executeResultValue;

    /**
     * 任务创建人
     */
    private String creator;

    /**
     * 任务创建人-值
     */
    private String creatorValue;

    /**
     * 开始运行时间
     */
    private String startTime;

    /**
     * 开始运行时间-值
     */
    private String startTimeValue;

    /**
     * 结束运行时间
     */
    private String endTime;

    /**
     * 开始运行时间-值
     */
    private String endTimeValue;

    /**
     * 跳转链接
     */
    private String actionUrl;

    private String userId;

    /**
     * 消息接收人
     */
    private Set<String> receivedUsers;

    public AutomaticSchedulerMsg() {
        this.key1 =  "任务名称";
        this.key2 = "运行结果";
        this.key3 = "用例执行明细";
        this.productName = "产品名称";
        this.creator = "任务创建人";
        this.startTime = "开始运行时间";
        this.endTime = "结束运行时间";
    }


}
