package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;

@GatewayModel(description = "评审意见")
@Data
public class ReviewOpinionVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "报告code", required = false)
    private String reportCode;

    @GatewayModelProperty(description = "编号", required = false)
    private String serialId;

    @GatewayModelProperty(description = "评审意见code", required = false)
    private String code;

    @GatewayModelProperty(description = "确认点以及修改项", required = false)
    private String description;

    @GatewayModelProperty(description = "责任人ID", required = false)
    private Long ownerUserId;

    @GatewayModelProperty(description = "责任人名字", required = false)
    private String ownerUserName;

    @GatewayModelProperty(description = "截至时间", required = false)
    private Date deadLineDate;

}