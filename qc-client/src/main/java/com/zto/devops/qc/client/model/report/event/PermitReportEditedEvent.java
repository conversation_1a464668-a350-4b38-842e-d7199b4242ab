package com.zto.devops.qc.client.model.report.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.report.entity.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class PermitReportEditedEvent extends ReportAddedEvent implements ActionEvent {

    private List<ModuleTestVO> moduleTestVOS;

    private List<CaseResultVO> caseResultVOS;

    private List<IssueInfoVO> issueInfoVOS;

    private List<IssueLegacyVO> issueLegacyVOS;

    private List<SecurityHoleVO> securityHoles;


    @Override
    public String action() {
        return "";
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        return "";
    }
}
