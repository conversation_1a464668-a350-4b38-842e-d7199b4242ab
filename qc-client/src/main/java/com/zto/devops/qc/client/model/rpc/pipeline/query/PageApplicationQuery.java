package com.zto.devops.qc.client.model.rpc.pipeline.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2021/7/26
 */
@Data
public class PageApplicationQuery extends PageQueryBase implements Serializable  {
    private String productCode;
    private List<String> applicationTypeCode;
    private String name;
    private String appId;
    private List<Long> alerterIds;
    private Boolean needPermissions;
    private List<Boolean> whiteList;
}
