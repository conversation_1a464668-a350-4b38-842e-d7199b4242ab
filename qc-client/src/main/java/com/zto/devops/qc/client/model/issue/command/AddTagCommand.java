package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.enums.issue.TagTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AddTagCommand extends BaseCommand {

    public AddTagCommand(String aggregateId) {
        super(aggregateId);
    }

    private DomainEnum domain;
    private String businessCode;
    private String code;
    private TagTypeEnum type;
    private String tagAlias;
    private String tagCode;
    private String tagName;
}
