package com.zto.devops.qc.client.model.testmanager.email.query;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class TestPlanToSendMailQuery implements Serializable {

    private static final long serialVersionUID = 1L;



    @GatewayModelProperty(description = "关联计划code", required = false)
    private String planCode;

    @GatewayModelProperty(description = "计划类型", required = false)
    private String planType;


}
