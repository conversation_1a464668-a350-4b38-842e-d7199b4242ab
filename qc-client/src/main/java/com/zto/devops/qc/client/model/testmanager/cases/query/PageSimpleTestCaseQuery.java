package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2022/10/27
 * @Version 1.0
 */
@Data
public class PageSimpleTestCaseQuery extends PageQueryBase {

    private String productCode;

    private TestcaseAttributeEnum attribute;

    private TestcaseTypeEnum type;

    private String searchKey;

    private List<TestcaseStatusEnum> status;

    @Override
    public int getPage() {
        return super.getPage() == 0 ? 1 : super.getPage();
    }

    @Override
    public int getSize() {
        return super.getSize() == 0 ? 10 : super.getSize();
    }
}
