package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EditAutomaticRecordLogCommand extends BaseCommand {

    public EditAutomaticRecordLogCommand(String aggregateId) {
        super(aggregateId);
    }

    private List<AddTestcaseCommand> addCaseList;

    private String status;

    private String failInformation;

    private String automaticSourceCode;
}
