package com.zto.devops.qc.client.model.report.command;

import com.zto.devops.qc.client.model.report.entity.CaseDetailVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;


@Setter
@Getter
public class AddOnlineSmokeReportDraftCommand extends BaseReportInfoComnand {


    @GatewayModelProperty(description = "是否按计划范围上线", required = false)
    private Integer asPlanedOnline;

    @GatewayModelProperty(description = "更新测试结果时间", required = false)
    private Date updateTestResultDate;

    @GatewayModelProperty(description = "用例执行明细", required = false)
    private List<CaseDetailVO> caseDetailVOS;

    public AddOnlineSmokeReportDraftCommand(String aggregateId) {
        super(aggregateId);
    }
}
