package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class TestcaseQuery extends BaseQuery {

    private TestcaseGroupTypeEnum groupType;

    private String productCode;

    private TestcaseTypeEnum type;

    private String typeString;

    private String parentCode;

    private String codeOrTitle;

    private List<TestcasePriorityEnum> priorityList;

    private List<TestcaseStatusEnum> statusList;

    private List<String> tagList;

    private List<Long> dutyUserList;

    private List<Long> creatorList;

    private Date createTimeBegin;

    private Date createTimeEnd;

    private Date modifyTimeBegin;

    private Date modifyTimeEnd;

    private TestPlanStageEnum testStage;

    private String testStageString;

    private String planCode;

    private Boolean planPattern;

    private String automaticSourceCode;

    private List<AutomaticNodeTypeEnum> nodeTypeList;

    private TestcaseAttributeEnum testcaseAttribute;

    private String testcaseAttributeString;

    private boolean isFilter;
    // 用例导出使用
    private String path;

    private List<HeartCaseFilterEnum> isHeart;

    private List<Integer> isHeartList;

    private List<AutomaticRecordTypeEnum> automaticTypeList;

    private Boolean filterModule;

    private List<String> filterModuleCodeList;

    private Boolean setCore;

    private String versionCode;

    private String code;
}
