package com.zto.devops.qc.client.model.issue.event;


import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.MyCreatedEvent;
import com.zto.devops.framework.client.simple.*;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.qc.client.model.common.handler.AbstractCurrentHandlerChangedEvent;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class IssueAddedEvent extends AbstractCurrentHandlerChangedEvent implements ActionEvent, MyCreatedEvent {

    private String code;
    private String title;
    private String description;
    private IssueStatus status = IssueStatus.WAIT_FIX;

    private IssueFindEnv findEnv;
    private IssueFindStage findStage;
    private IssuePriority priority;
    private IssueRepetitionRate repetitionRate;
    private IssueRootCause rootCause;
    private IssueTestMethod testMethod;
    private IssueType type;
    private RequirementLevel requirementLevel;

    private User finder;
    private User handler;
    private User developer;
    private User tester;

    private Product product;
    private Requirement requirement;
    private Version findVersion;
    private Sprint sprint;
    private Version fixVersion;

    private Date updateTime;
    private Date findTime;
    private String versionConfirm;

    private List<AttachmentVO> attachments;

    private List<TagVO> tags;

    private IssueApplicationType applicationType;

    private String msgCode;

    @Override
    public String action() {
        return OperationTypeEnum.ADDISSUE.getValue();
    }

    @Override
    public String actionCode() {
        return OperationTypeEnum.ADDISSUE.name();
    }

    @Override
    public String processType() {
        return null;
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        return "";
    }

    @Override
    public String getBusinessCode() {
        return this.code;
    }

    @Override
    public User getOperator() {
        return getTransactor();
    }

    @Override
    public DomainEnum domain() {
        return DomainEnum.ISSUE;
    }



}
