package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;


@Data
public class IssuePreVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "产品编码", required = false)
    private String productCode;

    @GatewayModelProperty(description = "产品名称", required = false)
    private String productName;

    @GatewayModelProperty(description = "发现版本编码", required = false)
    private String findVersionCode;

    @GatewayModelProperty(description = "发现版本名称", required = false)
    private String findVersionName;

    @GatewayModelProperty(description = "开发人员ID", required = false)
    private Long developUserId;

    @GatewayModelProperty(description = "开发人员名称", required = false)
    private String developUserName;

}
