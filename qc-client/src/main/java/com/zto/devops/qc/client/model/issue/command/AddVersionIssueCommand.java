package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.simple.*;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class AddVersionIssueCommand extends BaseCommand {
    private String title;
    private String description;
    private IssueStatus status;

    private IssueFindEnv findEnv = IssueFindEnv.PRODUCT_EVN;
    private IssueFindStage findStage = IssueFindStage.OPERATE_STAGE;
    private IssuePriority priority = IssuePriority.HIGH;
    private IssueRepetitionRate repetitionRate = IssueRepetitionRate.INEVITABLE;
    private IssueRootCause rootCause = IssueRootCause.FUNCTIONAL_DEVELOPMENT_BUG;
    private IssueTestMethod testMethod = IssueTestMethod.FUNCTION_TEST;
    private IssueType type = IssueType.FUNCTION_BUG;
    private RequirementLevel requirementLevel;

    private User finder;
    private User handler;
    private User developer;
    //

    private Product product;
    private Requirement requirement;
    private Version findVersion;
    private Sprint sprint;


    private List<AttachmentVO> attachments;

    private List<TagVO> tags;
    private String versionConfirm;

    // 给外部事件使用
    private Version fixVersion = new Version();
    //给外部事件使用
    private User tester;


    public AddVersionIssueCommand(String aggregateId) {
        super(aggregateId);
    }

}
