package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.RevisionEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EditAutomaticRecordEvent extends RevisionEvent {

    private String code;

    private AutomaticStatusEnum status;

    /**
     * 是否根据git提交操作，自动重新解析登记库（0:不是；1:是;）
     */
    private Boolean autoAnalysisFlag;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 登记库名称
     */
    private String name;

    /**
     * 备注
     */
    private String comment;

    /**
     * jmeter:源地址-OSS上的脚本文件地址;xunit-git地址
     */
    private String address;

    /**
     * 类型：1-jmeter, 2-testng, 3-postman, 4-junit,5-pyunit
     */
    private AutomaticRecordTypeEnum type;

    /**
     * 脚本文件名
     */
    private String fileName;

    /**
     * 数据文件地址
     */
    private String dataFileAddress;

    /**
     * 扩展jar包地址
     */
    private String extendJarAddress;

    /**
     * 第三方jar包地址
     */
    private String thirdJarAddress;

    private String bucketName;

    //所属目录code
    private String testcaseCode;

    private String path;

    private String workSpace;

    private String branch;

    private String scanDirectory;

    private String commitId;

    @Override
    public String action() {
        return "修改登记库";
    }
}
