package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import lombok.Data;

import java.util.Date;

@Data
public class CancelAnalysisAutomaticEvent extends BaseEvent implements ActionEvent {
    private String code;
    private String automaticSourceLogCode;

    private AutomaticStatusEnum status;

    private String productCode;

    @Override
    public String action() {
        return "放弃更新结果";
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }

}
