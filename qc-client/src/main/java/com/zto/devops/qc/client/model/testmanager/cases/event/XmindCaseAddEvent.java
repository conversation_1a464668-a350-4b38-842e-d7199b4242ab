package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class XmindCaseAddEvent extends BaseEvent {

    @ZsmpModelProperty(description = "新增节点类型(用例/模块/步骤)")
    private TestcaseAttributeEnum attribute;

    @ZsmpModelProperty(description = "节点名称")
    private String topic;

    @ZsmpModelProperty(description = "层级")
    private Integer layer;

    @ZsmpModelProperty(description = "上级节点code")
    private String parentCode;

    @ZsmpModelProperty(description = "产品code")
    private String productCode;

    @ZsmpModelProperty(description = "类型")
    private TestcaseTypeEnum type;

    @ZsmpModelProperty(description = "用例等级 HIGH MIDDLE LOW", required = false)
    private TestcasePriorityEnum priority;

}
