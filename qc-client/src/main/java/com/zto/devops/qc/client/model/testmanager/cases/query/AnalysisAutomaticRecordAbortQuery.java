package com.zto.devops.qc.client.model.testmanager.cases.query;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AnalysisAutomaticRecordAbortQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    private Integer recordAbortSecond;

    private List<AutomaticStatusEnum> typeList;
}
