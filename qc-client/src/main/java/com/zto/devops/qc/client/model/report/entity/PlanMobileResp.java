package com.zto.devops.qc.client.model.report.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExcelModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目名称：qc-parent
 * 类 名 称：PlanMobile
 * 类 描 述：TODO
 * 创建时间：2021/11/24 3:11 下午
 * 创 建 人：bulecat
 * <AUTHOR>
 */
@Data
public class PlanMobileResp implements Serializable, ExcelModel {

    /**
     * 测试类型
     */
    @ExcelProperty("测试类型")
    @GatewayModelProperty(description = "测试类型", required = false)
    private String type;

    @ExcelProperty("功能测试点")
    @GatewayModelProperty(description = "功能测试点", required = false)
    private String functionPoint;

    /**
     * 测试负责人/参与人id
     */
    @ExcelProperty("测试负责人/参与人")
    @GatewayModelProperty(description = "测试负责人/参与人", required = false)
    private String directorName;

    @Override
    public boolean isBeanNone() {
        return StringUtil.isBlank(this.type) && StringUtil.isBlank(this.functionPoint) && StringUtil.isBlank(this.directorName);
    }
}
