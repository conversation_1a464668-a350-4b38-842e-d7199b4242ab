package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import lombok.Data;

@Data
public class AnalysisAutomaticRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private String code;

    private String gitUrl;

    private String branchName;

    private String workDir;

    private AutomaticStatusEnum status;

    private String filename;

    private String commitId;

    private String productCode;

    private String testcaseCode;

    private String path;

    private String logCode;

    private Boolean insert;

    private User transactor;

    private String ossPath;

    private String dataPath;

    private String libPath;

    public void setTransactor(User transactor) {
        if (null == transactor) {
            transactor = new User();
        }
        this.transactor = new User(transactor.getUserId(), transactor.getUserName());
    }

}
