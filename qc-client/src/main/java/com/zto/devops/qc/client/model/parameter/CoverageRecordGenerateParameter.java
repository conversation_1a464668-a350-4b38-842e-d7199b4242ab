package com.zto.devops.qc.client.model.parameter;

import java.io.Serializable;

import com.zto.devops.framework.client.entity.BaseEntityDO;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.GenerateTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/10/12 15:31
 */
@ToString
@Data
public class CoverageRecordGenerateParameter extends BaseEntityDO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 产品编号
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 版本编码
     */
    private String versionCode;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 应用名称集
     */
    private List<String> appIdList;

    /**
     * 报告类型(BRANCH-分支,MASTER-主干)
     */
    private RecordTypeEnum recordType;

    /**
     * 生成类型(AUTO-自动生成,MANUAL-手动触发)
     */
    private GenerateTypeEnum generateType;

    /**
     * 差异类型
     */
    private DiffTypeEnum diffType;

    /**
     * 任务编号
     */
    private String taskId;

    /**
     * 空间名称
     */
    private String envName;

    /**
     * 发布泳道
     */
    private String flowLaneType;

}
