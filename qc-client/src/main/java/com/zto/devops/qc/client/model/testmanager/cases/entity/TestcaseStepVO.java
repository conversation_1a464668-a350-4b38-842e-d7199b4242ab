package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

@ZsmpModel(description = "测试步骤模型")
@Data
public class TestcaseStepVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ZsmpModelProperty(description = "用例编号", required = false)
    private String testcaseCode;

    @ZsmpModelProperty(description = "步骤描述", required = true)
    private String stepDesc;

    @ZsmpModelProperty(description = "预期结果", required = true)
    private String expectResult;

    @ZsmpModelProperty(description = "排序")
    private Integer sort;
}
