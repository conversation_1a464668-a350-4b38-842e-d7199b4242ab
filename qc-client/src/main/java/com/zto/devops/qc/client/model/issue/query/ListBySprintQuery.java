package com.zto.devops.qc.client.model.issue.query;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: minjd
 * @date: Created in 2021/9/15 10:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ListBySprintQuery implements Serializable {

    private List<String> productCode;

    private List<String> sprintCode;

    private List<String> code;

    private List<Long> dockingUserIdList;

    public ListBySprintQuery(List<String> productCode, List<String> sprintCode) {
        this.productCode = productCode;
        this.sprintCode = sprintCode;
    }
}
