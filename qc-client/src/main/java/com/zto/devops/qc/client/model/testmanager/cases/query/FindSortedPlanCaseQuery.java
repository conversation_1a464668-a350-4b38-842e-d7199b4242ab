package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class FindSortedPlanCaseQuery extends BaseQuery {

    private String caseCode;

    private String planCode;

    private TestPlanStageEnum testStage;

    private List<TestcasePriorityEnum> priorityList;

    private List<String> tagList;

    private List<TestPlanCaseStatusEnum> statusList;

    private List<Long> executorIdList;

    private String codeOrTitle;

    private Boolean setCore;

    private TestcaseTypeEnum testcaseType;
}
