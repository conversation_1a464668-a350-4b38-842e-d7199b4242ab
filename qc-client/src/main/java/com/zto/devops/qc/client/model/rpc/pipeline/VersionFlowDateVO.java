package com.zto.devops.qc.client.model.rpc.pipeline;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 版本流程时间出参
 * @Author: cher
 * @Date: 2021/10/26 14:37
 **/
@Data
public class VersionFlowDateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实际开发时间：第一次进入开发中的时间
     */
    private Date developDate;

    /**
     * 实际提测时间：第一次进入冒烟中的时间
     */
    private Date submitTestDate;

    /**
     * 实际发布时间：进入已验收时间
     */
    private Date releaseDate;

    /**
     * 准入时间：冒烟通过时间
     */
    private Date testAccessDate;

    /**
     * 准出时间：回归通过时间
     */
    private Date testExitDate;
}
