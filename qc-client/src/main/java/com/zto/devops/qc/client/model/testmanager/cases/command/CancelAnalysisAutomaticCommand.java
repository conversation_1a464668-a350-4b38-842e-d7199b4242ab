package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class CancelAnalysisAutomaticCommand extends BaseCommand implements ActionEvent {
    public CancelAnalysisAutomaticCommand(String aggregateId) {
        super(aggregateId);
    }
    private String code;
    private String automaticSourceLogCode;

    private String productCode;

    @Override
    public String action() {
        return "取消预览提交";
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }


    @Override
    public String makeString() {
        return null;
    }
}
