package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.qc.client.enums.issue.ColorEnumType;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "版本详情-统计测试用例个数")
public class StatisticCaseVO implements Serializable {
    @ZsmpModelProperty(description = "总数")
    private Integer totalNum;

    @ZsmpModelProperty(description = "手工用例比例")
    private Integer manualTestCaseNum;

    @ZsmpModelProperty(description = "手工用例")
    private String manualTestName;

    @ZsmpModelProperty(description = "手工用例比例颜色")
    private ColorEnumType manualTestCaseColor;

    @ZsmpModelProperty(description = "自动化用例比例")
    private Integer autoTestCaseNum;

    @ZsmpModelProperty(description = "自动化用例比例颜色")
    private ColorEnumType autoTestCaseColor;

    @ZsmpModelProperty(description = "自动化用例")
    private String autoTestName;

    @ZsmpModelProperty(description = "手工用例比例")
    private String manualTestCaseStatistic;

    @ZsmpModelProperty(description = "自动化用例比例")
    private String autoTestCaseStatistic;

}
