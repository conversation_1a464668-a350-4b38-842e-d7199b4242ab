package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import lombok.Data;

import java.util.List;

@Data
public class AutomaticNode implements Serializable {

    private static final long serialVersionUID = 1L;

    private String name;
    private String comment;
    private String xpath;
    private String interfaceName;
    private String type;
    private String codeId;
    private Boolean enable;
    private List<AutomaticNode> automaticNodeList;

    public boolean childIsEmpty(){
        if(automaticNodeList ==null){
            return true;
        }
        if(automaticNodeList.size()>0){
            return false;
        }else {
            return true;
        }
    }

    public void reSetChildEnable(){
        if(!childIsEmpty()){
            for(AutomaticNode node:automaticNodeList){
                /**
                 * 父节点为false时，子节点都设置为false*
                 */
                if(!enable) {
                    node.setEnable(enable);
                }
                node.reSetChildEnable();
            }
        }
    }

}
