package com.zto.devops.qc.client.model.testPlan.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TestPlanDataResp implements Serializable {
    private static final long serialVersionUID = 1L;
    //测试计划code
    private String code;
    //测试计划子表code
    private String testPlanMainCode;
    //测试名
    private String planName;
    //产品名称
    private String productName;
    //版本名称
    private String versionName;
    //版本Code
    private String versionCode;
    //权限测试
    private Boolean permissionsTest;
    //优先级
    private String priority;
    // 最晚测试时间
    private Date lastTestDate;
    //测试信息
    private String testInformation;
    //权限测试信息
    private String  priorityTestInformation;
    //产品code
    private String  productCode;

    private Long testerId;

    private String tester;

    private String mailTo;

    private String mailCc;

    private String status;

    private boolean statusChanged;

}
