package com.zto.devops.qc.client.model.issue.entity;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.enums.issue.TagTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class TagVO implements Serializable {
    private static final long serialVersionUID = -8784490561094350632L;

    @GatewayModelProperty(description = "领域",required = false)
    private DomainEnum domain;

    @GatewayModelProperty(description = "业务编码",required = false)
    private String businessCode;

    @GatewayModelProperty(description = "标签编码",required = false)
    private String code;

    @GatewayModelProperty(description = "标签类型",required = false)
    private TagTypeEnum type;
    @GatewayModelProperty(description = "标签别名",required = false)
    private String tagAlias;

    @GatewayModelProperty(description = "标签编码",required = true)
    private String tagCode;

    @GatewayModelProperty(description = "标签名称",required = true)
    private String tagName;

    @GatewayModelProperty(description = "删除",required = false)
    private Boolean enable;

    @GatewayModelProperty(description = "创建人id")
    private Long creatorId;
//    @GatewayModelProperty(description = "创建人id")
//    private Long creatorId;
//    @GatewayModelProperty(description = "创建人")
//    private String creator;
//    @GatewayModelProperty(description = "创建时间")
//    private Date gmtCreate;

}
