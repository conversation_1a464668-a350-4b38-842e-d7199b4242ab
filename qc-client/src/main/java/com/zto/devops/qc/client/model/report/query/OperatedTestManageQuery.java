package com.zto.devops.qc.client.model.report.query;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class OperatedTestManageQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "版本code", required = true)
    private String versionCode;

    @GatewayModelProperty(description = "产品code", required = true)
    private String productCode;

    @GatewayModelProperty(description = "当前用户userId", required = true)
    private Long userId;

}
