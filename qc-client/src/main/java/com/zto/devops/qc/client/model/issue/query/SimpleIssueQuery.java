package com.zto.devops.qc.client.model.issue.query;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：SimpleIssueQuery
 * 类 描 述：TODO
 * 创建时间：2021/9/14 11:00 上午
 * 创 建 人：bulecat
 */
@Data
public class SimpleIssueQuery implements Serializable {

    //"名称"
    private String codeOrTitle;

    //(description = "状态",
    private List<String> status;
    //(description = "当前处理人"
    private List<Long> currentHandler;
    //(description = "优先级"
    private List<String> priority;
    //(description = "创建时间"
    private Date createStart;
    //(description = "创建时间"
    private Date createEnd;

    private List<String> sprintCode;

    private List<String> productCode;
    private List<String> versionCodeList;

    private List<String> findVersionCodeList;

    private List<String> code;

    private List<String> requirementCode;
}
