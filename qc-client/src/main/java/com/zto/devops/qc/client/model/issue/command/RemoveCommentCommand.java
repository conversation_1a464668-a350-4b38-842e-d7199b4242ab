package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.enums.DomainEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RemoveCommentCommand extends BaseCommand {
    private DomainEnum domain;
    private String businessCode;
    private String code;
    public RemoveCommentCommand(String aggregateId) {
        super(aggregateId);
    }
}
