package com.zto.devops.qc.client.model.report.entity;

import com.zto.devops.qc.client.enums.report.CodeCoverResult;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 项目名称：qc-parent
 * 类 名 称：AddOnlineSmokeReportReq
 * 类 描 述：TODO
 * 创建时间：2021/11/10 5:57 下午
 * 创 建 人：bulecat
 */
@Data
public class SimpleTestReportVO extends BaseReportInfoVO {


    // "是否按计划范围上线"
    private Integer asPlanedOnline;

    // "是否延期", required = false)
    private Integer delay;

    // "开发人数", required = false)
    private Integer developerCount;

    // "测试人数", required = false)
    private Integer testerCount;

    //"计划冒烟用例数", required = false)
    private Integer planSmokeCase;

    // "首次冒烟用例数", required = false)
    private Integer firstPermitSmoke;

    // "用例执行结果", required = false)
    private List<CaseResultVO> caseResultVOS;

    // "缺陷信息统计", required = false)
    private List<IssueInfoVO> issueInfoVOS;

    // "遗留缺陷", required = false)
    private List<IssueLegacyVO> issueLegacyVOS;

    // @GatewayModelProperty(description = "代码覆盖率结果", required = false)
    private CodeCoverResult codeCoverResult;

    // @GatewayModelProperty(description = "代码覆盖率不达标原因", required = false)
    private List<Map<String, String>> codeCoverReason;

}
