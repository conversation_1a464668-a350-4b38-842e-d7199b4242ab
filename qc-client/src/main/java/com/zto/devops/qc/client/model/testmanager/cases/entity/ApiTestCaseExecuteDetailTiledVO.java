package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ApiTestCaseExecuteDetailTiledVO implements Serializable {
    private static final long serialVersionUID = -3751756037902380251L;

    /**
     * 自动化任务code
     */
    private String automaticTaskCode;

    /**
     * 接口code
     */
    private String apiCode;

    /**
     * 接口名称
     */
    private String apiName;

    /**
     * 父用例code
     */
    private String parentCode;

    /**
     * 父用例名称
     */
    private String parentName;

    /**
     * 用例code
     */
    private String caseCode;

    /**
     * 名称
     */
    private String caseName;

    /**
     * 用例类型code
     */
    private Integer caseTypeNum;

    /**
     * 用例状态
     */
    private Integer enable;

    /**
     * 结果
     */
    private TestPlanCaseStatusEnum result;

    /**
     * 执行人id
     */
    private Long executorId;

    /**
     * 执行人
     */
    private String executor;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date finishTime;

    /**
     * 报告
     */
    private String reportFile;

    /**
     * 执行日志
     */
    private String execLogFile;
}
