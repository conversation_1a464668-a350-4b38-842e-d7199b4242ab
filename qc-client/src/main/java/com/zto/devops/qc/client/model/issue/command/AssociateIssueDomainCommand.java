package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.enums.DomainEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class AssociateIssueDomainCommand extends IssueBaseCommand{

    private String code;

    private DomainEnum domain;

    private List<String> codeList;

    public AssociateIssueDomainCommand(String aggregateId) {
        super(aggregateId);
    }
}
