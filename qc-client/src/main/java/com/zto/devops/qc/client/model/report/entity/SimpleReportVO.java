package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SimpleReportVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "报告CODE", required = false)
    private String code;

    @GatewayModelProperty(description = "报告name", required = false)
    private String name;

    public SimpleReportVO(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
