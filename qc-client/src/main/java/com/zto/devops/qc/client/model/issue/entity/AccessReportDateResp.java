package com.zto.devops.qc.client.model.issue.entity;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

//准入报告
@Data
public class AccessReportDateResp implements Serializable {
    //版本编号
    private String versionCode;
    //计划名
    private String name;

    //实际提测时间
    private String actualTestStartDate;
    //计划冒烟用例数
    private Integer smokeTestCase;
    //首次通过冒烟用例数
    private Integer smokeTestCasePassed;
    //提测延期天数
    private Double delayForTest;
    //用例总数
    private Integer testCaseCount;
    //准入报告创建人
    private String createUser;
    //准入报告创建时间
    private String createTime;
    //计划提测时段
    private Integer planTestHalf;
    //实际提测时段
    private Integer actualTestHalf;
    //类型
    private Integer type;

    //抄送人
    private List<String> ccUsersEmail;

    private List<String> recipientsEmail;

}
