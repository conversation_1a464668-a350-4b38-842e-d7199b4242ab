package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class EditAutomaticPersonLiableEvent extends BaseEvent implements ActionEvent {
    private String code;

    private Long personLiableId;

    private String personLiable;

    @Override
    public String action() {
        return "修改责任人";
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }


    @Override
    public String makeString() {
        return String.format("修改为 >>> %s", personLiable);
    }
}
