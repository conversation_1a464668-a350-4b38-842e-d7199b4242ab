package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.enums.issue.OperationTypeEnum;
import com.zto.devops.qc.client.enums.issue.RequirementLevel;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class IssueRelatedRequirementEvent extends BaseEvent implements ActionEvent {

    private String requirementCode;
    private String requirementName;
    private RequirementLevel requirementLevel;
    private String issueCode;
    private Version fixVersion;

    @Override
    public String action() {
        return OperationTypeEnum.EDITEDISSUE.getValue();
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        return "";
    }
}
