package com.zto.devops.qc.client.model.report.event;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.report.CaseExecuteResult;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@GatewayModel(description = "用例执行明细")
@Data
public class CaseDetailAddedEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "冒烟执行计划code", required = true)
    private String frogPlanCode;
    @GatewayModelProperty(description = "冒烟执行计划名称", required = true)
    private String frogPlanName;
    @GatewayModelProperty(description = "报告code", required = false)
    private String reportCode;

    @GatewayModelProperty(description = "用例code", required = false)
    private String code;

    @GatewayModelProperty(description = "用例编号", required = false)
    private String frogSerialId;

    @GatewayModelProperty(description = "名称", required = false)
    private String name;

    @GatewayModelProperty(description = "执行结果 未执行；通过；失败；阻塞；跳过", required = false)
    private CaseExecuteResult executeResult;

}