package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.titans.common.util.StringUtil;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@ZsmpModel(description = "自动化任务执行记录子任务")
public class AutomaticChildTaskVO implements Serializable {
    private static final long serialVersionUID = 1350992057620527904L;

    @ZsmpModelProperty(description = "自动化任务类型")
    private AutomaticRecordTypeEnum type;

    @ZsmpModelProperty(description = "是否单接口测试")
    private Boolean singleTestFlag;

    @ZsmpModelProperty(description = "任务code集合（针对单接口）")
    private Set<String> taskCodeList;

    @ZsmpModelProperty(description = "接口code")
    private String apiCode;

    @ZsmpModelProperty(description = "定时任务code")
    private String schedulerCode;

    @ZsmpModelProperty(description = "定时任务name")
    private String schedulerName;

    @ZsmpModelProperty(description = "是否是父任务（true：是;false：否）")
    private Boolean parentFlag;

    @ZsmpModelProperty(description = "自动化任务code")
    private String code;

    @ZsmpModelProperty(description = "自动化任务id")
    private String taskId;

    @ZsmpModelProperty(description = "登记库")
    private String automaticRecord;

    @ZsmpModelProperty(description = "自动化任务结果")
    private AutomaticStatusEnum status;

    @ZsmpModelProperty(description = "自动化任务结果描述")
    private String statusDesc;

    @ZsmpModelProperty(description = "用例总数")
    private Integer totalCount;

    @ZsmpModelProperty(description = "用例执行结果VOS")
    private List<CountCaseStatusVO> caseStatusVOS;

    @ZsmpModelProperty(description = "测试计划编码")
    private String testPlanCode;

    @ZsmpModelProperty(description = "测试计划名称")
    private String testPlanName;

    @ZsmpModelProperty(description = "测试阶段")
    private TestPlanStageEnum testStage;

    @ZsmpModelProperty(description = "测试阶段描述")
    private String testStageDesc;

    @ZsmpModelProperty(description = "开始时间")
    private Date startTime;

    @ZsmpModelProperty(description = "结束时间")
    private Date finishTime;

    @ZsmpModelProperty(description = "执行人id")
    private Long executorId;

    @ZsmpModelProperty(description = "执行人")
    private String executor;

    @ZsmpModelProperty(description = "终止人id")
    private Long stopUserId;

    @ZsmpModelProperty(description = "终止人")
    private String stopUser;

    @ZsmpModelProperty(description = "是否自动终止")
    private Boolean autoStopFlag;

    @ZsmpModelProperty(description = "自动终止原因")
    private String autoStopReason;

    @ZsmpModelProperty(description = "执行空间")
    private String env;

    public Boolean getAutoStopFlag() {
        return StringUtil.isBlank(this.stopUser) ? null :
                (this.stopUser.equalsIgnoreCase("abort") ||
                        this.stopUser.equalsIgnoreCase("cookieFailAbort"));
    }

    public String getAutoStopReason() {
        Boolean autoStopFlag = getAutoStopFlag();
        if (autoStopFlag == null) {
            return null;
        }
        if (autoStopFlag) {
            return this.stopUser.equalsIgnoreCase("abort") ?
                    "超过一小时未执行，系统自动终止" : "cookie获取失败，系统自动终止，请联系管理员";
        }
        return null;
    }

    @ZsmpModelProperty(description = "报告")
    private String reportFile;

    @ZsmpModelProperty(description = "执行日志")
    private String execLogFile;

    @ZsmpModelProperty(description = "版本code")
    private String versionCode;

    @ZsmpModelProperty(description = "版本名称")
    private String versionName;

    @ZsmpModelProperty(description = "触发方式")
    private AutomaticTaskTrigModeEnum trigMode;

    @ZsmpModelProperty(description = "触发方式描述")
    private String trigModeDesc;

    @ZsmpModelProperty(description = "备注失败原因")
    private String comment;

    @ZsmpModelProperty(description = "错误日志地址")
    private String errorLogFile;

    @ZsmpModelProperty(description = "Jenkins构建id")
    private String buildId;

    public String getStatusDesc() {
        this.statusDesc = null != this.status ?
                this.status.equals(AutomaticStatusEnum.NOT_STARTED)
                        ? "排队中"
                        : this.status.getDesc()
                : "";
        return this.statusDesc;
    }

    public void setTestStage(TestPlanStageEnum testStage) {
        this.testStage = testStage;
        if (null != testStage) {
            this.testStageDesc = testStage.getValue();
        }
    }

    public void setTrigMode(AutomaticTaskTrigModeEnum trigMode) {
        this.trigMode = trigMode;
        if (null != trigMode) {
            this.trigModeDesc = trigMode.getDesc();
        }
    }

    /**
     * 参数组装
     *
     * @param voList
     * @return
     */
    public static AutomaticChildTaskVO buildSelf(List<AutomaticTaskVO> voList) {
        AutomaticTaskVO automaticTaskVO = voList.get(0);
        AutomaticChildTaskVO result = new AutomaticChildTaskVO();
        BeanUtils.copyProperties(automaticTaskVO, result);
        result.setParentFlag(false);
        result.setCaseStatusVOS(buildTestCaseStatusVOS(voList));
        result.setTotalCount(voList.size());
        result.setApiCode(automaticTaskVO.getApiCode());
        result.setTaskCodeList(voList.stream().map(AutomaticTaskVO::getCode).collect(Collectors.toSet()));
        return result;
    }

    /**
     * 组装用例执行结果
     *
     * @param voList {@link AutomaticTaskVO}
     * @return {@link CountCaseStatusVO}
     */
    public static List<CountCaseStatusVO> buildTestCaseStatusVOS(List<AutomaticTaskVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return new ArrayList<>();
        }

        //过滤没有用例执行记录情况
        voList = voList.stream().filter(vo -> (null != vo.getTestcaseResult())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(voList)) {
            return new ArrayList<>();
        }

        //根据状态分组统计
        List<CountCaseStatusVO> resultList = new ArrayList<>();
        Map<TestPlanCaseStatusEnum, List<AutomaticTaskVO>> caseStatusMap = voList.stream()
                .collect(Collectors.groupingBy(AutomaticTaskVO::getTestcaseResult));
        caseStatusMap.forEach((k, v) -> {
            resultList.add(CountCaseStatusVO.buildSelf(k, v));
        });
        return resultList;
    }
}
