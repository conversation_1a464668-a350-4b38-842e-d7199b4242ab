package com.zto.devops.qc.client.model.report.query;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * report query
 *
 * <AUTHOR>
 * @date 2023-03-30 14:08
 */
@Data
public class ReportTestResultQuery implements Serializable {

    @ZModelProperty(description = "版本code", required = true, sample = "['VER24041600205']")
    private List<String> versionCodeList;

}
