package com.zto.devops.qc.client.model.testmanager.plan.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ChangePlanCaseResultCommentCommand extends BaseCommand {

    private TestPlanStageEnum testStage;

    private String caseCode;

    private String resultComment;

    private String operateCaseCode;

    public ChangePlanCaseResultCommentCommand(String aggregateId) {
        super(aggregateId);
    }
}
