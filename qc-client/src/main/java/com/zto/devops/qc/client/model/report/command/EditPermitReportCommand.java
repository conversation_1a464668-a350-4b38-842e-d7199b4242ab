package com.zto.devops.qc.client.model.report.command;

import com.zto.devops.qc.client.model.report.entity.CaseDetailVO;
import com.zto.devops.qc.client.model.report.entity.CaseStatisticsVO;
import com.zto.devops.qc.client.model.report.entity.IssueInfoVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class EditPermitReportCommand extends BaseReportInfoComnand {

    public EditPermitReportCommand(String aggregateId) {
        super(aggregateId);
    }

    @GatewayModelProperty(description = "用例执行明细", required = false)
    private List<CaseDetailVO> caseDetailVOS;

    @GatewayModelProperty(description = "用例信息统计", required = false)
    private List<CaseStatisticsVO> caseStatisticsVOS;

    @GatewayModelProperty(description = "缺陷信息统计", required = false)
    private List<IssueInfoVO> issueInfoVOS;

}
