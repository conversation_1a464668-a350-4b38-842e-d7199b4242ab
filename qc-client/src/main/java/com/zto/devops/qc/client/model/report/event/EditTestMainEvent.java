package com.zto.devops.qc.client.model.report.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.ObservedEvent;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class EditTestMainEvent extends BaseEvent implements ObservedEvent {
    private String code;
    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date presentationDate;
    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date approvalExitDate;

    @GatewayModelProperty(description = "版本开始时间", required = false)
    private Date startDate;
    @GatewayModelProperty(description = "版本上线时间", required = false)
    private Date publishDate;

    @GatewayModelProperty(description = "计划提测时间-上下午", required = false)
    private String presentationDay;
    @GatewayModelProperty(description = "计划准出时间-上下午", required = false)
    private String approvalExitDay;

    @GatewayModelProperty(description = "版本开始时间-上下午", required = false)
    private String startDay;
    @GatewayModelProperty(description = "版本上线时间-上下午", required = false)
    private String publishDay;
}
