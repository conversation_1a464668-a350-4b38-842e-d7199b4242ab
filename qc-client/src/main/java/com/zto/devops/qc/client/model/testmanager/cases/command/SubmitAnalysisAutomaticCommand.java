package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordAnalyticMethodEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SubmitAnalysisAutomaticCommand extends BaseCommand {

    public SubmitAnalysisAutomaticCommand(String aggregateId) {
        super(aggregateId);
    }

    private String code;

    private String automaticSourceLogCode;

    private String fileName;

    private String commitId;

    private String address;

    private AutomaticRecordAnalyticMethodEnum analyticMethod;
}
