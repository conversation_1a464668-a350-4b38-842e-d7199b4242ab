package com.zto.devops.qc.client.model.testmanager.plan.query;

import com.zto.devops.framework.client.query.ExpQueryBase;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStrategyEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@EqualsAndHashCode(callSuper = true)
@Data
public class PlanListQuery extends ExpQueryBase implements Serializable {
    private static final long serialVersionUID = 1677991589342080974L;

    /**
     * 测试策略
     */
    private List<TestPlanStrategyEnum> testStrategyList;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划状态
     */
    private List<String> status;

    /**
     * 计划类型
     */
    private List<String> type;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 计划提测开始时间
     */
    private Date accessTimeStart;

    /**
     * 计划提测结束时间
     */
    private Date accessTimeEnd;

    /**
     * 计划准出开始时间
     */
    private Date permitTimeStart;

    /**
     * 计划准出结束时间
     */
    private Date permitTimeEnd;

    /**
     * 计划负责人id
     */
    private List<String> planDirectorId;

    /**
     * 创建开始时间
     */
    private Date createTimeStart;

    /**
     * 创建结束时间
     */
    private Date createTimeEnd;

    /**
     * 更新开始时间
     */
    private Date updateTimeStart;

    /**
     * 更新结束时间
     */
    private Date updateTimeEnd;

    /**
     * 更新人编码
     */
    private List<Long> modifierId;

    private List<String> productCodes;

    private String versionCode;

    private String orderField;

    private String orderType;

    public int getSize() {
        return 0 == super.getSize() ? 50 : super.getSize();
    }

}
