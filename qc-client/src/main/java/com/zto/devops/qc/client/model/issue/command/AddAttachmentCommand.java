package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentDocumentTypeEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class AddAttachmentCommand extends BaseCommand {
    public AddAttachmentCommand(String aggregateId) {
        super(aggregateId);
    }

    private DomainEnum domain;

    private String businessCode;

    private String code;

    private String url;

    private String name;

    private String remoteFileId;

    private AttachmentTypeEnum type;

    private AttachmentDocumentTypeEnum documentType;

}