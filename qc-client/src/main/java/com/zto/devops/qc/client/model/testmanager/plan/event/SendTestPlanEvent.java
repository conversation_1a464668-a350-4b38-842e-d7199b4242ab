package com.zto.devops.qc.client.model.testmanager.plan.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SendTestPlanEvent extends BaseEvent implements ActionEvent {

    @GatewayModelProperty(description = "邮件code", required = false)
    private String emailCode;

    @GatewayModelProperty(description = "邮件名称", required = false)
    private String name;

    @GatewayModelProperty(description = "邮件类型", required = false)
    //todo 改为enum
    private String emailType;

    @GatewayModelProperty(description = "报告/计划code", required = false)
    private String businessCode;

    @GatewayModelProperty(description = "报告/计划名称", required = false)
    private String businessName;

    @GatewayModelProperty(description = "产品code", required = false)
    private String productCode;

    @GatewayModelProperty(description = "产品名称", required = false)
    private String productName;

    @GatewayModelProperty(description = "关联计划code", required = false)
    private String relationPlanCode;

    @GatewayModelProperty(description = "关联计划名称", required = false)
    private String relationPlanName;

    @GatewayModelProperty(description = "版本code", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "版本名称", required = false)
    private String versionName;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date accessDate;

    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date permitDate;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<SendUserInfoVO> receiveUsers;

    @GatewayModelProperty(description = "抄送人", required = false)
    private List<SendUserInfoVO> ccUsers;

    @GatewayModelProperty(description = "预览html", required = false)
    private String preview;

    @GatewayModelProperty(description = "发送时间", required = false)
    private Date sendDate;

    @GatewayModelProperty(description = "发送人id", required = false)
    private Long senderId;

    @GatewayModelProperty(description = "发送人", required = false)
    private String sender;

    @Override
    public String action() {
        return "发送"+this.getName();
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }
}
