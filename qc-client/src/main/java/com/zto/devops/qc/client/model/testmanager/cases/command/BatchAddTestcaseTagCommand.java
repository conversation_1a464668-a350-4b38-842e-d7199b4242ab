package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.enums.issue.TagTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class BatchAddTestcaseTagCommand extends BaseCommand {

    private List<String> businessCodes;

    private List<String> tagNames;

    private DomainEnum domain;

    private TagTypeEnum type;

    public BatchAddTestcaseTagCommand(String aggregateId) {
        super(aggregateId);
    }
}
