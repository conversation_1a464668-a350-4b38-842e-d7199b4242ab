package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@GatewayModel(description = "评审更新信息")
@Data
public class ReviewRenewalVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "报告code", required = false)
    private String reportCode;

    @GatewayModelProperty(description = "编号", required = false)
    private String serialId;

    @GatewayModelProperty(description = "code", required = false)
    private String code;

    @GatewayModelProperty(description = "评审前信息", required = false)
    private String reviewBefore;

    @GatewayModelProperty(description = "评审后信息", required = false)
    private String reviewAfter;

    @GatewayModelProperty(description = "更新内容", required = false)
    private String renewalContent;

}