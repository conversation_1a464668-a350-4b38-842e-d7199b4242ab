package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.qc.client.enums.issue.ColorEnumType;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "测试覆盖率")
public class CoverageRateVO implements Serializable {
    @ZsmpModelProperty(description = "名字")
    private String name;

    @GatewayModelProperty(description = "覆盖率")
    private String coverageRate;

    @ZsmpModelProperty(description = "颜色")
    private ColorEnumType color;

}
