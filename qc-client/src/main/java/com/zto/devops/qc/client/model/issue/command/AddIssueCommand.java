package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.simple.*;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.RelevantUserVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Set;


@Setter
@Getter
public class AddIssueCommand extends BaseCommand {
    private String title;
    private String description;
    private IssueStatus status;

    private IssueFindEnv findEnv;
    private IssueFindStage findStage;
    private IssuePriority priority;
    private IssueRepetitionRate repetitionRate;
    private IssueRootCause rootCause;
    private IssueTestMethod testMethod;
    private IssueType type;
    private RequirementLevel requirementLevel;

    private User finder;
    private User handler;
    private User developer;

    private Product product;
    private Requirement requirement;
    private Version findVersion;
    private Sprint sprint;


    private List<AttachmentVO> attachments;

    private List<TagVO> tags;
    private String versionConfirm;

    // 给外部事件使用
    private Version fixVersion = new Version();
    //给外部事件使用
    private User tester;

    private IssueApplicationType applicationType;

    private Set<RelevantUserVO> relevantUserVOS;

    private List<String> testcaseCodes;
//    private List<TestcaseRelationVO> testcaseRelationVOS;

    private String msgCode;

    public AddIssueCommand(String aggregateId) {
        super(aggregateId);
    }

    private String productCode;

    private String productName;

    private Long testUserId;

    private String testUserName;

    private Long startAt;

    private String location;

    private String appId;

    private Boolean uploadDescFlag;

}
