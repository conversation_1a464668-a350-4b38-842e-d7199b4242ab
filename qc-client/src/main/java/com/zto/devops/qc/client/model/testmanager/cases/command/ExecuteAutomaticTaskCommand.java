package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ExecuteAutomaticTaskCommand extends BaseCommand {

    private String taskId;

    private String automaticSourceCode;

    private String env;

    private String tag;

    private List<String> testcaseCodeList;

    private String testPlanCode;

    private TestPlanStageEnum testStage;

    private String versionCode;

    private AutomaticTaskTrigModeEnum trigMode;

    private Boolean coverageFlag;

    private String schedulerCode;

    private String productCode;

    public ExecuteAutomaticTaskCommand(String aggregateId) {
        super(aggregateId);
    }
}
