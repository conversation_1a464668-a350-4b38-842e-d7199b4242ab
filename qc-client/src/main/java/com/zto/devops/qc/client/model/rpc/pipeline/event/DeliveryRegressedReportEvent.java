package com.zto.devops.qc.client.model.rpc.pipeline.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.rpc.FlowStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 回归报告事件
 * @Date 2021/9/7
 * @Version 1.0
 */
@Setter
@Getter
public class DeliveryRegressedReportEvent extends FlowChangeStatusEvent implements ActionEvent {

    /**
     * 回归不通过，状态退到指定状态
     */
    FlowStatusEnum backToStatus;

    /**
     * 版本code
     */
    List<String> flowCodes;

    /**
     * 回归结果
     */
    boolean result;

    /**
     * 回归不通过的流程关联的所有应用编码
     */
    private Set<String> applicationCodes;

    @Override
    public String action() {
        if (Objects.nonNull(getFlowEvent())) {
            return getFlowEvent().getValue();
        }
        return null;
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return getOccurred();
    }

    @Override
    public String makeString() {
        return "";
    }

}
