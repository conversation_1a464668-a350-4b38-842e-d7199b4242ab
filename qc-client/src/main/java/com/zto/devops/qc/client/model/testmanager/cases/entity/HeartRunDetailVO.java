package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class HeartRunDetailVO implements Serializable {

    @ZsmpModelProperty(description = "用例名称")
    private String caseName;

    @ZsmpModelProperty(description = "用例编号")
    private String caseCode;

    @ZsmpModelProperty(description = "运行结果")
    private TestPlanCaseStatusEnum status;

    @ZsmpModelProperty(description = "运行结果描述")
    private String statusDesc;

    @ZsmpModelProperty(description = "开始时间")
    private Date startTime;

    @ZsmpModelProperty(description = "结束时间")
    private Date finishTime;

    @ZsmpModelProperty(description = "报告")
    private String reportFile;

    @ZsmpModelProperty(description = "执行日志")
    private String execLogFile;

}
