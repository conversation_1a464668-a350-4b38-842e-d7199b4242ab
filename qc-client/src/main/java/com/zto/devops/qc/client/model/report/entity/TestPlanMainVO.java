package com.zto.devops.qc.client.model.report.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
@Data
public class TestPlanMainVO implements Serializable {
    private String code;
    private String testPlanCode;
    private String versionCode;
    private String versionName;
    private Integer developerNo;
    private Integer testerNo;
    private Long productDirectorId;
    private String productDirectorName;
    private Date versionStartTime;
    private Date planOnlineTime;
    private Boolean mobileSpecialTest;
    private Boolean integrateTest;
    private Boolean securityScan;
    private Boolean staticAnalysis;
    private Boolean performanceTest;
    private Date performanceTestTime;
    private Long performanceTestDirectorId;
    private String performanceTestDirectorName;
    private Boolean exploratoryTest;
    private Long exploratoryTestDirectorId;
    private String exploratoryTestDirectorName;
    private Long creatorId;
    private String creator;
    private Date gmtCreate;
    private Long modifierId;
    private String modifier;
    private Date gmtModified;
    private Boolean enable;
    private Date exploratoryTestTime;

    private Long staticAnalysisDirectorId;

    private String staticAnalysisDirectorName;

    private Date staticAnalysisTime;

}