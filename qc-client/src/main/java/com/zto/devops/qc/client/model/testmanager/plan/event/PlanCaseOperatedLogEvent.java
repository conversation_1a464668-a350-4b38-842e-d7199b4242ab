package com.zto.devops.qc.client.model.testmanager.plan.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class PlanCaseOperatedLogEvent extends BaseEvent implements ActionEvent {

    private String code;

    private String action;

    private String makeString;

    @Override
    public String action() {
        return this.action;
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return this.makeString;
    }

    @Override
    public String logBusinessCode() {
        return this.code;
    }
}
