package com.zto.devops.qc.client.model.testmanager.plan.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import java.util.Date;
import lombok.Data;

@Data
public class RelatedPlanVO extends RelatedPlanReportBaseVO{

    @GatewayModelProperty(description = "计划code", required = false)
    private String planCode;

    @GatewayModelProperty(description = "计划type", required = false)
    private String planType;

    @GatewayModelProperty(description = "计划type中文", required = false)
    private String planTypeDesc;

    @GatewayModelProperty(description = "计划名称", required = false)
    private String planName;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date accessDate;

    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date permitDate;

}
