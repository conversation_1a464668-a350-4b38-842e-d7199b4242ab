package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordAnalyticMethodEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AddAutomaticRecordLogCommand extends AddAutomaticRecordCommand {

    public AddAutomaticRecordLogCommand(String aggregateId) {
        super(aggregateId);
    }

    /**
     * 解析方式
     */
    private AutomaticRecordAnalyticMethodEnum analyticMethod;

    private List<AddTestcaseCommand> addCaseList;

    private String automaticSourceCode;

    private String status;

    private String failInformation;
}
