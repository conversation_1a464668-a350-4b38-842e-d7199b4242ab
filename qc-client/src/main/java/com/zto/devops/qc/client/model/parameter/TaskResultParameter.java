package com.zto.devops.qc.client.model.parameter;

import java.io.Serializable;

import com.zto.devops.framework.client.simple.Button;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @author: minjd
 * @date: Created in 2021/7/21 16:00
 */
@Data
@EqualsAndHashCode(of = "code")
public class TaskResultParameter implements Serializable {

    private static final long serialVersionUID = 1L;

    private String status;

    private String name;

    private String priority;

    private int priorityIndex;

    private String relevantUserType;

    private String warn;

    private int warnDay;

    private String productCode;

    private String productName;


    // 关联版本
    private String findVersionCode;

    private String findVersionName;

    //
    private Long developUserId;

    private String developUserName;

    private Long testUserId;

    private String testUserName;
    //业务code
    private String businessCode;

    // 当前处理人
    private Long handleUserId;
    private String handleUserName;
    // 报告人
    private Long findUserId;

    private List<Button> buttonvos;

    private Date gmtCreate;
}
