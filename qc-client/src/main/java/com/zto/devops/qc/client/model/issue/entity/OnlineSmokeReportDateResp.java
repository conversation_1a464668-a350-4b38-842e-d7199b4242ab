package com.zto.devops.qc.client.model.issue.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

//冒烟报告
@Data
public class OnlineSmokeReportDateResp implements Serializable {
    //版本编号
    private String versionCode;
    //计划名
    private String name;
    //验证测试结果时间
    private String checkResultDate;
    //实际上线时间
    private String actualVersionEndDate;
    //测试结果
    private String testResult;
    //冒烟报告创建人
    private String createUser;
    //冒烟报告创建时间
    private String createTime;
    //是否延期
    private Integer delayFlag;
    //是否按计划范围上线
    private Integer planFlag;
    //类型
    private Integer type;

    //抄送人
    private List<String> ccUsersEmail;

    private List<String> recipientsEmail;

}
