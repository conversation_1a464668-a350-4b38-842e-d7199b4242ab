package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.MyOperatedEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.OperationTypeEnum;
import com.zto.devops.qc.client.enums.issue.Reason;
import com.zto.devops.qc.client.model.common.TransitionNodeAddEvent;
import com.zto.devops.qc.client.model.common.handler.AbstractCurrentHandlerChangedEvent;
import lombok.Data;

import java.util.Date;

@Data
public class IssueCirculationedEvent extends AbstractCurrentHandlerChangedEvent implements ActionEvent, MyOperatedEvent, TransitionNodeAddEvent {

    private String code;
    private String businessCode;
    private IssueStatus status = IssueStatus.FIXING;
    private IssueStatus curStatus;
    private IssueStatus nextStatus;
    private String content;
    private User handler;
    private Long developId;
    private String developName;
    private Long testId;
    private String testName;
    private Long findUserId;
    private String findUserName;
    private Reason reason;
    private String transitionNodeCode;

    private String originTestName;
    private String originDevelopName;


    @Override
    public String action() {
        return OperationTypeEnum.CIRCULATION.getValue();
    }
    @Override
    public String actionCode() {
        return OperationTypeEnum.CIRCULATION.name();
    }

    @Override
    public String processType() {
        return null;
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        String ctx = "";
        if (developName != null && !developName.equals(originDevelopName)) {
            ctx = ctx + "开发人员：" + originDevelopName + "->" + developName;
            ctx = breakLine(ctx);
        }
        if (testName != null && !testName.equals(originTestName)) {
            ctx = ctx + "测试人员：" + originTestName + "->" + testName;
            ctx = breakLine(ctx);
        }
        return appendRemark(ctx, content);
    }

    @Override
    public User getOperator() {
        return getTransactor();
    }

    @Override
    public DomainEnum domain() {
        return DomainEnum.ISSUE;
    }


    @Override
    public String getBusinessCode() {
        return this.code;
    }
    @Override
    public IssueStatus getCurStatus() {
        return this.curStatus;
    }
    @Override
    public String getContent() {
        return this.content;
    }
    @Override
    public IssueStatus getNextStatus() {
        return this.nextStatus;
    }

    @Override
    public Reason getReason() {
        return this.reason;
    }
}
