package com.zto.devops.qc.client.model.report.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.report.ExecResultBusinessDomain;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AddExecResultCommand extends BaseCommand {

    private String code;

    private String businessCode;

    private ExecResultBusinessDomain businessDomain;

    private String executeResult;

    public AddExecResultCommand(String aggregateId) {
        super(aggregateId);
    }

}
