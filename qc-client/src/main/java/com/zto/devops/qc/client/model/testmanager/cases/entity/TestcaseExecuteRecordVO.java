package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Data;

import java.util.Date;

@Data
public class TestcaseExecuteRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long id;

    private String testPlanCode;

    private String testPlanName;

    private TestPlanStageEnum testStage;

    private String testStageDesc;

    private TestPlanCaseStatusEnum result;

    private String resultDesc;

    private Long executorId;

    private String executor;

    private Date executeTime;

    private Date startTime;

    private Date finishTime;

    private String reportFile;

    private String execLogFile;

    private String env;
}
