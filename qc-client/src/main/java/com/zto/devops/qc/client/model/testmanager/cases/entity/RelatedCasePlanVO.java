package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zto.devops.qc.client.enums.testPlan.TestPlanTypeEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RelatedCasePlanVO implements Serializable {
    private static final long serialVersionUID = -2863647052558555430L;

    @ZsmpModelProperty(description = "计划code")
    private String planCode;

    @ZsmpModelProperty(description = "计划name")
    private String planName;

    @ZsmpModelProperty(description = "计划类型")
    private TestPlanTypeEnum planType;

    @ZsmpModelProperty(description = "用例code")
    @JsonIgnore
    private String caseCode;

}
