package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * 项目名称：project-parent
 * 类 名 称：ButtonVo
 * 类 描 述：TODO
 * 创建时间：2021/8/5 9:56 上午
 * 创 建 人：bulecat
 */
@Data
public class FieldVO implements Serializable {
    private static final long serialVersionUID = -8045227288020341848L;

    @GatewayModelProperty(description = "字段名称")
    private String name;

    @GatewayModelProperty(description = "字段code")
    private String code;

    public FieldVO(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public FieldVO(String code) {
        this.code = code;
    }

    public FieldVO() {
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        FieldVO fieldVO = (FieldVO) o;
        return Objects.equals(code, fieldVO.code);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code);
    }
}
