package com.zto.devops.qc.client.model.issue.query;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @Date: 2021/12/27 10:18
 */
@Data
public class FindHolidayQuery implements Serializable {

    @GatewayModelProperty(description = "开始时间")
    private Date startDate;

    @GatewayModelProperty(description = "结束时间")
    private Date endDate;
    @GatewayModelProperty(description = "假期类型（0：工作日，1：周末，2：节假日，3：公司假日）")
    private List<Integer> type;
}
