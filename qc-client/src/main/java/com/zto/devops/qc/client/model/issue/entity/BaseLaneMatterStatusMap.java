package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;


import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.enums.issue.LaneStatusEnum;

import java.util.*;

/**
 * <AUTHOR>
 * @Description 泳道与事项的状态对应关系
 * @Date 2023/2/7
 * @Version 1.0
 */
public abstract class BaseLaneMatterStatusMap<C> implements Serializable {

    private static final long serialVersionUID = 1L;


    protected final C condition;

    public BaseLaneMatterStatusMap(C condition) {
        this.condition = condition;
    }

    /**
     *  根据状态获取泳道
     * @param status
     * @return
     */
    public LaneStatusEnum getLane(String status) {
        Map<LaneStatusEnum, Set<String>> map = laneMatterStatusMap();
        for (LaneStatusEnum l : map.keySet()) {
            if (map.get(l).contains(status)) {
                return l;
            }
        }
        return null;
    }

    /**
     * 根据泳道获取状态
     * @param laneStatus
     * @return
     */
    public List<String> getStatus(LaneStatusEnum laneStatus) {
        return new ArrayList<>(laneMatterStatusMap().getOrDefault(laneStatus, new HashSet<>()));
    }

    /**
     * 根据 事项状态，获取可以跳转的泳道及action
     * @param status
     * @return
     */
    public abstract List<LaneSkipCondition> getSkipCondition(String status);

    public List<LaneStatusEnum> getAllLane() {
        List<LaneStatusEnum> list = allLane();
        list.sort(Comparator.comparing(LaneStatusEnum::getNumber));
        return list;
    }

    protected abstract Map<LaneStatusEnum, Set<String>> laneMatterStatusMap();

    protected abstract List<LaneStatusEnum> allLane();

    public abstract List<Button> getMainBtn(String status);

    public abstract List<Button> getRequirementMainBtn(String status,String type);
}
