package com.zto.devops.qc.client.model.testmanager.plan.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.RelatedToMeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PagePlanIssueQuery extends PageQueryBase {

    @GatewayModelProperty(description = "计划Code")
    private String planCode;

    @GatewayModelProperty(description = "缺陷名称或编号", required = false)
    private String codeOrTitle;

    @GatewayModelProperty(description = "缺陷状态", required = false)
    private List<IssueStatus> statusList;

    @GatewayModelProperty(description = "当前用户id", required = false)
    private Long currentUserId;

    @GatewayModelProperty(description = "当前处理人", required = false)
    private List<Long> handleUserIdList;

    @GatewayModelProperty(description = "开发人员", required = false)
    private List<Long> developUserIdList;

    @GatewayModelProperty(description = "测试人员", required = false)
    private List<Long> testUserIdList;

    @GatewayModelProperty(description = "与我相关", required = false)
    private List<RelatedToMeEnum> relatedList;

    @GatewayModelProperty(description = "缺陷优先级", required = false)
    private List<IssuePriority> priorityList;

}
