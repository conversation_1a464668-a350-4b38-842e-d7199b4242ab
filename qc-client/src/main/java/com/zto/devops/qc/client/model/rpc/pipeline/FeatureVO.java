package com.zto.devops.qc.client.model.rpc.pipeline;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;

@Data
public class FeatureVO<T> implements Serializable {

    /**
     * 名称
     */
    protected transient String name;

    /**
     * 描述
     */
    protected transient String desc;

    /**
     * key
     */
    private String key;

    /**
     * 值
     */
    protected T value;

    /**
     * 是否显示
     */
    private Boolean isShow;

    /**
     * 不是超级管理员显示信息
     * @return
     */
    @JsonIgnore
    private String unAutoEnvDesc;

    /**
     * 是超级管理员显示信息
     * @return
     */
    @JsonIgnore
    private String autoEnvDesc;

    /**
     * 文档地址
     * @return
     */
    @JsonIgnore
    private String url;

    /**
     * 是否能操作开关
     */
    @JsonIgnore
    private Boolean canSwitch = true;


    public T defaultValue() {
        return null;
    }
}
