package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;

import com.zto.devops.framework.common.fsm.StateEnum;
import com.zto.devops.qc.client.enums.issue.LaneStatusEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/20
 * @Version 1.0
 */
@Getter
@Setter
public abstract class LaneCtxVO<M extends BaseLaneMatterVO<C>, C extends LaneStatusMapCondition, S extends StateEnum> implements Serializable {

    private static final long serialVersionUID = 1L;


    private List<LaneVO> list;

    private LaneCtxVO() {}

    public LaneCtxVO(List<M> list,
                     List<S> allStatus,
                     C condition) {
        doConstruct(list, allStatus, condition);
    }

    protected void doConstruct(List<M> list,
                               List<S> allStatus,
                               C condition) {
        if (list == null) {
            list = new ArrayList<>();
        }
        list.forEach(BaseLaneMatterVO::build);
        Map<LaneStatusEnum, List<BaseLaneMatterVO>> groupMatters = list.stream().collect(Collectors.groupingBy(m->m.getLane(condition)));
        ArrayList<LaneVO> data = new ArrayList<>();
        List<LaneStatusEnum> allLane = getLaneStatusMap(condition).getAllLane();
        Map<LaneStatusEnum, Long> countMap = countGroupByStatus(allStatus, condition);
        for (LaneStatusEnum lane : allLane) {
            LaneVO laneVO = new LaneVO();
            laneVO.setLaneKey(lane.name());
            laneVO.setLaneName(lane.getValue());
            laneVO.setTotal(countMap.getOrDefault(lane, 0L).intValue());
            laneVO.setList(groupMatters.getOrDefault(lane, new ArrayList<>()));
            data.add(laneVO);
        }
        this.list = data;
    }

    /**
     * 计算每个泳道的数据总量
     * @param allStatus
     * @param condition
     * @return
     */
    private Map<LaneStatusEnum, Long> countGroupByStatus(List<S> allStatus, C condition) {
        if (CollectionUtils.isEmpty(allStatus)) {
            return new HashMap<>();
        }
        BaseLaneMatterStatusMap<C> statusMap = getLaneStatusMap(condition);
        return allStatus.stream()
                .collect(Collectors.groupingBy(s -> statusMap.getLane(s.getCode()), Collectors.counting()));
    }

    protected abstract BaseLaneMatterStatusMap<C> getLaneStatusMap(C condition);
}
