package com.zto.devops.qc.client.model.testPlan.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class PlanDataResp implements Serializable {
    //版本编号
    private String versionCode;
    //计划名
    private String name;
    //产品负责人
    private String prdLeader;
    //开发人数
    private Integer developerNum;
    //测试人数
    private Integer testerNum;
    //计划创建人
    private String createUser;
    //计划创建时间
    private String createTime;
    //计划提测时段
    private Integer planTestHalf;
    //计划准出时段
    private Integer actualAccessHalf;
    //类型
    private Integer type;

 }
