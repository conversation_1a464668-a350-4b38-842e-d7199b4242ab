package com.zto.devops.qc.client.model.testmanager.email.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.email.EmailSourceEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PageEmailQuery extends PageQueryBase {

    @GatewayModelProperty(description = "邮件类型", required = false)
    private List<String> typeList;

    @GatewayModelProperty(description = "发件人id（ssoUserId）", required = false)
    private List<Long> sendUserIdList;

    @GatewayModelProperty(description = "版本code", required = false)
    private List<String> versionCodeList;

    @GatewayModelProperty(description = "关联计划code", required = false)
    private List<String> planCodeList;

    @GatewayModelProperty(description = "发送开始时间", required = false)
    private Date sendTimeStart;

    @GatewayModelProperty(description = "发送结束时间", required = false)
    private Date sendTimeEnd;

    @GatewayModelProperty(description = "邮件名或者报告/计划code", required = false)
    private String nameOrCode;

    @GatewayModelProperty(description = "邮件数据源枚举", required = false)
    private EmailSourceEnum emailSourceEnum;

    @GatewayModelProperty(description = "是否只查个人发送（true：是；false：否）")
    private Boolean isPersonal;

    @GatewayModelProperty(description = "产品code")
    private String productCode;

    @GatewayModelProperty(description = "当前用户id", required = false)
    private Long currentUserId;
}
