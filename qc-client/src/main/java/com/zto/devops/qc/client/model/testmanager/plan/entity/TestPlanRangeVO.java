package com.zto.devops.qc.client.model.testmanager.plan.entity;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanPriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanRangeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.report.TmTestResultEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TestPlanRangeVO implements Serializable {

    /**
     * 关联计划code
     */
    private String planCode;

    /**
     * 测试范围，MOBILE_TEST-移动专项测试|SAFETY_SCANNING-安全扫描|EXPLORATORY_TEST-探索性测试|STATIC_ANALYSIS-静态分析|PERFORMANCE_TEST-性能测试|PERMISSIONS_TEST-权限测试...
     */
    private TestPlanRangeTypeEnum testRange;

    private Date testTime;


    private Boolean testRangeStatus;

    private TestPlanPriorityEnum priority;

    private String permissionsTestInformation;

    private String testInformation;

    /**
     * 测试结果
     */
    private String status;

    /**
     * 执行人编码
     */
    private Long executorId;

    /**
     * 执行人
     */
    private String executor;

    /**
     * 总体测试结果
     */
    private TmTestResultEnum testResult;
}
