package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAbandonReasonEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class TestcaseStatusChangedEvent extends BaseEvent implements ActionEvent {

    private String code;

    private TestcaseStatusEnum status;

    private TestcaseAbandonReasonEnum abandonReason;

    @Override
    public String action() {
        return "修改用例状态";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        String str = String.format("修改为 >>> %s", this.status.getDesc());
        if (TestcaseStatusEnum.DISABLE.equals(this.status)) {
            str += String.format(", 停用原因: %s", this.abandonReason.getDesc());
        }
        return str;
    }
}
