package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ExecuteCallbackCommand extends BaseCommand {

    private String buildId;

    private AutomaticStatusEnum status;

    private String ossPath;

    public ExecuteCallbackCommand(String aggregateId) {
        super(aggregateId);
    }
}
