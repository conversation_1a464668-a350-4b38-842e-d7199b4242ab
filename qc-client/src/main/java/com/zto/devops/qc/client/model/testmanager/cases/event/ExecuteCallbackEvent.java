package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ExecuteCallbackEvent extends BaseEvent {

    private String code;

    private String buildId;

    private AutomaticStatusEnum status;

    private String ossPath;

    private String testPlanCode;

    private String productCode;

    private TestPlanStageEnum testStage;

    private AutomaticRecordTypeEnum type;

    private String env;

    private String executeTag;

    private String taskId;

    private Boolean coverageFlag;

    private String versionCode;

    private AutomaticTaskTrigModeEnum trigMode;

    private Date startTime;
}
