package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.report.OperatedButtonEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class OpertatedButton implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "按钮名称")
    private OperatedButtonEnum value;

    @GatewayModelProperty(description = "按钮描述")
    private String label;

    public OpertatedButton() {
    }

    public OpertatedButton(OperatedButtonEnum value, String label) {
        this.label = label;
        this.value = value;
    }
}
