package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.simple.Requirement;
import com.zto.devops.framework.client.simple.Sprint;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class EditIssueCommand extends IssueBaseCommand {
    private String title;
    private String description;

    private IssueFindEnv findEnv;
    private IssueFindStage findStage;
    private IssuePriority priority;
    private IssueRepetitionRate repetitionRate;
    private IssueRootCause rootCause;
    private IssueTestMethod testMethod;
    private IssueType type;

    private User developer;
    private User tester;

    private Requirement requirement;
    private RequirementLevel requirementLevel;
    private Version findVersion;
    private Version fixVersion;
    private Sprint sprint;
    private String versionConfirm;

    private IssueApplicationType applicationType;

    @GatewayModelProperty(description = "关联用例code",required = false)
    private List<String> testcaseCodes;

    private Date planStartDate;
    private Date planEndDate;

    public EditIssueCommand(String aggregateId) {
        super(aggregateId);
    }
}
