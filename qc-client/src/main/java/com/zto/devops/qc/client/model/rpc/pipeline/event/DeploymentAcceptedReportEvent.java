package com.zto.devops.qc.client.model.rpc.pipeline.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.ObservedEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.Objects;

/**
 * @Author: chenguangyu
 * @Description:
 * @Date: 2024/6/5 13:18
 */
@Getter
@Setter
@ToString
public class DeploymentAcceptedReportEvent extends FlowChangeStatusEvent implements ActionEvent, ObservedEvent {

    private String acceptRemark;

    private Boolean continueRelease;

    @Override
    public String action() {
        if (Objects.nonNull(getFlowEvent())) {
            return getFlowEvent().getValue();
        }
        return null;
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return getOccurred();
    }

    @Override
    public String makeString() {
        return acceptRemark;
    }
}

