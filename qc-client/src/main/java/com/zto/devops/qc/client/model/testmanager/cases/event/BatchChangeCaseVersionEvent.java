package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Map;

@Getter
@Setter
public class BatchChangeCaseVersionEvent extends BaseEvent implements ActionEvent {

    @Override
    public String action() {
        return "修改了用例版本";
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }

    @Override
    public Map<String, ?> metadata() {
        return ActionEvent.super.metadata();
    }

    @Override
    public String logBusinessCode() {
        return ActionEvent.super.logBusinessCode();
    }
}
