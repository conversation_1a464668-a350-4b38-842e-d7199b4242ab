package com.zto.devops.qc.client.model.report.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.report.ReportType;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PageReportQuery extends PageQueryBase {

    @GatewayModelProperty(description = "编号或名称", required = false)
    private String codeOrName;

    @GatewayModelProperty(description = "所属产品s", required = false)
    private List<String> productCodes;

    @GatewayModelProperty(description = "报告人", required = false)
    private List<Long> reportUserIds;

    @GatewayModelProperty(description = "报告类型", required = false)
    private List<ReportType> reportTypes;

    @GatewayModelProperty(description = "创建时间-开始", required = false)
    private Date gmtCreateStart;

    @GatewayModelProperty(description = "创建时间-结束", required = false)
    private Date gmtCreateEnd;

    @GatewayModelProperty(description = "草稿 ", required = false)
    private TestPlanStatusEnum status;

    @GatewayModelProperty(description = "版本号", required = false)
    private String versionCode;

    private User transactor;

    @GatewayModelProperty(description = "创建人ID", required = false)
    private Long creatorId;

    private String orderField;
    private String orderType;
}
