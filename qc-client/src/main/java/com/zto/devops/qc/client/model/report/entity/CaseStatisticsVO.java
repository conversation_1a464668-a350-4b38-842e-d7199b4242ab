package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@GatewayModel(description = "用例信息统计")
@Data
public class CaseStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "报告code", required = true)
    private String reportCode;

    @GatewayModelProperty(description = "用例code", required = true)
    private String code;

    @GatewayModelProperty(description = "精准平台的code", required = true)
    private String frogPlanCode;
    @GatewayModelProperty(description = "冒烟执行计划名称", required = true)
    private String frogPlanName;
    @GatewayModelProperty(description = "用例总数", required = true)
    private Integer caseCount;

    @GatewayModelProperty(description = "计划冒烟数", required = true)
    private Integer planSmokeCaseCount;

    @GatewayModelProperty(description = "冒烟通过用例总数", required = true)
    private Integer smokeAccessCount;

}