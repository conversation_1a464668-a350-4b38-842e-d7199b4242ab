package com.zto.devops.qc.client.model.issue.entity;

import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.framework.client.simple.Field;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 处理人维度的模型
 * @Date 2022/12/6
 * @Version 1.0
 */
@Data
public abstract class BaseMatterVO implements Serializable {

    private String code;

    private String title;

    private String creator;

    private Long creatorId;

    /**
     * 经办人id
     */
    private Long handlerId;

    /**
     * 经办人
     */
    private String handler;

    /**
     * 计划开始时间
     */
    private Date planStartDate;

    /**
     * 计划结束时间
     */
    private Date planEndDate;

    /**
     * 权限按钮
     */
    private List<Button> buttons = new ArrayList<>();

    /**
     * 可编辑的字段权限
     */
    private List<Field> fields = new ArrayList<>();

    /**
     *  状态描述
     */
    private String statusDesc;

    private String domain;

    private void setDomain(String domain) {
    }

    private void setStatusDesc(String statusDesc) {
    }

    public void build() {
        domain = domain();
        statusDesc = statusDesc();
    }

    public abstract String domain();

    public abstract String statusDesc();

    public List<Button> getButtons(Long currentHandleId) {
        return Collections.emptyList();
    }
}
