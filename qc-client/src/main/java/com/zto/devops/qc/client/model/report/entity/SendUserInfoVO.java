package com.zto.devops.qc.client.model.report.entity;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * 项目名称：qc-parent
 * 类 名 称：SendUserInfo
 * 类 描 述：TODO
 * 创建时间：2021/11/17 4:29 下午
 * 创 建 人：bulecat
 *
 * <AUTHOR>
 */
@Data
public class SendUserInfoVO implements Serializable {
    @ZModelProperty(description = "姓名", sample = "luban")
    private String userName;

    /*@GatewayModelProperty(description = "code")
    private String code;*/

    @ZModelProperty(description = "userId", sample = "5984549")
    private Long userId;


    @ZModelProperty(description = "描述", sample = "描述")
    private String desc;

    @ZModelProperty(description = "成员类型", sample = "成员类型")
    private String userType;

    /**
     * 用户头像
     */
    @ZModelProperty(description = "用户头像", sample = "用户头像")
    private String avatar;
    /**
     * 用户岗位
     */
    @ZModelProperty(description = "用户岗位", sample = "用户岗位")
    private String station;
    /**
     * 用户部门
     */
    @ZModelProperty(description = "部门名称", sample = "部门名称")
    private String deptName;

    @ZModelProperty(description = "邮件地址", sample = "邮件地址")
    private String email;

    private boolean isOverrideEquals = false;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SendUserInfoVO that = (SendUserInfoVO) o;
        if (that.userId != null && that.isOverrideEquals) {
            return userId.equals(that.userId);
        } else if (that.email != null && !"".equals(that.email) && that.isOverrideEquals) {
            return email.equals(that.email);
        }
        return Objects.equals(userName, that.userName) && Objects.equals(userId, that.userId) && Objects.equals(desc, that.desc) && Objects.equals(userType, that.userType) && Objects.equals(avatar, that.avatar) && Objects.equals(station, that.station) && Objects.equals(deptName, that.deptName) && Objects.equals(email, that.email);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userName, userId, desc, userType, avatar, station, deptName, email);
    }
}
