package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.OperationTypeEnum;
import lombok.Data;

import java.util.Date;

@Data
public class TagRemovedSimpleEvent extends BaseEvent implements ActionEvent {

    private String code;

    private String tagName;

    @Override
    public String action() { return OperationTypeEnum.REMOVETAG.getValue(); }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        if (tagName != null && !"".equals(tagName)) {
            return "【" + tagName + "】";
        }
       return "";
    }

}
