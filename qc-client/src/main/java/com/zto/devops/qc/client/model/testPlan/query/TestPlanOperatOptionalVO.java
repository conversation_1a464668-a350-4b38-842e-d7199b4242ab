package com.zto.devops.qc.client.model.testPlan.query;

import com.zto.devops.qc.client.enums.testPlan.OperatOptionalEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TestPlanOperatOptionalVO implements Serializable {
    @GatewayModelProperty(description = "可操作button", required = false)
    private OperatOptionalEnum button;
    @GatewayModelProperty(description = "可操作button 描述", required = false)
    private String buttonDesc;
}
