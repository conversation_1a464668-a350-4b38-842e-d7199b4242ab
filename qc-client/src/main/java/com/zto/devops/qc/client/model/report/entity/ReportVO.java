package com.zto.devops.qc.client.model.report.entity;

import com.zto.devops.qc.client.enums.report.ReportType;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ReportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String code;
    private String planCode;
    private String name;
    private ReportType reportType;
    private String reportTypeDesc;
    private String productCode;
    private String productName;
    private String versionCode;
    private String versionName;
    private Date gmtCreate;
    private Date gmtModified;
    private Long creatorId;
    private String creator;

    @GatewayModelProperty(description = "计划版本开始时间", required = false)
    private Date startDate;


    @GatewayModelProperty(description = "计划版本上线时间", required = false)
    private Date publishDate;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date presentationDate;
    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date approvalExitDate;

    /**
     * 按计划范围上线  1 是, 0 否
     */
    private Integer asPlanedOnline;

    @GatewayModelProperty(description = "实际提测时间", required = false)
    private Date actualPresentationDate;

    @GatewayModelProperty(description = "实际准出时间", required = false)
    private Date actualApprovalExitDate;
    @GatewayModelProperty(description = "实际发布时间", required = false)
    private Date actualPublishDate;

    @GatewayModelProperty(description = "验收开始时间", required = false)
    private Date  checkStartDate;

    @GatewayModelProperty(description = "验收结束时间", required = false)
    private Date  CheckEndDate;

    @GatewayModelProperty(description = "计划冒烟用例数", required = false)
    private Integer planSmokeCase;

    /**
     * 首次通过冒烟用例数
     */
    private Integer firstPermitSmoke;

    private String testResult;
    /**
     * 是否延期 1 是, 0 否
     */
    private Integer delay;

    private String checkType;

    private Date updateTestResultDate;

    private Integer developerCount;

    private Integer testerCount;

}
