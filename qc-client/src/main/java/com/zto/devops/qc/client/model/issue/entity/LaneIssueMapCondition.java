package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.issue.LaneDomainEnum;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/7
 * @Version 1.0
 */
public class LaneIssueMapCondition extends LaneStatusMapCondition implements Serializable {

    private static final long serialVersionUID = 1L;


    public LaneIssueMapCondition(LaneDomainEnum lane) {
        super(lane);
    }
}
