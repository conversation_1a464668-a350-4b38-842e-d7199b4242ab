package com.zto.devops.qc.client.model.testPlan.entity;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class TestFunctionPointVO implements Serializable {

    private String code ;

    /**
     * 测试类型
     */
    @GatewayModelProperty(description = "测试类型", required = false)
    private String type;

    /**
     * 功能测试点
     */
    @GatewayModelProperty(description = "功能测试点", required = false)
    private String functionPoint;

    /**
     * 测试负责人/参与人id
     */
    @GatewayModelProperty(description = "测试负责人/参与人id", required = false)
    private Long directorId;

    /**
     * 测试负责人/参与人名
     */
    @GatewayModelProperty(description = "测试负责人/参与人名", required = false)
    private String directorName;

    /**
     * 业务code
     */
    private String businessCode;

    @GatewayModelProperty(description = "编号", required = false)
    private String number;

}
