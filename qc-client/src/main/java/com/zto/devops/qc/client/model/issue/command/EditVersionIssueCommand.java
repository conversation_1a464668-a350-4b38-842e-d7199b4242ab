package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.simple.Requirement;
import com.zto.devops.framework.client.simple.Sprint;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.enums.issue.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EditVersionIssueCommand extends BaseCommand {
    private String title;
    private String description;

    private IssueFindEnv findEnv;
    private IssueFindStage findStage;
    private IssuePriority priority;
    private IssueRepetitionRate repetitionRate;
    private IssueRootCause rootCause;
    private IssueTestMethod testMethod;
    private IssueType type;

    private User developer;
    private User tester;

    private Requirement requirement;
    private RequirementLevel requirementLevel;
    private Version findVersion;
    private Version fixVersion;
    private Sprint sprint;
    private String versionConfirm;

    private Boolean removeFromVersion;
    private String currentVersionName;

    public EditVersionIssueCommand(String aggregateId) {
        super(aggregateId);
    }
}
