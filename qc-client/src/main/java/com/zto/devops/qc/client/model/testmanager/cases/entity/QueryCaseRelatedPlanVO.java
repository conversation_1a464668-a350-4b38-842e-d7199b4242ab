
package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class QueryCaseRelatedPlanVO implements Serializable {

    @ZsmpModelProperty(description = "是否有关联")
    private Boolean changeVersionFlag;

    @ZsmpModelProperty(description = "用例总数")
    private Integer totalNum;

    @ZsmpModelProperty(description = "关联计划数")
    private Integer relatedNum;

    @ZsmpModelProperty(description = "测试计划集合")
    private List<RelatedCasePlanVO> planList;

}
