package com.zto.devops.qc.client.model.report.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 项目名称：qc-parent
 * 类 名 称：AddOnlineSmokeReportReq
 * 类 描 述：TODO
 * 创建时间：2021/11/10 5:57 下午
 * 创 建 人：bulecat
 */

@Getter
@Setter
public class OnlineSmokeReporEdittEvent extends OnlineSmokeReportAddedEvent implements ActionEvent {



    @Override
    public String action() {
        return null;
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }
}
