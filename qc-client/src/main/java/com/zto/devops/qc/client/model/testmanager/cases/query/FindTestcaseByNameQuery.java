package com.zto.devops.qc.client.model.testmanager.cases.query;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import lombok.Data;

@Data
public class FindTestcaseByNameQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    private String name;

    private String parentCode;

    private String productCode;

    private TestcaseAttributeEnum attribute;

    private TestcaseTypeEnum type;

    private Boolean setCore;

    private String versionCode;
}
