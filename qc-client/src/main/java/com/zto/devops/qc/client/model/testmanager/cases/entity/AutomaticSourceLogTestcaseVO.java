package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseFlagEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AutomaticSourceLogTestcaseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * code
     */
    private String code;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 上级code
     */
    private String parentCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 属性
     */
    private String attribute;

    /**
     * 用例类型
     */
    private String type;

    /**
     * 等级
     */
    private String priority;

    /**
     * 状态
     */
    private String status;

    /**
     * 前置条件
     */
    private String precondition;

    /**
     * 责任人编码
     */
    private Long dutyUserId;

    /**
     * 责任人
     */
    private String dutyUser;

    /**
     * 备注
     */
    private String comment;

    /**
     * 解析log的code
     */
    private String automaticSourceLogCode;

    /**
     * 节点类型
     */
    private String nodeType;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 层级
     */
    private Integer layer;

    /**
     * 路径
     */
    private String path;

    /**
     * 是否删除
     */
    private Boolean enable;

    /**
     * 创建人编码
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 所有父节点类型
     */
    private String nodeTypePath;


    private String flag;

    private String flagDesc;

    private String statusDesc;

    private String attributeDesc;

    private Boolean hasChildren = false;

    private String testcaseCode;

    private List<TmTestPlanVO> testPlanList;

    private int count;

    private String planName;

    private String interfaceName;

    public void setFlag(String flag) {
        this.flag = flag;
        if (null != flag) {
            this.flagDesc = TestcaseFlagEnum.getDescByName(flag);
        }
    }

    public void setStatus(String status) {
        this.status = status;
        if (null != status) {
            this.statusDesc = TestcaseStatusEnum.getDescByName(status);
        }
    }

    public void setAttribute(String attribute) {
        this.attribute = attribute;
        if (null != attribute) {
            this.attributeDesc = TestcaseAttributeEnum.getEnumByName(attribute).getDesc();
        }
    }

    private List<AutomaticSourceLogTestcaseVO> sonList;
}
