package com.zto.devops.qc.client.model.testmanager.plan.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Data;

import java.util.Map;

@Data
public class DeletePlanCaseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long id;

    private String caseCode;

    private String planCode;

    private TestPlanNewStatusEnum planStatus;

    private Map<String, Object> planStageStatus;

    private TestPlanStageEnum testStage;

}
