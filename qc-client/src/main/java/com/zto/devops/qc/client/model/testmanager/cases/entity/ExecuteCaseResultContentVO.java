package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import lombok.Data;

import java.util.List;

@Data
public class ExecuteCaseResultContentVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private String code;

    private String testName;

    private String groupName;

    private String status;

    private String startTime;

    private String finishTime;

    private String url;

    private String method;

    private String cookies;

    private String queryString;

    private String requestData;

    private String responseCode;

    private String responseData;

    private String responseMessage;

    List<AssertionResultInfoVO> assertionList;
}
