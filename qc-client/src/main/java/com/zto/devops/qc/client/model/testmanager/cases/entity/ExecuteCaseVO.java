package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ExecuteCaseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private String code;

    private String automaticTaskCode;

    private String automaticTaskId;

    private String productCode;

    private String productName;

    private String versionCode;

    private String versionName;

    private String testPlanCode;

    private String testPlanName;

    private String parentCode;

    private List<String> parentFullName;

    private String name;

    private TestcaseTypeEnum type;

    private TestcasePriorityEnum priority;

    private TestPlanCaseStatusEnum result;

    private AutomaticNodeTypeEnum nodeType;

    private Long executorId;

    private String executor;

    private String comment;

    private Boolean enable;

    private Long modifierId;

    private String modifier;

    private Date gmtModified;

    private String path;

    private String resultFile;

    private List<ExecuteCaseResultContentVO> resultContent;

    private String reportFile;

    private String execLogFile;

    private AutomaticRecordTypeEnum automaticRecordType;

    private String interfaceName;

    private Long passedCount;

    private Long failedCount;
}
