package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.enums.DomainEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ReplyCommentCommand extends BaseCommand {

    private DomainEnum domain;
    private String businessCode;
    private String code;
    private String repliedCode;
    private String topRepliedCode;
    private Long repliedUserId;
    private String repliedUserName;
    private Integer level;
    private String content;

    public ReplyCommentCommand(String aggregateId) {
        super(aggregateId);
    }
}
