package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class HeartResultGetVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private String caseCode;

    /**
     * 触发方式
     */
    private AutomaticTaskTrigModeEnum trigMode;

    /**
     * 结果列表（成功、失败、未执行）
     */
    private List<TestPlanCaseStatusEnum> resultList;

    public HeartResultGetVO(String caseCode) {
        this.caseCode = caseCode;
        this.trigMode = AutomaticTaskTrigModeEnum.HEART_BEAT;
        this.resultList = getList();
    }

    public List<TestPlanCaseStatusEnum> getList() {
        List<TestPlanCaseStatusEnum> list = new ArrayList<>();
        list.add(TestPlanCaseStatusEnum.SUCCESS);
        list.add(TestPlanCaseStatusEnum.FAILED);
        list.add(TestPlanCaseStatusEnum.NOT_STARTED);
        return list;
    }

}
