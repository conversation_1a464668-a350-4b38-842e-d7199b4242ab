package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@GatewayModel(description = "评审 人 信息")
@Data
public class ReviewUserVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "用户id(SSO一致)", sample = "1",required = false)
    private Long ssoUserId;

    @GatewayModelProperty(description = "用户姓名", sample = "",required = false)
    private String cnName;

    @GatewayModelProperty(description = "部门id", sample = "817355",required = false)
    private Long deptId;

    @GatewayModelProperty(description = "部门名称", sample = "研发效能部",required = false)
    private String deptName;

    @GatewayModelProperty(description = "岗位名称", sample = "",required = false)
    private String stationName;

    @GatewayModelProperty(description = "用户图片", sample = "",required = false)
    private String avatar;

}
