package com.zto.devops.qc.client.model.testmanager.plan.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class RelatedReportVO extends RelatedPlanReportBaseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "报告code", required = false)
    private String reportCode;

    @GatewayModelProperty(description = "报告名称", required = false)
    private String reportName;

    @GatewayModelProperty(description = "报告type", required = false)
    private String reportType;

    @GatewayModelProperty(description = "报告type中文", required = false)
    private String reportTypeDesc;

}
