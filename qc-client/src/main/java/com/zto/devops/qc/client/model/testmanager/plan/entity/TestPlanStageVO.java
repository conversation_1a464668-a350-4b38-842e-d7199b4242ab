package com.zto.devops.qc.client.model.testmanager.plan.entity;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TestPlanStageVO implements Serializable {

    @GatewayModelProperty(description = "测试阶段,SMOKE_TEST-冒烟测试|FUNCTIONAL_TEST-功能测试|ONLINE_SMOKE_TEST-线上冒烟测试", sample = "1", required = false)
    private TestPlanStageEnum testStage;

    @GatewayModelProperty(description = "测试阶段描述", sample = "1", required = false)
    private String testStageDesc;

    @GatewayModelProperty(description = "测试阶段状态", sample = "1", required = false)
    private TestPlanStageStatusEnum testStageStatus;

    @GatewayModelProperty(description = "测试阶段状态描述", sample = "1", required = false)
    private String testStageStatusDesc;

    public String getTestStageDesc() {
        return  testStage == null  ? "" : testStage.getValue();
    }

    public String getTestStageStatusDesc() {
        return  testStageStatus == null  ? "" : testStageStatus.getValue();
    }
}
