package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RelatedTestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "相关计划", required = false)
    private List<RelatedBaseVO> testPlanVOS;
    @GatewayModelProperty(description = "相关报告", required = false)
    private List<RelatedBaseVO> testReportVOS;
}
