package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.OperationTypeEnum;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TagAddedEvent extends BaseEvent implements ActionEvent {
    private String businessCode;
    private List<TagVO> tags;

    @Override
    public String action() { return OperationTypeEnum.ADDTAG.getValue(); }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        if (tags == null || tags.size() < 1) {
          return "";
        }
        StringBuilder ctx = new StringBuilder("【");
        for (TagVO tag : tags) {
            ctx.append(tag.getTagName()).append("】");
        }
        return ctx.toString();
    }
}
