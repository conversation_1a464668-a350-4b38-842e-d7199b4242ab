package com.zto.devops.qc.client.model.rpc.pipeline;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
@GatewayModel(description = "通用出参实体")
@EqualsAndHashCode(of = "code")
public class GeneralVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @GatewayModelProperty(description = "编号")
    private String code;

    /**
     * 名称
     */
    @GatewayModelProperty(description = "名称")
    private String name;

    /**
     * 状态
     */
    @GatewayModelProperty(description = "状态")
    private Boolean status;

    /**
     * 角色
     */
    @GatewayModelProperty(description = "角色")
    private List<String> role;

    /**
     * 描述
     */
    @GatewayModelProperty(description = "描述")
    private String description;

    /**
     * 外链
     */
    @GatewayModelProperty(description = "外链")
    private String url;


    @GatewayModelProperty(description = "排序")
    private Integer sort;

    public GeneralVO(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
}
