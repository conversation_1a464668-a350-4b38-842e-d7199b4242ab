package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ListTestcaseModuleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String parentIds;

    private String code;

    private String name;

    private String productCode;

    private String versionCode;

    private String parentCode;

    private TestcaseTypeEnum type;

    private TestcaseStatusEnum status;

    private String automaticSourceCode;

    private AutomaticNodeTypeEnum nodeType;

    private Integer sort;

    private String path;

    private Integer layer;

    private String fullName;

    private int testcaseCount;

    private TestcaseAttributeEnum attribute;

    private AutomaticStatusEnum automaticSourceStatus;

    private String comment;

    private AutomaticRecordTypeEnum automaticSourceType;

    private Boolean setCore;

    private Long id;

    private Date gmtCreate;

    private List<ListTestcaseModuleVO> children;

    public List<ListTestcaseModuleVO> getChildren() {
        Boolean sceneFlag = this.getSceneFlag();
        if (null != sceneFlag && sceneFlag && CollectionUtil.isNotEmpty(this.children)) {
            this.children.stream().forEach(child -> {
                child.setSceneFlag(true);
            });
        }
        return this.children;
    }

    /**
     * 用例工厂顶级分组code
     */
    private String sceneTopModuleCode;

    /**
     * 是否关联场景
     */
    private Boolean sceneFlag;

    public Boolean getSceneFlag() {
        if (null == this.sceneFlag && StringUtil.isNotBlank(this.sceneTopModuleCode)) {
            this.sceneFlag = (StringUtil.isNotBlank(this.code) && this.code.equals(this.sceneTopModuleCode))
                    || (StringUtil.isNotBlank(this.path) && this.path.contains(this.sceneTopModuleCode));
        }
        return null == this.sceneFlag ? Boolean.FALSE : this.sceneFlag;
    }
}
