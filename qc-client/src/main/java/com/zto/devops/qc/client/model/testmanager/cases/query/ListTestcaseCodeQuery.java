package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ListTestcaseCodeQuery extends BaseQuery {

    private String productCode;

    private TestcaseTypeEnum type;

    private String parentCode;

    private String codeOrTitle;

    private List<TestcasePriorityEnum> priorityList;

    private List<TestcaseStatusEnum> statusList;

    private List<String> tagList;

    private List<Long> dutyUserList;

    private List<Long> creatorList;

    private Date createTimeBegin;

    private Date createTimeEnd;

    private Date modifyTimeBegin;

    private Date modifyTimeEnd;

    private TestPlanStageEnum testStage;

    private String planCode;

    private Boolean planPattern;

    private List<AutomaticNodeTypeEnum> nodeTypeList;

    private List<HeartCaseFilterEnum> isHeart;

    private List<AutomaticRecordTypeEnum> automaticTypeList;

    private List<Boolean> setCoreList;

    private String versionCode;
}
