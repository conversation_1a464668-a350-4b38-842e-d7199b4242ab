package com.zto.devops.qc.client.model.issue.entity;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.zto.devops.framework.client.event.RevisionBusinessName;
import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.framework.client.simple.Field;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Data
public class IssueVO implements Serializable {
    @GatewayModelProperty(description = "编号", required = false)
    private String code;

    @RevisionBusinessName(name = "缺陷标题")
    @GatewayModelProperty(description = "标题", required = false)
    private String title;

    @RevisionBusinessName(name = "缺陷描述")
    @GatewayModelProperty(description = "描述", required = false)
    private String description;

    @GatewayModelProperty(description = "重新打开时间", required = false)
    private Date reopenTime;
    @GatewayModelProperty(description = "开始修复时间", required = false)
    private Date startFixTime;
    @GatewayModelProperty(description = "延期修复时间", required = false)
    private Date delayFixTime;
    @GatewayModelProperty(description = "交付验证时间", required = false)
    private Date deliverTime;
    @GatewayModelProperty(description = "拒绝时间", required = false)
    private Date rejectTime;
    @GatewayModelProperty(description = "关闭时间", required = false)
    private Date closeTime;
    @GatewayModelProperty(description = "创建时间", required = false)
    private Date gmtCreate;
    @GatewayModelProperty(description = "修改时间", required = false)
    private Date gmtModified;
    @GatewayModelProperty(description = "更新时间", required = false)
    private Date updateTime;
    @GatewayModelProperty(description = "发现时间", required = false)
    private Date findTime;


    @GatewayModelProperty(description = "状态", required = false)
    private IssueStatus status;

    @RevisionBusinessName(name = "发现环境", method = "getValue")
    @GatewayModelProperty(description = "发现环境", required = false)
    private IssueFindEnv findEnv;

    @RevisionBusinessName(name = "发现阶段", method = "getValue")
    @GatewayModelProperty(description = "发现阶段", required = false)
    private IssueFindStage findStage;

    @RevisionBusinessName(name = "优先级", method = "getValue")
    @GatewayModelProperty(description = "优先级", required = false)
    private IssuePriority priority;

    /**
     * 等级排序值
     */
    private int priorityNum;

    public int getPriorityNum() {
        return Objects.nonNull(this.priority) ? this.priority.getSortValue() : 0;
    }

    @RevisionBusinessName(name = "重现概率", method = "getValue")
    @GatewayModelProperty(description = "复现概率", required = false)
    private IssueRepetitionRate repetitionRate;

    @RevisionBusinessName(name = "Bug根源", method = "getValue")
    @GatewayModelProperty(description = "缺陷根源", required = false)
    private IssueRootCause rootCause;

    @RevisionBusinessName(name = "测试方法", method = "getValue")
    @GatewayModelProperty(description = "测试方法", required = false)
    private IssueTestMethod testMethod;

    @GatewayModelProperty(description = "缺陷类型", required = false)
    @RevisionBusinessName(name = "BUG类别", method = "getValue")
    private IssueType type;

    @GatewayModelProperty(description = "产品编码", required = false)
    private String productCode;
    @GatewayModelProperty(description = "产品名称", required = false)
    private String productName;

    @GatewayModelProperty(description = "关联迭代code", required = false)
    private String  sprintCode ;

    @RevisionBusinessName(name = "关联迭代")
    @GatewayModelProperty(description = "关联迭代名称", required = false)
    private String sprintName ;

    @GatewayModelProperty(description = "需求编码", required = false)
    private String requirementCode;
    @GatewayModelProperty(description = "需求名称", required = false)
    private String requirementName;
    @GatewayModelProperty(description = "需求名称", required = false)
    private RequirementLevel requirementLevel;

    @GatewayModelProperty(description = "发现版本编码", required = false)
    private String findVersionCode;

    @RevisionBusinessName(name = "发现版本")
    @GatewayModelProperty(description = "发现版本名称", required = false)
    private String findVersionName;

    @GatewayModelProperty(description = "修复版本编码", required = false)
    private String fixVersionCode;

    @RevisionBusinessName(name = "修复版本")
    @GatewayModelProperty(description = "修复版本名称", required = false)
    private String fixVersionName;

    @GatewayModelProperty(description = "报告人ID", required = false)
    private Long findUserId;
    @GatewayModelProperty(description = "报告人名称", required = false)
    private String findUserName;

    @GatewayModelProperty(description = "当前处理人ID", required = false)
    private Long handleUserId;
    @GatewayModelProperty(description = "当前处理人名称", required = false)
    private String handleUserName;

    @GatewayModelProperty(description = "开发人员ID", required = false)
    private Long developUserId;
    @RevisionBusinessName(name = "开发人员")
    @GatewayModelProperty(description = "开发人员名称", required = false)
    private String developUserName;

    @GatewayModelProperty(description = "测试人员ID", required = false)
    private Long testUserId;
    @RevisionBusinessName(name = "测试人员")
    @GatewayModelProperty(description = "测试人员名称", required = false)
    private String testUserName;

    @GatewayModelProperty(description = "创建人ID", required = false)
    private Long creatorId;
    @GatewayModelProperty(description = "创建人名称", required = false)
    private String creator;
    @GatewayModelProperty(description = "文档个数", required = false)
    private Integer fileCount;
    @GatewayModelProperty(description = "用例个数", required = false)
    private Integer caseCount;
    @GatewayModelProperty(description = "更新人员ID", required = false)
    private Long updateUserId;
    @GatewayModelProperty(description = "更新人员名称", required = false)
    private String updateUserName;

    @GatewayModelProperty(description = "用例", required = false)
    private List<CaseVO> caseList;
    @GatewayModelProperty(description = "标签", required = false)
    private List<TagVO> tags;
    @GatewayModelProperty(description = "标签", required = false)
    private String tagName;

    @GatewayModelProperty(description = "状态说明", required = false)
    private String content;
    @GatewayModelProperty(description = "原因", required = false)
    private Reason reason;
    private String reasonDesc;
    @GatewayModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;

    @GatewayModelProperty(description = "评论", required = false)
    private List<CommentVO> comments;

    @GatewayModelProperty(description = "可编辑字段", required = false)
    private Set<String> editableFieldNames;

    @GatewayModelProperty(description = "可操作事件", required = false)
    private List<Event> operableEvents;

   /* @GatewayModelProperty(description = "可操作事件", required = false)
    private List<ButtonVO> operableEvents;*/

    @GatewayModelProperty(description = "实际工时", required = false)
    private Double actualWorkingHours;

    @GatewayModelProperty(description = "是否流转开发", required = false)
    private Boolean transitToDevelop;

    @GatewayModelProperty(description = "是否流转测试", required = false)
    private Boolean transitToTest;

    @GatewayModelProperty(description = "before 版本确认之前，after 版本确认之前", required = false)
    private String  versionConfirm;

    @GatewayModelProperty(description = "是否是多个 修复的缺陷", required = false)
    private Boolean morefixVersion;
    /**
     * 审查状态  1已审查 0未审查
     */
    private Boolean examination;
    /**
     * 测试遗漏  1为是 0为否
     */
    private Boolean testOmission;
    /**
     * 代码缺陷  1为是 0为否
     */
    private Boolean codeDefect;


    @GatewayModelProperty(description = "测试遗漏原因", required = false)
    private String testOmissionVersion;
    @GatewayModelProperty(description = "代码遗漏原因", required = false)
    private String codeDefectVersion;

    @RevisionBusinessName(name = "应用类型", method = "getValue")
    @GatewayModelProperty(description = "应用类型", required = false)
    private IssueApplicationType applicationType;

    private RefuseReason refuseReason;

    /**
     * 抄送人
     */
    @GatewayModelProperty(description = "抄送人", required = false)
    private Set<RelevantUserVO> ccManList;

    private String ccUserId;

    private String ccUserName;

    private List<Button> buttons;

    private List<Field> fields;

    @GatewayModelProperty(description = "计划开始时间", required = false)
    private Date planStartDate;
    @GatewayModelProperty(description = "计划结束时间", required = false)
    private Date planEndDate;
    @GatewayModelProperty(description = "宝盒聊天记录code", required = false)
    private String msgCode;
    @GatewayModelProperty(description = "提醒")
    private String warn;
    @GatewayModelProperty(description = "天数")
    private int warnDay;

    public int getWarnDay() {
        if (Objects.nonNull(this.gmtCreate)) {
            long betweenHour = DateUtil.between(this.gmtCreate, new Date(), DateUnit.HOUR);
            this.warnDay = (int) betweenHour;
        }
        return this.warnDay;
    }

    @GatewayModelProperty(description = "重新打开次数")
    private Integer reopen;

    @GatewayModelProperty(description = "是否有效缺陷",required = false)
    private Boolean validFlag;
}
