package com.zto.devops.qc.client.model.report.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.report.ReviewType;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@GatewayModel(description = "评审信息")
@Data
public class ReviewInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "评审信息code", required = false)
    private String code;

    @GatewayModelProperty(description = "报告code", required = false)
    private String reportCode;

    @GatewayModelProperty(description = "评审日期", required = true)
    private Date reviewDate;

    @GatewayModelProperty(description = "评审类型方式", required = true)
    private ReviewType reviewType;

    @GatewayModelProperty(description = "评审类型方式", required = true)
    private String reviewTypeDesc;

    @GatewayModelProperty(description = "记录人id", required = true)
    private Long recordUserId;

    @GatewayModelProperty(description = "记录人", required = true)
    private String recordUserName;

    @GatewayModelProperty(description = "产品参评人--json存储", required = false)
    private List<ReviewUserVO> productReviewUserList;

    @GatewayModelProperty(description = "开发参评人--json存储", required = false)
    private List<ReviewUserVO> developReviewUserList;

    @GatewayModelProperty(description = "测试参评人--json存储", required = false)
    private List<ReviewUserVO> testReviewUserList;
}