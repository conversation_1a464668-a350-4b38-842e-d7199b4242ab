package com.zto.devops.qc.client.model.testmanager.plan.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TmTestPlanVO;
import lombok.Data;

import java.util.Date;

@Data
public class SafeTestPlanEditedEvent extends BaseEvent implements ActionEvent {


    private TmTestPlanVO testPlanVO;

    @Override
    public String action() {
        return "编辑安全测试计划";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        if (testPlanVO == null){
            return "";
        }
        return testPlanVO.getPlanName();
    }
}
