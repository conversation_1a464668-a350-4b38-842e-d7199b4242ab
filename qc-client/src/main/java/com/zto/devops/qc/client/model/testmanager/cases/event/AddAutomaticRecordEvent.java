package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AddAutomaticRecordEvent extends BaseEvent implements ActionEvent {

    /**
     * 是否根据git提交操作，自动重新解析登记库（0:不是；1:是;）
     */
    private Boolean autoAnalysisFlag;

    /**
     * 唯一标识
     */
    private String code;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 登记库名称
     */
    private String name;

    /**
     * 备注
     */
    private String comment;

    /**
     * jmeter:源地址-OSS上的脚本文件地址;xunit-git地址
     */
    private String address;

    /**
     * 类型：1-jmeter, 2-testng, 3-postman, 4-junit,5-pyunit
     */
    private AutomaticRecordTypeEnum type;

    /**
     * 脚本文件名
     */
    private String fileName;

    /**
     * 数据文件地址
     */
    private String dataFileAddress;

    /**
     * 扩展jar包地址
     */
    private String extendJarAddress;

    /**
     * 第三方jar包地址
     */
    private String thirdJarAddress;

    private String bucketName;

    //所属目录code
    private String testcaseCode;

    private String workSpace;

    private String branch;

    private String scanDirectory;

    private String commitId;

    @Override
    public String action() {
        return "新增登记库";
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }
}
