package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import lombok.Data;

import java.util.Date;

@Data
public class DeleteTestcaseEvent extends BaseEvent implements ActionEvent {
    private String code;
    private TestcaseAttributeEnum attribute;
    private TestcaseTypeEnum type;

    @Override
    public String action() {
        return "删除手工测试用例";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }
}
