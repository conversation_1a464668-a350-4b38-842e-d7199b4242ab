package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

@Data
public class RelationTestcaseListVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ZsmpModelProperty(description = "编号")
    private String code;

    @ZsmpModelProperty(description = "标题")
    private String title;

    @ZsmpModelProperty(description = "用例状态")
    private String status;

    @ZsmpModelProperty(description = "用例状态")
    private String statusDesc;

    @ZsmpModelProperty(description = "用例类型")
    private String caseType;

    @ZsmpModelProperty(description = "用例类型描述")
    private String caseTypeDesc;

    @ZsmpModelProperty(description = "责任人")
    private String dutyUser;
}
