package com.zto.devops.qc.client.model.report.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.report.CodeCoverResult;
import com.zto.devops.qc.client.model.report.entity.*;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class PermitReportAddedEvent extends ReportAddedEvent implements ActionEvent {


    @GatewayModelProperty(description = "模块测试", required = false)
    private List<ModuleTestVO> moduleTestVOS;

    @GatewayModelProperty(description = "用例测试结果", required = false)
    private List<CaseResultVO> caseResultVOS;

    @GatewayModelProperty(description = "缺陷信息统计", required = false)
    private List<IssueInfoVO> issueInfoVOS;

    @GatewayModelProperty(description = "遗留缺陷", required = false)
    private List<IssueLegacyVO> issueLegacyVOS;

    @GatewayModelProperty(description = "安全漏洞", required = false)
    private List<SecurityHoleVO> securityHoles;

    @GatewayModelProperty(description = "代码覆盖率结果", required = false)
    private CodeCoverResult codeCoverResult;

    @GatewayModelProperty(description = "代码覆盖率不达标原因", required = false)
    private List<Map<String, String>> codeCoverReason;

    @Override
    public String action() {
        return null;
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }
}
