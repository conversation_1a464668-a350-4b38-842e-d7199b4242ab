package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TestcaseGroupVO implements Serializable {

    private static final long serialVersionUID = -8010291261883366208L;

    /**
     * 分组name
     */
    private String parentName;

    /**
     * 上级code（分组code）
     */
    private String parentCode;

    /**
     *  分组路径
     */
    private String parentPath;

    /**
     * 当前分组用例codeList
     */
    private List<String> codeList;

}
