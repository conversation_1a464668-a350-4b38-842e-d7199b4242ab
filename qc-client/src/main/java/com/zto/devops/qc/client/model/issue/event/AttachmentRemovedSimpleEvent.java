package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.OperationTypeEnum;
import lombok.Data;

import java.util.Date;

@Data
public class AttachmentRemovedSimpleEvent extends BaseEvent implements ActionEvent {

    private String code;

    private String attachmentName;

    @Override
    public String action() {
        return OperationTypeEnum.REMOVEATTACHMENT.getValue();
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        if (attachmentName == null) {
            return "";
        }
        return attachmentName;
    }
}
