package com.zto.devops.qc.client.model.rpc.pipeline.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.ObservedEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.rpc.FlowStatusEnum;
import com.zto.devops.qc.client.enums.rpc.FlowTagEnum;
import com.zto.devops.qc.client.enums.rpc.PlanStatusEnum;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/9/10
 * @Version 1.0
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryDeployEndedEvent extends BaseEvent implements ActionEvent, ObservedEvent {
    private String code;
    private FlowStatusEnum status;
    private FlowTagEnum tag = FlowTagEnum.NORMAL;

    /**
     * 部署状态
     */
    private PlanStatusEnum planStatus;

    /**
     * 通过planCode查询部署相关信息，便于发送钉钉消息
     */
    private String planCode;
    private boolean retryOrFire;

    @Override
    public String action() {
        if (retryOrFire) {
            return "重试" + planStatus.getValue();
        }
        return "部署" + planStatus.getValue();
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return getOccurred();
    }

    @Override
    public String makeString() {
        return "";
    }
}
