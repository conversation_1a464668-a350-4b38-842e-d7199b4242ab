package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2022/7/27
 * @Version 1.0
 */
@Setter
@Getter
public abstract class IssuePlannedEvent extends BaseEvent implements ActionEvent {

    private String code;

    private String businessCode;

    private String businessName;

    @Override
    public String action() {
        return "规划版本";
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public String makeString() {
        if (businessName != null) {
            return businessName;
        }
        return "";
    }
}
