package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageAutomaticTaskGroupVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private Integer page;

    private Integer size;

    private Long total;

    private List<AutomaticTaskGroupVO> list;

    /**
     * 参数组装
     *
     * @param page
     * @param size
     * @param total
     * @param list
     * @return
     */
    public static PageAutomaticTaskGroupVO buildSelf(Integer page, Integer size, Long total, List<AutomaticTaskGroupVO> list) {
        return PageAutomaticTaskGroupVO.builder()
                .page(page)
                .size(size)
                .total(total)
                .list(list)
                .build();
    }
}
