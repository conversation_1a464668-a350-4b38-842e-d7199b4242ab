package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DeleteTestcaseCommand extends BaseCommand {

    public DeleteTestcaseCommand(String aggregateId) {
        super(aggregateId);
    }

    private String code;

    private TestcaseAttributeEnum attribute;

    private TestcaseTypeEnum type;
}
