package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class TestcaseDutyUserChangedEvent extends BaseEvent implements ActionEvent {

    private String code;

    private String dutyUser;

    private Long dutyUserId;

    @Override
    public String action() {
        return "变更了用例责任人";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return String.format("修改为 >>> %s", this.dutyUser);
    }
}
