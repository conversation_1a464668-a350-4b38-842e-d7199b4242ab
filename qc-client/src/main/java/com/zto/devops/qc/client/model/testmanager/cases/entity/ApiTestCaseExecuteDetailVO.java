package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@ZsmpModel(description = "查询api用例执行结果明细VO")
public class ApiTestCaseExecuteDetailVO implements Serializable {
    private static final long serialVersionUID = -9128617858819853552L;

    @ZsmpModelProperty(description = "自动化任务code")
    private String automaticTaskCode;

    @ZsmpModelProperty(description = "code")
    private String code;

    @ZsmpModelProperty(description = "名称")
    private String name;

    @ZsmpModelProperty(description = "用例类型（正常数据/异常数据/接口用例）")
    private ApiCaseTypeEnum caseType;

    @ZsmpModelProperty(description = "属性（模块/用例）")
    private TestcaseAttributeEnum attribute;

    @ZsmpModelProperty(description = "上级code")
    private String parentCode;

    @ZsmpModelProperty(description = "已启用/删除")
    private Boolean enable;

    @ZsmpModelProperty(description = "结果")
    private TestPlanCaseStatusEnum result;

    @ZsmpModelProperty(description = "结果描述")
    private String resultDesc;

    @ZsmpModelProperty(description = "执行人id")
    private Long executorId;

    @ZsmpModelProperty(description = "执行人")
    private String executor;

    @ZsmpModelProperty(description = "开始时间")
    private Date startTime;

    @ZsmpModelProperty(description = "结束时间")
    private Date finishTime;

    @ZsmpModelProperty(description = "报告")
    private String reportFile;

    @ZsmpModelProperty(description = "执行日志")
    private String execLogFile;

    @ZsmpModelProperty(description = "用例数量")
    private Integer testcaseCount;

    @ZsmpModelProperty(description = "子节点")
    private List<ApiTestCaseExecuteDetailVO> children;

    public void setResult(TestPlanCaseStatusEnum result) {
        this.result = result;
        if (null != result) {
            this.resultDesc = result.getValue();
        }
    }

    /**
     * 组装父用例
     *
     * @param voList {@link ApiTestCaseExecuteDetailTiledVO}
     * @return {@link ApiTestCaseExecuteDetailVO}
     */
    public static ApiTestCaseExecuteDetailVO buildSelf(List<ApiTestCaseExecuteDetailTiledVO> voList) {
        if (CollectionUtil.isEmpty(voList)) {
            return new ApiTestCaseExecuteDetailVO();
        }
        ApiTestCaseExecuteDetailTiledVO sample = voList.get(0);
        ApiTestCaseExecuteDetailVO parentApiTestCase = new ApiTestCaseExecuteDetailVO();
        parentApiTestCase.setCode(sample.getParentCode());
        parentApiTestCase.setName(sample.getParentName());
        parentApiTestCase.setAttribute(TestcaseAttributeEnum.MODULE);
        parentApiTestCase.setChildren(buildChildren(voList));
        parentApiTestCase.setTestcaseCount(voList.size());
        return parentApiTestCase;
    }

    /**
     * 组装子用例
     *
     * @param voList {@link ApiTestCaseExecuteDetailTiledVO}
     * @return {@link ApiTestCaseExecuteDetailVO}
     */
    private static List<ApiTestCaseExecuteDetailVO> buildChildren(List<ApiTestCaseExecuteDetailTiledVO> voList) {
        if (CollectionUtil.isEmpty(voList)) {
            return new ArrayList<>();
        }
        List<ApiTestCaseExecuteDetailVO> resultList = new ArrayList<>(voList.size());
        voList.forEach(vo -> {
            ApiTestCaseExecuteDetailVO result = new ApiTestCaseExecuteDetailVO();
            BeanUtils.copyProperties(vo, result);
            result.setCode(vo.getCaseCode());
            result.setName(vo.getCaseName());
            result.setCaseType(ApiCaseTypeEnum.codeOf(vo.getCaseTypeNum()));
            result.setAttribute(TestcaseAttributeEnum.TESTCASE);
            result.setEnable(vo.getEnable().equals(ApiCaseEnableEnum.DELETED.getCode()));
            resultList.add(result);
        });
        return resultList;
    }

}
