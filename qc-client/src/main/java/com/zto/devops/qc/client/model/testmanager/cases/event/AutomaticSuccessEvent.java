package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.BaseEvent;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AutomaticSuccessEvent extends BaseEvent {

    private String code;

    private Integer addCaseNo;

    private Integer updateCaseNo;

    private Integer deleteCaseNo;

    private String fileName;

    private String productCode;

    private String commitId;
}