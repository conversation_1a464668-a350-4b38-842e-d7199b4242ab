package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.Requirement;
import com.zto.devops.framework.client.simple.Sprint;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class IssueVersionEditedEvent extends BaseEvent implements ActionEvent {

    private String code;
    private String title;
    private String description;

    private IssueFindEnv findEnv;
    private IssueFindStage findStage;
    private IssuePriority priority;
    private IssueRepetitionRate repetitionRate;
    private IssueRootCause rootCause;
    private IssueTestMethod testMethod;
    private IssueType type;

    private User handler;
    private User developer;
    private User tester;
    private User modifier;

    private Requirement requirement;
    private RequirementLevel requirementLevel;
    private Version findVersion;
    private Version fixVersion;
    private Sprint sprint;

    private Date updateTime;

    private List<AttachmentVO> attachments;

    private List<TagVO> tags;

    private String versionConfirm;

    private Boolean removeFromVersion;
    private String currentVersionName;

    @Override
    public String action() {
        if (removeFromVersion == null) {
            return OperationTypeEnum.EDITEDISSUE.getValue();
        }
        if (removeFromVersion) {
            return OperationTypeEnum.REMOVEFROMVERSION.getValue();
        }
        return OperationTypeEnum.PLANINTOVERSION.getValue();
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        if (currentVersionName != null) {
            return currentVersionName;
        }
        return "";
    }

}
