package com.zto.devops.qc.client.model.parameter;

import com.zto.devops.qc.client.enums.issue.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @author: minjd
 * @date: Created in 2021/11/2 18:25
 */
@Data
public class IssueQueryParameter implements Serializable {

    private static final long serialVersionUID = 1L;


    private String codeOrTitle;

    private Long currentUserId;

    private List<IssueStatus> statusList;

    private List<IssuePriority> priorityList;

    private List<RelatedToMeEnum> relatedList;

    private List<Long> handleUserIdList;

    private List<Long> developUserIdList;

    private List<Long> testUserIdList;

    private List<String> findVersionList;

    private List<String> fixVersionList;

    private List<String> fixOrFindVersionList;

    private List<String> relatedRequireList;

    private List<String> relatedProductList;

    private List<String> sprintCode;

    private List<IssueRootCause> rootCauseList;

    private List<IssueType> issueTypeList;

    private List<IssueTestMethod> testMethodList;

    private List<IssueRepetitionRate> repetitionRateList;

    private List<IssueFindStage> findStageList;

    private List<IssueFindEnv> findEnvList;

    private List<Long> findUserIdList;

    private List<String> tagName;

    private Date createTimeStart;

    private Date createTimeEnd;

    private Date closeTimeStart;

    private Date closeTimeEnd;

    private Boolean fixVersionIsNull;

    private Date gmtModifiedStart;

    private Date gmtModifiedEnd;

    private List<String> versionConfirm;

    private List<String> issueCodeList;
    private List<String> tagList;
    private List<Long> updateUserIdList;

    private List<Boolean> testOmission;
    /**
     * 代码缺陷  1为是 0为否
     */
    private List<Boolean> codeDefect;

    private String testOmissionVersion;
    private String codeDefectVersion;

    private List<Boolean> examination;

    private List<Long> modifierId;

    private List<RefuseReason> refuseReasonList;

    private List<IssueApplicationType> applicationTypeList;

    private String versionType;

    private Long CCUserId;

    private RelevantUserTypeEnum type;

    private String orderField;

    private String orderType;

    private List<Long> ccUserIdList;

    private Boolean validFlag;
}
