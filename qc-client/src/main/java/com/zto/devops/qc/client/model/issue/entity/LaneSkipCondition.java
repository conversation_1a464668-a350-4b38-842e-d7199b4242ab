package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;


import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.enums.issue.LaneStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 泳道之间跳转的条件
 * @Date 2023/2/7
 * @Version 1.0
 */
@Data
@AllArgsConstructor
public class LaneSkipCondition implements Serializable {

    private static final long serialVersionUID = 1L;


    // 可以跳转的泳道
    private LaneStatusEnum laneKey;

    // 需要触发的动作
    private Button action;
}
