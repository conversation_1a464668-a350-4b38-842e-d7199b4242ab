package com.zto.devops.qc.client.model.report.query;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class RelatedQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "版本编号", required = true)
    private String versionCode;

    @GatewayModelProperty(description = "计划的类型;", required = false)
    private String reportType;

    @GatewayModelProperty(description = "计划的类型;", required = false)
    private String planType;

}
