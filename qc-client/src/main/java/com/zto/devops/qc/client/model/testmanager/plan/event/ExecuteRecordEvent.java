package com.zto.devops.qc.client.model.testmanager.plan.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Map;

@Setter
@Getter
public class ExecuteRecordEvent extends BaseEvent implements ActionEvent {

    private String code;

    private String testPlanCode;

    private TestPlanStageEnum testStage;

    private TestPlanCaseStatusEnum result;

    @Override
    public String action() {
        return "修改用例执行结果";
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return String.format("修改为 >>> %s", TestPlanCaseStatusEnum.getDesc(this.result));
    }

    @Override
    public Map<String, ?> metadata() {
        return ActionEvent.super.metadata();
    }

    @Override
    public String logBusinessCode() {
        return this.code;
    }
}
