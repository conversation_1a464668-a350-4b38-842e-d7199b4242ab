package com.zto.devops.qc.client.model.message;

import com.zto.devops.framework.client.simple.BasePayload;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TestPlanOrReportMsg extends BasePayload {
    private String subject;
    private String sender;
    private String copyPerson;
    private String receiver;
    private String content;
    // 附件，list to json
    private Object attachment;



    //{
    //"subject": "${subject}",
    //"sender": "${sender}",
    //"copyPerson": "${copyPerson!''}",
    //"receiver": "${receiver}",
    //"content": "${content}",
    //"attachment": <#if attachment?exists><@listToString list = attachment /><#else>''</#if>
    //}
        // EmailAttachment emailAttachment = new EmailAttachment();
    //        emailAttachment.setAttachmentName("fileName");
    //        emailAttachment.setAttachmentURL("url");
    //        emailAttachment.setGroup("group");
    //        emailAttachment.setContentId("内嵌文件标识id");
    //        messageCO.putPayload("attachment", Arrays.asList(emailAttachment));

    //subject：邮件标题（必传）
    //
    //sender：发送人邮箱（必传）
    //
    //copyPerson：抄送人邮箱（非必传，多个用“,”隔开）
    //
    //receiver：收件人邮箱（必传）
    //
    //content：邮件内容
    //attachment:邮箱附件（N）
    //http://w.ztosys.com/134935843
}
