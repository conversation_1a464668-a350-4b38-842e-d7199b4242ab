package com.zto.devops.qc.client.model.testmanager.plan.entity;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class TmTestPlanReportVO implements Serializable {
    private static final long serialVersionUID = -1591348458280602683L;

    @GatewayModelProperty(description = "报告编号", required = false)
    private String code;

    @GatewayModelProperty(description = "报告名称", required = true)
    private String name;

    public TmTestPlanReportVO(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
