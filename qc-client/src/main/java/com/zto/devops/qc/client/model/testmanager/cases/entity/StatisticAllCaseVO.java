package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "版本详情-统计所有状态测试用例个数")
public class StatisticAllCaseVO implements Serializable {
    @ZsmpModelProperty(description = "总数")
    private Integer totalNum;

    @ZsmpModelProperty(description = "通过测试用例")
    private StatisticThingVO passTestCase;

    @ZsmpModelProperty(description = "失败用例")
    private StatisticThingVO failTestCase;

    @ZsmpModelProperty(description = "受阻用例")
    private StatisticThingVO blockingTestCase;

    @ZsmpModelProperty(description = "跳过用例")
    private StatisticThingVO jumpOverTestCase;

    @ZsmpModelProperty(description = "未测试用例")
    private StatisticThingVO notTestCase;
}
