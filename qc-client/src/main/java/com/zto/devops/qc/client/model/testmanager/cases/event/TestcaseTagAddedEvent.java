package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.TagTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class TestcaseTagAddedEvent extends BaseEvent implements ActionEvent {

    private String code;

    private DomainEnum domain;

    private String businessCode;

    private TagTypeEnum type;

    private String tagName;

    @Override
    public String action() {
        return "添加了标签";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return this.tagName;
    }

    @Override
    public String logBusinessCode() {
        return this.businessCode;
    }
}
