package com.zto.devops.qc.client.model.rpc.pipeline;

import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.qc.client.enums.rpc.EnvEnum;
import com.zto.devops.qc.client.enums.rpc.FlowLaneTypeEnum;
import com.zto.devops.qc.client.enums.rpc.NamespaceTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class JacocoApplicationVO implements Serializable {
    private static final long serialVersionUID = -3084343876480798842L;

    private String code;

    private String appId;

    private String branchName;

    private String commitId;

    private String packageName;

    private String gitUrl;

    private Integer port;

    private String namespaceName;

    /**
     * 环境
     */
    private EnvEnum env;

    /**
     * 空间类型
     */
    private NamespaceTypeEnum type;

    /**
     * planCode
     */
    private String deployCode;

    private List<Version> versions;

    private List<JacocoInstanceVO> instances;

    private String outputFileName;

    /**
     * 发布泳道
     */
    private FlowLaneTypeEnum flowLaneType;
}
