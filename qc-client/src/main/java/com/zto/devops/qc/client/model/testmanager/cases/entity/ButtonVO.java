package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

/**
 * 项目名称：project-parent
 * 类 名 称：ButtonVo
 * 类 描 述：TODO
 * 创建时间：2021/8/5 9:56 上午
 * 创 建 人：bulecat
 */
@Data
public class ButtonVO implements Comparable<ButtonVO>, Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "按钮名称")
    private String name;

    @GatewayModelProperty(description = "按钮code")
    private String code;

    @GatewayModelProperty(description = "排序值")
    private Integer index;

    public ButtonVO(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public ButtonVO(String name, String code, Integer index) {
        this.name = name;
        this.code = code;
        this.index = index;
    }


    public ButtonVO() {
    }

    @Override
    public int compareTo(ButtonVO o) {
        return index.compareTo(o.getIndex());
    }
}
