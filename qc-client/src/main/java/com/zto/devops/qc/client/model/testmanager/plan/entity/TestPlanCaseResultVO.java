package com.zto.devops.qc.client.model.testmanager.plan.entity;


import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@GatewayModel(description = "测试计划关联用例-执行结果统计")
public class TestPlanCaseResultVO implements Serializable {

    @GatewayModelProperty(description = "总用例", sample = "10", required = false)
    private Integer totalCount;
    //逻辑删除的用例看删除前的执行结果
    @GatewayModelProperty(description = "通过率", sample = "10%", required = false)
    private String passRate;

    @GatewayModelProperty(description = "已测", sample = "10", required = false)
    private Integer doneCount;

    @GatewayModelProperty(description = "通过", sample = "10", required = false)
    private Integer passedCount;

    @GatewayModelProperty(description = "未测(包含自动化的待执行,执行中)", sample = "10", required = false)
    private Integer initialCount;

    @GatewayModelProperty(description = "失败", sample = "10", required = false)
    private Integer failedCount;

    @GatewayModelProperty(description = "阻塞", sample = "10", required = false)
    private Integer blockCount;

    @GatewayModelProperty(description = "跳过", sample = "10", required = false)
    private Integer skipCount;

}
