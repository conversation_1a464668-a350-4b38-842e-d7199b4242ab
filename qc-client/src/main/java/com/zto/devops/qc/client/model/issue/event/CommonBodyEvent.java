package com.zto.devops.qc.client.model.issue.event;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonBodyEvent implements Serializable {

    private String messageCode;

    private String code;

    private Long userId;

    private Date occurred;

    private String eventAction;

    private String status;
    
}
