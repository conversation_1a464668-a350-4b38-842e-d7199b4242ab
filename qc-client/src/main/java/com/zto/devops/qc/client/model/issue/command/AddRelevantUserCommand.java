package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Setter
@Getter
public class AddRelevantUserCommand extends BaseCommand {
    public AddRelevantUserCommand(String aggregateId) {
        super(aggregateId);
    }
    private RelevantUserTypeEnum type;
    private String businessCode;
    private String code;
    private List<User> userList;
    private DomainEnum domain;
}
