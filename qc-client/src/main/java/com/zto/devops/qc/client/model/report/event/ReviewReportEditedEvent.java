package com.zto.devops.qc.client.model.report.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.report.entity.ReviewInfoDTO;
import com.zto.devops.qc.client.model.report.entity.ReviewOpinionVO;
import com.zto.devops.qc.client.model.report.entity.ReviewRenewalVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ReviewReportEditedEvent extends ReportAddedEvent implements ActionEvent {

    private ReviewInfoDTO reviewInfo;

    private List<ReviewOpinionVO> reviewOpinions;

    private List<ReviewRenewalVO> reviewRenewals;



    @Override
    public String action() {
        return "";
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        return "";
    }
}
