package com.zto.devops.qc.client.model.issue.entity;

import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: minjd
 * @date: Created in 2021/9/15 10:16
 */
@Data
public class IssueBaseVO implements Serializable {

    private String code;

    private String title;

    private IssueStatus status;

    private String sprintCode;

    private IssuePriority priority;

    private String creator;

    private Date gmtCreate;

    private Long creatorId;
    private String handleUserName;
    private String handleUserId;


}
