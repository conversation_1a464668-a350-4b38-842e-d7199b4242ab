package com.zto.devops.qc.client.model.rpc.pipeline;

import java.io.Serializable;

public class VersionSimpleVO implements Serializable {
    private String versionCode;
    private String name;
    private String versionNum;
    private String tag;

    public String getVersionCode() {
        return this.versionCode;
    }

    public String getName() {
        return this.name;
    }

    public String getVersionNum() {
        return this.versionNum;
    }

    public String getTag() {
        return this.tag;
    }

    public void setVersionCode(final String versionCode) {
        this.versionCode = versionCode;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setVersionNum(final String versionNum) {
        this.versionNum = versionNum;
    }

    public void setTag(final String tag) {
        this.tag = tag;
    }


    public String toString() {
        return "VersionSimpleVO(versionCode=" + this.getVersionCode() + ", name=" + this.getName() + ", versionNum=" + this.getVersionNum() + ", tag=" + this.getTag() + ")";
    }

    public VersionSimpleVO() {
    }
}

