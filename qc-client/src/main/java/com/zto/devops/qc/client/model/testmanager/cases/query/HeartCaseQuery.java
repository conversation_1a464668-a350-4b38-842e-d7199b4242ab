package com.zto.devops.qc.client.model.testmanager.cases.query;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class HeartCaseQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    public HeartCaseQuery(String caseCode) {
        this.caseCode = caseCode;
    }

    @GatewayModelProperty(description = "用例code")
    private String caseCode;

}
