package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.qc.client.enums.issue.Reason;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ValidatedAccessClosedCommand extends IssueBaseCommand {

    public ValidatedAccessClosedCommand(String aggregateId) {
        super(aggregateId);
    }
    private String fixVersionCode;
    private String fixVersion;
    private String content;
    private Reason reason;
}
