package com.zto.devops.qc.client.model.report.entity;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReviewReportDetailVO extends BaseReportInfoVO {

    @GatewayModelProperty(description = "评审信息", required = false)
    private ReviewInfoUserHandleVO reviewInfo;

    @GatewayModelProperty(description = "评审观点", required = false)
    private List<ReviewOpinionVO> reviewOpinions;

    @GatewayModelProperty(description = "评审更新信息", required = false)
    private List<ReviewRenewalVO> reviewRenewals;


}
