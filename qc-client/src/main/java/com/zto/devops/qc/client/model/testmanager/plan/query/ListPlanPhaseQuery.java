package com.zto.devops.qc.client.model.testmanager.plan.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class ListPlanPhaseQuery extends PageQueryBase implements Serializable {

    private String productCode;

    private TestcaseTypeEnum caseType;

    private String versionCode;

    private String planName;
}
