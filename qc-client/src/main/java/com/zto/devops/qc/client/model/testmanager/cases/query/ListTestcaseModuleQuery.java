package com.zto.devops.qc.client.model.testmanager.cases.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ListTestcaseModuleQuery extends BaseQuery {

    private TestcaseTypeEnum type;

    private String productCode;

    private Boolean planPattern;

    private String planCode;

    private TestPlanStageEnum testStage;

    private Boolean menuPattern;

    private String versionCode;

    private Boolean setCore;

    private Boolean factoryPattern;

    /**
     * 场景顶级分组code
     */
    private String sceneTopModuleCode;
}
