package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentDocumentTypeEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentFileTypeEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AddTestcaseAttachmentCommand extends BaseCommand {

    private DomainEnum domain;

    private String businessCode;

    private String url;

    private String remoteFileId;

    private String name;

    private AttachmentTypeEnum type;

    private AttachmentDocumentTypeEnum documentType;

    private AttachmentFileTypeEnum fileType;

    private String size;

    private String operateCaseCode;

    public AddTestcaseAttachmentCommand(String aggregateId) {
        super(aggregateId);
    }
}
