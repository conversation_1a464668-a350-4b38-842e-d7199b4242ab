package com.zto.devops.qc.client.model.report.entity;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AccessReportDetailVO extends BaseReportInfoVO {

    @GatewayModelProperty(description = "用例执行明细", required = false)
    private List<CaseDetailVO> caseDetailVOS;

    @GatewayModelProperty(description = "用例信息统计", required = false)
    private List<CaseStatisticsVO> caseStatisticVOS;

    @GatewayModelProperty(description = "缺陷信息统计", required = false)
    private List<IssueInfoVO> issueInfoVOS;

}
