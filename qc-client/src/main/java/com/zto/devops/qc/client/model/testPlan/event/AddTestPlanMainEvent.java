package com.zto.devops.qc.client.model.testPlan.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testPlan.entity.TestFunctionPointVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AddTestPlanMainEvent extends BaseEvent {
    private String code;
    private String testPlanCode;
    private String versionCode;
    private String versionName;
    private Integer developerNo;
    private Integer testerNo;
    private Long productDirectorId;
    private String productDirectorName;
    private Boolean mobileSpecialTest;
    private Boolean integrateTest;
    private Boolean securityScan;
    private Boolean staticAnalysis;
    private Boolean performanceTest;
    private Date performanceTestTime;
    private Long performanceTestDirectorId;
    private String performanceTestDirectorName;
    private Boolean exploratoryTest;
    private Long exploratoryTestDirectorId;
    private String exploratoryTestDirectorName;
    private Date exploratoryTestTime;
    private TestPlanStatusEnum status;
    private Long staticAnalysisDirectorId;
    private String planName;
    private String staticAnalysisDirectorName;

    private Date staticAnalysisTime;

    private List<TestFunctionPointVO> pointList;

    private List<SendUserInfoVO> recipients;

    //抄送人
    private List<SendUserInfoVO> ccUsers;

    private String remarks;

    @GatewayModelProperty(description = "版本开始时间", required = false)
    private Date startDate;


    @GatewayModelProperty(description = "版本上线时间", required = false)
    private Date publishDate;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date presentationDate;
    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date approvalExitDate;

    private String type;
}
