package com.zto.devops.qc.client.model.issue.query;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.issue.IssueTestMethod;
import lombok.Data;

import java.util.List;

@Data
public class IssueLegacyQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    private String businessCode;

    /**
     * 匹配的测试方法
     */
    private List<IssueTestMethod> issueTestMethodList;
    /**
     * 过滤的测试方法
     */
    private List<IssueTestMethod> filterIssueTestMethodList;

    public IssueLegacyQuery(String businessCode) {
        super();
        this.businessCode = businessCode;
    }

    public IssueLegacyQuery() {
    }
}
