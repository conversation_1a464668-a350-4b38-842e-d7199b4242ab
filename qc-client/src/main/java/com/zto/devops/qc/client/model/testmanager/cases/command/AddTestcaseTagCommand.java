package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.enums.issue.TagTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AddTestcaseTagCommand extends BaseCommand {

    private String businessCode;

    private String tagName;

    private DomainEnum domain;

    private TagTypeEnum type;

    public AddTestcaseTagCommand(String aggregateId) {
        super(aggregateId);
    }
}
