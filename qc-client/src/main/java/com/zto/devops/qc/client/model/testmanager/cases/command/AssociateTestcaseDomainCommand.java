package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.enums.DomainEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class AssociateTestcaseDomainCommand extends BaseCommand {

    private DomainEnum domain;

    private List<String> codeList;

    private String operateCaseCode;

    public AssociateTestcaseDomainCommand(String aggregateId) {
        super(aggregateId);
    }
}
