package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.qc.client.enums.issue.Reason;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CirculationIssueCommand extends IssueBaseCommand {

    public CirculationIssueCommand(String aggregateId) {
        super(aggregateId);
    }
    private String content;

    private Long developId;
    private String developName;
    private Long testId;
    private String testName;
    private Reason reason;
}
