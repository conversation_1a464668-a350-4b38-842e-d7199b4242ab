package com.zto.devops.qc.client.model.testmanager.plan.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.plan.PlanTipEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2022/10/20
 * @Version 1.0
 */
@Data
public class TestPlanTipVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "提示的内容")
    private String tip;

    @GatewayModelProperty(description = "图片的名称")
    private String planTipTitle;

    @GatewayModelProperty(description = "图片类型")
    private PlanTipEnum planTipEnum;
}
