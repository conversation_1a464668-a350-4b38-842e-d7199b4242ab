package com.zto.devops.qc.client.model.report.entity;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：SendResultResp
 * 类 描 述：TODO
 * 创建时间：2021/11/11 2:02 下午
 * 创 建 人：bulecat
 * <AUTHOR>
 */
@Data
public class SendResultVO implements Serializable {

    @GatewayModelProperty(description = "抄送人", required = false)
    private List<QcUserNoticeVO> ccNotice;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<QcUserNoticeVO> recipientNotice;
}
