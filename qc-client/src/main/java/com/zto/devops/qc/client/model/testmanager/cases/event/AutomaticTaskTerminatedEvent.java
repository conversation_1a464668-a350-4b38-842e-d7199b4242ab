package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AutomaticTaskTerminatedEvent extends BaseEvent {

    private String code;

    private String buildId;

    private String taskId;

    private AutomaticRecordTypeEnum type;

    private String productCode;

    private String testPlanCode;

    private TestPlanStageEnum testStage;

    private AutomaticTaskTrigModeEnum trigMode;
}
