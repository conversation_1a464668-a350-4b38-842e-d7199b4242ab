package com.zto.devops.qc.client.model.testmanager.plan.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class PlanCaseResultCommentChangedEvent extends BaseEvent implements ActionEvent {

    private String planCode;

    private TestPlanStageEnum testStage;

    private String caseCode;

    private String resultComment;

    private String operateCaseCode;

    @Override
    public String action() {
        return "修改了结果备注";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return this.resultComment;
    }

    @Override
    public String logBusinessCode() {
        return this.operateCaseCode;
    }
}
