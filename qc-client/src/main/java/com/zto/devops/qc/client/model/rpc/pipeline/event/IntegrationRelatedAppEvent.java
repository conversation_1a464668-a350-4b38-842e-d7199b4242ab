package com.zto.devops.qc.client.model.rpc.pipeline.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.rpc.pipeline.BaseApplicationDO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: integration已关联应用事件
 * @Author: cher
 * @Date: 2021/9/6 10:49
 **/
@Getter
@Setter
public class IntegrationRelatedAppEvent extends FlowChangeStatusEvent implements ActionEvent {
    /**
     * 是否第一次关联应用
     */
    private Boolean first;

    /**
     * 功能分支名
     */
    private String featureBranch;

    /**
     * 关联的应用编码List
     */
    private List<String> applicationCodeList;

    /**
     * 关联的应用
     */
    List<BaseApplicationDO> applications;

    /**
     * 是否需要代码审查
     */
    private Boolean codeReview = false;

    /**
     * 版本编码
     */
    private String versionCode;

    @Override
    public String action() {
        if (Objects.nonNull(getFlowEvent())) {
            return getFlowEvent().getValue();
        }
        return null;
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return getOccurred();
    }

    @Override
    public String makeString() {
        if (applicationCodeList == null || applicationCodeList.size() <= 0) {
            return "取消所有关联应用";
        }
        if (applications != null && applications.size() > 0) {
            return applications.stream().map(BaseApplicationDO::getName).collect(Collectors.joining("、"));
        }
        return "";
    }
}
