package com.zto.devops.qc.client.model.issue.query;

import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class FindIssueByVersionCodeQuery implements Serializable {
    @GatewayModelProperty(description = "版本编码", sample = "", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "next节点", sample = "", required = false)
    private IssueStatus nextStatus;
}
