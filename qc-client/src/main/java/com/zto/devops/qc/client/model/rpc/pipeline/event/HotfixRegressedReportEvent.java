package com.zto.devops.qc.client.model.rpc.pipeline.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
public class HotfixRegressedReportEvent extends FlowChangeStatusEvent implements ActionEvent {
    /**
     * 回归结果
     */
    private boolean result;

    @Override
    public String action() {
        if (Objects.nonNull(getFlowEvent())) {
            return getFlowEvent().getValue();
        }
        return null;
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return getOccurred();
    }

    @Override
    public String makeString() {
        return "";
    }
}
