package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.issue.IssueStatus;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/20
 * @Version 1.0
 */
@Data
public class LaneIssueCtxVO extends LaneCtxVO<LaneIssueMatterVO, LaneIssueMapCondition, IssueStatus> implements Serializable {

    private static final long serialVersionUID = 1L;

    public LaneIssueCtxVO(List<LaneIssueMatterVO> list, List<IssueStatus> allStatus, LaneIssueMapCondition condition) {
        super(list, allStatus, condition);
    }

    @Override
    protected BaseLaneMatterStatusMap<LaneIssueMapCondition> getLaneStatusMap(LaneIssueMapCondition laneIssueMapCondition) {
        return new LaneIssueMap(laneIssueMapCondition);
    }
}
