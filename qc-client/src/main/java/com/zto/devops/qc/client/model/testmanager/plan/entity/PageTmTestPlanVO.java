package com.zto.devops.qc.client.model.testmanager.plan.entity;

import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.enums.testmanager.plan.*;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ZModel(description = "分页查询测试计划VO")
public class PageTmTestPlanVO implements Serializable {
    private static final long serialVersionUID = -2403778233999620255L;

    private Long total;

    private List<TmTestPlanVO> planList;

    public static PageTmTestPlanVO buildSelf(List<TmTestPlanVO> planList, Long total) {
        return PageTmTestPlanVO.builder()
                .planList(planList)
                .total(Objects.isNull(total) ? 0L : total)
                .build();
    }
}
