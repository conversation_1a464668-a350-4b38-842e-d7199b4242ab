package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Data
@ZsmpModel(description = "自动化任务执行记录响应模型(父任务分组)")
public class AutomaticTaskGroupVO implements Serializable {
    private static final long serialVersionUID = 9177561458939182596L;

    @ZsmpModelProperty(description = "接口code")
    private String apiCode;

    @ZsmpModelProperty(description = "父任务id")
    private String parentTaskId;

    @ZsmpModelProperty(description = "是否api用例（true：是;false：否）")
    private Boolean apiCaseFlag;

    @ZsmpModelProperty(description = "定时任务code")
    private String schedulerCode;

    @ZsmpModelProperty(description = "定时任务name")
    private String schedulerName;

    @ZsmpModelProperty(description = "是否是父任务（true：是;false：否）")
    private Boolean parentFlag;

    @ZsmpModelProperty(description = "自动化任务id")
    private String taskId;

    @ZsmpModelProperty(description = "自动化任务结果")
    private AutomaticStatusEnum status;

    @ZsmpModelProperty(description = "自动化任务结果描述")
    private String statusDesc;

    @ZsmpModelProperty(description = "用例总数")
    private Integer totalCount;

    @ZsmpModelProperty(description = "用例执行结果VOS")
    private List<CountCaseStatusVO> caseStatusVOS;

    @ZsmpModelProperty(description = "版本code")
    private String versionCode;

    @ZsmpModelProperty(description = "版本名称")
    private String versionName;

    @ZsmpModelProperty(description = "测试计划编码")
    private String testPlanCode;

    @ZsmpModelProperty(description = "测试计划名称")
    private String testPlanName;

    @ZsmpModelProperty(description = "测试阶段")
    private TestPlanStageEnum testStage;

    @ZsmpModelProperty(description = "测试阶段描述")
    private String testStageDesc;

    @ZsmpModelProperty(description = "触发方式")
    private AutomaticTaskTrigModeEnum trigMode;

    @ZsmpModelProperty(description = "触发方式描述")
    private String trigModeDesc;

    @ZsmpModelProperty(description = "开始时间")
    private Date startTime;

    @ZsmpModelProperty(description = "结束时间")
    private Date finishTime;

    @ZsmpModelProperty(description = "执行人id")
    private Long executorId;

    @ZsmpModelProperty(description = "执行人")
    private String executor;

    @ZsmpModelProperty(description = "终止人id")
    private Long stopUserId;

    @ZsmpModelProperty(description = "终止人")
    private String stopUser;

    @ZsmpModelProperty(description = "子任务")
    private List<AutomaticChildTaskVO> children;

    @ZsmpModelProperty(description = "执行空间")
    private String env;

    /**
     * 父任务状态
     *
     * @return {@link AutomaticStatusEnum}
     */
    public AutomaticStatusEnum getStatus() {
        if (null == this.getChildren()) {
            this.status = AutomaticStatusEnum.UNKNOWN;
            return this.status;
        }
        List<AutomaticStatusEnum> statusList = children.stream().map(AutomaticChildTaskVO::getStatus).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(statusList)) {
            this.status = AutomaticStatusEnum.UNKNOWN;
            return this.status;
        }
        //1）已终止：有一个子任务是终止，则父任务为终止
        if (statusList.contains(AutomaticStatusEnum.TERMINATION)) {
            this.status = AutomaticStatusEnum.TERMINATION;
            return this.status;
        }
        //2）排队中：没有终止的子任务时，有一个为排队中则状态为排队中
        if (statusList.contains(AutomaticStatusEnum.NOT_STARTED)) {
            this.status = AutomaticStatusEnum.NOT_STARTED;
            return this.status;
        }
        //3）待执行：没有终止与排队中任务时，有一个任务为待执行则为待执行
        if (statusList.contains(AutomaticStatusEnum.SUBMITTED)) {
            this.status = AutomaticStatusEnum.SUBMITTED;
            return this.status;
        }
        //4）执行中：在没有终止/排队/待执行的情况下，有一个任务为执行中，都为执行中
        if (statusList.contains(AutomaticStatusEnum.IN_PROGRESS)) {
            this.status = AutomaticStatusEnum.IN_PROGRESS;
            return this.status;
        }
        //5）成功：子任务全部执行完，且全部成功为成功
        if (statusList.size() == 1 && statusList.contains(AutomaticStatusEnum.SUCCESS)) {
            this.status = AutomaticStatusEnum.SUCCESS;
            return this.status;
        }
        //6）错误：子任务全部执行完，有一个错误，则为错误
        if (statusList.contains(AutomaticStatusEnum.ERROR)) {
            this.status = AutomaticStatusEnum.ERROR;
            return this.status;
        }
        //7）失败：子任务全部执行完，没有错误，有一个为失败就是失败
        if (statusList.contains(AutomaticStatusEnum.FAIL)) {
            this.status = AutomaticStatusEnum.FAIL;
            return this.status;
        }
        return this.status;
    }

    /**
     * 父任务状态描述
     *
     * @return
     */
    public String getStatusDesc() {
        this.statusDesc = null != this.status ?
                this.status.equals(AutomaticStatusEnum.NOT_STARTED)
                        ? "排队中"
                        : this.status.getDesc()
                : "";
        return this.statusDesc;
    }

    /**
     * 用例总数-子任务用例数之和
     *
     * @return
     */
    public Integer getTotalCount() {
        if (CollectionUtils.isNotEmpty(this.caseStatusVOS)) {
            this.totalCount = this.caseStatusVOS.stream().mapToInt(CountCaseStatusVO::getNum).sum();
        }
        return this.totalCount;
    }

    /**
     * 用例状态分组-子任务用例分组合并
     *
     * @return
     */
    public List<CountCaseStatusVO> getCaseStatusVOS() {
        //收集用例数据
        if (null == this.getChildren()) {
            return null;
        }
        List<CountCaseStatusVO> voList = new ArrayList<>();
        this.getChildren().forEach(child -> {
            voList.addAll(child.getCaseStatusVOS());
        });
        if (CollectionUtils.isEmpty(voList)) {
            return null;
        }

        //按状态分组统计
        List<CountCaseStatusVO> resultList = new ArrayList<>();
        Map<TestPlanCaseStatusEnum, List<CountCaseStatusVO>> map = voList.stream().collect(Collectors.groupingBy(CountCaseStatusVO::getStatus));
        map.forEach((status, list) -> {
            resultList.add(CountCaseStatusVO.builder().status(status).num(list.stream().mapToInt(CountCaseStatusVO::getNum).sum()).build());
        });
        this.caseStatusVOS = resultList;
        return this.caseStatusVOS;
    }

    public void setTestStage(TestPlanStageEnum testStage) {
        this.testStage = testStage;
        if (null != testStage) {
            this.testStageDesc = testStage.getValue();
        }
    }

    public void setTrigMode(AutomaticTaskTrigModeEnum trigMode) {
        this.trigMode = trigMode;
        if (null != trigMode) {
            this.trigModeDesc = trigMode.getDesc();
        }
    }

    /**
     * 组装参数
     *
     * @param list {@link AutomaticTaskVO}
     * @return
     */
    public static List<AutomaticTaskGroupVO> buildListByGroup(List<AutomaticTaskVO> list) {
        List<AutomaticTaskGroupVO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return resultList;
        }
        Map<String, List<AutomaticTaskVO>> map = list.stream().collect(Collectors.groupingBy(AutomaticTaskVO::getTaskId, LinkedHashMap::new, Collectors.toList()));
        map.forEach((key, value) -> {
            if (CollectionUtils.isNotEmpty(value)) {
                List<AutomaticChildTaskVO> children = new ArrayList<>();
                Map<String, List<AutomaticTaskVO>> childrenMap;
                if (value.get(0).getSingleTestFlag()) {
                    childrenMap = value.stream().collect(Collectors.groupingBy(AutomaticTaskVO::getApiCode));
                } else {
                    childrenMap = value.stream().collect(Collectors.groupingBy(AutomaticTaskVO::getCode));
                }
                childrenMap.forEach((code, voList) -> {
                    if (CollectionUtils.isNotEmpty(voList)) {
                        children.add(AutomaticChildTaskVO.buildSelf(voList));
                    }
                });
                resultList.add(AutomaticTaskGroupVO.buildByChildren(children));
            }
        });
        return resultList;
    }

    /**
     * 根据子任务组装参数
     *
     * @param children 子任务列表
     * @return {@link AutomaticTaskGroupVO}
     */
    private static AutomaticTaskGroupVO buildByChildren(List<AutomaticChildTaskVO> children) {
        children = children.stream()
                .sorted(Comparator.comparing(AutomaticChildTaskVO::getStartTime, Comparator.nullsFirst(Comparator.naturalOrder())))
                .collect(Collectors.toList());
        AutomaticChildTaskVO child = children.get(0);
        AutomaticTaskGroupVO vo = new AutomaticTaskGroupVO();
        BeanUtils.copyProperties(child, vo);
        vo.setParentFlag(true);
        vo.setChildren(children);
        if (vo.getStatus().equals(AutomaticStatusEnum.NOT_STARTED)
                || vo.getStatus().equals(AutomaticStatusEnum.IN_PROGRESS)
                || vo.getStatus().equals(AutomaticStatusEnum.SUBMITTED)) {
            vo.setFinishTime(null);
        } else {
            //结束时间：子任务中最晚结束时间，如有未终结子任务，则为空
            List<AutomaticChildTaskVO> timeSortList = children.stream()
                    .filter(item -> (null != item.getFinishTime()))
                    .sorted(Comparator.comparing(AutomaticChildTaskVO::getFinishTime))
                    .collect(Collectors.toList());
            vo.setFinishTime(timeSortList.get(timeSortList.size() - 1).getFinishTime());
            //是否自动终止 ： 以最后更新的子任务，状态为准
            vo.setStopUser(timeSortList.get(timeSortList.size() - 1).getStopUser());
        }
        return vo;
    }
}
