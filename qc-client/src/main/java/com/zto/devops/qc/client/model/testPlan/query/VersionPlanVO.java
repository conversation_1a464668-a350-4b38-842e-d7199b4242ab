package com.zto.devops.qc.client.model.testPlan.query;

import com.zto.devops.qc.client.enums.testPlan.TestPlanTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class VersionPlanVO implements Serializable {

    @GatewayModelProperty(description = "编号", required = false)
    private String code;

    @GatewayModelProperty(description = "产品code", required = false)
    private String productCode;

    @GatewayModelProperty(description = "版本code", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "计划名", required = false)
    private String planName;

    @GatewayModelProperty(description = "类型", required = false)
    private TestPlanTypeEnum type;

    @GatewayModelProperty(description = "类型 描述", required = false)
    private String typeDesc;

    @GatewayModelProperty(description = "计划编号", required = false)
    private String planCode;

    @GatewayModelProperty(description = "测试负责人id", required = false)
    private Long testDirectorId;

    @GatewayModelProperty(description = "测试负责人名", required = false)
    private String testDirectorName;

    @GatewayModelProperty(description = "创建时间")
    private Date gmtCreate;
    @GatewayModelProperty(description = "修改时间")
    private Date gmtModified;
}
