package com.zto.devops.qc.client.model.report.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExcelModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目名称：qc-parent
 * 类 名 称：PlanIntegrateResp
 * 类 描 述：TODO
 * 创建时间：2021/11/24 3:10 下午
 * 创 建 人：bulecat
 * <AUTHOR>
 */
@Data
public class PlanIntegrateResp implements Serializable, ExcelModel {

/*
    @ExcelProperty("编号")
    @GatewayModelProperty(description = "编号", required = false)
    private String number;*/

    @ExcelProperty("用例描述")
    @GatewayModelProperty(description = "用例描述", required = false)
    private String functionPoint;

    @Override
    public boolean isBeanNone() {
        return StringUtil.isBlank(this.functionPoint);
    }
}
