package com.zto.devops.qc.client.model.report.command;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.CaseResultVO;
import com.zto.devops.qc.client.model.report.entity.IssueInfoVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：AddOnlineSmokeReportReq
 * 类 描 述：TODO
 * 创建时间：2021/11/10 5:57 下午
 * 创 建 人：bulecat
 */
@Getter
@Setter
public class EditSimpleTestReportCommand extends BaseReportInfoComnand {
    @GatewayModelProperty(description = "是否按计划范围上线", required = false)
    private Integer asPlanedOnline;

    @GatewayModelProperty(description = "是否延期", required = false)
    private Integer delay;

    @GatewayModelProperty(description = "开发人数", required = false)
    private Integer developerCount;

    @GatewayModelProperty(description = "测试人数", required = false)
    private Integer testerCount;

    @GatewayModelProperty(description = "计划冒烟用例数", required = false)
    private Integer planSmokeCase;

    @GatewayModelProperty(description = "首次冒烟用例数", required = false)
    private Integer firstPermitSmoke;

    @GatewayModelProperty(description = "用例执行结果", required = false)
    private List<CaseResultVO> caseResultVOS;

    @GatewayModelProperty(description = "缺陷信息统计", required = false)
    private List<IssueInfoVO> issueInfoVOS;

    @GatewayModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;

    public EditSimpleTestReportCommand(String aggregateId) {
        super(aggregateId);
    }

}
