package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.OperationTypeEnum;
import lombok.Data;

import java.util.Date;

@Data
public class SprintIssueLinkedEvent extends BaseEvent implements ActionEvent {

    private String code;

    private String sprintCode;

    private String sprintName;

    @Override
    public String action() {
        if ("NotAssociated".equals(sprintCode)) {
            return OperationTypeEnum.REMOVEFROMSPRINT.getValue();
        }
        return OperationTypeEnum.PLANINTOSPRINT.getValue();
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        if (sprintName != null) {
            return sprintName;
        }
        return "";
    }
}
