package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ListXmindDetailVO implements Serializable {

    @ZsmpModelProperty(description = "版本code")
    private String versionCode;

    @ZsmpModelProperty(description = "用例code")
    private String id;

    @ZsmpModelProperty(description = "节点描述")
    private String topic;

    @ZsmpModelProperty(description = "父节点code")
    private String parentCode;

    @ZsmpModelProperty(description = "节点在左边(右边)")
    private DirectionEnum direction;

    @ZsmpModelProperty(description = "是否允许编辑(topic)")
    private Boolean disabled;

    @ZsmpModelProperty(description = "是否展开")
    private Boolean expanded;

    @ZsmpModelProperty(description = "标签名字")
    private String tagName;

    @ZsmpModelProperty(description = "标签名字")
    private TestCaseTagNameEnum tagValue;

    @ZsmpModelProperty(description = "用例数量")
    private Integer testcaseCount;

    @ZsmpModelProperty(description = "标签编辑权限，默认false")
    private Boolean tagEdit;

    @ZsmpModelProperty(description = "子节点")
    private List<ListXmindDetailVO> children;

    @ZsmpModelProperty(description = "节点属性")
    private TestcaseAttributeEnum attribute;

    @ZsmpModelProperty(description = "是否有下级节点")
    private Boolean hasChilds;

    @ZsmpModelProperty(description = "等级")
    private TestcasePriorityEnum priority;

    @ZsmpModelProperty(description = "等级描述")
    private String priorityDesc;

    @ZsmpModelProperty(description = "用例创建人编码")
    private Long creatorId;

    @ZsmpModelProperty(description = "用例创建人")
    private String creator;

    @ZsmpModelProperty(description = "责任人编码")
    private Long dutyUserId;

    @ZsmpModelProperty(description = "责任人")
    private String dutyUser;

    @ZsmpModelProperty(description = "路径")
    private String path;

    @ZsmpModelProperty(description = "执行状态")
    private TestPlanCaseStatusEnum executionStatus;

    @ZsmpModelProperty(description = "执行状态中文")
    private String executionStatusDesc;

    @ZsmpModelProperty(description = "用例状态")
    private TestcaseStatusEnum testcaseStatus;

    @ZsmpModelProperty(description = "用例状态中文")
    private String testcaseStatusDesc;

    public String getPriorityDesc() {
        if(null != this.priority) {
            return this.priority.getValue();
        }
        return null;
    }

    public String getTestcaseStatusDesc() {
        if(null != this.testcaseStatus) {
            return this.testcaseStatus.getDesc();
        }
        return null;
    }

    @ZsmpModelProperty(description = "用例自增id")
    private long incId;
}
