package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class TestcaseTagRemovedEvent extends BaseEvent implements ActionEvent {

    private String code;

    private String businessCode;

    private String tagName;

    @Override
    public String action() {
        return "删除了标签";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return this.tagName;
    }

    @Override
    public String logBusinessCode() {
        return this.businessCode;
    }
}
