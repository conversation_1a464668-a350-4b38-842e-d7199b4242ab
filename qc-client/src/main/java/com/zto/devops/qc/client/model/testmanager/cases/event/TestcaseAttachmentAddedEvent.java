package com.zto.devops.qc.client.model.testmanager.cases.event;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.issue.AttachmentDocumentTypeEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentFileTypeEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class TestcaseAttachmentAddedEvent extends BaseEvent implements ActionEvent {

    private String code;

    private DomainEnum domain;

    private String businessCode;

    private String url;

    private String remoteFileId;

    private String name;

    private AttachmentTypeEnum type;

    private AttachmentDocumentTypeEnum documentType;

    private String size;

    private AttachmentFileTypeEnum fileType;

    @Override
    public String action() {
        return "上传了文档";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return this.name;
    }

    @Override
    public String logBusinessCode() {
        return this.businessCode;
    }
}
