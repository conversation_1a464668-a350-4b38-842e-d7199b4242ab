package com.zto.devops.qc.client.model.issue.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2022/7/27
 * @Version 1.0
 */
@Setter
@Getter
public class RemoveIssueAttachmentCommand extends BaseCommand {

    private String attachmentName;

    public RemoveIssueAttachmentCommand(String aggregateId) {
        super(aggregateId);
    }
}
