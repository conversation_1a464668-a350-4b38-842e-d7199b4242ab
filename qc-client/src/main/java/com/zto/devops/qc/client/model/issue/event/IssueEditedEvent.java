package com.zto.devops.qc.client.model.issue.event;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.MyOperatedEvent;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.MyOperatedEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.*;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.qc.client.model.common.handler.AbstractCurrentHandlerChangedEvent;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import lombok.Data;

import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Data
public class IssueEditedEvent extends AbstractCurrentHandlerChangedEvent implements ActionEvent, MyOperatedEvent {

    private String code;
    private String title;
    private String description;

    private IssueFindEnv findEnv;
    private IssueFindStage findStage;
    private IssuePriority priority;
    private IssueRepetitionRate repetitionRate;
    private IssueRootCause rootCause;
    private IssueTestMethod testMethod;
    private IssueType type;

    private User handler;
    private User developer;
    private User tester;
    private User modifier;

    private Requirement requirement;
    private RequirementLevel requirementLevel;
    private Version findVersion;
    private Version fixVersion;
    private Sprint sprint;

    private Date updateTime;

    private String  versionConfirm;

    private Date planStartDate;
    private Date planEndDate;

    private List<AttachmentVO> attachments;

    private List<TagVO> tags;

    private IssueApplicationType applicationType;

    private Map<String, Diff> changesLog;

    @Override
    public String action() {
        return OperationTypeEnum.EDITEDISSUE.getValue();
    }

    @Override
    public User transactor() {
        return getTransactor();
    }

    @Override
    public Date occurred() {
        return super.getOccurred();
    }

    @Override
    public String makeString() {
        if (this.changesLog != null && !this.changesLog.isEmpty()) {
            StringBuilder result = new StringBuilder();
            Iterator iterator = this.changesLog.entrySet().iterator();
            while(iterator.hasNext()) {
                Map.Entry<String, Diff> diffEntry = (Map.Entry)iterator.next();
                result.append((String) diffEntry.getKey()).append(":");
                if (diffEntry.getValue() != null && !((Diff)diffEntry.getValue()).getOriginal().equals(((Diff)diffEntry.getValue()).getValue())) {
                    String originValue = ((Diff) diffEntry.getValue()).getOriginal();
                    if (originValue != null && !"".equals(originValue)) {
                        result.append(originValue).append("->");
                    }
                    result.append(((Diff) diffEntry.getValue()).getValue()).append(" \n");
                }
            }
            return result.toString();
        }
        return "";
    }

    @Override
    public String getBusinessCode() {
        return this.code;
    }

    @Override
    public User getOperator() {
        return getTransactor();
    }

    @Override
    public DomainEnum domain() {
        return DomainEnum.ISSUE;
    }

    @Override
    public String actionCode() {
        return OperationTypeEnum.EDITEDISSUE.name();
    }

    @Override
    public String processType() {
        return null;
    }
}
