package com.zto.devops.qc.client.model.issue.entity;

import java.io.Serializable;


import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.enums.issue.IssueEvent;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.LaneDomainEnum;
import com.zto.devops.qc.client.enums.issue.LaneStatusEnum;

import java.util.*;

/**
 * <AUTHOR>
 * @Description  泳道 - 缺陷状态 关系映射
 * @Date 2023/2/7
 * @Version 1.0
 */
public class LaneIssueMap extends BaseLaneMatterStatusMap<LaneIssueMapCondition> implements Serializable {

    private static final long serialVersionUID = 1L;


    private static final Map<LaneStatusEnum, Set<String>> statusMap;
    private static final Map<String, List<LaneSkipCondition>> eventActionMap;
    private static final Map<LaneStatusEnum, List<Button>> mainBtnMap;
    private static final Map<LaneStatusEnum, Set<String>> statusListMap;


    static {
        statusMap = new HashMap<>();
        statusMap.put(LaneStatusEnum.NOT_START,
                new HashSet<>(Arrays.asList( IssueStatus.START.name(), IssueStatus.WAIT_FIX.name(), IssueStatus.DELAY_FIX.name())));
        statusMap.put(LaneStatusEnum.DOING,  new HashSet<>(Arrays.asList(IssueStatus.FIXING.name())));
        statusMap.put(LaneStatusEnum.TESTING,  new HashSet<>(Arrays.asList(IssueStatus.REJECTED.name(), IssueStatus.TESTING.name())));
        statusMap.put(LaneStatusEnum.FINISH,  new HashSet<>(Arrays.asList(IssueStatus.CLOSED.name())));

        statusListMap = new HashMap<>();
        statusListMap.put(LaneStatusEnum.NOT_START,
                new HashSet<>(Arrays.asList( IssueStatus.START.name(), IssueStatus.WAIT_FIX.name(), IssueStatus.DELAY_FIX.name())));
        statusListMap.put(LaneStatusEnum.FIXING,  new HashSet<>(Arrays.asList(IssueStatus.FIXING.name())));
        statusListMap.put(LaneStatusEnum.TESTING,  new HashSet<>(Arrays.asList(IssueStatus.REJECTED.name(), IssueStatus.TESTING.name())));
        statusListMap.put(LaneStatusEnum.CLOSED,  new HashSet<>(Arrays.asList(IssueStatus.CLOSED.name())));

        // 事件的映射
        eventActionMap = new HashMap<>();
        // 待修复
        eventActionMap.put(IssueStatus.WAIT_FIX.name(), Arrays.asList(
                new LaneSkipCondition(LaneStatusEnum.DOING, new Button(IssueEvent.FIX.getName(), IssueEvent.FIX.name())),
                new LaneSkipCondition(LaneStatusEnum.FIXING, new Button(IssueEvent.FIX.getName(), IssueEvent.FIX.name()))
        ));
        // 延期修复
        eventActionMap.put(IssueStatus.DELAY_FIX.name(), Arrays.asList(
                new LaneSkipCondition(LaneStatusEnum.DOING, new Button(IssueEvent.FIX.getName(), IssueEvent.FIX.name())),
                new LaneSkipCondition(LaneStatusEnum.FIXING, new Button(IssueEvent.FIX.getName(), IssueEvent.FIX.name()))
        ));
        // 修复中
        eventActionMap.put(IssueStatus.FIXING.name(), Arrays.asList(
                new LaneSkipCondition(LaneStatusEnum.TESTING, new Button(IssueEvent.DELIVER.getName(), IssueEvent.DELIVER.name()))
        ));
        // 验证中
        eventActionMap.put(IssueStatus.TESTING.name(), Arrays.asList(
                new LaneSkipCondition(LaneStatusEnum.FINISH, new Button(IssueEvent.TEST_PASS_CLOSE.getName(), IssueEvent.TEST_PASS_CLOSE.name())),
                new LaneSkipCondition(LaneStatusEnum.CLOSED, new Button(IssueEvent.TEST_PASS_CLOSE.getName(), IssueEvent.TEST_PASS_CLOSE.name())),
                new LaneSkipCondition(LaneStatusEnum.NOT_START, new Button(IssueEvent.RETURN.getName(), IssueEvent.RETURN.name())))
        );
        // 已完成
        eventActionMap.put(IssueStatus.CLOSED.name(), Arrays.asList(
                new LaneSkipCondition(LaneStatusEnum.NOT_START, new Button(IssueEvent.REOPEN.getName(), IssueEvent.REOPEN.name()))
        ));
        // 已拒绝
        eventActionMap.put(IssueStatus.REJECTED.name(), Arrays.asList(
                new LaneSkipCondition(LaneStatusEnum.NOT_START, new Button(IssueEvent.RETURN.getName(), IssueEvent.RETURN.name())),
                new LaneSkipCondition(LaneStatusEnum.FINISH, new Button(IssueEvent.CONFIRM_CLOSE.getName(), IssueEvent.CONFIRM_CLOSE.name())),
                new LaneSkipCondition(LaneStatusEnum.CLOSED, new Button(IssueEvent.CONFIRM_CLOSE.getName(), IssueEvent.CONFIRM_CLOSE.name()))
        ));

        mainBtnMap = new HashMap<>();
        mainBtnMap.put(LaneStatusEnum.NOT_START,Arrays.asList(new Button("去修复",IssueEvent.FIX.name())));
        mainBtnMap.put(LaneStatusEnum.DOING,Arrays.asList(new Button("交付验证",IssueEvent.DELIVER.name())));
        mainBtnMap.put(LaneStatusEnum.FIXING,Arrays.asList(new Button("交付验证",IssueEvent.DELIVER.name())));
        mainBtnMap.put(LaneStatusEnum.TESTING,Arrays.asList(new Button("验证通过",IssueEvent.TEST_PASS_CLOSE.name())));
    }

    public LaneIssueMap(LaneIssueMapCondition condition) {
        super(condition);
    }

    @Override
    public List<LaneSkipCondition> getSkipCondition(String status) {
        return eventActionMap.getOrDefault(status, Collections.emptyList());
    }

    @Override
    protected Map<LaneStatusEnum, Set<String>> laneMatterStatusMap() {
        if (LaneDomainEnum.LIST.equals(condition.getLaneDomain())){
            return statusListMap;
        }
        return statusMap;
    }

    @Override
    protected List<LaneStatusEnum> allLane() {
        if (LaneDomainEnum.LIST.equals(condition.getLaneDomain())){
            return new ArrayList<>(statusListMap.keySet());
        }
        return new ArrayList<>(statusMap.keySet());
    }

    @Override
    public List<Button> getMainBtn(String status) {
        LaneStatusEnum laneStatusEnum = getLane(status);
        return mainBtnMap.getOrDefault(laneStatusEnum, Collections.emptyList());
    }

    @Override
    public List<Button> getRequirementMainBtn(String status,String type) {
        return null;
    }
}
