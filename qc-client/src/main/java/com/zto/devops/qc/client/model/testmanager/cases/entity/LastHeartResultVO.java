package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import lombok.Data;

import java.util.List;

@Data
public class LastHeartResultVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private String caseCode;

    private AutomaticTaskTrigModeEnum trigMode;

    private List<TestPlanCaseStatusEnum> resultList;

}
