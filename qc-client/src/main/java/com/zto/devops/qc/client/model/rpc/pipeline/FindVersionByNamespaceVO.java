package com.zto.devops.qc.client.model.rpc.pipeline;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FindVersionByNamespaceVO implements Serializable {

    @GatewayModelProperty(description = "产品编码", sample = "NS202101010001")
    private String productCode;

    @GatewayModelProperty(description = "版本code")
    private String versionCode;

    @GatewayModelProperty(description = "版本名")
    private String versionName;

    @GatewayModelProperty(description = "flowCode")
    private String flowCode;

}
