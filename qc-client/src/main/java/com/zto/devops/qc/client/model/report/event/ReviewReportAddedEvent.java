package com.zto.devops.qc.client.model.report.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.report.entity.ReviewInfoDTO;
import com.zto.devops.qc.client.model.report.entity.ReviewOpinionVO;
import com.zto.devops.qc.client.model.report.entity.ReviewRenewalVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Setter
@Getter
public class ReviewReportAddedEvent extends ReportAddedEvent implements ActionEvent {

    @GatewayModelProperty(description = "评审信息", required = false)
    private ReviewInfoDTO reviewInfo;

    @GatewayModelProperty(description = "评审观点", required = false)
    private List<ReviewOpinionVO> reviewOpinions;

    @GatewayModelProperty(description = "评审更新信息", required = false)
    private List<ReviewRenewalVO> reviewRenewals;



    @Override
    public String action() {
        return null;
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }
}
