package com.zto.devops.qc.client.model.issue.query;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 项目名称：project-parent
 * 类 名 称：QueryRelationInfoVo
 * 类 描 述：TODO
 * 创建时间：2023/2/8 5:03 下午
 * 创 建 人：renxinhui
 */
@Data
public class ListRelatedIssueStatusCountQuery implements Serializable {

    @GatewayModelProperty(description = "需求编号集合")
    private List<String> requirementCodeList;

}
