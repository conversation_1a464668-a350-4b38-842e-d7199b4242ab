package com.zto.devops.qc.client.model.testmanager.plan.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class PageTestPlanQuery extends PageQueryBase {

    @GatewayModelProperty(description = "计划名称", required = false)
    private String planName;

    @GatewayModelProperty(description = "计划名称", required = false)
    private String productCode;

}
