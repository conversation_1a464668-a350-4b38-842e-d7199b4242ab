package com.zto.devops.qc.client.model.testmanager.cases.entity;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ZsmpModel(description = "测试用例-状态统计个数")
public class TestCaseStatusNumVO implements Serializable {

    @ZsmpModelProperty(description = "总数")
    private Long totalNum;

    @ZsmpModelProperty(description = "心跳用例数")
    private Long heartNum;

    @ZsmpModelProperty(description = "非心跳用例数")
    private Long notHeartNum;

    public Long getNotHeartNum() {
        return 0 > (this.getTotalNum() - this.getHeartNum()) ? 0 : (this.getTotalNum() - this.getHeartNum());
    }

    @ZsmpModelProperty(description = "禁用用例数")
    private Long disableNum;

}
