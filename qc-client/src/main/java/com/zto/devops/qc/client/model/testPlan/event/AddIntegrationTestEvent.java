package com.zto.devops.qc.client.model.testPlan.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testPlan.entity.TestFunctionPointVO;
import lombok.Data;

import java.util.List;

@Data
public class AddIntegrationTestEvent extends BaseEvent {

    private String remarks;

    private String testPlanMainCode;

    private String code;

    private List<TestFunctionPointVO> pointList;

    private List<SendUserInfoVO> recipients;

    //抄送人
    private List<SendUserInfoVO> ccUsers;

    private String testPlanCode;
}
