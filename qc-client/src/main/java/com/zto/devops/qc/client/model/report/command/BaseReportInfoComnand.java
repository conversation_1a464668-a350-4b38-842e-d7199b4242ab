package com.zto.devops.qc.client.model.report.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.simple.Field;
import com.zto.devops.qc.client.enums.report.*;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：BaseReqportReq
 * 类 描 述：TODO
 * 创建时间：2021/11/10 5:31 下午
 * 创 建 人：bulecat
 */

@Getter
@Setter
public class BaseReportInfoComnand extends BaseCommand {


    @GatewayModelProperty(description = "编号", required = false)
    private String code;

    @GatewayModelProperty(description = "报告名称", required = true)
    private String name;

    @GatewayModelProperty(description = "计划编号", required = true)
    private String testPlanCode;

    @GatewayModelProperty(description = "版本code", required = true)
    private String versionCode;

    @GatewayModelProperty(description = "版本名称", required = true)
    private String versionName;

    @GatewayModelProperty(description = "报告类型", required = true)
    private ReportType reportType;

    @GatewayModelProperty(description = "总体测试结果-enum", required = false)
    private TestResultEunm testResult;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date presentationDate;

    @GatewayModelProperty(description = "计划提测时间-上下午", required = false)
    private String presentationDay;

    @GatewayModelProperty(description = "实际提测时间", required = false)
    private Date actualPresentationDate;

    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date approvalExitDate;

    @GatewayModelProperty(description = "计划准出时间--上下午", required = false)
    private String approvalExitDay;

    @GatewayModelProperty(description = "实际准出时间", required = false)
    private Date actualApprovalExitDate;

    @GatewayModelProperty(description = "实际提测时间上午、下午", required = false)
    private String actualPresentationDay;

    @GatewayModelProperty(description = "实际准出时间上午、下午", required = false)
    private String actualApprovalExitDay;

    @GatewayModelProperty(description = "实际上线时间", required = false)
    private Date actualPublishDate;

    @GatewayModelProperty(description = "计划发布时间", required = false)
    private Date publishDate;
    @GatewayModelProperty(description = "计划发布时间", required = false)
    private String publishDay;

    @GatewayModelProperty(description = "自动化测试结果-enum", required = false)
    private AutoExecuteResult autoTestResult;

    @GatewayModelProperty(description = "实际测试开始时间", required = false)
    private Date actualTestStart;

    @GatewayModelProperty(description = "实际测试开始时间", required = false)
    private String actualTestStartDay;

    @GatewayModelProperty(description = "实际测试结束时间", required = false)
    private Date actualTestEnd;

    @GatewayModelProperty(description = "实际测试结束时间", required = false)
    private String actualTestEndDay;

    @GatewayModelProperty(description = "验收开始时间", required = false)
    private Date checkStartDate;

    @GatewayModelProperty(description = "验收结束时间", required = false)
    private Date checkEndDate;

    @GatewayModelProperty(description = "版本开始时间", required = false)
    private Date startDate;

    @GatewayModelProperty(description = "版本开始时间--上下午", required = false)
    private String startDay;

    @GatewayModelProperty(description = "总结、分析、描述", required = false)
    private String summary;

    @GatewayModelProperty(description = "报告状态： 草稿；已发送", required = false)
    private TestPlanStatusEnum status;

    @GatewayModelProperty(description = "安全测试人ID", required = false)
    private Long securityUserId;

    @GatewayModelProperty(description = "安全测试人名称", required = false)
    private String securityUserName;

    private SecurityTestResult securityTestResult;

   @GatewayModelProperty(description = "邮件预览", required = false)
    private String  preview;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<SendUserInfoVO> receiveUsers;

    @GatewayModelProperty(description = "抄送人", required = false)
    private List<SendUserInfoVO> ccUsers;

    @GatewayModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;

    @GatewayModelProperty(description = "可操作按钮", required = false)
    private ReportButtonEnum buttonVOS;

    @GatewayModelProperty(description = "可编辑字段", required = false)
    private List<Field> fieldVOS;

    @GatewayModelProperty(description = "延期 -1 否，1 是", required = false)
    private Integer delay;

    public BaseReportInfoComnand(String aggregateId) {
        super(aggregateId);
    }
}
