package com.zto.devops.qc.client.model.rpc.pipeline.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.rpc.FlowStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 版本状态变化事件
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VersionStatusChangedEvent extends BaseEvent {

    /**
     * 版本编码
     */
    private List<String> versionCodes;

    /**
     * 当前状态
     */
    private FlowStatusEnum status;

    /**
     * 上一状态
     */
    private FlowStatusEnum preStatus;

}
