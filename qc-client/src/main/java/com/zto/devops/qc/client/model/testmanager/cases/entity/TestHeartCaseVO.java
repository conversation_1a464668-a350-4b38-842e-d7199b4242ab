package com.zto.devops.qc.client.model.testmanager.cases.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TestHeartCaseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ZsmpModelProperty(description = "用例名称")
    private String caseName;

    @ZsmpModelProperty(description = "用例编号")
    private String caseCode;

    @ZsmpModelProperty(description = "最新运行结果")
    private TestPlanCaseStatusEnum status;

    @ZsmpModelProperty(description = "最新运行结果描述")
    private String statusDesc;

    @ZsmpModelProperty(description = "最新告警时间")
    private Date warnTime;

    @ZsmpModelProperty(description = "AppID")
    private String appId;

    @ZsmpModelProperty(description = "AppID的名称")
    private String appIdName;

    @ZsmpModelProperty(description = "AppID的code")
    private String appIdCode;

    @ZsmpModelProperty(description = "用例属性")
    private AutomaticNodeTypeEnum nodeType;

    @ZsmpModelProperty(description = "创建人id")
    private Long creatorId;

    @ZsmpModelProperty(description = "创建人")
    private String creator;

    @ZsmpModelProperty(description = "更新人id")
    private Long modifierId;

    @ZsmpModelProperty(description = "更新人")
    private String modifier;

    @ZsmpModelProperty(description = "关注人")
    private List<SendUserInfoVO> followerList;

    @ZsmpModelProperty(description = "权限按钮")
    private List<Button> buttonList;

    @ZsmpModelProperty(description = "状态:启用/删除")
    private Boolean enable;

}
