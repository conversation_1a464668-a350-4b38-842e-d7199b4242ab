package com.zto.devops.qc.client.model.auth;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * API密钥信息
 * 
 * <AUTHOR>
 * @since 2025-08-14
 */
@Data
public class ApiKeyInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 访问密钥ID
     */
    private String accessKey;
    
    /**
     * 访问密钥Secret
     */
    private String secretKey;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 应用描述
     */
    private String appDescription;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 最后使用时间
     */
    private Date lastUsedTime;
    
    /**
     * 使用次数
     */
    private Long usageCount;
}
