package com.zto.devops.qc.client.model.testmanager.cases.query;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class CountUserHeartCaseQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "用例code")
    private List<String> caseCodeList;

    @GatewayModelProperty(description = "用户id")
    private Long userId;

}
