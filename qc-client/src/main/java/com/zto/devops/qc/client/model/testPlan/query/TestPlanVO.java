package com.zto.devops.qc.client.model.testPlan.query;

import com.zto.devops.qc.client.enums.testPlan.PlanButtonEnum;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
public class TestPlanVO implements Serializable {

    /**
     * 编号
     */
    @GatewayModelProperty(description = "编号", required = false)
    private String code;

    /**
     * 计划名
     */
    @GatewayModelProperty(description = "计划名", required = false)
    private String planName;

    /**
     * 所属产品id
     */
    @GatewayModelProperty(description = "所属产品code", required = false)
    private String productCode;

    /**
     * 产品名称
     */
    @GatewayModelProperty(description = "产品名称", required = false)
    private String productName;

    /**
     * 类型
     */
    @GatewayModelProperty(description = "类型", required = false)
    private String type;

    /**
     * 类型
     */
    @GatewayModelProperty(description = "类型说明", required = false)
    private String typeDesc;

    /**
     * 计划编号
     */
    @GatewayModelProperty(description = "计划编号", required = false)
    private String planCode;

    /**
     * 测试负责人id
     */
    @GatewayModelProperty(description = "测试负责人id", required = false)
    private Long testDirectorId;

    /**
     * 测试负责人名
     */
    @GatewayModelProperty(description = "测试负责人名", required = false)
    private String testDirectorName;

    /**
     * 创建人编码
     */
    @GatewayModelProperty(description = " 创建人编码", required = false)
    private Long creatorId;

    /**
     * 创建人
     */
    @GatewayModelProperty(description = "创建人", required = false)
    private String creator;

    /**
     * 创建时间
     */
    @GatewayModelProperty(description = "创建时间", required = false)
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    @GatewayModelProperty(description = "更新人id", required = false)
    private Long modifierId;

    /**
     * 更新人
     */
    @GatewayModelProperty(description = "更新人", required = false)
    private String modifier;

    /**
     * 更新时间
     */
    @GatewayModelProperty(description = "更新时间", required = false)
    private Date gmtModified;

    /**
     * 是否删除 1 未删除, 0 已删除
     */
    @GatewayModelProperty(description = "是否删除", required = false)
    private Boolean enable;

    /**
     * 修改次数
     */
    @GatewayModelProperty(description = "修改次数", required = false)
    private Integer editNo;

    /**
     * 状态
     */
    @GatewayModelProperty(description = "状态", required = false)
    private String status;

    /**
     * 预览html
     */
    @GatewayModelProperty(description = "预览html", required = false)
    private String preview;

    @GatewayModelProperty(description = "版本名", required = false)
    private String versionName;

    @GatewayModelProperty(description = "版本code", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "部门Id", required = false)
    private Long deptId;

    @GatewayModelProperty(description = "部门名", required = false)
    private String deptName;

    @GatewayModelProperty(description = "产品负责人id", required = false)
    private Long productDirectorId;

    @GatewayModelProperty(description = "产品负责人名", required = false)
    private String productDirectorName;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<SendUserInfoVO> receiveUsers = new ArrayList<>();

    @GatewayModelProperty(description = "抄送人", required = false)
    private List<SendUserInfoVO> ccUsers = new ArrayList<>();

    @GatewayModelProperty(description = "邮件发送时间", required = false)
    private Date sendTime;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date presentationDate;
    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date approvalExitDate;

    @GatewayModelProperty(description = "版本开始时间", required = false)
    private Date startDate;
    @GatewayModelProperty(description = "版本上线时间", required = false)
    private Date publishDate;

    @GatewayModelProperty(description = "计划提测时间-上下午", required = false)
    private String presentationDay;
    @GatewayModelProperty(description = "计划准出时间-上下午", required = false)
    private String approvalExitDay;

    @GatewayModelProperty(description = "版本开始时间-上下午", required = false)
    private String startDay;
    @GatewayModelProperty(description = "版本上线时间-上下午", required = false)
    private String publishDay;
    @GatewayModelProperty(description = "可操作按钮 (更新计划)", required = false)
    private PlanButtonEnum buttonVOS;

    public void setDateDay() {
        this.setPresentationDay(Objects.isNull(this.getPresentationDate()) ? null : (ifAfternoonByDate(this.getPresentationDate()) ? "09:00:00" : "18:00:00"));
        this.setApprovalExitDay(Objects.isNull(this.getApprovalExitDate()) ? null : (ifAfternoonByDate(this.getApprovalExitDate()) ? "09:00:00" : "18:00:00"));
        this.setStartDay(Objects.isNull(this.getStartDate()) ? null : (ifAfternoonByDate(this.getStartDate()) ? "09:00:00" : "18:00:00"));
        this.setPublishDay(Objects.isNull(this.getPublishDate()) ? null : (ifAfternoonByDate(this.getPublishDate()) ? "09:00:00" : "18:00:00"));
    }

    public boolean ifAfternoonByDate(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH");
        String format = simpleDateFormat.format(date);
        if (Integer.parseInt(format) < 12) {
            return true;
        } else {
            return false;
        }
    }
}
