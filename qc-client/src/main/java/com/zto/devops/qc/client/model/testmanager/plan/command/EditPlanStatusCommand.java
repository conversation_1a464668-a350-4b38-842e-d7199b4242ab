package com.zto.devops.qc.client.model.testmanager.plan.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EditPlanStatusCommand extends BaseCommand {
    public EditPlanStatusCommand(String aggregateId) {
        super(aggregateId);
    }

    private String planCode;
    private TestPlanNewStatusEnum status;
    private TestPlanNewTypeEnum planType;
    private String relationPlanCode;
    private SecurityTestResult securityTestResult;

}
