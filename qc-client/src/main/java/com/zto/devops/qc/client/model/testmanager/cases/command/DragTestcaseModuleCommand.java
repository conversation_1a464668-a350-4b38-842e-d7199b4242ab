package com.zto.devops.qc.client.model.testmanager.cases.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.DragModuleActionEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DragTestcaseModuleCommand extends BaseCommand {

    private String parentCode;

    private String targetCode;

    private DragModuleActionEnum action;

    private TestcaseTypeEnum type;

    public DragTestcaseModuleCommand(String aggregateId) {
        super(aggregateId);
    }
}
